#!/usr/bin/env python3
"""
Comprehensive Performance Testing Script
Measures response times, throughput, and resource utilization across all AI services
"""

import requests
import json
import time
import statistics
import concurrent.futures
from typing import Dict, List, Any
import threading

class PerformanceTester:
    def __init__(self):
        # Modal endpoints
        self.base_url = "https://trade-digital--courseai-a100-simple"
        self.endpoints = {
            "health": f"{self.base_url}-health.modal.run",
            "mistral": f"{self.base_url}-api-mistral.modal.run",
            "tts": f"{self.base_url}-api-tts.modal.run",
            "voices": f"{self.base_url}-api-voices.modal.run",
            "slides": f"{self.base_url}-api-slides.modal.run",
            "avatar": f"{self.base_url}-api-avatar.modal.run",
            "course_avatar": f"{self.base_url}-api-course-avatar.modal.run"
        }
        
        self.results = {}
        self.test_data = self._prepare_test_data()

    def _prepare_test_data(self):
        """Prepare test data for performance testing"""
        # Simple test image (1x1 pixel PNG)
        test_image = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        
        # Simple test audio (empty WAV)
        test_audio = "UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA="
        
        return {
            "image_base64": test_image,
            "audio_base64": test_audio,
            "test_prompts": [
                "Explain machine learning basics",
                "Describe photosynthesis process",
                "Introduction to programming",
                "Benefits of renewable energy",
                "How computers work"
            ],
            "test_texts": [
                "Hello, this is a test of the speech system.",
                "Welcome to our AI-powered learning platform.",
                "Today we will explore advanced technologies.",
                "Machine learning is transforming education.",
                "Thank you for joining our course."
            ]
        }

    def measure_response_time(self, func, *args, **kwargs):
        """Measure response time for a function call"""
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            return {
                "success": True,
                "response_time": end_time - start_time,
                "result": result
            }
        except Exception as e:
            end_time = time.time()
            return {
                "success": False,
                "response_time": end_time - start_time,
                "error": str(e)
            }

    def test_health_performance(self, iterations=10):
        """Test health endpoint performance"""
        print("🏥 Testing Health Endpoint Performance...")
        
        def single_health_test():
            response = requests.get(self.endpoints["health"], timeout=30)
            return response.status_code == 200
        
        times = []
        successes = 0
        
        for i in range(iterations):
            result = self.measure_response_time(single_health_test)
            times.append(result["response_time"])
            if result["success"]:
                successes += 1
            print(f"  Test {i+1}/{iterations}: {result['response_time']:.2f}s")
        
        self.results["health"] = {
            "iterations": iterations,
            "success_rate": successes / iterations,
            "avg_response_time": statistics.mean(times),
            "min_response_time": min(times),
            "max_response_time": max(times),
            "median_response_time": statistics.median(times),
            "std_dev": statistics.stdev(times) if len(times) > 1 else 0
        }

    def test_mistral_performance(self, iterations=5):
        """Test Mistral text generation performance"""
        print("\n🧠 Testing Mistral LLM Performance...")
        
        def single_mistral_test(prompt):
            payload = {"prompt": prompt, "max_tokens": 100, "temperature": 0.7}
            response = requests.post(self.endpoints["mistral"], json=payload, timeout=120)
            data = response.json()
            return data.get("success", False)
        
        times = []
        successes = 0
        
        for i in range(iterations):
            prompt = self.test_data["test_prompts"][i % len(self.test_data["test_prompts"])]
            result = self.measure_response_time(single_mistral_test, prompt)
            times.append(result["response_time"])
            if result["success"]:
                successes += 1
            print(f"  Test {i+1}/{iterations}: {result['response_time']:.2f}s")
        
        self.results["mistral"] = {
            "iterations": iterations,
            "success_rate": successes / iterations,
            "avg_response_time": statistics.mean(times),
            "min_response_time": min(times),
            "max_response_time": max(times),
            "median_response_time": statistics.median(times),
            "std_dev": statistics.stdev(times) if len(times) > 1 else 0
        }

    def test_avatar_performance(self, iterations=3):
        """Test avatar generation performance"""
        print("\n🎭 Testing Avatar Generation Performance...")
        
        def single_avatar_test():
            payload = {
                "ref_image_base64": self.test_data["image_base64"],
                "audio_base64": self.test_data["audio_base64"],
                "config": {"width": 256, "height": 256, "fps": 12, "max_frames": 24}
            }
            response = requests.post(self.endpoints["avatar"], json=payload, timeout=300)
            data = response.json()
            return data.get("success", False)
        
        times = []
        successes = 0
        
        for i in range(iterations):
            result = self.measure_response_time(single_avatar_test)
            times.append(result["response_time"])
            if result["success"]:
                successes += 1
            print(f"  Test {i+1}/{iterations}: {result['response_time']:.2f}s")
        
        self.results["avatar"] = {
            "iterations": iterations,
            "success_rate": successes / iterations,
            "avg_response_time": statistics.mean(times),
            "min_response_time": min(times),
            "max_response_time": max(times),
            "median_response_time": statistics.median(times),
            "std_dev": statistics.stdev(times) if len(times) > 1 else 0
        }

    def test_concurrent_load(self, concurrent_requests=3):
        """Test concurrent load handling"""
        print(f"\n⚡ Testing Concurrent Load ({concurrent_requests} requests)...")
        
        def concurrent_health_test():
            response = requests.get(self.endpoints["health"], timeout=30)
            return response.status_code == 200
        
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = [executor.submit(concurrent_health_test) for _ in range(concurrent_requests)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        successes = sum(results)
        
        self.results["concurrent_load"] = {
            "concurrent_requests": concurrent_requests,
            "total_time": total_time,
            "success_rate": successes / concurrent_requests,
            "requests_per_second": concurrent_requests / total_time,
            "avg_time_per_request": total_time / concurrent_requests
        }
        
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Success rate: {successes}/{concurrent_requests}")
        print(f"  Requests/second: {concurrent_requests/total_time:.2f}")

    def test_end_to_end_pipeline(self, iterations=2):
        """Test complete course avatar pipeline performance"""
        print("\n🎓 Testing End-to-End Pipeline Performance...")
        
        def single_pipeline_test(prompt):
            payload = {
                "prompt": prompt,
                "ref_image_base64": self.test_data["image_base64"],
                "voice_id": "tts_models/en/ljspeech/tacotron2-DDC",
                "config": {"width": 256, "height": 256, "fps": 12, "max_frames": 36}
            }
            response = requests.post(self.endpoints["course_avatar"], json=payload, timeout=600)
            data = response.json()
            return data.get("success", False)
        
        times = []
        successes = 0
        
        for i in range(iterations):
            prompt = self.test_data["test_prompts"][i % len(self.test_data["test_prompts"])]
            result = self.measure_response_time(single_pipeline_test, prompt)
            times.append(result["response_time"])
            if result["success"]:
                successes += 1
            print(f"  Pipeline {i+1}/{iterations}: {result['response_time']:.2f}s")
        
        self.results["pipeline"] = {
            "iterations": iterations,
            "success_rate": successes / iterations,
            "avg_response_time": statistics.mean(times),
            "min_response_time": min(times),
            "max_response_time": max(times),
            "median_response_time": statistics.median(times),
            "std_dev": statistics.stdev(times) if len(times) > 1 else 0
        }

    def run_performance_tests(self):
        """Run comprehensive performance test suite"""
        print("🚀 Starting Comprehensive Performance Testing\n")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run all performance tests
        self.test_health_performance(10)
        self.test_mistral_performance(5)
        self.test_avatar_performance(3)
        self.test_concurrent_load(3)
        self.test_end_to_end_pipeline(2)
        
        total_time = time.time() - start_time
        
        # Print comprehensive results
        self.print_performance_summary(total_time)

    def print_performance_summary(self, total_time):
        """Print comprehensive performance results"""
        print("\n" + "=" * 60)
        print("📊 PERFORMANCE TEST RESULTS")
        print("=" * 60)
        
        for service, metrics in self.results.items():
            print(f"\n🔧 {service.upper()} PERFORMANCE:")
            print(f"  Iterations: {metrics.get('iterations', 'N/A')}")
            print(f"  Success Rate: {metrics.get('success_rate', 0):.1%}")
            print(f"  Avg Response Time: {metrics.get('avg_response_time', 0):.2f}s")
            print(f"  Min/Max Time: {metrics.get('min_response_time', 0):.2f}s / {metrics.get('max_response_time', 0):.2f}s")
            print(f"  Median Time: {metrics.get('median_response_time', 0):.2f}s")
            print(f"  Std Deviation: {metrics.get('std_dev', 0):.2f}s")
            
            if service == "concurrent_load":
                print(f"  Requests/Second: {metrics.get('requests_per_second', 0):.2f}")
        
        print("\n" + "-" * 60)
        print(f"🎯 TOTAL TEST TIME: {total_time:.2f} seconds")
        
        # Performance assessment
        avg_times = [m.get('avg_response_time', 0) for m in self.results.values() if 'avg_response_time' in m]
        success_rates = [m.get('success_rate', 0) for m in self.results.values() if 'success_rate' in m]
        
        overall_success = statistics.mean(success_rates) if success_rates else 0
        
        print(f"📈 OVERALL SUCCESS RATE: {overall_success:.1%}")
        
        if overall_success >= 0.9:
            print("🎉 EXCELLENT PERFORMANCE! All services performing optimally.")
        elif overall_success >= 0.7:
            print("✅ GOOD PERFORMANCE! Most services performing well.")
        else:
            print("⚠️ PERFORMANCE ISSUES DETECTED! Review failed services.")
        
        print("\n📋 Performance Recommendations:")
        
        # Health endpoint
        health_avg = self.results.get("health", {}).get("avg_response_time", 0)
        if health_avg > 5:
            print("  ⚠️ Health endpoint slow - check GPU availability")
        else:
            print("  ✅ Health endpoint performing well")
        
        # Avatar generation
        avatar_avg = self.results.get("avatar", {}).get("avg_response_time", 0)
        if avatar_avg > 30:
            print("  ⚠️ Avatar generation slow - consider reducing resolution/frames")
        else:
            print("  ✅ Avatar generation performing well")
        
        # Pipeline
        pipeline_avg = self.results.get("pipeline", {}).get("avg_response_time", 0)
        if pipeline_avg > 120:
            print("  ⚠️ Pipeline slow - consider optimizing text length/video settings")
        else:
            print("  ✅ End-to-end pipeline performing well")

if __name__ == "__main__":
    tester = PerformanceTester()
    tester.run_performance_tests()
