// User related types
export interface User {
  id: number;
  username: string;
  email: string;
  name: string | null;
  emailVerified: boolean;
  avatarUrl: string | null;
  plan: string;
  role: string;
  stripeCustomerId: string | null;
  stripeSubscriptionId: string | null;
  createdAt: string | Date;
}

// User stats type (defined below with full fields)

// Payment method type
export interface PaymentMethod {
  id: string;
  userId: number;
  type: string;
  lastFour: string;
  expiryDate: string;
  isDefault: boolean;
}

// Billing history entry type
export interface BillingHistory {
  id: string | number;
  userId: number;
  amount: number;
  currency: string;
  description: string;
  status: string;
  transactionDate: Date | string;
}

// Course related types
export interface Course {
  id: number;
  userId: number;
  title: string;
  description: string;
  category: string;
  status: string;
  targetAudience: string | null;
  thumbnailUrl: string | null;
  completion: number;
  lessonsCount: number;
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface Lesson {
  id: number;
  courseId: number;
  title: string;
  description: string | null;
  content: string | null;
  script: string | null;
  duration: number | null;
  order: number;
  createdAt: Date;
}

// Media library item type
export interface Media {
  id: number;
  userId: number;
  title: string;
  type: string;
  url: string;
  size: number;
  createdAt: Date;
}

// Integration related types
export interface Integration {
  id: number;
  userId: number;
  platform: string;
  status: string;
  platformUserId: string | null;
  accessToken: string | null;
  refreshToken: string | null;
  tokenExpiry: Date | null;
  config: Record<string, any>;
  createdAt: Date;
}

// Publishing related types
export interface Publishing {
  id: number;
  courseId: number;
  platform: string;
  status: string;
  platformUrl: string | null;
  publishedAt: Date;
  analyticsData: Record<string, any>;
}

// User stats type
export interface UserStats {
  userId: number;
  activeCourses: number;
  publishedCourses: number;
  aiCredits: number;
  storageUsed: number;
  storageLimit: number;
}

// Team related types
export interface Team {
  id: number;
  name: string;
  ownerId: number;
  description: string | null;
  logoUrl: string | null;
  createdAt: Date;
}

export interface TeamMember {
  teamId: number;
  userId: number;
  role: string;
  joinedAt: Date;
}

// Course collaborator type
export interface CourseCollaborator {
  courseId: number;
  userId: number;
  role: string;
  invitedAt: Date;
  joinedAt: Date | null;
}

// Analytics related types
export interface AnalyticsEvent {
  id: number;
  userId: number | null;
  courseId: number | null;
  lessonId: number | null;
  eventType: string;
  eventData: Record<string, any>;
  createdAt: Date;
}

export interface CourseAnalytics {
  courseId: number;
  views: number;
  enrollments: number;
  completions: number;
  averageRating: number;
  completionPercentage: number;
  updatedAt: Date;
}

export interface UserEngagementAnalytics {
  viewsByDay: { date: string; count: number }[];
  enrollmentsByDay: { date: string; count: number }[];
  totalEnrollments: number;
  totalViews: number;
  coursePerformance: {
    courseId: number;
    title: string;
    views: number;
    enrollments: number;
    averageRating: number;
  }[];
  earnings?: {
    totalEarnings: number;
    earningsByMonth: { month: string; amount: number }[];
  };
}

export interface LessonAnalytics {
  lessonId: number;
  views: number;
  completions: number;
  averageTimeSpent: number;
  dropoffRate: number;
  updatedAt: Date;
}

// Template type
export interface Template {
  id: number;
  name: string;
  description: string;
  icon: string;
  type: string;
}