import React from 'react';
import { FaFacebook, FaInstagram, FaLinkedin, FaTwitter, FaYoutube, FaTiktok } from 'react-icons/fa';
import { 
  SiUdemy, SiCoursera, SiSkillshare, 
  SiVimeo, SiTwitch, SiPinterest, SiReddit, SiMedium
} from 'react-icons/si';
import { Award, Bookmark, Book, HelpCircle, GraduationCap, Bell } from 'lucide-react';

interface PlatformIconProps {
  platform: string;
  size?: number;
  className?: string;
}

export const PlatformIcon: React.FC<PlatformIconProps> = ({ platform, size = 24, className = '' }) => {
  const iconClass = `${className}`;
  
  // Match the platform to its icon
  switch (platform.toLowerCase()) {
    case 'facebook':
      return <FaFacebook size={size} className={iconClass} />;
    case 'instagram':
      return <FaInstagram size={size} className={iconClass} />;
    case 'twitter':
      return <FaTwitter size={size} className={iconClass} />;
    case 'linkedin':
      return <FaLinkedin size={size} className={iconClass} />;
    case 'youtube':
      return <FaYoutube size={size} className={iconClass} />;
    case 'tiktok':
      return <FaTiktok size={size} className={iconClass} />;
    case 'udemy':
      return <SiUdemy size={size} className={iconClass} />;
    case 'teachable':
      return <GraduationCap size={size} className={iconClass} />;
    case 'coursera':
      return <SiCoursera size={size} className={iconClass} />;
    case 'skillshare':
      return <SiSkillshare size={size} className={iconClass} />;
    case 'vimeo':
      return <SiVimeo size={size} className={iconClass} />;
    case 'twitch':
      return <SiTwitch size={size} className={iconClass} />;
    case 'pinterest':
      return <SiPinterest size={size} className={iconClass} />;
    case 'reddit':
      return <SiReddit size={size} className={iconClass} />;
    case 'medium':
      return <SiMedium size={size} className={iconClass} />;
    // Default or unsupported platforms use generic icons based on type
    case 'education':
      return <Book size={size} className={iconClass} />;
    case 'marketplace':
      return <Award size={size} className={iconClass} />;
    case 'social':
      return <Bookmark size={size} className={iconClass} />;
    default:
      return <HelpCircle size={size} className={iconClass} />;
  }
};

export default PlatformIcon;