"""
Direct A100 80G GPU Deployment for Course AI Platform
Simplified deployment script with comprehensive GPU setup
"""

import os
import subprocess
import sys

def check_credentials():
    """Check Modal credentials"""
    token_id = os.getenv('MODAL_TOKEN_ID')
    token_secret = os.getenv('MODAL_TOKEN_SECRET')
    
    print("=== Modal Credentials Check ===")
    print(f"Token ID: {token_id[:10]}..." if token_id else "Token ID: Not found")
    print(f"Token Secret: {token_secret[:10]}..." if token_secret else "Token Secret: Not found")
    
    if not token_id or not token_secret:
        print("\n❌ Modal credentials missing!")
        print("Please provide correct Modal API credentials:")
        print("1. Visit modal.com and log into your account")
        print("2. Go to Settings > API Tokens")
        print("3. Create or copy your API token")
        print("4. The token should have a specific format (not starting with 'ak-' or 'wk-')")
        return False
    
    return True

def create_simple_gpu_test():
    """Create simple GPU test script"""
    
    gpu_test_content = '''
import modal

# Create Modal app
app = modal.App("courseai-gpu-test")

# Define A100 GPU image
gpu_image = modal.Image.debian_slim(python_version="3.11").pip_install([
    "torch==2.1.0", "torchvision==0.16.0", "torchaudio==2.1.0"
])

@app.function(
    gpu="A100-80GB",
    timeout=300,
    memory=16384,
    image=gpu_image
)
def test_gpu():
    """Simple GPU test function"""
    import torch
    
    # Check GPU availability
    gpu_available = torch.cuda.is_available()
    gpu_count = torch.cuda.device_count() if gpu_available else 0
    
    gpu_info = {}
    if gpu_available:
        gpu_info = {
            "name": torch.cuda.get_device_name(0),
            "memory_gb": torch.cuda.get_device_properties(0).total_memory / 1024**3,
            "compute_capability": torch.cuda.get_device_capability(0)
        }
        
        # Test GPU computation
        test_tensor = torch.randn(1000, 1000, device='cuda')
        result = torch.mm(test_tensor, test_tensor.T)
        gpu_info["test_passed"] = result.shape == (1000, 1000)
    
    return {
        "gpu_available": gpu_available,
        "gpu_count": gpu_count,
        "gpu_info": gpu_info,
        "status": "success" if gpu_available else "no_gpu"
    }

@app.local_entrypoint()
def main():
    """Test the GPU deployment"""
    print("Testing A100 GPU...")
    result = test_gpu.remote()
    print(f"GPU Test Result: {result}")
    
    if result.get("gpu_available"):
        print("✅ A100 GPU is active and working!")
        print(f"GPU: {result['gpu_info']['name']}")
        print(f"Memory: {result['gpu_info']['memory_gb']:.1f}GB")
    else:
        print("❌ GPU not available")
    
    return result

if __name__ == "__main__":
    main()
'''
    
    with open('modal_gpu_test.py', 'w') as f:
        f.write(gpu_test_content.strip())
    
    print("✅ Created simple GPU test script: modal_gpu_test.py")

def deploy_gpu_test():
    """Deploy the GPU test to Modal"""
    try:
        print("\n=== Deploying A100 GPU Test ===")
        
        # Deploy to Modal
        result = subprocess.run([
            'modal', 'deploy', 'modal_gpu_test.py'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ A100 GPU deployed successfully!")
            print(f"Output: {result.stdout}")
            
            # Run the test
            print("\n=== Running GPU Test ===")
            test_result = subprocess.run([
                'modal', 'run', 'modal_gpu_test.py'
            ], capture_output=True, text=True, timeout=300)
            
            if test_result.returncode == 0:
                print("✅ GPU test completed!")
                print(f"Test output: {test_result.stdout}")
            else:
                print("❌ GPU test failed")
                print(f"Error: {test_result.stderr}")
        else:
            print("❌ Deployment failed")
            print(f"Error: {result.stderr}")
            
            if "Token ID is malformed" in result.stderr:
                print("\n🔑 Token format issue detected!")
                print("Modal tokens should be in format: ak-XXXXXX or similar")
                print("Please check your Modal dashboard for the correct format")
                
    except subprocess.TimeoutExpired:
        print("⏰ Deployment timed out - this is normal for first-time GPU setup")
    except Exception as e:
        print(f"❌ Deployment error: {e}")

def main():
    """Main deployment function"""
    print("Course AI Platform - A100 80G GPU Deployment")
    print("=" * 50)
    
    # Check credentials
    if not check_credentials():
        return
    
    # Create test script
    create_simple_gpu_test()
    
    # Deploy to Modal
    deploy_gpu_test()
    
    print("\n=== Next Steps ===")
    print("1. Check your Modal dashboard for active GPU instances")
    print("2. If successful, the A100 GPU should be visible and running")
    print("3. GPU will scale to zero when not in use to save costs")

if __name__ == "__main__":
    main()