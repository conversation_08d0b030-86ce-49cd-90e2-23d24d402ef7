import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import EnterpriseUpgradeModal from '@/components/stripe/enterprise-upgrade-modal';
import { JitsiMeeting } from '@jitsi/react-sdk';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Video, 
  Calendar, 
  Clock, 
  Users, 
  Plus,
  Copy,
  Play,
  Download,
  Maximize,
  Minimize,
  X,
  Crown,
  CalendarPlus,
  VideoIcon,
  MoreVertical,
  Settings,
  Eye,
  ExternalLink
} from 'lucide-react';
import { format, parseISO, isAfter, isBefore, startOfDay } from 'date-fns';

// Declare global window interface for Jitsi API
declare global {
  interface Window {
    jitsiApi: any;
  }
}

interface Meeting {
  id: number;
  title: string;
  description?: string;
  scheduledFor: string;
  duration: number;
  roomId: string;
  status: 'scheduled' | 'active' | 'completed' | 'cancelled';
  recordingUrl?: string;
  attendees?: string[];
  createdBy: number;
  createdAt: string;
}

interface NewMeeting {
  title: string;
  description?: string;
  scheduledFor: string;
  duration: number;
  attendees?: string[];
}

export default function MeetingsPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // State management
  const [activeMeeting, setActiveMeeting] = useState<string | null>(null);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [selectedMeeting, setSelectedMeeting] = useState<Meeting | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);
  
  // Form state for scheduling
  const [newMeeting, setNewMeeting] = useState<NewMeeting>({
    title: '',
    description: '',
    scheduledFor: '',
    duration: 60,
    attendees: []
  });

  // Check if user has Enterprise subscription
  const userTyped = user as any;
  const isEnterprise = userTyped?.subscription === 'enterprise';

  // Fetch meetings data
  const { data: meetings = [], isLoading } = useQuery({
    queryKey: ['/api/meetings'],
    enabled: isEnterprise
  });

  // Schedule meeting mutation
  const scheduleMeeting = useMutation({
    mutationFn: (meetingData: NewMeeting) =>
      apiRequest('POST', '/api/meetings', meetingData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/meetings'] });
      setIsScheduleDialogOpen(false);
      setNewMeeting({
        title: '',
        description: '',
        scheduledFor: '',
        duration: 60,
        attendees: []
      });
      toast({
        title: 'Meeting Scheduled',
        description: 'Your meeting has been scheduled successfully.',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to schedule meeting. Please try again.',
        variant: 'destructive',
      });
    }
  });

  // Join meeting mutation
  const joinMeeting = useMutation({
    mutationFn: (meetingId: number) =>
      apiRequest('POST', `/api/meetings/${meetingId}/join`),
    onSuccess: (data: any) => {
      setActiveMeeting(data.roomId);
      queryClient.invalidateQueries({ queryKey: ['/api/meetings'] });
    }
  });

  // End meeting mutation
  const endMeeting = useMutation({
    mutationFn: (meetingId: number) =>
      apiRequest('POST', `/api/meetings/${meetingId}/end`),
    onSuccess: () => {
      setActiveMeeting(null);
      setIsFullScreen(false);
      queryClient.invalidateQueries({ queryKey: ['/api/meetings'] });
      toast({
        title: 'Meeting Ended',
        description: 'The meeting has been ended and recorded.',
      });
    }
  });

  // Filter meetings by status
  const meetingsArray = Array.isArray(meetings) ? meetings : [];
  const upcomingMeetings = meetingsArray.filter((m: any) => 
    m.status === 'scheduled' && isAfter(parseISO(m.scheduledFor), new Date())
  );
  
  const activeMeetings = meetingsArray.filter((m: any) => m.status === 'active');
  
  const completedMeetings = meetingsArray.filter((m: any) => 
    m.status === 'completed'
  ).sort((a: any, b: any) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  // Handle instant meeting creation
  const createInstantMeeting = () => {
    const roomId = `instant-${user?.id}-${Date.now()}`;
    setActiveMeeting(roomId);
    
    // Create meeting record
    scheduleMeeting.mutate({
      title: 'Instant Meeting',
      scheduledFor: new Date().toISOString(),
      duration: 60
    });
  };

  // Jitsi configuration
  const jitsiConfig = {
    roomName: activeMeeting || '',
    configOverwrite: {
      startWithAudioMuted: false,
      startWithVideoMuted: false,
      enableClosePage: false,
      prejoinPageEnabled: false,
      disableDeepLinking: true,
      enableWelcomePage: false,
      enableNoisyMicDetection: true,
      resolution: 720,
      defaultLayout: 'tile',
      toolbarButtons: [
        'microphone', 'camera', 'desktop', 'fullscreen',
        'hangup', 'profile', 'chat', 'settings', 'raisehand',
        'videoquality', 'filmstrip', 'tileview', 'recording'
      ],
      recordingService: {
        enabled: true,
        sharingEnabled: true,
        hideStorageWarning: false
      }
    },
    interfaceConfigOverwrite: {
      TOOLBAR_BUTTONS: [
        'microphone', 'camera', 'desktop', 'fullscreen',
        'hangup', 'profile', 'chat', 'settings', 'raisehand',
        'videoquality', 'filmstrip', 'tileview', 'recording'
      ],
      SHOW_JITSI_WATERMARK: false,
      SHOW_WATERMARK_FOR_GUESTS: false,
      DEFAULT_BACKGROUND: '#1a1b1c',
      DEFAULT_REMOTE_DISPLAY_NAME: 'Team Member',
      DEFAULT_LOCAL_DISPLAY_NAME: user?.username || 'You',
      DISABLE_JOIN_LEAVE_NOTIFICATIONS: true,
      TOOLBAR_ALWAYS_VISIBLE: true,
      MOBILE_APP_PROMO: false
    },
    userInfo: {
      displayName: user?.username || 'Guest',
      email: user?.email || '',
    },
    onApiReady: (api: any) => {
      window.jitsiApi = api;
      
      api.addListener('videoConferenceJoined', () => {
        toast({
          title: 'Joined Meeting',
          description: 'You have successfully joined the meeting.',
        });
      });
      
      api.addListener('videoConferenceLeft', () => {
        setActiveMeeting(null);
        setIsFullScreen(false);
      });

      api.addListener('recordingStatusChanged', (status: any) => {
        if (status.on) {
          toast({
            title: 'Recording Started',
            description: 'This meeting is now being recorded.',
          });
        } else {
          toast({
            title: 'Recording Stopped',
            description: 'Meeting recording has been saved.',
          });
        }
      });
    },
  };

  // Enterprise plan check
  if (!isEnterprise) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Crown className="w-8 h-8 text-yellow-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Enterprise Feature</h2>
            <p className="text-gray-600 mb-6">
              Video meetings and collaboration features are available exclusively for Enterprise plan users. Upgrade your subscription to access advanced meeting scheduling, recording, live streaming, and team collaboration tools with Jitsi integration.
            </p>
            <Button 
              onClick={() => setIsUpgradeModalOpen(true)}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              Upgrade to Enterprise
            </Button>
          </div>
        </div>

        {/* Enterprise Upgrade Modal */}
        <EnterpriseUpgradeModal
          isOpen={isUpgradeModalOpen}
          onClose={() => setIsUpgradeModalOpen(false)}
          onSuccess={() => {
            toast({
              title: "Welcome to Enterprise!",
              description: "Your account has been upgraded. Refreshing page...",
            });
            // Refresh page to update user plan
            setTimeout(() => window.location.reload(), 2000);
          }}
        />
      </div>
    );
  }

  // Active meeting view (full screen)
  if (activeMeeting) {
    return (
      <div className={`${isFullScreen ? 'fixed inset-0 z-50 bg-black' : 'h-screen'} flex flex-col`}>
        {/* Meeting Controls Header */}
        <div className="bg-slate-900 text-white p-4 flex justify-between items-center z-10">
          <div className="flex items-center gap-3">
            <Video className="h-5 w-5" />
            <span className="font-medium">{selectedMeeting?.title || 'Meeting'}</span>
            {activeMeetings.length > 0 && (
              <Badge variant="destructive" className="animate-pulse">
                LIVE
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsFullScreen(!isFullScreen)}
              className="text-white hover:bg-slate-700"
            >
              {isFullScreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
            
            <Button
              size="sm"
              variant="destructive"
              onClick={() => {
                if (selectedMeeting) {
                  endMeeting.mutate(selectedMeeting.id);
                } else {
                  setActiveMeeting(null);
                  setIsFullScreen(false);
                }
              }}
            >
              <X className="h-4 w-4 mr-1" />
              End Meeting
            </Button>
          </div>
        </div>

        {/* Jitsi Meeting Container */}
        <div className="flex-1 bg-black">
          {activeMeeting && (
            <JitsiMeeting
              roomName={activeMeeting || ''}
              configOverwrite={jitsiConfig.configOverwrite}
              interfaceConfigOverwrite={jitsiConfig.interfaceConfigOverwrite}
              userInfo={jitsiConfig.userInfo}
              onApiReady={jitsiConfig.onApiReady}
              getIFrameRef={(iframeRef: any) => {
                if (iframeRef && (iframeRef as any).current) {
                  (iframeRef as any).current.style.height = '100%';
                  (iframeRef as any).current.style.width = '100%';
                }
              }}
            />
          )}
        </div>
      </div>
    );
  }

  // Main meetings dashboard
  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Video className="h-8 w-8 text-primary" />
            Enterprise Meetings
          </h1>
          <p className="text-muted-foreground">
            Schedule, join, and manage your team video conferences with recording capabilities
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={createInstantMeeting}
          >
            <Video className="h-4 w-4 mr-2" />
            Instant Meeting
          </Button>
          
          <Button onClick={() => setIsScheduleDialogOpen(true)}>
            <CalendarPlus className="h-4 w-4 mr-2" />
            Schedule Meeting
          </Button>
        </div>
      </div>

      {/* Active Meetings Alert */}
      {activeMeetings.length > 0 && (
        <Alert className="mb-6 border-red-200 bg-red-50">
          <Video className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>
              You have {activeMeetings.length} active meeting{activeMeetings.length > 1 ? 's' : ''}
            </span>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => {
                const meeting = activeMeetings[0];
                setSelectedMeeting(meeting);
                setActiveMeeting(meeting.roomId);
              }}
            >
              Rejoin Meeting
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="upcoming" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="upcoming" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Upcoming ({upcomingMeetings.length})
          </TabsTrigger>
          <TabsTrigger value="recordings" className="flex items-center gap-2">
            <VideoIcon className="h-4 w-4" />
            Recordings ({completedMeetings.filter(m => m.recordingUrl).length})
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            History ({completedMeetings.length})
          </TabsTrigger>
        </TabsList>

        {/* Upcoming Meetings */}
        <TabsContent value="upcoming" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Scheduled Meetings</h2>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'list' ? 'calendar' : 'list')}
              >
                {viewMode === 'list' ? 'Calendar View' : 'List View'}
              </Button>
            </div>
          </div>

          {isLoading ? (
            <div className="grid gap-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="animate-pulse space-y-3">
                      <div className="h-4 bg-gray-200 rounded w-3/4" />
                      <div className="h-3 bg-gray-200 rounded w-1/2" />
                      <div className="h-3 bg-gray-200 rounded w-1/4" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : upcomingMeetings.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Upcoming Meetings</h3>
                <p className="text-muted-foreground mb-4">
                  Schedule your first meeting to get started with team collaboration
                </p>
                <Button onClick={() => setIsScheduleDialogOpen(true)}>
                  <CalendarPlus className="h-4 w-4 mr-2" />
                  Schedule Meeting
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {upcomingMeetings.map((meeting: Meeting) => (
                <Card key={meeting.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <h3 className="font-semibold text-lg">{meeting.title}</h3>
                        {meeting.description && (
                          <p className="text-muted-foreground">{meeting.description}</p>
                        )}
                        
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {format(parseISO(meeting.scheduledFor), 'PPP')}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {format(parseISO(meeting.scheduledFor), 'p')} ({meeting.duration} min)
                          </div>
                          {meeting.attendees && meeting.attendees.length > 0 && (
                            <div className="flex items-center gap-1">
                              <Users className="h-4 w-4" />
                              {meeting.attendees.length} attendees
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            navigator.clipboard.writeText(
                              `${window.location.origin}/meetings/${meeting.roomId}`
                            );
                            toast({
                              title: 'Link Copied',
                              description: 'Meeting link has been copied to clipboard',
                            });
                          }}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          size="sm"
                          onClick={() => {
                            setSelectedMeeting(meeting);
                            joinMeeting.mutate(meeting.id);
                          }}
                          disabled={isBefore(parseISO(meeting.scheduledFor), new Date())}
                        >
                          <Video className="h-4 w-4 mr-2" />
                          Join
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Recordings */}
        <TabsContent value="recordings" className="space-y-4">
          <h2 className="text-xl font-semibold">Meeting Recordings</h2>
          
          {completedMeetings.filter(m => m.recordingUrl).length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <VideoIcon className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Recordings Available</h3>
                <p className="text-muted-foreground">
                  Meeting recordings will appear here after your meetings are completed
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {completedMeetings
                .filter(m => m.recordingUrl)
                .map((meeting: Meeting) => (
                <Card key={meeting.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <h3 className="font-semibold">{meeting.title}</h3>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {format(parseISO(meeting.scheduledFor), 'PPP')}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {meeting.duration} minutes
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        {meeting.recordingUrl && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(meeting.recordingUrl, '_blank')}
                            >
                              <Play className="h-4 w-4 mr-2" />
                              Watch
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const link = document.createElement('a');
                                link.href = meeting.recordingUrl!;
                                link.download = `${meeting.title}-recording.mp4`;
                                link.click();
                              }}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* History */}
        <TabsContent value="history" className="space-y-4">
          <h2 className="text-xl font-semibold">Meeting History</h2>
          
          {completedMeetings.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Clock className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Meeting History</h3>
                <p className="text-muted-foreground">
                  Your completed meetings will appear here
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {completedMeetings.map((meeting: Meeting) => (
                <Card key={meeting.id}>
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{meeting.title}</h3>
                          <Badge variant={meeting.recordingUrl ? 'default' : 'secondary'}>
                            {meeting.recordingUrl ? 'Recorded' : 'No Recording'}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {format(parseISO(meeting.scheduledFor), 'PPP')}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {meeting.duration} minutes
                          </div>
                        </div>
                      </div>
                      
                      {meeting.recordingUrl && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(meeting.recordingUrl, '_blank')}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Recording
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Schedule Meeting Dialog */}
      <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Schedule New Meeting</DialogTitle>
            <DialogDescription>
              Create a new meeting and invite your team members
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="title">Meeting Title</Label>
              <Input
                id="title"
                value={newMeeting.title}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Team Standup"
              />
            </div>
            
            <div>
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={newMeeting.description}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Discuss project updates and roadmap"
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="datetime">Date & Time</Label>
              <Input
                id="datetime"
                type="datetime-local"
                value={newMeeting.scheduledFor}
                onChange={(e) => setNewMeeting(prev => ({ ...prev, scheduledFor: e.target.value }))}
                min={new Date().toISOString().slice(0, 16)}
              />
            </div>
            
            <div>
              <Label htmlFor="duration">Duration (minutes)</Label>
              <Select
                value={newMeeting.duration.toString()}
                onValueChange={(value) => setNewMeeting(prev => ({ ...prev, duration: parseInt(value) }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15">15 minutes</SelectItem>
                  <SelectItem value="30">30 minutes</SelectItem>
                  <SelectItem value="60">1 hour</SelectItem>
                  <SelectItem value="90">1.5 hours</SelectItem>
                  <SelectItem value="120">2 hours</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsScheduleDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => scheduleMeeting.mutate(newMeeting)}
              disabled={!newMeeting.title || !newMeeting.scheduledFor || scheduleMeeting.isPending}
            >
              {scheduleMeeting.isPending ? 'Scheduling...' : 'Schedule Meeting'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}