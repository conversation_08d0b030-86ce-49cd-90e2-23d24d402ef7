#!/usr/bin/env python3
"""
Simple Modal Endpoint Testing Script
"""

import requests
import json
import time

def test_health():
    """Test health endpoint"""
    print("🏥 Testing Health Endpoint...")
    try:
        url = "https://trade-digital--courseai-a100-simple-health.modal.run"
        response = requests.get(url, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_voices():
    """Test voice discovery endpoint"""
    print("\n🎤 Testing Voice Discovery...")
    try:
        url = "https://trade-digital--courseai-a100-simple-api-voices.modal.run"
        response = requests.get(url, timeout=60)
        print(f"Status Code: {response.status_code}")
        data = response.json()
        if data.get("success"):
            voices = data.get("voices", [])
            print(f"✅ Discovered {len(voices)} voices")
            for voice in voices[:3]:
                print(f"  - {voice.get('name', 'Unknown')}: {voice.get('id', 'No ID')}")
            return True
        else:
            print(f"❌ Voice discovery failed: {data}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_mistral():
    """Test Mistral LLM"""
    print("\n🧠 Testing Mistral LLM...")
    try:
        url = "https://trade-digital--courseai-a100-simple-api-mistral.modal.run"
        payload = {
            "prompt": "Explain machine learning in simple terms:",
            "max_tokens": 100,
            "temperature": 0.7
        }
        response = requests.post(url, json=payload, timeout=120)
        print(f"Status Code: {response.status_code}")
        data = response.json()
        if data.get("success"):
            text = data.get("generated_text", "")
            print(f"✅ Generated text ({len(text)} chars): {text[:100]}...")
            return True
        else:
            print(f"❌ Mistral failed: {data}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_tts():
    """Test TTS generation"""
    print("\n🔊 Testing TTS Generation...")
    try:
        url = "https://trade-digital--courseai-a100-simple-api-tts.modal.run"
        payload = {
            "text": "Hello, this is a test of the text-to-speech system.",
            "voice_id": "tts_models/en/ljspeech/tacotron2-DDC"
        }
        response = requests.post(url, json=payload, timeout=120)
        print(f"Status Code: {response.status_code}")
        data = response.json()
        if data.get("success"):
            audio_size = len(data.get("audio_base64", ""))
            print(f"✅ Generated audio: {audio_size} bytes (base64)")
            return True
        else:
            print(f"❌ TTS failed: {data}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_slides():
    """Test slide generation"""
    print("\n📊 Testing Slide Generation...")
    try:
        url = "https://trade-digital--courseai-a100-simple-api-slides.modal.run"
        markdown = """---
marp: true
---

# Test Slide
This is a test presentation.

---

# Second Slide
Thank you!
"""
        payload = {
            "markdown": markdown,
            "theme": "default"
        }
        response = requests.post(url, json=payload, timeout=60)
        print(f"Status Code: {response.status_code}")
        data = response.json()
        if data.get("success"):
            html_size = len(data.get("html_content", ""))
            slides = data.get("slide_count", 0)
            print(f"✅ Generated {slides} slides, HTML size: {html_size} chars")
            return True
        else:
            print(f"❌ Slides failed: {data}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Modal GPU Deployment\n")
    
    results = {}
    results["health"] = test_health()
    results["voices"] = test_voices()
    results["mistral"] = test_mistral()
    results["tts"] = test_tts()
    results["slides"] = test_slides()
    
    print("\n" + "="*50)
    print("📊 RESULTS SUMMARY")
    print("="*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test.upper()}: {status}")
    
    print(f"\nTOTAL: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All services are working correctly!")
    else:
        print(f"⚠️ {total - passed} services need attention")

if __name__ == "__main__":
    main()
