import { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { 
  Sparkles, 
  Save, 
  RefreshCw, 
  Play, 
  Eye, 
  Edit3,
  FileText,
  Mic,
  Video,
  Download,
  Copy,
  CheckCircle,
  Volume2,
  Plus,
  Presentation
} from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';

import { ModernVoicePanel } from './ModernVoicePanel';
import { SlideGenerationModal } from './SlideGenerationModal';

interface Lesson {
  id: string;
  title: string;
  description?: string;
}

interface Module {
  id: string;
  title: string;
  description?: string;
  lessons: Lesson[];
  complexity?: string;
}

interface ModuleScriptEditorProps {
  modules: Module[];
  courseScripts: Record<string, Record<string, string>>;
  onScriptChange: (moduleId: string, lessonId: string, script: string) => void;
  onGenerateScript: (moduleId: string, lessonId: string) => void;
  onSave: () => void;
  onBack: () => void;
  courseTitle?: string;
  courseDescription?: string;
}

interface GenerateScriptParams {
  moduleTitle: string;
  moduleDescription?: string;
  lessonTitle?: string;
  lessonDescription?: string;
  courseTitle?: string;
  targetAudience?: string;
  tone: string;
  wordCount: number;
}

export function ModuleScriptEditor({
  modules,
  courseScripts,
  onScriptChange,
  onGenerateScript,
  onSave,
  onBack,
  courseTitle = "Untitled Course",
  courseDescription = ""
}: ModuleScriptEditorProps) {
  const { toast } = useToast();
  const [activeModule, setActiveModule] = useState(0);
  const [activeLesson, setActiveLesson] = useState(0);
  const [generatingScripts, setGeneratingScripts] = useState<Record<string, boolean>>({});
  const [scriptSettings, setScriptSettings] = useState({
    tone: 'conversational',
    wordCount: 500,
    targetAudience: 'General learners'
  });

  const [bulkGeneration, setBulkGeneration] = useState({
    isRunning: false,
    currentModule: 0,
    currentLesson: 0,
    completed: 0,
    total: 0,
    errors: [] as string[],
    progress: 0
  });
  
  // Voice creation state
  const [showVoicePanel, setShowVoicePanel] = useState(false);
  const [voiceScripts, setVoiceScripts] = useState<Array<{
    moduleId: string;
    lessonId: string;
    text: string;
    moduleTitle: string;
    lessonTitle: string;
  }>>([]);

  // Slide generation state
  const [showSlideModal, setShowSlideModal] = useState(false);
  const [currentSlideData, setCurrentSlideData] = useState<{
    moduleId: string;
    moduleTitle: string;
    script: string;
  } | null>(null);

  // PDF Export mutation
  const pdfExportMutation = useMutation({
    mutationFn: async (courseData: any) => {
      const response = await fetch('/api/export/course/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ courseData }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }

      // Get the blob and create download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${courseData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_course.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
    onSuccess: () => {
      toast({
        title: "PDF Downloaded",
        description: "Course content has been successfully exported to PDF",
      });
    },
    onError: (error) => {
      toast({
        title: "Export Failed",
        description: "Failed to generate PDF. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Generate script for specific lesson
  const generateLessonScriptMutation = useMutation({
    mutationFn: async (params: GenerateScriptParams) => {
      const response = await apiRequest('POST', '/api/ai/generate-lesson-script', params);
      if (!response.ok) {
        throw new Error('Failed to generate script');
      }
      return await response.json();
    },
    onSuccess: (data, variables) => {
      const currentModuleData = modules[activeModule];
      const currentLessonData = currentModuleData?.lessons[activeLesson];
      const moduleId = currentModuleData?.id;
      const lessonId = currentLessonData?.id;
      
      console.log('Lesson script generation success:', data);
      console.log('Script content length:', data.script?.length);
      console.log('Module ID:', moduleId, 'Lesson ID:', lessonId);
      console.log('Active indices:', { activeModule, activeLesson, moduleData: currentModuleData, lessonData: currentLessonData });
      
      if (data.script && data.script.trim().length > 0) {
        // Force update the script content with immediate state refresh
        const scriptContent = data.script.trim();
        console.log('Applying script to UI:', { moduleId, lessonId, scriptLength: scriptContent.length });
        
        onScriptChange(moduleId, lessonId, scriptContent);
        
        // Force a re-render to ensure the rich editor updates
        setTimeout(() => {
          console.log('Script application complete - checking display');
          const editor = document.querySelector('[contenteditable="true"]') as HTMLDivElement;
          if (editor && (!editor.textContent || editor.textContent.includes('Generated script will appear here'))) {
            console.log('Force setting rich editor content');
            editor.textContent = scriptContent;
            editor.dispatchEvent(new Event('input', { bubbles: true }));
          }
        }, 50);
        
        toast({
          title: "Script Generated!",
          description: `Clean script created for "${variables.lessonTitle}" - ready for voice generation.`,
        });
      } else {
        console.error('Script generation issue - no content:', data);
        toast({
          title: "Generation Issue",
          description: "Script was generated but content is missing. Please try again.",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      console.error('Script generation failed:', error);
      toast({
        title: "Generation Failed",
        description: "Could not generate script. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Generate script for entire module
  const generateModuleScriptMutation = useMutation({
    mutationFn: async (params: GenerateScriptParams) => {
      const response = await apiRequest('POST', '/api/ai/generate-module-script', params);
      if (!response.ok) {
        throw new Error('Failed to generate module script');
      }
      return await response.json();
    },
    onSuccess: (data, variables) => {
      const moduleId = modules[activeModule].id;
      
      console.log('Module script generation success:', data);
      
      if (data.script && data.script.trim().length > 0) {
        // Apply the generated module script to the current lesson
        const scriptContent = data.script.trim();
        const firstLessonId = modules[activeModule].lessons[0]?.id;
        
        console.log('Module script success:', { moduleId, firstLessonId, scriptLength: scriptContent.length });
        
        if (firstLessonId) {
          onScriptChange(moduleId, firstLessonId, scriptContent);
          
          // Force update the rich editor for module overview
          setTimeout(() => {
            const editor = document.querySelector('[contenteditable="true"]') as HTMLDivElement;
            if (editor) {
              console.log('Setting module overview in rich editor');
              editor.textContent = scriptContent;
              editor.dispatchEvent(new Event('input', { bubbles: true }));
            }
          }, 50);
          
          toast({
            title: "Module Overview Generated!",
            description: `Clean module overview script created - ready for voice generation.`,
          });
        }
      } else {
        console.error('Module script response missing script content:', data);
        toast({
          title: "Generation Issue",
          description: "Module script was generated but content is missing. Please try again.",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      console.error('Module script generation failed:', error);
      toast({
        title: "Generation Failed",
        description: "Could not generate module script. Please try again.",
        variant: "destructive",
      });
    }
  });

  const handleGenerateLessonScript = () => {
    const module = modules[activeModule];
    const lesson = module.lessons[activeLesson];
    
    if (!lesson) return;

    const params: GenerateScriptParams = {
      moduleTitle: module.title,
      moduleDescription: module.description,
      lessonTitle: lesson.title,
      lessonDescription: lesson.description,
      tone: scriptSettings.tone,
      wordCount: scriptSettings.wordCount,
      targetAudience: scriptSettings.targetAudience
    };

    generateLessonScriptMutation.mutate(params);
  };

  const handleGenerateModuleScript = () => {
    const module = modules[activeModule];
    
    const params: GenerateScriptParams = {
      moduleTitle: module.title,
      moduleDescription: module.description,
      tone: scriptSettings.tone,
      wordCount: scriptSettings.wordCount * 2, // Module scripts are typically longer
      targetAudience: scriptSettings.targetAudience
    };

    generateModuleScriptMutation.mutate(params);
  };

  const handleGenerateAllScripts = async () => {
    if (bulkGeneration.isRunning) return;

    // Calculate total lessons
    const totalLessons = modules.reduce((total, module) => total + (module.lessons?.length || 0), 0);
    
    setBulkGeneration({
      isRunning: true,
      currentModule: 0,
      currentLesson: 0,
      completed: 0,
      total: totalLessons,
      errors: [],
      progress: 0
    });

    try {
      let completedCount = 0;
      
      for (let moduleIndex = 0; moduleIndex < modules.length; moduleIndex++) {
        const module = modules[moduleIndex];
        
        setBulkGeneration(prev => ({
          ...prev,
          currentModule: moduleIndex
        }));

        if (module.lessons) {
          for (let lessonIndex = 0; lessonIndex < module.lessons.length; lessonIndex++) {
            const lesson = module.lessons[lessonIndex];
            
            setBulkGeneration(prev => ({
              ...prev,
              currentLesson: lessonIndex,
              progress: Math.round((completedCount / totalLessons) * 100)
            }));

            // Skip if script already exists
            if (courseScripts[module.id]?.[lesson.id]?.trim()) {
              completedCount++;
              setBulkGeneration(prev => ({
                ...prev,
                completed: completedCount
              }));
              continue;
            }

            try {
              const params: GenerateScriptParams = {
                moduleTitle: module.title,
                moduleDescription: module.description,
                lessonTitle: lesson.title,
                lessonDescription: lesson.description,
                tone: scriptSettings.tone,
                wordCount: scriptSettings.wordCount,
                targetAudience: scriptSettings.targetAudience
              };

              await new Promise<void>((resolve, reject) => {
                generateLessonScriptMutation.mutate(params, {
                  onSuccess: (data) => {
                    if (data.script && data.script.trim()) {
                      onScriptChange(module.id, lesson.id, data.script.trim());
                      console.log(`Script successfully saved for ${module.title} - ${lesson.title}`, {
                        moduleId: module.id,
                        lessonId: lesson.id,
                        scriptLength: data.script.trim().length
                      });
                    }
                    completedCount++;
                    setBulkGeneration(prev => ({
                      ...prev,
                      completed: completedCount,
                      progress: Math.round((completedCount / totalLessons) * 100)
                    }));
                    resolve();
                  },
                  onError: (error: any) => {
                    setBulkGeneration(prev => ({
                      ...prev,
                      errors: [...prev.errors, `${module.title} - ${lesson.title}: ${error.message || 'Generation failed'}`]
                    }));
                    completedCount++;
                    setBulkGeneration(prev => ({
                      ...prev,
                      completed: completedCount,
                      progress: Math.round((completedCount / totalLessons) * 100)
                    }));
                    resolve(); // Continue with next script even if one fails
                  }
                });
              });

              // Add delay to prevent API rate limiting
              await new Promise(resolve => setTimeout(resolve, 2000));

            } catch (error) {
              console.error(`Failed to generate script for ${lesson.title}:`, error);
            }
          }
        }
      }

      const successCount = completedCount - bulkGeneration.errors.length;
      toast({
        title: "Script Generation Complete",
        description: `Generated ${successCount} out of ${totalLessons} scripts successfully`,
      });

    } catch (error) {
      toast({
        title: "Bulk Generation Failed",
        description: "An error occurred during script generation",
        variant: "destructive"
      });
    } finally {
      setBulkGeneration(prev => ({
        ...prev,
        isRunning: false,
        progress: 100
      }));
    }
  };

  const validateAllScripts = () => {
    const missingScripts: string[] = [];
    
    modules.forEach((module, moduleIndex) => {
      if (module.lessons) {
        module.lessons.forEach((lesson, lessonIndex) => {
          if (!courseScripts[module.id]?.[lesson.id]?.trim()) {
            missingScripts.push(`Module ${moduleIndex + 1}: ${lesson.title}`);
          }
        });
      }
    });

    return missingScripts;
  };

  const canProceedToNextStep = () => {
    return validateAllScripts().length === 0;
  };

  // Slide generation handler
  const handleGenerateSlides = (moduleId: string, lessonId: string) => {
    const module = modules.find(m => m.id === moduleId);
    const lesson = module?.lessons.find(l => l.id === lessonId);
    const script = courseScripts[moduleId]?.[lessonId];

    if (!module || !lesson || !script?.trim()) {
      toast({
        title: "No Script Available",
        description: "Please write or generate a script first before creating slides.",
        variant: "destructive"
      });
      return;
    }

    setCurrentSlideData({
      moduleId,
      moduleTitle: module.title,
      script: script.trim()
    });
    setShowSlideModal(true);
  };

  const handleSlidesGenerated = (slideData: any) => {
    toast({
      title: "Slides Generated Successfully",
      description: "Your presentation slides have been created and saved to the Media Library.",
    });
    setShowSlideModal(false);
    setCurrentSlideData(null);
  };

  const currentModule = modules && modules.length > 0 ? modules[activeModule] : null;
  const currentLesson = currentModule && currentModule.lessons && currentModule.lessons.length > 0 
    ? currentModule.lessons[activeLesson] 
    : null;
    
  // Force reactive currentScript by creating a dependency on courseScripts
  const currentScript = useMemo(() => {
    if (!currentModule || !currentLesson) return '';
    return courseScripts[currentModule.id]?.[currentLesson.id] || '';
  }, [courseScripts, currentModule?.id, currentLesson?.id]);

  // Debug logging to see script state
  console.log('Current script state:', {
    moduleId: currentModule?.id,
    lessonId: currentLesson?.id,
    scriptLength: currentScript?.length,
    hasScript: !!currentScript,
    courseScripts: courseScripts
  });

  const handleScriptChange = (value: string) => {
    if (currentModule && currentLesson) {
      onScriptChange(currentModule.id, currentLesson.id, value);
    }
  };

  const copyScriptToClipboard = () => {
    if (currentScript) {
      navigator.clipboard.writeText(currentScript);
      toast({
        title: "Copied!",
        description: "Script copied to clipboard",
      });
    }
  };

  const getScriptWordCount = (script: string) => {
    if (!script) return 0;
    return script.trim().split(/\s+/).filter(word => word.length > 0).length;
  };

  // Prepare course data for PDF export
  const prepareCourseDataForPDF = () => {
    return {
      title: courseTitle,
      description: courseDescription,
      modules: modules.map((module, moduleIndex) => ({
        title: module.title,
        description: module.description || '',
        lessons: module.lessons.map((lesson, lessonIndex) => ({
          title: lesson.title,
          description: lesson.description || '',
          script: courseScripts[module.id]?.[lesson.id] || ''
        }))
      }))
    };
  };

  // Handle PDF download
  const handleDownloadPDF = () => {
    const courseData = prepareCourseDataForPDF();
    
    // Check if there's any content to export
    const hasScripts = modules.some(module => 
      module.lessons.some(lesson => 
        courseScripts[module.id]?.[lesson.id]?.trim()
      )
    );

    if (!hasScripts) {
      toast({
        title: "No Scripts Available",
        description: "Generate some lesson scripts before downloading the PDF",
        variant: "destructive",
      });
      return;
    }

    pdfExportMutation.mutate(courseData);
  };

  // AI-powered lesson addition
  const addLessonMutation = useMutation({
    mutationFn: async (moduleIndex: number) => {
      const module = modules[moduleIndex];
      const response = await apiRequest('POST', '/api/ai/generate-lesson', {
        moduleTitle: module.title,
        moduleDescription: module.description,
        existingLessons: module.lessons?.map(l => ({ title: l.title, description: l.description })) || [],
        courseTitle: modules[0]?.title || 'Course',
        targetAudience: scriptSettings.targetAudience
      });
      if (!response.ok) {
        throw new Error('Failed to generate lesson');
      }
      return await response.json();
    },
    onSuccess: (data, moduleIndex) => {
      const newLesson = {
        id: `lesson-${modules[moduleIndex].id}-${Date.now()}`,
        title: data.title,
        description: data.description
      };
      
      toast({
        title: "Lesson Added",
        description: `"${data.title}" has been added to ${modules[moduleIndex].title}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add lesson",
        variant: "destructive",
      });
    }
  });

  const handleAddLesson = (moduleIndex: number) => {
    addLessonMutation.mutate(moduleIndex);
  };

  const getModuleCompletionBadge = (module: Module, moduleIndex: number) => {
    if (!module.lessons || module.lessons.length === 0) {
      return { variant: "outline" as const, text: "No lessons" };
    }
    
    const completedLessons = module.lessons.filter(lesson => 
      courseScripts[module.id]?.[lesson.id]?.trim()
    ).length;
    
    if (completedLessons === 0) {
      return { variant: "outline" as const, text: "Not started" };
    } else if (completedLessons === module.lessons.length) {
      return { variant: "default" as const, text: "Complete" };
    } else {
      return { variant: "secondary" as const, text: `${completedLessons}/${module.lessons.length}` };
    }
  };

  const getCompletionProgress = () => {
    let totalLessons = 0;
    let completedLessons = 0;

    modules.forEach(module => {
      module.lessons.forEach(lesson => {
        totalLessons++;
        if (courseScripts[module.id]?.[lesson.id]?.trim()) {
          completedLessons++;
        }
      });
    });

    return { completed: completedLessons, total: totalLessons };
  };

  // Voice creation functions
  const prepareScriptsForVoice = () => {
    const scripts: Array<{
      moduleId: string;
      lessonId: string;
      text: string;
      moduleTitle: string;
      lessonTitle: string;
    }> = [];

    modules.forEach(module => {
      module.lessons.forEach(lesson => {
        const script = courseScripts[module.id]?.[lesson.id];
        if (script?.trim()) {
          scripts.push({
            moduleId: module.id,
            lessonId: lesson.id,
            text: script.trim(),
            moduleTitle: module.title,
            lessonTitle: lesson.title
          });
        }
      });
    });

    return scripts;
  };

  const handleCreateVoices = () => {
    const scripts = prepareScriptsForVoice();
    if (scripts.length === 0) {
      toast({
        title: "No Scripts Available",
        description: "Please generate some scripts first before creating voices.",
        variant: "destructive",
      });
      return;
    }

    setVoiceScripts(scripts);
    setShowVoicePanel(true);
  };

  const handleVoiceGenerated = (results: any[]) => {
    console.log('Voice generation results:', results);
    const successCount = results.filter(r => r.success).length;
    
    toast({
      title: "Voice Creation Complete!",
      description: `Successfully created ${successCount} audio files for your course.`,
    });
    
    setShowVoicePanel(false);
  };

  const progress = getCompletionProgress();

  // Early return if no modules are provided
  if (!modules || modules.length === 0) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <h2 className="text-xl font-semibold mb-2">No Modules Available</h2>
          <p className="text-muted-foreground mb-4">
            Please generate a course structure first to create scripts.
          </p>
          <Button variant="outline" onClick={onBack}>
            Back to Course Structure
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Module Scripts</h2>
          <p className="text-sm text-muted-foreground">
            Generate clean, voice-ready scripts for each lesson. Progress: {progress.completed}/{progress.total} lessons
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <FileText className="h-3 w-3" />
            {progress.completed}/{progress.total} Complete
          </Badge>
          <Button 
            onClick={handleGenerateAllScripts}
            disabled={bulkGeneration.isRunning}
            variant="outline"
            className="flex items-center gap-2"
          >
            {bulkGeneration.isRunning ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Generating All Scripts...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4" />
                Generate All Scripts
              </>
            )}
          </Button>
          {progress.completed > 0 && (
            <>
              <Button 
                onClick={handleDownloadPDF}
                disabled={pdfExportMutation.isPending}
                className="flex items-center gap-2"
                variant="outline"
              >
                {pdfExportMutation.isPending ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    Generating PDF...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    Download Course PDF
                  </>
                )}
              </Button>
              <Button 
                onClick={handleCreateVoices}
                className="flex items-center gap-2"
                variant="default"
              >
                <Volume2 className="h-4 w-4" />
                Create Voices
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Bulk Generation Progress */}
      {bulkGeneration.isRunning && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-blue-900">Generating All Scripts</h3>
                <Badge variant="outline" className="bg-blue-100 text-blue-800">
                  {bulkGeneration.completed}/{bulkGeneration.total}
                </Badge>
              </div>
              
              {/* Progress Bar */}
              <div className="w-full bg-blue-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(bulkGeneration.completed / bulkGeneration.total) * 100}%` }}
                />
              </div>
              
              {/* Current Generation Status */}
              <div className="text-sm text-blue-700">
                {bulkGeneration.completed < bulkGeneration.total ? (
                  <>
                    Generating script for: <span className="font-medium">
                      {modules[bulkGeneration.currentModule]?.title} - {modules[bulkGeneration.currentModule]?.lessons?.[bulkGeneration.currentLesson]?.title}
                    </span>
                  </>
                ) : (
                  <span className="text-green-700 font-medium">All scripts generated successfully!</span>
                )}
              </div>
              
              {/* Error Summary */}
              {bulkGeneration.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <p className="text-sm text-red-800 font-medium mb-2">
                    {bulkGeneration.errors.length} script(s) failed to generate:
                  </p>
                  <ul className="text-xs text-red-700 space-y-1">
                    {bulkGeneration.errors.slice(0, 3).map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                    {bulkGeneration.errors.length > 3 && (
                      <li>• ... and {bulkGeneration.errors.length - 3} more</li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Module Navigation - Clean & Intuitive Design */}
        <div className="lg:col-span-3">
          <Card className="h-fit">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">Course Structure</CardTitle>
              <CardDescription>
                Navigate through modules and lessons
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-[70vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {modules.map((module, moduleIndex) => (
                  <div key={module.id} className="border-b last:border-b-0">
                    {/* Module Header - Always Visible */}
                    <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-gray-900 text-sm leading-tight">
                            Module {moduleIndex + 1}: {module.title}
                          </h3>
                          {module.description && (
                            <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                              {module.description}
                            </p>
                          )}
                          <div className="flex items-center gap-2 mt-2">
                            <Badge variant="outline" className="text-xs">
                              {module.lessons?.length || 0} lessons
                            </Badge>
                            <Badge variant={getModuleCompletionBadge(module, moduleIndex).variant} className="text-xs">
                              {getModuleCompletionBadge(module, moduleIndex).text}
                            </Badge>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-white/60"
                          onClick={() => handleAddLesson(moduleIndex)}
                          title="Add New Lesson"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    {/* Lessons Grid - Always Visible */}
                    {module.lessons && module.lessons.length > 0 && (
                      <div className="p-3 space-y-2">
                        {module.lessons.map((lesson, lessonIndex) => {
                          const isActive = activeModule === moduleIndex && activeLesson === lessonIndex;
                          const hasScript = courseScripts[module.id]?.[lesson.id]?.trim();
                          
                          return (
                            <button
                              key={lesson.id || `lesson-${lessonIndex}`}
                              className={`
                                w-full p-3 rounded-lg border transition-all duration-200 text-left group
                                ${isActive 
                                  ? 'bg-blue-100 border-blue-300 shadow-sm ring-2 ring-blue-200' 
                                  : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm'
                                }
                              `}
                              onClick={() => {
                                setActiveModule(moduleIndex);
                                setActiveLesson(lessonIndex);
                              }}
                            >
                              <div className="flex items-start gap-3">
                                <div className={`
                                  flex items-center justify-center w-7 h-7 rounded-full text-xs font-medium flex-shrink-0
                                  ${isActive 
                                    ? 'bg-blue-600 text-white' 
                                    : hasScript 
                                      ? 'bg-green-100 text-green-700'
                                      : 'bg-gray-100 text-gray-600'
                                  }
                                `}>
                                  {lessonIndex + 1}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h4 className={`
                                    font-medium text-sm leading-tight
                                    ${isActive ? 'text-blue-900' : 'text-gray-900'}
                                  `}>
                                    {lesson.title || 'Untitled Lesson'}
                                  </h4>
                                  {lesson.description && (
                                    <p className={`
                                      text-xs mt-1 line-clamp-2
                                      ${isActive ? 'text-blue-700' : 'text-gray-600'}
                                    `}>
                                      {lesson.description}
                                    </p>
                                  )}
                                  <div className="flex items-center gap-2 mt-2">
                                    {hasScript ? (
                                      <Badge variant="default" className="text-xs bg-green-100 text-green-800 border-green-200">
                                        <CheckCircle className="h-3 w-3 mr-1" />
                                        Script Ready
                                      </Badge>
                                    ) : (
                                      <Badge variant="outline" className="text-xs">
                                        No Script
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    )}
                    
                    {/* Empty State */}
                    {(!module.lessons || module.lessons.length === 0) && (
                      <div className="p-6 text-center">
                        <div className="text-gray-400 mb-2">
                          <FileText className="h-8 w-8 mx-auto" />
                        </div>
                        <p className="text-sm text-gray-500 mb-3">No lessons in this module</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAddLesson(moduleIndex)}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add First Lesson
                        </Button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Script Editor */}
        <div className="lg:col-span-9">
          <Card className="h-fit">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Edit3 className="h-5 w-5" />
                    {currentLesson?.title}
                  </CardTitle>
                  <CardDescription>
                    Module {activeModule + 1}: {currentModule?.title}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  {currentScript && (
                    <Badge variant="outline">
                      {getScriptWordCount(currentScript)} words
                    </Badge>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyScriptToClipboard}
                    disabled={!currentScript}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Tab Navigation */}
              <Tabs defaultValue="editor" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="editor" className="flex items-center gap-2">
                    <Edit3 className="h-4 w-4" />
                    Script Editor
                  </TabsTrigger>
                  <TabsTrigger value="voice" className="flex items-center gap-2" disabled={!currentScript?.trim()}>
                    <Mic className="h-4 w-4" />
                    Voice Generation
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="editor" className="space-y-4">
                  {/* Script Generation Controls */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                <div>
                  <label className="text-sm font-medium">Tone</label>
                  <select
                    value={scriptSettings.tone}
                    onChange={(e) => setScriptSettings(prev => ({ ...prev, tone: e.target.value }))}
                    className="w-full mt-1 px-3 py-1.5 border rounded text-sm"
                  >
                    <option value="conversational">Conversational</option>
                    <option value="professional">Professional</option>
                    <option value="friendly">Friendly</option>
                    <option value="academic">Academic</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">Word Count</label>
                  <select
                    value={scriptSettings.wordCount}
                    onChange={(e) => setScriptSettings(prev => ({ ...prev, wordCount: parseInt(e.target.value) }))}
                    className="w-full mt-1 px-3 py-1.5 border rounded text-sm"
                  >
                    <option value={300}>Short (300 words)</option>
                    <option value={500}>Medium (500 words)</option>
                    <option value={800}>Long (800 words)</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium">Target Audience</label>
                  <select
                    value={scriptSettings.targetAudience}
                    onChange={(e) => setScriptSettings(prev => ({ ...prev, targetAudience: e.target.value }))}
                    className="w-full mt-1 px-3 py-1.5 border rounded text-sm"
                  >
                    <option value="Beginners">Beginners</option>
                    <option value="Intermediate learners">Intermediate</option>
                    <option value="Advanced professionals">Advanced</option>
                    <option value="General learners">General</option>
                  </select>
                </div>
              </div>

              {/* Generation Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={handleGenerateLessonScript}
                  disabled={generateLessonScriptMutation.isPending}
                  className="flex-1"
                >
                  {generateLessonScriptMutation.isPending ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Generating Lesson Script...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Lesson Script
                    </>
                  )}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => handleGenerateSlides(currentModule?.id || '', currentLesson?.id || '')}
                  disabled={!currentScript?.trim()}
                  title={!currentScript?.trim() ? 'Please write or generate a script first' : 'Generate presentation slides from script'}
                >
                  <Presentation className="h-4 w-4 mr-2" />
                  Generate Slides
                </Button>

              </div>

              {/* Rich Script Editor */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Script Content</label>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Mic className="h-3 w-3" />
                    Voice-ready format
                  </div>
                </div>
                
                {/* Formatting Toolbar */}
                <div className="flex items-center gap-1 p-2 border rounded-t-lg bg-gray-50 flex-wrap">
                  <button 
                    type="button"
                    className="p-1.5 hover:bg-gray-200 rounded text-sm font-bold"
                    onClick={() => document.execCommand('bold')}
                    title="Bold"
                  >
                    B
                  </button>
                  <button 
                    type="button"
                    className="p-1.5 hover:bg-gray-200 rounded text-sm italic"
                    onClick={() => document.execCommand('italic')}
                    title="Italic"
                  >
                    I
                  </button>
                  <button 
                    type="button"
                    className="p-1.5 hover:bg-gray-200 rounded text-sm underline"
                    onClick={() => document.execCommand('underline')}
                    title="Underline"
                  >
                    U
                  </button>
                  <div className="w-px h-6 bg-gray-300 mx-1"></div>
                  
                  <select 
                    className="text-xs px-2 py-1 border rounded"
                    onChange={(e) => document.execCommand('fontSize', false, e.target.value)}
                  >
                    <option value="3">12px</option>
                    <option value="4" selected>14px</option>
                    <option value="5">16px</option>
                    <option value="6">18px</option>
                    <option value="7">24px</option>
                  </select>
                  
                  <select 
                    className="text-xs px-2 py-1 border rounded ml-1"
                    onChange={(e) => document.execCommand('fontName', false, e.target.value)}
                  >
                    <option value="Arial">Arial</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Times New Roman">Times</option>
                    <option value="Verdana">Verdana</option>
                    <option value="Courier New">Courier</option>
                  </select>
                  
                  <div className="w-px h-6 bg-gray-300 mx-1"></div>
                  
                  <button 
                    type="button"
                    className="p-1.5 hover:bg-gray-200 rounded text-xs"
                    onClick={() => document.execCommand('justifyLeft')}
                    title="Align Left"
                  >
                    ←
                  </button>
                  <button 
                    type="button"
                    className="p-1.5 hover:bg-gray-200 rounded text-xs"
                    onClick={() => document.execCommand('justifyCenter')}
                    title="Center"
                  >
                    ↔
                  </button>
                  <button 
                    type="button"
                    className="p-1.5 hover:bg-gray-200 rounded text-xs"
                    onClick={() => document.execCommand('justifyRight')}
                    title="Align Right"
                  >
                    →
                  </button>
                  
                  <div className="w-px h-6 bg-gray-300 mx-1"></div>
                  
                  <button 
                    type="button"
                    className="p-1.5 hover:bg-gray-200 rounded text-xs"
                    onClick={() => document.execCommand('insertUnorderedList')}
                    title="Bullet List"
                  >
                    •
                  </button>
                  <button 
                    type="button"
                    className="p-1.5 hover:bg-gray-200 rounded text-xs"
                    onClick={() => document.execCommand('insertOrderedList')}
                    title="Numbered List"
                  >
                    1.
                  </button>
                </div>
                
                {/* Rich Text Editor */}
                <div 
                  contentEditable
                  className="min-h-[300px] p-4 border border-t-0 rounded-b-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white overflow-y-auto"
                  style={{ 
                    fontSize: '14px',
                    lineHeight: '1.6',
                    fontFamily: 'Arial, sans-serif'
                  }}
                  suppressContentEditableWarning={true}
                  onInput={(e) => {
                    const content = e.currentTarget.textContent || '';
                    handleScriptChange(content);
                  }}
                  onPaste={(e) => {
                    e.preventDefault();
                    const text = e.clipboardData.getData('text/plain');
                    document.execCommand('insertText', false, text);
                  }}
                  dangerouslySetInnerHTML={{ 
                    __html: currentScript || '<p style="color: #999; font-style: italic;">Generated script will appear here, or you can write your own...</p>' 
                  }}
                />
                
                <div className="flex justify-between items-center text-xs text-muted-foreground">
                  <span>Rich editor - full formatting control like Word</span>
                  <span>{getScriptWordCount(currentScript)} words</span>
                </div>
              </div>

              {/* Preview Actions */}
              {currentScript && (
                <div className="flex gap-2 pt-4 border-t">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                  <Button variant="outline" size="sm">
                    <Video className="h-4 w-4 mr-2" />
                    Generate Video
                  </Button>
                </div>
              )}
                </TabsContent>

                <TabsContent value="voice" className="space-y-4">
                  {currentScript && currentLesson && currentModule ? (
                    <ModernVoicePanel
                      script={currentScript}
                      moduleId={currentModule.id}
                      lessonId={currentLesson.id}
                      moduleTitle={currentModule.title}
                      lessonTitle={currentLesson.title}
                      onVoiceGenerated={(audioUrl: string, audioData: any) => {
                        console.log('Voice generated:', { audioUrl, audioData });
                        // Handle voice generation completion here
                      }}
                    />
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Mic className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>Please generate a script first to enable voice generation.</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Validation Warning */}
      {(() => {
        const missingScripts = validateAllScripts();
        return missingScripts.length > 0 && (
          <Card className="bg-amber-50 border-amber-200">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs font-bold">!</span>
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium text-amber-900">
                    Missing Scripts ({missingScripts.length} remaining)
                  </h3>
                  <p className="text-sm text-amber-800">
                    Please generate scripts for all lessons before proceeding to the next step.
                  </p>
                  <div className="space-y-1">
                    {missingScripts.slice(0, 5).map((script, index) => (
                      <p key={index} className="text-xs text-amber-700">• {script}</p>
                    ))}
                    {missingScripts.length > 5 && (
                      <p className="text-xs text-amber-700">• ... and {missingScripts.length - 5} more</p>
                    )}
                  </div>
                  <Button 
                    onClick={handleGenerateAllScripts}
                    disabled={bulkGeneration.isRunning}
                    size="sm"
                    className="bg-amber-600 hover:bg-amber-700 text-white"
                  >
                    {bulkGeneration.isRunning ? (
                      <>
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-3 w-3 mr-1" />
                        Generate Missing Scripts
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })()}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Back to Structure
        </Button>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onSave}>
            <Save className="h-4 w-4 mr-2" />
            Save Scripts
          </Button>
          <Button 
            onClick={onSave}
            disabled={!canProceedToNextStep()}
            className={!canProceedToNextStep() ? 'opacity-50 cursor-not-allowed' : ''}
          >
            Continue to Media
            {!canProceedToNextStep() && (
              <span className="ml-2 text-xs">({validateAllScripts().length} scripts missing)</span>
            )}
          </Button>
        </div>
      </div>

      {/* Voice Selection Panel Modal */}
      {showVoicePanel && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-2xl w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Voice Generation Complete</h3>
            <p className="text-sm text-gray-600 mb-4">
              Voice narration has been generated successfully for your lesson scripts.
            </p>
            <button
              onClick={() => setShowVoicePanel(false)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Continue
            </button>
          </div>
        </div>
      )}

      {/* Slide Generation Modal */}
      {showSlideModal && currentSlideData && (
        <SlideGenerationModal
          isOpen={showSlideModal}
          onClose={() => {
            setShowSlideModal(false);
            setCurrentSlideData(null);
          }}
          courseTitle={courseTitle}
          lessonTitle={currentSlideData.moduleTitle}
          initialScript={currentSlideData.script}
          onSlidesGenerated={handleSlidesGenerated}
        />
      )}
    </div>
  );
}