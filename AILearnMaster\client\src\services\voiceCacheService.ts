import { VoiceModel } from '@/types/voice';

interface VoiceCacheData {
  voices: VoiceModel[];
  statistics: {
    total: number;
    byService: { [key: string]: number };
    byLanguage: { [key: string]: number };
    byCategory: { [key: string]: number };
    byQuality: { [key: string]: number };
  };
  lastUpdated: string;
  version: string;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class VoiceCacheService {
  private readonly CACHE_PREFIX = 'ailearn_voice_';
  private readonly DEFAULT_TTL = 24 * 60 * 60 * 1000; // 24 hours
  private readonly CACHE_VERSION = '1.0';

  /**
   * Get cached voice data
   */
  async getVoices(): Promise<VoiceCacheData | null> {
    try {
      const cached = this.getFromCache<VoiceCacheData>('voices');
      if (cached && this.isValidCache(cached)) {
        return cached.data;
      }
      return null;
    } catch (error) {
      console.warn('Failed to get cached voices:', error);
      return null;
    }
  }

  /**
   * Cache voice data
   */
  async setVoices(data: VoiceCacheData): Promise<void> {
    try {
      const cacheData: CacheEntry<VoiceCacheData> = {
        data: {
          ...data,
          lastUpdated: new Date().toISOString(),
          version: this.CACHE_VERSION
        },
        timestamp: Date.now(),
        ttl: this.DEFAULT_TTL
      };
      
      this.setToCache('voices', cacheData);
    } catch (error) {
      console.warn('Failed to cache voices:', error);
    }
  }

  /**
   * Get cached voice statistics
   */
  async getVoiceStatistics(): Promise<VoiceCacheData['statistics'] | null> {
    const cached = await this.getVoices();
    return cached?.statistics || null;
  }

  /**
   * Get voices for a specific service from cache
   */
  async getVoicesByService(service: string): Promise<VoiceModel[]> {
    const cached = await this.getVoices();
    if (!cached) return [];
    
    return cached.voices.filter(voice => voice.service === service);
  }

  /**
   * Get voices for a specific language from cache
   */
  async getVoicesByLanguage(language: string): Promise<VoiceModel[]> {
    const cached = await this.getVoices();
    if (!cached) return [];
    
    return cached.voices.filter(voice => voice.language === language);
  }

  /**
   * Search voices in cache
   */
  async searchVoices(query: string): Promise<VoiceModel[]> {
    const cached = await this.getVoices();
    if (!cached) return [];
    
    const searchTerm = query.toLowerCase();
    return cached.voices.filter(voice => 
      voice.name.toLowerCase().includes(searchTerm) ||
      voice.description.toLowerCase().includes(searchTerm) ||
      voice.language.toLowerCase().includes(searchTerm) ||
      voice.category.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Cache voice test results
   */
  async setVoiceTestResults(results: Array<{
    voiceId: string;
    available: boolean;
    error?: string;
    testedAt: string;
  }>): Promise<void> {
    try {
      const cacheData: CacheEntry<typeof results> = {
        data: results,
        timestamp: Date.now(),
        ttl: 12 * 60 * 60 * 1000 // 12 hours for test results
      };
      
      this.setToCache('voice_tests', cacheData);
    } catch (error) {
      console.warn('Failed to cache voice test results:', error);
    }
  }

  /**
   * Get cached voice test results
   */
  async getVoiceTestResults(): Promise<Array<{
    voiceId: string;
    available: boolean;
    error?: string;
    testedAt: string;
  }> | null> {
    try {
      const cached = this.getFromCache<Array<{
        voiceId: string;
        available: boolean;
        error?: string;
        testedAt: string;
      }>>('voice_tests');
      
      if (cached && this.isValidCache(cached)) {
        return cached.data;
      }
      return null;
    } catch (error) {
      console.warn('Failed to get cached voice test results:', error);
      return null;
    }
  }

  /**
   * Cache user voice preferences
   */
  async setUserPreferences(preferences: {
    favoriteVoices: string[];
    recentlyUsed: string[];
    defaultService: string;
    defaultLanguage: string;
  }): Promise<void> {
    try {
      const cacheData: CacheEntry<typeof preferences> = {
        data: preferences,
        timestamp: Date.now(),
        ttl: 30 * 24 * 60 * 60 * 1000 // 30 days for user preferences
      };
      
      this.setToCache('user_preferences', cacheData);
    } catch (error) {
      console.warn('Failed to cache user preferences:', error);
    }
  }

  /**
   * Get cached user voice preferences
   */
  async getUserPreferences(): Promise<{
    favoriteVoices: string[];
    recentlyUsed: string[];
    defaultService: string;
    defaultLanguage: string;
  } | null> {
    try {
      const cached = this.getFromCache<{
        favoriteVoices: string[];
        recentlyUsed: string[];
        defaultService: string;
        defaultLanguage: string;
      }>('user_preferences');
      
      if (cached && this.isValidCache(cached)) {
        return cached.data;
      }
      return null;
    } catch (error) {
      console.warn('Failed to get cached user preferences:', error);
      return null;
    }
  }

  /**
   * Clear all voice-related cache
   */
  async clearCache(): Promise<void> {
    try {
      const keys = Object.keys(localStorage).filter(key => 
        key.startsWith(this.CACHE_PREFIX)
      );
      
      keys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.warn('Failed to clear voice cache:', error);
    }
  }

  /**
   * Clear expired cache entries
   */
  async clearExpiredCache(): Promise<void> {
    try {
      const keys = Object.keys(localStorage).filter(key => 
        key.startsWith(this.CACHE_PREFIX)
      );
      
      keys.forEach(key => {
        try {
          const cached = JSON.parse(localStorage.getItem(key) || '{}');
          if (!this.isValidCache(cached)) {
            localStorage.removeItem(key);
          }
        } catch {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Failed to clear expired cache:', error);
    }
  }

  /**
   * Get cache size and statistics
   */
  getCacheInfo(): {
    totalEntries: number;
    totalSize: number;
    entries: Array<{
      key: string;
      size: number;
      lastUpdated: string;
      isExpired: boolean;
    }>;
  } {
    const entries: Array<{
      key: string;
      size: number;
      lastUpdated: string;
      isExpired: boolean;
    }> = [];
    
    let totalSize = 0;
    
    try {
      const keys = Object.keys(localStorage).filter(key => 
        key.startsWith(this.CACHE_PREFIX)
      );
      
      keys.forEach(key => {
        try {
          const value = localStorage.getItem(key) || '';
          const size = new Blob([value]).size;
          const cached = JSON.parse(value);
          
          entries.push({
            key: key.replace(this.CACHE_PREFIX, ''),
            size,
            lastUpdated: new Date(cached.timestamp).toISOString(),
            isExpired: !this.isValidCache(cached)
          });
          
          totalSize += size;
        } catch {
          // Invalid cache entry
        }
      });
    } catch (error) {
      console.warn('Failed to get cache info:', error);
    }
    
    return {
      totalEntries: entries.length,
      totalSize,
      entries
    };
  }

  private getFromCache<T>(key: string): CacheEntry<T> | null {
    try {
      const cached = localStorage.getItem(this.CACHE_PREFIX + key);
      if (!cached) return null;
      
      return JSON.parse(cached) as CacheEntry<T>;
    } catch {
      return null;
    }
  }

  private setToCache<T>(key: string, data: CacheEntry<T>): void {
    try {
      localStorage.setItem(this.CACHE_PREFIX + key, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to set cache:', error);
    }
  }

  private isValidCache<T>(cached: CacheEntry<T>): boolean {
    if (!cached || !cached.timestamp || !cached.ttl) return false;
    
    const now = Date.now();
    const isExpired = (now - cached.timestamp) > cached.ttl;
    
    // Also check version compatibility for voice data
    if (cached.data && typeof cached.data === 'object' && 'version' in cached.data) {
      const dataVersion = (cached.data as any).version;
      if (dataVersion !== this.CACHE_VERSION) {
        return false;
      }
    }
    
    return !isExpired;
  }
}

// Export singleton instance
export const voiceCacheService = new VoiceCacheService();
