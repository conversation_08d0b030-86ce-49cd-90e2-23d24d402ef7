import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  ImageIcon, 
  Wand2, 
  Download, 
  Settings, 
  Sparkles, 
  Palette, 
  Camera, 
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  Copy,
  Eye,
  Trash2,
  RotateCcw,
  Save,
  Share2,
  Grid3X3,
  Maximize2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface GeneratedImage {
  id: string;
  prompt: string;
  imageUrl: string;
  model: string;
  style: string;
  createdAt: string;
  width: number;
  height: number;
  seed?: number;
}

interface ImagePreset {
  id: string;
  name: string;
  description: string;
  prompt: string;
  style: string;
  category: string;
  thumbnail: string;
}

export default function AIImageGeneratorPage() {
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('photorealistic');
  const [selectedSize, setSelectedSize] = useState('1024x1024');
  const [selectedModel, setSelectedModel] = useState('sdxl');
  const [numImages, setNumImages] = useState(1);
  const [seed, setSeed] = useState('');
  const [guidanceScale, setGuidanceScale] = useState(7.5);
  const [steps, setSteps] = useState(30);
  const [selectedPreset, setSelectedPreset] = useState<ImagePreset | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch generated images
  const { data: generatedImages = [], isLoading: imagesLoading } = useQuery({
    queryKey: ['/api/ai/images'],
    queryFn: () => apiRequest('/api/ai/images')
  });

  // Generate image mutation
  const generateImageMutation = useMutation({
    mutationFn: async (imageData: any) => {
      return apiRequest('/api/ai/generate-image', {
        method: 'POST',
        body: JSON.stringify(imageData)
      });
    },
    onSuccess: () => {
      toast({
        title: "Image Generated",
        description: "Your AI-generated image is ready!",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/ai/images'] });
    },
    onError: (error: any) => {
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate image",
        variant: "destructive",
      });
    }
  });

  const imageStyles = [
    { id: 'photorealistic', name: 'Photorealistic', description: 'Lifelike and detailed' },
    { id: 'artistic', name: 'Artistic', description: 'Creative and stylized' },
    { id: 'cartoon', name: 'Cartoon', description: 'Fun and animated style' },
    { id: 'abstract', name: 'Abstract', description: 'Creative and conceptual' },
    { id: 'vintage', name: 'Vintage', description: 'Classic and retro feel' },
    { id: 'minimalist', name: 'Minimalist', description: 'Clean and simple' },
    { id: 'cyberpunk', name: 'Cyberpunk', description: 'Futuristic and neon' },
    { id: 'watercolor', name: 'Watercolor', description: 'Soft painting style' }
  ];

  const imageSizes = [
    { id: '512x512', name: '512×512', description: 'Square (1:1)' },
    { id: '768x768', name: '768×768', description: 'Large Square (1:1)' },
    { id: '1024x1024', name: '1024×1024', description: 'XL Square (1:1)' },
    { id: '1024x768', name: '1024×768', description: 'Landscape (4:3)' },
    { id: '768x1024', name: '768×1024', description: 'Portrait (3:4)' },
    { id: '1536x1024', name: '1536×1024', description: 'Wide (3:2)' }
  ];

  const presetTemplates: ImagePreset[] = [
    {
      id: '1',
      name: 'Course Thumbnail',
      description: 'Professional course thumbnail design',
      prompt: 'Modern educational course thumbnail, professional design, clean layout, vibrant colors, learning concept',
      style: 'photorealistic',
      category: 'Education',
      thumbnail: '/api/placeholder/300/200'
    },
    {
      id: '2',
      name: 'Tech Presentation',
      description: 'Technology-focused presentation background',
      prompt: 'Clean technology background, circuit patterns, blue and white color scheme, professional presentation',
      style: 'minimalist',
      category: 'Technology',
      thumbnail: '/api/placeholder/300/200'
    },
    {
      id: '3',
      name: 'Business Concept',
      description: 'Corporate and business-themed imagery',
      prompt: 'Professional business concept, teamwork, growth charts, modern office environment',
      style: 'photorealistic',
      category: 'Business',
      thumbnail: '/api/placeholder/300/200'
    },
    {
      id: '4',
      name: 'Creative Art',
      description: 'Artistic and creative designs',
      prompt: 'Abstract creative artwork, colorful composition, artistic expression, modern art style',
      style: 'artistic',
      category: 'Creative',
      thumbnail: '/api/placeholder/300/200'
    }
  ];

  const handleGenerate = () => {
    if (!prompt.trim()) {
      toast({
        title: "Prompt Required",
        description: "Please enter a description for your image",
        variant: "destructive",
      });
      return;
    }

    const [width, height] = selectedSize.split('x').map(Number);
    
    generateImageMutation.mutate({
      prompt: prompt.trim(),
      negativePrompt: negativePrompt.trim(),
      style: selectedStyle,
      model: selectedModel,
      width,
      height,
      numImages,
      seed: seed ? parseInt(seed) : undefined,
      guidanceScale,
      steps
    });
  };

  const handlePresetSelect = (preset: ImagePreset) => {
    setPrompt(preset.prompt);
    setSelectedStyle(preset.style);
    setSelectedPreset(preset);
    setShowSettings(false);
  };

  const copyPrompt = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Prompt copied to clipboard",
    });
  };

  const downloadImage = (imageUrl: string, filename: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: "Download Started",
      description: "Your image is being downloaded",
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-pink-500 to-purple-600">
              <ImageIcon className="h-6 w-6 text-white" />
            </div>
            AI Image Generator
          </h1>
          <p className="text-muted-foreground mt-2">
            Create stunning images with SDXL on A100 GPU for your courses and content
          </p>
        </div>
        <Dialog open={showSettings} onOpenChange={setShowSettings}>
          <DialogTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Settings className="h-4 w-4" />
              Advanced Settings
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Advanced Generation Settings</DialogTitle>
              <DialogDescription>
                Fine-tune your image generation parameters
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="guidance">Guidance Scale</Label>
                  <Input
                    id="guidance"
                    type="number"
                    min="1"
                    max="20"
                    step="0.5"
                    value={guidanceScale}
                    onChange={(e) => setGuidanceScale(parseFloat(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground mt-1">How closely to follow the prompt (7.5 recommended)</p>
                </div>
                <div>
                  <Label htmlFor="steps">Inference Steps</Label>
                  <Input
                    id="steps"
                    type="number"
                    min="10"
                    max="100"
                    value={steps}
                    onChange={(e) => setSteps(parseInt(e.target.value))}
                  />
                  <p className="text-xs text-muted-foreground mt-1">Quality vs speed (30 recommended)</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="seed">Seed (Optional)</Label>
                  <Input
                    id="seed"
                    placeholder="Random"
                    value={seed}
                    onChange={(e) => setSeed(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground mt-1">For reproducible results</p>
                </div>
                <div>
                  <Label htmlFor="num-images">Number of Images</Label>
                  <Select value={numImages.toString()} onValueChange={(value) => setNumImages(parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Image</SelectItem>
                      <SelectItem value="2">2 Images</SelectItem>
                      <SelectItem value="4">4 Images</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Generation Panel */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="h-5 w-5" />
                Create Your Image
              </CardTitle>
              <CardDescription>
                Describe what you want to generate and customize the style
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="prompt">Image Description</Label>
                <Textarea
                  id="prompt"
                  placeholder="A beautiful sunset over mountains, photorealistic, highly detailed..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="negative-prompt">Negative Prompt (Optional)</Label>
                <Textarea
                  id="negative-prompt"
                  placeholder="blurry, low quality, distorted..."
                  value={negativePrompt}
                  onChange={(e) => setNegativePrompt(e.target.value)}
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Style</Label>
                  <Select value={selectedStyle} onValueChange={setSelectedStyle}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {imageStyles.map((style) => (
                        <SelectItem key={style.id} value={style.id}>
                          {style.name} - {style.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Size</Label>
                  <Select value={selectedSize} onValueChange={setSelectedSize}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {imageSizes.map((size) => (
                        <SelectItem key={size.id} value={size.id}>
                          {size.name} - {size.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Button 
                onClick={handleGenerate} 
                disabled={generateImageMutation.isPending || !prompt.trim()}
                className="w-full"
                size="lg"
              >
                {generateImageMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Image
                  </>
                )}
              </Button>

              {generateImageMutation.isPending && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Processing on A100 GPU...</span>
                    <span>~30 seconds</span>
                  </div>
                  <Progress value={33} className="h-2" />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Generated Images */}
          <Card>
            <CardHeader>
              <CardTitle>Generated Images</CardTitle>
              <CardDescription>Your recently created images</CardDescription>
            </CardHeader>
            <CardContent>
              {imagesLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : generatedImages.length === 0 ? (
                <div className="text-center py-8">
                  <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No images generated yet</p>
                  <p className="text-sm text-muted-foreground">Create your first AI image above</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {generatedImages.slice(0, 6).map((image: GeneratedImage) => (
                    <div key={image.id} className="relative group">
                      <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                        <img
                          src={image.imageUrl}
                          alt={image.prompt}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                        />
                      </div>
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                        <div className="flex gap-2">
                          <Button size="sm" variant="secondary" onClick={() => downloadImage(image.imageUrl, `ai-image-${image.id}.png`)}>
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="secondary" onClick={() => copyPrompt(image.prompt)}>
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="absolute bottom-2 left-2 right-2">
                        <Badge variant="secondary" className="text-xs truncate max-w-full">
                          {image.style}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Presets Panel */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Quick Templates
              </CardTitle>
              <CardDescription>
                Ready-to-use prompts for common use cases
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {presetTemplates.map((preset) => (
                <div
                  key={preset.id}
                  className="p-4 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => handlePresetSelect(preset)}
                >
                  <div className="flex items-start gap-3">
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white">
                      <Camera className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium">{preset.name}</h3>
                      <p className="text-sm text-muted-foreground mb-2">{preset.description}</p>
                      <Badge variant="outline" className="text-xs">{preset.category}</Badge>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                AI Credits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Remaining Credits</span>
                  <Badge variant="secondary">245</Badge>
                </div>
                <Progress value={68} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  Each image generation uses 5 credits
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  Purchase More Credits
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Tips & Guidelines
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div>
                  <h4 className="font-medium">Better Prompts:</h4>
                  <p className="text-muted-foreground">Be specific about style, lighting, and composition</p>
                </div>
                <div>
                  <h4 className="font-medium">Avoid:</h4>
                  <p className="text-muted-foreground">Copyrighted characters, real people's names</p>
                </div>
                <div>
                  <h4 className="font-medium">For Course Content:</h4>
                  <p className="text-muted-foreground">Use professional, educational themes</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}