import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Play, Pause, CheckCircle, AlertCircle, Clock, Video, User } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface CourseGenerationJob {
  id: string;
  type: "traditional" | "avatar";
  title: string;
  status: "pending" | "in-progress" | "completed" | "failed";
  progress: number;
  currentStage: string;
  stages: {
    name: string;
    status: "pending" | "in-progress" | "completed" | "failed";
    progress: number;
    duration?: number;
  }[];
  startedAt: string;
  estimatedCompletion?: string;
  completedAt?: string;
  outputUrl?: string;
}

export default function CourseProgressPage() {
  const [, setLocation] = useLocation();
  const [selectedJob, setSelectedJob] = useState<string | null>(null);

  // Mock data for demonstration - in real app, this would come from API
  const mockJobs: CourseGenerationJob[] = [
    {
      id: "1",
      type: "traditional",
      title: "Introduction to AI and Machine Learning",
      status: "in-progress",
      progress: 65,
      currentStage: "Pexels/Pixabay Media Search",
      stages: [
        { name: "Mistral AI Structure Generation", status: "completed", progress: 100, duration: 45 },
        { name: "Coqui TTS Voice Synthesis", status: "completed", progress: 100, duration: 120 },
        { name: "Pexels/Pixabay Media Search", status: "in-progress", progress: 75 },
        { name: "Marp Slide Generation", status: "pending", progress: 0 },
        { name: "FFmpeg Video Assembly", status: "pending", progress: 0 }
      ],
      startedAt: "2025-06-24T10:30:00Z",
      estimatedCompletion: "2025-06-24T11:45:00Z"
    },
    {
      id: "2",
      type: "avatar",
      title: "Digital Marketing Fundamentals",
      status: "completed",
      progress: 100,
      currentStage: "Completed",
      stages: [
        { name: "Mistral AI Structure Generation", status: "completed", progress: 100, duration: 38 },
        { name: "EchoMimic V2 Avatar Processing", status: "completed", progress: 100, duration: 180 },
        { name: "Avatar-optimized TTS", status: "completed", progress: 100, duration: 90 },
        { name: "Background Scene Generation", status: "completed", progress: 100, duration: 60 },
        { name: "Avatar Video Assembly", status: "completed", progress: 100, duration: 150 }
      ],
      startedAt: "2025-06-24T09:00:00Z",
      completedAt: "2025-06-24T10:15:00Z",
      outputUrl: "/courses/generated/digital-marketing-avatar"
    },
    {
      id: "3",
      type: "traditional",
      title: "Web Development Bootcamp",
      status: "failed",
      progress: 25,
      currentStage: "Failed at Coqui TTS Voice Synthesis",
      stages: [
        { name: "Mistral AI Structure Generation", status: "completed", progress: 100, duration: 52 },
        { name: "Coqui TTS Voice Synthesis", status: "failed", progress: 25 },
        { name: "Pexels/Pixabay Media Search", status: "pending", progress: 0 },
        { name: "Marp Slide Generation", status: "pending", progress: 0 },
        { name: "FFmpeg Video Assembly", status: "pending", progress: 0 }
      ],
      startedAt: "2025-06-24T08:15:00Z"
    }
  ];

  const { data: jobs = mockJobs } = useQuery({
    queryKey: ['/api/course-generation/jobs'],
    refetchInterval: 2000, // Refetch every 2 seconds for real-time updates
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "in-progress":
        return <Play className="h-4 w-4 text-blue-600" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "green";
      case "in-progress": return "blue";
      case "failed": return "red";
      default: return "gray";
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              onClick={() => setLocation('/dashboard')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Course Generation Progress</h1>
              <p className="text-gray-600 mt-1">
                Monitor your Enhanced Traditional and Avatar Course generation workflows
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Jobs List */}
          <div className="lg:col-span-2 space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Generation Jobs</h2>
              <Badge variant="secondary">{jobs.length} Total</Badge>
            </div>
            
            {jobs.map((job) => (
              <Card 
                key={job.id} 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedJob === job.id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedJob(job.id)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {job.type === "traditional" ? (
                        <Video className="h-5 w-5 text-blue-600" />
                      ) : (
                        <User className="h-5 w-5 text-purple-600" />
                      )}
                      <div>
                        <CardTitle className="text-lg">{job.title}</CardTitle>
                        <CardDescription>
                          {job.type === "traditional" ? "Enhanced Traditional Course" : "Enhanced Avatar Course"}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(job.status)}
                      <Badge variant={getStatusColor(job.status) as any}>
                        {job.status.replace("-", " ")}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>{job.currentStage}</span>
                        <span>{job.progress}%</span>
                      </div>
                      <Progress value={job.progress} className="h-2" />
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Started: {formatTime(job.startedAt)}</span>
                      {job.estimatedCompletion && job.status === "in-progress" && (
                        <span>ETA: {formatTime(job.estimatedCompletion)}</span>
                      )}
                      {job.completedAt && (
                        <span>Completed: {formatTime(job.completedAt)}</span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Detailed View */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Detailed Progress</h2>
            
            {selectedJob ? (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {jobs.find(j => j.id === selectedJob)?.title}
                  </CardTitle>
                  <CardDescription>
                    Stage-by-stage progress breakdown
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {jobs.find(j => j.id === selectedJob)?.stages.map((stage, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(stage.status)}
                            <span className="text-sm font-medium">{stage.name}</span>
                          </div>
                          {stage.duration && (
                            <span className="text-xs text-muted-foreground">
                              {formatDuration(stage.duration)}
                            </span>
                          )}
                        </div>
                        <Progress value={stage.progress} className="h-1" />
                      </div>
                    ))}
                  </div>
                  
                  {jobs.find(j => j.id === selectedJob)?.outputUrl && (
                    <div className="mt-6 pt-4 border-t">
                      <Button className="w-full" asChild>
                        <a href={jobs.find(j => j.id === selectedJob)?.outputUrl}>
                          View Generated Course
                        </a>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    Select a generation job to view detailed progress
                  </p>
                </CardContent>
              </Card>
            )}

            {/* System Status */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">System Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Mistral AI</span>
                  <Badge variant="green">Online</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Coqui TTS</span>
                  <Badge variant="green">Online</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">EchoMimic V2</span>
                  <Badge variant="green">Online</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">FFmpeg Pipeline</span>
                  <Badge variant="green">Ready</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Media APIs</span>
                  <Badge variant="green">Connected</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}