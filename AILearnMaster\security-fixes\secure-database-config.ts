/**
 * Secure Database Configuration for AILearnMaster
 * Implements security best practices for PostgreSQL connections
 */

import { Pool, neonConfig, PoolConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";
import { sql } from 'drizzle-orm';
import crypto from 'crypto';

// Configure Neon database for serverless environment
neonConfig.webSocketConstructor = ws;

interface SecureDatabaseConfig {
  connectionString: string;
  maxConnections: number;
  idleTimeoutMs: number;
  connectionTimeoutMs: number;
  statementTimeoutMs: number;
  queryTimeoutMs: number;
  retryAttempts: number;
  retryDelayMs: number;
  sslMode: 'require' | 'prefer' | 'disable';
  applicationName: string;
  enableQueryLogging: boolean;
  enableSlowQueryLogging: boolean;
  slowQueryThresholdMs: number;
}

interface DatabaseSecurityMetrics {
  connectionAttempts: number;
  failedConnections: number;
  slowQueries: number;
  suspiciousQueries: number;
  lastSecurityScan: Date | null;
}

class SecureDatabaseManager {
  private pool: Pool | null = null;
  private db: ReturnType<typeof drizzle> | null = null;
  private config: SecureDatabaseConfig;
  private securityMetrics: DatabaseSecurityMetrics;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private securityScanInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.config = this.getSecureConfig();
    this.securityMetrics = {
      connectionAttempts: 0,
      failedConnections: 0,
      slowQueries: 0,
      suspiciousQueries: 0,
      lastSecurityScan: null
    };
    this.initialize();
    this.startSecurityMonitoring();
  }

  /**
   * Get secure database configuration
   */
  private getSecureConfig(): SecureDatabaseConfig {
    const environment = process.env.NODE_ENV || 'development';
    
    // Validate required environment variables
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    // Ensure SSL is enforced in production
    let connectionString = process.env.DATABASE_URL;
    if (environment === 'production' && !connectionString.includes('sslmode=')) {
      connectionString += connectionString.includes('?') ? '&sslmode=require' : '?sslmode=require';
    }

    return {
      connectionString,
      maxConnections: environment === 'production' ? 15 : 8, // Reduced for security
      idleTimeoutMs: environment === 'production' ? 30000 : 20000, // Shorter idle timeout
      connectionTimeoutMs: 8000, // Shorter connection timeout
      statementTimeoutMs: 20000, // Prevent long-running queries
      queryTimeoutMs: 15000, // Query timeout for security
      retryAttempts: 2, // Reduced retry attempts
      retryDelayMs: 1000,
      sslMode: environment === 'production' ? 'require' : 'prefer',
      applicationName: `ailearn-master-${environment}-${crypto.randomBytes(4).toString('hex')}`,
      enableQueryLogging: environment === 'development',
      enableSlowQueryLogging: true,
      slowQueryThresholdMs: 1000
    };
  }

  /**
   * Initialize secure database connection
   */
  private async initialize(): Promise<void> {
    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        this.securityMetrics.connectionAttempts++;
        
        console.log(`🔒 Attempting secure database connection (${attempt}/${this.config.retryAttempts})...`);
        
        // Create secure connection pool
        const poolConfig: PoolConfig = {
          connectionString: this.config.connectionString,
          max: this.config.maxConnections,
          idleTimeoutMillis: this.config.idleTimeoutMs,
          connectionTimeoutMillis: this.config.connectionTimeoutMs,
          statementTimeoutMillis: this.config.statementTimeoutMs,
          queryTimeoutMillis: this.config.queryTimeoutMs,
          allowExitOnIdle: true,
          keepAlive: true,
          keepAliveInitialDelayMillis: 5000,
          // Security-focused settings
          application_name: this.config.applicationName,
          ssl: this.config.sslMode !== 'disable'
        };

        this.pool = new Pool(poolConfig);
        
        // Initialize Drizzle ORM with security logging
        this.db = drizzle({ 
          client: this.pool, 
          schema,
          logger: this.config.enableQueryLogging ? this.createSecureLogger() : false
        });

        // Test connection with security validation
        await this.validateSecureConnection();
        
        console.log(`✅ Secure database connection established (attempt ${attempt})`);
        console.log(`🔒 SSL Mode: ${this.config.sslMode}, App: ${this.config.applicationName}`);
        
        return;
        
      } catch (error) {
        this.securityMetrics.failedConnections++;
        console.error(`❌ Secure database connection attempt ${attempt} failed:`, this.sanitizeError(error));
        
        if (attempt < this.config.retryAttempts) {
          console.log(`⏳ Retrying in ${this.config.retryDelayMs}ms...`);
          await this.delay(this.config.retryDelayMs);
        }
      }
    }

    throw new Error("All secure database connection attempts failed");
  }

  /**
   * Validate secure connection
   */
  private async validateSecureConnection(): Promise<void> {
    if (!this.db) {
      throw new Error("Database not initialized");
    }

    // Test basic connectivity
    const result = await this.db.execute(sql`SELECT 1 as test, NOW() as timestamp, version() as version`);
    if (!result || result.length === 0) {
      throw new Error("Database test query failed");
    }

    // Validate SSL connection in production
    if (this.config.sslMode === 'require') {
      const sslResult = await this.db.execute(sql`SELECT ssl_is_used() as ssl_enabled`);
      if (!sslResult[0]?.ssl_enabled) {
        throw new Error("SSL connection required but not established");
      }
    }

    // Check database permissions (should not have superuser access)
    const permResult = await this.db.execute(sql`SELECT usesuper FROM pg_user WHERE usename = current_user`);
    if (permResult[0]?.usesuper) {
      console.warn("⚠️ WARNING: Database user has superuser privileges - security risk");
    }
  }

  /**
   * Create secure query logger
   */
  private createSecureLogger() {
    return {
      logQuery: (query: string, params: unknown[]) => {
        const startTime = Date.now();
        
        return {
          end: () => {
            const duration = Date.now() - startTime;
            
            // Log slow queries
            if (duration > this.config.slowQueryThresholdMs) {
              this.securityMetrics.slowQueries++;
              console.warn(`🐌 SLOW QUERY (${duration}ms):`, this.sanitizeQuery(query));
            }
            
            // Detect suspicious queries
            if (this.isSuspiciousQuery(query)) {
              this.securityMetrics.suspiciousQueries++;
              console.error(`🚨 SUSPICIOUS QUERY DETECTED:`, this.sanitizeQuery(query));
            }
            
            // Log in development
            if (this.config.enableQueryLogging) {
              console.log(`📊 QUERY (${duration}ms):`, this.sanitizeQuery(query));
            }
          }
        };
      }
    };
  }

  /**
   * Detect suspicious SQL queries
   */
  private isSuspiciousQuery(query: string): boolean {
    const suspiciousPatterns = [
      /union\s+select/gi,
      /information_schema/gi,
      /pg_user/gi,
      /pg_shadow/gi,
      /pg_database/gi,
      /drop\s+table/gi,
      /drop\s+database/gi,
      /alter\s+table/gi,
      /create\s+user/gi,
      /grant\s+all/gi,
      /--\s*$/gm,
      /\/\*.*\*\//gs,
      /xp_cmdshell/gi,
      /sp_executesql/gi
    ];

    return suspiciousPatterns.some(pattern => pattern.test(query));
  }

  /**
   * Sanitize query for logging (remove sensitive data)
   */
  private sanitizeQuery(query: string): string {
    // Remove potential sensitive data patterns
    return query
      .replace(/password\s*=\s*'[^']*'/gi, "password='[REDACTED]'")
      .replace(/token\s*=\s*'[^']*'/gi, "token='[REDACTED]'")
      .replace(/secret\s*=\s*'[^']*'/gi, "secret='[REDACTED]'")
      .replace(/key\s*=\s*'[^']*'/gi, "key='[REDACTED]'")
      .substring(0, 500); // Limit length
  }

  /**
   * Sanitize error messages
   */
  private sanitizeError(error: any): string {
    const errorMessage = error?.message || String(error);
    
    // Remove sensitive information from error messages
    return errorMessage
      .replace(/password=[^&\s]*/gi, 'password=[REDACTED]')
      .replace(/user=[^&\s]*/gi, 'user=[REDACTED]')
      .replace(/host=[^&\s]*/gi, 'host=[REDACTED]')
      .replace(/postgresql:\/\/[^@]*@/gi, 'postgresql://[REDACTED]@');
  }

  /**
   * Execute query with security monitoring
   */
  public async secureExecute<T>(query: any, params?: any[]): Promise<T | null> {
    if (!this.db) {
      throw new Error("Database not available");
    }

    const startTime = Date.now();
    
    try {
      const result = await this.db.execute(query);
      
      const duration = Date.now() - startTime;
      
      // Monitor for slow queries
      if (duration > this.config.slowQueryThresholdMs) {
        this.securityMetrics.slowQueries++;
      }
      
      return result as T;
      
    } catch (error) {
      console.error('🚨 Secure database operation failed:', this.sanitizeError(error));
      
      // Check if it's a security-related error
      const errorMessage = String(error).toLowerCase();
      if (errorMessage.includes('permission') || errorMessage.includes('access') || errorMessage.includes('authentication')) {
        console.error('🔒 SECURITY ALERT: Database access violation detected');
      }
      
      return null;
    }
  }

  /**
   * Get database instance with security validation
   */
  public getSecureDatabase() {
    if (!this.db) {
      throw new Error("Secure database not available");
    }
    return this.db;
  }

  /**
   * Get security metrics
   */
  public getSecurityMetrics(): DatabaseSecurityMetrics & { config: Partial<SecureDatabaseConfig> } {
    return {
      ...this.securityMetrics,
      config: {
        maxConnections: this.config.maxConnections,
        sslMode: this.config.sslMode,
        applicationName: this.config.applicationName,
        enableQueryLogging: this.config.enableQueryLogging
      }
    };
  }

  /**
   * Start security monitoring
   */
  private startSecurityMonitoring(): void {
    // Health check every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      await this.performSecurityHealthCheck();
    }, 30000);

    // Security scan every 5 minutes
    this.securityScanInterval = setInterval(async () => {
      await this.performSecurityScan();
    }, 300000);
  }

  /**
   * Perform security health check
   */
  private async performSecurityHealthCheck(): Promise<void> {
    try {
      if (!this.db) return;

      // Check connection health
      await this.db.execute(sql`SELECT 1`);
      
      // Check for suspicious activity
      if (this.securityMetrics.suspiciousQueries > 10) {
        console.error('🚨 HIGH SUSPICIOUS QUERY ACTIVITY DETECTED');
      }
      
      if (this.securityMetrics.slowQueries > 50) {
        console.warn('⚠️ HIGH SLOW QUERY ACTIVITY DETECTED');
      }
      
    } catch (error) {
      console.error('❌ Security health check failed:', this.sanitizeError(error));
    }
  }

  /**
   * Perform comprehensive security scan
   */
  private async performSecurityScan(): Promise<void> {
    try {
      if (!this.db) return;

      console.log('🔍 Performing database security scan...');
      
      // Check for unauthorized schema changes
      const schemaCheck = await this.db.execute(sql`
        SELECT schemaname, tablename, tableowner 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        ORDER BY tablename
      `);
      
      // Check for suspicious connections
      const connectionCheck = await this.db.execute(sql`
        SELECT count(*) as active_connections, 
               array_agg(DISTINCT application_name) as applications
        FROM pg_stat_activity 
        WHERE state = 'active'
      `);
      
      this.securityMetrics.lastSecurityScan = new Date();
      
      console.log('✅ Database security scan completed');
      
    } catch (error) {
      console.error('❌ Security scan failed:', this.sanitizeError(error));
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Graceful shutdown with security cleanup
   */
  public async secureShutdown(): Promise<void> {
    console.log('🔒 Initiating secure database shutdown...');
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    if (this.securityScanInterval) {
      clearInterval(this.securityScanInterval);
    }
    
    if (this.pool) {
      await this.pool.end();
    }
    
    // Log final security metrics
    console.log('📊 Final security metrics:', this.getSecurityMetrics());
    console.log('✅ Secure database shutdown completed');
  }
}

// Create singleton secure database manager
const secureDbManager = new SecureDatabaseManager();

// Export secure database functions
export const getSecureDatabase = () => secureDbManager.getSecureDatabase();
export const secureDbExecute = secureDbManager.secureExecute.bind(secureDbManager);
export const getDbSecurityMetrics = secureDbManager.getSecurityMetrics.bind(secureDbManager);

// Graceful shutdown handlers
process.on('SIGTERM', async () => {
  await secureDbManager.secureShutdown();
  process.exit(0);
});

process.on('SIGINT', async () => {
  await secureDbManager.secureShutdown();
  process.exit(0);
});

export { secureDbManager };
