
#!/bin/bash

echo "Setting up Coqui TTS locally on Replit..."

# Install Coqui TTS and dependencies
echo "Installing Coqui TTS and dependencies..."
pip install coqui-tts[all]
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install soundfile librosa numpy scipy

# Create directories
echo "Creating directories..."
mkdir -p temp
mkdir -p uploads/audio

# Download a default model to test
echo "Downloading default English model..."
python -c "
import TTS
from TTS.api import TTS

# Initialize with a lightweight model
tts = TTS('tts_models/en/ljspeech/tacotron2-DDC')
print('Default model downloaded successfully!')

# Test generation
print('Testing TTS generation...')
tts.tts_to_file(text='Hello, this is a test of Coqui TTS on Replit!', file_path='temp/test_coqui.wav')
print('Test completed! Check temp/test_coqui.wav')
"

echo "Coqui TTS setup complete!"
echo ""
echo "Available endpoints:"
echo "- POST /api/local-tts/initialize - Initialize the service"
echo "- GET /api/local-tts/status - Check service status"
echo "- POST /api/local-tts/test - Test TTS generation"
echo "- POST /api/local-tts/download-model - Download additional models"
echo ""
echo "The service will automatically be used as the primary TTS option when available."
