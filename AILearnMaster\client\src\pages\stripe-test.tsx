import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useUser } from '@/hooks/use-user';
import { loadStripe } from '@stripe/stripe-js';

const stripePublicKey = import.meta.env.VITE_STRIPE_PUBLIC_KEY;
if (!stripePublicKey) {
  console.warn('Stripe public key is missing. Payment functionality will not work properly.');
}
const stripePromise = stripePublicKey ? loadStripe(stripePublicKey) : null;

export default function StripeTest() {
  const { user, isLoading } = useUser();
  const { toast } = useToast();
  const [selectedPlan, setSelectedPlan] = useState('starter');
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleCheckout = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in before continuing to checkout",
        variant: "destructive",
      });
      return;
    }

    if (!stripePromise) {
      toast({
        title: "Configuration Error",
        description: "Stripe is not properly configured",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Create a checkout session
      const response = await apiRequest("POST", "/api/payments/create-checkout-session", { 
        planId: selectedPlan, 
        billingInterval 
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create checkout session');
      }

      const data = await response.json();

      if (!data.sessionId) {
        throw new Error('No session ID returned from server');
      }
      
      // Check if this is a mock session (for development)
      if (data.sessionId.startsWith('mock_session_')) {
        console.log("Using mock session - redirecting directly to:", data.url);
        // For mock sessions, redirect directly to the success URL
        window.location.href = data.url;
        return;
      }

      const stripe = await stripePromise;

      if (!stripe) {
        throw new Error('Stripe not initialized properly');
      }

      // Redirect to Stripe checkout
      const { error } = await stripe.redirectToCheckout({
        sessionId: data.sessionId
      });

      if (error) {
        throw new Error(error.message || 'Failed to redirect to Stripe checkout');
      }
    } catch (err: any) {
      console.error('Error creating checkout session:', err);
      toast({
        title: "Checkout Error",
        description: err.message || 'Failed to initialize checkout',
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const createPortalSession = async () => {
    if (!user?.stripeCustomerId) {
      toast({
        title: "No Subscription Found",
        description: "You don't have an active subscription to manage",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const response = await apiRequest("POST", "/api/payments/create-portal-session", {
        customerId: user.stripeCustomerId
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create customer portal session');
      }

      const { url } = await response.json();

      // Redirect to Stripe customer portal
      window.location.href = url;
    } catch (err: any) {
      console.error('Error creating portal session:', err);
      toast({
        title: "Portal Session Error",
        description: err.message || 'Failed to initialize customer portal',
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return <div className="container py-10">Loading user data...</div>;
  }

  return (
    <div className="container py-10 max-w-xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Stripe Integration Test</h1>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Subscription Status</CardTitle>
          <CardDescription>Your current subscription information</CardDescription>
        </CardHeader>
        <CardContent>
          {user ? (
            <div className="space-y-2">
              <div>
                <span className="font-semibold">Subscription Plan:</span> {user.plan || 'Free'}
              </div>
              {user.stripeCustomerId && (
                <div>
                  <span className="font-semibold">Customer ID:</span> {user.stripeCustomerId}
                </div>
              )}
              {user.stripeSubscriptionId && (
                <div>
                  <span className="font-semibold">Subscription ID:</span> {user.stripeSubscriptionId}
                </div>
              )}
            </div>
          ) : (
            <p>Please log in to view your subscription details</p>
          )}
        </CardContent>
        {user?.stripeCustomerId && (
          <CardFooter>
            <Button 
              onClick={createPortalSession} 
              disabled={isProcessing}
              className="w-full"
            >
              {isProcessing ? 'Please wait...' : 'Manage Subscription'}
            </Button>
          </CardFooter>
        )}
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test Checkout</CardTitle>
          <CardDescription>Select a plan and billing interval to test Stripe checkout</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">Select Plan</h3>
            <RadioGroup 
              defaultValue={selectedPlan}
              onValueChange={setSelectedPlan}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="starter" id="starter" />
                <Label htmlFor="starter">Starter ($49/month or $470/year)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="pro" id="pro" />
                <Label htmlFor="pro">Pro ($129/month or $1238/year)</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="business" id="business" />
                <Label htmlFor="business">Business ($299/month or $2870/year)</Label>
              </div>
            </RadioGroup>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-2">Billing Interval</h3>
            <RadioGroup 
              defaultValue={billingInterval}
              onValueChange={(value) => setBillingInterval(value as 'monthly' | 'yearly')}
              className="flex flex-col space-y-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="monthly" id="monthly" />
                <Label htmlFor="monthly">Monthly</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yearly" id="yearly" />
                <Label htmlFor="yearly">Yearly (Save ~15%)</Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
        <CardFooter>
          <Button 
            onClick={handleCheckout} 
            disabled={isProcessing}
            className="w-full"
          >
            {isProcessing ? 'Processing...' : 'Test Checkout'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}