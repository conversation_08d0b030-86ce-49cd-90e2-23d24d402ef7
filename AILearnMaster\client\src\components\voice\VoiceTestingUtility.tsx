import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Clock, Play, AlertTriangle, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface VoiceTestResult {
  voiceId: string;
  available: boolean;
  error?: string;
  responseTime?: number;
  testResult?: {
    available: boolean;
    error?: string;
  };
}

interface VoiceTestingUtilityProps {
  voices: Array<{
    id: string;
    name: string;
    language: string;
    service: string;
    quality?: string;
    category?: string;
  }>;
  onTestComplete?: (results: VoiceTestResult[]) => void;
}

export function VoiceTestingUtility({ voices, onTestComplete }: VoiceTestingUtilityProps) {
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<VoiceTestResult[]>([]);
  const [currentTestIndex, setCurrentTestIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const { toast } = useToast();

  const testSingleVoice = useCallback(async (voice: any): Promise<VoiceTestResult> => {
    const startTime = Date.now();
    
    try {
      const response = await apiRequest('POST', '/api/tts/test-voice', {
        voiceId: voice.id
      });
      
      const responseTime = Date.now() - startTime;
      
      return {
        voiceId: voice.id,
        available: response.testResult?.available || false,
        error: response.testResult?.error,
        responseTime,
        testResult: response.testResult
      };
    } catch (error) {
      return {
        voiceId: voice.id,
        available: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime
      };
    }
  }, []);

  const testAllVoices = useCallback(async () => {
    if (!voices || voices.length === 0) {
      toast({
        title: "No Voices to Test",
        description: "No voices available for testing",
        variant: "destructive"
      });
      return;
    }

    setTesting(true);
    setTestResults([]);
    setCurrentTestIndex(0);
    setProgress(0);

    const results: VoiceTestResult[] = [];

    for (let i = 0; i < voices.length; i++) {
      setCurrentTestIndex(i);
      setProgress((i / voices.length) * 100);

      const result = await testSingleVoice(voices[i]);
      results.push(result);
      setTestResults([...results]);

      // Small delay to prevent overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    setProgress(100);
    setTesting(false);
    
    const successCount = results.filter(r => r.available).length;
    const failureCount = results.filter(r => !r.available).length;

    toast({
      title: "Voice Testing Complete",
      description: `${successCount} voices working, ${failureCount} failed`,
      variant: successCount > failureCount ? "default" : "destructive"
    });

    onTestComplete?.(results);
  }, [voices, testSingleVoice, toast, onTestComplete]);

  const getStatusIcon = (result: VoiceTestResult) => {
    if (result.available) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    } else {
      return <XCircle className="h-4 w-4 text-red-600" />;
    }
  };

  const getStatusBadge = (result: VoiceTestResult) => {
    if (result.available) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Working</Badge>;
    } else {
      return <Badge variant="destructive">Failed</Badge>;
    }
  };

  const getResponseTimeColor = (responseTime?: number) => {
    if (!responseTime) return 'text-gray-500';
    if (responseTime < 1000) return 'text-green-600';
    if (responseTime < 3000) return 'text-yellow-600';
    return 'text-red-600';
  };

  const workingVoices = testResults.filter(r => r.available);
  const failedVoices = testResults.filter(r => !r.available);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5" />
          Voice Testing Utility
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Test Controls */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600">
              Test all {voices.length} voices for availability and functionality
            </p>
          </div>
          <Button 
            onClick={testAllVoices} 
            disabled={testing || voices.length === 0}
            className="flex items-center gap-2"
          >
            {testing ? (
              <>
                <Clock className="h-4 w-4 animate-spin" />
                Testing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4" />
                Test All Voices
              </>
            )}
          </Button>
        </div>

        {/* Progress */}
        {testing && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Testing voice {currentTestIndex + 1} of {voices.length}</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="w-full" />
            {voices[currentTestIndex] && (
              <p className="text-xs text-gray-500">
                Currently testing: {voices[currentTestIndex].name}
              </p>
            )}
          </div>
        )}

        {/* Results Summary */}
        {testResults.length > 0 && (
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{workingVoices.length}</div>
              <div className="text-sm text-gray-600">Working</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{failedVoices.length}</div>
              <div className="text-sm text-gray-600">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{testResults.length}</div>
              <div className="text-sm text-gray-600">Total</div>
            </div>
          </div>
        )}

        {/* Failed Voices Alert */}
        {failedVoices.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {failedVoices.length} voice{failedVoices.length > 1 ? 's' : ''} failed testing. 
              These voices may not work properly in course generation.
            </AlertDescription>
          </Alert>
        )}

        {/* Detailed Results */}
        {testResults.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium">Test Results</h4>
            <div className="max-h-96 overflow-y-auto space-y-2">
              {testResults.map((result) => {
                const voice = voices.find(v => v.id === result.voiceId);
                if (!voice) return null;

                return (
                  <div 
                    key={result.voiceId}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(result)}
                      <div>
                        <div className="font-medium">{voice.name}</div>
                        <div className="text-xs text-gray-500">
                          {voice.language} • {voice.service}
                          {voice.quality && ` • ${voice.quality}`}
                        </div>
                        {result.error && (
                          <div className="text-xs text-red-600 mt-1">
                            Error: {result.error}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {result.responseTime && (
                        <span className={`text-xs ${getResponseTimeColor(result.responseTime)}`}>
                          {result.responseTime}ms
                        </span>
                      )}
                      {getStatusBadge(result)}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
