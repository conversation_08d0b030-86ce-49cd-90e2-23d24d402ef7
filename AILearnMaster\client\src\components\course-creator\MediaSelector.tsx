import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  Filter,
  Grid3X3, 
  List, 
  CheckCircle2, 
  ImageIcon, 
  PlayCircle, 
  FileText,
  Calendar,
  Tag,
  X,
  Plus,
  ArrowLeft,
  Eye,
  Trash2,
  RefreshCw,
  Upload,
  BookOpen
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useMediaAssignments } from '@/hooks/useMediaAssignments';

interface MediaItem {
  id: string;
  name: string;
  title?: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnailUrl?: string;
  size: number;
  uploadedAt: string;
  source: 'upload' | 'pexels' | 'pixabay';
  tags?: string[];
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    author?: string;
  };
}

interface Lesson {
  id: string;
  title: string;
  assignedMedia?: MediaItem;
}

interface Module {
  id: string;
  title: string;
  lessons: Lesson[];
  assignedMedia?: MediaItem;
}

interface MediaSelectorProps {
  modules: Module[];
  onMediaAssigned: (moduleId: string, lessonId: string | null, media: MediaItem) => void;
  onMediaRemoved: (moduleId: string, lessonId: string | null) => void;
  onClose?: () => void;
  onBack?: () => void;
}

interface MediaFilters {
  type: 'all' | 'image' | 'video' | 'document';
  source: 'all' | 'upload' | 'pexels' | 'pixabay';
  uploadDate: 'all' | 'today' | 'week' | 'month';
  tags: string[];
}

const MediaSelector: React.FC<MediaSelectorProps> = ({
  modules,
  onMediaAssigned,
  onMediaRemoved,
  onClose
}) => {
  const [selectedTarget, setSelectedTarget] = useState<{
    moduleId: string;
    lessonId: string | null;
    title: string;
  } | null>(null);
  
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [previewMedia, setPreviewMedia] = useState<MediaItem | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  
  const [filters, setFilters] = useState<MediaFilters>({
    type: 'all',
    source: 'all',
    uploadDate: 'all',
    tags: []
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Use persistent media assignments
  const {
    assignMediaToModule,
    assignMediaToLesson,
    removeMediaFromModule,
    removeMediaFromLesson,
    getModuleMedia,
    getLessonMedia,
    getStats
  } = useMediaAssignments();

  // Fetch media library
  const { data: mediaItems = [], isLoading, refetch } = useQuery({
    queryKey: ['/api/media'],
    queryFn: async () => {
      const response = await fetch('/api/media');
      if (!response.ok) throw new Error('Failed to fetch media');
      return response.json();
    }
  });

  // Filter media based on search and filters
  const filteredMedia = useCallback(() => {
    let filtered = mediaItems;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter((item: MediaItem) => 
        item.name.toLowerCase().includes(query) ||
        item.title?.toLowerCase().includes(query) ||
        item.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter((item: MediaItem) => item.type === filters.type);
    }

    // Source filter
    if (filters.source !== 'all') {
      filtered = filtered.filter((item: MediaItem) => item.source === filters.source);
    }

    // Date filter
    if (filters.uploadDate !== 'all') {
      const now = new Date();
      const filterDate = new Date();
      
      switch (filters.uploadDate) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
      }
      
      filtered = filtered.filter((item: MediaItem) => 
        new Date(item.uploadedAt) >= filterDate
      );
    }

    // Tags filter
    if (filters.tags.length > 0) {
      filtered = filtered.filter((item: MediaItem) =>
        filters.tags.some(tag => item.tags?.includes(tag))
      );
    }

    return filtered;
  }, [mediaItems, searchQuery, filters]);

  const handleMediaSelect = (media: MediaItem) => {
    if (!selectedTarget) return;

    // Use persistent assignment system
    if (selectedTarget.lessonId) {
      assignMediaToLesson(selectedTarget.moduleId, selectedTarget.lessonId, media);
    } else {
      assignMediaToModule(selectedTarget.moduleId, media);
    }

    // Also call the original callback for compatibility
    onMediaAssigned(selectedTarget.moduleId, selectedTarget.lessonId, media);
    
    toast({
      title: "Media Assigned",
      description: `${media.name} has been assigned to ${selectedTarget.title}`
    });

    setSelectedTarget(null);
  };

  const handleMediaRemove = (moduleId: string, lessonId: string | null) => {
    // Use persistent assignment system
    if (lessonId) {
      removeMediaFromLesson(moduleId, lessonId);
    } else {
      removeMediaFromModule(moduleId);
    }

    // Also call the original callback for compatibility
    onMediaRemoved(moduleId, lessonId);
    
    const target = lessonId 
      ? modules.find(m => m.id === moduleId)?.lessons.find(l => l.id === lessonId)
      : modules.find(m => m.id === moduleId);
    
    toast({
      title: "Media Removed",
      description: `Media removed from ${target?.title || 'Unknown'}`
    });
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image': return <ImageIcon className="h-6 w-6" />;
      case 'video': return <PlayCircle className="h-6 w-6" />;
      case 'document': return <FileText className="h-6 w-6" />;
      default: return <FileText className="h-6 w-6" />;
    }
  };

  const renderMediaItem = (item: MediaItem) => (
    <Card 
      key={item.id} 
      className="group relative overflow-hidden hover:shadow-lg transition-all cursor-pointer border-2 border-transparent hover:border-primary/20"
      onClick={() => handleMediaSelect(item)}
    >
      <div className="relative">
        {item.type === 'image' || item.type === 'video' ? (
          <div className="aspect-video bg-gray-100 overflow-hidden">
            <img
              src={item.thumbnailUrl || item.url}
              alt={item.name}
              className="w-full h-full object-cover"
              loading="lazy"
            />
            {item.type === 'video' && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20">
                <PlayCircle className="h-8 w-8 text-white" />
              </div>
            )}
          </div>
        ) : (
          <div className="aspect-video bg-gray-100 flex items-center justify-center">
            {getFileIcon(item.type)}
          </div>
        )}

        {/* Quick actions overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="secondary"
              onClick={(e) => {
                e.stopPropagation();
                setPreviewMedia(item);
              }}
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="default"
              onClick={() => handleMediaSelect(item)}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Source badge */}
        <Badge variant="secondary" className="absolute top-2 right-2 capitalize">
          {item.source}
        </Badge>

        {/* Duration badge for videos */}
        {item.metadata?.duration && (
          <Badge variant="outline" className="absolute bottom-2 right-2">
            {formatDuration(item.metadata.duration)}
          </Badge>
        )}
      </div>

      <CardContent className="p-3">
        <h4 className="font-medium text-sm line-clamp-2 mb-1">
          {item.title || item.name}
        </h4>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
          <span>{item.type}</span>
          <span>{formatFileSize(item.size)}</span>
        </div>

        {item.metadata?.width && item.metadata?.height && (
          <p className="text-xs text-muted-foreground">
            {item.metadata.width} × {item.metadata.height}
          </p>
        )}

        {item.tags && item.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {item.tags.slice(0, 2).map(tag => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {item.tags.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{item.tags.length - 2}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderModuleTarget = (module: Module) => {
    const moduleMedia = getModuleMedia(module.id);
    
    return (
      <Card key={module.id} className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">{module.title}</CardTitle>
            <div className="flex gap-2">
              {moduleMedia ? (
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    {getFileIcon(moduleMedia.type)}
                    <span className="max-w-[100px] truncate">{moduleMedia.name}</span>
                  </Badge>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleMediaRemove(module.id, null)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setSelectedTarget({
                    moduleId: module.id,
                    lessonId: null,
                    title: module.title
                  })}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Assign Media
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-2 max-h-[400px] overflow-y-auto">
            {module.lessons && module.lessons.length > 0 ? (
              module.lessons.map(lesson => {
                const lessonMedia = getLessonMedia(module.id, lesson.id);
                
                return (
                  <div key={lesson.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex-1 min-w-0">
                      <span className="font-medium text-sm block truncate">{lesson.title}</span>
                      {lessonMedia && (
                        <span className="text-xs text-muted-foreground truncate block">
                          Assigned: {lessonMedia.name}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      {lessonMedia ? (
                        <div className="flex items-center gap-2">
                          <Badge variant="default" className="flex items-center gap-1 text-xs">
                            {getFileIcon(lessonMedia.type)}
                            <CheckCircle2 className="h-3 w-3" />
                          </Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleMediaRemove(module.id, lesson.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedTarget({
                            moduleId: module.id,
                            lessonId: lesson.id,
                            title: lesson.title
                          })}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Assign
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                <span className="text-sm">No lessons in this module</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (selectedTarget) {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedTarget(null)}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h2 className="text-xl font-semibold">Select Media</h2>
              <p className="text-muted-foreground">
                Choose media for: <strong>{selectedTarget.title}</strong>
              </p>
            </div>
          </div>
          {onClose && (
            <Button variant="outline" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Search and Filters */}
        <div className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search media by name, title, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? 'bg-muted' : ''}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
            <Button
              variant="outline"
              onClick={() => refetch()}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>

          {/* Media Type Tabs */}
          <Tabs value={filters.type} onValueChange={(value) => 
            setFilters(prev => ({ ...prev, type: value as any }))
          }>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="all">All Media</TabsTrigger>
              <TabsTrigger value="image">Images</TabsTrigger>
              <TabsTrigger value="video">Videos</TabsTrigger>
              <TabsTrigger value="document">Documents</TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Extended Filters */}
          {showFilters && (
            <Card className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Source</label>
                  <select
                    value={filters.source}
                    onChange={(e) => setFilters(prev => ({ ...prev, source: e.target.value as any }))}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="all">All Sources</option>
                    <option value="upload">Uploaded</option>
                    <option value="pexels">Pexels</option>
                    <option value="pixabay">Pixabay</option>
                  </select>
                </div>
                
                <div>
                  <label className="text-sm font-medium mb-2 block">Upload Date</label>
                  <select
                    value={filters.uploadDate}
                    onChange={(e) => setFilters(prev => ({ ...prev, uploadDate: e.target.value as any }))}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={() => setFilters({
                      type: 'all',
                      source: 'all',
                      uploadDate: 'all',
                      tags: []
                    })}
                    className="w-full"
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Results header */}
        {filteredMedia().length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              {filteredMedia().length} media items found
            </p>
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant={viewMode === 'list' ? 'default' : 'outline'}
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Media Grid */}
        <ScrollArea className="max-h-[60vh]">
          {isLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading media library...</p>
            </div>
          ) : filteredMedia().length === 0 ? (
            <div className="text-center py-12">
              <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium mb-2">No media found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || showFilters ? 'Try adjusting your search or filters' : 'Upload some media to get started'}
              </p>
              <Button variant="outline" onClick={() => {
                setSearchQuery('');
                setFilters({
                  type: 'all',
                  source: 'all',
                  uploadDate: 'all',
                  tags: []
                });
              }}>
                Clear search
              </Button>
            </div>
          ) : (
            <div className={viewMode === 'grid' 
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
              : "space-y-4"
            }>
              {filteredMedia().map(renderMediaItem)}
            </div>
          )}
        </ScrollArea>

        {/* Preview Dialog */}
        <Dialog open={!!previewMedia} onOpenChange={() => setPreviewMedia(null)}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>{previewMedia?.title || previewMedia?.name}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setPreviewMedia(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </DialogTitle>
            </DialogHeader>
            
            {previewMedia && (
              <div className="space-y-4">
                <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                  {previewMedia.type === 'video' ? (
                    <video
                      src={previewMedia.url}
                      controls
                      className="w-full h-full object-contain"
                      poster={previewMedia.thumbnailUrl}
                    />
                  ) : previewMedia.type === 'image' ? (
                    <img
                      src={previewMedia.url}
                      alt={previewMedia.name}
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      {getFileIcon(previewMedia.type)}
                    </div>
                  )}
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p><strong>Type:</strong> {previewMedia.type}</p>
                    <p><strong>Size:</strong> {formatFileSize(previewMedia.size)}</p>
                    <p><strong>Source:</strong> {previewMedia.source}</p>
                  </div>
                  <div>
                    {previewMedia.metadata?.width && previewMedia.metadata?.height && (
                      <p><strong>Dimensions:</strong> {previewMedia.metadata.width} × {previewMedia.metadata.height}</p>
                    )}
                    {previewMedia.metadata?.duration && (
                      <p><strong>Duration:</strong> {formatDuration(previewMedia.metadata.duration)}</p>
                    )}
                    <p><strong>Uploaded:</strong> {new Date(previewMedia.uploadedAt).toLocaleDateString()}</p>
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button
                    onClick={() => {
                      handleMediaSelect(previewMedia);
                      setPreviewMedia(null);
                    }}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Assign to {selectedTarget.title}
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  return (
    <div className="space-y-6 h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between flex-shrink-0">
        <div>
          <h2 className="text-xl font-semibold">Media Assignment</h2>
          <p className="text-muted-foreground">
            Assign media files to your course modules and lessons
          </p>
        </div>
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Assignment Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ImageIcon className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-800">
              Media Assignment Progress
            </span>
          </div>
          <div className="text-sm text-blue-700">
            {getStats().totalAssignments} assignments made
          </div>
        </div>
      </div>

      {/* Course Structure - Full Height with Scroll */}
      <div className="flex-1 min-h-0">
        <ScrollArea className="h-full">
          <div className="space-y-4 pr-4">
            {modules.length === 0 ? (
              <div className="text-center py-12">
                <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="font-medium mb-2">No Course Structure Found</h3>
                <p className="text-muted-foreground">
                  Please create your course structure first to assign media to lessons and modules.
                </p>
              </div>
            ) : (
              modules.map(renderModuleTarget)
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};

export default MediaSelector;