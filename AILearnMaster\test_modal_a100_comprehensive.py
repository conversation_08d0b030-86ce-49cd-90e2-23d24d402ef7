#!/usr/bin/env python3
"""
Comprehensive Modal A100 GPU Diagnostic and Testing Script
Tests all 7 AI services with detailed logging and error reporting
"""

import requests
import base64
import json
import time
import io
from PIL import Image, ImageDraw
import wave
import struct
import math

# Modal A100 URL - Update this with your actual Modal URL
MODAL_URL = "https://hajhasni1984--courseai-a100-production.modal.run"

class ModalA100Tester:
    def __init__(self, base_url=MODAL_URL):
        self.base_url = base_url
        self.results = {}
        
    def create_test_image(self):
        """Create a simple test image for avatar generation"""
        img = Image.new('RGB', (512, 512), color='white')
        draw = ImageDraw.Draw(img)
        
        # Draw a simple face
        draw.ellipse([100, 100, 400, 400], fill='lightblue', outline='black')  # Face
        draw.ellipse([150, 180, 200, 230], fill='black')  # Left eye
        draw.ellipse([300, 180, 350, 230], fill='black')  # Right eye
        draw.ellipse([220, 280, 280, 320], fill='pink')   # Nose
        draw.arc([180, 320, 320, 380], 0, 180, fill='black', width=3)  # Mouth
        
        # Convert to base64
        buffer = io.BytesIO()
        img.save(buffer, format='JPEG')
        return base64.b64encode(buffer.getvalue()).decode()
    
    def create_test_audio(self, duration=3, frequency=440):
        """Create a simple test audio file"""
        sample_rate = 44100
        frames = int(duration * sample_rate)
        
        # Generate sine wave
        audio_data = []
        for i in range(frames):
            value = int(32767 * math.sin(2 * math.pi * frequency * i / sample_rate))
            audio_data.append(struct.pack('<h', value))
        
        # Create WAV file in memory
        buffer = io.BytesIO()
        with wave.open(buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 2 bytes per sample
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(b''.join(audio_data))
        
        return base64.b64encode(buffer.getvalue()).decode()
    
    def test_health_check(self):
        """Test the health check endpoint"""
        print("🔍 Testing Health Check...")
        try:
            response = requests.get(f"{self.base_url}/health", timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                self.results['health'] = {
                    'status': 'success',
                    'data': data,
                    'gpu_available': data.get('gpu_available', False),
                    'services': data.get('services', {})
                }
                
                print(f"✅ Health Check: {data.get('status', 'unknown')}")
                print(f"   GPU: {data.get('gpu_name', 'Unknown')}")
                print(f"   Memory: {data.get('gpu_memory_free_gb', 0):.1f}GB free")
                
                services = data.get('services', {})
                for service, status in services.items():
                    status_icon = "✅" if status else "❌"
                    print(f"   {service}: {status_icon}")
                    
            else:
                self.results['health'] = {
                    'status': 'error',
                    'error': f"HTTP {response.status_code}: {response.text}"
                }
                print(f"❌ Health Check failed: {response.status_code}")
                
        except Exception as e:
            self.results['health'] = {'status': 'error', 'error': str(e)}
            print(f"❌ Health Check error: {e}")
    
    def test_mistral_llm(self):
        """Test Mistral LLM content generation"""
        print("\n🧠 Testing Mistral LLM...")
        try:
            payload = {
                "prompt": "Create a brief course outline for learning Python programming:",
                "max_tokens": 500,
                "temperature": 0.7
            }
            
            response = requests.post(f"{self.base_url}/mistral", json=payload, timeout=120)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.results['mistral'] = {'status': 'success', 'data': data}
                    print("✅ Mistral LLM: Content generated successfully")
                    print(f"   Generated {len(data.get('generated_text', ''))} characters")
                else:
                    self.results['mistral'] = {'status': 'error', 'error': data.get('error')}
                    print(f"❌ Mistral LLM failed: {data.get('error')}")
            else:
                self.results['mistral'] = {'status': 'error', 'error': f"HTTP {response.status_code}"}
                print(f"❌ Mistral LLM HTTP error: {response.status_code}")
                
        except Exception as e:
            self.results['mistral'] = {'status': 'error', 'error': str(e)}
            print(f"❌ Mistral LLM error: {e}")
    
    def test_sdxl_image_generation(self):
        """Test SDXL image generation"""
        print("\n🎨 Testing SDXL Image Generation...")
        try:
            payload = {
                "prompt": "A professional headshot of a teacher in a classroom",
                "negative_prompt": "blurry, low quality",
                "width": 512,
                "height": 512,
                "num_inference_steps": 20,
                "guidance_scale": 7.5
            }
            
            response = requests.post(f"{self.base_url}/sdxl", json=payload, timeout=180)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.results['sdxl'] = {'status': 'success', 'data': data}
                    print("✅ SDXL: Image generated successfully")
                    print(f"   Size: {data.get('width')}x{data.get('height')}")
                else:
                    self.results['sdxl'] = {'status': 'error', 'error': data.get('error')}
                    print(f"❌ SDXL failed: {data.get('error')}")
            else:
                self.results['sdxl'] = {'status': 'error', 'error': f"HTTP {response.status_code}"}
                print(f"❌ SDXL HTTP error: {response.status_code}")
                
        except Exception as e:
            self.results['sdxl'] = {'status': 'error', 'error': str(e)}
            print(f"❌ SDXL error: {e}")
    
    def test_coqui_tts(self):
        """Test Coqui TTS voice synthesis"""
        print("\n🎤 Testing Coqui TTS...")
        try:
            payload = {
                "text": "Hello, this is a test of the Coqui text-to-speech system running on Modal A100 GPU.",
                "voice_preset": "tts_models/en/ljspeech/tacotron2-DDC",
                "speed": 1.0,
                "volume": 1.0
            }
            
            response = requests.post(f"{self.base_url}/tts", json=payload, timeout=120)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.results['tts'] = {'status': 'success', 'data': data}
                    print("✅ Coqui TTS: Audio generated successfully")
                    print(f"   Voice: {data.get('voice_preset')}")
                    print(f"   Format: {data.get('audio_format')}")
                else:
                    self.results['tts'] = {'status': 'error', 'error': data.get('error')}
                    print(f"❌ Coqui TTS failed: {data.get('error')}")
            else:
                self.results['tts'] = {'status': 'error', 'error': f"HTTP {response.status_code}"}
                print(f"❌ Coqui TTS HTTP error: {response.status_code}")
                
        except Exception as e:
            self.results['tts'] = {'status': 'error', 'error': str(e)}
            print(f"❌ Coqui TTS error: {e}")
    
    def test_echomimic_avatar(self):
        """Test EchoMimic V2 avatar generation"""
        print("\n👤 Testing EchoMimic V2 Avatar...")
        try:
            # Use test image and audio
            test_image = self.create_test_image()
            test_audio = self.create_test_audio()
            
            payload = {
                "image_base64": test_image,
                "audio_base64": test_audio,
                "style": "professional",
                "background": "studio"
            }
            
            response = requests.post(f"{self.base_url}/api_avatar", json=payload, timeout=300)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.results['avatar'] = {'status': 'success', 'data': data}
                    print("✅ EchoMimic V2: Avatar video generated successfully")
                    print(f"   Style: {data.get('style')}")
                    print(f"   Format: {data.get('video_format')}")
                else:
                    self.results['avatar'] = {'status': 'error', 'error': data.get('error')}
                    print(f"❌ EchoMimic V2 failed: {data.get('error')}")
            else:
                self.results['avatar'] = {'status': 'error', 'error': f"HTTP {response.status_code}"}
                print(f"❌ EchoMimic V2 HTTP error: {response.status_code}")
                
        except Exception as e:
            self.results['avatar'] = {'status': 'error', 'error': str(e)}
            print(f"❌ EchoMimic V2 error: {e}")
    
    def test_whisper_stt(self):
        """Test Whisper speech-to-text"""
        print("\n🎧 Testing Whisper STT...")
        try:
            # Create test audio with speech-like content
            test_audio = self.create_test_audio(duration=2, frequency=200)
            
            payload = {
                "audio_base64": test_audio,
                "model_size": "base",
                "output_format": "srt"
            }
            
            response = requests.post(f"{self.base_url}/whisper", json=payload, timeout=120)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.results['whisper'] = {'status': 'success', 'data': data}
                    print("✅ Whisper: Transcription completed successfully")
                    print(f"   Model: {data.get('model_size')}")
                    print(f"   Format: {data.get('output_format')}")
                else:
                    self.results['whisper'] = {'status': 'error', 'error': data.get('error')}
                    print(f"❌ Whisper failed: {data.get('error')}")
            else:
                self.results['whisper'] = {'status': 'error', 'error': f"HTTP {response.status_code}"}
                print(f"❌ Whisper HTTP error: {response.status_code}")
                
        except Exception as e:
            self.results['whisper'] = {'status': 'error', 'error': str(e)}
            print(f"❌ Whisper error: {e}")
    
    def test_marp_slides(self):
        """Test Marp slide generation"""
        print("\n📊 Testing Marp Slides...")
        try:
            payload = {
                "markdown_content": """# Test Presentation

## Slide 1
This is a test slide generated by Marp.

## Slide 2
- Point 1
- Point 2
- Point 3

## Conclusion
Thank you for testing!""",
                "theme": "default",
                "output_format": "pdf"
            }
            
            response = requests.post(f"{self.base_url}/slides", json=payload, timeout=120)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.results['slides'] = {'status': 'success', 'data': data}
                    print("✅ Marp: Slides generated successfully")
                    print(f"   Theme: {data.get('theme')}")
                    print(f"   Format: {data.get('format')}")
                else:
                    self.results['slides'] = {'status': 'error', 'error': data.get('error')}
                    print(f"❌ Marp failed: {data.get('error')}")
            else:
                self.results['slides'] = {'status': 'error', 'error': f"HTTP {response.status_code}"}
                print(f"❌ Marp HTTP error: {response.status_code}")
                
        except Exception as e:
            self.results['slides'] = {'status': 'error', 'error': str(e)}
            print(f"❌ Marp error: {e}")
    
    def test_ffmpeg_processing(self):
        """Test FFmpeg video processing"""
        print("\n🎬 Testing FFmpeg...")
        try:
            # Create simple test data
            test_video = base64.b64encode(b"fake_video_data").decode()
            test_audio = self.create_test_audio()
            
            payload = {
                "video_base64": test_video,
                "audio_base64": test_audio,
                "operation": "merge",
                "output_format": "mp4"
            }
            
            response = requests.post(f"{self.base_url}/ffmpeg", json=payload, timeout=120)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.results['ffmpeg'] = {'status': 'success', 'data': data}
                    print("✅ FFmpeg: Video processing completed successfully")
                    print(f"   Operation: {data.get('operation')}")
                    print(f"   Format: {data.get('output_format')}")
                else:
                    self.results['ffmpeg'] = {'status': 'error', 'error': data.get('error')}
                    print(f"❌ FFmpeg failed: {data.get('error')}")
            else:
                self.results['ffmpeg'] = {'status': 'error', 'error': f"HTTP {response.status_code}"}
                print(f"❌ FFmpeg HTTP error: {response.status_code}")
                
        except Exception as e:
            self.results['ffmpeg'] = {'status': 'error', 'error': str(e)}
            print(f"❌ FFmpeg error: {e}")
    
    def run_comprehensive_test(self):
        """Run all tests and generate report"""
        print("🚀 Starting Comprehensive Modal A100 GPU Testing...")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run all tests
        self.test_health_check()
        self.test_mistral_llm()
        self.test_sdxl_image_generation()
        self.test_coqui_tts()
        self.test_echomimic_avatar()
        self.test_whisper_stt()
        self.test_marp_slides()
        self.test_ffmpeg_processing()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Generate report
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        successful_tests = 0
        total_tests = len(self.results)
        
        for service, result in self.results.items():
            status_icon = "✅" if result.get('status') == 'success' else "❌"
            print(f"{status_icon} {service.upper()}: {result.get('status', 'unknown')}")
            
            if result.get('status') == 'success':
                successful_tests += 1
            elif result.get('error'):
                print(f"   Error: {result['error']}")
        
        print(f"\nSUCCESS RATE: {successful_tests}/{total_tests} ({(successful_tests/total_tests)*100:.1f}%)")
        print(f"TOTAL TIME: {total_time:.1f} seconds")
        
        # Save detailed results
        with open('modal_a100_test_results.json', 'w') as f:
            json.dump({
                'timestamp': time.time(),
                'total_time': total_time,
                'success_rate': successful_tests / total_tests,
                'results': self.results
            }, f, indent=2)
        
        print(f"\nDetailed results saved to: modal_a100_test_results.json")
        
        return successful_tests == total_tests

if __name__ == "__main__":
    tester = ModalA100Tester()
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n🎉 All Modal A100 services are working correctly!")
        exit(0)
    else:
        print("\n⚠️  Some services failed. Check the report above for details.")
        exit(1)
