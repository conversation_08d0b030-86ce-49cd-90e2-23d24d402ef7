import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Play, 
  Pause, 
  Download, 
  Volume2, 
  Mic, 
  Sparkles, 
  X,
  CheckCircle2,
  Clock,
  AlertCircle,
  Wand2,
  Settings,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { VoiceServiceSelector } from '@/components/voice/VoiceServiceSelector';
import VoicePreviewSystem from '@/components/voice/VoicePreviewSystem';

interface Voice {
  id: string;
  name: string;
  gender?: string;
  accent?: string;
  language?: string;
  preview_url?: string;
}

interface VoiceProject {
  id: string;
  moduleId: string;
  lessonId: string;
  moduleTitle: string;
  lessonTitle: string;
  script: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  audioUrl?: string;
  progress: number;
}

interface VoiceGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  modules: any[];
  scripts: any;
  onVoiceGenerated?: (results: any[]) => void;
}

export default function VoiceGenerationModal({ 
  isOpen, 
  onClose, 
  modules, 
  scripts,
  onVoiceGenerated 
}: VoiceGenerationModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // State management
  const [voiceSettings, setVoiceSettings] = useState<any>(null);
  const [voiceProjects, setVoiceProjects] = useState<VoiceProject[]>([]);
  const [isGeneratingAll, setIsGeneratingAll] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const [playingPreview, setPlayingPreview] = useState<string | null>(null);
  const [selectedVoice, setSelectedVoice] = useState<string>('');
  const [availableVoices, setAvailableVoices] = useState<Voice[]>([]);

  // Initialize voice projects from modules and scripts
  useEffect(() => {
    if (!isOpen) return;

    const projects: VoiceProject[] = [];
    
    modules.forEach(module => {
      module.lessons?.forEach((lesson: any) => {
        const script = scripts[module.id]?.[lesson.id] || '';
        if (script) {
          projects.push({
            id: `${module.id}-${lesson.id}`,
            moduleId: module.id,
            lessonId: lesson.id,
            moduleTitle: module.title,
            lessonTitle: lesson.title,
            script,
            status: 'pending',
            progress: 0
          });
        }
      });
    });
    
    setVoiceProjects(projects);

    // Initialize available voices with default options
    const defaultVoices: Voice[] = [
      { id: 'alloy', name: 'Alloy (OpenAI)', gender: 'neutral', language: 'en' },
      { id: 'echo', name: 'Echo (OpenAI)', gender: 'male', language: 'en' },
      { id: 'fable', name: 'Fable (OpenAI)', gender: 'neutral', language: 'en' },
      { id: 'onyx', name: 'Onyx (OpenAI)', gender: 'male', language: 'en' },
      { id: 'nova', name: 'Nova (OpenAI)', gender: 'female', language: 'en' },
      { id: 'shimmer', name: 'Shimmer (OpenAI)', gender: 'female', language: 'en' }
    ];
    setAvailableVoices(defaultVoices);
    if (!selectedVoice && defaultVoices.length > 0) {
      setSelectedVoice(defaultVoices[0].id);
    }
  }, [modules, scripts, isOpen]);

  const totalLessons = voiceProjects.length;
  const completedVoices = voiceProjects.filter(p => p.status === 'completed').length;
  const processingVoices = voiceProjects.filter(p => p.status === 'processing').length;

  // Generate single voice audio
  const generateSingleVoice = async (projectId: string) => {
    const project = voiceProjects.find(p => p.id === projectId);
    if (!project) return;

    setVoiceProjects(prev => prev.map(p => 
      p.id === projectId 
        ? { ...p, status: 'processing', progress: 0 }
        : p
    ));

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setVoiceProjects(prev => prev.map(p => 
          p.id === projectId 
            ? { ...p, progress: Math.min(p.progress + 20, 90) }
            : p
        ));
      }, 500);

      const response = await apiRequest('POST', '/api/ai/text-to-speech', {
        text: project.script,
        voice: selectedVoice,
        format: 'mp3',
        title: `${project.moduleTitle} - ${project.lessonTitle}`
      });

      clearInterval(progressInterval);

      if (response.audioUrl) {
        setVoiceProjects(prev => prev.map(p => 
          p.id === projectId 
            ? { 
                ...p, 
                status: 'completed', 
                progress: 100,
                audioUrl: response.audioUrl
              }
            : p
        ));

        toast({
          title: "Voice Generated",
          description: `Audio created for "${project.lessonTitle}"`,
        });
      } else {
        throw new Error('No audio URL returned');
      }

    } catch (error: any) {
      setVoiceProjects(prev => prev.map(p => 
        p.id === projectId 
          ? { ...p, status: 'error', progress: 0 }
          : p
      ));

      toast({
        title: "Generation Failed",
        description: `Failed to generate audio for "${project.lessonTitle}": ${error.message}`,
        variant: "destructive",
      });
    }
  };

  // Generate all voice audio
  const generateAllVoices = async () => {
    setIsGeneratingAll(true);
    setOverallProgress(0);
    
    const pendingProjects = voiceProjects.filter(p => p.status === 'pending');
    
    for (let i = 0; i < pendingProjects.length; i++) {
      const project = pendingProjects[i];
      await generateSingleVoice(project.id);
      setOverallProgress(((i + 1) / pendingProjects.length) * 100);
    }
    
    setIsGeneratingAll(false);
    
    const completedResults = voiceProjects
      .filter(p => p.status === 'completed')
      .map(p => ({
        moduleId: p.moduleId,
        lessonId: p.lessonId,
        audioUrl: p.audioUrl,
        title: p.lessonTitle
      }));

    if (onVoiceGenerated) {
      onVoiceGenerated(completedResults);
    }

    toast({
      title: "All Voices Generated",
      description: `Successfully generated ${pendingProjects.length} voice recordings.`,
    });
  };

  // Preview voice
  const previewVoice = async (voiceId: string) => {
    try {
      const sampleText = "Hello! This is a preview of how your course narration will sound. The voice will be clear, professional, and engaging for your students.";
      
      const response = await apiRequest('POST', '/api/ai/text-to-speech', {
        text: sampleText,
        voice: voiceId,
        format: 'mp3'
      });

      if (response.audioUrl) {
        const audio = new Audio(response.audioUrl);
        audio.play();
        setPlayingPreview(voiceId);
        audio.onended = () => setPlayingPreview(null);
      }
    } catch (error: any) {
      toast({
        title: "Preview Failed",
        description: error.message || "Failed to generate voice preview",
        variant: "destructive",
      });
    }
  };

  // Download audio
  const downloadAudio = (project: VoiceProject) => {
    if (!project.audioUrl) return;
    
    const link = document.createElement('a');
    link.href = project.audioUrl;
    link.download = `${project.moduleTitle}-${project.lessonTitle}.mp3`;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Complete';
      case 'processing': return 'Processing';
      case 'error': return 'Failed';
      default: return 'Pending';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            Voice Generation Studio
            <Badge variant="secondary" className="ml-auto">
              OpenAI TTS
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Voice Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Mic className="h-5 w-5 text-blue-500" />
                  <span className="text-sm font-medium">Total Lessons</span>
                </div>
                <p className="text-2xl font-bold mt-1">{totalLessons}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <span className="text-sm font-medium">Completed</span>
                </div>
                <p className="text-2xl font-bold mt-1">{completedVoices}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-5 w-5 text-purple-500" />
                  <span className="text-sm font-medium">Processing</span>
                </div>
                <p className="text-2xl font-bold mt-1">{processingVoices}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Volume2 className="h-5 w-5 text-yellow-500" />
                  <span className="text-sm font-medium">Progress</span>
                </div>
                <p className="text-2xl font-bold mt-1">{Math.round((completedVoices / totalLessons) * 100)}%</p>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="generation" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="generation">Voice Generation</TabsTrigger>
              <TabsTrigger value="settings">Voice Settings</TabsTrigger>
            </TabsList>

            {/* Generation Tab */}
            <TabsContent value="generation" className="space-y-6">
              {/* Bulk Generation */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wand2 className="h-5 w-5" />
                    Bulk Voice Generation
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Generate All Voices</p>
                      <p className="text-sm text-muted-foreground">
                        Creates professional voice narration for all lesson scripts using OpenAI TTS
                      </p>
                    </div>
                    <Button 
                      onClick={generateAllVoices}
                      disabled={isGeneratingAll || voiceProjects.every(p => p.status === 'completed')}
                      className="min-w-[140px]"
                    >
                      {isGeneratingAll ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Generate All
                        </>
                      )}
                    </Button>
                  </div>
                  
                  {isGeneratingAll && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Overall Progress</span>
                        <span>{Math.round(overallProgress)}%</span>
                      </div>
                      <Progress value={overallProgress} className="w-full" />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Individual Voice Projects */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Individual Voice Projects</h3>
                <div className="grid gap-4">
                  {modules.map(module => (
                    <Card key={module.id} className="border-l-4 border-l-blue-500">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Volume2 className="h-5 w-5" />
                          {module.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {module.lessons?.map((lesson: any) => {
                            const project = voiceProjects.find(p => p.moduleId === module.id && p.lessonId === lesson.id);
                            if (!project) return null;

                            return (
                              <div key={lesson.id} className="border rounded-lg p-4 bg-gray-50">
                                <div className="flex items-center justify-between mb-2">
                                  <h4 className="font-medium">{lesson.title}</h4>
                                  <div className="flex items-center gap-2">
                                    {getStatusIcon(project.status)}
                                    <Badge variant="outline">
                                      {getStatusText(project.status)}
                                    </Badge>
                                  </div>
                                </div>
                                
                                {project.status === 'processing' && (
                                  <div className="space-y-2 mb-3">
                                    <div className="flex justify-between text-sm">
                                      <span>Generating audio...</span>
                                      <span>{project.progress}%</span>
                                    </div>
                                    <Progress value={project.progress} className="w-full" />
                                  </div>
                                )}
                                
                                <div className="flex items-center justify-between">
                                  <p className="text-sm text-muted-foreground">
                                    Script: {project.script.length} characters
                                  </p>
                                  <div className="flex gap-2">
                                    {project.status === 'completed' && (
                                      <>
                                        <Button variant="outline" size="sm" onClick={() => downloadAudio(project)}>
                                          <Download className="h-4 w-4 mr-2" />
                                          Download
                                        </Button>
                                        {project.audioUrl && (
                                          <Button variant="outline" size="sm" onClick={() => {
                                            const audio = new Audio(project.audioUrl);
                                            audio.play();
                                          }}>
                                            <Play className="h-4 w-4 mr-2" />
                                            Play
                                          </Button>
                                        )}
                                      </>
                                    )}
                                    {project.status === 'pending' && (
                                      <Button 
                                        size="sm"
                                        onClick={() => generateSingleVoice(project.id)}
                                      >
                                        <Play className="h-4 w-4 mr-2" />
                                        Generate
                                      </Button>
                                    )}
                                    {project.status === 'error' && (
                                      <Button 
                                        size="sm" 
                                        variant="destructive"
                                        onClick={() => generateSingleVoice(project.id)}
                                      >
                                        <RefreshCw className="h-4 w-4 mr-2" />
                                        Retry
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              <VoicePreviewSystem
                selectedVoice={selectedVoice}
                onVoiceSelect={(voiceId, provider) => {
                  setSelectedVoice(voiceId);
                  setVoiceSettings(prev => ({ ...prev, provider }));
                }}
                previewText="Welcome to your course! This is how your voice will sound throughout the lessons."
                onVoiceSettingsChange={(settings) => {
                  setVoiceSettings(prev => ({ ...prev, ...settings }));
                }}
              />
            </TabsContent>
          </Tabs>

          {/* Footer Actions */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="text-sm text-muted-foreground">
              {completedVoices} of {totalLessons} voices completed
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              {completedVoices > 0 && (
                <Button onClick={onClose}>
                  Continue with {completedVoices} Voice{completedVoices !== 1 ? 's' : ''}
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}