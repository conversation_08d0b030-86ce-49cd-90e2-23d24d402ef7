import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { 
  Play, 
  Upload, 
  Video, 
  Eye, 
  ThumbsUp,
  Share2,
  Bar<PERSON><PERSON>3,
  <PERSON>,
  <PERSON>,
  TrendingUp,
  <PERSON><PERSON>s,
  ExternalLink,
  Loader2,
  CheckCircle,
  Calendar,
  Globe
} from 'lucide-react';

interface VideoPlatform {
  id: string;
  name: string;
  description: string;
  logo: string;
  category: 'social' | 'professional' | 'education' | 'live';
  features: string[];
  maxFileSize: string;
  supportedFormats: string[];
  monetization: boolean;
  analytics: boolean;
  liveStreaming: boolean;
  status: 'connected' | 'available' | 'premium';
}

interface VideoContent {
  id: number;
  title: string;
  platform: string;
  status: 'published' | 'processing' | 'scheduled' | 'draft';
  views: number;
  likes: number;
  duration: string;
  publishDate: string;
  thumbnail: string;
}

export default function VideoPlatforms() {
  const [selectedPlatform, setSelectedPlatform] = useState<VideoPlatform | null>(null);
  const [uploadData, setUploadData] = useState({
    title: '',
    description: '',
    tags: '',
    privacy: 'public',
    scheduleDate: '',
    platform: ''
  });
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch video content
  const { data: videoContent = [], isLoading: contentLoading } = useQuery({
    queryKey: ['/api/video/content'],
    queryFn: () => apiRequest('/api/video/content')
  });

  // Fetch video analytics
  const { data: analytics, isLoading: analyticsLoading } = useQuery({
    queryKey: ['/api/video/analytics'],
    queryFn: () => apiRequest('/api/video/analytics')
  });

  // Upload video mutation
  const uploadVideoMutation = useMutation({
    mutationFn: async (data: any) => {
      return apiRequest('/api/video/upload', {
        method: 'POST',
        body: JSON.stringify(data)
      });
    },
    onSuccess: () => {
      toast({
        title: "Video Uploaded",
        description: "Your video has been uploaded successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/video/content'] });
      setSelectedPlatform(null);
      setUploadData({
        title: '',
        description: '',
        tags: '',
        privacy: 'public',
        scheduleDate: '',
        platform: ''
      });
    },
    onError: (error: any) => {
      toast({
        title: "Upload Failed",
        description: error.message || "Failed to upload video",
        variant: "destructive",
      });
    }
  });

  const videoPlatforms: VideoPlatform[] = [
    {
      id: 'youtube',
      name: 'YouTube',
      description: 'World\'s largest video platform with massive reach',
      logo: '📺',
      category: 'social',
      features: ['Monetization', 'Live streaming', 'Analytics', 'Community'],
      maxFileSize: '256 GB',
      supportedFormats: ['MP4', 'MOV', 'AVI', 'WMV', 'FLV'],
      monetization: true,
      analytics: true,
      liveStreaming: true,
      status: 'connected'
    },
    {
      id: 'vimeo',
      name: 'Vimeo',
      description: 'Professional video hosting with high quality',
      logo: '🎬',
      category: 'professional',
      features: ['High quality', 'No ads', 'Custom players', 'Privacy controls'],
      maxFileSize: '5 GB',
      supportedFormats: ['MP4', 'MOV', 'AVI', 'WMV'],
      monetization: false,
      analytics: true,
      liveStreaming: true,
      status: 'premium'
    },
    {
      id: 'twitch',
      name: 'Twitch',
      description: 'Live streaming platform for interactive content',
      logo: '🎮',
      category: 'live',
      features: ['Live streaming', 'Chat interaction', 'Clips', 'VODs'],
      maxFileSize: 'Unlimited',
      supportedFormats: ['Live stream', 'MP4 (VODs)'],
      monetization: true,
      analytics: true,
      liveStreaming: true,
      status: 'available'
    },
    {
      id: 'linkedin',
      name: 'LinkedIn Video',
      description: 'Professional network video sharing',
      logo: '💼',
      category: 'professional',
      features: ['Professional audience', 'Native player', 'Engagement tracking'],
      maxFileSize: '5 GB',
      supportedFormats: ['MP4', 'MOV', 'AVI'],
      monetization: false,
      analytics: true,
      liveStreaming: true,
      status: 'available'
    },
    {
      id: 'facebook',
      name: 'Facebook Video',
      description: 'Social video sharing with wide reach',
      logo: '📘',
      category: 'social',
      features: ['Social sharing', 'Live streaming', 'Watch parties', 'Monetization'],
      maxFileSize: '10 GB',
      supportedFormats: ['MP4', 'MOV'],
      monetization: true,
      analytics: true,
      liveStreaming: true,
      status: 'available'
    },
    {
      id: 'tiktok',
      name: 'TikTok',
      description: 'Short-form video content platform',
      logo: '🎵',
      category: 'social',
      features: ['Short videos', 'Viral potential', 'Music library', 'Effects'],
      maxFileSize: '4 GB',
      supportedFormats: ['MP4', 'MOV'],
      monetization: true,
      analytics: true,
      liveStreaming: true,
      status: 'available'
    }
  ];

  const mockVideoContent: VideoContent[] = [
    {
      id: 1,
      title: 'Introduction to Web Development',
      platform: 'YouTube',
      status: 'published',
      views: 15420,
      likes: 892,
      duration: '12:45',
      publishDate: '2025-06-20',
      thumbnail: '/placeholder-thumbnail.jpg'
    },
    {
      id: 2,
      title: 'React Hooks Explained',
      platform: 'YouTube',
      status: 'published',
      views: 8750,
      likes: 634,
      duration: '18:30',
      publishDate: '2025-06-18',
      thumbnail: '/placeholder-thumbnail.jpg'
    },
    {
      id: 3,
      title: 'Live Coding Session',
      platform: 'Twitch',
      status: 'scheduled',
      views: 0,
      likes: 0,
      duration: '60:00',
      publishDate: '2025-06-25',
      thumbnail: '/placeholder-thumbnail.jpg'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'connected': return 'bg-green-100 text-green-800';
      case 'premium': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'social': return <Share2 className="h-4 w-4" />;
      case 'professional': return <Users className="h-4 w-4" />;
      case 'education': return <Play className="h-4 w-4" />;
      case 'live': return <Video className="h-4 w-4" />;
      default: return <Video className="h-4 w-4" />;
    }
  };

  const handleUploadVideo = () => {
    if (!uploadData.title || !uploadData.platform) return;

    uploadVideoMutation.mutate(uploadData);
  };

  if (contentLoading || analyticsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Video Platforms</h1>
          <p className="text-muted-foreground mt-2">
            Distribute your video content across multiple platforms to maximize reach
          </p>
        </div>
        <Button onClick={() => setSelectedPlatform({} as VideoPlatform)}>
          <Upload className="h-4 w-4 mr-2" />
          Upload Video
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="platforms">Platforms</TabsTrigger>
          <TabsTrigger value="content">Content Library</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Video Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">24,170</div>
                <p className="text-xs text-muted-foreground">+18.2% from last month</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Likes</CardTitle>
                <ThumbsUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,526</div>
                <p className="text-xs text-muted-foreground">+12.3% from last month</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Published Videos</CardTitle>
                <Video className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockVideoContent.filter(v => v.status === 'published').length}</div>
                <p className="text-xs text-muted-foreground">Across 2 platforms</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Watch Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">142h</div>
                <p className="text-xs text-muted-foreground">+25.1% from last month</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Videos */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Videos</CardTitle>
              <CardDescription>Your latest video content across all platforms</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockVideoContent.slice(0, 3).map((video) => (
                  <div key={video.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center text-white">
                        <Play className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{video.title}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-sm text-muted-foreground">{video.platform}</span>
                          <Badge className={getStatusColor(video.status)}>
                            {video.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <p className="text-sm font-medium">{video.views.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">Views</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium">{video.likes.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">Likes</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium">{video.duration}</p>
                        <p className="text-xs text-muted-foreground">Duration</p>
                      </div>
                      <Button size="sm" variant="outline">
                        <ExternalLink className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="platforms" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {videoPlatforms.map((platform) => (
              <Card key={platform.id} className="relative hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-3xl">{platform.logo}</span>
                      <div>
                        <CardTitle className="text-xl">{platform.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          {getCategoryIcon(platform.category)}
                          <span className="text-sm text-muted-foreground">{platform.category}</span>
                          <Badge className={getStatusColor(platform.status)}>
                            {platform.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{platform.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Max file size:</span>
                      <p className="text-muted-foreground">{platform.maxFileSize}</p>
                    </div>
                    <div>
                      <span className="font-medium">Formats:</span>
                      <p className="text-muted-foreground">{platform.supportedFormats.slice(0, 2).join(', ')}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {platform.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <CheckCircle className={`h-4 w-4 mr-1 ${platform.monetization ? 'text-green-500' : 'text-gray-400'}`} />
                        <span>Monetization</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className={`h-4 w-4 mr-1 ${platform.liveStreaming ? 'text-green-500' : 'text-gray-400'}`} />
                        <span>Live</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      className="flex-1"
                      onClick={() => setSelectedPlatform(platform)}
                      disabled={platform.status === 'premium'}
                    >
                      {platform.status === 'connected' ? (
                        <>
                          <Upload className="h-4 w-4 mr-1" />
                          Upload Video
                        </>
                      ) : platform.status === 'premium' ? (
                        <>
                          <Settings className="h-4 w-4 mr-1" />
                          Upgrade Required
                        </>
                      ) : (
                        <>
                          <ExternalLink className="h-4 w-4 mr-1" />
                          Connect
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Video Content Library</CardTitle>
              <CardDescription>Manage all your video content across platforms</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockVideoContent.map((video) => (
                  <div key={video.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-20 h-14 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center text-white">
                        <Play className="h-6 w-6" />
                      </div>
                      <div>
                        <h3 className="font-semibold">{video.title}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-sm text-muted-foreground">{video.platform}</span>
                          <Badge className={getStatusColor(video.status)}>
                            {video.status}
                          </Badge>
                          <span className="text-sm text-muted-foreground">•</span>
                          <span className="text-sm text-muted-foreground">{video.duration}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <p className="text-sm font-medium">{video.views.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">Views</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium">{video.likes.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">Likes</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium">{video.publishDate}</p>
                        <p className="text-xs text-muted-foreground">Published</p>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Settings className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button size="sm" variant="outline">
                          <BarChart3 className="h-4 w-4 mr-1" />
                          Analytics
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Views by Platform</CardTitle>
                <CardDescription>Video performance across platforms</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { platform: 'YouTube', views: 22650, percentage: 94 },
                    { platform: 'Twitch', views: 1520, percentage: 6 }
                  ].map((item) => (
                    <div key={item.platform} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{item.platform}</span>
                        <span className="font-medium">{item.views.toLocaleString()} views</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full" 
                          style={{ width: `${item.percentage}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Engagement Metrics</CardTitle>
                <CardDescription>Average engagement across all videos</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Like Rate</span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium mr-2">6.3%</span>
                      <Badge variant="secondary" className="text-green-600">+0.8%</Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Watch Time</span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium mr-2">5:42 avg</span>
                      <Badge variant="secondary" className="text-green-600">+1:12</Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Completion Rate</span>
                    <div className="flex items-center">
                      <span className="text-sm font-medium mr-2">67%</span>
                      <Badge variant="secondary" className="text-green-600">+5%</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Upload Video Modal */}
      {selectedPlatform && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-lg">
            <CardHeader>
              <CardTitle>Upload to {selectedPlatform.name}</CardTitle>
              <CardDescription>Configure your video upload settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Video Title</Label>
                <Input
                  id="title"
                  placeholder="Enter video title"
                  value={uploadData.title}
                  onChange={(e) => setUploadData(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Enter video description"
                  value={uploadData.description}
                  onChange={(e) => setUploadData(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input
                  id="tags"
                  placeholder="Enter tags separated by commas"
                  value={uploadData.tags}
                  onChange={(e) => setUploadData(prev => ({ ...prev, tags: e.target.value }))}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="privacy">Privacy</Label>
                  <Select value={uploadData.privacy} onValueChange={(value) => setUploadData(prev => ({ ...prev, privacy: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">Public</SelectItem>
                      <SelectItem value="unlisted">Unlisted</SelectItem>
                      <SelectItem value="private">Private</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="scheduleDate">Schedule (Optional)</Label>
                  <Input
                    id="scheduleDate"
                    type="datetime-local"
                    value={uploadData.scheduleDate}
                    onChange={(e) => setUploadData(prev => ({ ...prev, scheduleDate: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Platform Settings</Label>
                <div className="bg-muted p-3 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Enable monetization</span>
                    <Switch disabled={!selectedPlatform.monetization} />
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setSelectedPlatform(null)}
                >
                  Cancel
                </Button>
                <Button 
                  className="flex-1"
                  onClick={handleUploadVideo}
                  disabled={!uploadData.title || uploadVideoMutation.isPending}
                >
                  {uploadVideoMutation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Upload
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}