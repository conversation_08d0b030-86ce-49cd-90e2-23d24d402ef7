import React from 'react';
import { Link } from 'wouter';
import { User } from '@/types';
import { 
  AlertCircle, 
  CheckCircle2, 
  CreditCard, 
  ExternalLink, 
  ShieldCheck, 
  TrendingUp 
} from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface SubscriptionStatusProps {
  user: User | undefined;
}

export function SubscriptionStatus({ user }: SubscriptionStatusProps) {
  // Determine if user has an active subscription
  const hasSubscription = Boolean(user?.stripeSubscriptionId);
  const planName = user?.plan || 'Free';
  
  if (!user) return null;
  
  return (
    <Card className="border-2 overflow-hidden">
      <div className={`h-1 w-full ${hasSubscription ? 'bg-green-500' : 'bg-amber-500'}`} />
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">Subscription Status</CardTitle>
            <CardDescription>Your current plan and features</CardDescription>
          </div>
          <Badge 
            variant={hasSubscription ? "default" : "outline"}
            className={hasSubscription ? "bg-green-500" : "border-amber-500 text-amber-500"}
          >
            {hasSubscription ? (
              <><CheckCircle2 className="mr-1 h-3 w-3" /> Active</>
            ) : (
              <><AlertCircle className="mr-1 h-3 w-3" /> Not Subscribed</>
            )}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center mb-3">
          <ShieldCheck className="h-5 w-5 mr-2 text-primary" />
          <span className="font-medium">{planName} Plan</span>
        </div>
        
        {hasSubscription ? (
          <div className="space-y-2 text-sm">
            <div className="flex items-center text-gray-600">
              <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
              <span>Full access to AI course generation</span>
            </div>
            <div className="flex items-center text-gray-600">
              <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
              <span>High quality video rendering</span>
            </div>
            <div className="flex items-center text-gray-600">
              <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
              <span>Priority customer support</span>
            </div>
          </div>
        ) : (
          <div className="space-y-2 text-sm">
            <div className="flex items-center text-gray-600">
              <AlertCircle className="h-4 w-4 mr-2 text-amber-500" />
              <span>Limited access to platform features</span>
            </div>
            <div className="flex items-center text-gray-600">
              <TrendingUp className="h-4 w-4 mr-2 text-primary" />
              <span>Upgrade to unlock premium features</span>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-0">
        {hasSubscription ? (
          <Button variant="outline" size="sm" className="w-full" asChild>
            <Link href="/profile">
              Manage Subscription
              <CreditCard className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        ) : (
          <Button className="w-full" asChild>
            <Link href="/pricing">
              Upgrade Now
              <ExternalLink className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}