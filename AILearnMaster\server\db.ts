import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

// Configure Neon database to work in serverless environment
neonConfig.webSocketConstructor = ws;

// Create a wrapper for the database connection that's more resilient to errors
let pool: Pool | null = null;
let db: ReturnType<typeof drizzle> | null = null;

try {
  // Check if database URL exists
  if (!process.env.DATABASE_URL) {
    console.error("DATABASE_URL is not set. Using fallback mode for database operations.");
  } else {
    // Create database connection pool
    pool = new Pool({ 
      connectionString: process.env.DATABASE_URL,
      // Add reasonable connection limits to prevent resource exhaustion
      max: 10,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 5000
    });
    
    // Initialize Drizzle ORM with the pool and schema
    db = drizzle({ client: pool, schema });
    
    console.log("Database connection initialized successfully");
  }
} catch (error) {
  console.error("Failed to initialize database connection:", error);
  // Keep the application running even if database connection fails
  pool = null;
  db = null;
}

// Export the pool and db, which might be null if initialization failed
export { pool, db };
