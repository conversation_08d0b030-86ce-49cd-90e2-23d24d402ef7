import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { LoadingScreen } from '@/components/ui/loading-screen';

interface LoadingContextType {
  // Show a loading screen with customizable options
  showLoading: (options?: LoadingOptions) => void;
  
  // Hide the loading screen
  hideLoading: () => void;
  
  // Check if loading is currently shown
  isLoading: boolean;
}

export interface LoadingOptions {
  text?: string;
  subText?: string;
  fullScreen?: boolean;
  duration?: number;
  mascotExpression?: 'happy' | 'thinking' | 'surprised' | 'excited' | 'working';
  withProgressBar?: boolean;
  withFunFacts?: boolean;
  withTips?: boolean;
}

const defaultLoadingOptions: LoadingOptions = {
  text: 'Loading...',
  subText: 'Please wait while we prepare your content.',
  fullScreen: true,
  mascotExpression: 'working',
  withProgressBar: true,
  withFunFacts: true,
  withTips: true,
};

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

export function LoadingProvider({ children }: { children: ReactNode }) {
  const [isVisible, setIsVisible] = useState(false);
  const [options, setOptions] = useState<LoadingOptions>(defaultLoadingOptions);
  
  // Function to show loading screen with custom options
  const showLoading = (customOptions?: LoadingOptions) => {
    // Merge default options with custom options
    setOptions({ ...defaultLoadingOptions, ...customOptions });
    setIsVisible(true);
  };
  
  // Function to hide loading screen
  const hideLoading = () => {
    setIsVisible(false);
  };
  
  // If duration is set, automatically hide after that duration
  useEffect(() => {
    if (isVisible && options.duration) {
      const timer = setTimeout(() => {
        hideLoading();
      }, options.duration);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, options.duration]);
  
  return (
    <LoadingContext.Provider 
      value={{ 
        showLoading, 
        hideLoading, 
        isLoading: isVisible
      }}
    >
      {children}
      
      {isVisible && (
        <LoadingScreen
          text={options.text}
          subText={options.subText}
          fullScreen={options.fullScreen}
          duration={options.duration}
          mascotExpression={options.mascotExpression}
          withProgressBar={options.withProgressBar}
          withFunFacts={options.withFunFacts}
          withTips={options.withTips}
          onComplete={hideLoading}
        />
      )}
    </LoadingContext.Provider>
  );
}