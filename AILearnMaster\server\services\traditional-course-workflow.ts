/**
 * Complete Traditional Course Creation Workflow
 * Orchestrates the entire pipeline: Title → Content → Voice → Media → Slides → Video → Subtitles → Final Course
 */

import { dbManager, safeDbOperation } from '../db-enhanced';
import { courses, modules, lessons, mediaLibrary } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';
import axios from 'axios';

// Import AI services
import * as mistralService from './mistralPrimaryService';
import * as coquiTTS from './coqui-tts';
import { marpSlideService } from './marpSlideService';
import * as pexelsService from './pexels-service';
import * as pixabayService from './pixabay-service';
import { awsS3Service } from './awsS3Service';
import { workflowOptimizer } from './workflow-performance-optimizer';

// Types
export interface TraditionalCourseRequest {
  title: string;
  userId: number;
  category?: string;
  targetAudience?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  duration?: 'short' | 'medium' | 'long';
  voiceSettings?: {
    provider: 'coqui' | 'kokoro';
    voiceId: string;
    speed: number;
    pitch: number;
    volume: number;
  };
  mediaSettings?: {
    imageProvider: 'pexels' | 'pixabay';
    videoProvider: 'pexels' | 'pixabay';
    quality: 'standard' | 'high' | 'ultra';
    slideStyle: 'professional' | 'educational' | 'creative' | 'minimal';
  };
}

export interface WorkflowProgress {
  jobId: string;
  status: 'initializing' | 'generating_content' | 'selecting_voice' | 'generating_audio' | 
          'gathering_media' | 'creating_slides' | 'assembling_video' | 'generating_subtitles' | 
          'finalizing' | 'completed' | 'error';
  progress: number; // 0-100
  currentStep: string;
  estimatedTimeRemaining?: number;
  courseId?: number;
  error?: string;
  startTime: Date;
  lastUpdate: Date;
  results?: {
    courseStructure?: any;
    audioFiles?: string[];
    mediaAssets?: string[];
    slideFiles?: string[];
    videoFiles?: string[];
    subtitleFiles?: string[];
    finalCourseUrl?: string;
  };
}

export interface CourseStructure {
  title: string;
  description: string;
  targetAudience: string;
  learningObjectives: string[];
  modules: ModuleStructure[];
  estimatedDuration: number;
}

export interface ModuleStructure {
  title: string;
  description: string;
  learningObjectives: string[];
  lessons: LessonStructure[];
  order: number;
}

export interface LessonStructure {
  title: string;
  description: string;
  script: string;
  searchTerms: string[];
  duration: number;
  order: number;
  keyPoints: string[];
}

export class TraditionalCourseWorkflow {
  private progressMap = new Map<string, WorkflowProgress>();
  private readonly MODAL_A100_URL = process.env.MODAL_A100_URL || 'https://hajhasni1984--courseai-a100-production.modal.run';
  private readonly MAX_RETRIES = 3;
  private readonly LESSON_TARGET_DURATION = 300; // 5 minutes
  private readonly SCENE_CHANGE_INTERVAL = 45; // seconds

  /**
   * Start the complete traditional course creation workflow
   */
  async startWorkflow(request: TraditionalCourseRequest): Promise<{ jobId: string; estimatedTime: number }> {
    const jobId = uuidv4();
    const estimatedTime = this.calculateEstimatedTime(request);

    // Initialize progress tracking
    const progress: WorkflowProgress = {
      jobId,
      status: 'initializing',
      progress: 0,
      currentStep: 'Initializing traditional course workflow...',
      estimatedTimeRemaining: estimatedTime,
      startTime: new Date(),
      lastUpdate: new Date(),
      results: {}
    };

    this.progressMap.set(jobId, progress);

    // Start the workflow asynchronously
    this.executeWorkflow(jobId, request).catch(error => {
      console.error(`Workflow ${jobId} failed:`, error);
      this.updateProgress(jobId, {
        status: 'error',
        error: error.message,
        progress: 0
      });
    });

    return { jobId, estimatedTime };
  }

  /**
   * Get workflow progress
   */
  getProgress(jobId: string): WorkflowProgress | null {
    return this.progressMap.get(jobId) || null;
  }

  /**
   * Execute the complete workflow
   */
  private async executeWorkflow(jobId: string, request: TraditionalCourseRequest): Promise<void> {
    try {
      // Step 1: Generate course content using Mistral LLM
      await this.step1_GenerateContent(jobId, request);

      // Step 2: User voice selection (handled by frontend)
      await this.step2_VoiceSelection(jobId, request);

      // Step 3: Generate audio using Coqui TTS
      await this.step3_GenerateAudio(jobId, request);

      // Step 4: Gather media assets
      await this.step4_GatherMedia(jobId, request);

      // Step 5: Generate slides using Marp
      await this.step5_GenerateSlides(jobId, request);

      // Step 6: Assemble videos using FFmpeg
      await this.step6_AssembleVideos(jobId, request);

      // Step 7: Generate subtitles using Whisper
      await this.step7_GenerateSubtitles(jobId, request);

      // Step 8: Finalize course
      await this.step8_FinalizeCourse(jobId, request);

      this.updateProgress(jobId, {
        status: 'completed',
        progress: 100,
        currentStep: 'Course creation completed successfully!'
      });

    } catch (error: any) {
      console.error(`Workflow step failed for ${jobId}:`, error);

      // Enhanced error handling for Modal A100 issues
      let errorMessage = error.message;
      if (error.message?.includes('404') || error.message?.includes('modal-http')) {
        errorMessage = `❌ Modal A100 GPU service is not available. Please check deployment status.\n\nTo fix this issue:\n1. Run: modal token new\n2. Deploy: modal deploy modal_a100_comprehensive_fixed.py\n3. Update MODAL_A100_URL environment variable\n\nOriginal error: ${error.message}`;
      } else if (error.message?.includes('timeout') || error.message?.includes('ECONNREFUSED')) {
        errorMessage = `❌ Modal A100 GPU service is not responding. Please check service status.\n\nTroubleshooting:\n1. Check Modal dashboard for app status\n2. Verify GPU allocation\n3. Try redeploying the Modal app\n\nOriginal error: ${error.message}`;
      } else if (error.message?.includes('ENOTFOUND') || error.message?.includes('DNS')) {
        errorMessage = `❌ Cannot reach Modal A100 service. Please check the URL.\n\nCurrent URL: ${this.MODAL_A100_URL}\n\nPlease verify the correct Modal URL and update MODAL_A100_URL environment variable.\n\nOriginal error: ${error.message}`;
      }

      this.updateProgress(jobId, {
        status: 'error',
        error: errorMessage,
        progress: 0
      });
      throw error;
    }
  }

  /**
   * Update workflow progress
   */
  private updateProgress(jobId: string, updates: Partial<WorkflowProgress>): void {
    const current = this.progressMap.get(jobId);
    if (current) {
      const updated = {
        ...current,
        ...updates,
        lastUpdate: new Date()
      };
      this.progressMap.set(jobId, updated);
    }
  }

  /**
   * Calculate estimated completion time
   */
  private calculateEstimatedTime(request: TraditionalCourseRequest): number {
    const baseDuration = request.duration || 'medium';
    const durationMultipliers = {
      'short': 1,    // ~2 minutes
      'medium': 2,   // ~4 minutes  
      'long': 3      // ~6 minutes
    };
    
    return 120 * durationMultipliers[baseDuration]; // seconds
  }

  /**
   * Step 1: Generate course content using Mistral LLM
   */
  private async step1_GenerateContent(jobId: string, request: TraditionalCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'generating_content',
      progress: 10,
      currentStep: 'Generating course structure with Mistral AI...'
    });

    try {
      // Call Modal A100 Mistral service for content generation with optimization
      const response = await workflowOptimizer.optimizedModalRequest('/mistral', {
        prompt: this.buildContentGenerationPrompt(request),
        max_tokens: 4000,
        temperature: 0.7
      });

      const courseStructure = this.parseCourseStructure(response.text);
      
      // Save to database
      const courseId = await this.saveCourseToDatabase(request, courseStructure);
      
      this.updateProgress(jobId, {
        progress: 20,
        currentStep: 'Course structure generated successfully',
        courseId,
        results: {
          ...this.progressMap.get(jobId)?.results,
          courseStructure
        }
      });

    } catch (error) {
      throw new Error(`Content generation failed: ${error.message}`);
    }
  }

  /**
   * Step 2: Voice selection (handled by frontend, just validate here)
   */
  private async step2_VoiceSelection(jobId: string, request: TraditionalCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'selecting_voice',
      progress: 25,
      currentStep: 'Voice settings configured'
    });

    // Validate voice settings
    if (!request.voiceSettings?.voiceId) {
      throw new Error('Voice settings are required');
    }
  }

  /**
   * Step 3: Generate audio using Coqui TTS
   */
  private async step3_GenerateAudio(jobId: string, request: TraditionalCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'generating_audio',
      progress: 30,
      currentStep: 'Generating audio with Coqui TTS...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const courseStructure = progress?.results?.courseStructure;
      
      if (!courseStructure) {
        throw new Error('Course structure not found');
      }

      const audioFiles: string[] = [];
      let currentProgress = 30;
      const progressIncrement = 20 / courseStructure.modules.length;

      // Prepare all TTS operations for parallel processing
      const ttsOperations: Array<{ name: string; operation: () => Promise<string> }> = [];

      for (const module of courseStructure.modules) {
        for (const lesson of module.lessons) {
          ttsOperations.push({
            name: `TTS-${lesson.title}`,
            operation: async () => {
              // Generate TTS audio via Modal A100 with optimization
              const audioResponse = await workflowOptimizer.optimizedModalRequest('/tts', {
                text: lesson.script,
                voice_preset: request.voiceSettings?.voiceId,
                speed: request.voiceSettings?.speed || 1.0
              });

              // Save audio file
              return await this.saveAudioFile(audioResponse, lesson.title, jobId);
            }
          });
        }
      }

      // Execute TTS operations in parallel with concurrency control
      const results = await workflowOptimizer.executeParallel(ttsOperations, 3);
      audioFiles.push(...results);

      // Update progress for each module
      for (let i = 0; i < courseStructure.modules.length; i++) {
        currentProgress += progressIncrement;
        this.updateProgress(jobId, {
          progress: Math.round(currentProgress),
          currentStep: `Generated audio for module: ${courseStructure.modules[i].title}`
        });
      }

      this.updateProgress(jobId, {
        progress: 50,
        currentStep: 'Audio generation completed',
        results: {
          ...progress?.results,
          audioFiles
        }
      });

    } catch (error) {
      throw new Error(`Audio generation failed: ${error.message}`);
    }
  }

  /**
   * Step 4: Gather media assets from Pexels/Pixabay
   */
  private async step4_GatherMedia(jobId: string, request: TraditionalCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'gathering_media',
      progress: 55,
      currentStep: 'Gathering media assets...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const courseStructure = progress?.results?.courseStructure;

      if (!courseStructure) {
        throw new Error('Course structure not found');
      }

      const mediaAssets: string[] = [];
      let currentProgress = 55;
      const progressIncrement = 15 / courseStructure.modules.length;

      for (const module of courseStructure.modules) {
        for (const lesson of module.lessons) {
          // Search for media assets for each lesson
          for (const searchTerm of lesson.searchTerms) {
            try {
              let mediaUrl: string;

              if (request.mediaSettings?.imageProvider === 'pixabay') {
                const pixabayResult = await pixabayService.searchImages(searchTerm, 1);
                mediaUrl = pixabayResult.hits[0]?.webformatURL;
              } else {
                const pexelsResult = await pexelsService.searchPhotos(searchTerm, 1);
                mediaUrl = pexelsResult.photos[0]?.src?.large;
              }

              if (mediaUrl) {
                const savedPath = await this.downloadAndSaveMedia(mediaUrl, searchTerm, jobId);
                mediaAssets.push(savedPath);
              }
            } catch (mediaError) {
              console.warn(`Failed to get media for ${searchTerm}:`, mediaError);
            }
          }
        }

        currentProgress += progressIncrement;
        this.updateProgress(jobId, {
          progress: Math.round(currentProgress),
          currentStep: `Gathered media for module: ${module.title}`
        });
      }

      this.updateProgress(jobId, {
        progress: 70,
        currentStep: 'Media gathering completed',
        results: {
          ...progress?.results,
          mediaAssets
        }
      });

    } catch (error) {
      throw new Error(`Media gathering failed: ${error.message}`);
    }
  }

  /**
   * Step 5: Generate slides using Marp
   */
  private async step5_GenerateSlides(jobId: string, request: TraditionalCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'creating_slides',
      progress: 75,
      currentStep: 'Creating presentation slides with Marp...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const courseStructure = progress?.results?.courseStructure;

      if (!courseStructure) {
        throw new Error('Course structure not found');
      }

      const slideFiles: string[] = [];
      let currentProgress = 75;
      const progressIncrement = 10 / courseStructure.modules.length;

      // Prepare slide generation operations for parallel processing
      const slideOperations: Array<{ name: string; operation: () => Promise<string> }> = [];

      for (const module of courseStructure.modules) {
        for (const lesson of module.lessons) {
          slideOperations.push({
            name: `Slides-${lesson.title}`,
            operation: async () => {
              // Generate Marp slides via Modal A100 with optimization
              const slideMarkdown = this.generateSlideMarkdown(lesson, request.mediaSettings?.slideStyle || 'professional');

              const slideResponse = await workflowOptimizer.optimizedModalRequest('/slides', {
                markdown_content: slideMarkdown,
                theme: request.mediaSettings?.slideStyle || 'professional',
                output_format: 'pdf'
              });

              // Save slide file
              return await this.saveSlideFile(slideResponse, lesson.title, jobId);
            }
          });
        }
      }

      // Execute slide operations in parallel
      const slideResults = await workflowOptimizer.executeParallel(slideOperations, 4);
      slideFiles.push(...slideResults);

      // Update progress for each module
      for (let i = 0; i < courseStructure.modules.length; i++) {
        currentProgress += progressIncrement;
        this.updateProgress(jobId, {
          progress: Math.round(currentProgress),
          currentStep: `Generated slides for module: ${courseStructure.modules[i].title}`
        });
      }

      this.updateProgress(jobId, {
        progress: 85,
        currentStep: 'Slide generation completed',
        results: {
          ...progress?.results,
          slideFiles
        }
      });

    } catch (error) {
      throw new Error(`Slide generation failed: ${error.message}`);
    }
  }

  /**
   * Step 6: Assemble videos using FFmpeg
   */
  private async step6_AssembleVideos(jobId: string, request: TraditionalCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'assembling_video',
      progress: 90,
      currentStep: 'Assembling final videos with FFmpeg...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const { courseStructure, audioFiles, mediaAssets, slideFiles } = progress?.results || {};

      if (!courseStructure || !audioFiles || !mediaAssets || !slideFiles) {
        throw new Error('Required assets not found for video assembly');
      }

      const videoFiles: string[] = [];

      // Assemble videos for each lesson
      let lessonIndex = 0;
      for (const module of courseStructure.modules) {
        for (const lesson of module.lessons) {
          const videoPath = await this.assembleVideo({
            audioFile: audioFiles[lessonIndex],
            mediaAssets: mediaAssets.slice(lessonIndex * 3, (lessonIndex + 1) * 3), // 3 media per lesson
            slideFile: slideFiles[lessonIndex],
            lessonTitle: lesson.title,
            jobId
          });

          videoFiles.push(videoPath);
          lessonIndex++;
        }
      }

      this.updateProgress(jobId, {
        progress: 95,
        currentStep: 'Video assembly completed',
        results: {
          ...progress?.results,
          videoFiles
        }
      });

    } catch (error) {
      throw new Error(`Video assembly failed: ${error.message}`);
    }
  }

  /**
   * Step 7: Generate subtitles using Whisper
   */
  private async step7_GenerateSubtitles(jobId: string, request: TraditionalCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'generating_subtitles',
      progress: 97,
      currentStep: 'Generating subtitles with Whisper...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const { audioFiles } = progress?.results || {};

      if (!audioFiles) {
        throw new Error('Audio files not found for subtitle generation');
      }

      const subtitleFiles: string[] = [];

      // Prepare subtitle generation operations for batch processing
      const subtitleOperations = audioFiles.map(audioFile => ({
        name: `Subtitles-${path.basename(audioFile)}`,
        operation: async () => {
          // Generate subtitles via Modal A100 Whisper service with optimization
          const subtitleResponse = await workflowOptimizer.optimizedModalRequest('/whisper', {
            audio_base64: await this.fileToBase64(audioFile),
            output_format: 'srt'
          });

          return await this.saveSubtitleFile(subtitleResponse, audioFile, jobId);
        }
      }));

      // Execute subtitle operations in parallel
      const subtitleResults = await workflowOptimizer.executeParallel(subtitleOperations, 3);
      subtitleFiles.push(...subtitleResults);

      this.updateProgress(jobId, {
        progress: 98,
        currentStep: 'Subtitle generation completed',
        results: {
          ...progress?.results,
          subtitleFiles
        }
      });

    } catch (error) {
      throw new Error(`Subtitle generation failed: ${error.message}`);
    }
  }

  /**
   * Step 8: Finalize course
   */
  private async step8_FinalizeCourse(jobId: string, request: TraditionalCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'finalizing',
      progress: 99,
      currentStep: 'Finalizing course and uploading to S3...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const { videoFiles, subtitleFiles } = progress?.results || {};

      if (!videoFiles || !subtitleFiles) {
        throw new Error('Video or subtitle files not found for finalization');
      }

      // Upload all files to S3
      const uploadedUrls: string[] = [];

      for (let i = 0; i < videoFiles.length; i++) {
        const videoUrl = await awsS3Service.uploadFile(videoFiles[i], `courses/${jobId}/videos/`);
        const subtitleUrl = await awsS3Service.uploadFile(subtitleFiles[i], `courses/${jobId}/subtitles/`);

        uploadedUrls.push(videoUrl);
        uploadedUrls.push(subtitleUrl);
      }

      // Update database with final course data
      await this.updateCourseInDatabase(progress.courseId!, uploadedUrls);

      this.updateProgress(jobId, {
        progress: 100,
        currentStep: 'Course finalization completed',
        results: {
          ...progress?.results,
          finalCourseUrl: uploadedUrls[0] // First video URL
        }
      });

    } catch (error) {
      throw new Error(`Course finalization failed: ${error.message}`);
    }
  }

  // Helper Methods

  /**
   * Build content generation prompt for Mistral
   */
  private buildContentGenerationPrompt(request: TraditionalCourseRequest): string {
    return `Create a comprehensive course structure for: "${request.title}"

Target Audience: ${request.targetAudience || 'General learners'}
Category: ${request.category || 'General'}
Difficulty: ${request.difficulty || 'intermediate'}
Duration: ${request.duration || 'medium'}

Please provide a detailed course structure with:
1. Course description and learning objectives
2. 3-5 modules with clear titles and descriptions
3. 3-5 lessons per module with detailed scripts (300-500 words each)
4. Search terms for each lesson (for finding relevant media)
5. Key learning points for each lesson

Format the response as JSON with the following structure:
{
  "title": "Course Title",
  "description": "Course description",
  "targetAudience": "Target audience",
  "learningObjectives": ["objective1", "objective2"],
  "estimatedDuration": 120,
  "modules": [
    {
      "title": "Module Title",
      "description": "Module description",
      "order": 1,
      "learningObjectives": ["objective1"],
      "lessons": [
        {
          "title": "Lesson Title",
          "description": "Lesson description",
          "script": "Detailed lesson script...",
          "searchTerms": ["term1", "term2"],
          "duration": 300,
          "order": 1,
          "keyPoints": ["point1", "point2"]
        }
      ]
    }
  ]
}`;
  }

  /**
   * Parse course structure from Mistral response
   */
  private parseCourseStructure(response: string): CourseStructure {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      return parsed as CourseStructure;
    } catch (error) {
      throw new Error(`Failed to parse course structure: ${error.message}`);
    }
  }

  /**
   * Save course to database
   */
  private async saveCourseToDatabase(request: TraditionalCourseRequest, structure: CourseStructure): Promise<number> {
    return await safeDbOperation(async (db) => {
      // Create course
      const [course] = await db.insert(courses).values({
        title: structure.title,
        description: structure.description,
        userId: request.userId,
        category: request.category || 'general',
        difficulty: request.difficulty || 'intermediate',
        estimatedDuration: structure.estimatedDuration,
        status: 'generating',
        format: 'traditional'
      }).returning();

      // Create modules and lessons
      for (const moduleData of structure.modules) {
        const [module] = await db.insert(modules).values({
          courseId: course.id,
          title: moduleData.title,
          description: moduleData.description,
          order: moduleData.order
        }).returning();

        for (const lessonData of moduleData.lessons) {
          await db.insert(lessons).values({
            moduleId: module.id,
            title: lessonData.title,
            content: lessonData.description,
            script: lessonData.script,
            duration: lessonData.duration,
            order: lessonData.order
          });
        }
      }

      return course.id;
    });
  }

  /**
   * Save audio file
   */
  private async saveAudioFile(audioData: any, lessonTitle: string, jobId: string): Promise<string> {
    const fileName = `${lessonTitle.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.mp3`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'audio', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });

    if (typeof audioData === 'string') {
      // Base64 encoded audio
      const buffer = Buffer.from(audioData, 'base64');
      await fs.writeFile(filePath, buffer);
    } else {
      // Binary audio data
      await fs.writeFile(filePath, audioData);
    }

    return filePath;
  }

  /**
   * Download and save media
   */
  private async downloadAndSaveMedia(url: string, searchTerm: string, jobId: string): Promise<string> {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    const fileName = `${searchTerm.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.jpg`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'media', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, response.data);

    return filePath;
  }

  /**
   * Generate slide markdown for Marp
   */
  private generateSlideMarkdown(lesson: LessonStructure, style: string): string {
    const themeClass = style === 'professional' ? 'class: lead' : '';

    return `---
marp: true
theme: ${style}
${themeClass}
---

# ${lesson.title}

${lesson.description}

---

## Key Learning Points

${lesson.keyPoints.map(point => `- ${point}`).join('\n')}

---

## Lesson Content

${lesson.script.split('\n\n').map(paragraph => `${paragraph}\n\n---\n\n`).join('')}

---

## Summary

- Covered: ${lesson.title}
- Duration: ${Math.round(lesson.duration / 60)} minutes
- Next: Continue to next lesson

`;
  }

  /**
   * Save slide file
   */
  private async saveSlideFile(slideData: any, lessonTitle: string, jobId: string): Promise<string> {
    const fileName = `${lessonTitle.replace(/[^a-zA-Z0-9]/g, '_')}_slides_${Date.now()}.pdf`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'slides', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });

    if (typeof slideData === 'string') {
      const buffer = Buffer.from(slideData, 'base64');
      await fs.writeFile(filePath, buffer);
    } else {
      await fs.writeFile(filePath, slideData);
    }

    return filePath;
  }

  /**
   * Assemble video using FFmpeg
   */
  private async assembleVideo(options: {
    audioFile: string;
    mediaAssets: string[];
    slideFile: string;
    lessonTitle: string;
    jobId: string;
  }): Promise<string> {
    const { spawn } = require('child_process');
    const outputPath = path.join(process.cwd(), 'temp', options.jobId, 'videos',
      `${options.lessonTitle.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.mp4`);

    await fs.mkdir(path.dirname(outputPath), { recursive: true });

    return new Promise((resolve, reject) => {
      // Create FFmpeg command for video assembly
      const ffmpegArgs = [
        '-y', // Overwrite output
        '-i', options.audioFile, // Audio input
        '-loop', '1', '-i', options.mediaAssets[0] || '', // Background image
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-b:a', '192k',
        '-pix_fmt', 'yuv420p',
        '-shortest',
        outputPath
      ];

      const ffmpeg = spawn('ffmpeg', ffmpegArgs);

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          resolve(outputPath);
        } else {
          reject(new Error(`FFmpeg failed with code ${code}`));
        }
      });

      ffmpeg.on('error', (error) => {
        reject(new Error(`FFmpeg error: ${error.message}`));
      });
    });
  }

  /**
   * Save subtitle file
   */
  private async saveSubtitleFile(subtitleData: any, audioFile: string, jobId: string): Promise<string> {
    const baseName = path.basename(audioFile, path.extname(audioFile));
    const fileName = `${baseName}_subtitles.srt`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'subtitles', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, subtitleData);

    return filePath;
  }

  /**
   * Convert file to base64
   */
  private async fileToBase64(filePath: string): Promise<string> {
    const buffer = await fs.readFile(filePath);
    return buffer.toString('base64');
  }

  /**
   * Update course in database with final URLs
   */
  private async updateCourseInDatabase(courseId: number, urls: string[]): Promise<void> {
    await safeDbOperation(async (db) => {
      await db.update(courses)
        .set({
          status: 'published',
          videoUrl: urls[0], // First video URL
          updatedAt: new Date()
        })
        .where(eq(courses.id, courseId));
    });
  }
}

// Export singleton instance
export const traditionalCourseWorkflow = new TraditionalCourseWorkflow();
