/**
 * SadTalker Avatar Generation Router
 * API endpoints for avatar-based course creation with A100 GPU acceleration
 */

import { Router, Request, Response } from 'express';
import { sadTalkerA100Service } from '../services/sadTalkerA100Service.js';

const router = Router();

// Health check for SadTalker A100 service
router.get('/health', async (req: Request, res: Response) => {
  try {
    const health = await sadTalkerA100Service.checkA100Health();
    
    const status = health.gpu_available && health.sadtalker_installed ? 'healthy' : 'degraded';
    
    res.json({
      status,
      service: health.service,
      gpu_available: health.gpu_available,
      sadtalker_ready: health.sadtalker_installed && health.checkpoints_available,
      available_models: health.available_models || 0,
      capabilities: {
        avatar_generation: true,
        batch_processing: health.gpu_available,
        custom_training: health.gpu_available,
        high_resolution: health.gpu_available
      }
    });
  } catch (error) {
    console.error('SadTalker health check error:', error);
    res.status(503).json({
      status: 'unhealthy',
      service: 'SadTalker-Unknown',
      error: 'Health check failed'
    });
  }
});

// Generate single avatar video
router.post('/generate-avatar', async (req: Request, res: Response) => {
  try {
    const {
      sourceImage,
      audioData,
      settings = {}
    } = req.body;

    if (!sourceImage || !audioData) {
      return res.status(400).json({
        message: 'Source image and audio data are required'
      });
    }

    console.log('Generating avatar video...');
    
    const result = await sadTalkerA100Service.generateAvatarVideo(
      sourceImage,
      audioData,
      settings
    );

    if (result.success) {
      res.json({
        success: true,
        videoData: result.videoData,
        format: result.format,
        duration: result.durationSeconds,
        size: result.sizeBytes,
        resolution: result.resolution,
        settings: settings
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.error || 'Avatar generation failed'
      });
    }
  } catch (error) {
    console.error('Avatar generation error:', error);
    res.status(500).json({
      message: 'Avatar generation failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate complete avatar course
router.post('/generate-course', async (req: Request, res: Response) => {
  try {
    const {
      courseData,
      avatarImage,
      settings = {}
    } = req.body;

    if (!courseData || !avatarImage) {
      return res.status(400).json({
        message: 'Course data and avatar image are required'
      });
    }

    if (!courseData.modules || courseData.modules.length === 0) {
      return res.status(400).json({
        message: 'Course must contain at least one module'
      });
    }

    console.log(`Generating avatar course: ${courseData.title}`);
    console.log(`Modules: ${courseData.modules.length}`);
    
    const result = await sadTalkerA100Service.generateAvatarCourse(
      courseData,
      avatarImage,
      settings
    );

    if (result.totalModules > 0) {
      res.json({
        success: true,
        courseTitle: result.courseTitle,
        totalModules: result.totalModules,
        totalLessons: result.modules.reduce((sum, mod) => sum + mod.lessons.length, 0),
        totalDuration: result.totalDuration,
        totalSize: result.totalSize,
        modules: result.modules,
        settings: settings
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'No course modules were generated successfully'
      });
    }
  } catch (error) {
    console.error('Avatar course generation error:', error);
    res.status(500).json({
      message: 'Avatar course generation failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Train custom avatar
router.post('/train-avatar', async (req: Request, res: Response) => {
  try {
    const {
      trainingImages,
      trainingVideos = [],
      avatarName = 'custom_avatar'
    } = req.body;

    if (!trainingImages || !Array.isArray(trainingImages) || trainingImages.length < 5) {
      return res.status(400).json({
        message: 'At least 5 training images are required'
      });
    }

    console.log(`Training custom avatar: ${avatarName}`);
    console.log(`Training images: ${trainingImages.length}`);
    console.log(`Training videos: ${trainingVideos.length}`);
    
    const result = await sadTalkerA100Service.trainCustomAvatar(
      trainingImages,
      trainingVideos,
      avatarName
    );

    if (result.success) {
      res.json({
        success: true,
        avatarName: result.avatarName,
        trainingImages: result.trainingImages,
        trainingVideos: result.trainingVideos,
        status: result.status,
        recommendations: [
          'Use high-resolution frontal face images',
          'Ensure consistent lighting across training images',
          'Include various expressions for better results',
          'Videos should show natural head movements'
        ]
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.error || 'Avatar training failed'
      });
    }
  } catch (error) {
    console.error('Avatar training error:', error);
    res.status(500).json({
      message: 'Avatar training failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get avatar generation settings
router.get('/settings', async (req: Request, res: Response) => {
  try {
    const health = await sadTalkerA100Service.checkA100Health();
    
    res.json({
      availableSettings: {
        pose_style: {
          type: 'number',
          min: 0,
          max: 45,
          default: 0,
          description: 'Head pose variation style'
        },
        expression_scale: {
          type: 'number',
          min: 0.1,
          max: 3.0,
          default: 1.0,
          description: 'Expression intensity multiplier'
        },
        still_mode: {
          type: 'boolean',
          default: false,
          description: 'Minimize head movement for still avatar'
        },
        preprocess: {
          type: 'select',
          options: ['crop', 'resize', 'full'],
          default: 'crop',
          description: 'Image preprocessing method'
        },
        size: {
          type: 'select',
          options: [256, 512],
          default: 256,
          description: 'Output video resolution'
        },
        enhancer: {
          type: 'select',
          options: ['gfpgan', 'RestoreFormer', 'codeformer'],
          default: 'gfpgan',
          description: 'Face enhancement model'
        }
      },
      qualityTiers: {
        standard: {
          size: 256,
          enhancer: 'gfpgan',
          description: 'Good quality, fast generation'
        },
        high: {
          size: 512,
          enhancer: 'RestoreFormer',
          description: 'High quality, longer generation time'
        },
        premium: {
          size: 512,
          enhancer: 'codeformer',
          expression_scale: 1.2,
          description: 'Best quality with A100 GPU'
        }
      },
      capabilities: {
        gpu_acceleration: health.gpu_available,
        batch_processing: health.gpu_available,
        custom_training: health.gpu_available,
        max_resolution: health.gpu_available ? 512 : 256,
        supported_formats: ['mp4', 'avi', 'mov']
      }
    });
  } catch (error) {
    console.error('Settings retrieval error:', error);
    res.status(500).json({
      message: 'Failed to retrieve settings'
    });
  }
});

// Get usage statistics
router.get('/stats', async (req: Request, res: Response) => {
  try {
    // Return basic statistics - in production, this would come from database
    res.json({
      total_videos_generated: 0,
      total_courses_created: 0,
      total_processing_time: 0,
      average_generation_time: 0,
      gpu_utilization: 0,
      success_rate: 100,
      popular_settings: {
        pose_style: 0,
        expression_scale: 1.0,
        size: 256,
        enhancer: 'gfpgan'
      }
    });
  } catch (error) {
    console.error('Stats retrieval error:', error);
    res.status(500).json({
      message: 'Failed to retrieve statistics'
    });
  }
});

export default router;