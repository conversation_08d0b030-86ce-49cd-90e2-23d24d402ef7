import express, { Request, Response } from 'express';
import { OAuth2Client } from 'google-auth-library';
import { storage } from '../storage';

const router = express.Router();

// Initialize Google OAuth client
const oauth2Client = new OAuth2Client({
  clientId: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  redirectUri: process.env.GOOGLE_REDIRECT_URI || `https://${process.env.REPL_SLUG}.${process.env.REPL_OWNER}.repl.co/api/auth/google/callback`
});

// Google OAuth login URL generation
router.get('/google', (req: Request, res: Response) => {
  try {
    const scopes = [
      'https://www.googleapis.com/auth/userinfo.profile',
      'https://www.googleapis.com/auth/userinfo.email'
    ];

    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      include_granted_scopes: true,
    });

    res.json({ authUrl });
  } catch (error) {
    console.error('Google OAuth URL generation error:', error);
    res.status(500).json({ message: 'Failed to generate OAuth URL' });
  }
});

// Google OAuth callback handler
router.get('/google/callback', async (req: Request, res: Response) => {
  try {
    const { code } = req.query;

    if (!code) {
      return res.redirect('/auth?error=oauth_cancelled');
    }

    // Exchange code for tokens
    const { tokens } = await oauth2Client.getToken(code as string);
    oauth2Client.setCredentials(tokens);

    // Get user info from Google
    const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${tokens.access_token}`,
      },
    });

    if (!userInfoResponse.ok) {
      throw new Error('Failed to fetch user info from Google');
    }

    const googleUser = await userInfoResponse.json();

    // Check if user exists
    let user = await storage.getUserByEmail(googleUser.email);

    if (!user) {
      // Create new user with Google OAuth
      const newUser = {
        username: googleUser.email.split('@')[0] + '_' + Date.now(), // Generate unique username
        email: googleUser.email,
        name: googleUser.name || 'Google User',
        emailVerified: true,
        avatarUrl: googleUser.picture,
        googleId: googleUser.id,
        authProvider: 'google'
      };

      user = await storage.createUser(newUser);
    } else if (!user.googleId) {
      // Link existing account with Google
      await storage.updateUser(user.id, {
        googleId: googleUser.id,
        authProvider: 'google',
        avatarUrl: user.avatarUrl || googleUser.picture,
        emailVerified: true
      });
    }

    // Set session
    if (req.session) {
      req.session.userId = user.id;
    }

    // Redirect to dashboard on success
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Google OAuth callback error:', error);
    res.redirect('/auth?error=oauth_failed');
  }
});

// Google OAuth verification endpoint for frontend
router.post('/google/verify', async (req: Request, res: Response) => {
  try {
    const { credential } = req.body;

    if (!credential) {
      return res.status(400).json({ message: 'Missing Google credential' });
    }

    // Verify the Google JWT token
    const ticket = await oauth2Client.verifyIdToken({
      idToken: credential,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    if (!payload) {
      return res.status(400).json({ message: 'Invalid Google token' });
    }

    // Check if user exists
    let user = await storage.getUserByEmail(payload.email!);

    if (!user) {
      // Create new user with Google OAuth
      const newUser = {
        username: payload.email!.split('@')[0] + '_' + Date.now(),
        email: payload.email!,
        name: payload.name || 'Google User',
        emailVerified: true,
        avatarUrl: payload.picture,
        googleId: payload.sub,
        authProvider: 'google'
      };

      user = await storage.createUser(newUser);
    } else if (!user.googleId) {
      // Link existing account with Google
      await storage.updateUser(user.id, {
        googleId: payload.sub,
        authProvider: 'google',
        avatarUrl: user.avatarUrl || payload.picture,
        emailVerified: true
      });
    }

    // Set session
    if (req.session) {
      req.session.userId = user.id;
    }

    // Return safe user object
    const safeUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      plan: user.plan,
      emailVerified: user.emailVerified,
      avatarUrl: user.avatarUrl,
      authProvider: user.authProvider
    };

    res.json(safeUser);
  } catch (error) {
    console.error('Google OAuth verification error:', error);
    res.status(500).json({ message: 'OAuth verification failed' });
  }
});

export default router;