#!/usr/bin/env python3
"""
Simple Modal Authentication Test
"""

import os
import sys
from pathlib import Path

def check_modal_config():
    """Check if Modal config file exists"""
    config_path = Path.home() / '.modal.toml'
    if config_path.exists():
        print(f"✓ Modal config found at: {config_path}")
        return True
    else:
        print(f"❌ Modal config not found at: {config_path}")
        return False

def main():
    print("🔧 Modal Authentication Test")
    print("=" * 40)

    # Check environment variables
    token_id = os.getenv('MODAL_TOKEN_ID')
    token_secret = os.getenv('MODAL_TOKEN_SECRET')

    print(f"MODAL_TOKEN_ID: {'Set' if token_id else 'Not set'}")
    print(f"MODAL_TOKEN_SECRET: {'Set' if token_secret else 'Not set'}")

    # Check Modal config file
    config_exists = check_modal_config()

    # Test Modal connection
    print(f"\nTesting Modal connection...")
    try:
        import modal
        print(f"Modal version: {modal.__version__}")

        # Try to create a test app to verify authentication
        test_app = modal.App("auth-test")
        print(f"✓ Authentication successful! Modal client is working.")
        return True

    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        if not config_exists and not (token_id and token_secret):
            print("\nYou need to set up Modal authentication.")
            print("Options:")
            print("1. Use browser authentication: python -m modal token new")
            print("2. Set environment variables manually")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nResult: {'SUCCESS' if success else 'FAILED'}")
