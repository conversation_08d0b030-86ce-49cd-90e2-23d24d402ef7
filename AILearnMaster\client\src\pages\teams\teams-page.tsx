import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, Search, Users, Crown } from "lucide-react";
import { useState } from "react";
import { useLocation } from "wouter";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { z } from "zod";
import EnterpriseModal from "@/components/modals/enterprise-modal";

// Define the Team type based on our database schema
type Team = {
  id: number;
  name: string;
  description: string | null;
  ownerId: number;
  avatarUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
};

export default function TeamsPage() {
  const { toast } = useToast();
  const [_, setLocation] = useLocation();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [newTeamName, setNewTeamName] = useState("");
  const [newTeamDescription, setNewTeamDescription] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);

  // Check user authentication and plan
  const { data: user } = useQuery({
    queryKey: ['/api/auth/me'],
  });

  // Check if user has Enterprise subscription
  const userTyped = user as any;
  const isEnterprise = userTyped?.subscription === 'enterprise';

  // Show Enterprise upgrade notice if not Enterprise user
  if (user && !isEnterprise) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Crown className="w-8 h-8 text-yellow-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Enterprise Feature</h2>
            <p className="text-gray-600 mb-6">
              Teams are available exclusively for Enterprise plan users. Upgrade your subscription to access collaborative team workspaces, shared course creation, and advanced team management features.
            </p>
            <Button 
              onClick={() => {
                console.log('Upgrade button clicked, setting modal open to true');
                setIsUpgradeModalOpen(true);
              }}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              Upgrade to Enterprise
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Fetch teams data
  const { data: teams, isLoading, error } = useQuery<Team[]>({
    queryKey: ["/api/teams"],
    staleTime: 10 * 1000, // 10 seconds
    enabled: isEnterprise
  });

  // Filter teams based on search query
  const filteredTeams = teams?.filter((team) => {
    if (!searchQuery) return true;
    return (
      team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (team.description &&
        team.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  });

  // Handle create team submission
  const handleCreateTeam = async () => {
    // Validate team name is not empty
    if (!newTeamName.trim()) {
      toast({
        title: "Team name is required",
        description: "Please enter a name for your team",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const schema = z.object({
        name: z.string().min(1, "Team name is required"),
        description: z.string().optional(),
      });

      const validatedData = schema.parse({
        name: newTeamName,
        description: newTeamDescription || undefined,
      });

      const response = await apiRequest("POST", "/api/teams", validatedData);
      const newTeam = await response.json();

      // Reset form and close dialog
      setNewTeamName("");
      setNewTeamDescription("");
      setIsCreateDialogOpen(false);

      // Show success message
      toast({
        title: "Team created",
        description: `Team "${newTeam.name}" has been created successfully`,
      });

      // Invalidate teams query to refresh data
      queryClient.invalidateQueries({ queryKey: ["/api/teams"] });
    } catch (error) {
      console.error("Error creating team:", error);
      toast({
        title: "Failed to create team",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle team selection
  const viewTeam = (teamId: number) => {
    setLocation(`/teams/${teamId}`);
  };

  return (
    <div className="container max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">Teams</h1>
          <p className="text-slate-600 mt-1">Create and manage your teams for collaborative course creation</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="md:w-auto w-full">
              <Plus className="mr-2 h-4 w-4" />
              Create New Team
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Create a new team</DialogTitle>
              <DialogDescription>
                Create a team to collaborate on courses with other users.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={newTeamName}
                  onChange={(e) => setNewTeamName(e.target.value)}
                  className="col-span-3"
                  placeholder="Enter team name"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={newTeamDescription}
                  onChange={(e) => setNewTeamDescription(e.target.value)}
                  className="col-span-3"
                  placeholder="Brief description of the team (optional)"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                onClick={handleCreateTeam}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Team"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and filters */}
      <div className="relative flex-grow mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
        <Input
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search teams..."
          className="pl-10"
        />
      </div>

      {/* Teams grid */}
      {isLoading ? (
        <div className="flex justify-center items-center py-10">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : error ? (
        <div className="text-center py-10">
          <p className="text-red-500">
            Error loading teams. Please try again later.
          </p>
        </div>
      ) : filteredTeams?.length === 0 ? (
        <div className="text-center py-10">
          <Users className="h-12 w-12 mx-auto text-slate-300" />
          <h3 className="mt-4 text-lg font-medium text-slate-900">
            No teams found
          </h3>
          <p className="mt-1 text-sm text-slate-500">
            {searchQuery
              ? "No teams match your search criteria."
              : "You haven't created or joined any teams yet."}
          </p>
          <div className="mt-6">
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create New Team
            </Button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTeams?.map((team) => (
            <Card key={team.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center">
                  {team.avatarUrl ? (
                    <img
                      src={team.avatarUrl}
                      alt={team.name}
                      className="h-8 w-8 rounded-full mr-2"
                    />
                  ) : (
                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                      <Users className="h-4 w-4 text-primary" />
                    </div>
                  )}
                  {team.name}
                </CardTitle>
                <CardDescription>
                  {team.description || "No description provided"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-slate-500">
                  Created on {new Date(team.createdAt).toLocaleDateString()}
                </p>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button variant="outline" onClick={() => viewTeam(team.id)}>
                  View Team
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Enterprise Upgrade Modal */}
      <EnterpriseModal
        isOpen={isUpgradeModalOpen}
        onClose={() => setIsUpgradeModalOpen(false)}
        onSuccess={() => {
          toast({
            title: "Welcome to Enterprise!",
            description: "Your account has been upgraded. Refreshing page...",
          });
          setTimeout(() => window.location.reload(), 2000);
        }}
      />
    </div>
  );
}