# AILearnMaster Production Environment Configuration
# Copy this file to .env.production and fill in the actual values
# DO NOT commit this file with real values to version control

# =============================================================================
# CRITICAL SECURITY SETTINGS
# =============================================================================

# Environment
NODE_ENV=production

# Session Security (REQUIRED - Generate a strong 32+ character secret)
SESSION_SECRET=your-super-secure-session-secret-32-chars-minimum

# Secrets Encryption (REQUIRED - Generate a strong 32+ character key)
SECRETS_ENCRYPTION_KEY=your-encryption-key-for-secrets-32-chars-minimum

# JWT Secret (REQUIRED - Generate a strong 32+ character secret)
JWT_SECRET=your-jwt-secret-32-chars-minimum

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database URL with SSL enforcement (REQUIRED)
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# =============================================================================
# CORS AND SECURITY
# =============================================================================

# Allowed Origins (REQUIRED - Comma-separated list of allowed domains)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Security Settings
ENABLE_CSRF=true
ENABLE_RATE_LIMITING=true
ENABLE_SECURITY_HEADERS=true
LOG_LEVEL=warn
DEBUG=false

# =============================================================================
# AI SERVICE CONFIGURATION
# =============================================================================

# OpenAI API Key (Optional - for fallback text generation)
OPENAI_API_KEY=sk-your-openai-api-key

# ElevenLabs API Key (Optional - for voice synthesis fallback)
ELEVENLABS_API_KEY=your-elevenlabs-api-key

# Google API Key (Optional - for additional services)
GOOGLE_API_KEY=your-google-api-key

# Pexels API Key (Optional - for stock media)
PEXELS_API_KEY=your-pexels-api-key

# Pixabay API Key (Optional - for stock media)
PIXABAY_API_KEY=your-pixabay-api-key

# =============================================================================
# MODAL A100 GPU CONFIGURATION
# =============================================================================

# Modal Tokens (REQUIRED for AI processing)
MODAL_TOKEN_ID=your-modal-token-id
MODAL_TOKEN_SECRET=your-modal-token-secret

# Modal Configuration
MODAL_ENVIRONMENT=production
MODAL_WARM_CONTAINERS=1
MODAL_MAX_CONCURRENT_JOBS=5

# =============================================================================
# AWS S3 CONFIGURATION
# =============================================================================

# AWS Credentials (REQUIRED for file storage)
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# CloudFront CDN (Optional but recommended)
AWS_CLOUDFRONT_DOMAIN=your-cloudfront-domain.cloudfront.net

# =============================================================================
# RUNPOD CONFIGURATION (Optional - for additional GPU processing)
# =============================================================================

# RunPod API Key
RUNPOD_API_KEY=your-runpod-api-key

# RunPod Endpoints
RUNPOD_MISTRAL_ENDPOINT=your-mistral-endpoint
RUNPOD_MIXTRAL_ENDPOINT=your-mixtral-endpoint
RUNPOD_KOKORO_ENDPOINT=your-kokoro-endpoint
RUNPOD_COQUI_ENDPOINT=your-coqui-endpoint
RUNPOD_KANDINSKY_ENDPOINT=your-kandinsky-endpoint
RUNPOD_WAN_ENDPOINT=your-wan-endpoint
RUNPOD_ANIMATEDIFF_ENDPOINT=your-animatediff-endpoint

# =============================================================================
# STRIPE PAYMENT CONFIGURATION (Optional)
# =============================================================================

# Stripe Keys
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=pk_live_your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Server Configuration
PORT=3001
HOST=0.0.0.0

# Application URLs
FRONTEND_URL=https://yourdomain.com
BACKEND_URL=https://api.yourdomain.com

# =============================================================================
# AI SERVICE SECURITY
# =============================================================================

# AI Security Settings
AI_PROMPT_SANITIZATION=true
AI_CONTENT_FILTERING=true
AI_RATE_LIMITING=true
AI_MAX_TOKENS=2000
AI_TIMEOUT_MS=30000

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================

# Logging Configuration
LOG_LEVEL=warn
ENABLE_ACCESS_LOGS=true
ENABLE_ERROR_LOGS=true
ENABLE_SECURITY_LOGS=true

# Monitoring
ENABLE_HEALTH_CHECKS=true
HEALTH_CHECK_INTERVAL=30000
ENABLE_METRICS=true

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================

# Backup Configuration
ENABLE_AUTO_BACKUP=true
BACKUP_INTERVAL=daily
BACKUP_RETENTION_DAYS=30

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================

# Rate Limits (requests per time window)
RATE_LIMIT_GENERAL_MAX=300
RATE_LIMIT_GENERAL_WINDOW=900000
RATE_LIMIT_AUTH_MAX=5
RATE_LIMIT_AUTH_WINDOW=900000
RATE_LIMIT_AI_MAX=3
RATE_LIMIT_AI_WINDOW=60000
RATE_LIMIT_UPLOAD_MAX=2
RATE_LIMIT_UPLOAD_WINDOW=60000

# =============================================================================
# FILE UPLOAD SECURITY
# =============================================================================

# Upload Limits
MAX_FILE_SIZE=5242880
MAX_FILES_PER_REQUEST=5
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,audio/mpeg,audio/wav

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================

# Session Settings
SESSION_MAX_AGE=28800000
SESSION_SECURE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# =============================================================================
# SECURITY HEADERS
# =============================================================================

# Content Security Policy
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self'
CSP_STYLE_SRC='self' 'unsafe-inline' https://fonts.googleapis.com
CSP_IMG_SRC='self' data: https: blob:
CSP_CONNECT_SRC='self' https://api.openai.com https://api.mistral.ai

# =============================================================================
# DEPLOYMENT INFORMATION
# =============================================================================

# Deployment Metadata
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_DATE=2024-12-01
DEPLOYMENT_ENVIRONMENT=production
DEPLOYMENT_COMMIT_HASH=your-git-commit-hash

# =============================================================================
# SECURITY VALIDATION
# =============================================================================

# Security Validation Settings
SECURITY_VALIDATION_ENABLED=true
SECURITY_SCORE_THRESHOLD=85
SECURITY_AUDIT_INTERVAL=weekly

# =============================================================================
# NOTES
# =============================================================================

# 1. Replace all placeholder values with actual production values
# 2. Ensure all secrets are strong and randomly generated
# 3. Use a secure secrets management service in production
# 4. Regularly rotate secrets and API keys
# 5. Monitor for unauthorized access attempts
# 6. Keep this file secure and never commit with real values
# 7. Run security validation before deployment: npm run security:production-ready
# 8. Verify SSL/TLS certificates are properly configured
# 9. Enable monitoring and alerting for security events
# 10. Conduct regular security audits and penetration testing
