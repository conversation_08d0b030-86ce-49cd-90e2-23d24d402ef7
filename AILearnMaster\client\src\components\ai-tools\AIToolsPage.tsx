import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import TextToSpeechTool from './TextToSpeechTool';
import AnimationGenerationTool from './AnimationGenerationTool';
import MarpSlideEditor from './MarpSlideEditor';

import AIToolsSettings from '../settings/AIToolsSettings';
import { Button } from '@/components/ui/button';
import { Settings, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

interface AIToolsPageProps {
  courseId?: number;
  lessonId?: number;
}

export function AIToolsPage({ courseId, lessonId }: AIToolsPageProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('text-to-speech');
  const [showSettings, setShowSettings] = useState(false);
  const [showAPIWarning, setShowAPIWarning] = useState(true);

  const handleAudioGenerated = (url: string) => {
    toast({
      title: 'Audio generated',
      description: 'Audio generation successful',
    });
  };

  const handleAnimationGenerated = (url: string) => {
    toast({
      title: 'Animation generated',
      description: 'Animation generation successful',
    });
  };

  const handleSlidesGenerated = (url: string, format: string) => {
    toast({
      title: 'Slides generated',
      description: `Slides generated in ${format.toUpperCase()} format`,
    });
  };



  return (
    <div className="container mx-auto p-4 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">AI Tools</h1>
        <Button 
          variant={showSettings ? "default" : "outline"} 
          onClick={() => setShowSettings(!showSettings)}
        >
          <Settings className="h-4 w-4 mr-2" />
          {showSettings ? "Hide Settings" : "Settings"}
        </Button>
      </div>
      
      {showAPIWarning && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Configuration Required</AlertTitle>
          <AlertDescription>
            To use these AI tools, you need to configure RunPod API key and endpoints, as well as AWS S3 storage. Click the Settings button to get started.
            <Button 
              variant="outline" 
              size="sm" 
              className="ml-2" 
              onClick={() => setShowAPIWarning(false)}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}
      
      {showSettings ? (
        <div className="mb-8">
          <AIToolsSettings />
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-8">
            <TabsTrigger value="text-to-speech">Speech</TabsTrigger>
            <TabsTrigger value="animation-generation">Talking Avatar</TabsTrigger>
            <TabsTrigger value="slides">Slides</TabsTrigger>
          </TabsList>
          
          <TabsContent value="text-to-speech">
            <TextToSpeechTool 
              onAudioGenerated={handleAudioGenerated} 
              defaultModel="kokoro"
            />
          </TabsContent>
          
          <TabsContent value="animation-generation">
            <AnimationGenerationTool 
              onAnimationGenerated={handleAnimationGenerated} 
            />
          </TabsContent>
          
          <TabsContent value="slides">
            <MarpSlideEditor 
              onSlidesGenerated={handleSlidesGenerated} 
              courseId={courseId} 
              lessonId={lessonId} 
            />
          </TabsContent>
          

        </Tabs>
      )}
    </div>
  );
}

export default AIToolsPage;