import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import EnterpriseModal from "@/components/modals/enterprise-modal";
import {
  Share2,
  Users,
  Crown,
  Plus,
  Search,
  Filter,
  Eye,
  Edit3,
  UserPlus,
  Settings,
  BookOpen,
  Calendar,
  Clock,
  TrendingUp,
  Star,
  MoreVertical,
  Copy,
  Send,
  UserCheck,
  UserX,
  Shield,
  ChevronDown
} from "lucide-react";

// Types for shared courses
type SharedCourse = {
  id: number;
  title: string;
  description: string;
  thumbnailUrl?: string;
  createdBy: string;
  createdAt: string;
  status: 'active' | 'draft' | 'archived';
  collaborators: number;
  views: number;
  rating: number;
  category: string;
  permissions: {
    canEdit: boolean;
    canView: boolean;
    canShare: boolean;
  };
};

type Collaborator = {
  id: number;
  name: string;
  email: string;
  role: 'owner' | 'editor' | 'viewer';
  avatar?: string;
  addedAt: string;
  lastActive: string;
};

export default function SharedCoursesPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterRole, setFilterRole] = useState<string>("all");
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isManageModalOpen, setIsManageModalOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<SharedCourse | null>(null);
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);
  const { toast } = useToast();

  // Check if user has Enterprise plan
  const { data: user } = useQuery({
    queryKey: ['/api/auth/me'],
  });

  const isEnterprise = user?.plan === 'enterprise';

  // Fetch shared courses
  const { data: sharedCourses, isLoading } = useQuery({
    queryKey: ['/api/shared-courses'],
    enabled: isEnterprise,
  });

  // Fetch collaborators for selected course
  const { data: collaborators } = useQuery({
    queryKey: ['/api/courses', selectedCourse?.id, 'collaborators'],
    enabled: !!selectedCourse && isEnterprise,
  });

  const handleShareCourse = (course: SharedCourse) => {
    setSelectedCourse(course);
    setIsShareModalOpen(true);
  };

  const handleManageCollaborators = (course: SharedCourse) => {
    setSelectedCourse(course);
    setIsManageModalOpen(true);
  };

  const filteredCourses = (sharedCourses || []).filter((course: SharedCourse) => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.createdBy.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === "all" || course.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  if (!isEnterprise) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mb-4">
                <Share2 className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Shared Courses</h1>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Collaborate with your team on course creation and share knowledge across your organization
              </p>
            </div>

            <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-xl">
              <CardContent className="p-12 text-center">
                <div className="flex justify-center mb-6">
                  <Crown className="h-16 w-16 text-yellow-500" />
                </div>
                <h2 className="text-2xl font-bold mb-4">Enterprise Feature</h2>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  Shared Courses is available exclusively for Enterprise plan subscribers. 
                  Upgrade to unlock advanced collaboration features.
                </p>
                
                <div className="space-y-4 mb-8">
                  <div className="flex items-center justify-center gap-3 text-sm text-gray-700">
                    <Users className="h-4 w-4 text-blue-500" />
                    <span>Collaborate with unlimited team members</span>
                  </div>
                  <div className="flex items-center justify-center gap-3 text-sm text-gray-700">
                    <Share2 className="h-4 w-4 text-green-500" />
                    <span>Share courses with role-based permissions</span>
                  </div>
                  <div className="flex items-center justify-center gap-3 text-sm text-gray-700">
                    <Shield className="h-4 w-4 text-purple-500" />
                    <span>Advanced security and access controls</span>
                  </div>
                </div>

                <Button 
                  onClick={() => setIsUpgradeModalOpen(true)}
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                  size="lg"
                >
                  <Crown className="mr-2 h-4 w-4" />
                  Upgrade to Enterprise
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        <EnterpriseModal
          isOpen={isUpgradeModalOpen}
          onClose={() => setIsUpgradeModalOpen(false)}
          onSuccess={() => {
            toast({
              title: "Welcome to Enterprise!",
              description: "Shared Courses is now available. Refreshing page...",
            });
            setTimeout(() => window.location.reload(), 2000);
          }}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                  <Share2 className="h-5 w-5 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-gray-900">Shared Courses</h1>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                  <Crown className="h-3 w-3 mr-1" />
                  Enterprise
                </Badge>
              </div>
              <p className="text-gray-600">Collaborate and share courses with your team</p>
            </div>
            <Button 
              onClick={() => setIsShareModalOpen(true)}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              <Plus className="mr-2 h-4 w-4" />
              Share Course
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Shared</p>
                    <p className="text-2xl font-bold text-gray-900">{sharedCourses?.length || 0}</p>
                  </div>
                  <BookOpen className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Collaborators</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {sharedCourses?.reduce((total: number, course: SharedCourse) => total + course.collaborators, 0) || 0}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Views</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {sharedCourses?.reduce((total: number, course: SharedCourse) => total + course.views, 0) || 0}
                    </p>
                  </div>
                  <Eye className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Rating</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {sharedCourses?.length 
                        ? (sharedCourses.reduce((total: number, course: SharedCourse) => total + course.rating, 0) / sharedCourses.length).toFixed(1)
                        : "0.0"
                      }
                    </p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters and Search */}
          <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg mb-8">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search courses, creators, or descriptions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Loading State */}
          {isLoading && (
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="mt-4 text-gray-600">Loading shared courses...</p>
            </div>
          )}

          {/* Courses Grid */}
          {!isLoading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCourses.map((course: SharedCourse) => (
                <Card key={course.id} className="backdrop-blur-sm bg-white/70 border-0 shadow-lg hover:shadow-xl transition-shadow">
                  <CardHeader className="p-0">
                    <div className="relative">
                      <div className="w-full h-48 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-t-lg flex items-center justify-center">
                        <BookOpen className="h-12 w-12 text-blue-500" />
                      </div>
                      <Badge 
                        variant={course.status === 'active' ? 'default' : course.status === 'draft' ? 'secondary' : 'outline'}
                        className="absolute top-2 right-2"
                      >
                        {course.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-semibold text-lg mb-2">{course.title}</h3>
                        <p className="text-sm text-gray-600 line-clamp-2">{course.description}</p>
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span>By {course.createdBy}</span>
                        <span>{new Date(course.createdAt).toLocaleDateString()}</span>
                      </div>

                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          <span>{course.collaborators}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4" />
                          <span>{course.views}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span>{course.rating}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        {course.permissions.canEdit && (
                          <Button size="sm" variant="outline">
                            <Edit3 className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                        )}
                        {course.permissions.canShare && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleShareCourse(course)}
                          >
                            <Share2 className="h-4 w-4 mr-1" />
                            Share
                          </Button>
                        )}
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleManageCollaborators(course)}
                        >
                          <Settings className="h-4 w-4 mr-1" />
                          Manage
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {!isLoading && filteredCourses.length === 0 && (
            <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
              <CardContent className="p-12 text-center">
                <BookOpen className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No shared courses found</h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm || filterStatus !== "all" 
                    ? "Try adjusting your search or filters"
                    : "Start collaborating by sharing your first course"
                  }
                </p>
                {!searchTerm && filterStatus === "all" && (
                  <Button 
                    onClick={() => setIsShareModalOpen(true)}
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Share Your First Course
                  </Button>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Share Course Modal */}
      <Dialog open={isShareModalOpen} onOpenChange={setIsShareModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Share2 className="h-5 w-5 text-blue-500" />
              Share Course
            </DialogTitle>
          </DialogHeader>
          <Tabs defaultValue="invite" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="invite">Invite People</TabsTrigger>
              <TabsTrigger value="link">Share Link</TabsTrigger>
            </TabsList>
            
            <TabsContent value="invite" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="email">Email addresses</Label>
                  <Input
                    id="email"
                    placeholder="Enter email addresses separated by commas"
                  />
                </div>
                <div>
                  <Label htmlFor="role">Role</Label>
                  <Select defaultValue="viewer">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="viewer">Viewer - Can view content</SelectItem>
                      <SelectItem value="editor">Editor - Can edit content</SelectItem>
                      <SelectItem value="admin">Admin - Full access</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="message">Personal message (optional)</Label>
                  <Textarea
                    id="message"
                    placeholder="Add a personal message to your invitation"
                    rows={3}
                  />
                </div>
                <Button className="w-full">
                  <Send className="mr-2 h-4 w-4" />
                  Send Invitations
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="link" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label>Share link</Label>
                  <div className="flex gap-2">
                    <Input 
                      readOnly 
                      value="https://koursia.com/shared/course/abc123"
                      className="flex-1"
                    />
                    <Button variant="outline">
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div>
                  <Label htmlFor="link-permissions">Link permissions</Label>
                  <Select defaultValue="viewer">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="viewer">Anyone with link can view</SelectItem>
                      <SelectItem value="editor">Anyone with link can edit</SelectItem>
                      <SelectItem value="restricted">Restricted access</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Manage Collaborators Modal */}
      <Dialog open={isManageModalOpen} onOpenChange={setIsManageModalOpen}>
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              Manage Collaborators - {selectedCourse?.title}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div className="flex gap-2">
                <Input 
                  placeholder="Search collaborators..." 
                  className="w-64"
                />
                <Button variant="outline">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add
                </Button>
              </div>
            </div>

            <div className="space-y-3">
              {(collaborators || []).map((collaborator: Collaborator) => (
                <div key={collaborator.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                      {collaborator.name.charAt(0)}
                    </div>
                    <div>
                      <p className="font-medium">{collaborator.name}</p>
                      <p className="text-sm text-gray-500">{collaborator.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={
                      collaborator.role === 'owner' ? 'default' : 
                      collaborator.role === 'editor' ? 'secondary' : 'outline'
                    }>
                      {collaborator.role}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              {(!collaborators || collaborators.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>No collaborators found</p>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}