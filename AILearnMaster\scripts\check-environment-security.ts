#!/usr/bin/env tsx
/**
 * Environment Security Check Script
 * Validates environment configuration for security compliance
 */

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

interface EnvironmentCheck {
  name: string;
  status: 'secure' | 'warning' | 'critical';
  message: string;
  recommendation?: string;
}

class EnvironmentSecurityChecker {
  private checks: EnvironmentCheck[] = [];

  /**
   * Run comprehensive environment security checks
   */
  async checkEnvironmentSecurity(): Promise<void> {
    console.log('🔒 Environment Security Check\n');
    console.log('=' .repeat(50));

    // Check environment variables
    this.checkEnvironmentVariables();

    // Check file security
    this.checkFilesSecurity();

    // Check network configuration
    this.checkNetworkConfiguration();

    // Check dependency security
    await this.checkDependencySecurity();

    // Generate security report
    this.generateSecurityReport();
  }

  /**
   * Check environment variables for security issues
   */
  private checkEnvironmentVariables(): void {
    // Check NODE_ENV
    const nodeEnv = process.env.NODE_ENV;
    if (nodeEnv === 'production') {
      this.addCheck('NODE_ENV', 'secure', 'Production environment properly set');
    } else if (nodeEnv === 'development') {
      this.addCheck('NODE_ENV', 'warning', 'Development environment detected', 
        'Ensure this is not a production deployment');
    } else {
      this.addCheck('NODE_ENV', 'critical', 'NODE_ENV not properly set',
        'Set NODE_ENV to "production" for production deployments');
    }

    // Check SESSION_SECRET
    const sessionSecret = process.env.SESSION_SECRET;
    if (!sessionSecret) {
      this.addCheck('SESSION_SECRET', 'critical', 'SESSION_SECRET not configured',
        'Set a strong SESSION_SECRET of at least 32 characters');
    } else if (sessionSecret.length < 32) {
      this.addCheck('SESSION_SECRET', 'critical', 'SESSION_SECRET too weak',
        'Use a SESSION_SECRET of at least 32 characters');
    } else if (this.isWeakSecret(sessionSecret)) {
      this.addCheck('SESSION_SECRET', 'warning', 'SESSION_SECRET may be weak',
        'Use a cryptographically strong random string');
    } else {
      this.addCheck('SESSION_SECRET', 'secure', 'SESSION_SECRET properly configured');
    }

    // Check DATABASE_URL
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      this.addCheck('DATABASE_URL', 'critical', 'DATABASE_URL not configured');
    } else {
      if (nodeEnv === 'production' && !databaseUrl.includes('sslmode=require')) {
        this.addCheck('DATABASE_SSL', 'critical', 'Database SSL not enforced',
          'Add "?sslmode=require" to DATABASE_URL for production');
      } else {
        this.addCheck('DATABASE_SSL', 'secure', 'Database SSL properly configured');
      }

      // Check for exposed credentials
      if (databaseUrl.includes('localhost') && nodeEnv === 'production') {
        this.addCheck('DATABASE_HOST', 'warning', 'Database appears to be localhost in production');
      }
    }

    // Check CORS configuration
    const allowedOrigins = process.env.ALLOWED_ORIGINS;
    if (nodeEnv === 'production') {
      if (!allowedOrigins) {
        this.addCheck('CORS_ORIGINS', 'critical', 'ALLOWED_ORIGINS not set for production',
          'Set specific allowed origins for CORS');
      } else if (allowedOrigins.includes('*')) {
        this.addCheck('CORS_ORIGINS', 'critical', 'CORS allows all origins',
          'Remove wildcard (*) from ALLOWED_ORIGINS');
      } else {
        this.addCheck('CORS_ORIGINS', 'secure', 'CORS origins properly restricted');
      }
    }

    // Check debug settings
    if (process.env.DEBUG === 'true' && nodeEnv === 'production') {
      this.addCheck('DEBUG_MODE', 'warning', 'Debug mode enabled in production',
        'Disable debug mode for production');
    } else {
      this.addCheck('DEBUG_MODE', 'secure', 'Debug mode properly configured');
    }

    // Check API keys presence (without exposing values)
    const apiKeys = [
      'OPENAI_API_KEY',
      'MODAL_TOKEN_ID',
      'MODAL_TOKEN_SECRET',
      'AWS_ACCESS_KEY_ID',
      'AWS_SECRET_ACCESS_KEY'
    ];

    apiKeys.forEach(keyName => {
      const keyValue = process.env[keyName];
      if (keyValue) {
        if (this.isWeakSecret(keyValue)) {
          this.addCheck(keyName, 'warning', `${keyName} may be weak or default`);
        } else {
          this.addCheck(keyName, 'secure', `${keyName} configured`);
        }
      } else {
        this.addCheck(keyName, 'warning', `${keyName} not configured`,
          'Configure if this service is required');
      }
    });
  }

  /**
   * Check file security
   */
  private checkFilesSecurity(): void {
    // Check for .env files
    const envFiles = ['.env', '.env.local', '.env.production', '.env.development'];
    envFiles.forEach(file => {
      if (fs.existsSync(file)) {
        try {
          const stats = fs.statSync(file);
          const mode = stats.mode & parseInt('777', 8);
          
          if (mode > parseInt('600', 8)) {
            this.addCheck(`${file}_PERMISSIONS`, 'warning', 
              `${file} has overly permissive permissions`,
              `Run: chmod 600 ${file}`);
          } else {
            this.addCheck(`${file}_PERMISSIONS`, 'secure', 
              `${file} has appropriate permissions`);
          }
        } catch (error) {
          this.addCheck(`${file}_PERMISSIONS`, 'warning', 
            `Could not check ${file} permissions`);
        }
      }
    });

    // Check for sensitive files in public directories
    const publicDirs = ['public', 'static', 'assets'];
    publicDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        const sensitiveFiles = ['.env', 'config.json', 'secrets.json'];
        sensitiveFiles.forEach(file => {
          const filePath = path.join(dir, file);
          if (fs.existsSync(filePath)) {
            this.addCheck('PUBLIC_SENSITIVE_FILES', 'critical',
              `Sensitive file ${file} found in public directory ${dir}`,
              `Move ${file} outside of public directories`);
          }
        });
      }
    });

    // Check for .git directory in production
    if (fs.existsSync('.git') && process.env.NODE_ENV === 'production') {
      this.addCheck('GIT_DIRECTORY', 'warning',
        '.git directory present in production',
        'Consider excluding .git directory from production builds');
    }
  }

  /**
   * Check network configuration
   */
  private checkNetworkConfiguration(): void {
    const port = process.env.PORT || '3001';
    
    // Check if running on default ports
    if (port === '3000' || port === '8000' || port === '8080') {
      this.addCheck('PORT_SECURITY', 'warning',
        'Running on common default port',
        'Consider using a non-standard port for production');
    } else {
      this.addCheck('PORT_SECURITY', 'secure', 'Using appropriate port');
    }

    // Check host binding
    const host = process.env.HOST || 'localhost';
    if (host === '0.0.0.0' && process.env.NODE_ENV === 'production') {
      this.addCheck('HOST_BINDING', 'warning',
        'Binding to all interfaces (0.0.0.0)',
        'Consider binding to specific interface for security');
    }
  }

  /**
   * Check dependency security
   */
  private async checkDependencySecurity(): Promise<void> {
    // Check package.json for security-related configurations
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        
        // Check for security scripts
        if (packageJson.scripts) {
          const hasSecurityScripts = Object.keys(packageJson.scripts).some(script => 
            script.includes('security') || script.includes('audit')
          );
          
          if (hasSecurityScripts) {
            this.addCheck('SECURITY_SCRIPTS', 'secure', 
              'Security scripts configured in package.json');
          } else {
            this.addCheck('SECURITY_SCRIPTS', 'warning',
              'No security scripts found in package.json',
              'Add security audit scripts to package.json');
          }
        }

        // Check for known vulnerable packages (simplified check)
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
        const knownVulnerable = ['lodash@4.17.15', 'moment@2.24.0', 'axios@0.18.0'];
        
        let hasVulnerable = false;
        Object.entries(dependencies).forEach(([name, version]) => {
          const packageVersion = `${name}@${version}`;
          if (knownVulnerable.some(vuln => packageVersion.includes(vuln))) {
            hasVulnerable = true;
          }
        });

        if (hasVulnerable) {
          this.addCheck('VULNERABLE_DEPENDENCIES', 'warning',
            'Potentially vulnerable dependencies detected',
            'Run npm audit and update dependencies');
        } else {
          this.addCheck('VULNERABLE_DEPENDENCIES', 'secure',
            'No known vulnerable dependencies detected');
        }

      } catch (error) {
        this.addCheck('PACKAGE_JSON', 'warning', 'Could not parse package.json');
      }
    }

    // Check for package-lock.json or yarn.lock
    if (fs.existsSync('package-lock.json') || fs.existsSync('yarn.lock')) {
      this.addCheck('DEPENDENCY_LOCK', 'secure', 'Dependency lock file present');
    } else {
      this.addCheck('DEPENDENCY_LOCK', 'warning',
        'No dependency lock file found',
        'Use npm ci or yarn install --frozen-lockfile for reproducible builds');
    }
  }

  /**
   * Check if a secret is weak
   */
  private isWeakSecret(secret: string): boolean {
    const weakPatterns = [
      'password', 'secret', 'key', '123456', 'admin',
      'test', 'demo', 'default', 'changeme', 'example'
    ];
    
    const lowerSecret = secret.toLowerCase();
    return weakPatterns.some(pattern => lowerSecret.includes(pattern)) ||
           secret.length < 16 ||
           !/[A-Z]/.test(secret) ||
           !/[a-z]/.test(secret) ||
           !/[0-9]/.test(secret);
  }

  /**
   * Add security check result
   */
  private addCheck(name: string, status: EnvironmentCheck['status'], 
                  message: string, recommendation?: string): void {
    this.checks.push({ name, status, message, recommendation });
  }

  /**
   * Generate security report
   */
  private generateSecurityReport(): void {
    console.log('\n📊 ENVIRONMENT SECURITY REPORT');
    console.log('=' .repeat(50));

    const secure = this.checks.filter(c => c.status === 'secure').length;
    const warnings = this.checks.filter(c => c.status === 'warning').length;
    const critical = this.checks.filter(c => c.status === 'critical').length;

    console.log(`\nSummary:`);
    console.log(`  Secure: ${secure} ✅`);
    console.log(`  Warnings: ${warnings} ⚠️`);
    console.log(`  Critical: ${critical} ❌`);

    // Calculate security score
    const total = this.checks.length;
    const score = Math.round(((secure + warnings * 0.5) / total) * 100);
    console.log(`  Security Score: ${score}% ${score >= 80 ? '✅' : score >= 60 ? '⚠️' : '❌'}`);

    // Show detailed results
    console.log('\nDetailed Results:');
    this.checks.forEach(check => {
      const icon = check.status === 'secure' ? '✅' : 
                   check.status === 'warning' ? '⚠️' : '❌';
      console.log(`  ${icon} ${check.name}: ${check.message}`);
      if (check.recommendation) {
        console.log(`     💡 ${check.recommendation}`);
      }
    });

    // Recommendations
    if (critical > 0) {
      console.log('\n🚨 CRITICAL ISSUES FOUND!');
      console.log('Address critical issues before deployment.');
    } else if (warnings > 0) {
      console.log('\n⚠️ Warnings found - review before production deployment.');
    } else {
      console.log('\n🎉 Environment security check passed!');
    }

    console.log('\n' + '=' .repeat(50));
  }
}

// CLI interface
async function main() {
  const checker = new EnvironmentSecurityChecker();
  await checker.checkEnvironmentSecurity();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Environment security check failed:', error);
    process.exit(1);
  });
}

export { EnvironmentSecurityChecker };
