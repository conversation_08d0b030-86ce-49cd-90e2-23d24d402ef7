import React, { useState, useRef, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Upload, 
  Search, 
  Image, 
  Video, 
  Download, 
  Plus, 
  Trash2, 
  Eye,
  ExternalLink,
  Grid3X3,
  List,
  CheckCircle2,
  AlertCircle,
  RefreshCw,
  X,
  Heart,
  HeartOff,
  Filter,
  SortAsc,
  Loader2,
  ImageIcon,
  PlayCircle,
  <PERSON>mIn,
  Star
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import StockMediaBrowser from './StockMediaBrowser';
import EnhancedStockMedia from './EnhancedStockMedia';

interface MediaItem {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnailUrl?: string;
  size: number;
  uploadedAt: string;
  source: 'upload' | 'pexels' | 'pixabay';
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    author?: string;
    description?: string;
  };
}

interface StockMedia {
  id: string;
  url: string;
  thumbnailUrl: string;
  type: 'image' | 'video';
  title?: string;
  author?: string;
  tags?: string[];
  source: 'pexels' | 'pixabay';
}

interface EnhancedMediaManagerProps {
  onBack: () => void;
  onNext: () => void;
}

export function EnhancedMediaManager({ onBack, onNext }: EnhancedMediaManagerProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State management
  const [activeTab, setActiveTab] = useState('library');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [lightboxMedia, setLightboxMedia] = useState<MediaItem | StockMedia | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Fetch user's media library
  const { data: mediaLibrary = [], isLoading: isLoadingLibrary, error: libraryError } = useQuery({
    queryKey: ['/api/media'],
    refetchOnWindowFocus: false,
  });

  // Fetch stock photos
  const { data: stockPhotos = [], isLoading: isLoadingPhotos } = useQuery({
    queryKey: ['/api/stock/photos', searchQuery],
    enabled: activeTab === 'stock' && searchQuery.length > 2,
    refetchOnWindowFocus: false,
  });

  // Fetch stock videos
  const { data: stockVideos = [], isLoading: isLoadingVideos } = useQuery({
    queryKey: ['/api/stock/videos', searchQuery],
    enabled: activeTab === 'stock' && searchQuery.length > 2,
    refetchOnWindowFocus: false,
  });

  // Upload mutation with enhanced error handling
  const uploadMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      setIsUploading(true);
      setUploadProgress(0);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      try {
        const response = await fetch('/api/media/upload', {
          method: 'POST',
          body: formData,
        });

        clearInterval(progressInterval);
        setUploadProgress(100);

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Upload failed');
        }

        return response.json();
      } catch (error) {
        clearInterval(progressInterval);
        setUploadProgress(0);
        throw error;
      } finally {
        setTimeout(() => {
          setIsUploading(false);
          setUploadProgress(0);
        }, 1000);
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "Upload Successful",
        description: `${data.name} has been added to your media library.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Upload Failed",
        description: error.message || "Failed to upload file. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Import stock media mutation with better error handling
  const importMutation = useMutation({
    mutationFn: async ({ media, source }: { media: StockMedia; source: 'pexels' | 'pixabay' }) => {
      const endpoint = source === 'pexels' ? '/api/pexels/import' : '/api/pixabay/import';
      
      return await apiRequest('POST', endpoint, {
        id: media.id,
        type: media.type,
        title: media.title || `${source} ${media.type} ${media.id}`,
        url: media.url,
        thumbnailUrl: media.thumbnailUrl,
        author: media.author,
      });
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "Media Imported",
        description: `Successfully added to your media library.`,
      });
      setFavorites(prev => new Set([...prev, variables.media.id]));
    },
    onError: (error: any) => {
      toast({
        title: "Import Failed",
        description: error.message || "Failed to import media. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (mediaId: string) => {
      return await apiRequest(`/api/media/${mediaId}`, {
        method: 'DELETE',
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "Media Deleted",
        description: "Media has been removed from your library.",
      });
      setIsLightboxOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete media.",
        variant: "destructive",
      });
    },
  });

  // File upload handler
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    Array.from(files).forEach(file => {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/avi', 'video/mov'];
      if (!validTypes.includes(file.type)) {
        toast({
          title: "Invalid File Type",
          description: `${file.name} is not a supported file type.`,
          variant: "destructive",
        });
        return;
      }

      // Validate file size (50MB limit)
      if (file.size > 50 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: `${file.name} exceeds the 50MB limit.`,
          variant: "destructive",
        });
        return;
      }

      const formData = new FormData();
      formData.append('file', file);
      uploadMutation.mutate(formData);
    });

    // Reset input
    if (event.target) {
      event.target.value = '';
    }
  }, [uploadMutation, toast]);

  // Open lightbox
  const openLightbox = (media: MediaItem | StockMedia) => {
    setLightboxMedia(media);
    setIsLightboxOpen(true);
  };

  // Import stock media
  const handleImportMedia = (media: StockMedia) => {
    importMutation.mutate({ media, source: media.source });
  };

  // Download media
  const handleDownloadMedia = (media: MediaItem) => {
    const link = document.createElement('a');
    link.href = media.url;
    link.download = media.name;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Filter media
  const filteredMedia = mediaLibrary.filter((item: MediaItem) => {
    if (selectedCategory !== 'all' && item.type !== selectedCategory) return false;
    if (searchQuery && !item.name.toLowerCase().includes(searchQuery.toLowerCase())) return false;
    return true;
  });

  const getMediaIcon = (type: string) => {
    switch (type) {
      case 'image': return <ImageIcon className="h-4 w-4" />;
      case 'video': return <PlayCircle className="h-4 w-4" />;
      default: return <ImageIcon className="h-4 w-4" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Media Library</h1>
        <p className="text-muted-foreground">
          Manage your course media assets and import from stock libraries
        </p>
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
              <div className="flex-1">
                <div className="flex justify-between text-sm mb-1">
                  <span className="font-medium">Uploading...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <Progress value={uploadProgress} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="library">My Library ({mediaLibrary.length})</TabsTrigger>
          <TabsTrigger value="upload">Upload Media</TabsTrigger>
          <TabsTrigger value="stock">Stock Media</TabsTrigger>
        </TabsList>

        {/* My Library Tab */}
        <TabsContent value="library" className="space-y-4">
          {/* Controls */}
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search media..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9 w-64"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="all">All Types</option>
                <option value="image">Images</option>
                <option value="video">Videos</option>
              </select>
            </div>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Media Grid/List */}
          {isLoadingLibrary ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : libraryError ? (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-6 text-center">
                <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-2" />
                <h3 className="font-medium text-red-800 mb-1">Failed to load media</h3>
                <p className="text-red-600 text-sm">Please refresh the page and try again.</p>
              </CardContent>
            </Card>
          ) : filteredMedia.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <ImageIcon className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Media Found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery ? 'No media matches your search.' : 'Upload some media to get started.'}
                </p>
                <Button onClick={() => setActiveTab('upload')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Upload Media
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className={viewMode === 'grid' 
              ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
              : "space-y-2"
            }>
              {filteredMedia.map((media: MediaItem) => (
                <Card 
                  key={media.id} 
                  className={`group cursor-pointer hover:shadow-md transition-shadow ${
                    viewMode === 'list' ? 'p-3' : ''
                  }`}
                  onClick={() => openLightbox(media)}
                >
                  {viewMode === 'grid' ? (
                    <CardContent className="p-0">
                      <div className="aspect-square relative overflow-hidden rounded-t-lg">
                        {media.type === 'image' ? (
                          <img
                            src={media.thumbnailUrl || media.url}
                            alt={media.name}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                            loading="lazy"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                            <PlayCircle className="h-12 w-12 text-gray-400" />
                          </div>
                        )}
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                          <Eye className="h-6 w-6 text-white" />
                        </div>
                      </div>
                      <div className="p-3">
                        <p className="font-medium text-sm truncate">{media.name}</p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs text-muted-foreground">{formatFileSize(media.size)}</span>
                          <Badge variant="outline" className="text-xs">
                            {getMediaIcon(media.type)}
                            <span className="ml-1">{media.type}</span>
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  ) : (
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded overflow-hidden bg-gray-100 flex-shrink-0">
                        {media.type === 'image' ? (
                          <img
                            src={media.thumbnailUrl || media.url}
                            alt={media.name}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <PlayCircle className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm truncate">{media.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(media.size)} • {media.type} • {new Date(media.uploadedAt).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost" onClick={(e) => { e.stopPropagation(); handleDownloadMedia(media); }}>
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" onClick={(e) => { e.stopPropagation(); deleteMutation.mutate(media.id); }}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Upload Tab */}
        <TabsContent value="upload" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Media Files
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div 
                className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
                onDrop={(e) => {
                  e.preventDefault();
                  const files = Array.from(e.dataTransfer.files);
                  if (files.length > 0) {
                    const event = { target: { files } } as any;
                    handleFileUpload(event);
                  }
                }}
                onDragOver={(e) => e.preventDefault()}
              >
                <Upload className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">Drop files here or click to upload</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  Supports: JPG, PNG, GIF, MP4, AVI, MOV (max 50MB each)
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Choose Files
                </Button>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*,video/*"
                onChange={handleFileUpload}
                className="hidden"
              />

              <div className="text-sm text-muted-foreground">
                <p>• Maximum file size: 50MB</p>
                <p>• Supported formats: Images (JPG, PNG, GIF), Videos (MP4, AVI, MOV)</p>
                <p>• Multiple files can be uploaded at once</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Stock Media Tab */}
        <TabsContent value="stock" className="space-y-4">
          <StockMediaBrowser
            onMediaImported={(importedMedia) => {
              toast({
                title: "Media Imported Successfully",
                description: `${importedMedia.length} items added to your Media Library`,
              });
              queryClient.invalidateQueries({ queryKey: ['/api/media'] });
            }}
          />
        </TabsContent>


              {/* Stock Photos */}
              {stockPhotos.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                    <ImageIcon className="h-5 w-5" />
                    Photos
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {stockPhotos.map((photo: StockMedia) => (
                      <Card key={`${photo.source}-${photo.id}`} className="group cursor-pointer hover:shadow-md transition-shadow">
                        <CardContent className="p-0">
                          <div className="aspect-square relative overflow-hidden rounded-t-lg">
                            <img
                              src={photo.thumbnailUrl}
                              alt={photo.title || 'Stock photo'}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                              loading="lazy"
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <div className="flex gap-2">
                                <Button size="sm" variant="secondary" onClick={(e) => { e.stopPropagation(); openLightbox(photo); }}>
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button 
                                  size="sm" 
                                  onClick={(e) => { e.stopPropagation(); handleImportMedia(photo); }}
                                  disabled={importMutation.isPending}
                                >
                                  {importMutation.isPending ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : favorites.has(photo.id) ? (
                                    <CheckCircle2 className="h-4 w-4" />
                                  ) : (
                                    <Plus className="h-4 w-4" />
                                  )}
                                </Button>
                              </div>
                            </div>
                          </div>
                          <div className="p-2">
                            <div className="flex items-center justify-between">
                              <Badge variant="outline" className="text-xs">
                                <ImageIcon className="h-3 w-3 mr-1" />
                                {photo.source}
                              </Badge>
                              {photo.author && (
                                <span className="text-xs text-muted-foreground truncate ml-2">
                                  by {photo.author}
                                </span>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Stock Videos */}
              {stockVideos.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                    <PlayCircle className="h-5 w-5" />
                    Videos
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {stockVideos.map((video: StockMedia) => (
                      <Card key={`${video.source}-${video.id}`} className="group cursor-pointer hover:shadow-md transition-shadow">
                        <CardContent className="p-0">
                          <div className="aspect-video relative overflow-hidden rounded-t-lg">
                            <img
                              src={video.thumbnailUrl}
                              alt={video.title || 'Stock video'}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                              loading="lazy"
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <div className="flex gap-2">
                                <Button size="sm" variant="secondary" onClick={(e) => { e.stopPropagation(); openLightbox(video); }}>
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button 
                                  size="sm" 
                                  onClick={(e) => { e.stopPropagation(); handleImportMedia(video); }}
                                  disabled={importMutation.isPending}
                                >
                                  {importMutation.isPending ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : favorites.has(video.id) ? (
                                    <CheckCircle2 className="h-4 w-4" />
                                  ) : (
                                    <Plus className="h-4 w-4" />
                                  )}
                                </Button>
                              </div>
                            </div>
                            <PlayCircle className="absolute top-2 left-2 h-6 w-6 text-white" />
                          </div>
                          <div className="p-2">
                            <div className="flex items-center justify-between">
                              <Badge variant="outline" className="text-xs">
                                <PlayCircle className="h-3 w-3 mr-1" />
                                {video.source}
                              </Badge>
                              {video.author && (
                                <span className="text-xs text-muted-foreground truncate ml-2">
                                  by {video.author}
                                </span>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Loading states */}
              {(isLoadingPhotos || isLoadingVideos) && (
                <div className="flex items-center justify-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              )}

              {/* No results */}
              {searchQuery.length > 2 && !isLoadingPhotos && !isLoadingVideos && stockPhotos.length === 0 && stockVideos.length === 0 && (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Search className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Results Found</h3>
                    <p className="text-muted-foreground">
                      Try different keywords or check your spelling
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Lightbox Modal */}
      <Dialog open={isLightboxOpen} onOpenChange={setIsLightboxOpen}>
        <DialogContent className="max-w-4xl w-full">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{lightboxMedia?.title || (lightboxMedia as MediaItem)?.name || 'Media Preview'}</span>
              <Button variant="ghost" size="sm" onClick={() => setIsLightboxOpen(false)}>
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {lightboxMedia && (
            <div className="space-y-4">
              <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                {lightboxMedia.type === 'image' ? (
                  <img
                    src={lightboxMedia.url}
                    alt={(lightboxMedia as MediaItem).name || lightboxMedia.title}
                    className="w-full h-full object-contain"
                  />
                ) : (
                  <video
                    src={lightboxMedia.url}
                    controls
                    className="w-full h-full"
                  />
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  {(lightboxMedia as StockMedia).author && (
                    <p className="text-sm text-muted-foreground">
                      by {(lightboxMedia as StockMedia).author}
                    </p>
                  )}
                  {(lightboxMedia as MediaItem).size && (
                    <p className="text-sm text-muted-foreground">
                      {formatFileSize((lightboxMedia as MediaItem).size)}
                    </p>
                  )}
                </div>
                
                <div className="flex gap-2">
                  {'source' in lightboxMedia ? (
                    <Button onClick={() => handleImportMedia(lightboxMedia as StockMedia)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Import to Library
                    </Button>
                  ) : (
                    <>
                      <Button variant="outline" onClick={() => handleDownloadMedia(lightboxMedia as MediaItem)}>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                      <Button variant="destructive" onClick={() => deleteMutation.mutate((lightboxMedia as MediaItem).id)}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Navigation */}
      <div className="flex items-center justify-between pt-6 border-t">
        <Button variant="outline" onClick={onBack}>
          Back to Scripts
        </Button>
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            {mediaLibrary.length} media files in your library
          </p>
        </div>
        <Button onClick={onNext}>
          Continue to Quizzes
        </Button>
      </div>
    </div>
  );
}