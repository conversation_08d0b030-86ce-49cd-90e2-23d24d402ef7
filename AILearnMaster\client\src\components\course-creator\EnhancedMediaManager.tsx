import React, { useState, useRef, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Upload, 
  Search, 
  Image, 
  Video, 
  Download, 
  Plus, 
  Trash2, 
  Eye,
  ExternalLink,
  Grid3X3,
  List,
  CheckCircle2,
  AlertCircle,
  RefreshCw,
  X,
  Heart,
  HeartOff,
  Filter,
  SortAsc,
  Loader2,
  ImageIcon,
  PlayCircle,
  ZoomIn,
  Star
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface MediaItem {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnailUrl?: string;
  size: number;
  uploadedAt: string;
  source: 'upload' | 'pexels' | 'pixabay';
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    author?: string;
    description?: string;
  };
}

interface StockMedia {
  id: string;
  url: string;
  thumbnailUrl: string;
  type: 'image' | 'video';
  title?: string;
  author?: string;
  tags?: string[];
  source: 'pexels' | 'pixabay';
}

interface EnhancedMediaManagerProps {
  onBack: () => void;
  onNext: () => void;
}

export function EnhancedMediaManager({ onBack, onNext }: EnhancedMediaManagerProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State management
  const [activeTab, setActiveTab] = useState('library');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [lightboxMedia, setLightboxMedia] = useState<MediaItem | StockMedia | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Utility functions
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleImportMedia = async (media: StockMedia) => {
    try {
      const response = await apiRequest('POST', '/api/media/import-stock', {
        url: media.url,
        title: media.title || 'Imported Media',
        type: media.type,
        source: media.source
      });
      
      if (response.ok) {
        queryClient.invalidateQueries({ queryKey: ['/api/media'] });
        toast({
          title: "Media Imported",
          description: "Stock media has been added to your library",
        });
        setIsLightboxOpen(false);
      }
    } catch (error) {
      toast({
        title: "Import Failed",
        description: "Failed to import stock media",
        variant: "destructive"
      });
    }
  };

  const handleDownloadMedia = (media: MediaItem) => {
    const link = document.createElement('a');
    link.href = media.url;
    link.download = media.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Fetch user's media library
  const { data: mediaLibrary = [], isLoading: isLoadingLibrary } = useQuery<MediaItem[]>({
    queryKey: ['/api/media'],
    refetchOnWindowFocus: false,
  });

  // Fetch stock photos
  const { data: stockPhotos = [], isLoading: isLoadingPhotos } = useQuery<StockMedia[]>({
    queryKey: ['/api/stock/photos', searchQuery],
    enabled: activeTab === 'stock' && searchQuery.length > 2,
    refetchOnWindowFocus: false,
  });

  // Fetch stock videos
  const { data: stockVideos = [], isLoading: isLoadingVideos } = useQuery<StockMedia[]>({
    queryKey: ['/api/stock/videos', searchQuery],
    enabled: activeTab === 'stock' && searchQuery.length > 2,
    refetchOnWindowFocus: false,
  });

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      setIsUploading(true);
      setUploadProgress(0);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      try {
        const response = await fetch('/api/media/upload', {
          method: 'POST',
          body: formData,
        });

        clearInterval(progressInterval);
        setUploadProgress(100);

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Upload failed');
        }

        return response.json();
      } catch (error) {
        clearInterval(progressInterval);
        setUploadProgress(0);
        throw error;
      } finally {
        setTimeout(() => {
          setIsUploading(false);
          setUploadProgress(0);
        }, 1000);
      }
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "Upload Successful",
        description: `${data.name} has been added to your media library.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Upload Failed",
        description: error.message || "Failed to upload file. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Delete media mutation
  const deleteMutation = useMutation({
    mutationFn: async (mediaId: string) => {
      const response = await apiRequest('DELETE', `/api/media/${mediaId}`);
      if (!response.ok) {
        throw new Error('Failed to delete media');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "Media Deleted",
        description: "Media file has been removed from your library",
      });
      setIsLightboxOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete media file",
        variant: "destructive"
      });
    }
  });

  // Handle file upload
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    files.forEach(file => {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Invalid File Type",
          description: `${file.name} is not a supported file type.`,
          variant: "destructive",
        });
        return;
      }

      // Validate file size (50MB limit)
      if (file.size > 50 * 1024 * 1024) {
        toast({
          title: "File Too Large",
          description: `${file.name} exceeds the 50MB limit.`,
          variant: "destructive",
        });
        return;
      }

      const formData = new FormData();
      formData.append('file', file);
      uploadMutation.mutate(formData);
    });

    // Reset input
    if (event.target) {
      event.target.value = '';
    }
  }, [uploadMutation, toast]);

  // Open lightbox
  const openLightbox = (media: MediaItem | StockMedia) => {
    setLightboxMedia(media);
    setIsLightboxOpen(true);
  };

  // Filter media
  const filteredMedia = mediaLibrary.filter((item: MediaItem) => {
    if (selectedCategory !== 'all' && item.type !== selectedCategory) return false;
    if (searchQuery && !item.name.toLowerCase().includes(searchQuery.toLowerCase())) return false;
    return true;
  });

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Media Manager</h1>
          <p className="text-sm text-gray-600 mt-1">
            Upload, organize, and manage your course media assets
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}>
            {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="library">My Library</TabsTrigger>
          <TabsTrigger value="stock">Stock Media</TabsTrigger>
        </TabsList>

        <TabsContent value="library" className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search your media..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="all">All Types</option>
                <option value="image">Images</option>
                <option value="video">Videos</option>
                <option value="document">Documents</option>
              </select>
              <Button onClick={() => fileInputRef.current?.click()}>
                <Upload className="h-4 w-4 mr-2" />
                Upload Media
              </Button>
            </div>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*,video/*,.pdf"
            onChange={handleFileUpload}
            className="hidden"
          />

          {isUploading && (
            <Card>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Uploading...</span>
                    <span className="text-sm text-gray-500">{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              </CardContent>
            </Card>
          )}

          {isLoadingLibrary ? (
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {[...Array(8)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <div className="aspect-video bg-gray-200 rounded-t-lg" />
                  <CardContent className="p-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className={viewMode === 'grid' ? 
              "grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4" : 
              "space-y-2"
            }>
              {filteredMedia.map((media: MediaItem) => (
                <Card key={media.id} className="group hover:shadow-md transition-shadow cursor-pointer">
                  <div className="relative">
                    <div className="aspect-video bg-gray-100 overflow-hidden rounded-t-lg">
                      {media.type === 'image' ? (
                        <img
                          src={media.thumbnailUrl || media.url}
                          alt={media.name}
                          className="w-full h-full object-cover"
                        />
                      ) : media.type === 'video' ? (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <PlayCircle className="h-8 w-8 text-gray-400" />
                        </div>
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <ImageIcon className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center rounded-t-lg">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                        <Button size="sm" variant="secondary" onClick={() => openLightbox(media)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="secondary" onClick={() => handleDownloadMedia(media)}>
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  <CardContent className="p-3">
                    <h3 className="font-medium text-sm truncate">{media.name}</h3>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="outline" className="text-xs">
                        {media.type}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {formatFileSize(media.size)}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {!isLoadingLibrary && filteredMedia.length === 0 && (
            <div className="text-center py-12">
              <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No media found</h3>
              <p className="text-gray-500 mb-4">
                {searchQuery || selectedCategory !== 'all' 
                  ? 'Try adjusting your search or filter settings.' 
                  : 'Upload your first media file to get started.'
                }
              </p>
              {!searchQuery && selectedCategory === 'all' && (
                <Button onClick={() => fileInputRef.current?.click()}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Media
                </Button>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="stock" className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search stock photos and videos..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {searchQuery.length <= 2 ? (
            <div className="text-center py-12">
              <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Search Stock Media</h3>
              <p className="text-gray-500">
                Enter at least 3 characters to search for photos and videos
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Stock Photos */}
              {stockPhotos.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium mb-4">Photos</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {stockPhotos.map((photo: StockMedia) => (
                      <Card key={photo.id} className="group hover:shadow-md transition-shadow cursor-pointer">
                        <div className="relative">
                          <div className="aspect-video bg-gray-100 overflow-hidden rounded-t-lg">
                            <img
                              src={photo.thumbnailUrl}
                              alt={photo.title || 'Stock photo'}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center rounded-t-lg">
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                              <Button size="sm" variant="secondary" onClick={() => openLightbox(photo)}>
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="secondary" onClick={() => handleImportMedia(photo)}>
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                        <CardContent className="p-3">
                          <h3 className="font-medium text-sm truncate">{photo.title || 'Untitled'}</h3>
                          <div className="flex items-center justify-between mt-2">
                            <Badge variant="outline" className="text-xs">
                              {photo.source}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {photo.author}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {/* Stock Videos */}
              {stockVideos.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium mb-4">Videos</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {stockVideos.map((video: StockMedia) => (
                      <Card key={video.id} className="group hover:shadow-md transition-shadow cursor-pointer">
                        <div className="relative">
                          <div className="aspect-video bg-gray-100 overflow-hidden rounded-t-lg">
                            <img
                              src={video.thumbnailUrl}
                              alt={video.title || 'Stock video'}
                              className="w-full h-full object-cover"
                            />
                            <div className="absolute inset-0 flex items-center justify-center">
                              <PlayCircle className="h-8 w-8 text-white opacity-80" />
                            </div>
                          </div>
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center rounded-t-lg">
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-2">
                              <Button size="sm" variant="secondary" onClick={() => openLightbox(video)}>
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="secondary" onClick={() => handleImportMedia(video)}>
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                        <CardContent className="p-3">
                          <h3 className="font-medium text-sm truncate">{video.title || 'Untitled'}</h3>
                          <div className="flex items-center justify-between mt-2">
                            <Badge variant="outline" className="text-xs">
                              {video.source}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {video.author}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {stockPhotos.length === 0 && stockVideos.length === 0 && (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
                  <p className="text-gray-500">
                    Try different search terms to find photos and videos
                  </p>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Lightbox Modal */}
      <Dialog open={isLightboxOpen} onOpenChange={setIsLightboxOpen}>
        <DialogContent className="max-w-4xl w-full">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{lightboxMedia?.title || (lightboxMedia as MediaItem)?.name || 'Media Preview'}</span>
              <Button variant="ghost" size="sm" onClick={() => setIsLightboxOpen(false)}>
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {lightboxMedia && (
            <div className="space-y-4">
              <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                {lightboxMedia.type === 'image' ? (
                  <img
                    src={lightboxMedia.url}
                    alt={lightboxMedia.title || (lightboxMedia as MediaItem)?.name || 'Media'}
                    className="w-full h-full object-contain"
                  />
                ) : lightboxMedia.type === 'video' ? (
                  <video
                    src={lightboxMedia.url}
                    controls
                    className="w-full h-full"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <ImageIcon className="h-16 w-16 text-gray-400" />
                  </div>
                )}
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">
                    {lightboxMedia.title || (lightboxMedia as MediaItem)?.name || 'Untitled'}
                  </h3>
                  {(lightboxMedia as MediaItem).size && (
                    <p className="text-sm text-muted-foreground">
                      {formatFileSize((lightboxMedia as MediaItem).size)}
                    </p>
                  )}
                </div>
                
                <div className="flex gap-2">
                  {'source' in lightboxMedia ? (
                    <Button onClick={() => handleImportMedia(lightboxMedia as StockMedia)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Import to Library
                    </Button>
                  ) : (
                    <>
                      <Button variant="outline" onClick={() => handleDownloadMedia(lightboxMedia as MediaItem)}>
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                      <Button variant="destructive" onClick={() => deleteMutation.mutate((lightboxMedia as MediaItem).id)}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Navigation */}
      <div className="flex items-center justify-between pt-6 border-t">
        <Button variant="outline" onClick={onBack}>
          Back to Scripts
        </Button>
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            {mediaLibrary.length} media files in your library
          </p>
        </div>
        <Button onClick={onNext}>
          Create Assessment
        </Button>
      </div>
    </div>
  );
}