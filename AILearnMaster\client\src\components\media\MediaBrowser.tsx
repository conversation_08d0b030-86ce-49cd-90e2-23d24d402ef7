import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Search, Film, FileText, Image, Music, File, RefreshCw } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { MediaItem } from "@/types/media";
import { cn } from "@/lib/utils";

interface MediaBrowserProps {
  filter?: 'video' | 'document' | 'image' | 'audio' | 'all';
  onSelect: (media: MediaItem) => void;
  maxHeight?: string;
}

export function MediaBrowser({ 
  filter = 'all', 
  onSelect,
  maxHeight = '400px'
}: MediaBrowserProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<'my-media' | 'stock'>('my-media');
  
  // Mock query for media items
  const { data: mediaItems, isLoading } = useQuery({
    queryKey: ['/api/media', filter],
    queryFn: async () => {
      // In a real app, this would fetch from the server
      // const res = await apiRequest('GET', `/api/media?type=${filter}`);
      // return res.json();
      
      // For now, return mock data
      return [
        {
          id: '1',
          name: 'Introduction Video',
          url: 'https://example.com/video1.mp4',
          type: 'video',
          description: 'Course introduction video',
          createdAt: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Course Syllabus',
          url: 'https://example.com/syllabus.pdf',
          type: 'document',
          description: 'Course syllabus document',
          createdAt: new Date().toISOString()
        },
        {
          id: '3',
          name: 'Course Banner',
          url: 'https://example.com/banner.jpg',
          type: 'image',
          description: 'Course banner image',
          createdAt: new Date().toISOString()
        }
      ] as MediaItem[];
    }
  });

  // Filter media items based on type and search term
  const filteredItems = mediaItems?.filter(item => 
    (filter === 'all' || item.type === filter) && 
    (item.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
     (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase())))
  );

  const getIconForType = (type: string) => {
    switch (type) {
      case 'video':
        return <Film className="h-5 w-5 text-blue-500" />;
      case 'document':
        return <FileText className="h-5 w-5 text-green-500" />;
      case 'image':
        return <Image className="h-5 w-5 text-purple-500" />;
      case 'audio':
        return <Music className="h-5 w-5 text-amber-500" />;
      default:
        return <File className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search media..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Button variant="outline" size="icon" title="Refresh">
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'my-media' | 'stock')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="my-media">My Media</TabsTrigger>
          <TabsTrigger value="stock">Stock Media</TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-media" className="pt-4">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : filteredItems && filteredItems.length > 0 ? (
            <ScrollArea className={`h-${maxHeight}`}>
              <div className="grid grid-cols-1 gap-4">
                {filteredItems.map((media) => (
                  <Card 
                    key={media.id}
                    className="cursor-pointer hover:border-primary transition-colors"
                    onClick={() => onSelect(media)}
                  >
                    <CardContent className="p-4 flex items-center gap-3">
                      <div className="flex-shrink-0">
                        {getIconForType(media.type)}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium truncate">{media.name}</div>
                        <div className="text-xs text-muted-foreground flex items-center gap-2">
                          <span className="capitalize">{media.type}</span>
                          <span>•</span>
                          <span>{formatDate(media.createdAt)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              {searchTerm 
                ? "No media found matching your search." 
                : "No media available. Upload some content first."}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="stock" className="pt-4">
          <div className="text-center py-8 text-muted-foreground">
            Stock media functionality will be available soon.
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}