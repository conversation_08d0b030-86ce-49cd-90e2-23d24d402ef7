import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, Video, UserCircle, ArrowRight } from "lucide-react";

interface ModernCourseFormatSelectorProps {
  onSelect?: (format: 'traditional' | 'avatar') => void;
  onSelectFormat?: (format: 'traditional' | 'avatar') => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ModernCourseFormatSelector({
  onSelect,
  onSelectFormat,
  open,
  onOpenChange
}: ModernCourseFormatSelectorProps) {
  const [selectedFormat, setSelectedFormat] = useState<'traditional' | 'avatar' | null>(null);

  const handleSelect = (format: 'traditional' | 'avatar') => {
    setSelectedFormat(format);
  };

  const handleContinue = () => {
    if (selectedFormat) {
      // Use onSelectFormat if provided, otherwise fall back to onSelect
      if (onSelectFormat) {
        onSelectFormat(selectedFormat);
      } else if (onSelect) {
        onSelect(selectedFormat);
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card 
          className={`overflow-hidden cursor-pointer transition-all hover:border-primary ${
            selectedFormat === 'traditional' ? 'border-2 border-primary ring-2 ring-primary/20' : ''
          }`}
          onClick={() => handleSelect('traditional')}
        >
          <CardContent className="p-0">
            <div className="relative aspect-video bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center">
              <Video className="h-16 w-16 text-primary/80" />
              {selectedFormat === 'traditional' && (
                <div className="absolute top-3 right-3 bg-primary text-primary-foreground rounded-full p-1">
                  <CheckCircle className="h-5 w-5" />
                </div>
              )}
            </div>
            <div className="p-4">
              <h3 className="text-lg font-semibold mb-2">Traditional Course</h3>
              <p className="text-muted-foreground text-sm">
                Create a structured course with sections, lessons, and various content types including videos, documents, and quizzes.
              </p>
              {selectedFormat === 'traditional' && (
                <div className="mt-3 pt-3 border-t">
                  <p className="text-sm font-medium text-primary">Selected</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card 
          className={`overflow-hidden cursor-pointer transition-all hover:border-primary ${
            selectedFormat === 'avatar' ? 'border-2 border-primary ring-2 ring-primary/20' : ''
          }`}
          onClick={() => handleSelect('avatar')}
        >
          <CardContent className="p-0">
            <div className="relative aspect-video bg-gradient-to-br from-purple-50 to-purple-100 flex items-center justify-center">
              <UserCircle className="h-16 w-16 text-primary/80" />
              {selectedFormat === 'avatar' && (
                <div className="absolute top-3 right-3 bg-primary text-primary-foreground rounded-full p-1">
                  <CheckCircle className="h-5 w-5" />
                </div>
              )}
            </div>
            <div className="p-4">
              <h3 className="text-lg font-semibold mb-2">Talking Avatar Course</h3>
              <p className="text-muted-foreground text-sm">
                Create an engaging course with an AI-powered avatar that presents your content based on your script.
              </p>
              {selectedFormat === 'avatar' && (
                <div className="mt-3 pt-3 border-t">
                  <p className="text-sm font-medium text-primary">Selected</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="flex justify-center">
        <Button 
          onClick={handleContinue}
          disabled={!selectedFormat}
          size="lg"
          className="gap-2 min-w-[200px] mt-4"
        >
          Continue
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}