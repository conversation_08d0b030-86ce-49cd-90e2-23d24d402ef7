import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'wouter';
import { 
  Settings,
  Shield,
  Key,
  Bell,
  Clock,
  Globe,
  Database,
  Zap,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Info,
  ExternalLink,
  Save,
  ArrowLeft,
  Activity,
  BarChart3,
  Lock,
  Webhook,
  Code,
  Download,
  Upload,
  Trash2,
  Eye,
  EyeOff,
  Copy
} from 'lucide-react';

interface IntegrationSettings {
  id: string;
  autoSync: boolean;
  syncFrequency: string;
  errorNotifications: boolean;
  webhookVerification: boolean;
  requireTwoFactor: boolean;
  rateLimitWarnings: boolean;
  debugMode: boolean;
  retryAttempts: number;
  timeoutDuration: number;
  batchSize: number;
  logLevel: string;
}

export default function PlatformIntegrationsSettings() {
  const [settings, setSettings] = useState<IntegrationSettings>({
    id: 'default',
    autoSync: true,
    syncFrequency: 'hourly',
    errorNotifications: true,
    webhookVerification: true,
    requireTwoFactor: false,
    rateLimitWarnings: true,
    debugMode: false,
    retryAttempts: 3,
    timeoutDuration: 30,
    batchSize: 50,
    logLevel: 'info'
  });

  const [apiKeys, setApiKeys] = useState({
    udemy: '',
    youtube: '',
    teachable: '',
    mailchimp: '',
    google_analytics: ''
  });

  const [showApiKeys, setShowApiKeys] = useState({
    udemy: false,
    youtube: false,
    teachable: false,
    mailchimp: false,
    google_analytics: false
  });

  // Use webhooks from API or fallback to default
  const webhookEndpoints = webhooks || [
    { id: 1, url: 'https://api.koursia.com/webhooks/udemy', platform: 'Udemy', status: 'active' },
    { id: 2, url: 'https://api.koursia.com/webhooks/youtube', platform: 'YouTube', status: 'active' },
    { id: 3, url: 'https://api.koursia.com/webhooks/mailchimp', platform: 'Mailchimp', status: 'pending' }
  ];

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch current settings
  const { data: currentSettings, isLoading: settingsLoading } = useQuery({
    queryKey: ['/api/integrations/settings'],
    queryFn: () => apiRequest('/api/integrations/settings')
  });

  // Fetch webhooks
  const { data: webhooks, isLoading: webhooksLoading } = useQuery({
    queryKey: ['/api/integrations/webhooks'],
    queryFn: () => apiRequest('/api/integrations/webhooks')
  });

  // Save settings mutation
  const saveSettingsMutation = useMutation({
    mutationFn: async (newSettings: IntegrationSettings) => {
      return apiRequest('/api/integrations/settings', {
        method: 'POST',
        body: JSON.stringify(newSettings)
      });
    },
    onSuccess: () => {
      toast({
        title: "Settings Saved",
        description: "Integration settings have been updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations/settings'] });
    },
    onError: (error: any) => {
      toast({
        title: "Save Failed",
        description: error.message || "Failed to save settings",
        variant: "destructive",
      });
    }
  });

  // Save API keys mutation
  const saveApiKeysMutation = useMutation({
    mutationFn: async (keys: typeof apiKeys) => {
      return apiRequest('/api/integrations/api-keys', {
        method: 'POST',
        body: JSON.stringify(keys)
      });
    },
    onSuccess: () => {
      toast({
        title: "API Keys Saved",
        description: "API keys have been updated securely",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Save Failed",
        description: error.message || "Failed to save API keys",
        variant: "destructive",
      });
    }
  });

  // Test webhook mutation
  const testWebhookMutation = useMutation({
    mutationFn: async (webhookId: number) => {
      return apiRequest(`/api/integrations/webhooks/${webhookId}/test`, {
        method: 'POST'
      });
    },
    onSuccess: (data) => {
      toast({
        title: "Webhook Test Successful",
        description: `Response time: ${data.responseTime}ms`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Webhook Test Failed",
        description: error.message || "Failed to test webhook",
        variant: "destructive",
      });
    }
  });

  const handleSettingChange = (key: keyof IntegrationSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleApiKeyChange = (platform: string, value: string) => {
    setApiKeys(prev => ({ ...prev, [platform]: value }));
  };

  const toggleApiKeyVisibility = (platform: string) => {
    setShowApiKeys(prev => ({ ...prev, [platform]: !prev[platform] }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Text copied to clipboard",
    });
  };

  const exportSettings = () => {
    const settingsData = {
      settings,
      apiKeys: Object.keys(apiKeys).reduce((acc, key) => {
        acc[key] = apiKeys[key] ? '***REDACTED***' : '';
        return acc;
      }, {} as any),
      webhookEndpoints,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(settingsData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `koursia-integration-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Settings Exported",
      description: "Settings have been exported to your downloads folder",
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/platform-integrations">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Integrations
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600">
                <Settings className="h-6 w-6 text-white" />
              </div>
              Integration Settings
            </h1>
            <p className="text-muted-foreground mt-2">
              Configure global settings, API keys, and advanced options for platform integrations
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportSettings}>
            <Download className="h-4 w-4 mr-2" />
            Export Settings
          </Button>
          <Button 
            onClick={() => saveSettingsMutation.mutate(settings)}
            disabled={saveSettingsMutation.isPending}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Sync Settings
                </CardTitle>
                <CardDescription>Configure how and when platforms sync data</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Auto-sync enabled</Label>
                    <p className="text-sm text-muted-foreground">Automatically sync data with connected platforms</p>
                  </div>
                  <Switch 
                    checked={settings.autoSync}
                    onCheckedChange={(checked) => handleSettingChange('autoSync', checked)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Sync frequency</Label>
                  <Select 
                    value={settings.syncFrequency} 
                    onValueChange={(value) => handleSettingChange('syncFrequency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="realtime">Real-time</SelectItem>
                      <SelectItem value="5min">Every 5 minutes</SelectItem>
                      <SelectItem value="15min">Every 15 minutes</SelectItem>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Batch size</Label>
                  <Input
                    type="number"
                    value={settings.batchSize}
                    onChange={(e) => handleSettingChange('batchSize', parseInt(e.target.value))}
                    min={1}
                    max={1000}
                  />
                  <p className="text-xs text-muted-foreground">Number of items to process per batch</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notifications
                </CardTitle>
                <CardDescription>Control what notifications you receive</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Error notifications</Label>
                    <p className="text-sm text-muted-foreground">Get notified when sync errors occur</p>
                  </div>
                  <Switch 
                    checked={settings.errorNotifications}
                    onCheckedChange={(checked) => handleSettingChange('errorNotifications', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="font-medium">Rate limit warnings</Label>
                    <p className="text-sm text-muted-foreground">Alert when approaching API rate limits</p>
                  </div>
                  <Switch 
                    checked={settings.rateLimitWarnings}
                    onCheckedChange={(checked) => handleSettingChange('rateLimitWarnings', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Log level</Label>
                  <Select 
                    value={settings.logLevel} 
                    onValueChange={(value) => handleSettingChange('logLevel', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="error">Error only</SelectItem>
                      <SelectItem value="warn">Warning & Error</SelectItem>
                      <SelectItem value="info">Info, Warning & Error</SelectItem>
                      <SelectItem value="debug">All (Debug mode)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="api-keys" className="space-y-6">
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              API keys are encrypted and stored securely. We never share your credentials with third parties.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {Object.entries(apiKeys).map(([platform, key]) => (
              <Card key={platform}>
                <CardHeader>
                  <CardTitle className="capitalize flex items-center justify-between">
                    {platform.replace('_', ' ')}
                    <Badge variant={key ? 'default' : 'secondary'}>
                      {key ? 'Configured' : 'Not Set'}
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    API key for {platform.replace('_', ' ')} integration
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>API Key</Label>
                    <div className="flex gap-2">
                      <Input
                        type={showApiKeys[platform] ? 'text' : 'password'}
                        placeholder="Enter your API key"
                        value={key}
                        onChange={(e) => handleApiKeyChange(platform, e.target.value)}
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleApiKeyVisibility(platform)}
                      >
                        {showApiKeys[platform] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <Button variant="outline" size="sm">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Get API Key
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => copyToClipboard(`https://api.koursia.com/webhooks/${platform}`)}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy Webhook URL
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardFooter className="flex justify-end pt-6">
              <Button 
                onClick={() => saveApiKeysMutation.mutate(apiKeys)}
                disabled={saveApiKeysMutation.isPending}
              >
                {saveApiKeysMutation.isPending ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save API Keys
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="webhooks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Webhook className="h-5 w-5" />
                Webhook Endpoints
              </CardTitle>
              <CardDescription>
                Manage webhook URLs for real-time data synchronization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {webhookEndpoints.map((webhook) => (
                  <div key={webhook.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{webhook.platform}</span>
                        <Badge variant={webhook.status === 'active' ? 'default' : 'secondary'}>
                          {webhook.status}
                        </Badge>
                      </div>
                      <code className="text-sm text-muted-foreground">{webhook.url}</code>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => testWebhookMutation.mutate(webhook.id)}
                        disabled={testWebhookMutation.isPending}
                      >
                        <Activity className="h-4 w-4 mr-2" />
                        {testWebhookMutation.isPending ? 'Testing...' : 'Test'}
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => copyToClipboard(webhook.url)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Webhook Configuration</CardTitle>
              <CardDescription>Global webhook settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-medium">Signature verification</Label>
                  <p className="text-sm text-muted-foreground">Verify webhook signatures for security</p>
                </div>
                <Switch 
                  checked={settings.webhookVerification}
                  onCheckedChange={(checked) => handleSettingChange('webhookVerification', checked)}
                />
              </div>

              <div className="space-y-2">
                <Label>Timeout duration (seconds)</Label>
                <Input
                  type="number"
                  value={settings.timeoutDuration}
                  onChange={(e) => handleSettingChange('timeoutDuration', parseInt(e.target.value))}
                  min={5}
                  max={300}
                />
              </div>

              <div className="space-y-2">
                <Label>Retry attempts</Label>
                <Input
                  type="number"
                  value={settings.retryAttempts}
                  onChange={(e) => handleSettingChange('retryAttempts', parseInt(e.target.value))}
                  min={0}
                  max={10}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Settings
              </CardTitle>
              <CardDescription>Configure security policies for integrations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-medium">Require 2FA for new connections</Label>
                  <p className="text-sm text-muted-foreground">Enforce two-factor authentication when adding new platforms</p>
                </div>
                <Switch 
                  checked={settings.requireTwoFactor}
                  onCheckedChange={(checked) => handleSettingChange('requireTwoFactor', checked)}
                />
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h4 className="font-medium">Security Audit</h4>
                <div className="grid grid-cols-2 gap-4">
                  <Button variant="outline" className="w-full">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Security Logs
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Export Audit Report
                  </Button>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Data Protection</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">API keys encrypted</span>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">HTTPS enforcement</span>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Webhook signatures</span>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                Advanced Configuration
              </CardTitle>
              <CardDescription>Advanced settings for developers and power users</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-medium">Debug mode</Label>
                  <p className="text-sm text-muted-foreground">Enable detailed logging for troubleshooting</p>
                </div>
                <Switch 
                  checked={settings.debugMode}
                  onCheckedChange={(checked) => handleSettingChange('debugMode', checked)}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Data Management</h4>
                <div className="grid grid-cols-3 gap-4">
                  <Button variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Export All Data
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Upload className="h-4 w-4 mr-2" />
                    Import Settings
                  </Button>
                  <Button variant="outline" className="w-full text-red-600 hover:text-red-700">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Reset All
                  </Button>
                </div>
              </div>

              <Separator />

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Warning:</strong> Advanced settings can affect integration performance. 
                  Only modify these if you understand the implications.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}