
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';

interface LocalSadTalkerParams {
  audioPath: string;
  imagePath: string;
  outputPath: string;
  enhanceMode?: boolean;
  preprocess?: 'crop' | 'resize' | 'full';
  stillMode?: boolean;
  useGfpgan?: boolean;
  batchSize?: number;
}

interface SadTalkerResult {
  success: boolean;
  outputPath?: string;
  error?: string;
}

export class LocalSadTalkerService {
  private sadTalkerPath: string;
  private modelsPath: string;

  constructor() {
    this.sadTalkerPath = path.join(process.cwd(), 'SadTalker');
    this.modelsPath = path.join(this.sadTalkerPath, 'checkpoints');
  }

  async isInitialized(): Promise<boolean> {
    return fs.existsSync(this.sadTalkerPath) && fs.existsSync(this.modelsPath);
  }

  async generateTalkingVideo(params: LocalSadTalkerParams): Promise<SadTalkerResult> {
    try {
      if (!await this.isInitialized()) {
        throw new Error('Sad<PERSON>alker not initialized. Please run the setup first.');
      }

      const {
        audioPath,
        imagePath,
        outputPath,
        enhanceMode = true,
        preprocess = 'crop',
        stillMode = false,
        useGfpgan = true,
        batchSize = 1
      } = params;

      // Ensure output directory exists
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      const args = [
        'inference.py',
        '--driven_audio', audioPath,
        '--source_image', imagePath,
        '--result_dir', outputDir,
        '--preprocess', preprocess,
        '--batch_size', batchSize.toString()
      ];

      if (enhanceMode) {
        args.push('--enhancer', 'gfpgan');
      }

      if (stillMode) {
        args.push('--still');
      }

      if (useGfpgan) {
        args.push('--use_GFPGAN');
      }

      return new Promise((resolve, reject) => {
        const pythonProcess = spawn('python', args, {
          cwd: this.sadTalkerPath,
          stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        pythonProcess.stdout.on('data', (data) => {
          stdout += data.toString();
          console.log('SadTalker stdout:', data.toString());
        });

        pythonProcess.stderr.on('data', (data) => {
          stderr += data.toString();
          console.log('SadTalker stderr:', data.toString());
        });

        pythonProcess.on('close', (code) => {
          if (code === 0) {
            // Find the generated video file
            const generatedFiles = fs.readdirSync(outputDir).filter(file => 
              file.endsWith('.mp4') && file.includes(path.basename(imagePath, path.extname(imagePath)))
            );

            if (generatedFiles.length > 0) {
              const finalOutputPath = path.join(outputDir, generatedFiles[0]);
              resolve({
                success: true,
                outputPath: finalOutputPath
              });
            } else {
              resolve({
                success: false,
                error: 'No output video generated'
              });
            }
          } else {
            resolve({
              success: false,
              error: `SadTalker process exited with code ${code}: ${stderr}`
            });
          }
        });

        pythonProcess.on('error', (error) => {
          reject({
            success: false,
            error: `Failed to start SadTalker process: ${error.message}`
          });
        });
      });

    } catch (error) {
      console.error('Error in generateTalkingVideo:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async downloadModels(): Promise<boolean> {
    try {
      console.log('Downloading SadTalker models...');
      
      const downloadScript = spawn('bash', ['scripts/download_models.sh'], {
        cwd: this.sadTalkerPath,
        stdio: 'inherit'
      });

      return new Promise((resolve) => {
        downloadScript.on('close', (code) => {
          resolve(code === 0);
        });

        downloadScript.on('error', () => {
          resolve(false);
        });
      });
    } catch (error) {
      console.error('Error downloading models:', error);
      return false;
    }
  }
}

export const localSadTalker = new LocalSadTalkerService();
