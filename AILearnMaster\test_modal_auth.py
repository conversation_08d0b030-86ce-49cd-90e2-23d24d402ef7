#!/usr/bin/env python3
"""
Test Modal Authentication and Credentials
Simple test to verify Modal credentials are working
"""

import os
import subprocess
import sys

def test_modal_credentials():
    """Test if Modal credentials are working"""
    print("=== Testing Modal Credentials ===\n")
    
    # Use the new credentials directly
    token_id = "wk-FSHk02pvME2Sn8HryoWdtx"
    token_secret = "ws-PmR0IZZFvYyBj3x4LsRCX6"
    
    print(f"✅ Using new Modal credentials")
    print(f"Token ID: {token_id[:10]}...")
    print(f"Token Secret: {token_secret[:10]}...")
    
    # Test modal auth
    try:
        print("\n🔑 Setting up Modal authentication...")
        
        # Try to set token first
        set_result = subprocess.run([
            'modal', 'token', 'set',
            '--token-id', token_id,
            '--token-secret', token_secret
        ], capture_output=True, text=True, timeout=30)
        
        if set_result.returncode == 0:
            print("✅ Modal token set successfully")
            print(f"Output: {set_result.stdout.strip()}")
            
            # Verify the token is working
            verify_result = subprocess.run(['modal', 'token', 'list'], 
                                         capture_output=True, text=True, timeout=30)
            
            if verify_result.returncode == 0:
                print("✅ Modal token verification successful")
                return True
            else:
                print("❌ Modal token verification failed")
                print(f"Error: {verify_result.stderr.strip()}")
                return False
        else:
            print("❌ Failed to set Modal token")
            print(f"Error: {set_result.stderr.strip()}")
            return False
                
    except subprocess.TimeoutExpired:
        print("❌ Modal command timed out")
        return False
    except FileNotFoundError:
        print("❌ Modal CLI not found")
        return False
    except Exception as e:
        print(f"❌ Error testing Modal: {e}")
        return False

def test_modal_simple_deploy():
    """Test simple Modal deployment"""
    print("\n=== Testing Simple Modal Deployment ===\n")
    
    try:
        # Create minimal test app
        test_app = """
import modal

app = modal.App("test-credentials")

@app.function()
def hello():
    return "Hello from Modal!"

if __name__ == "__main__":
    with app.run():
        print(hello.remote())
"""
        
        with open('test_modal_app.py', 'w') as f:
            f.write(test_app)
        
        print("📝 Created test Modal app")
        
        # Try to run the test app
        result = subprocess.run(['modal', 'run', 'test_modal_app.py'], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Modal test app ran successfully")
            print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print("❌ Modal test app failed")
            print(f"Error: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Modal deployment: {e}")
        return False

def main():
    """Main test function"""
    print("Modal Credentials and Authentication Test\n")
    
    # Test credentials
    creds_ok = test_modal_credentials()
    
    if creds_ok:
        # Test simple deployment
        deploy_ok = test_modal_simple_deploy()
        
        if deploy_ok:
            print("\n🎉 All Modal tests passed!")
            print("Your Modal credentials are working correctly.")
            return True
    
    print("\n❌ Modal tests failed")
    print("Please check your Modal credentials and try again.")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)