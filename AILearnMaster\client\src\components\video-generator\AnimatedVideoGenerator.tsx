import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Loader2, Upload, ImageIcon, FileVideo, Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { apiRequest } from "@/lib/queryClient";
import { Progress } from "@/components/ui/progress";

const videoFormSchema = z.object({
  title: z.string()
    .min(3, { message: "Title must be at least 3 characters long" })
    .max(100, { message: "Title must be less than 100 characters" }),
  script: z.string()
    .min(20, { message: "Script must be at least 20 characters long" })
    .max(5000, { message: "Script must be less than 5000 characters" }),
  useAIVoiceover: z.boolean().default(true),
  includeSubtitles: z.boolean().default(true),
  imageSource: z.enum(["upload", "library", "ai"]),
});

type VideoFormValues = z.infer<typeof videoFormSchema>;

const AnimatedVideoGenerator: React.FC = () => {
  const { toast } = useToast();
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedVideo, setGeneratedVideo] = useState<{
    url: string;
    title: string;
  } | null>(null);
  const [progress, setProgress] = useState(0);

  const form = useForm<VideoFormValues>({
    resolver: zodResolver(videoFormSchema),
    defaultValues: {
      title: "",
      script: "",
      useAIVoiceover: true,
      includeSubtitles: true,
      imageSource: "ai",
    },
  });

  const imageSource = form.watch("imageSource");
  const includeSubtitles = form.watch("includeSubtitles");
  const useAIVoiceover = form.watch("useAIVoiceover");

  const onSubmit = async (data: VideoFormValues) => {
    try {
      setIsGenerating(true);
      setProgress(10);
      
      // Upload any images first if the user selected the upload option
      let imageIds: number[] = [];
      
      if (data.imageSource === "upload" && uploadedFiles.length > 0) {
        const formData = new FormData();
        uploadedFiles.forEach(file => {
          formData.append('images', file);
        });
        
        setProgress(30);
        const uploadRes = await fetch('/api/video-generator/upload', {
          method: 'POST',
          body: formData,
        });
        
        if (!uploadRes.ok) {
          throw new Error("Failed to upload images");
        }
        
        const uploadData = await uploadRes.json();
        imageIds = uploadData.media
          .filter((media: any) => media.type === 'image')
          .map((media: any) => media.id);
      }
      
      setProgress(50);
      
      // Generate the video
      const videoRes = await apiRequest('POST', '/api/video-generator/standalone-video', {
        title: data.title,
        script: data.script,
        useAIVoiceover: data.useAIVoiceover,
        includeSubtitles: data.includeSubtitles,
        imageIds: imageIds.length > 0 ? imageIds : undefined,
      });
      
      if (!videoRes.ok) {
        const errorData = await videoRes.json();
        throw new Error(errorData.message || "Failed to generate video");
      }
      
      setProgress(100);
      const videoData = await videoRes.json();
      
      setGeneratedVideo({
        url: videoData.video.url,
        title: videoData.video.title,
      });
      
      toast({
        title: "Video generated successfully!",
        description: `Used ${videoData.creditsUsed} AI credits`,
      });
    } catch (error) {
      console.error("Error generating video:", error);
      toast({
        title: "Error generating video",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const fileList = Array.from(e.target.files);
      const imageFiles = fileList.filter(file => file.type.startsWith('image/'));
      setUploadedFiles(prev => [...prev, ...imageFiles]);
    }
  };

  const removeFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl">Create Animated Video</CardTitle>
          <CardDescription>
            Generate professional-looking videos with AI-powered narration, images, and animations.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {generatedVideo ? (
            <div className="space-y-6">
              <div className="flex items-center justify-center bg-muted rounded-lg p-2">
                <Check className="h-10 w-10 text-green-500" />
                <h3 className="ml-2 text-xl font-semibold">Video Generated Successfully!</h3>
              </div>
              
              <div className="aspect-video w-full bg-black rounded-lg overflow-hidden">
                <video 
                  controls 
                  className="w-full h-full" 
                  src={generatedVideo.url}
                  poster="/uploads/images/placeholder-image.jpg"
                >
                  Your browser does not support the video tag.
                </video>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <Button onClick={() => setGeneratedVideo(null)}>
                  Create Another Video
                </Button>
                <Button variant="outline" onClick={() => window.open(generatedVideo.url, '_blank')}>
                  Download Video
                </Button>
              </div>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Video Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter a title for your video" {...field} />
                      </FormControl>
                      <FormDescription>
                        This will be used as the title of your generated video.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="script"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Video Script</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Enter the script for your video..." 
                          className="min-h-[200px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        This text will be used for narration and subtitles.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="imageSource"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel>Image Source</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-1"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="ai" id="ai" />
                            <Label htmlFor="ai">Use AI-generated images</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="upload" id="upload" />
                            <Label htmlFor="upload">Upload my own images</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="library" id="library" />
                            <Label htmlFor="library">Choose from media library</Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                {imageSource === "upload" && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-center w-full">
                      <label
                        htmlFor="fileUpload"
                        className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted"
                      >
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <Upload className="w-8 h-8 mb-2 text-gray-500" />
                          <p className="mb-2 text-sm text-gray-500">
                            <span className="font-semibold">Click to upload</span> or drag and drop
                          </p>
                          <p className="text-xs text-gray-500">
                            Images only (max 5MB each)
                          </p>
                        </div>
                        <input
                          id="fileUpload"
                          type="file"
                          accept="image/*"
                          className="hidden"
                          multiple
                          onChange={handleFileChange}
                        />
                      </label>
                    </div>
                    
                    {uploadedFiles.length > 0 && (
                      <div className="grid grid-cols-4 gap-4 mt-4">
                        {uploadedFiles.map((file, index) => (
                          <div key={index} className="relative group">
                            <div className="aspect-square bg-muted rounded-lg overflow-hidden flex items-center justify-center">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`Uploaded ${index + 1}`}
                                className="object-cover w-full h-full"
                              />
                            </div>
                            <button
                              type="button"
                              onClick={() => removeFile(index)}
                              className="absolute -top-2 -right-2 bg-destructive text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                            <p className="text-xs truncate mt-1">{file.name}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
                
                {imageSource === "library" && (
                  <div className="p-8 border rounded-lg flex flex-col items-center justify-center">
                    <ImageIcon className="h-12 w-12 text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Media library selection will be available in the next version
                    </p>
                  </div>
                )}
                
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="useAIVoiceover"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            AI Voiceover
                          </FormLabel>
                          <FormDescription>
                            Generate professional narration
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="includeSubtitles"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Subtitles
                          </FormLabel>
                          <FormDescription>
                            Add text captions to the video
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-sm">AI Credit Cost Estimate</h4>
                      <p className="text-xs text-muted-foreground">
                        Based on your selections
                      </p>
                    </div>
                    <div className="text-right">
                      <span className="font-semibold">200 credits</span>
                      <p className="text-xs text-muted-foreground">
                        Video generation: 200 credits
                      </p>
                    </div>
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={isGenerating}
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating Video...
                      </>
                    ) : (
                      <>
                        <FileVideo className="mr-2 h-4 w-4" />
                        Generate Video
                      </>
                    )}
                  </Button>
                  
                  {isGenerating && (
                    <div className="mt-2">
                      <Progress value={progress} className="h-2" />
                      <p className="text-xs text-center mt-1 text-muted-foreground">
                        {progress < 30 ? "Preparing assets..." : 
                         progress < 50 ? "Processing images..." : 
                         progress < 80 ? "Generating video..." : 
                         "Finalizing..."}
                      </p>
                    </div>
                  )}
                </div>
              </form>
            </Form>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AnimatedVideoGenerator;