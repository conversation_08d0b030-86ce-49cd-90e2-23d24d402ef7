#!/bin/bash

# Production Monitoring Setup Script
# Configures CloudWatch, alarms, and monitoring for AILearnMaster

set -e

echo "📊 Setting up Production Monitoring"
echo "=================================="

# Configuration
REGION="us-east-1"
APP_NAME="ailearn-master-prod"
DOMAIN="ailearn.com"
API_DOMAIN="api.ailearn.com"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to create CloudWatch log groups
create_log_groups() {
    print_status "Creating CloudWatch log groups..."
    
    local log_groups=(
        "/aws/amplify/$APP_NAME"
        "/aws/lambda/ailearn-master"
        "/ailearn/application"
        "/ailearn/security"
        "/ailearn/performance"
    )
    
    for log_group in "${log_groups[@]}"; do
        if aws logs create-log-group --log-group-name "$log_group" --region "$REGION" 2>/dev/null; then
            print_success "Created log group: $log_group"
        else
            print_warning "Log group already exists or failed to create: $log_group"
        fi
        
        # Set retention policy
        aws logs put-retention-policy \
            --log-group-name "$log_group" \
            --retention-in-days 30 \
            --region "$REGION" 2>/dev/null || true
    done
}

# Function to create CloudWatch alarms
create_cloudwatch_alarms() {
    print_status "Creating CloudWatch alarms..."
    
    # Get Amplify App ID
    local app_id=$(aws amplify list-apps --query "apps[?name=='$APP_NAME'].appId" --output text)
    
    if [ -z "$app_id" ] || [ "$app_id" = "None" ]; then
        print_error "Amplify app not found"
        return 1
    fi
    
    # High error rate alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "AILearnMaster-HighErrorRate" \
        --alarm-description "High error rate detected" \
        --metric-name "4XXError" \
        --namespace "AWS/CloudFront" \
        --statistic "Sum" \
        --period 300 \
        --threshold 10 \
        --comparison-operator "GreaterThanThreshold" \
        --evaluation-periods 2 \
        --alarm-actions "arn:aws:sns:$REGION:$(aws sts get-caller-identity --query Account --output text):ailearn-alerts" \
        --region "$REGION" 2>/dev/null || print_warning "Failed to create error rate alarm"
    
    # High latency alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "AILearnMaster-HighLatency" \
        --alarm-description "High response latency detected" \
        --metric-name "OriginLatency" \
        --namespace "AWS/CloudFront" \
        --statistic "Average" \
        --period 300 \
        --threshold 5000 \
        --comparison-operator "GreaterThanThreshold" \
        --evaluation-periods 2 \
        --alarm-actions "arn:aws:sns:$REGION:$(aws sts get-caller-identity --query Account --output text):ailearn-alerts" \
        --region "$REGION" 2>/dev/null || print_warning "Failed to create latency alarm"
    
    # Database connection alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "AILearnMaster-DatabaseConnections" \
        --alarm-description "High database connection count" \
        --metric-name "DatabaseConnections" \
        --namespace "AWS/RDS" \
        --statistic "Average" \
        --period 300 \
        --threshold 80 \
        --comparison-operator "GreaterThanThreshold" \
        --evaluation-periods 2 \
        --alarm-actions "arn:aws:sns:$REGION:$(aws sts get-caller-identity --query Account --output text):ailearn-alerts" \
        --region "$REGION" 2>/dev/null || print_warning "Failed to create database alarm"
    
    print_success "CloudWatch alarms created"
}

# Function to create SNS topic for alerts
create_sns_alerts() {
    print_status "Creating SNS topic for alerts..."
    
    # Create SNS topic
    local topic_arn=$(aws sns create-topic \
        --name "ailearn-alerts" \
        --region "$REGION" \
        --query 'TopicArn' --output text 2>/dev/null)
    
    if [ -n "$topic_arn" ]; then
        print_success "Created SNS topic: $topic_arn"
        
        # Add email subscription (interactive)
        read -p "Enter email address for alerts: " email_address
        if [ -n "$email_address" ]; then
            aws sns subscribe \
                --topic-arn "$topic_arn" \
                --protocol email \
                --notification-endpoint "$email_address" \
                --region "$REGION" >/dev/null 2>&1
            
            print_success "Email subscription added: $email_address"
            print_warning "Please check your email and confirm the subscription"
        fi
    else
        print_warning "Failed to create SNS topic"
    fi
}

# Function to create custom metrics
create_custom_metrics() {
    print_status "Setting up custom metrics..."
    
    # Create custom metric for security events
    cat > /tmp/security_metric.json << 'EOF'
{
    "MetricData": [
        {
            "MetricName": "SecurityEvents",
            "Dimensions": [
                {
                    "Name": "Application",
                    "Value": "AILearnMaster"
                }
            ],
            "Value": 0,
            "Unit": "Count"
        }
    ],
    "Namespace": "AILearnMaster/Security"
}
EOF
    
    aws cloudwatch put-metric-data \
        --cli-input-json file:///tmp/security_metric.json \
        --region "$REGION" 2>/dev/null || print_warning "Failed to create security metric"
    
    # Create custom metric for course generation
    cat > /tmp/course_metric.json << 'EOF'
{
    "MetricData": [
        {
            "MetricName": "CourseGenerations",
            "Dimensions": [
                {
                    "Name": "Application",
                    "Value": "AILearnMaster"
                }
            ],
            "Value": 0,
            "Unit": "Count"
        }
    ],
    "Namespace": "AILearnMaster/Business"
}
EOF
    
    aws cloudwatch put-metric-data \
        --cli-input-json file:///tmp/course_metric.json \
        --region "$REGION" 2>/dev/null || print_warning "Failed to create course metric"
    
    # Clean up
    rm -f /tmp/security_metric.json /tmp/course_metric.json
    
    print_success "Custom metrics configured"
}

# Function to create CloudWatch dashboard
create_dashboard() {
    print_status "Creating CloudWatch dashboard..."
    
    # Create dashboard configuration
    cat > /tmp/dashboard.json << EOF
{
    "widgets": [
        {
            "type": "metric",
            "x": 0,
            "y": 0,
            "width": 12,
            "height": 6,
            "properties": {
                "metrics": [
                    [ "AWS/CloudFront", "Requests", "DistributionId", "YOUR_DISTRIBUTION_ID" ],
                    [ ".", "BytesDownloaded", ".", "." ]
                ],
                "period": 300,
                "stat": "Sum",
                "region": "$REGION",
                "title": "CloudFront Traffic"
            }
        },
        {
            "type": "metric",
            "x": 12,
            "y": 0,
            "width": 12,
            "height": 6,
            "properties": {
                "metrics": [
                    [ "AWS/CloudFront", "4xxErrorRate", "DistributionId", "YOUR_DISTRIBUTION_ID" ],
                    [ ".", "5xxErrorRate", ".", "." ]
                ],
                "period": 300,
                "stat": "Average",
                "region": "$REGION",
                "title": "Error Rates"
            }
        },
        {
            "type": "metric",
            "x": 0,
            "y": 6,
            "width": 12,
            "height": 6,
            "properties": {
                "metrics": [
                    [ "AILearnMaster/Security", "SecurityEvents", "Application", "AILearnMaster" ]
                ],
                "period": 300,
                "stat": "Sum",
                "region": "$REGION",
                "title": "Security Events"
            }
        },
        {
            "type": "metric",
            "x": 12,
            "y": 6,
            "width": 12,
            "height": 6,
            "properties": {
                "metrics": [
                    [ "AILearnMaster/Business", "CourseGenerations", "Application", "AILearnMaster" ]
                ],
                "period": 300,
                "stat": "Sum",
                "region": "$REGION",
                "title": "Course Generations"
            }
        },
        {
            "type": "log",
            "x": 0,
            "y": 12,
            "width": 24,
            "height": 6,
            "properties": {
                "query": "SOURCE '/aws/amplify/$APP_NAME' | fields @timestamp, @message\n| filter @message like /ERROR/\n| sort @timestamp desc\n| limit 20",
                "region": "$REGION",
                "title": "Recent Errors"
            }
        }
    ]
}
EOF
    
    # Create dashboard
    aws cloudwatch put-dashboard \
        --dashboard-name "AILearnMaster-Production" \
        --dashboard-body file:///tmp/dashboard.json \
        --region "$REGION" 2>/dev/null || print_warning "Failed to create dashboard"
    
    rm -f /tmp/dashboard.json
    print_success "CloudWatch dashboard created"
}

# Function to setup health checks
setup_health_checks() {
    print_status "Setting up health checks..."
    
    # Create health check script
    cat > /tmp/health_check.sh << 'EOF'
#!/bin/bash

# Health check script for AILearnMaster
DOMAIN="ailearn.com"
API_DOMAIN="api.ailearn.com"

check_endpoint() {
    local url=$1
    local name=$2
    
    if curl -f -s "$url" >/dev/null; then
        echo "✅ $name is healthy"
        return 0
    else
        echo "❌ $name is unhealthy"
        return 1
    fi
}

# Check frontend
check_endpoint "https://$DOMAIN" "Frontend"

# Check API
check_endpoint "https://$API_DOMAIN/api/health" "API"

# Check database
check_endpoint "https://$API_DOMAIN/api/db/health" "Database"

echo "Health check completed at $(date)"
EOF
    
    chmod +x /tmp/health_check.sh
    
    # Create CloudWatch event rule for health checks
    aws events put-rule \
        --name "ailearn-health-check" \
        --schedule-expression "rate(5 minutes)" \
        --description "Health check for AILearnMaster" \
        --region "$REGION" 2>/dev/null || print_warning "Failed to create health check rule"
    
    print_success "Health checks configured"
}

# Function to create monitoring documentation
create_monitoring_docs() {
    print_status "Creating monitoring documentation..."
    
    cat > monitoring-guide.md << EOF
# AILearnMaster Production Monitoring Guide

## CloudWatch Resources

### Log Groups
- \`/aws/amplify/$APP_NAME\` - Application logs
- \`/ailearn/security\` - Security events
- \`/ailearn/performance\` - Performance metrics

### Alarms
- **AILearnMaster-HighErrorRate** - Triggers when error rate > 10 per 5 minutes
- **AILearnMaster-HighLatency** - Triggers when latency > 5 seconds
- **AILearnMaster-DatabaseConnections** - Triggers when DB connections > 80

### Dashboard
- **AILearnMaster-Production** - Main monitoring dashboard

## Key Metrics to Monitor

### Application Health
- Response times
- Error rates (4xx, 5xx)
- Request volume
- Database connections

### Security Metrics
- Failed authentication attempts
- Rate limiting triggers
- Suspicious activity
- SSL certificate expiry

### Business Metrics
- Course generations per hour
- User registrations
- File uploads
- API usage patterns

## Alert Thresholds

### Critical (Immediate Response)
- Error rate > 5%
- Response time > 10 seconds
- Database unavailable
- SSL certificate expires in < 7 days

### Warning (Monitor Closely)
- Error rate > 1%
- Response time > 5 seconds
- High database connections
- Unusual traffic patterns

## Monitoring URLs

- CloudWatch Console: https://console.aws.amazon.com/cloudwatch/
- Application: https://$DOMAIN
- API Health: https://$API_DOMAIN/api/health
- Database Health: https://$API_DOMAIN/api/db/health

## Troubleshooting

### High Error Rates
1. Check application logs in CloudWatch
2. Verify database connectivity
3. Check Modal AI service status
4. Review recent deployments

### High Latency
1. Check CloudFront cache hit ratio
2. Monitor database query performance
3. Review Modal AI processing times
4. Check S3 response times

### Security Alerts
1. Review security logs
2. Check for unusual IP addresses
3. Verify rate limiting is working
4. Monitor authentication failures

## Maintenance Tasks

### Daily
- Review error logs
- Check security alerts
- Monitor performance metrics

### Weekly
- Review capacity utilization
- Check backup status
- Update security configurations

### Monthly
- Security audit
- Performance optimization
- Cost analysis
- Disaster recovery testing
EOF
    
    print_success "Monitoring documentation created: monitoring-guide.md"
}

# Main monitoring setup function
main() {
    print_status "Starting production monitoring setup..."
    
    # Check AWS CLI
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS CLI not configured"
        exit 1
    fi
    
    # Create monitoring resources
    create_log_groups
    create_sns_alerts
    create_cloudwatch_alarms
    create_custom_metrics
    create_dashboard
    setup_health_checks
    create_monitoring_docs
    
    echo ""
    print_success "🎉 Production monitoring setup completed!"
    echo "======================================"
    echo ""
    echo "Monitoring Resources Created:"
    echo "• CloudWatch log groups for application logs"
    echo "• CloudWatch alarms for error rates and latency"
    echo "• SNS topic for alert notifications"
    echo "• Custom metrics for security and business events"
    echo "• CloudWatch dashboard for visualization"
    echo "• Health check automation"
    echo ""
    echo "Next Steps:"
    echo "1. Confirm SNS email subscription"
    echo "2. Review CloudWatch dashboard"
    echo "3. Test alert notifications"
    echo "4. Set up additional custom metrics as needed"
    echo "5. Review monitoring-guide.md for operational procedures"
    echo ""
    echo "CloudWatch Dashboard: https://console.aws.amazon.com/cloudwatch/home?region=$REGION#dashboards:name=AILearnMaster-Production"
}

# Run main function
main "$@"
