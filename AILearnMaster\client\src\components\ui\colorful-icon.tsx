import React from 'react';
import type { LucideProps } from 'lucide-react';
import { cn } from '@/lib/utils';

// Icon category types
export type IconCategory = 
  | 'dashboard'
  | 'content'
  | 'analytics'
  | 'settings'
  | 'media'
  | 'users'
  | 'courses'
  | 'ai'
  | 'calendar'
  | 'chat'
  | 'warning'
  | 'success'
  | 'info'
  | 'collaboration'
  | 'marketing'
  | 'default';

// Get appropriate color based on category
const getCategoryClassName = (category: IconCategory) => {
  switch (category) {
    case 'dashboard': return 'text-blue-500';
    case 'content': return 'text-indigo-500';
    case 'analytics': return 'text-orange-500';
    case 'settings': return 'text-slate-500';
    case 'media': return 'text-fuchsia-500';
    case 'users': return 'text-teal-500';
    case 'courses': return 'text-green-500';
    case 'ai': return 'text-rose-500';
    case 'calendar': return 'text-amber-500';
    case 'chat': return 'text-emerald-500';
    case 'warning': return 'text-red-500';
    case 'success': return 'text-green-500';
    case 'info': return 'text-cyan-500';
    case 'collaboration': return 'text-purple-500';
    case 'marketing': return 'text-pink-500';
    default: return 'text-gray-500';
  }
};

interface ColorfulIconProps {
  icon: React.FC<LucideProps>;
  category?: IconCategory;
  className?: string;
  size?: number;
  strokeWidth?: number;
  onClick?: () => void;
}

export function ColorfulIcon({
  icon: Icon,
  category = 'default',
  className,
  size = 24,
  strokeWidth = 2,
  onClick
}: ColorfulIconProps) {
  // Apply category-specific styling
  const categoryClass = getCategoryClassName(category);
  
  return (
    <Icon 
      className={cn('icon-colorful', categoryClass, className)}
      size={size} 
      strokeWidth={strokeWidth}
      onClick={onClick}
    />
  );
}