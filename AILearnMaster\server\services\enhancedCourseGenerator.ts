import OpenAI from "openai";
import * as geminiService from "./gemini";
import { CourseStructure } from "./openAIPrimaryService";

// Initialize OpenAI client
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

/**
 * Enhanced course structure generation with learning progression focus
 * Creates a pedagogically sound course structure with logical flow between modules
 */
export async function generateEnhancedCourseStructure(courseData: {
  title: string;
  description: string;
  category: string;
  targetAudience?: string;
  keyTopics?: string;
  contentNotes?: string;
  moduleCount?: number;
}): Promise<CourseStructure> {
  try {
    console.log("Generating enhanced course structure with advanced learning progression");
    
    // Craft a detailed instructional design prompt
    const prompt = `
    You are an expert instructional designer tasked with creating a comprehensive, pedagogically sound course structure for:
    
    Course Title: "${courseData.title}"
    Category: "${courseData.category}"
    ${courseData.description ? `Description: "${courseData.description}"` : ''}
    ${courseData.targetAudience ? `Target Audience: ${courseData.targetAudience}` : ''}
    ${courseData.keyTopics ? `Key Topics: ${courseData.keyTopics}` : ''}
    ${courseData.contentNotes ? `Additional Notes: ${courseData.contentNotes}` : ''}
    
    Create a course structure with exactly ${courseData.moduleCount || 4} modules that follows advanced learning design principles:

    1. Ensure clear KNOWLEDGE PROGRESSION where each module builds directly on concepts from previous modules
    2. Start with foundational concepts and systematically advance to complex applications
    3. Each module should have a distinct PURPOSE within the overall learning journey
    4. Create explicit CONNECTIONS between modules (e.g., "Building on the foundations from Module 1...")
    5. Balance theoretical understanding with practical skill development
    6. Include knowledge checkpoints to reinforce learning between modules
    7. Each lesson within a module should follow a micro-progression that supports the module's objectives
    
    For each module:
    - Create a focused learning objective that builds toward course mastery
    - Include 3-5 lessons that follow a logical skill-building sequence
    - Ensure each lesson title clearly communicates its specific contribution to the module
    - Write descriptions that explicitly reference how the content connects to previous learning
    
    Return your response as a JSON object with this exact structure:
    {
      "modules": [
        {
          "title": "Module title that shows its place in the learning sequence",
          "description": "Description that explains how this module builds on previous concepts and sets up future modules",
          "lessons": [
            {
              "title": "Lesson title showing its role in skill progression",
              "description": "Description that connects to module objectives and previous lessons"
            }
          ]
        }
      ]
    }`;

    // The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: "You are an expert learning designer specializing in creating logical, progressive educational structures. You excel at creating courses where each module builds on previous knowledge in a clear learning pathway."
        },
        { role: "user", content: prompt }
      ],
      response_format: { type: "json_object" },
      temperature: 0.7,
    });

    // Parse the JSON response
    const content = response.choices[0].message.content || '{"modules":[]}';
    const structureData = JSON.parse(content);
    
    // Format as CourseStructure
    const result = {
      title: courseData.title,
      description: courseData.description || "",
      modules: structureData.modules || []
    };

    console.log("Successfully generated enhanced course structure with learning progression");
    return result;
    
  } catch (error) {
    console.error("Enhanced course generation error:", error);
    
    try {
      // If our enhanced generation fails, fall back to the standard OpenAI generation
      console.log("Falling back to standard course structure generation");
      
      // Use Gemini as ultimate fallback if OpenAI fails
      const fallbackStructure = await geminiService.generateCourseStructure(courseData);
      return fallbackStructure;
    } catch (fallbackError) {
      console.error("All course generation methods failed:", fallbackError);
      
      // Return a basic structure if both generation methods fail
      return {
        title: courseData.title,
        description: courseData.description || "",
        modules: [
          {
            title: "Introduction to " + courseData.title,
            description: "Get started with the foundational concepts of " + courseData.title,
            lessons: [
              {
                title: "Course Overview",
                description: "A comprehensive introduction to the course topics and learning path"
              },
              {
                title: "Key Concepts",
                description: "Essential foundational concepts that will be built upon throughout the course"
              }
            ]
          }
        ]
      };
    }
  }
}