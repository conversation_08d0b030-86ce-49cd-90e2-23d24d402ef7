import React, { useState, useRef } from 'react';
import { Card, CardContent, TextField, Button, Grid, Typography, CircularProgress, Slider, FormControl, InputLabel, Select, MenuItem, Chip } from '@mui/material';
import { RecordVoiceOver, Download, Add, Clear } from '@mui/icons-material';
import apiService from '../services/apiService';

const TTSCoqui = () => {
  const [singleText, setSingleText] = useState('');
  const [batchTexts, setBatchTexts] = useState(['']);
  const [formData, setFormData] = useState({
    voiceModel: 'tts_models/en/vctk/vits',
    speaker: 'p225',
    emotion: 'neutral',
    speed: 1.0
  });
  
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');
  const [mode, setMode] = useState('single');
  const audioRefs = useRef({});

  const voiceModels = [
    { value: 'tts_models/en/vctk/vits', label: 'VCTK English (Multi-speaker)' },
    { value: 'tts_models/en/ljspeech/tacotron2-DDC', label: 'LJSpeech Tacotron2' },
    { value: 'tts_models/en/ljspeech/glow-tts', label: 'LJSpeech Glow-TTS' },
    { value: 'tts_models/en/ljspeech/speedy-speech', label: 'LJSpeech Speedy Speech' },
    { value: 'tts_models/en/sam/tacotron-DDC', label: 'SAM Tacotron' }
  ];

  const vctk_speakers = [
    { value: 'p225', label: 'p225 (Female, British)' },
    { value: 'p226', label: 'p226 (Male, British)' },
    { value: 'p227', label: 'p227 (Male, British)' },
    { value: 'p228', label: 'p228 (Female, British)' },
    { value: 'p229', label: 'p229 (Female, British)' },
    { value: 'p230', label: 'p230 (Female, British)' },
    { value: 'p231', label: 'p231 (Female, British)' },
    { value: 'p232', label: 'p232 (Male, British)' },
    { value: 'p233', label: 'p233 (Female, British)' },
    { value: 'p234', label: 'p234 (Female, British)' }
  ];

  const emotions = [
    { value: 'neutral', label: 'Neutral' },
    { value: 'happy', label: 'Happy' },
    { value: 'sad', label: 'Sad' },
    { value: 'angry', label: 'Angry' },
    { value: 'surprised', label: 'Surprised' },
    { value: 'fearful', label: 'Fearful' }
  ];

  const courseTexts = [
    "Today we will explore advanced machine learning algorithms and their practical applications.",
    "Neural networks form the backbone of modern artificial intelligence systems.",
    "Data visualization helps us understand complex patterns and relationships in datasets.",
    "Feature engineering is crucial for improving model performance and accuracy.",
    "Cross-validation ensures our models generalize well to unseen data."
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addBatchText = () => {
    setBatchTexts(prev => [...prev, '']);
  };

  const removeBatchText = (index) => {
    setBatchTexts(prev => prev.filter((_, i) => i !== index));
  };

  const updateBatchText = (index, value) => {
    setBatchTexts(prev => prev.map((text, i) => i === index ? value : text));
  };

  const loadCourseTexts = () => {
    setBatchTexts(courseTexts);
    setMode('batch');
  };

  const handleGenerate = async () => {
    const textsToProcess = mode === 'single' ? [singleText] : batchTexts.filter(text => text.trim());
    
    if (textsToProcess.length === 0 || textsToProcess.every(text => !text.trim())) {
      setError('Please enter text to convert to speech');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const requestData = {
        text: textsToProcess[0],
        batchTexts: mode === 'batch' ? textsToProcess : null,
        ...formData
      };

      const cacheKey = apiService.generateCacheKey(requestData);
      const cached = apiService.getCachedResult(cacheKey);
      
      if (cached) {
        setResults(cached.results.map((result, index) => ({ 
          ...result, 
          id: Date.now() + index,
          cached: true 
        })));
        setLoading(false);
        return;
      }

      const result = await apiService.generateCoquiTTS(requestData);
      
      if (result.status === 'success') {
        const newResults = result.results.map((res, index) => ({
          ...res,
          id: Date.now() + index,
          cached: res.cached || false
        }));
        setResults(newResults);
        
        if (!result.cached) {
          apiService.setCachedResult(cacheKey, result);
        }
      } else {
        setError(result.error || 'Coqui TTS generation failed');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = (audioBase64, index, text) => {
    const filename = `coqui-tts-${Date.now()}-${index}.wav`;
    apiService.downloadBase64File(audioBase64, filename, 'audio/wav');
  };

  const clearAll = () => {
    setSingleText('');
    setBatchTexts(['']);
    setResults([]);
    setError('');
  };

  const isVCTKModel = formData.voiceModel.includes('vctk');

  return (
    <div className="tts-coqui">
      <Typography variant="h4" gutterBottom>
        <RecordVoiceOver /> Coqui TTS Generator
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Text Input</Typography>
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Mode</InputLabel>
                <Select
                  value={mode}
                  onChange={(e) => setMode(e.target.value)}
                >
                  <MenuItem value="single">Single Text</MenuItem>
                  <MenuItem value="batch">Batch Processing</MenuItem>
                </Select>
              </FormControl>

              {mode === 'single' ? (
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Text to Speech"
                  value={singleText}
                  onChange={(e) => setSingleText(e.target.value)}
                  margin="normal"
                  placeholder="Enter the text for high-quality voice synthesis..."
                />
              ) : (
                <div>
                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                    Batch Texts:
                  </Typography>
                  {batchTexts.map((text, index) => (
                    <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                      <TextField
                        fullWidth
                        multiline
                        rows={2}
                        label={`Text ${index + 1}`}
                        value={text}
                        onChange={(e) => updateBatchText(index, e.target.value)}
                        size="small"
                      />
                      {batchTexts.length > 1 && (
                        <Button
                          onClick={() => removeBatchText(index)}
                          size="small"
                          sx={{ ml: 1 }}
                        >
                          <Clear />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    startIcon={<Add />}
                    onClick={addBatchText}
                    size="small"
                    sx={{ mr: 1 }}
                  >
                    Add Text
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={loadCourseTexts}
                    size="small"
                  >
                    Load Course Examples
                  </Button>
                </div>
              )}

              <Button
                variant="outlined"
                onClick={clearAll}
                fullWidth
                sx={{ mt: 2 }}
              >
                Clear All
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Voice Configuration</Typography>
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Voice Model</InputLabel>
                <Select
                  value={formData.voiceModel}
                  onChange={(e) => handleInputChange('voiceModel', e.target.value)}
                >
                  {voiceModels.map((model) => (
                    <MenuItem key={model.value} value={model.value}>
                      {model.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {isVCTKModel && (
                <FormControl fullWidth margin="normal">
                  <InputLabel>Speaker</InputLabel>
                  <Select
                    value={formData.speaker}
                    onChange={(e) => handleInputChange('speaker', e.target.value)}
                  >
                    {vctk_speakers.map((speaker) => (
                      <MenuItem key={speaker.value} value={speaker.value}>
                        {speaker.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}

              <FormControl fullWidth margin="normal">
                <InputLabel>Emotion</InputLabel>
                <Select
                  value={formData.emotion}
                  onChange={(e) => handleInputChange('emotion', e.target.value)}
                >
                  {emotions.map((emotion) => (
                    <MenuItem key={emotion.value} value={emotion.value}>
                      {emotion.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Typography gutterBottom sx={{ mt: 2 }}>
                Speech Speed: {formData.speed}
              </Typography>
              <Slider
                value={formData.speed}
                onChange={(e, value) => handleInputChange('speed', value)}
                min={0.5}
                max={2.0}
                step={0.1}
                marks
                valueLabelDisplay="auto"
              />

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleGenerate}
                disabled={loading}
                sx={{ mt: 3 }}
              >
                {loading ? <CircularProgress size={24} /> : `Generate ${mode === 'batch' ? 'Batch ' : ''}Coqui TTS`}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {error && (
          <Grid item xs={12}>
            <Card sx={{ backgroundColor: '#ffebee' }}>
              <CardContent>
                <Typography color="error">{error}</Typography>
              </CardContent>
            </Card>
          </Grid>
        )}

        {results.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Generated Voice Synthesis {loading && '(Processing...)'}
                </Typography>
                <Grid container spacing={2}>
                  {results.map((result, index) => (
                    <Grid item xs={12} sm={6} md={4} key={result.id}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="subtitle2" gutterBottom>
                            Audio {result.index + 1}
                            {result.cached && <Chip label="Cached" size="small" sx={{ ml: 1 }} />}
                          </Typography>
                          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                            {result.text.substring(0, 50)}...
                          </Typography>
                          
                          <audio
                            controls
                            style={{ width: '100%', marginBottom: '8px' }}
                            src={`data:audio/wav;base64,${result.audio_base64}`}
                          />
                          
                          <Button
                            fullWidth
                            size="small"
                            startIcon={<Download />}
                            onClick={() => handleDownload(result.audio_base64, index, result.text)}
                            variant="outlined"
                          >
                            Download WAV
                          </Button>
                          
                          <Typography variant="caption" display="block" sx={{ mt: 1, textAlign: 'center' }}>
                            Speaker: {formData.speaker} | Speed: {formData.speed}x
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </div>
  );
};

export default TTSCoqui;