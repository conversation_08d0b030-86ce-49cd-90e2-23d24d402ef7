#!/bin/bash

# Complete Production Deployment Orchestration Script
# Deploys AILearnMaster to AWS with full validation

set -e

echo "🚀 AILearnMaster Production Deployment"
echo "====================================="

# Configuration
DOMAIN="ailearn.com"
API_DOMAIN="api.ailearn.com"
REGION="us-east-1"
APP_NAME="ailearn-master-prod"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${MAGENTA}$1${NC}"
    echo -e "${MAGENTA}$(echo "$1" | sed 's/./=/g')${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Deployment progress tracking
PHASE=0
TOTAL_PHASES=6

next_phase() {
    PHASE=$((PHASE + 1))
    print_header "Phase $PHASE/$TOTAL_PHASES: $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking deployment prerequisites..."
    
    local missing_tools=()
    
    # Check required tools
    local required_tools=("aws" "node" "npm" "git" "curl" "openssl")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        exit 1
    fi
    
    # Check AWS CLI configuration
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS CLI not configured. Run 'aws configure' first."
        exit 1
    fi
    
    # Check Node.js version
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        print_error "Node.js 18+ required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check if in git repository
    if ! git rev-parse --git-dir >/dev/null 2>&1; then
        print_error "Not in a git repository"
        exit 1
    fi
    
    print_success "All prerequisites met"
}

# Function to run pre-deployment security validation
run_security_validation() {
    print_status "Running comprehensive security validation..."
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm ci >/dev/null 2>&1
    fi
    
    # Run security tests
    if npm run security:production-ready >/dev/null 2>&1; then
        print_success "Security validation passed"
    else
        print_error "Security validation failed - deployment blocked"
        exit 1
    fi
    
    # Test security implementation
    if npx tsx scripts/test-security-implementation.ts >/dev/null 2>&1; then
        print_success "Security implementation verified"
    else
        print_error "Security implementation verification failed"
        exit 1
    fi
}

# Function to setup AWS infrastructure
setup_aws_infrastructure() {
    print_status "Setting up AWS infrastructure..."
    
    # Make scripts executable
    chmod +x deployment/setup-aws-infrastructure.sh
    chmod +x deployment/setup-secrets.sh
    
    # Run infrastructure setup
    if ./deployment/setup-aws-infrastructure.sh; then
        print_success "AWS infrastructure setup completed"
    else
        print_error "AWS infrastructure setup failed"
        exit 1
    fi
    
    # Setup secrets (interactive)
    print_warning "Setting up AWS Secrets Manager..."
    print_status "You will be prompted to enter production secrets..."
    
    if ./deployment/setup-secrets.sh; then
        print_success "AWS Secrets Manager configured"
    else
        print_error "AWS Secrets Manager setup failed"
        exit 1
    fi
}

# Function to configure environment variables
configure_environment() {
    print_status "Configuring production environment variables..."
    
    # Get Amplify App ID
    local app_id=$(aws amplify list-apps --query "apps[?name=='$APP_NAME'].appId" --output text)
    
    if [ -z "$app_id" ] || [ "$app_id" = "None" ]; then
        print_error "Amplify app not found. Run infrastructure setup first."
        exit 1
    fi
    
    export AMPLIFY_APP_ID="$app_id"
    print_success "Found Amplify app: $app_id"
    
    # Run environment setup
    if node deployment/production-env-setup.js; then
        print_success "Environment variables configured"
    else
        print_error "Environment configuration failed"
        exit 1
    fi
}

# Function to setup database
setup_database() {
    print_status "Setting up production database..."
    
    # Make script executable
    chmod +x deployment/setup-database.sh
    
    # Run database setup
    if ./deployment/setup-database.sh; then
        print_success "Database setup completed"
    else
        print_error "Database setup failed"
        exit 1
    fi
}

# Function to verify Modal integration
verify_modal_integration() {
    print_status "Verifying Modal A100 GPU integration..."
    
    # Check if Modal tokens are set
    if [ -z "$MODAL_TOKEN_ID" ] || [ -z "$MODAL_TOKEN_SECRET" ]; then
        print_warning "Modal tokens not set in environment"
        print_status "Please set MODAL_TOKEN_ID and MODAL_TOKEN_SECRET"
        read -p "Press Enter after setting Modal tokens..."
    fi
    
    # Make script executable
    chmod +x deployment/verify-modal-integration.sh
    
    # Run Modal verification
    if ./deployment/verify-modal-integration.sh; then
        print_success "Modal A100 GPU integration verified"
    else
        print_warning "Modal integration verification failed - continuing deployment"
    fi
}

# Function to deploy application
deploy_application() {
    print_status "Deploying application to AWS Amplify..."
    
    # Ensure we're on the main branch
    local current_branch=$(git branch --show-current)
    if [ "$current_branch" != "main" ]; then
        print_warning "Not on main branch. Current: $current_branch"
        read -p "Switch to main branch? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            git checkout main
        fi
    fi
    
    # Commit any pending changes
    if ! git diff --quiet || ! git diff --cached --quiet; then
        print_status "Committing deployment configuration..."
        git add .
        git commit -m "Production deployment configuration - $(date '+%Y-%m-%d %H:%M:%S')" || true
    fi
    
    # Push to trigger Amplify build
    print_status "Pushing to GitHub to trigger Amplify deployment..."
    git push origin main
    
    # Get Amplify App ID
    local app_id=$(aws amplify list-apps --query "apps[?name=='$APP_NAME'].appId" --output text)
    
    # Start Amplify build
    local job_id=$(aws amplify start-job \
        --app-id "$app_id" \
        --branch-name main \
        --job-type RELEASE \
        --query 'jobSummary.jobId' --output text)
    
    print_success "Amplify deployment started: Job ID $job_id"
    
    # Monitor deployment progress
    print_status "Monitoring deployment progress..."
    local status="PENDING"
    local attempts=0
    local max_attempts=60  # 30 minutes max
    
    while [ "$status" != "SUCCEED" ] && [ "$status" != "FAILED" ] && [ $attempts -lt $max_attempts ]; do
        sleep 30
        attempts=$((attempts + 1))
        
        status=$(aws amplify get-job \
            --app-id "$app_id" \
            --branch-name main \
            --job-id "$job_id" \
            --query 'job.summary.status' --output text)
        
        print_status "Deployment status: $status (attempt $attempts/$max_attempts)"
        
        if [ "$status" = "FAILED" ]; then
            print_error "Amplify deployment failed"
            
            # Get build logs
            print_status "Fetching build logs..."
            aws amplify get-job \
                --app-id "$app_id" \
                --branch-name main \
                --job-id "$job_id" \
                --query 'job.steps[*].logUrl' --output text
            
            exit 1
        fi
    done
    
    if [ "$status" = "SUCCEED" ]; then
        print_success "Amplify deployment completed successfully"
        
        # Get app URL
        local app_url=$(aws amplify get-app --app-id "$app_id" --query 'app.defaultDomain' --output text)
        print_success "Application deployed to: https://$app_url"
    else
        print_error "Deployment timed out or failed"
        exit 1
    fi
}

# Function to run post-deployment validation
run_post_deployment_validation() {
    print_status "Running post-deployment validation..."
    
    # Wait for DNS propagation
    print_status "Waiting for DNS propagation..."
    sleep 60
    
    # Make validation script executable
    chmod +x deployment/validate-production.sh
    
    # Run production validation
    if ./deployment/validate-production.sh; then
        print_success "Post-deployment validation passed"
    else
        print_warning "Some post-deployment validations failed - review logs"
    fi
    
    # Test specific functionality
    print_status "Testing core functionality..."
    
    # Test frontend
    if curl -f -s "https://$DOMAIN" >/dev/null; then
        print_success "Frontend accessible"
    else
        print_error "Frontend not accessible"
    fi
    
    # Test API
    if curl -f -s "https://$API_DOMAIN/api/health" >/dev/null; then
        print_success "API accessible"
    else
        print_error "API not accessible"
    fi
    
    # Test database connectivity
    if curl -f -s "https://$API_DOMAIN/api/db/health" >/dev/null; then
        print_success "Database connectivity verified"
    else
        print_warning "Database connectivity test failed"
    fi
}

# Function to display deployment summary
display_deployment_summary() {
    echo ""
    print_header "🎉 Deployment Summary"
    
    echo "AILearnMaster has been successfully deployed to production!"
    echo ""
    echo "🌐 Production URLs:"
    echo "   Frontend: https://$DOMAIN"
    echo "   API: https://$API_DOMAIN"
    echo "   Admin: https://$DOMAIN/admin"
    echo ""
    echo "🔧 Infrastructure:"
    echo "   AWS Amplify: Hosting frontend and backend"
    echo "   AWS S3: File storage with CloudFront CDN"
    echo "   Neon PostgreSQL: Database with SSL encryption"
    echo "   Modal A100 GPU: AI course generation"
    echo "   AWS Secrets Manager: Secure credentials storage"
    echo ""
    echo "🛡️ Security Features:"
    echo "   ✅ SSL/TLS encryption everywhere"
    echo "   ✅ Security headers (CSP, HSTS, etc.)"
    echo "   ✅ Rate limiting and CSRF protection"
    echo "   ✅ Input validation and sanitization"
    echo "   ✅ Secure secrets management"
    echo ""
    echo "📊 Next Steps:"
    echo "   1. Monitor application performance and logs"
    echo "   2. Set up monitoring alerts and dashboards"
    echo "   3. Schedule regular security audits"
    echo "   4. Plan for scaling and optimization"
    echo "   5. Implement user feedback collection"
    echo ""
    print_success "Production deployment completed successfully! 🚀"
}

# Main deployment orchestration
main() {
    print_header "🚀 AILearnMaster Production Deployment"
    echo "Starting complete production deployment process..."
    echo ""
    
    # Phase 1: Prerequisites and Security
    next_phase "Prerequisites and Security Validation"
    check_prerequisites
    run_security_validation
    
    # Phase 2: AWS Infrastructure
    next_phase "AWS Infrastructure Setup"
    setup_aws_infrastructure
    
    # Phase 3: Environment Configuration
    next_phase "Environment Configuration"
    configure_environment
    
    # Phase 4: Database and Modal Setup
    next_phase "Database and Modal Integration"
    setup_database
    verify_modal_integration
    
    # Phase 5: Application Deployment
    next_phase "Application Deployment"
    deploy_application
    
    # Phase 6: Post-Deployment Validation
    next_phase "Post-Deployment Validation"
    run_post_deployment_validation
    
    # Display summary
    display_deployment_summary
}

# Handle script interruption
trap 'print_error "Deployment interrupted"; exit 1' INT TERM

# Run main deployment
main "$@"
