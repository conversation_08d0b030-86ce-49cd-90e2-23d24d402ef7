#!/usr/bin/env python3
"""
Simple SDXL Test Script
"""

import requests
import json

def test_sdxl():
    """Test SDXL endpoint directly"""
    url = "https://trade-digital--courseai-a100-simple-api-sdxl.modal.run"
    
    payload = {
        "prompt": "A simple test image, digital art",
        "negative_prompt": "blurry, low quality",
        "width": 1024,
        "height": 1024,
        "num_inference_steps": 10,  # Very fast for testing
        "guidance_scale": 7.5,
        "seed": 42
    }
    
    print("🔍 Testing SDXL endpoint...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, json=payload, timeout=300)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_sdxl()
