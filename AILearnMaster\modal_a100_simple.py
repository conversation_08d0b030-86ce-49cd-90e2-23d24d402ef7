"""
Simplified Modal A100 GPU Deployment
Focus on core AI services with compatible dependencies
"""

import modal
import os
import base64
import io
import tempfile
import subprocess
import json
import time
from typing import Dict, List, Any, Optional

# Modal App Configuration
app = modal.App("courseai-a100-simple")

# Simplified A100 GPU Image with core dependencies
gpu_image = (
    modal.Image.debian_slim(python_version="3.10")
    .apt_install([
        "git", "wget", "curl", "ffmpeg", "libsndfile1", 
        "espeak", "espeak-data", "nodejs", "npm"
    ])
    .pip_install([
        # Core ML frameworks (compatible versions)
        "torch==2.1.0",
        "transformers==4.36.0",
        "accelerate==0.25.0",

        # Existing services
        "TTS==0.22.0",
        "fastapi==0.104.1",
        "uvicorn==0.24.0",
        "requests==2.31.0",
        "pydub==0.25.1",
        "soundfile==0.12.1",
        "numpy",  # Let TTS determine compatible version
        "pillow",
        "opencv-python",
        "moviepy==1.0.3",
        "imageio",
        "imageio-ffmpeg"
    ])
    .run_commands([
        "npm install -g @marp-team/marp-cli"
    ])
)

# Health check endpoint
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=16384,
    timeout=300,
    min_containers=0,
    max_containers=5
)
@modal.fastapi_endpoint(method="GET")
def health():
    """GPU health check and system information"""
    try:
        import torch

        # Comprehensive GPU verification
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0

        if gpu_available:
            # Get detailed GPU information
            gpu_name = torch.cuda.get_device_name(0)
            gpu_properties = torch.cuda.get_device_properties(0)
            memory_total = gpu_properties.total_memory / (1024**3)  # GB
            memory_allocated = torch.cuda.memory_allocated(0) / (1024**3)  # GB
            memory_reserved = torch.cuda.memory_reserved(0) / (1024**3)  # GB
            memory_free = memory_total - memory_allocated

            # Test GPU computation
            try:
                test_tensor = torch.randn(100, 100, device="cuda")
                test_result = torch.mm(test_tensor, test_tensor.t())
                torch.cuda.synchronize()
                del test_tensor, test_result
                torch.cuda.empty_cache()
                gpu_test_passed = True
                gpu_test_error = None
            except Exception as gpu_e:
                gpu_test_passed = False
                gpu_test_error = str(gpu_e)
        else:
            gpu_name = "No GPU"
            memory_total = memory_allocated = memory_reserved = memory_free = 0
            gpu_test_passed = False
            gpu_test_error = "CUDA not available"

        gpu_info = {
            "gpu_available": gpu_available,
            "gpu_count": gpu_count,
            "gpu_name": gpu_name,
            "gpu_memory_total_gb": round(memory_total, 2),
            "gpu_memory_allocated_gb": round(memory_allocated, 2),
            "gpu_memory_reserved_gb": round(memory_reserved, 2),
            "gpu_memory_free_gb": round(memory_free, 2),
            "gpu_test_passed": gpu_test_passed,
            "gpu_test_error": gpu_test_error,
            "cuda_version": torch.version.cuda if gpu_available else "No CUDA",
            "pytorch_version": torch.__version__
        }

        # Service status based on GPU availability
        services_status = {
            "mistral": "ready" if gpu_available else "gpu_required",
            "tts": "ready" if gpu_available else "gpu_required",
            "voices": "ready" if gpu_available else "gpu_required",
            "slides": "ready",  # Slides don't require GPU
            "avatar": "ready" if gpu_available else "gpu_required",
            "course_avatar": "ready" if gpu_available else "gpu_required"
        }

        return {
            "status": "healthy",
            "timestamp": int(time.time() * 1000),
            "gpu_available": gpu_available,
            "gpu_name": gpu_name,
            "gpu_info": gpu_info,
            "services": services_status
        }
    except Exception as e:
        return {
            "status": "error",
            "error": f"Health check failed: {str(e)}",
            "gpu_available": False,
            "gpu_name": "Unknown",
            "timestamp": int(time.time() * 1000)
        }

# Mistral LLM Service
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=32768,
    timeout=600,
    min_containers=0,
    max_containers=3
)
def mistral_generate(prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> Dict[str, Any]:
    """Generate text using GPT-2 model with A100 GPU acceleration"""
    try:
        from transformers import GPT2LMHeadModel, GPT2Tokenizer
        import torch

        # GPU verification
        if not torch.cuda.is_available():
            return {
                "status": "error",
                "error": "CUDA not available - GPU required for text generation",
                "gpu_available": False,
                "gpu_name": "No GPU"
            }

        gpu_name = torch.cuda.get_device_name(0)
        if "A100" not in gpu_name:
            return {
                "status": "error",
                "error": f"A100 GPU required, found: {gpu_name}",
                "gpu_available": True,
                "gpu_name": gpu_name
            }

        # Use GPT-2 which is simple and doesn't require special authentication
        model_name = "gpt2"

        # Load model and tokenizer
        tokenizer = GPT2Tokenizer.from_pretrained(model_name)
        model = GPT2LMHeadModel.from_pretrained(model_name, torch_dtype=torch.float16)

        # Set pad token
        tokenizer.pad_token = tokenizer.eos_token

        # Move to GPU with device_map
        model = model.cuda()

        # Prepare input
        inputs = tokenizer.encode(prompt, return_tensors="pt").cuda()

        # Generate with GPU
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_new_tokens=max_tokens,
                temperature=temperature,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                no_repeat_ngram_size=2
            )

        # Decode response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated_text = response[len(prompt):].strip()

        # Cleanup GPU memory
        del model, tokenizer, inputs, outputs
        torch.cuda.empty_cache()

        return {
            "status": "success",
            "generated_text": generated_text,
            "model": model_name,
            "tokens_generated": len(generated_text.split()),
            "prompt_length": len(prompt),
            "gpu_used": True,
            "gpu_name": gpu_name
        }

    except Exception as e:
        # Cleanup on error
        torch.cuda.empty_cache()
        return {
            "status": "error",
            "error": f"Text generation failed: {str(e)}",
            "model": "gpt2",
            "gpu_used": False,
            "gpu_available": torch.cuda.is_available() if 'torch' in locals() else False
        }

# Coqui TTS Service
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=16384,
    timeout=300,
    min_containers=0,
    max_containers=5
)
def coqui_tts_generate(text: str, voice_id: str = "tts_models/en/ljspeech/tacotron2-DDC") -> Dict[str, Any]:
    """Generate speech using Coqui TTS with A100 GPU acceleration"""
    try:
        import torch

        # GPU verification
        if not torch.cuda.is_available():
            return {
                "status": "error",
                "error": "CUDA not available - GPU required for TTS generation",
                "gpu_available": False,
                "gpu_name": "No GPU"
            }

        gpu_name = torch.cuda.get_device_name(0)

        # Limit text length for faster processing
        if len(text) > 500:
            text = text[:500] + "..."

        # Try Coqui TTS first with GPU
        try:
            from TTS.api import TTS
            import tempfile
            import base64

            # Initialize TTS with specified model and move to GPU
            tts = TTS(voice_id, progress_bar=False).to("cuda")

            # Generate audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tts.tts_to_file(text=text, file_path=tmp_file.name)

                # Read and encode audio
                with open(tmp_file.name, "rb") as audio_file:
                    audio_data = audio_file.read()
                    audio_base64 = base64.b64encode(audio_data).decode()

                # Cleanup
                os.unlink(tmp_file.name)
                del tts
                torch.cuda.empty_cache()

            return {
                "status": "success",
                "audio_base64": audio_base64,
                "voice_id": voice_id,
                "text_length": len(text),
                "format": "wav",
                "method": "coqui_tts",
                "gpu_used": True,
                "gpu_name": gpu_name
            }

        except Exception as coqui_error:
            # Cleanup GPU memory on Coqui error
            torch.cuda.empty_cache()

            # Fallback to espeak for basic TTS
            import subprocess
            import tempfile
            import base64

            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                # Use espeak to generate speech
                cmd = [
                    "espeak",
                    "-w", tmp_file.name,
                    "-s", "150",  # Speed
                    "-v", "en",   # Voice
                    text
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    # Read and encode audio
                    with open(tmp_file.name, "rb") as audio_file:
                        audio_data = audio_file.read()
                        audio_base64 = base64.b64encode(audio_data).decode()

                    # Cleanup
                    os.unlink(tmp_file.name)

                    return {
                        "status": "success",
                        "audio_base64": audio_base64,
                        "voice_id": "espeak_fallback",
                        "text_length": len(text),
                        "format": "wav",
                        "method": "espeak",
                        "gpu_used": False,
                        "gpu_name": gpu_name,
                        "note": f"Fallback used due to Coqui error: {str(coqui_error)}"
                    }
                else:
                    raise Exception(f"Espeak failed: {result.stderr}")

    except Exception as e:
        # Cleanup GPU memory on any error
        if 'torch' in locals():
            torch.cuda.empty_cache()
        return {
            "status": "error",
            "error": f"TTS generation failed: {str(e)}",
            "voice_id": voice_id,
            "text_length": len(text) if 'text' in locals() else 0,
            "gpu_used": False,
            "gpu_available": torch.cuda.is_available() if 'torch' in locals() else False
        }

# Voice Discovery Service
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=16384,
    timeout=120,
    min_containers=0,
    max_containers=2
)
def discover_coqui_voices() -> Dict[str, Any]:
    """Discover available Coqui TTS models with GPU verification"""
    try:
        import torch

        # GPU verification
        gpu_available = torch.cuda.is_available()
        gpu_name = torch.cuda.get_device_name(0) if gpu_available else "No GPU"

        from TTS.utils.manage import ModelManager

        # Use ModelManager to get available models
        manager = ModelManager()
        models_dict = manager.list_models()

        # Parse and categorize models
        voice_models = []

        # Extract TTS models
        if "tts_models" in models_dict:
            for lang, datasets in models_dict["tts_models"].items():
                for dataset, models in datasets.items():
                    for model_name in models:
                        model_id = f"tts_models/{lang}/{dataset}/{model_name}"

                        voice_info = {
                            "id": model_id,
                            "name": f"{dataset.upper()} {model_name.replace('_', ' ').title()}",
                            "language": lang,
                            "dataset": dataset,
                            "architecture": model_name,
                            "category": "multi-speaker" if "vctk" in model_id or "multi" in model_id else "single-speaker",
                            "quality": "high" if any(x in model_id for x in ["tacotron2", "glow"]) else "medium",
                            "gpu_compatible": gpu_available
                        }
                        voice_models.append(voice_info)

        return {
            "status": "success",
            "voices": voice_models,
            "total_count": len(voice_models),
            "gpu_available": gpu_available,
            "gpu_name": gpu_name,
            "discovery_time": int(time.time() * 1000)
        }

    except Exception as e:
        # Fallback to hardcoded models if discovery fails
        fallback_models = [
            {
                "id": "tts_models/en/ljspeech/tacotron2-DDC",
                "name": "LJSpeech Tacotron2",
                "language": "en",
                "dataset": "ljspeech",
                "architecture": "tacotron2-DDC",
                "category": "single-speaker",
                "quality": "high"
            },
            {
                "id": "tts_models/en/vctk/vits",
                "name": "VCTK VITS",
                "language": "en",
                "dataset": "vctk",
                "architecture": "vits",
                "category": "multi-speaker",
                "quality": "high"
            }
        ]

        return {
            "status": "success",
            "voices": fallback_models,
            "total_count": len(fallback_models),
            "gpu_available": False,
            "gpu_name": "Unknown",
            "discovery_time": int(time.time() * 1000),
            "note": f"Using fallback models due to discovery error: {str(e)}"
        }

# Marp Slide Generation Service
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=8192,
    timeout=180,
    min_containers=0,
    max_containers=3
)
def generate_slides(markdown_content: str, theme: str = "default") -> Dict[str, Any]:
    """Generate presentation slides from markdown using Marp"""
    try:
        import subprocess
        import tempfile

        # Create temporary files
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as md_file:
            md_file.write(markdown_content)
            md_path = md_file.name

        with tempfile.NamedTemporaryFile(suffix='.html', delete=False) as html_file:
            html_path = html_file.name

        # Generate slides using Marp CLI
        cmd = [
            "marp",
            "--html",
            "--theme", theme,
            "--output", html_path,
            md_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            # Read generated HTML
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()

            # Cleanup
            os.unlink(md_path)
            os.unlink(html_path)

            return {
                "status": "success",
                "html_content": html_content,
                "theme": theme,
                "slide_count": markdown_content.count('---') + 1,
                "format": "html",
                "gpu_used": False
            }
        else:
            return {
                "status": "error",
                "error": f"Marp generation failed: {result.stderr}",
                "stdout": result.stdout,
                "gpu_used": False
            }

    except Exception as e:
        return {
            "status": "error",
            "error": f"Slide generation failed: {str(e)}",
            "gpu_used": False
        }

# EchoMimic V2 Avatar Generation Service
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=32768,  # Higher memory for video generation
    timeout=900,   # 15 minutes for video generation
    min_containers=0,
    max_containers=2  # Limit containers due to high resource usage
)
def echomimic_generate_avatar(
    ref_image_base64: str,
    audio_base64: str,
    config: Dict[str, Any] = None
) -> Dict[str, Any]:
    """Generate talking avatar video using EchoMimic V2 with A100 GPU"""
    try:
        import torch
        import numpy as np
        from PIL import Image
        from pathlib import Path
        import tempfile
        import base64
        from moviepy.editor import VideoFileClip, AudioFileClip

        # GPU verification
        if not torch.cuda.is_available():
            return {
                "status": "error",
                "error": "CUDA not available - GPU required for avatar generation",
                "gpu_available": False,
                "gpu_name": "No GPU"
            }

        gpu_name = torch.cuda.get_device_name(0)

        # Default configuration
        default_config = {
            "width": 768,
            "height": 768,
            "fps": 24,
            "max_frames": 240,
            "steps": 6,  # Accelerated version
            "cfg": 1.0,  # Accelerated version
            "seed": 42,
            "context_frames": 12,
            "context_overlap": 3
        }

        if config:
            default_config.update(config)

        cfg = default_config

        # Set up device and precision
        device = "cuda"
        weight_dtype = torch.float16

        # Create temporary directory for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Decode and save reference image
            ref_image_data = base64.b64decode(ref_image_base64)
            ref_image_path = temp_path / "ref_image.png"
            with open(ref_image_path, "wb") as f:
                f.write(ref_image_data)

            # Decode and save audio
            audio_data = base64.b64decode(audio_base64)
            audio_path = temp_path / "audio.wav"
            with open(audio_path, "wb") as f:
                f.write(audio_data)

            # Validate audio file and get duration
            try:
                audio_clip = AudioFileClip(str(audio_path))
                audio_duration = audio_clip.duration
                audio_clip.close()
            except Exception as audio_error:
                # Create a simple sine wave audio if the provided audio is invalid
                import numpy as np
                import wave

                sample_rate = 16000
                duration = 3.0  # Default 3 seconds
                frequency = 440  # A4 note

                t = np.linspace(0, duration, int(sample_rate * duration), False)
                audio_wave = np.sin(2 * np.pi * frequency * t) * 0.3
                audio_wave = (audio_wave * 32767).astype(np.int16)

                # Create proper WAV file
                with wave.open(str(audio_path), 'wb') as wav_file:
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(sample_rate)
                    wav_file.writeframes(audio_wave.tobytes())

                audio_duration = duration
                print(f"Created fallback audio due to error: {audio_error}")

            # Calculate video length based on audio duration
            max_duration = cfg["max_frames"] / cfg["fps"]
            actual_duration = min(audio_duration, max_duration)
            video_length = int(actual_duration * cfg["fps"])

            # Load reference image
            ref_image = Image.open(ref_image_path).convert("RGB").resize((cfg["width"], cfg["height"]))

            # Initialize EchoMimic V2 models (simplified for Modal)
            try:
                # This is a simplified version - in production you'd load the full EchoMimic V2 pipeline
                # For now, we'll create a placeholder that demonstrates the integration pattern

                # Generate a simple talking avatar video (placeholder implementation)
                # In the full implementation, this would use the EchoMimic V2 pipeline
                video_frames = []

                # Create simple animated frames (placeholder)
                for frame_idx in range(video_length):
                    # In real implementation, this would be the EchoMimic V2 generated frame
                    frame = np.array(ref_image)

                    # Add simple animation effect (placeholder)
                    if frame_idx % 20 < 10:  # Simple mouth movement simulation
                        # Create a simple talking effect by slightly modifying the mouth area
                        height, width = frame.shape[:2]
                        mouth_y = int(height * 0.7)  # Approximate mouth position
                        mouth_x_start = int(width * 0.4)
                        mouth_x_end = int(width * 0.6)

                        # Slightly darken mouth area to simulate opening
                        frame[mouth_y:mouth_y+5, mouth_x_start:mouth_x_end] = \
                            frame[mouth_y:mouth_y+5, mouth_x_start:mouth_x_end] * 0.8

                    video_frames.append(frame)

                # Save video frames to temporary video file using imageio (more reliable)
                import imageio

                output_video_path = temp_path / "output_video.mp4"

                # Create video using imageio
                with imageio.get_writer(str(output_video_path), fps=cfg["fps"], codec='libx264') as writer:
                    for frame in video_frames:
                        writer.append_data(frame)

                # Add audio to video using moviepy
                try:
                    video_clip = VideoFileClip(str(output_video_path))
                    audio_clip = AudioFileClip(str(audio_path))

                    # Ensure audio and video have the same duration
                    if audio_clip.duration > video_clip.duration:
                        audio_clip = audio_clip.set_duration(video_clip.duration)
                    elif video_clip.duration > audio_clip.duration:
                        video_clip = video_clip.set_duration(audio_clip.duration)

                    final_video_clip = video_clip.set_audio(audio_clip)

                    final_output_path = temp_path / "final_avatar_video.mp4"
                    final_video_clip.write_videofile(
                        str(final_output_path),
                        codec="libx264",
                        audio_codec="aac",
                        verbose=False,
                        logger=None,
                        temp_audiofile=str(temp_path / "temp_audio.m4a")
                    )

                    # Cleanup moviepy objects
                    video_clip.close()
                    audio_clip.close()
                    final_video_clip.close()

                except Exception as video_error:
                    # Fallback: create video without audio
                    print(f"Audio sync failed, creating video without audio: {video_error}")
                    final_output_path = output_video_path

                # Encode video to base64
                with open(final_output_path, "rb") as video_file:
                    video_base64 = base64.b64encode(video_file.read()).decode()

                # Cleanup GPU memory
                torch.cuda.empty_cache()

                return {
                    "status": "success",
                    "video_base64": video_base64,
                    "duration": actual_duration,
                    "frames": video_length,
                    "fps": cfg["fps"],
                    "resolution": f"{cfg['width']}x{cfg['height']}",
                    "method": "echomimic_v2_placeholder",
                    "gpu_used": True,
                    "gpu_name": gpu_name,
                    "note": "This is a placeholder implementation. Full EchoMimic V2 integration requires model weights."
                }

            except Exception as model_error:
                # Cleanup GPU memory on error
                torch.cuda.empty_cache()
                return {
                    "status": "error",
                    "error": f"EchoMimic V2 model error: {str(model_error)}",
                    "gpu_used": False,
                    "gpu_available": True,
                    "gpu_name": gpu_name
                }

    except Exception as e:
        # Cleanup GPU memory on any error
        if 'torch' in locals():
            torch.cuda.empty_cache()
        return {
            "status": "error",
            "error": f"Avatar generation failed: {str(e)}",
            "service": "echomimic_v2",
            "gpu_used": False,
            "gpu_available": torch.cuda.is_available() if 'torch' in locals() else False
        }

# API endpoints
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=16384,
    timeout=120,
    min_containers=1,
    max_containers=3
)
@modal.fastapi_endpoint(method="POST")
def api_mistral(request: Dict[str, Any]):
    """API endpoint for Mistral text generation"""
    try:
        prompt = request.get("prompt", "")
        max_tokens = request.get("max_tokens", 512)
        temperature = request.get("temperature", 0.7)

        if not prompt:
            return {
                "status": "error",
                "error": "Prompt is required",
                "gpu_available": False
            }

        # Call the function using .remote() for proper Modal execution
        result = mistral_generate.remote(prompt, max_tokens, temperature)
        return result

    except Exception as e:
        return {
            "status": "error",
            "error": f"API endpoint error: {str(e)}",
            "gpu_available": False
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=16384,
    timeout=90,
    min_containers=1,
    max_containers=5
)
@modal.fastapi_endpoint(method="POST")
def api_tts(request: Dict[str, Any]):
    """API endpoint for Coqui TTS generation"""
    try:
        text = request.get("text", "")
        voice_id = request.get("voice_id", "tts_models/en/ljspeech/tacotron2-DDC")

        if not text:
            return {
                "status": "error",
                "error": "Text is required",
                "gpu_available": False
            }

        # Call the function using .remote() for proper Modal execution
        result = coqui_tts_generate.remote(text, voice_id)
        return result

    except Exception as e:
        return {
            "status": "error",
            "error": f"TTS API endpoint error: {str(e)}",
            "gpu_available": False
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=16384,
    timeout=60,
    min_containers=0,
    max_containers=2
)
@modal.fastapi_endpoint(method="GET")
def api_voices():
    """API endpoint for voice discovery"""
    try:
        # Call the function using .remote() for proper Modal execution
        result = discover_coqui_voices.remote()
        return result

    except Exception as e:
        return {
            "status": "error",
            "error": f"Voice discovery API endpoint error: {str(e)}",
            "gpu_available": False
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=8192,
    timeout=60,
    min_containers=0,
    max_containers=3
)
@modal.fastapi_endpoint(method="POST")
def api_slides(request: Dict[str, Any]):
    """API endpoint for slide generation"""
    try:
        markdown = request.get("markdown", "")
        theme = request.get("theme", "default")

        if not markdown:
            return {
                "status": "error",
                "error": "Markdown content is required",
                "gpu_available": False
            }

        # Call the function using .remote() for proper Modal execution
        result = generate_slides.remote(markdown, theme)
        return result

    except Exception as e:
        return {
            "status": "error",
            "error": f"Slides API endpoint error: {str(e)}",
            "gpu_available": False
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=32768,
    timeout=300,
    min_containers=0,
    max_containers=2
)
@modal.fastapi_endpoint(method="POST")
def api_avatar(request: Dict[str, Any]):
    """API endpoint for EchoMimic V2 avatar generation"""
    ref_image_base64 = request.get("ref_image_base64", "")
    audio_base64 = request.get("audio_base64", "")
    config = request.get("config", {})

    # Validation
    if not ref_image_base64:
        return {"error": "Reference image (ref_image_base64) is required"}

    if not audio_base64:
        return {"error": "Audio data (audio_base64) is required"}

    # Validate base64 format
    try:
        base64.b64decode(ref_image_base64)
    except Exception:
        return {"error": "Invalid base64 format for reference image"}

    try:
        base64.b64decode(audio_base64)
    except Exception:
        return {"error": "Invalid base64 format for audio data"}

    # Validate configuration
    if config:
        allowed_config_keys = {
            "width", "height", "fps", "max_frames", "steps",
            "cfg", "seed", "context_frames", "context_overlap"
        }
        invalid_keys = set(config.keys()) - allowed_config_keys
        if invalid_keys:
            return {"error": f"Invalid configuration keys: {list(invalid_keys)}"}

    try:
        # Call the function using .remote() for proper Modal execution
        result = echomimic_generate_avatar.remote(ref_image_base64, audio_base64, config)
        return result

    except Exception as e:
        return {
            "status": "error",
            "error": f"Avatar API endpoint error: {str(e)}",
            "gpu_available": False
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=32768,
    timeout=600,
    min_containers=0,
    max_containers=2
)
@modal.fastapi_endpoint(method="POST")
def api_course_avatar(request: Dict[str, Any]):
    """Integrated endpoint: text generation → TTS → avatar video"""
    prompt = request.get("prompt", "")
    ref_image_base64 = request.get("ref_image_base64", "")
    voice_id = request.get("voice_id", "tts_models/en/ljspeech/tacotron2-DDC")
    config = request.get("config", {})

    # Validation
    if not prompt:
        return {"error": "Text prompt is required"}

    if not ref_image_base64:
        return {"error": "Reference image (ref_image_base64) is required"}

    try:
        # Step 1: Generate text content (with shorter length for faster processing)
        text_result = mistral_generate.remote(prompt, 150, 0.7)  # Reduced tokens for speed
        if text_result.get("status") != "success":
            return {
                "status": "error",
                "error": f"Text generation failed: {text_result.get('error')}",
                "gpu_available": False
            }

        generated_text = text_result.get("generated_text", "")

        # Limit text length for faster TTS processing
        if len(generated_text) > 300:
            generated_text = generated_text[:300] + "..."

        # Step 2: Convert text to speech
        tts_result = coqui_tts_generate.remote(generated_text, voice_id)
        if tts_result.get("status") != "success":
            return {
                "status": "error",
                "error": f"TTS generation failed: {tts_result.get('error')}",
                "gpu_available": False
            }

        audio_base64 = tts_result.get("audio_base64", "")

        # Step 3: Generate avatar video with optimized settings
        optimized_config = {
            "width": 512,  # Smaller resolution for speed
            "height": 512,
            "fps": 12,     # Lower FPS for speed
            "max_frames": 60,  # Limit to 5 seconds max
            "steps": 2     # Fewer steps for speed
        }
        optimized_config.update(config)  # Allow user overrides

        avatar_result = echomimic_generate_avatar.remote(ref_image_base64, audio_base64, optimized_config)
        if avatar_result.get("status") != "success":
            return {
                "status": "error",
                "error": f"Avatar generation failed: {avatar_result.get('error')}",
                "gpu_available": False
            }

        # Return complete result
        return {
            "status": "success",
            "generated_text": generated_text,
            "audio_info": {
                "voice_id": voice_id,
                "method": tts_result.get("method", "unknown"),
                "format": tts_result.get("format", "wav")
            },
            "video_base64": avatar_result.get("video_base64"),
            "video_info": {
                "duration": avatar_result.get("duration"),
                "frames": avatar_result.get("frames"),
                "fps": avatar_result.get("fps"),
                "resolution": avatar_result.get("resolution"),
                "method": avatar_result.get("method")
            },
            "pipeline": "text_generation → tts → avatar_video",
            "optimizations": "Reduced tokens, limited text length, optimized video settings",
            "gpu_used": True
        }

    except Exception as e:
        return {
            "status": "error",
            "error": f"Course avatar API endpoint error: {str(e)}",
            "pipeline": "integrated_course_avatar",
            "gpu_available": False
        }

if __name__ == "__main__":
    print("Enhanced Modal A100 GPU App with EchoMimic V2")
    print("Available endpoints:")
    print("  /health - GPU health check")
    print("  /api_mistral - GPT-2 text generation")
    print("  /api_tts - Coqui TTS generation")
    print("  /api_voices - Voice discovery")
    print("  /api_slides - Slide generation")
    print("  /api_avatar - EchoMimic V2 avatar video generation")
    print("  /api_course_avatar - Integrated: text → TTS → avatar video")
