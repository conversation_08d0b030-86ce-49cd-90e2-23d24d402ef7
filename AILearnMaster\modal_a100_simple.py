"""
Simplified Modal A100 GPU Deployment
Focus on core AI services with compatible dependencies
"""

import modal
import os
import base64
import io
import tempfile
import subprocess
import json
import time
from typing import Dict, List, Any, Optional

# Modal App Configuration
app = modal.App("courseai-a100-simple")

# Simplified A100 GPU Image with core dependencies
gpu_image = (
    modal.Image.debian_slim(python_version="3.10")
    .apt_install([
        "git", "wget", "curl", "ffmpeg", "libsndfile1", 
        "espeak", "espeak-data", "nodejs", "npm"
    ])
    .pip_install([
        "torch==2.1.0",
        "transformers==4.36.0",
        "accelerate==0.25.0",
        "TTS==0.22.0",
        "fastapi==0.104.1",
        "uvicorn==0.24.0",
        "requests==2.31.0",
        "pydub==0.25.1",
        "soundfile==0.12.1",
        "numpy",  # Let TTS determine compatible version
        "pillow",
        "opencv-python"
    ])
    .run_commands([
        "npm install -g @marp-team/marp-cli"
    ])
)

# Health check endpoint
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=16384,
    timeout=300,
    min_containers=0,
    max_containers=5
)
@modal.fastapi_endpoint(method="GET")
def health():
    """GPU health check and system information"""
    try:
        import torch
        
        gpu_info = {
            "gpu_available": torch.cuda.is_available(),
            "gpu_count": torch.cuda.device_count(),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "No GPU",
            "cuda_version": torch.version.cuda if torch.cuda.is_available() else "No CUDA",
            "pytorch_version": torch.__version__,
            "memory_allocated": torch.cuda.memory_allocated(0) if torch.cuda.is_available() else 0,
            "memory_reserved": torch.cuda.memory_reserved(0) if torch.cuda.is_available() else 0
        }
        
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "gpu_info": gpu_info,
            "services": ["tts", "mistral", "slides"]
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

# Mistral LLM Service
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=32768,
    timeout=600,
    min_containers=0,
    max_containers=3
)
def mistral_generate(prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> Dict[str, Any]:
    """Generate text using GPT-2 model (simple and reliable)"""
    try:
        from transformers import GPT2LMHeadModel, GPT2Tokenizer
        import torch

        # Use GPT-2 which is simple and doesn't require special authentication
        model_name = "gpt2"

        # Load model and tokenizer
        tokenizer = GPT2Tokenizer.from_pretrained(model_name)
        model = GPT2LMHeadModel.from_pretrained(model_name)

        # Set pad token
        tokenizer.pad_token = tokenizer.eos_token

        # Move to GPU if available
        if torch.cuda.is_available():
            model = model.cuda()

        # Prepare input
        inputs = tokenizer.encode(prompt, return_tensors="pt")
        if torch.cuda.is_available():
            inputs = inputs.cuda()

        # Generate
        with torch.no_grad():
            outputs = model.generate(
                inputs,
                max_new_tokens=max_tokens,
                temperature=temperature,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                no_repeat_ngram_size=2
            )

        # Decode response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated_text = response[len(prompt):].strip()

        return {
            "success": True,
            "generated_text": generated_text,
            "model": model_name,
            "tokens_generated": len(tokenizer.encode(generated_text)),
            "prompt_length": len(prompt)
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "model": "gpt2"
        }

# Coqui TTS Service
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=16384,
    timeout=300,
    min_containers=0,
    max_containers=5
)
def coqui_tts_generate(text: str, voice_id: str = "tts_models/en/ljspeech/tacotron2-DDC") -> Dict[str, Any]:
    """Generate speech using Coqui TTS with fallback to simple TTS"""
    try:
        # Limit text length for faster processing
        if len(text) > 500:
            text = text[:500] + "..."

        # Try Coqui TTS first
        try:
            from TTS.api import TTS
            import tempfile
            import base64

            # Initialize TTS with specified model
            tts = TTS(voice_id, progress_bar=False)

            # Generate audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tts.tts_to_file(text=text, file_path=tmp_file.name)

                # Read and encode audio
                with open(tmp_file.name, "rb") as audio_file:
                    audio_data = audio_file.read()
                    audio_base64 = base64.b64encode(audio_data).decode()

                # Cleanup
                os.unlink(tmp_file.name)

            return {
                "success": True,
                "audio_base64": audio_base64,
                "voice_id": voice_id,
                "text_length": len(text),
                "format": "wav",
                "method": "coqui_tts"
            }

        except Exception as coqui_error:
            # Fallback to espeak for basic TTS
            import subprocess
            import tempfile
            import base64

            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                # Use espeak to generate speech
                cmd = [
                    "espeak",
                    "-w", tmp_file.name,
                    "-s", "150",  # Speed
                    "-v", "en",   # Voice
                    text
                ]

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    # Read and encode audio
                    with open(tmp_file.name, "rb") as audio_file:
                        audio_data = audio_file.read()
                        audio_base64 = base64.b64encode(audio_data).decode()

                    # Cleanup
                    os.unlink(tmp_file.name)

                    return {
                        "success": True,
                        "audio_base64": audio_base64,
                        "voice_id": "espeak_fallback",
                        "text_length": len(text),
                        "format": "wav",
                        "method": "espeak",
                        "note": f"Fallback used due to Coqui error: {str(coqui_error)}"
                    }
                else:
                    raise Exception(f"Espeak failed: {result.stderr}")

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "voice_id": voice_id,
            "text_length": len(text) if 'text' in locals() else 0
        }

# Voice Discovery Service
@app.function(
    image=gpu_image,
    memory=8192,
    timeout=120,
    min_containers=0,
    max_containers=2
)
def discover_coqui_voices() -> Dict[str, Any]:
    """Discover available Coqui TTS models"""
    try:
        from TTS.utils.manage import ModelManager

        # Use ModelManager to get available models
        manager = ModelManager()
        models_dict = manager.list_models()

        # Parse and categorize models
        voice_models = []

        # Extract TTS models
        if "tts_models" in models_dict:
            for lang, datasets in models_dict["tts_models"].items():
                for dataset, models in datasets.items():
                    for model_name in models:
                        model_id = f"tts_models/{lang}/{dataset}/{model_name}"

                        voice_info = {
                            "id": model_id,
                            "name": f"{dataset.upper()} {model_name.replace('_', ' ').title()}",
                            "language": lang,
                            "dataset": dataset,
                            "architecture": model_name,
                            "category": "multi-speaker" if "vctk" in model_id or "multi" in model_id else "single-speaker",
                            "quality": "high" if any(x in model_id for x in ["tacotron2", "glow"]) else "medium"
                        }
                        voice_models.append(voice_info)

        return {
            "success": True,
            "voices": voice_models,
            "total_count": len(voice_models),
            "discovery_time": time.time()
        }

    except Exception as e:
        # Fallback to hardcoded models if discovery fails
        fallback_models = [
            {
                "id": "tts_models/en/ljspeech/tacotron2-DDC",
                "name": "LJSpeech Tacotron2",
                "language": "en",
                "dataset": "ljspeech",
                "architecture": "tacotron2-DDC",
                "category": "single-speaker",
                "quality": "high"
            },
            {
                "id": "tts_models/en/vctk/vits",
                "name": "VCTK VITS",
                "language": "en",
                "dataset": "vctk",
                "architecture": "vits",
                "category": "multi-speaker",
                "quality": "high"
            }
        ]

        return {
            "success": True,
            "voices": fallback_models,
            "total_count": len(fallback_models),
            "discovery_time": time.time(),
            "note": f"Using fallback models due to discovery error: {str(e)}"
        }

# Marp Slide Generation Service
@app.function(
    image=gpu_image,
    memory=4096,
    timeout=180,
    min_containers=0,
    max_containers=3
)
def generate_slides(markdown_content: str, theme: str = "default") -> Dict[str, Any]:
    """Generate presentation slides from markdown using Marp"""
    try:
        import subprocess
        import tempfile
        
        # Create temporary files
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as md_file:
            md_file.write(markdown_content)
            md_path = md_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.html', delete=False) as html_file:
            html_path = html_file.name
        
        # Generate slides using Marp CLI
        cmd = [
            "marp", 
            "--html", 
            "--theme", theme,
            "--output", html_path,
            md_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            # Read generated HTML
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # Cleanup
            os.unlink(md_path)
            os.unlink(html_path)
            
            return {
                "success": True,
                "html_content": html_content,
                "theme": theme,
                "slide_count": markdown_content.count('---') + 1
            }
        else:
            return {
                "success": False,
                "error": result.stderr,
                "stdout": result.stdout
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# API endpoints
@app.function(image=gpu_image, memory=2048, timeout=60)
@modal.fastapi_endpoint(method="POST")
def api_mistral(request: Dict[str, Any]):
    """API endpoint for Mistral text generation"""
    prompt = request.get("prompt", "")
    max_tokens = request.get("max_tokens", 512)
    temperature = request.get("temperature", 0.7)
    
    if not prompt:
        return {"error": "Prompt is required"}
    
    return mistral_generate.remote(prompt, max_tokens, temperature)

@app.function(image=gpu_image, memory=2048, timeout=60)
@modal.fastapi_endpoint(method="POST")
def api_tts(request: Dict[str, Any]):
    """API endpoint for Coqui TTS generation"""
    text = request.get("text", "")
    voice_id = request.get("voice_id", "tts_models/en/ljspeech/tacotron2-DDC")
    
    if not text:
        return {"error": "Text is required"}
    
    return coqui_tts_generate.remote(text, voice_id)

@app.function(image=gpu_image, memory=2048, timeout=60)
@modal.fastapi_endpoint(method="GET")
def api_voices():
    """API endpoint for voice discovery"""
    return discover_coqui_voices.remote()

@app.function(image=gpu_image, memory=2048, timeout=60)
@modal.fastapi_endpoint(method="POST")
def api_slides(request: Dict[str, Any]):
    """API endpoint for slide generation"""
    markdown = request.get("markdown", "")
    theme = request.get("theme", "default")
    
    if not markdown:
        return {"error": "Markdown content is required"}
    
    return generate_slides.remote(markdown, theme)

if __name__ == "__main__":
    print("Simplified Modal A100 GPU App")
    print("Available endpoints:")
    print("  /health - GPU health check")
    print("  /api_mistral - Mistral text generation")
    print("  /api_tts - Coqui TTS generation")
    print("  /api_voices - Voice discovery")
    print("  /api_slides - Slide generation")
