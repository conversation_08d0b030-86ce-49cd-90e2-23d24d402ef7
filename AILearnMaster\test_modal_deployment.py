#!/usr/bin/env python3
"""
Comprehensive Modal GPU Deployment Testing Script
Tests all deployed AI services and validates functionality
"""

import requests
import json
import time
import base64
from typing import Dict, List, Any
import sys

class ModalDeploymentTester:
    def __init__(self):
        # Modal endpoints from deployment
        self.base_url = "https://trade-digital--courseai-a100-simple"
        self.endpoints = {
            "health": f"{self.base_url}-health.modal.run",
            "mistral": f"{self.base_url}-api-mistral.modal.run",
            "tts": f"{self.base_url}-api-tts.modal.run",
            "voices": f"{self.base_url}-api-voices.modal.run",
            "slides": f"{self.base_url}-api-slides.modal.run"
        }
        
        self.results = {
            "health": {"passed": 0, "failed": 0, "tests": []},
            "mistral": {"passed": 0, "failed": 0, "tests": []},
            "tts": {"passed": 0, "failed": 0, "tests": []},
            "voices": {"passed": 0, "failed": 0, "tests": []},
            "slides": {"passed": 0, "failed": 0, "tests": []},
            "integration": {"passed": 0, "failed": 0, "tests": []}
        }

    def test_health_endpoint(self):
        """Test GPU health check and system information"""
        print("🏥 Testing Health Endpoint...")
        
        try:
            response = requests.get(self.endpoints["health"], timeout=30)
            data = response.json()
            
            if response.status_code == 200 and data.get("status") == "healthy":
                gpu_info = data.get("gpu_info", {})
                print(f"  ✅ Health check passed")
                print(f"  📊 GPU Available: {gpu_info.get('gpu_available', False)}")
                print(f"  🔧 GPU Name: {gpu_info.get('gpu_name', 'Unknown')}")
                print(f"  🐍 PyTorch Version: {gpu_info.get('pytorch_version', 'Unknown')}")
                print(f"  🎯 Services: {data.get('services', [])}")
                
                self.results["health"]["passed"] += 1
                self.results["health"]["tests"].append({
                    "name": "Health Check",
                    "status": "PASS",
                    "gpu_info": gpu_info
                })
                return True
            else:
                print(f"  ❌ Health check failed: {data}")
                self.results["health"]["failed"] += 1
                return False
                
        except Exception as e:
            print(f"  ❌ Health endpoint error: {e}")
            self.results["health"]["failed"] += 1
            return False

    def test_mistral_llm(self):
        """Test Mistral LLM text generation"""
        print("\n🧠 Testing Mistral LLM Service...")
        
        test_prompts = [
            {
                "name": "Course Content Generation",
                "prompt": "Create a brief introduction to machine learning for beginners:",
                "max_tokens": 200
            },
            {
                "name": "Script Generation",
                "prompt": "Write a short script explaining photosynthesis:",
                "max_tokens": 150
            }
        ]
        
        for test in test_prompts:
            try:
                print(f"  🔄 Testing: {test['name']}")
                
                payload = {
                    "prompt": test["prompt"],
                    "max_tokens": test["max_tokens"],
                    "temperature": 0.7
                }
                
                start_time = time.time()
                response = requests.post(
                    self.endpoints["mistral"], 
                    json=payload, 
                    timeout=120
                )
                response_time = time.time() - start_time
                
                data = response.json()
                
                if response.status_code == 200 and data.get("success"):
                    generated_text = data.get("generated_text", "")
                    tokens_generated = data.get("tokens_generated", 0)
                    
                    print(f"    ✅ {test['name']} - Success")
                    print(f"    ⏱️ Response time: {response_time:.2f}s")
                    print(f"    📝 Tokens generated: {tokens_generated}")
                    print(f"    📄 Preview: {generated_text[:100]}...")
                    
                    self.results["mistral"]["passed"] += 1
                    self.results["mistral"]["tests"].append({
                        "name": test["name"],
                        "status": "PASS",
                        "response_time": response_time,
                        "tokens_generated": tokens_generated
                    })
                else:
                    print(f"    ❌ {test['name']} - Failed: {data}")
                    self.results["mistral"]["failed"] += 1
                    
            except Exception as e:
                print(f"    ❌ {test['name']} - Error: {e}")
                self.results["mistral"]["failed"] += 1

    def test_voice_discovery(self):
        """Test Coqui TTS voice discovery"""
        print("\n🎤 Testing Voice Discovery Service...")
        
        try:
            start_time = time.time()
            response = requests.get(self.endpoints["voices"], timeout=60)
            response_time = time.time() - start_time
            
            data = response.json()
            
            if response.status_code == 200 and data.get("success"):
                voices = data.get("voices", [])
                total_count = data.get("total_count", 0)
                
                print(f"  ✅ Voice discovery successful")
                print(f"  ⏱️ Response time: {response_time:.2f}s")
                print(f"  🎵 Total voices discovered: {total_count}")
                
                # Analyze voice categories
                languages = set()
                categories = set()
                architectures = set()
                
                for voice in voices[:10]:  # Sample first 10
                    languages.add(voice.get("language", "unknown"))
                    categories.add(voice.get("category", "unknown"))
                    architectures.add(voice.get("architecture", "unknown"))
                    
                print(f"  🌍 Languages: {', '.join(sorted(languages))}")
                print(f"  📂 Categories: {', '.join(sorted(categories))}")
                print(f"  🏗️ Architectures: {', '.join(sorted(architectures))}")
                
                self.results["voices"]["passed"] += 1
                self.results["voices"]["tests"].append({
                    "name": "Voice Discovery",
                    "status": "PASS",
                    "response_time": response_time,
                    "voice_count": total_count,
                    "sample_voices": voices[:5]
                })
                
                return voices
            else:
                print(f"  ❌ Voice discovery failed: {data}")
                self.results["voices"]["failed"] += 1
                return []
                
        except Exception as e:
            print(f"  ❌ Voice discovery error: {e}")
            self.results["voices"]["failed"] += 1
            return []

    def test_tts_generation(self, available_voices=None):
        """Test Coqui TTS speech synthesis"""
        print("\n🔊 Testing TTS Generation Service...")
        
        # Default voice if discovery failed
        test_voices = [
            "tts_models/en/ljspeech/tacotron2-DDC",
            "tts_models/en/vctk/vits"
        ]
        
        # Use discovered voices if available
        if available_voices:
            test_voices = [voice["id"] for voice in available_voices[:3]]
        
        test_texts = [
            "Welcome to your AI-powered learning experience!",
            "This is a test of the text-to-speech synthesis system."
        ]
        
        for voice_id in test_voices:
            for text in test_texts:
                try:
                    print(f"  🔄 Testing voice: {voice_id}")
                    
                    payload = {
                        "text": text,
                        "voice_id": voice_id
                    }
                    
                    start_time = time.time()
                    response = requests.post(
                        self.endpoints["tts"], 
                        json=payload, 
                        timeout=120
                    )
                    response_time = time.time() - start_time
                    
                    data = response.json()
                    
                    if response.status_code == 200 and data.get("success"):
                        audio_base64 = data.get("audio_base64", "")
                        text_length = data.get("text_length", 0)
                        
                        print(f"    ✅ TTS generation successful")
                        print(f"    ⏱️ Response time: {response_time:.2f}s")
                        print(f"    📝 Text length: {text_length} chars")
                        print(f"    🎵 Audio generated: {len(audio_base64)} bytes (base64)")
                        
                        self.results["tts"]["passed"] += 1
                        self.results["tts"]["tests"].append({
                            "name": f"TTS - {voice_id}",
                            "status": "PASS",
                            "response_time": response_time,
                            "text_length": text_length,
                            "audio_size": len(audio_base64)
                        })
                        break  # Success, move to next voice
                    else:
                        print(f"    ❌ TTS failed for {voice_id}: {data}")
                        
                except Exception as e:
                    print(f"    ❌ TTS error for {voice_id}: {e}")
                    
            # Only test first voice if we have multiple to save time
            if len(test_voices) > 1:
                break

    def test_slide_generation(self):
        """Test Marp slide generation"""
        print("\n📊 Testing Slide Generation Service...")
        
        test_slides = [
            {
                "name": "Simple Presentation",
                "markdown": """---
marp: true
theme: default
---

# Welcome to AI Learning
## Powered by Advanced Technology

---

# Key Features
- Intelligent Content Generation
- Voice Synthesis
- Interactive Presentations

---

# Thank You
Questions?
""",
                "theme": "default"
            }
        ]
        
        for test in test_slides:
            try:
                print(f"  🔄 Testing: {test['name']}")
                
                payload = {
                    "markdown": test["markdown"],
                    "theme": test["theme"]
                }
                
                start_time = time.time()
                response = requests.post(
                    self.endpoints["slides"], 
                    json=payload, 
                    timeout=60
                )
                response_time = time.time() - start_time
                
                data = response.json()
                
                if response.status_code == 200 and data.get("success"):
                    html_content = data.get("html_content", "")
                    slide_count = data.get("slide_count", 0)
                    
                    print(f"    ✅ Slide generation successful")
                    print(f"    ⏱️ Response time: {response_time:.2f}s")
                    print(f"    📄 Slides generated: {slide_count}")
                    print(f"    📝 HTML size: {len(html_content)} chars")
                    
                    self.results["slides"]["passed"] += 1
                    self.results["slides"]["tests"].append({
                        "name": test["name"],
                        "status": "PASS",
                        "response_time": response_time,
                        "slide_count": slide_count,
                        "html_size": len(html_content)
                    })
                else:
                    print(f"    ❌ Slide generation failed: {data}")
                    self.results["slides"]["failed"] += 1
                    
            except Exception as e:
                print(f"    ❌ Slide generation error: {e}")
                self.results["slides"]["failed"] += 1

    def test_integration_workflow(self):
        """Test end-to-end integration workflow"""
        print("\n🔄 Testing Integration Workflow...")
        
        try:
            # Step 1: Generate course content with Mistral
            print("  📝 Step 1: Generating course content...")
            content_payload = {
                "prompt": "Create a 2-minute lesson about renewable energy:",
                "max_tokens": 300,
                "temperature": 0.7
            }
            
            content_response = requests.post(
                self.endpoints["mistral"], 
                json=content_payload, 
                timeout=120
            )
            
            if content_response.status_code != 200:
                raise Exception("Content generation failed")
                
            content_data = content_response.json()
            lesson_text = content_data.get("generated_text", "")
            
            print(f"    ✅ Content generated: {len(lesson_text)} chars")
            
            # Step 2: Convert to speech with TTS
            print("  🎤 Step 2: Converting to speech...")
            tts_payload = {
                "text": lesson_text[:500],  # Limit for testing
                "voice_id": "tts_models/en/ljspeech/tacotron2-DDC"
            }
            
            tts_response = requests.post(
                self.endpoints["tts"], 
                json=tts_payload, 
                timeout=120
            )
            
            if tts_response.status_code != 200:
                raise Exception("TTS generation failed")
                
            tts_data = tts_response.json()
            audio_data = tts_data.get("audio_base64", "")
            
            print(f"    ✅ Audio generated: {len(audio_data)} bytes")
            
            # Step 3: Create presentation slides
            print("  📊 Step 3: Creating presentation...")
            slide_markdown = f"""---
marp: true
theme: default
---

# Renewable Energy Lesson

{lesson_text[:200]}...

---

# Key Points
- Clean energy sources
- Environmental benefits
- Future sustainability

---

# Thank You
End of lesson
"""
            
            slide_payload = {
                "markdown": slide_markdown,
                "theme": "default"
            }
            
            slide_response = requests.post(
                self.endpoints["slides"], 
                json=slide_payload, 
                timeout=60
            )
            
            if slide_response.status_code != 200:
                raise Exception("Slide generation failed")
                
            slide_data = slide_response.json()
            html_content = slide_data.get("html_content", "")
            
            print(f"    ✅ Slides generated: {len(html_content)} chars")
            
            print("  🎉 Integration workflow completed successfully!")
            
            self.results["integration"]["passed"] += 1
            self.results["integration"]["tests"].append({
                "name": "End-to-End Workflow",
                "status": "PASS",
                "content_length": len(lesson_text),
                "audio_size": len(audio_data),
                "html_size": len(html_content)
            })
            
        except Exception as e:
            print(f"  ❌ Integration workflow failed: {e}")
            self.results["integration"]["failed"] += 1

    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🚀 Starting Modal GPU Deployment Testing\n")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test all services
        health_ok = self.test_health_endpoint()
        
        if health_ok:
            self.test_mistral_llm()
            available_voices = self.test_voice_discovery()
            self.test_tts_generation(available_voices)
            self.test_slide_generation()
            self.test_integration_workflow()
        else:
            print("❌ Health check failed - skipping other tests")
        
        total_time = time.time() - start_time
        
        # Print results summary
        self.print_results_summary(total_time)

    def print_results_summary(self, total_time):
        """Print comprehensive test results"""
        print("\n" + "=" * 60)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 60)
        
        total_passed = 0
        total_failed = 0
        
        for category, results in self.results.items():
            passed = results["passed"]
            failed = results["failed"]
            total_passed += passed
            total_failed += failed
            
            status_icon = "✅" if failed == 0 else "⚠️" if passed > failed else "❌"
            print(f"{status_icon} {category.upper()}: {passed} passed, {failed} failed")
            
            if failed > 0:
                for test in results["tests"]:
                    if test.get("status") == "FAIL":
                        print(f"    ❌ {test['name']}")
        
        print("\n" + "-" * 60)
        print(f"🎯 TOTAL: {total_passed} passed, {total_failed} failed")
        print(f"⏱️ Total test time: {total_time:.2f} seconds")
        
        if total_failed == 0:
            print("🎉 ALL TESTS PASSED! Modal GPU deployment is fully functional.")
        else:
            print(f"⚠️ {total_failed} tests failed. Please review the deployment.")
        
        print("\n📋 Deployment URLs:")
        for name, url in self.endpoints.items():
            print(f"  {name}: {url}")

if __name__ == "__main__":
    tester = ModalDeploymentTester()
    tester.run_all_tests()
