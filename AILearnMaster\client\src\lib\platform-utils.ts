import { PlatformIntegration, platformCategories, defaultPlatforms } from '@/types/platform-integration';

// Re-export for convenience
export { platformCategories, defaultPlatforms };

/**
 * Formats a timestamp into a human-readable "last synced" time
 */
export function formatLastSyncedTime(timestamp: string): string {
  const date = new Date(timestamp);
  const now = new Date();
  
  // Convert both dates to milliseconds since Unix epoch
  const dateMs = date.getTime();
  const nowMs = now.getTime();
  
  // Calculate the difference in milliseconds
  const diffMs = nowMs - dateMs;
  
  // Convert to appropriate units
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  // Format the date
  if (diffDays > 30) {
    // If more than 30 days, show the date
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  } else if (diffDays > 0) {
    // If more than a day, show days ago
    return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
  } else if (diffHours > 0) {
    // If more than an hour, show hours ago
    return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
  } else if (diffMinutes > 0) {
    // If more than a minute, show minutes ago
    return `${diffMinutes} minute${diffMinutes === 1 ? '' : 's'} ago`;
  } else {
    // Otherwise, show seconds ago or "just now"
    return diffSeconds < 30 ? 'just now' : `${diffSeconds} seconds ago`;
  }
}

/**
 * Get platforms grouped by category
 */
export function getPlatformsByCategory(platforms: PlatformIntegration[] | any): Record<string, PlatformIntegration[]> {
  if (!platforms || !Array.isArray(platforms)) {
    return {} as Record<string, PlatformIntegration[]>;
  }
  
  return platforms.reduce((acc, platform) => {
    const category = platform.category || 'other';
    
    if (!acc[category]) {
      acc[category] = [];
    }
    
    acc[category].push(platform);
    return acc;
  }, {} as Record<string, PlatformIntegration[]>);
}

/**
 * Check if a platform supports OAuth authentication
 */
export function platformSupportsOAuth(platform: PlatformIntegration): boolean {
  return platform.authType === 'oauth';
}

/**
 * Check if a platform requires an API key for authentication
 */
export function platformRequiresApiKey(platform: PlatformIntegration): boolean {
  return platform.authType === 'api_key';
}

/**
 * Get the OAuth URL for a platform
 */
export function getPlatformOAuthUrl(platform: PlatformIntegration): string {
  if (!platformSupportsOAuth(platform)) {
    return '';
  }
  
  // Use the oauth callback URL if provided, otherwise construct a default one
  if (platform.oauthCallbackUrl) {
    return platform.oauthCallbackUrl;
  }
  
  // Construct a default OAuth URL
  const redirectUri = encodeURIComponent(`${window.location.origin}/oauth/callback/${platform.slug}`);
  return `/api/platform-integrations/${platform.slug}/oauth?redirect_uri=${redirectUri}`;
}

/**
 * Get connected platforms count
 */
export function getConnectedPlatformsCount(platforms: PlatformIntegration[] | any): number {
  if (!platforms || !Array.isArray(platforms)) {
    return 0;
  }
  return platforms.filter(p => p.isConnected).length;
}

/**
 * Handle missing Teachable icon in react-icons/si
 * This is a temporary workaround until the icon library is updated
 * or we add a custom icon
 */
export function getTeachableLogoSvg(): string {
  return `<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.9949 2H6.00507C3.79223 2 2 3.79223 2 6.00507V17.9949C2 20.2078 3.79223 22 6.00507 22H17.9949C20.2078 22 22 20.2078 22 17.9949V6.00507C22 3.79223 20.2078 2 17.9949 2Z"/>
    <path d="M8.65873 11.1696H8.03185V14.0764H8.65873V11.1696Z"/>
    <path d="M14.5402 9.56177H14.0031V15.6842H14.5402V9.56177Z"/>
    <path d="M17.0244 10.1795H16.3975V15.0664H17.0244V10.1795Z"/>
    <path d="M11.6136 7.95398H11.0765V17.2919H11.6136V7.95398Z"/>
  </svg>`;
}

