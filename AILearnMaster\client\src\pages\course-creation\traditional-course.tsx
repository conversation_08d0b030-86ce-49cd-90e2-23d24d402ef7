import React, { useState, useCallback, useEffect } from "react";
import { useLocation } from "wouter";
import { HorizontalStepIndicator } from "@/components/course-creator/HorizontalStepIndicator";
import { CourseDetailsForm } from "@/components/course-creator/CourseDetailsForm";
import { CoursePublishForm } from "@/components/course-creator/CoursePublishForm";
import { ArrowLeft, Video, Brain, Mic, Image, Film, Settings, Play, CheckCircle, AlertCircle, Upload, Download, Eye, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";

type TraditionalCourseStep = 
  | "course-details"
  | "ai-structure"
  | "voice-settings"
  | "media-configuration"
  | "video-production"
  | "review-publish";

const TRADITIONAL_COURSE_STEPS: Record<TraditionalCourseStep, { 
  title: string; 
  description: string; 
  icon: React.ComponentType; 
  color: string;
}> = {
  "course-details": { 
    title: "Course Details", 
    description: "Basic course information and learning objectives",
    icon: Settings,
    color: "blue"
  },
  "ai-structure": { 
    title: "AI Structure Generation", 
    description: "Mistral AI creates your course outline",
    icon: Brain,
    color: "purple"
  },
  "voice-settings": { 
    title: "Voice Configuration", 
    description: "Configure Coqui TTS voice synthesis",
    icon: Mic,
    color: "green"
  },
  "media-configuration": { 
    title: "Media & Slides", 
    description: "Pexels/Pixabay media and Marp slide generation",
    icon: Image,
    color: "orange"
  },
  "video-production": { 
    title: "Video Assembly", 
    description: "FFmpeg professional video production",
    icon: Film,
    color: "red"
  },
  "review-publish": { 
    title: "Review & Publish", 
    description: "Final review and course publication",
    icon: Video,
    color: "indigo"
  }
};

interface CourseModule {
  id: string;
  title: string;
  description: string;
  lessons: CourseLesson[];
}

interface CourseLesson {
  id: string;
  title: string;
  content: string;
  script: string;
  duration: number;
  mediaAssets: string[];
  videoUrl?: string;
  slides?: string[];
}

interface GeneratedCourse {
  modules: CourseModule[];
  totalDuration: number;
  estimatedCompletion: string;
}

interface VoiceSettings {
  provider: 'coqui' | 'kokoro';
  voiceId: string;
  speed: number;
  pitch: number;
  volume: number;
}

interface MediaSettings {
  imageProvider: 'pexels' | 'pixabay';
  videoProvider: 'pexels' | 'pixabay';
  quality: 'standard' | 'high' | 'ultra';
  slideStyle: 'professional' | 'educational' | 'creative' | 'minimal';
}

export default function TraditionalCoursePage() {
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  
  const [currentStep, setCurrentStep] = useState<TraditionalCourseStep>("course-details");
  const [courseData, setCourseData] = useState({
    title: "",
    category: "",
    description: "",
    targetAudience: "",
    useAI: true,
  });
  
  const [generatedCourse, setGeneratedCourse] = useState<GeneratedCourse | null>(null);
  const [voiceSettings, setVoiceSettings] = useState<VoiceSettings>({
    provider: 'coqui',
    voiceId: 'tts_models/en/ljspeech/tacotron2-DDC',
    speed: 1.0,
    pitch: 1.0,
    volume: 1.0,
  });
  
  const [mediaSettings, setMediaSettings] = useState<MediaSettings>({
    imageProvider: 'pexels',
    videoProvider: 'pexels',
    quality: 'high',
    slideStyle: 'professional',
  });
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStage, setGenerationStage] = useState('');
  const [videoProgress, setVideoProgress] = useState<{ [key: string]: number }>({});

  const handleNext = useCallback(() => {
    const steps: TraditionalCourseStep[] = ["course-details", "ai-structure", "voice-settings", "media-configuration", "video-production", "review-publish"];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  }, [currentStep]);

  const handlePrevious = useCallback(() => {
    const steps: TraditionalCourseStep[] = ["course-details", "ai-structure", "voice-settings", "media-configuration", "video-production", "review-publish"];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  }, [currentStep]);

  const handleGenerateStructure = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);
    setGenerationStage('Analyzing course requirements...');

    try {
      // Simulate AI structure generation
      const response = await fetch('/api/ai/generate-structure', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(courseData)
      });

      if (!response.ok) {
        throw new Error('Failed to generate course structure');
      }

      const result = await response.json();
      setGeneratedCourse(result);
      
      toast({
        title: "Course Structure Generated",
        description: `Successfully created ${result.modules.length} modules`,
      });

      setTimeout(() => handleNext(), 1500);
    } catch (error: any) {
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate course structure",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
      setGenerationStage('');
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "course-details":
        return (
          <CourseDetailsForm
            onSubmit={(values) => {
              setCourseData({
                ...values,
                targetAudience: values.targetAudience || "All levels"
              });
              handleNext();
            }}
            defaultValues={courseData}
          />
        );

      case "ai-structure":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-purple-600" />
                  AI Structure Generation
                </CardTitle>
                <CardDescription>
                  Mistral AI will analyze your course details and create a comprehensive structure
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {!generatedCourse ? (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <h3 className="font-medium mb-2">Course Structure</h3>
                        <ul className="text-sm space-y-1">
                          <li>• Detailed course outline with modules</li>
                          <li>• Learning objectives for each section</li>
                          <li>• Logical content progression</li>
                        </ul>
                      </div>
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <h3 className="font-medium mb-2">Lesson Scripts</h3>
                        <ul className="text-sm space-y-1">
                          <li>• Engaging lesson narratives</li>
                          <li>• Clear explanations and examples</li>
                          <li>• Interactive elements</li>
                        </ul>
                      </div>
                    </div>

                    {isGenerating && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">{generationStage}</span>
                          <span className="font-medium">{generationProgress}%</span>
                        </div>
                        <Progress value={generationProgress} />
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Button onClick={handlePrevious} variant="outline">
                        Previous
                      </Button>
                      <Button 
                        onClick={handleGenerateStructure} 
                        className="flex-1"
                        disabled={isGenerating}
                      >
                        {isGenerating ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            Generating Structure...
                          </>
                        ) : (
                          <>
                            <Brain className="h-4 w-4 mr-2" />
                            Generate Course Structure
                          </>
                        )}
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="font-medium">Course structure generated successfully!</span>
                      </div>
                      
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h3 className="font-medium mb-2">Generated Course Overview</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Modules:</span> {generatedCourse.modules.length}
                          </div>
                          <div>
                            <span className="font-medium">Total Lessons:</span> {generatedCourse.modules.reduce((acc, mod) => acc + mod.lessons.length, 0)}
                          </div>
                          <div>
                            <span className="font-medium">Est. Duration:</span> {generatedCourse.estimatedCompletion}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button onClick={handlePrevious} variant="outline">
                        Previous
                      </Button>
                      <Button onClick={handleNext} className="flex-1">
                        Continue to Voice Settings
                      </Button>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        );

      case "voice-settings":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mic className="h-5 w-5 text-green-600" />
                  Voice Configuration
                </CardTitle>
                <CardDescription>
                  Configure Coqui TTS and Kokoro voice synthesis settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <Label>Voice Provider</Label>
                      <Select value={voiceSettings.provider} onValueChange={(value) => 
                        setVoiceSettings(prev => ({ ...prev, provider: value as 'coqui' | 'kokoro' }))
                      }>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="coqui">Coqui TTS (Recommended)</SelectItem>
                          <SelectItem value="kokoro">Kokoro TTS</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-3">
                      <Label>Voice Model</Label>
                      <Select value={voiceSettings.voiceId} onValueChange={(value) => 
                        setVoiceSettings(prev => ({ ...prev, voiceId: value }))
                      }>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="tts_models/en/ljspeech/tacotron2-DDC">English Female (Professional)</SelectItem>
                          <SelectItem value="tts_models/en/vctk/vits">English Multi-speaker</SelectItem>
                          <SelectItem value="tts_models/en/sam/tacotron-DDC">English Male (Natural)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <Label>Speed: {voiceSettings.speed}x</Label>
                      <Input
                        type="range"
                        min="0.5"
                        max="2.0"
                        step="0.1"
                        value={voiceSettings.speed}
                        onChange={(e) => setVoiceSettings(prev => ({ ...prev, speed: parseFloat(e.target.value) }))}
                      />
                    </div>
                    
                    <div className="space-y-3">
                      <Label>Volume: {voiceSettings.volume}x</Label>
                      <Input
                        type="range"
                        min="0.1"
                        max="2.0"
                        step="0.1"
                        value={voiceSettings.volume}
                        onChange={(e) => setVoiceSettings(prev => ({ ...prev, volume: parseFloat(e.target.value) }))}
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2">Voice Preview</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    Preview how your selected voice will sound
                  </p>
                  <Button variant="outline" size="sm">
                    <Play className="h-4 w-4 mr-2" />
                    Test Voice
                  </Button>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handlePrevious} variant="outline">Previous</Button>
                  <Button onClick={handleNext} className="flex-1">
                    Continue to Media Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "media-configuration":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Image className="h-5 w-5 text-orange-600" />
                  Media & Slides Configuration
                </CardTitle>
                <CardDescription>
                  Configure media sources and Marp slide generation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-semibold">Media Providers</h3>
                    
                    <div className="space-y-3">
                      <Label>Image Provider</Label>
                      <Select value={mediaSettings.imageProvider} onValueChange={(value) => 
                        setMediaSettings(prev => ({ ...prev, imageProvider: value as 'pexels' | 'pixabay' }))
                      }>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pexels">Pexels (Recommended)</SelectItem>
                          <SelectItem value="pixabay">Pixabay</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-3">
                      <Label>Media Quality</Label>
                      <Select value={mediaSettings.quality} onValueChange={(value) => 
                        setMediaSettings(prev => ({ ...prev, quality: value as 'standard' | 'high' | 'ultra' }))
                      }>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="standard">Standard (720p)</SelectItem>
                          <SelectItem value="high">High (1080p)</SelectItem>
                          <SelectItem value="ultra">Ultra (4K)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-semibold">Slide Styles</h3>
                    
                    <div className="grid grid-cols-2 gap-3">
                      {[
                        { id: 'professional', name: 'Professional', desc: 'Clean corporate design' },
                        { id: 'educational', name: 'Educational', desc: 'Academic-focused layout' },
                        { id: 'creative', name: 'Creative', desc: 'Vibrant and engaging' },
                        { id: 'minimal', name: 'Minimal', desc: 'Simple and clean' }
                      ].map((style) => (
                        <div 
                          key={style.id} 
                          className={`border rounded-lg p-3 cursor-pointer transition-all ${
                            mediaSettings.slideStyle === style.id ? 'border-orange-500 bg-orange-50' : 'hover:border-orange-300'
                          }`}
                          onClick={() => setMediaSettings(prev => ({ ...prev, slideStyle: style.id as any }))}
                        >
                          <div className="font-medium text-sm">{style.name}</div>
                          <div className="text-xs text-muted-foreground">{style.desc}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handlePrevious} variant="outline">
                    Previous
                  </Button>
                  <Button onClick={handleNext} className="flex-1">
                    Continue to Video Production
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "video-production":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Film className="h-5 w-5 text-red-600" />
                  Video Production Studio
                </CardTitle>
                <CardDescription>
                  FFmpeg professional video assembly and production
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {generatedCourse && (
                  <div className="space-y-4">
                    <h3 className="font-semibold">Course Modules</h3>
                    {generatedCourse.modules.map((module, index) => (
                      <div key={module.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium">{module.title}</h4>
                          <Badge variant="outline">{module.lessons.length} lessons</Badge>
                        </div>
                        
                        <div className="space-y-2">
                          {module.lessons.map((lesson) => (
                            <div key={lesson.id} className="flex items-center justify-between text-sm">
                              <span className="text-muted-foreground">{lesson.title}</span>
                              <div className="flex items-center gap-2">
                                {videoProgress[lesson.id] ? (
                                  <Progress value={videoProgress[lesson.id]} className="w-20" />
                                ) : (
                                  <Badge variant="secondary">Ready</Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                <div className="bg-red-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2">Video Generation Process</h3>
                  <ul className="text-sm space-y-1">
                    <li>• TTS audio generation for all lessons</li>
                    <li>• Media asset compilation and optimization</li>
                    <li>• Marp slide rendering and integration</li>
                    <li>• FFmpeg video assembly and encoding</li>
                  </ul>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handlePrevious} variant="outline">
                    Previous
                  </Button>
                  <Button onClick={handleNext} className="flex-1">
                    Start Video Generation
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "review-publish":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Video className="h-5 w-5 text-indigo-600" />
                  Review & Publish Course
                </CardTitle>
                <CardDescription>
                  Final review and course publication
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <CoursePublishForm 
                  courseData={{
                    ...courseData,
                    generatedCourse,
                    voiceSettings,
                    mediaSettings
                  }}
                  onPublish={(publishData) => {
                    toast({
                      title: "Course Published!",
                      description: "Your course has been published successfully.",
                    });
                    setLocation('/dashboard');
                  }}
                />
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => setLocation('/dashboard')}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Enhanced Traditional Course Generator
          </h1>
          <p className="text-gray-600">
            Create professional video courses with AI-powered content generation, voice synthesis, and automated video production
          </p>
        </div>

        <div className="mb-8">
          <HorizontalStepIndicator
            steps={Object.entries(TRADITIONAL_COURSE_STEPS).map(([key, config]) => {
              const IconComponent = config.icon;
              return {
                id: key,
                title: config.title,
                description: config.description,
                icon: <IconComponent className="h-4 w-4" />,
              };
            })}
            activeStep={Object.keys(TRADITIONAL_COURSE_STEPS).indexOf(currentStep)}
            completedSteps={Object.keys(TRADITIONAL_COURSE_STEPS).slice(0, Object.keys(TRADITIONAL_COURSE_STEPS).indexOf(currentStep)).map((_, index) => index)}
            onStepClick={(stepIndex) => {
              // Allow navigation to completed steps only
              const steps: TraditionalCourseStep[] = ["course-details", "ai-structure", "voice-settings", "media-configuration", "video-production", "review-publish"];
              const currentIndex = steps.indexOf(currentStep);
              if (stepIndex <= currentIndex) {
                setCurrentStep(steps[stepIndex]);
              }
            }}
          />
        </div>

        {renderStepContent()}
      </div>
    </div>
  );
}