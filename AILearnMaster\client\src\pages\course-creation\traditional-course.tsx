import React from "react";
import { useLocation } from "wouter";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { WorkflowSelector } from "@/components/workflows/WorkflowSelector";

export default function TraditionalCoursePage() {
  const [, setLocation] = useLocation();

  const handleCourseComplete = (courseId: number) => {
    setLocation(`/my-courses/${courseId}`);
  };

  const handleCancel = () => {
    setLocation('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => setLocation('/dashboard')}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>

        <WorkflowSelector
          onComplete={handleCourseComplete}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
}

