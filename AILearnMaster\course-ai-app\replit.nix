{ pkgs }: {
  deps = [
    pkgs.nodejs_20
    pkgs.python312Full
    pkgs.python312Packages.pip
    pkgs.ffmpeg
    pkgs.imagemagick
    pkgs.git
    pkgs.curl
    pkgs.wget
    pkgs.postgresql
  ];
  
  env = {
    PYTHON_LD_LIBRARY_PATH = pkgs.lib.makeLibraryPath [
      pkgs.stdenv.cc.cc.lib
      pkgs.zlib
      pkgs.ffi
    ];
    PYTHONPATH = "${pkgs.python312}/lib/python3.12/site-packages";
    NODE_ENV = "development";
  };
}