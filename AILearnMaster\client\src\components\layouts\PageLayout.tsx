import React, { ReactNode } from "react";
import { <PERSON> } from "wouter";
import { ArrowUp } from "lucide-react";

interface PageLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
}

export function PageLayout({ children, title, description }: PageLayoutProps) {
  return (
    <div className="min-h-screen bg-white">
      {/* Header/Navbar */}
      <header className="border-b bg-white shadow-sm">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex justify-start lg:w-0 lg:flex-1">
              <Link href="/" className="flex items-center space-x-2 cursor-pointer">
                <div className="w-8 h-8 rounded-md bg-primary flex items-center justify-center">
                  <i className="ri-vidicon-line text-white"></i>
                </div>
                <span className="font-bold text-xl text-gray-900"><PERSON><PERSON><PERSON></span>
              </Link>
            </div>
            <nav className="hidden md:flex space-x-10">
              <Link href="/product/features" className="text-base font-medium text-gray-700 hover:text-gray-900">
                Features
              </Link>
              <Link href="/product/how-it-works" className="text-base font-medium text-gray-700 hover:text-gray-900">
                How It Works
              </Link>
              <Link href="/product/pricing" className="text-base font-medium text-gray-700 hover:text-gray-900">
                Pricing
              </Link>
              <Link href="/product/testimonials" className="text-base font-medium text-gray-700 hover:text-gray-900">
                Testimonials
              </Link>
            </nav>
            <div className="hidden md:flex items-center justify-end md:flex-1 lg:w-0 space-x-4">
              <Link href="/auth" className="whitespace-nowrap text-base font-medium text-gray-700 hover:text-gray-900 cursor-pointer">
                Sign in
              </Link>
              <Link href="/auth" className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark">
                Get started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Page Content */}
      <main className="py-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-10">
            <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">
              {title}
            </h1>
            {description && (
              <p className="mt-4 text-xl text-gray-600 mx-auto max-w-2xl">
                {description}
              </p>
            )}
          </div>
          <div className="prose prose-lg prose-primary mx-auto">
            {children}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
          <div className="xl:grid xl:grid-cols-4 xl:gap-8">
            <div className="grid grid-cols-2 gap-8 xl:col-span-4">
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">
                    PRODUCT
                  </h3>
                  <ul role="list" className="mt-4 space-y-4">
                    <li>
                      <Link href="/product/features" className="text-base text-gray-400 hover:text-gray-300">
                        Features
                      </Link>
                    </li>
                    <li>
                      <Link href="/product/pricing" className="text-base text-gray-400 hover:text-gray-300">
                        Pricing
                      </Link>
                    </li>
                    <li>
                      <Link href="/product/integrations" className="text-base text-gray-400 hover:text-gray-300">
                        Integrations
                      </Link>
                    </li>
                    <li>
                      <Link href="/product/marketplace" className="text-base text-gray-400 hover:text-gray-300">
                        Marketplace
                      </Link>
                    </li>
                  </ul>
                </div>
                <div className="mt-12 md:mt-0">
                  <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">
                    SUPPORT
                  </h3>
                  <ul role="list" className="mt-4 space-y-4">
                    <li>
                      <Link href="/support/documentation" className="text-base text-gray-400 hover:text-gray-300">
                        Documentation
                      </Link>
                    </li>
                    <li>
                      <Link href="/support/tutorials" className="text-base text-gray-400 hover:text-gray-300">
                        Tutorials
                      </Link>
                    </li>
                    <li>
                      <Link href="/support/help-center" className="text-base text-gray-400 hover:text-gray-300">
                        Help Center
                      </Link>
                    </li>
                    <li>
                      <Link href="/support/contact-us" className="text-base text-gray-400 hover:text-gray-300">
                        Contact Us
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">
                    COMPANY
                  </h3>
                  <ul role="list" className="mt-4 space-y-4">
                    <li>
                      <Link href="/company/about-us" className="text-base text-gray-400 hover:text-gray-300">
                        About Us
                      </Link>
                    </li>
                    <li>
                      <Link href="/company/blog" className="text-base text-gray-400 hover:text-gray-300">
                        Blog
                      </Link>
                    </li>
                    <li>
                      <Link href="/company/careers" className="text-base text-gray-400 hover:text-gray-300">
                        Careers
                      </Link>
                    </li>
                    <li>
                      <Link href="/company/press" className="text-base text-gray-400 hover:text-gray-300">
                        Press
                      </Link>
                    </li>
                  </ul>
                </div>
                <div className="mt-12 md:mt-0">
                  <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">
                    LEGAL
                  </h3>
                  <ul role="list" className="mt-4 space-y-4">
                    <li>
                      <Link href="/legal/privacy-policy" className="text-base text-gray-400 hover:text-gray-300">
                        Privacy Policy
                      </Link>
                    </li>
                    <li>
                      <Link href="/legal/terms-of-service" className="text-base text-gray-400 hover:text-gray-300">
                        Terms of Service
                      </Link>
                    </li>
                    <li>
                      <Link href="/legal/cookie-policy" className="text-base text-gray-400 hover:text-gray-300">
                        Cookie Policy
                      </Link>
                    </li>
                    <li>
                      <Link href="/legal/gdpr" className="text-base text-gray-400 hover:text-gray-300">
                        GDPR
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8">
            <div className="flex items-center justify-center mb-4">
              <button 
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="flex items-center justify-center p-2 bg-primary/20 hover:bg-primary/30 rounded-full text-white transition-colors duration-200"
                title="Back to top"
              >
                <ArrowUp className="h-5 w-5" />
              </button>
            </div>
            <p className="text-base text-gray-400 xl:text-center">
              &copy; {new Date().getFullYear()} Koursia. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}