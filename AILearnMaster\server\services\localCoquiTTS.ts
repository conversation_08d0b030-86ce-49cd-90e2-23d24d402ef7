
import { spawn, ChildProcess } from 'child_process';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

interface CoquiTTSConfig {
  model: string;
  vocoder?: string;
  speakerId?: string;
  languageId?: string;
  emotion?: string;
  speed?: number;
  pitch?: number;
}

interface CoquiVoice {
  id: string;
  name: string;
  language: string;
  gender?: string;
  description?: string;
}

// Available Coqui TTS models and voices
const COQUI_MODELS = [
  {
    id: 'tts_models/en/ljspeech/tacotron2-DDC',
    name: 'LJSpeech Tacotron2',
    language: 'en',
    description: 'High quality English TTS model'
  },
  {
    id: 'tts_models/en/ljspeech/glow-tts',
    name: 'LJSpeech Glow-TTS',
    language: 'en',
    description: 'Fast English TTS model'
  },
  {
    id: 'tts_models/en/sam/tacotron-DDC',
    name: 'SAM Tacotron',
    language: 'en',
    description: 'Multi-speaker English model'
  },
  {
    id: 'tts_models/multilingual/multi-dataset/your_tts',
    name: 'YourTTS Multilingual',
    language: 'multilingual',
    description: 'Multilingual voice cloning model'
  }
];

const DEFAULT_VOICES: CoquiVoice[] = [
  { id: 'ljspeech', name: 'LJSpeech Female', language: 'en', gender: 'female' },
  { id: 'sam_male', name: 'SAM Male', language: 'en', gender: 'male' },
  { id: 'sam_female', name: 'SAM Female', language: 'en', gender: 'female' },
  { id: 'yourtts_default', name: 'YourTTS Default', language: 'en', gender: 'neutral' }
];

class LocalCoquiTTSService {
  private isInitialized = false;
  private availableModels: string[] = [];
  private currentModel = 'tts_models/en/ljspeech/tacotron2-DDC';
  private outputDir = path.join(process.cwd(), 'temp');

  constructor() {
    this.ensureOutputDir();
  }

  private ensureOutputDir() {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * Initialize Coqui TTS and check available models
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('Initializing local Coqui TTS...');
      
      // Check if TTS is installed
      const isInstalled = await this.checkInstallation();
      if (!isInstalled) {
        throw new Error('Coqui TTS is not installed');
      }

      // List available models
      this.availableModels = await this.listAvailableModels();
      console.log(`Found ${this.availableModels.length} available TTS models`);

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize Coqui TTS:', error);
      return false;
    }
  }

  /**
   * Check if Coqui TTS is properly installed
   */
  private async checkInstallation(): Promise<boolean> {
    return new Promise((resolve) => {
      const process = spawn('python', ['-c', 'import TTS; print("TTS installed")'], {
        stdio: 'pipe'
      });

      process.on('close', (code) => {
        resolve(code === 0);
      });

      process.on('error', () => {
        resolve(false);
      });
    });
  }

  /**
   * List available TTS models
   */
  private async listAvailableModels(): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const process = spawn('tts', ['--list_models'], {
        stdio: 'pipe'
      });

      let output = '';
      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          // Parse the output to extract model names
          const models = output
            .split('\n')
            .filter(line => line.includes('tts_models/'))
            .map(line => line.trim());
          resolve(models);
        } else {
          reject(new Error('Failed to list TTS models'));
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Generate speech from text using Coqui TTS
   */
  async generateSpeech(
    text: string,
    config: Partial<CoquiTTSConfig> = {}
  ): Promise<{ audioPath: string; duration: number }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const fileName = `coqui_${uuidv4()}.wav`;
    const outputPath = path.join(this.outputDir, fileName);

    const ttsConfig: CoquiTTSConfig = {
      model: config.model || this.currentModel,
      speed: config.speed || 1.0,
      ...config
    };

    try {
      await this.runTTSCommand(text, outputPath, ttsConfig);
      
      // Calculate approximate duration
      const duration = this.estimateDuration(text, ttsConfig.speed || 1.0);
      
      return {
        audioPath: outputPath,
        duration
      };
    } catch (error) {
      console.error('Failed to generate speech with Coqui TTS:', error);
      throw error;
    }
  }

  /**
   * Run the TTS command with specified parameters
   */
  private async runTTSCommand(
    text: string,
    outputPath: string,
    config: CoquiTTSConfig
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const args = [
        '--text', text,
        '--model_name', config.model,
        '--out_path', outputPath
      ];

      // Add optional parameters
      if (config.speakerId) {
        args.push('--speaker_idx', config.speakerId);
      }
      if (config.languageId) {
        args.push('--language_idx', config.languageId);
      }
      if (config.emotion) {
        args.push('--emotion', config.emotion);
      }

      console.log('Running TTS command:', 'tts', args.join(' '));

      const process = spawn('tts', args, {
        stdio: 'pipe'
      });

      let errorOutput = '';
      process.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0 && fs.existsSync(outputPath)) {
          resolve();
        } else {
          reject(new Error(`TTS command failed with code ${code}: ${errorOutput}`));
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * Estimate audio duration based on text length and speed
   */
  private estimateDuration(text: string, speed: number): number {
    // Rough estimate: average reading speed is ~150 words per minute
    const wordCount = text.split(/\s+/).length;
    const baseDuration = (wordCount / 150) * 60; // seconds
    return Math.max(1, Math.round(baseDuration / speed));
  }

  /**
   * Get available voices
   */
  getAvailableVoices(): CoquiVoice[] {
    return DEFAULT_VOICES;
  }

  /**
   * Get available models
   */
  getAvailableModels() {
    return COQUI_MODELS;
  }

  /**
   * Set the current model
   */
  setModel(modelId: string): boolean {
    if (this.availableModels.includes(modelId) || COQUI_MODELS.some(m => m.id === modelId)) {
      this.currentModel = modelId;
      return true;
    }
    return false;
  }

  /**
   * Check if the service is ready
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Download and install a specific model
   */
  async downloadModel(modelId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      console.log(`Downloading TTS model: ${modelId}`);
      
      const process = spawn('tts', ['--model_name', modelId, '--text', 'test'], {
        stdio: 'pipe'
      });

      process.on('close', (code) => {
        if (code === 0) {
          console.log(`Model ${modelId} downloaded successfully`);
          resolve(true);
        } else {
          console.error(`Failed to download model ${modelId}`);
          resolve(false);
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }
}

// Export singleton instance
export const localCoquiTTS = new LocalCoquiTTSService();

// Export types
export type { CoquiTTSConfig, CoquiVoice };
