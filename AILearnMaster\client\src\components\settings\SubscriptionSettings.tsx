import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Label } from "@/components/ui/label";
import { useToast } from '@/hooks/use-toast';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { User, PaymentMethod, BillingHistory, UserStats } from '@/types';
import { ExternalLink, CreditCard, CheckCircle, XCircle, RefreshCw, AlertTriangle, PlusCircle, BarChart3 } from 'lucide-react';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Link } from "wouter";
import PaymentMethodDialog from './PaymentMethodDialog';
import PlansDialog from './PlansDialog';

interface SubscriptionSettingsProps {
  user: User | undefined;
}

type PlanDetail = {
  name: string;
  price: string;
  features: string[];
  color: string;
};

type PlanDetailsMap = {
  [key: string]: PlanDetail;
};

const PLAN_DETAILS: PlanDetailsMap = {
  'starter': {
    name: 'Starter',
    price: '$49/month',
    features: [
      'Basic AI course creation',
      '5 courses',
      'Limited AI credits',
      'Standard support'
    ],
    color: 'bg-blue-100 text-blue-800 hover:bg-blue-200/80'
  },
  'pro': {
    name: 'Pro',
    price: '$129/month',
    features: [
      'Advanced AI features',
      'Unlimited courses',
      '500 AI credits/month',
      'Priority support',
      'Custom branding'
    ],
    color: 'bg-purple-100 text-purple-800 hover:bg-purple-200/80'
  },
  'business': {
    name: 'Business',
    price: '$299/month',
    features: [
      'All Pro features',
      'Team collaboration',
      '1500 AI credits/month',
      'Advanced analytics',
      'API access',
      'Dedicated support'
    ],
    color: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200/80'
  },
  'enterprise': {
    name: 'Enterprise',
    price: 'Custom',
    features: [
      'All Business features',
      'Custom solutions',
      'Unlimited AI credits',
      'White-label option',
      'SLA guarantees',
      'Dedicated account manager'
    ],
    color: 'bg-slate-100 text-slate-800 hover:bg-slate-200/80'
  },
};

export default function SubscriptionSettings({ user }: SubscriptionSettingsProps) {
  const { toast } = useToast();
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [showAddPaymentDialog, setShowAddPaymentDialog] = useState(false);
  const [showPlansDialog, setShowPlansDialog] = useState(false);
  const currentPlan = user?.plan?.toLowerCase() || 'starter';

  // Mock data for payment methods and billing history
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [billingHistory, setBillingHistory] = useState<BillingHistory[]>([]);

  // Fetch payment methods
  const { data: paymentMethodsData, isLoading: isLoadingPaymentMethods } = useQuery({
    queryKey: ['/api/billing/payment-methods'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/billing/payment-methods');
        return await response.json();
      } catch (error) {
        // Return mock data since API might not be implemented yet
        return [
          {
            id: "pm_mock_1",
            userId: user?.id || 0,
            type: 'visa',
            lastFour: '4242',
            expiryDate: '04/2028',
            isDefault: true
          }
        ] as PaymentMethod[];
      }
    },
    enabled: !!user?.id
  });

  // Fetch billing history
  const { data: billingHistoryData, isLoading: isLoadingBillingHistory } = useQuery({
    queryKey: ['/api/billing/history'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/billing/history');
        return await response.json();
      } catch (error) {
        // Return mock data since API might not be implemented yet
        return [
          {
            id: "inv_mock_1",
            userId: user?.id || 0,
            amount: 129,
            currency: 'USD',
            description: 'Pro Plan - Monthly',
            status: 'completed',
            transactionDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
          },
          {
            id: "inv_mock_2",
            userId: user?.id || 0,
            amount: 129,
            currency: 'USD',
            description: 'Pro Plan - Monthly',
            status: 'completed',
            transactionDate: new Date(Date.now() - 33 * 24 * 60 * 60 * 1000)
          }
        ] as BillingHistory[];
      }
    },
    enabled: !!user?.id
  });

  // Update state when data is loaded
  useEffect(() => {
    if (paymentMethodsData) {
      setPaymentMethods(paymentMethodsData);
    }
    if (billingHistoryData) {
      setBillingHistory(billingHistoryData);
    }
  }, [paymentMethodsData, billingHistoryData]);

  // Mutation for changing plan
  const changePlanMutation = useMutation({
    mutationFn: async (planId: string) => {
      const response = await apiRequest('POST', '/api/billing/change-plan', { planId });
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/auth/me'] });
      toast({
        title: 'Plan updated',
        description: 'Your subscription plan has been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error updating plan',
        description: error.message || 'Something went wrong. Please try again.',
      });
    }
  });

  // Mutation for canceling subscription
  const cancelSubscriptionMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('POST', '/api/billing/cancel-subscription', {});
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/auth/me'] });
      setShowCancelConfirm(false);
      toast({
        title: 'Subscription canceled',
        description: 'Your subscription has been canceled. You will have access until the end of your billing period.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error canceling subscription',
        description: error.message || 'Something went wrong. Please try again.',
      });
    }
  });

  // Format date for display
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  // Format currency for display
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  // Handle opening the payment method dialog
  const handleAddPaymentMethod = () => {
    setShowAddPaymentDialog(true);
  };

  // Handle payment method dialog close
  const handleClosePaymentDialog = () => {
    setShowAddPaymentDialog(false);
  };

  // Handle successful payment method addition
  const handlePaymentMethodAdded = () => {
    queryClient.invalidateQueries({ queryKey: ['/api/billing/payment-methods'] });
    toast({
      title: 'Payment method added',
      description: 'Your payment method has been added successfully.',
    });
  };

  // Handle opening the plans dialog
  const handleOpenPlansDialog = () => {
    setShowPlansDialog(true);
  };

  // Handle plans dialog close
  const handleClosePlansDialog = () => {
    setShowPlansDialog(false);
  };

  // Handle successful plan selection
  const handlePlanSelected = () => {
    setShowPlansDialog(false);
    queryClient.invalidateQueries({ queryKey: ['/api/auth/me'] });
    toast({
      title: 'Plan updated',
      description: 'Your subscription plan has been updated successfully.',
    });
  };
  
  return (
    <div className="space-y-8">
      {/* Payment Method Dialog */}
      {showAddPaymentDialog && (
        <PaymentMethodDialog 
          isOpen={showAddPaymentDialog}
          onClose={handleClosePaymentDialog}
          onSuccess={handlePaymentMethodAdded}
        />
      )}

      {/* Plans Dialog */}
      <PlansDialog 
        isOpen={showPlansDialog}
        onClose={handleClosePlansDialog}
        currentPlan={currentPlan}
        onPlanSelected={handlePlanSelected}
      />
      
      {/* Current Plan */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Current Plan</CardTitle>
              <CardDescription>
                Your current subscription plan and details
              </CardDescription>
            </div>
            {currentPlan && PLAN_DETAILS[currentPlan] && (
              <Badge className={PLAN_DETAILS[currentPlan].color}>
                {PLAN_DETAILS[currentPlan].name}
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg border border-border p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold text-lg">{PLAN_DETAILS[currentPlan]?.name || 'Free'} Plan</h3>
                <p className="text-muted-foreground mt-1">{PLAN_DETAILS[currentPlan]?.price || 'Free'}</p>
                
                {user?.stripeSubscriptionId && (
                  <div className="mt-4 flex items-center text-sm text-muted-foreground">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Active subscription
                  </div>
                )}
              </div>
              
              <div>
                <h4 className="font-medium text-sm mb-2">Includes:</h4>
                <ul className="space-y-2">
                  {PLAN_DETAILS[currentPlan]?.features.map((feature: string, index: number) => (
                    <li key={index} className="flex items-start text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              {/* Upgrade/Change Plan Button */}
              <Button 
                onClick={handleOpenPlansDialog}
                className="bg-gradient-to-r from-primary to-indigo-600 hover:from-primary/90 hover:to-indigo-500"
              >
                {currentPlan === 'enterprise' 
                  ? 'Contact Sales' 
                  : currentPlan && currentPlan !== 'free' 
                  ? 'Change Plan' 
                  : 'Upgrade Plan'}
              </Button>
              
              {/* Compare Plans Button */}
              <Button variant="outline" asChild>
                <Link href="/pricing">
                  Compare Plans
                </Link>
              </Button>
              
              {/* Cancel Subscription Button & Dialog */}
              {user?.stripeSubscriptionId && (
                <AlertDialog open={showCancelConfirm} onOpenChange={setShowCancelConfirm}>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      onClick={() => setShowCancelConfirm(true)}
                    >
                      Cancel Subscription
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure you want to cancel?</AlertDialogTitle>
                      <AlertDialogDescription>
                        Your subscription will be canceled, but you'll still have access until the end of your current billing period. After that, you'll lose access to premium features.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel onClick={() => setShowCancelConfirm(false)}>
                        Keep Subscription
                      </AlertDialogCancel>
                      <AlertDialogAction 
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        onClick={() => cancelSubscriptionMutation.mutate()}
                        disabled={cancelSubscriptionMutation.isPending}
                      >
                        {cancelSubscriptionMutation.isPending ? 'Canceling...' : 'Yes, Cancel'}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Methods */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <CardDescription>
            Manage your saved payment methods
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isLoadingPaymentMethods ? (
            <div className="flex items-center justify-center py-6">
              <RefreshCw className="h-5 w-5 animate-spin text-muted-foreground" />
            </div>
          ) : paymentMethods.length > 0 ? (
            <div className="space-y-4">
              {paymentMethods.map((method) => (
                <div key={method.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-md flex items-center justify-center bg-slate-100">
                      <CreditCard className="h-5 w-5 text-slate-600" />
                    </div>
                    <div>
                      <div className="font-medium">
                        {method.type.charAt(0).toUpperCase() + method.type.slice(1)} •••• {method.lastFour}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Expires {method.expiryDate}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {method.isDefault && (
                      <Badge variant="outline" className="mr-2">Default</Badge>
                    )}
                    <Button variant="ghost" size="sm">
                      Edit
                    </Button>
                    {!method.isDefault && (
                      <Button variant="ghost" size="sm">
                        Remove
                      </Button>
                    )}
                  </div>
                </div>
              ))}
              <Button 
                variant="outline" 
                className="w-full"
                onClick={handleAddPaymentMethod}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Payment Method
              </Button>
            </div>
          ) : (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium text-lg mb-2">No payment methods</h3>
              <p className="text-muted-foreground mb-4">You haven't added any payment methods yet.</p>
              <Button onClick={handleAddPaymentMethod}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Payment Method
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>
            View and download your past invoices
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoadingBillingHistory ? (
            <div className="flex items-center justify-center py-6">
              <RefreshCw className="h-5 w-5 animate-spin text-muted-foreground" />
            </div>
          ) : billingHistory.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {billingHistory.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{formatDate(new Date(item.transactionDate))}</TableCell>
                    <TableCell>{item.description}</TableCell>
                    <TableCell>{formatCurrency(item.amount, item.currency)}</TableCell>
                    <TableCell>
                      {item.status === 'completed' ? (
                        <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-200/80">Paid</Badge>
                      ) : item.status === 'failed' ? (
                        <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-200/80">Failed</Badge>
                      ) : (
                        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200/80">Pending</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" className="flex items-center gap-1">
                        <span>Invoice</span>
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium text-lg mb-2">No billing history</h3>
              <p className="text-muted-foreground mb-4">You don't have any billing history yet.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}