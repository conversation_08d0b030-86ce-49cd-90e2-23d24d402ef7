/**
 * Mistral AI service running on Modal GPU infrastructure
 * Handles all AI generation tasks using Mistral models
 */

import axios from 'axios';

// Modal configuration - these should be environment variables
const MODAL_BASE_URL = process.env.MODAL_BASE_URL;
const MODAL_API_KEY = process.env.MODAL_API_KEY;

// Check if Modal is available
const MODAL_AVAILABLE = MODAL_BASE_URL && MODAL_API_KEY;

interface MistralResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

/**
 * Generate course structure using Mistral AI on Modal GPU
 */
export async function generateCourseStructure({
  title,
  description,
  category,
  targetAudience,
  useAI = true
}: {
  title: string;
  description: string;
  category?: string;
  targetAudience?: string;
  useAI?: boolean;
}) {
  if (!useAI || !MODAL_AVAILABLE) {
    // Return basic template structure if AI is disabled or Modal not available
    console.log('Using fallback course structure generation');
    return generateBasicCourseStructure(title, description);
  }

  try {
    const prompt = `Create a comprehensive course structure for "${title}".
    
Course Details:
- Title: ${title}
- Description: ${description}
- Category: ${category || 'General'}
- Target Audience: ${targetAudience || 'All levels'}

Please generate a detailed course structure with:
1. 4-6 modules covering the topic comprehensively
2. Each module should have 3-4 lessons
3. Each lesson should have a title, description, estimated duration, and type (video/interactive/text)
4. Include learning objectives and prerequisites
5. Estimate total course duration

Return the response as a JSON object with this structure:
{
  "id": timestamp,
  "title": "course title",
  "description": "course description",
  "category": "category",
  "targetAudience": "target audience",
  "totalDuration": "estimated total time",
  "modules": [
    {
      "id": number,
      "title": "module title",
      "description": "module description",
      "lessons": [
        {
          "id": number,
          "title": "lesson title",
          "description": "lesson description",
          "duration": "duration",
          "type": "video|interactive|text"
        }
      ]
    }
  ],
  "estimatedCompletionTime": "time estimate",
  "difficulty": "difficulty level",
  "prerequisites": "prerequisites",
  "learningObjectives": ["objective1", "objective2", ...]
}`;

    const response = await axios.post(`${MODAL_BASE_URL}/mistral/generate`, {
      prompt,
      max_tokens: 2000,
      temperature: 0.7,
      model: 'mistral-7b-instruct'
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    const mistralResponse: MistralResponse = response.data;
    const content = mistralResponse.choices[0]?.message?.content;
    
    if (!content) {
      console.warn('No content received from Mistral, falling back to template');
      return generateBasicCourseStructure(title, description);
    }

    // Parse JSON response from Mistral
    try {
      const parsedContent = JSON.parse(content);
      return parsedContent;
    } catch (parseError) {
      console.warn('Failed to parse Mistral response as JSON, falling back to template');
      return generateBasicCourseStructure(title, description);
    }

  } catch (error) {
    console.error('Error calling Mistral Modal service:', error);
    
    // Fallback to basic structure if Modal service is unavailable
    return generateBasicCourseStructure(title, description);
  }
}

/**
 * Generate course description using Mistral AI
 */
export async function generateCourseDescription({
  title,
  category,
  targetAudience
}: {
  title: string;
  category?: string;
  targetAudience?: string;
}) {
  if (!MODAL_AVAILABLE) {
    console.log('Modal not available, using fallback description generation');
    return `Learn the fundamentals of ${title} in this comprehensive course designed for ${targetAudience || 'learners of all levels'}. Master key concepts, practical applications, and industry best practices through structured lessons and hands-on exercises.`;
  }

  try {
    const response = await axios.post(`${MODAL_BASE_URL}/course-description`, {
      title,
      category,
      targetAudience
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    return response.data.description || `Learn the fundamentals of ${title} in this comprehensive course designed for ${targetAudience || 'learners of all levels'}.`;

  } catch (error) {
    console.error('Error generating course description with Mistral:', error);
    return `Learn the fundamentals of ${title} in this comprehensive course designed for ${targetAudience || 'learners of all levels'}.`;
  }
}

/**
 * Generate lesson scripts using Mistral AI
 */
export async function generateLessonScripts({
  courseTitle,
  moduleTitle,
  lessonTitle,
  lessonDescription,
  duration,
  voiceStyle = 'professional'
}: {
  courseTitle: string;
  moduleTitle: string;
  lessonTitle: string;
  lessonDescription: string;
  duration: string;
  voiceStyle?: string;
}) {
  if (!MODAL_AVAILABLE) {
    console.log('Modal not available, using fallback lesson script generation');
    
    // Generate a comprehensive script template
    const script = `# ${lessonTitle}

## Introduction
Welcome to this lesson on ${lessonTitle}. I'm excited to guide you through ${lessonDescription}.

This lesson is part of the ${moduleTitle} module in our ${courseTitle} course. By the end of this lesson, you'll have a solid understanding of the key concepts we'll cover.

## Learning Objectives
In this lesson, you will:
- Understand the fundamental concepts of ${lessonTitle.toLowerCase()}
- Learn practical applications and real-world examples
- Gain hands-on experience through interactive elements
- Build confidence in applying these concepts

## Main Content

### Key Concept 1: Understanding the Basics
Let's start by exploring what ${lessonTitle.toLowerCase()} really means. ${lessonDescription}

This concept is important because it forms the foundation for everything else we'll learn in this module.

### Key Concept 2: Practical Applications
Now that we understand the basics, let's look at how this applies in real-world scenarios.

Here are some practical examples:
- Example 1: A common use case you'll encounter frequently
- Example 2: An advanced application that builds on the basics
- Example 3: A creative approach that shows the versatility of these concepts

### Key Concept 3: Best Practices
To help you succeed, here are some important best practices to keep in mind:
- Always start with the fundamentals before moving to advanced topics
- Practice regularly to reinforce your learning
- Don't be afraid to experiment and make mistakes - that's how we learn

## Summary and Next Steps
Let's recap what we've covered in this lesson:
- We explored the fundamental concepts of ${lessonTitle.toLowerCase()}
- We saw practical applications and real-world examples
- We learned important best practices to guide our work

In our next lesson, we'll build on these concepts and dive deeper into more advanced topics.

## Call to Action
Before moving on, take a moment to:
1. Review the key concepts we covered
2. Think about how you might apply these ideas in your own work
3. Practice with the examples we discussed

Remember, learning is a journey, and every step forward builds on what you've already accomplished. You're doing great!

Thank you for joining me in this lesson. I'll see you in the next one!`;

    return script;
  }

  try {
    const response = await axios.post(`${MODAL_BASE_URL}/lesson-script`, {
      courseTitle,
      moduleTitle,
      lessonTitle,
      lessonDescription,
      duration,
      voiceStyle
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    return response.data.script || `Welcome to ${lessonTitle}. In this lesson, we'll explore ${lessonDescription}. Let's begin our journey into this important topic.`;

  } catch (error) {
    console.error('Error generating lesson script with Mistral:', error);
    return `Welcome to ${lessonTitle}. In this lesson, we'll explore ${lessonDescription}. Let's begin our journey into this important topic.`;
  }
}

/**
 * Generate avatar script using Mistral AI
 */
export async function generateAvatarScript({
  title,
  description,
  topic,
  tone = 'professional',
  duration = '30 seconds'
}: {
  title: string;
  description: string;
  topic?: string;
  tone?: string;
  duration?: string;
}) {
  if (!MODAL_AVAILABLE) {
    console.log('Modal not available, using fallback avatar script generation');
    return `Hello and welcome to ${title}. ${description} Today we'll be exploring ${topic || 'this fascinating topic'}. Let's get started with this ${tone} presentation that will take approximately ${duration}.`;
  }

  try {
    const response = await axios.post(`${MODAL_BASE_URL}/lesson-script`, {
      courseTitle: title,
      lessonTitle: topic || title,
      lessonDescription: description,
      duration,
      voiceStyle: tone
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    return response.data.script || `Hello and welcome to ${title}. ${description} Today we'll be exploring ${topic || 'this fascinating topic'}. Let's get started with this ${tone} presentation that will take approximately ${duration}.`;

  } catch (error) {
    console.error('Error generating avatar script with Mistral:', error);
    return `Hello and welcome to ${title}. ${description} Today we'll be exploring ${topic || 'this fascinating topic'}. Let's get started with this ${tone} presentation that will take approximately ${duration}.`;
  }
}

/**
 * Generate target audience using Mistral AI
 */
export async function generateTargetAudience({
  title,
  category,
  description
}: {
  title: string;
  category?: string;
  description?: string;
}) {
  if (!MODAL_AVAILABLE) {
    console.log('Modal not available, using fallback target audience generation');
    const categoryAudience = {
      'technology': 'aspiring developers and tech professionals',
      'business': 'entrepreneurs and business professionals', 
      'design': 'creative professionals and designers',
      'marketing': 'marketers and business owners',
      'education': 'educators and students',
      'health': 'health professionals and wellness enthusiasts'
    };
    
    const audienceType = categoryAudience[category?.toLowerCase() as keyof typeof categoryAudience] || 'learners';
    return `This course is designed for ${audienceType} who want to master ${title}. Perfect for beginners to intermediate level students seeking practical skills they can apply immediately in real-world scenarios.`;
  }

  try {
    const response = await axios.post(`${MODAL_BASE_URL}/target-audience`, {
      title,
      category,
      description
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    return response.data.targetAudience || `This course is designed for learners who want to master ${title}. Perfect for beginners to intermediate level students seeking practical skills.`;

  } catch (error) {
    console.error('Error generating target audience with Mistral:', error);
    const categoryAudience = {
      'technology': 'aspiring developers and tech professionals',
      'business': 'entrepreneurs and business professionals', 
      'design': 'creative professionals and designers',
      'marketing': 'marketers and business owners'
    };
    
    const audienceType = categoryAudience[category?.toLowerCase() as keyof typeof categoryAudience] || 'learners';
    return `This course is designed for ${audienceType} who want to master ${title}. Perfect for beginners to intermediate level students seeking practical skills they can apply immediately.`;
  }
}

/**
 * Basic course structure generator (fallback)
 */
function generateBasicCourseStructure(title: string, description: string) {
  const timestamp = Date.now();
  
  return {
    id: timestamp,
    title,
    description,
    category: "General",
    targetAudience: "All levels",
    totalDuration: "2 hours 30 minutes",
    modules: [
      {
        id: 1,
        title: "Introduction and Fundamentals",
        description: `Getting started with ${title} - core concepts and foundations`,
        lessons: [
          {
            id: 1,
            title: "Course Overview",
            description: "Introduction to the course and what you'll learn",
            duration: "5 min",
            type: "video"
          },
          {
            id: 2,
            title: "Key Concepts",
            description: "Understanding the fundamental principles",
            duration: "10 min",
            type: "video"
          },
          {
            id: 3,
            title: "Getting Started",
            description: "Your first steps in the subject",
            duration: "8 min",
            type: "video"
          }
        ]
      },
      {
        id: 2,
        title: "Core Principles",
        description: "Deep dive into the essential principles and methods",
        lessons: [
          {
            id: 4,
            title: "Essential Methods",
            description: "Learn the most important techniques",
            duration: "12 min",
            type: "video"
          },
          {
            id: 5,
            title: "Best Practices",
            description: "Industry-standard approaches and guidelines",
            duration: "10 min",
            type: "video"
          },
          {
            id: 6,
            title: "Common Pitfalls",
            description: "Avoid these common mistakes",
            duration: "8 min",
            type: "video"
          }
        ]
      },
      {
        id: 3,
        title: "Practical Application",
        description: "Hands-on exercises and real-world examples",
        lessons: [
          {
            id: 7,
            title: "Case Study 1",
            description: "Real-world example and analysis",
            duration: "15 min",
            type: "video"
          },
          {
            id: 8,
            title: "Practice Exercise",
            description: "Apply what you've learned",
            duration: "20 min",
            type: "interactive"
          },
          {
            id: 9,
            title: "Advanced Techniques",
            description: "Take your skills to the next level",
            duration: "12 min",
            type: "video"
          }
        ]
      },
      {
        id: 4,
        title: "Conclusion and Next Steps",
        description: "Wrap up and plan your continued learning journey",
        lessons: [
          {
            id: 10,
            title: "Course Summary",
            description: "Review key takeaways and concepts",
            duration: "8 min",
            type: "video"
          },
          {
            id: 11,
            title: "Next Steps",
            description: "How to continue your learning journey",
            duration: "6 min",
            type: "video"
          },
          {
            id: 12,
            title: "Resources and References",
            description: "Additional materials and further reading",
            duration: "5 min",
            type: "text"
          }
        ]
      }
    ],
    estimatedCompletionTime: "1-2 weeks",
    difficulty: "Beginner to Intermediate",
    prerequisites: "None required",
    learningObjectives: [
      `Understand the core concepts of ${title}`,
      "Apply practical techniques and methods",
      "Avoid common mistakes and pitfalls",
      "Build confidence through hands-on practice"
    ]
  };
}