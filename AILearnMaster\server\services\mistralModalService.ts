/**
 * Mistral AI service running on Modal GPU infrastructure
 * Handles all AI generation tasks using Mistral models
 */

import axios from 'axios';

// Modal configuration - these should be environment variables
const MODAL_BASE_URL = process.env.MODAL_BASE_URL;
const MODAL_API_KEY = process.env.MODAL_API_KEY;

// Check if Modal is available
const MODAL_AVAILABLE = MODAL_BASE_URL && MODAL_API_KEY;

interface MistralResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

/**
 * Generate course structure using Mistral AI on Modal GPU
 */
export async function generateCourseStructure({
  title,
  description,
  category,
  targetAudience,
  useAI = true
}: {
  title: string;
  description: string;
  category?: string;
  targetAudience?: string;
  useAI?: boolean;
}) {
  if (!useAI || !MODAL_AVAILABLE) {
    // Return basic template structure if AI is disabled or Modal not available
    console.log('Using fallback course structure generation');
    return generateBasicCourseStructure(title, description);
  }

  try {
    const prompt = `Create a comprehensive course structure for "${title}".
    
Course Details:
- Title: ${title}
- Description: ${description}
- Category: ${category || 'General'}
- Target Audience: ${targetAudience || 'All levels'}

Please generate a detailed course structure with:
1. 4-6 modules covering the topic comprehensively
2. Each module should have 3-4 lessons
3. Each lesson should have a title, description, estimated duration, and type (video/interactive/text)
4. Include learning objectives and prerequisites
5. Estimate total course duration

Return the response as a JSON object with this structure:
{
  "id": timestamp,
  "title": "course title",
  "description": "course description",
  "category": "category",
  "targetAudience": "target audience",
  "totalDuration": "estimated total time",
  "modules": [
    {
      "id": number,
      "title": "module title",
      "description": "module description",
      "lessons": [
        {
          "id": number,
          "title": "lesson title",
          "description": "lesson description",
          "duration": "duration",
          "type": "video|interactive|text"
        }
      ]
    }
  ],
  "estimatedCompletionTime": "time estimate",
  "difficulty": "difficulty level",
  "prerequisites": "prerequisites",
  "learningObjectives": ["objective1", "objective2", ...]
}`;

    const response = await axios.post(`${MODAL_BASE_URL}/mistral/generate`, {
      prompt,
      max_tokens: 2000,
      temperature: 0.7,
      model: 'mistral-7b-instruct'
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    const mistralResponse: MistralResponse = response.data;
    const content = mistralResponse.choices[0]?.message?.content;
    
    if (!content) {
      console.warn('No content received from Mistral, falling back to template');
      return generateBasicCourseStructure(title, description);
    }

    // Parse JSON response from Mistral
    try {
      const parsedContent = JSON.parse(content);
      return parsedContent;
    } catch (parseError) {
      console.warn('Failed to parse Mistral response as JSON, falling back to template');
      return generateBasicCourseStructure(title, description);
    }

  } catch (error) {
    console.error('Error calling Mistral Modal service:', error);
    
    // Fallback to basic structure if Modal service is unavailable
    return generateBasicCourseStructure(title, description);
  }
}

/**
 * Generate course description using Mistral AI
 */
export async function generateCourseDescription({
  title,
  category,
  targetAudience
}: {
  title: string;
  category?: string;
  targetAudience?: string;
}) {
  if (!MODAL_AVAILABLE) {
    console.log('Modal not available, using fallback description generation');
    return `Learn the fundamentals of ${title} in this comprehensive course designed for ${targetAudience || 'learners of all levels'}. Master key concepts, practical applications, and industry best practices through structured lessons and hands-on exercises.`;
  }

  try {
    const response = await axios.post(`${MODAL_BASE_URL}/course-description`, {
      title,
      category,
      targetAudience
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    return response.data.description || `Learn the fundamentals of ${title} in this comprehensive course designed for ${targetAudience || 'learners of all levels'}.`;

  } catch (error) {
    console.error('Error generating course description with Mistral:', error);
    return `Learn the fundamentals of ${title} in this comprehensive course designed for ${targetAudience || 'learners of all levels'}.`;
  }
}

/**
 * Generate lesson scripts using Mistral AI
 */
export async function generateLessonScripts({
  courseTitle,
  moduleTitle,
  lessonTitle,
  lessonDescription,
  duration,
  voiceStyle = 'professional'
}: {
  courseTitle: string;
  moduleTitle: string;
  lessonTitle: string;
  lessonDescription: string;
  duration: string;
  voiceStyle?: string;
}) {
  if (!MODAL_AVAILABLE) {
    console.log('Modal not available, using fallback lesson script generation');
    
    // Generate a comprehensive script template
    const script = `# ${lessonTitle}

## Introduction
Welcome to this lesson on ${lessonTitle}. I'm excited to guide you through ${lessonDescription}.

This lesson is part of the ${moduleTitle} module in our ${courseTitle} course. By the end of this lesson, you'll have a solid understanding of the key concepts we'll cover.

## Learning Objectives
In this lesson, you will:
- Understand the fundamental concepts of ${lessonTitle.toLowerCase()}
- Learn practical applications and real-world examples
- Gain hands-on experience through interactive elements
- Build confidence in applying these concepts

## Main Content

### Key Concept 1: Understanding the Basics
Let's start by exploring what ${lessonTitle.toLowerCase()} really means. ${lessonDescription}

This concept is important because it forms the foundation for everything else we'll learn in this module.

### Key Concept 2: Practical Applications
Now that we understand the basics, let's look at how this applies in real-world scenarios.

Here are some practical examples:
- Example 1: A common use case you'll encounter frequently
- Example 2: An advanced application that builds on the basics
- Example 3: A creative approach that shows the versatility of these concepts

### Key Concept 3: Best Practices
To help you succeed, here are some important best practices to keep in mind:
- Always start with the fundamentals before moving to advanced topics
- Practice regularly to reinforce your learning
- Don't be afraid to experiment and make mistakes - that's how we learn

## Summary and Next Steps
Let's recap what we've covered in this lesson:
- We explored the fundamental concepts of ${lessonTitle.toLowerCase()}
- We saw practical applications and real-world examples
- We learned important best practices to guide our work

In our next lesson, we'll build on these concepts and dive deeper into more advanced topics.

## Call to Action
Before moving on, take a moment to:
1. Review the key concepts we covered
2. Think about how you might apply these ideas in your own work
3. Practice with the examples we discussed

Remember, learning is a journey, and every step forward builds on what you've already accomplished. You're doing great!

Thank you for joining me in this lesson. I'll see you in the next one!`;

    return script;
  }

  try {
    const response = await axios.post(`${MODAL_BASE_URL}/lesson-script`, {
      courseTitle,
      moduleTitle,
      lessonTitle,
      lessonDescription,
      duration,
      voiceStyle
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    return response.data.script || `Welcome to ${lessonTitle}. In this lesson, we'll explore ${lessonDescription}. Let's begin our journey into this important topic.`;

  } catch (error) {
    console.error('Error generating lesson script with Mistral:', error);
    return `Welcome to ${lessonTitle}. In this lesson, we'll explore ${lessonDescription}. Let's begin our journey into this important topic.`;
  }
}

/**
 * Generate avatar script using Mistral AI
 */
export async function generateAvatarScript({
  title,
  description,
  topic,
  tone = 'professional',
  duration = '30 seconds'
}: {
  title: string;
  description: string;
  topic?: string;
  tone?: string;
  duration?: string;
}) {
  if (!MODAL_AVAILABLE) {
    console.log('Modal not available, using fallback avatar script generation');
    return `Hello and welcome to ${title}. ${description} Today we'll be exploring ${topic || 'this fascinating topic'}. Let's get started with this ${tone} presentation that will take approximately ${duration}.`;
  }

  try {
    const response = await axios.post(`${MODAL_BASE_URL}/lesson-script`, {
      courseTitle: title,
      lessonTitle: topic || title,
      lessonDescription: description,
      duration,
      voiceStyle: tone
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    return response.data.script || `Hello and welcome to ${title}. ${description} Today we'll be exploring ${topic || 'this fascinating topic'}. Let's get started with this ${tone} presentation that will take approximately ${duration}.`;

  } catch (error) {
    console.error('Error generating avatar script with Mistral:', error);
    return `Hello and welcome to ${title}. ${description} Today we'll be exploring ${topic || 'this fascinating topic'}. Let's get started with this ${tone} presentation that will take approximately ${duration}.`;
  }
}

/**
 * Generate target audience using Mistral AI
 */
export async function generateTargetAudience({
  title,
  category,
  description
}: {
  title: string;
  category?: string;
  description?: string;
}) {
  if (!MODAL_AVAILABLE) {
    console.log('Modal not available, using fallback target audience generation');
    const categoryAudience = {
      'technology': 'aspiring developers and tech professionals',
      'business': 'entrepreneurs and business professionals', 
      'design': 'creative professionals and designers',
      'marketing': 'marketers and business owners',
      'education': 'educators and students',
      'health': 'health professionals and wellness enthusiasts'
    };
    
    const audienceType = categoryAudience[category?.toLowerCase() as keyof typeof categoryAudience] || 'learners';
    return `This course is designed for ${audienceType} who want to master ${title}. Perfect for beginners to intermediate level students seeking practical skills they can apply immediately in real-world scenarios.`;
  }

  try {
    const response = await axios.post(`${MODAL_BASE_URL}/target-audience`, {
      title,
      category,
      description
    }, {
      headers: {
        'Authorization': `Bearer ${MODAL_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    return response.data.targetAudience || `This course is designed for learners who want to master ${title}. Perfect for beginners to intermediate level students seeking practical skills.`;

  } catch (error) {
    console.error('Error generating target audience with Mistral:', error);
    const categoryAudience = {
      'technology': 'aspiring developers and tech professionals',
      'business': 'entrepreneurs and business professionals', 
      'design': 'creative professionals and designers',
      'marketing': 'marketers and business owners'
    };
    
    const audienceType = categoryAudience[category?.toLowerCase() as keyof typeof categoryAudience] || 'learners';
    return `This course is designed for ${audienceType} who want to master ${title}. Perfect for beginners to intermediate level students seeking practical skills they can apply immediately.`;
  }
}

/**
 * Enhanced contextual course structure generator
 */
function generateBasicCourseStructure(title: string, description: string) {
  const timestamp = Date.now();
  
  // Generate contextually relevant content based on the course title
  const courseStructures = generateContextualCourseStructure(title, description);
  
  return {
    id: timestamp,
    title,
    description,
    category: "General",
    targetAudience: "All levels",
    totalDuration: "2 hours 30 minutes",
    modules: courseStructures.modules,
    estimatedCompletionTime: "1-2 weeks",
    difficulty: "Beginner to Intermediate",
    prerequisites: courseStructures.prerequisites,
    learningObjectives: courseStructures.learningObjectives
  };
}

/**
 * Generate contextually relevant course structures based on course title and content
 */
function generateContextualCourseStructure(title: string, description: string) {
  const titleLower = title.toLowerCase();
  
  console.log(`Contextual generation for: "${title}"`);
  console.log(`Title contains 'ai': ${titleLower.includes('ai')}`);
  console.log(`Title contains 'money': ${titleLower.includes('money')}`);
  
  // AI/Technology focused courses
  if (titleLower.includes('ai') || titleLower.includes('artificial intelligence') || titleLower.includes('machine learning')) {
    if (titleLower.includes('money') || titleLower.includes('business') || titleLower.includes('income')) {
      console.log('Using AI Business course structure');
      return generateAIBusinessCourseStructure(title);
    } else {
      console.log('Using adaptive course structure for AI topic');
      return generateAdaptiveCourseStructure(title, description);
    }
  }
  
  // Business/Marketing courses
  if (titleLower.includes('business') || titleLower.includes('marketing') || titleLower.includes('money') || titleLower.includes('income')) {
    console.log('Using adaptive course structure for business topic');
    return generateAdaptiveCourseStructure(title, description);
  }
  
  // Default contextual structure
  console.log('Using default adaptive course structure');
  return generateAdaptiveCourseStructure(title, description);
}

/**
 * Generate AI Business/Money-making course structure
 */
function generateAIBusinessCourseStructure(title: string) {
  return {
    modules: [
      {
        id: 1,
        title: "AI Fundamentals for Business",
        description: "Understanding AI technologies and their profit potential",
        lessons: [
          {
            id: 1,
            title: "Introduction to AI Business Opportunities",
            description: "Overview of how AI is transforming business and creating new income streams",
            duration: "8 min",
            type: "video"
          },
          {
            id: 2,
            title: "AI Tools Landscape",
            description: "Essential AI tools every entrepreneur should know about",
            duration: "12 min",
            type: "video"
          },
          {
            id: 3,
            title: "Market Analysis and Opportunities",
            description: "Identifying profitable niches in the AI economy",
            duration: "10 min",
            type: "video"
          }
        ]
      },
      {
        id: 2,
        title: "AI-Powered Business Models",
        description: "Proven strategies for monetizing AI capabilities",
        lessons: [
          {
            id: 4,
            title: "Service-Based AI Businesses",
            description: "Creating AI consulting and service offerings",
            duration: "15 min",
            type: "video"
          },
          {
            id: 5,
            title: "Product Development with AI",
            description: "Building and selling AI-enhanced products",
            duration: "12 min",
            type: "video"
          },
          {
            id: 6,
            title: "Content Creation and AI",
            description: "Leveraging AI for scalable content businesses",
            duration: "10 min",
            type: "video"
          }
        ]
      },
      {
        id: 3,
        title: "Implementation and Scaling",
        description: "Putting your AI business ideas into action",
        lessons: [
          {
            id: 7,
            title: "Setting Up Your AI Workflow",
            description: "Technical setup and tool integration for maximum efficiency",
            duration: "18 min",
            type: "video"
          },
          {
            id: 8,
            title: "Client Acquisition Strategies",
            description: "Finding and securing your first AI business clients",
            duration: "15 min",
            type: "video"
          },
          {
            id: 9,
            title: "Scaling Your Operations",
            description: "Automation and systems for growing your AI business",
            duration: "12 min",
            type: "video"
          }
        ]
      },
      {
        id: 4,
        title: "Advanced Strategies and Future Trends",
        description: "Staying ahead in the evolving AI landscape",
        lessons: [
          {
            id: 10,
            title: "Advanced AI Monetization",
            description: "High-value strategies for experienced AI entrepreneurs",
            duration: "14 min",
            type: "video"
          },
          {
            id: 11,
            title: "Future AI Trends and Opportunities",
            description: "Preparing for the next wave of AI innovations",
            duration: "10 min",
            type: "video"
          },
          {
            id: 12,
            title: "Building Your AI Empire",
            description: "Long-term strategies for sustained success",
            duration: "8 min",
            type: "video"
          }
        ]
      }
    ],
    prerequisites: "Basic computer skills and entrepreneurial mindset",
    learningObjectives: [
      "Master AI tools for business applications",
      "Develop profitable AI-powered business models",
      "Create scalable income streams using AI technology",
      "Build a sustainable AI business empire"
    ]
  };
}

/**
 * Generate adaptive course structure for any topic
 */
function generateAdaptiveCourseStructure(title: string, description: string) {
  const words = title.toLowerCase().split(' ');
  const mainTopic = words[0];
  
  return {
    modules: [
      {
        id: 1,
        title: `${title} Fundamentals`,
        description: `Core concepts and principles of ${title}`,
        lessons: [
          {
            id: 1,
            title: `Introduction to ${title}`,
            description: `Understanding the basics and importance of ${title}`,
            duration: "8 min",
            type: "video"
          },
          {
            id: 2,
            title: `Key Components of ${title}`,
            description: `Essential elements you need to master`,
            duration: "12 min",
            type: "video"
          },
          {
            id: 3,
            title: `${title} Best Practices`,
            description: `Industry standards and proven approaches`,
            duration: "10 min",
            type: "video"
          }
        ]
      },
      {
        id: 2,
        title: `Practical ${title} Applications`,
        description: `Real-world implementation of ${title} concepts`,
        lessons: [
          {
            id: 4,
            title: `${title} in Practice`,
            description: `Hands-on examples and case studies`,
            duration: "15 min",
            type: "video"
          },
          {
            id: 5,
            title: `Common ${title} Challenges`,
            description: `Troubleshooting and problem-solving strategies`,
            duration: "12 min",
            type: "video"
          },
          {
            id: 6,
            title: `${title} Tools and Resources`,
            description: `Essential tools for ${title} success`,
            duration: "10 min",
            type: "video"
          }
        ]
      },
      {
        id: 3,
        title: `Advanced ${title} Techniques`,
        description: `Mastering complex ${title} strategies`,
        lessons: [
          {
            id: 7,
            title: `Advanced ${title} Methods`,
            description: `Professional-level techniques and strategies`,
            duration: "18 min",
            type: "video"
          },
          {
            id: 8,
            title: `${title} Optimization`,
            description: `Maximizing efficiency and results`,
            duration: "15 min",
            type: "video"
          },
          {
            id: 9,
            title: `${title} Innovation`,
            description: `Cutting-edge approaches and future trends`,
            duration: "12 min",
            type: "video"
          }
        ]
      },
      {
        id: 4,
        title: `${title} Mastery and Success`,
        description: `Achieving excellence in ${title}`,
        lessons: [
          {
            id: 10,
            title: `Becoming a ${title} Expert`,
            description: `Path to mastery and professional recognition`,
            duration: "14 min",
            type: "video"
          },
          {
            id: 11,
            title: `${title} Success Stories`,
            description: `Learning from successful practitioners`,
            duration: "10 min",
            type: "video"
          },
          {
            id: 12,
            title: `Your ${title} Action Plan`,
            description: `Next steps for continued growth and success`,
            duration: "8 min",
            type: "video"
          }
        ]
      }
    ],
    prerequisites: `Interest in ${title} and willingness to learn`,
    learningObjectives: [
      `Master the fundamentals of ${title}`,
      `Apply ${title} concepts in real-world scenarios`,
      `Develop advanced ${title} skills and techniques`,
      `Achieve measurable success in ${title}`
    ]
  };
}