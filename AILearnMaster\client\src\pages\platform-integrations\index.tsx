import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  Store,
  Film,
  FileText,
  Settings2,
  BookOpen,
  Youtube,
  Download,
  CheckCircle2,
  Loader2,
  Sparkles,
  ArrowUpRight,
  Plus,
  RefreshCw,
} from 'lucide-react';
import { SiUdemy, SiYoutube, SiVimeo } from 'react-icons/si';
import { FaChalkboardTeacher, FaGraduationCap, FaBookOpen } from 'react-icons/fa';

// Define the platform types
type PlatformCategory = 'marketplace' | 'video_platform' | 'hosted_platform' | 'lms';

interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
  description: string;
  category: PlatformCategory;
  authType: 'apiKey' | 'oauth';
  fields?: {
    name: string;
    label: string;
    type: string;
    required: boolean;
    placeholder?: string;
  }[];
  oauthUrl?: string;
}

interface Integration {
  id: number;
  userId: number;
  platform: string;
  config: Record<string, string>;
  status: 'active' | 'pending' | 'error';
  createdAt: string;
  error?: string;
}

// Define the form schema
const formSchema = z.object({
  platform: z.string(),
  // Dynamically add fields based on the selected platform
});

export default function PlatformIntegrationsPage() {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('all');
  const [connectDialogOpen, setConnectDialogOpen] = useState(false);
  const [currentPlatform, setCurrentPlatform] = useState<Platform | null>(null);
  const [connecting, setConnecting] = useState(false);
  
  const form = useForm<any>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      platform: '',
    },
  });
  
  // Fetch all available platforms
  const { data: platforms, isLoading: isLoadingPlatforms } = useQuery({
    queryKey: ['/api/platforms'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/platforms');
        return await response.json();
      } catch (error) {
        console.error('Error fetching platforms:', error);
        return [];
      }
    },
  });
  
  // Fetch user's integrations
  const { data: integrations, isLoading: isLoadingIntegrations } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/integrations');
        return await response.json();
      } catch (error) {
        console.error('Error fetching integrations:', error);
        return [];
      }
    },
  });
  
  const openConnectDialog = (platform: Platform) => {
    setCurrentPlatform(platform);
    form.reset({ platform: platform.slug });
    
    // Initialize fields based on platform requirements
    if (platform.fields) {
      const defaultValues: Record<string, string> = { platform: platform.slug };
      platform.fields.forEach(field => {
        defaultValues[field.name] = '';
      });
      form.reset(defaultValues);
    }
    
    setConnectDialogOpen(true);
  };
  
  const onSubmit = async (values: any) => {
    setConnecting(true);
    
    try {
      // Submit to API
      const response = await apiRequest('POST', '/api/integrations', values);
      
      if (!response.ok) {
        throw new Error('Failed to connect to platform');
      }
      
      toast({
        title: 'Platform connected successfully',
        description: `You've successfully connected to ${currentPlatform?.name}.`,
      });
      
      // Close dialog and refetch data
      setConnectDialogOpen(false);
    } catch (error) {
      toast({
        title: 'Connection failed',
        description: 'There was an error connecting to the platform. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setConnecting(false);
    }
  };
  
  const handleDisconnectPlatform = async (integration: Integration) => {
    try {
      const response = await apiRequest('DELETE', `/api/integrations/${integration.id}`);
      
      if (!response.ok) {
        throw new Error('Failed to disconnect platform');
      }
      
      toast({
        title: 'Platform disconnected',
        description: `You've successfully disconnected from ${integration.platform}.`,
      });
    } catch (error) {
      toast({
        title: 'Disconnection failed',
        description: 'There was an error disconnecting from the platform. Please try again.',
        variant: 'destructive',
      });
    }
  };
  
  const getPlatformIcon = (slug: string, size = 24) => {
    switch (slug) {
      case 'udemy':
        return <SiUdemy size={size} className="text-[#ea5252]" />;
      case 'teachable':
        return <FaChalkboardTeacher size={size} className="text-[#29b2fe]" />;
      case 'thinkific':
        return <FaGraduationCap size={size} className="text-[#1caee6]" />;
      case 'kajabi':
        return <FaBookOpen size={size} className="text-[#7719aa]" />;
      case 'youtube':
        return <SiYoutube size={size} className="text-[#ff0000]" />;
      case 'vimeo':
        return <SiVimeo size={size} className="text-[#1ab7ea]" />;
      default:
        return <Store size={size} className="text-primary" />;
    }
  };
  
  const getCategoryIcon = (category: PlatformCategory, size = 24) => {
    switch (category) {
      case 'marketplace':
        return <Store size={size} className="text-primary" />;
      case 'video_platform':
        return <Film size={size} className="text-red-500" />;
      case 'hosted_platform':
        return <BookOpen size={size} className="text-blue-500" />;
      case 'lms':
        return <FileText size={size} className="text-green-500" />;
      default:
        return <Store size={size} className="text-primary" />;
    }
  };
  
  // Filter platforms based on the active tab
  // Ensure platforms is an array before filtering
  const platformsArray = Array.isArray(platforms) ? platforms : [];
  const filteredPlatforms = platformsArray.filter((platform: Platform) => {
    if (activeTab === 'all') return true;
    return platform.category === activeTab;
  });
  
  // Group integrations by category for the dashboard
  const getIntegrationsByCategory = () => {
    const result: Record<string, Integration[]> = {
      marketplace: [],
      video_platform: [],
      hosted_platform: [],
      lms: [],
    };
    
    // Ensure integrations and platforms are arrays
    const integrationsArray = Array.isArray(integrations) ? integrations : [];
    const platformsArray = Array.isArray(platforms) ? platforms : [];
    
    if (integrationsArray.length > 0 && platformsArray.length > 0) {
      integrationsArray.forEach((integration: Integration) => {
        const platform = platformsArray.find((p: Platform) => p.slug === integration.platform);
        if (platform) {
          // Ensure the category exists in the result object
          if (!result[platform.category]) {
            result[platform.category] = [];
          }
          result[platform.category].push(integration);
        }
      });
    }
    
    return result;
  };
  
  const integrationsByCategory = getIntegrationsByCategory();
  
  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col md:flex-row justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2 gradient-heading">Platform Integrations</h1>
          <p className="text-slate-600">Connect your content to external platforms and marketplaces</p>
        </div>
        
        <div className="mt-4 md:mt-0 flex flex-col md:flex-row gap-2">
          <Button variant="outline" onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Status
          </Button>
          <Link href="/platform-integrations-settings">
            <Button variant="outline">
              <Settings2 className="h-4 w-4 mr-2" />
              Advanced Settings
            </Button>
          </Link>
        </div>
      </div>
      
      {/* Integration Dashboard */}
      {integrations && integrations.length > 0 && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Connected Platforms</CardTitle>
            <CardDescription>
              Manage your existing platform connections
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Object.entries(integrationsByCategory).map(([category, items]) => (
                items.length > 0 && (
                  <Card key={category} className="border shadow-sm">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center">
                        {getCategoryIcon(category as PlatformCategory, 16)}
                        <span className="ml-2">
                          {category === 'marketplace' ? 'Marketplaces' :
                          category === 'video_platform' ? 'Video Platforms' :
                          category === 'hosted_platform' ? 'Course Platforms' :
                          'Learning Management'}
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2">
                        {items.map((integration: Integration) => {
                          const platform = platforms?.find((p: Platform) => p.slug === integration.platform);
                          return (
                            <div key={integration.id} className="flex items-center justify-between">
                              <div className="flex items-center">
                                {getPlatformIcon(integration.platform, 20)}
                                <span className="ml-2 text-sm font-medium">{platform?.name || integration.platform}</span>
                              </div>
                              <div className="flex items-center">
                                {integration.status === 'active' ? (
                                  <span className="text-xs text-green-600 bg-green-50 px-2 py-0.5 rounded-full flex items-center">
                                    <CheckCircle2 className="h-3 w-3 mr-1" />
                                    Active
                                  </span>
                                ) : integration.status === 'pending' ? (
                                  <span className="text-xs text-amber-600 bg-amber-50 px-2 py-0.5 rounded-full flex items-center">
                                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                    Pending
                                  </span>
                                ) : (
                                  <span className="text-xs text-red-600 bg-red-50 px-2 py-0.5 rounded-full flex items-center">
                                    <div className="h-3 w-3 mr-1 bg-red-600 rounded-full"></div>
                                    Error
                                  </span>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                    <CardFooter className="pt-0">
                      <Link href={`/platform-integrations/${category.replace('_', '-')}`}>
                        <Button variant="link" className="p-0 h-auto text-xs">
                          Manage platforms
                          <ArrowUpRight className="h-3 w-3 ml-1" />
                        </Button>
                      </Link>
                    </CardFooter>
                  </Card>
                )
              ))}
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Available Platforms */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="all">All Platforms</TabsTrigger>
          <TabsTrigger value="marketplace">Marketplaces</TabsTrigger>
          <TabsTrigger value="video_platform">Video Platforms</TabsTrigger>
          <TabsTrigger value="hosted_platform">Course Platforms</TabsTrigger>
          <TabsTrigger value="lms">Learning Management</TabsTrigger>
        </TabsList>
        
        <TabsContent value={activeTab}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPlatforms?.map((platform: Platform) => {
              // Check if platform is already connected
              const integration = integrations?.find((i: Integration) => i.platform === platform.slug);
              const isConnected = !!integration;
              
              return (
                <Card key={platform.id} className={isConnected ? 'border-primary shadow-md' : ''}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        {getPlatformIcon(platform.slug)}
                        <CardTitle className="ml-2">{platform.name}</CardTitle>
                      </div>
                      {isConnected && (
                        <span className="text-xs text-primary bg-primary/10 px-2 py-0.5 rounded-full flex items-center">
                          <CheckCircle2 className="h-3 w-3 mr-1" />
                          Connected
                        </span>
                      )}
                    </div>
                    <CardDescription>{platform.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm text-slate-600">
                      <div className="flex items-center mb-1">
                        <span className="font-medium mr-2">Integration Type:</span>
                        <span>
                          {platform.authType === 'apiKey' ? 
                            'API Key Authentication' : 
                            'OAuth Authentication'}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className="font-medium mr-2">Category:</span>
                        <span>
                          {platform.category === 'marketplace' ? 'Marketplace' :
                          platform.category === 'video_platform' ? 'Video Platform' :
                          platform.category === 'hosted_platform' ? 'Course Platform' :
                          'Learning Management System'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    {isConnected ? (
                      <div className="w-full space-y-3">
                        {integration.status === 'active' && (
                          <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md">
                            <div className="flex items-center space-x-2 text-green-700 dark:text-green-400 text-sm">
                              <Sparkles className="h-4 w-4" />
                              <span>Ready to publish content</span>
                            </div>
                          </div>
                        )}
                        
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            className="flex-1"
                            onClick={() => window.location.href = `/platform-integrations/${platform.category.replace('_', '-')}/${platform.slug}`}
                          >
                            <Settings2 className="h-4 w-4 mr-2" />
                            Manage
                          </Button>
                          <Button 
                            variant="destructive" 
                            onClick={() => handleDisconnectPlatform(integration)}
                            className="flex-1"
                          >
                            Disconnect
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <Button 
                        className="w-full"
                        onClick={() => openConnectDialog(platform)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Connect Platform
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Connect Platform Dialog */}
      <Dialog open={connectDialogOpen} onOpenChange={setConnectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Connect to {currentPlatform?.name}</DialogTitle>
            <DialogDescription>
              Enter your {currentPlatform?.name} credentials to connect your account.
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {currentPlatform?.authType === 'apiKey' && currentPlatform?.fields && (
                <div className="space-y-4">
                  {currentPlatform.fields.map((field) => (
                    <FormField
                      key={field.name}
                      control={form.control}
                      name={field.name}
                      render={({ field: formField }) => (
                        <FormItem>
                          <FormLabel>{field.label}</FormLabel>
                          <FormControl>
                            <Input 
                              type={field.type} 
                              placeholder={field.placeholder || ''} 
                              {...formField} 
                            />
                          </FormControl>
                          {field.name === 'apiKey' && (
                            <FormDescription>
                              You can find your API key in your {currentPlatform.name} account settings.
                            </FormDescription>
                          )}
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ))}
                </div>
              )}
              
              {currentPlatform?.authType === 'oauth' && (
                <div className="py-4">
                  <p className="text-sm text-slate-600 mb-4">
                    You'll be redirected to {currentPlatform.name} to authorize access to your account.
                  </p>
                  <Button 
                    type="button" 
                    className="w-full"
                    onClick={() => {
                      window.location.href = currentPlatform.oauthUrl || '';
                    }}
                  >
                    <div className="mr-2">
                      {getPlatformIcon(currentPlatform.slug, 18)}
                    </div>
                    Continue with {currentPlatform.name}
                  </Button>
                </div>
              )}
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setConnectDialogOpen(false)}
                >
                  Cancel
                </Button>
                {currentPlatform?.authType === 'apiKey' && (
                  <Button type="submit" disabled={connecting}>
                    {connecting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Connect
                  </Button>
                )}
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}