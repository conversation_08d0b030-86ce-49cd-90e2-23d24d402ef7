import React, { useState, useCallback, useEffect } from "react";
import { useLocation } from "wouter";
import { HorizontalStepIndicator } from "@/components/course-creator/HorizontalStepIndicator";
import { CourseDetailsForm } from "@/components/course-creator/CourseDetailsForm";
import { CoursePublishForm } from "@/components/course-creator/CoursePublishForm";
import { ArrowLeft, Video, Brain, Mic, Image, Film, Settings, Play, CheckCircle, AlertCircle, Upload, Download, Eye, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";

type TraditionalCourseStep = 
  | "course-details"
  | "ai-structure"
  | "voice-settings"
  | "media-configuration"
  | "video-production"
  | "review-publish";

const TRADITIONAL_COURSE_STEPS: Record<TraditionalCourseStep, { 
  title: string; 
  description: string; 
  icon: React.ComponentType; 
  color: string;
}> = {
  "course-details": { 
    title: "Course Details", 
    description: "Basic course information and learning objectives",
    icon: Settings,
    color: "blue"
  },
  "ai-structure": { 
    title: "AI Structure Generation", 
    description: "Mistral AI creates your course outline",
    icon: Brain,
    color: "purple"
  },
  "voice-settings": { 
    title: "Voice Configuration", 
    description: "Configure Coqui TTS voice synthesis",
    icon: Mic,
    color: "green"
  },
  "media-configuration": { 
    title: "Media & Slides", 
    description: "Pexels/Pixabay media and Marp slide generation",
    icon: Image,
    color: "orange"
  },
  "video-production": { 
    title: "Video Assembly", 
    description: "FFmpeg professional video production",
    icon: Film,
    color: "red"
  },
  "review-publish": { 
    title: "Review & Publish", 
    description: "Final review and course publication",
    icon: Video,
    color: "indigo"
  }
};

interface CourseModule {
  id: string;
  title: string;
  description: string;
  lessons: CourseLesson[];
}

interface CourseLesson {
  id: string;
  title: string;
  content: string;
  script: string;
  duration: number;
  mediaAssets: string[];
  videoUrl?: string;
  slides?: string[];
}

interface GeneratedCourse {
  modules: CourseModule[];
  totalDuration: number;
  estimatedCompletion: string;
}

interface VoiceSettings {
  provider: 'coqui' | 'kokoro';
  voiceId: string;
  speed: number;
  pitch: number;
  volume: number;
}

interface MediaSettings {
  imageProvider: 'pexels' | 'pixabay';
  videoProvider: 'pexels' | 'pixabay';
  slideStyle: 'professional' | 'educational' | 'creative' | 'minimal';
  quality: 'standard' | 'high' | 'ultra';
}

export default function TraditionalCoursePage() {
  const [, setLocation] = useLocation();
  const [currentStep, setCurrentStep] = useState<TraditionalCourseStep>("course-details");
  const [courseData, setCourseData] = useState<any>({
    title: '',
    description: '',
    category: '',
    difficulty: 'beginner',
    targetAudience: '',
    objectives: []
  });
  const [generatedCourse, setGeneratedCourse] = useState<GeneratedCourse | null>(null);
  const [voiceSettings, setVoiceSettings] = useState<VoiceSettings>({
    provider: 'coqui',
    voiceId: 'tts_models/en/ljspeech/tacotron2-DDC',
    speed: 1.0,
    pitch: 1.0,
    volume: 1.0
  });
  const [mediaSettings, setMediaSettings] = useState<MediaSettings>({
    imageProvider: 'pexels',
    videoProvider: 'pexels',
    slideStyle: 'professional',
    quality: 'high'
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStage, setGenerationStage] = useState('');
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [generatedVideos, setGeneratedVideos] = useState<any[]>([]);
  const { toast } = useToast();

  const steps = Object.keys(TRADITIONAL_COURSE_STEPS) as TraditionalCourseStep[];
  const currentStepIndex = steps.indexOf(currentStep);
  const stepConfig = TRADITIONAL_COURSE_STEPS[currentStep];

  // Convert traditional course steps to HorizontalStepIndicator format
  const stepIndicatorData = steps.map((stepKey, index) => {
    const config = TRADITIONAL_COURSE_STEPS[stepKey];
    const IconComponent = config.icon;
    return {
      id: stepKey,
      title: config.title,
      description: config.description,
      icon: <IconComponent className="h-4 w-4" />
    };
  });

  const handleNext = useCallback(async () => {
    // Validate current step before proceeding
    const isValid = await validateCurrentStep();
    if (!isValid) return;

    // Mark current step as completed
    if (!completedSteps.includes(currentStepIndex)) {
      setCompletedSteps(prev => [...prev, currentStepIndex]);
    }

    if (currentStepIndex < steps.length - 1) {
      setCurrentStep(steps[currentStepIndex + 1]);
    }
  }, [currentStepIndex, steps, completedSteps]);

  const handlePrevious = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStep(steps[currentStepIndex - 1]);
    }
  }, [currentStepIndex, steps]);

  const validateCurrentStep = async (): Promise<boolean> => {
    switch (currentStep) {
      case 'course-details':
        if (!courseData.title || !courseData.description || !courseData.category) {
          toast({
            title: "Missing Information",
            description: "Please fill in all required course details",
            variant: "destructive"
          });
          return false;
        }
        return true;
      case 'ai-structure':
        if (!generatedCourse) {
          toast({
            title: "Course Structure Required",
            description: "Please generate the course structure before proceeding",
            variant: "destructive"
          });
          return false;
        }
        return true;
      case 'voice-settings':
        if (!voiceSettings.voiceId) {
          toast({
            title: "Voice Selection Required",
            description: "Please select a voice for narration",
            variant: "destructive"
          });
          return false;
        }
        return true;
      case 'media-configuration':
        return true; // Media settings have defaults
      case 'video-production':
        if (generatedVideos.length === 0) {
          toast({
            title: "Video Generation Required",
            description: "Please generate videos before proceeding to review",
            variant: "destructive"
          });
          return false;
        }
        return true;
      default:
        return true;
    }
  };

  const handleGenerateStructure = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);
    setGenerationStage('Initializing Mistral AI...');

    try {
      // Stage 1: Generate course structure with Mistral AI
      setGenerationStage('Generating course structure with Mistral AI...');
      setGenerationProgress(25);

      const structureResponse = await apiRequest('/api/ai/generate-structure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: courseData.title,
          description: courseData.description,
          category: courseData.category,
          difficulty: courseData.difficulty,
          targetAudience: courseData.targetAudience,
          objectives: courseData.objectives
        })
      });

      setGenerationProgress(50);
      setGenerationStage('Generating lesson scripts...');

      // Stage 2: Generate detailed scripts for each lesson
      const scriptsResponse = await apiRequest('/api/ai/generate-scripts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          courseStructure: structureResponse,
          voiceSettings: voiceSettings
        })
      });

      setGenerationProgress(75);
      setGenerationStage('Finalizing course structure...');

      // Stage 3: Combine structure and scripts
      const finalCourse: GeneratedCourse = {
        modules: scriptsResponse.modules,
        totalDuration: scriptsResponse.totalDuration,
        estimatedCompletion: scriptsResponse.estimatedCompletion
      };

      setGenerationProgress(100);
      setGenerationStage('Structure generation complete!');
      setGeneratedCourse(finalCourse);

      toast({
        title: "Course Structure Generated",
        description: `Successfully created ${finalCourse.modules.length} modules with ${finalCourse.modules.reduce((acc, mod) => acc + mod.lessons.length, 0)} lessons`,
      });

      // Auto-advance to next step after a brief delay
      setTimeout(() => {
        handleNext();
      }, 1500);

    } catch (error: any) {
      console.error('Structure generation failed:', error);
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate course structure. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
      setGenerationStage('');
    }
  };

  const handleGenerateMedia = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);
    setGenerationStage('Searching for media assets...');

    try {
      // Generate media assets for all lessons
      const mediaResponse = await apiRequest('/api/ai/generate-assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          course: generatedCourse,
          settings: mediaSettings
        })
      });

      setGenerationProgress(100);
      setGenerationStage('Media assets ready!');

      toast({
        title: "Media Assets Generated",
        description: "Successfully found and prepared all media assets",
      });

      setTimeout(() => {
        handleNext();
      }, 1500);

    } catch (error: any) {
      console.error('Media generation failed:', error);
      toast({
        title: "Media Generation Failed",
        description: error.message || "Failed to generate media assets. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
      setGenerationStage('');
    }
  };

  const handleGenerateVideos = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);
    setGenerationStage('Starting video production pipeline...');

    try {
      const totalLessons = generatedCourse?.modules.reduce((acc, mod) => acc + mod.lessons.length, 0) || 0;
      let processedLessons = 0;

      const videoResults = [];

      // Process each lesson
      for (const module of generatedCourse?.modules || []) {
        for (const lesson of module.lessons) {
          setGenerationStage(`Generating video for: ${lesson.title}`);
          setGenerationProgress((processedLessons / totalLessons) * 100);

          // Generate video for this lesson
          const videoResponse = await apiRequest('/api/ai/generate-lesson', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              lesson,
              voiceSettings,
              mediaSettings,
              slideStyle: mediaSettings.slideStyle
            })
          });

          videoResults.push({
            lessonId: lesson.id,
            lessonTitle: lesson.title,
            videoUrl: videoResponse.videoUrl,
            thumbnailUrl: videoResponse.thumbnailUrl,
            duration: videoResponse.duration,
            status: 'completed'
          });

          processedLessons++;
        }
      }

      setGeneratedVideos(videoResults);
      setGenerationProgress(100);
      setGenerationStage('All videos generated successfully!');

      toast({
        title: "Video Production Complete",
        description: `Successfully generated ${videoResults.length} lesson videos`,
      });

      setTimeout(() => {
        handleNext();
      }, 1500);

    } catch (error: any) {
      console.error('Video generation failed:', error);
      toast({
        title: "Video Generation Failed",
        description: error.message || "Failed to generate videos. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
      setGenerationStage('');
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "course-details":
        return (
          <CourseDetailsForm
            onSubmit={(values) => {
              setCourseData(values);
              handleNext();
            }}
            defaultValues={courseData}
          />
        );

      case "ai-structure":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            {!generatedCourse ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-purple-600" />
                    AI Structure Generation
                  </CardTitle>
                  <CardDescription>
                    Mistral AI will analyze your course details and create a comprehensive structure with modules, lessons, and scripts
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <h3 className="font-medium mb-2 flex items-center gap-2">
                        <Sparkles className="h-4 w-4" />
                        Course Structure
                      </h3>
                      <ul className="text-sm space-y-1">
                        <li>• Detailed course outline with modules</li>
                        <li>• Learning objectives for each section</li>
                        <li>• Logical content progression</li>
                        <li>• Duration estimates</li>
                      </ul>
                    </div>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h3 className="font-medium mb-2 flex items-center gap-2">
                        <Video className="h-4 w-4" />
                        Lesson Scripts
                      </h3>
                      <ul className="text-sm space-y-1">
                        <li>• Engaging lesson narratives</li>
                        <li>• Clear explanations and examples</li>
                        <li>• Interactive elements</li>
                        <li>• Assessment strategies</li>
                      </ul>
                    </div>
                  </div>

                  {isGenerating && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">{generationStage}</span>
                        <span className="font-medium">{generationProgress}%</span>
                      </div>
                      <Progress value={generationProgress} className="h-2" />
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Button onClick={handlePrevious} variant="outline" disabled={isGenerating}>
                      Previous
                    </Button>
                    <Button 
                      onClick={handleGenerateStructure} 
                      className="flex-1" 
                      disabled={isGenerating}
                    >
                      {isGenerating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Brain className="h-4 w-4 mr-2" />
                          Generate Structure with Mistral AI
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Course Structure Generated
                  </CardTitle>
                  <CardDescription>
                    Review your course structure and make adjustments if needed
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {generatedCourse.modules.length}
                      </div>
                      <div className="text-sm text-muted-foreground">Modules</div>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {generatedCourse.modules.reduce((acc, mod) => acc + mod.lessons.length, 0)}
                      </div>
                      <div className="text-sm text-muted-foreground">Lessons</div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">
                        {generatedCourse.totalDuration}min
                      </div>
                      <div className="text-sm text-muted-foreground">Duration</div>
                    </div>
                  </div>

                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {generatedCourse.modules.map((module, moduleIndex) => (
                      <div key={module.id} className="border rounded-lg p-4">
                        <h3 className="font-semibold mb-2">
                          Module {moduleIndex + 1}: {module.title}
                        </h3>
                        <p className="text-sm text-muted-foreground mb-3">{module.description}</p>
                        <div className="space-y-2">
                          {module.lessons.map((lesson, lessonIndex) => (
                            <div key={lesson.id} className="flex items-center gap-2 text-sm">
                              <Badge variant="outline" className="text-xs">
                                {lessonIndex + 1}
                              </Badge>
                              <span>{lesson.title}</span>
                              <span className="text-muted-foreground">({lesson.duration}min)</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex gap-2">
                    <Button onClick={handlePrevious} variant="outline">Previous</Button>
                    <Button onClick={() => setGeneratedCourse(null)} variant="outline">
                      Regenerate
                    </Button>
                    <Button onClick={handleNext} className="flex-1">
                      Continue to Voice Settings
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        );

      case "voice-settings":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mic className="h-5 w-5 text-green-600" />
                  Voice Configuration
                </CardTitle>
                <CardDescription>
                  Configure Coqui TTS and Kokoro voice synthesis settings for narration
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Tabs value={voiceSettings.provider} onValueChange={(value) => 
                  setVoiceSettings(prev => ({ ...prev, provider: value as 'coqui' | 'kokoro' }))
                }>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="coqui">Coqui TTS (Primary)</TabsTrigger>
                    <TabsTrigger value="kokoro">Kokoro (Fallback)</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="coqui" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      {[
                        { id: 'tts_models/en/ljspeech/tacotron2-DDC', name: 'Professional Female', accent: 'American' },
                        { id: 'tts_models/en/vctk/vits', name: 'Professional Male', accent: 'British' },
                        { id: 'tts_models/en/jenny/jenny', name: 'Friendly Narrator', accent: 'American' },
                        { id: 'tts_models/en/ek1/tacotron2', name: 'Educational Tone', accent: 'Neutral' }
                      ].map((voice) => (
                        <div 
                          key={voice.id} 
                          className={`border rounded-lg p-4 cursor-pointer transition-all ${
                            voiceSettings.voiceId === voice.id ? 'border-green-500 bg-green-50' : 'hover:border-green-300'
                          }`}
                          onClick={() => setVoiceSettings(prev => ({ ...prev, voiceId: voice.id }))}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium">{voice.name}</span>
                            <Badge variant="secondary">Coqui</Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">{voice.accent} accent</div>
                          {voiceSettings.voiceId === voice.id && (
                            <div className="mt-2">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="kokoro" className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      {[
                        { id: 'kokoro-v0_19', name: 'Kokoro Standard', quality: 'High' },
                        { id: 'kokoro-v0_19-fast', name: 'Kokoro Fast', quality: 'Medium' }
                      ].map((voice) => (
                        <div 
                          key={voice.id} 
                          className={`border rounded-lg p-4 cursor-pointer transition-all ${
                            voiceSettings.voiceId === voice.id ? 'border-green-500 bg-green-50' : 'hover:border-green-300'
                          }`}
                          onClick={() => setVoiceSettings(prev => ({ ...prev, voiceId: voice.id }))}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium">{voice.name}</span>
                            <Badge variant="outline">Kokoro</Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">Quality: {voice.quality}</div>
                          {voiceSettings.voiceId === voice.id && (
                            <div className="mt-2">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="speed">Speech Speed</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="speed"
                        type="range"
                        min="0.5"
                        max="2.0"
                        step="0.1"
                        value={voiceSettings.speed}
                        onChange={(e) => setVoiceSettings(prev => ({ ...prev, speed: parseFloat(e.target.value) }))}
                        className="flex-1"
                      />
                      <span className="text-sm w-12">{voiceSettings.speed}x</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="pitch">Pitch</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="pitch"
                        type="range"
                        min="0.5"
                        max="2.0"
                        step="0.1"
                        value={voiceSettings.pitch}
                        onChange={(e) => setVoiceSettings(prev => ({ ...prev, pitch: parseFloat(e.target.value) }))}
                        className="flex-1"
                      />
                      <span className="text-sm w-12">{voiceSettings.pitch}x</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="volume">Volume</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="volume"
                        type="range"
                        min="0.1"
                        max="2.0"
                        step="0.1"
                        value={voiceSettings.volume}
                        onChange={(e) => setVoiceSettings(prev => ({ ...prev, volume: parseFloat(e.target.value) }))}
                        className="flex-1"
                      />
                      <span className="text-sm w-12">{voiceSettings.volume}x</span>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2">Voice Preview</h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    Preview how your selected voice will sound with current settings
                  </p>
                  <Button variant="outline" size="sm">
                    <Play className="h-4 w-4 mr-2" />
                    Test Voice
                  </Button>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handlePrevious} variant="outline">Previous</Button>
                  <Button onClick={handleNext} className="flex-1" disabled={!voiceSettings.voiceId}>
                    Continue to Media Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "media-configuration":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Image className="h-5 w-5 text-orange-600" />
                  Media & Slides Configuration
                </CardTitle>
                <CardDescription>
                  Configure Pexels/Pixabay media sources and Marp slide generation settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-semibold">Media Providers</h3>
                    
                    <div className="space-y-3">
                      <Label>Image Provider</Label>
                      <Select value={mediaSettings.imageProvider} onValueChange={(value) => 
                        setMediaSettings(prev => ({ ...prev, imageProvider: value as 'pexels' | 'pixabay' }))
                      }>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pexels">Pexels (Recommended)</SelectItem>
                          <SelectItem value="pixabay">Pixabay</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-3">
                      <Label>Video Provider</Label>
                      <Select value={mediaSettings.videoProvider} onValueChange={(value) => 
                        setMediaSettings(prev => ({ ...prev, videoProvider: value as 'pexels' | 'pixabay' }))
                      }>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pexels">Pexels (Recommended)</SelectItem>
                          <SelectItem value="pixabay">Pixabay</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-3">
                      <Label>Media Quality</Label>
                      <Select value={mediaSettings.quality} onValueChange={(value) => 
                        setMediaSettings(prev => ({ ...prev, quality: value as 'standard' | 'high' | 'ultra' }))
                      }>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="standard">Standard (720p)</SelectItem>
                          <SelectItem value="high">High (1080p)</SelectItem>
                          <SelectItem value="ultra">Ultra (4K)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-semibold">Marp Slide Styles</h3>
                    
                    <div className="grid grid-cols-2 gap-3">
                      {[
                        { id: 'professional', name: 'Professional', desc: 'Clean corporate design' },
                        { id: 'educational', name: 'Educational', desc: 'Academic-focused layout' },
                        { id: 'creative', name: 'Creative', desc: 'Vibrant and engaging' },
                        { id: 'minimal', name: 'Minimal', desc: 'Simple and clean' }
                      ].map((style) => (
                        <div 
                          key={style.id} 
                          className={`border rounded-lg p-3 cursor-pointer transition-all ${
                            mediaSettings.slideStyle === style.id ? 'border-orange-500 bg-orange-50' : 'hover:border-orange-300'
                          }`}
                          onClick={() => setMediaSettings(prev => ({ ...prev, slideStyle: style.id as any }))}
                        >
                          <div className="font-medium text-sm">{style.name}</div>
                          <div className="text-xs text-muted-foreground">{style.desc}</div>
                          {mediaSettings.slideStyle === style.id && (
                            <div className="mt-2">
                              <CheckCircle className="h-3 w-3 text-orange-600" />
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="bg-orange-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2 flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    What will be generated
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h4 className="font-medium mb-1">Media Assets</h4>
                      <ul className="space-y-1 text-muted-foreground">
                        <li>• Background images for each lesson</li>
                        <li>• Relevant stock videos and animations</li>
                        <li>• Icons and visual elements</li>
                        <li>• Custom thumbnails</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium mb-1">Marp Slides</h4>
                      <ul className="space-y-1 text-muted-foreground">
                        <li>• Automated slide generation from scripts</li>
                        <li>• Consistent visual branding</li>
                        <li>• Optimized for video production</li>
                        <li>• Interactive elements and transitions</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {isGenerating && (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">{generationStage}</span>
                      <span className="font-medium">{generationProgress}%</span>
                    </div>
                    <Progress value={generationProgress} className="h-2" />
                  </div>
                )}

                <div className="flex gap-2">
                  <Button onClick={handlePrevious} variant="outline" disabled={isGenerating}>
                    Previous
                  </Button>
                  <Button 
                    onClick={handleGenerateMedia} 
                    className="flex-1"
                    disabled={isGenerating}
                  >
                    {isGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Generating Media...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Generate Media & Slides
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "video-production":
        return (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-gray-600" />
                Media Configuration
              </CardTitle>
              <CardDescription>
                Configure media sources and slide generation settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <span className="font-medium">Pexels Integration</span>
                    <p className="text-sm text-muted-foreground">High-quality stock photos</p>
                  </div>
                  <Badge variant="secondary">Active</Badge>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <span className="font-medium">Pixabay Integration</span>
                    <p className="text-sm text-muted-foreground">Diverse media library</p>
                  </div>
                  <Badge variant="secondary">Active</Badge>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <span className="font-medium">Marp Slide Engine</span>
                    <p className="text-sm text-muted-foreground">Professional presentations</p>
                  </div>
                  <Badge variant="secondary">Ready</Badge>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button onClick={handlePrevious} variant="outline">Previous</Button>
                <Button onClick={handleNext} className="flex-1">Generate Course Videos</Button>
              </div>
            </CardContent>
          </Card>
        );

      case "video-production":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Film className="h-5 w-5 text-red-600" />
                  Video Assembly
                </CardTitle>
                <CardDescription>
                  FFmpeg and Whisper will create professional videos with captions and subtitles
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {generatedVideos.length === 0 ? (
                  <>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <h3 className="font-medium mb-2 flex items-center gap-2">
                        <Film className="h-4 w-4" />
                        Video Production Pipeline
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <h4 className="font-medium mb-1">FFmpeg Processing</h4>
                          <ul className="space-y-1 text-muted-foreground">
                            <li>• Combine slides with voice narration</li>
                            <li>• Add background music and effects</li>
                            <li>• Optimize video quality and compression</li>
                            <li>• Generate multiple resolution outputs</li>
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-medium mb-1">Whisper Integration</h4>
                          <ul className="space-y-1 text-muted-foreground">
                            <li>• Automatic caption generation</li>
                            <li>• Multi-language subtitle support</li>
                            <li>• Timestamp synchronization</li>
                            <li>• Accessibility compliance</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {generatedCourse && (
                      <div className="space-y-3">
                        <h3 className="font-medium">Lessons to be generated:</h3>
                        <div className="space-y-2 max-h-48 overflow-y-auto">
                          {generatedCourse.modules.map((module, moduleIndex) => (
                            <div key={module.id} className="border rounded-lg p-3">
                              <h4 className="font-medium text-sm mb-2">
                                Module {moduleIndex + 1}: {module.title}
                              </h4>
                              <div className="space-y-1">
                                {module.lessons.map((lesson, lessonIndex) => (
                                  <div key={lesson.id} className="flex items-center gap-2 text-sm text-muted-foreground">
                                    <Badge variant="outline" className="text-xs">
                                      {lessonIndex + 1}
                                    </Badge>
                                    <span>{lesson.title}</span>
                                    <span>({lesson.duration}min)</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {isGenerating && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">{generationStage}</span>
                          <span className="font-medium">{generationProgress}%</span>
                        </div>
                        <Progress value={generationProgress} className="h-2" />
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Button onClick={handlePrevious} variant="outline" disabled={isGenerating}>
                        Previous
                      </Button>
                      <Button 
                        onClick={handleGenerateVideos} 
                        className="flex-1"
                        disabled={isGenerating || !generatedCourse}
                      >
                        {isGenerating ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            Generating Videos...
                          </>
                        ) : (
                          <>
                            <Film className="h-4 w-4 mr-2" />
                            Start Video Production
                          </>
                        )}
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex items-center justify-between">
                      <h3 className="font-semibold flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        Generated Videos ({generatedVideos.length})
                      </h3>
                      <Button variant="outline" size="sm" onClick={() => setGeneratedVideos([])}>
                        Regenerate All
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                      {generatedVideos.map((video, index) => (
                        <Card key={video.lessonId} className="overflow-hidden">
                          <div className="aspect-video bg-gray-100 relative">
                            {video.thumbnailUrl ? (
                              <img 
                                src={video.thumbnailUrl} 
                                alt={video.lessonTitle}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Video className="h-12 w-12 text-gray-400" />
                              </div>
                            )}
                            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                              <Button variant="secondary" size="sm">
                                <Play className="h-4 w-4 mr-2" />
                                Preview
                              </Button>
                            </div>
                          </div>
                          <CardContent className="p-4">
                            <h4 className="font-medium text-sm mb-1">{video.lessonTitle}</h4>
                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <span>Duration: {video.duration}</span>
                              <Badge variant="outline" className="text-xs">
                                {video.status}
                              </Badge>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    <div className="flex gap-2">
                      <Button onClick={handlePrevious} variant="outline">Previous</Button>
                      <Button onClick={handleNext} className="flex-1">
                        Continue to Review & Publish
                      </Button>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        );

      case "video-production":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Film className="h-5 w-5 text-red-600" />
                  FFmpeg Video Production
                </CardTitle>
                <CardDescription>
                  Professional video assembly and rendering
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {isGenerating ? (
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                        <span className="text-sm font-medium text-blue-600">Generating Enhanced Traditional Course</span>
                      </div>
                    </div>
                    <Progress value={generationProgress} className="w-full" />
                    <p className="text-center text-sm text-muted-foreground">
                      {generationProgress < 20 ? "Mistral AI Structure Generation" :
                       generationProgress < 40 ? "Coqui TTS Voice Synthesis" :
                       generationProgress < 60 ? "Pexels/Pixabay Media Search" :
                       generationProgress < 80 ? "Marp Slide Generation" :
                       generationProgress < 100 ? "FFmpeg Video Assembly" : "Complete"}
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="bg-red-50 p-4 rounded-lg">
                    <h3 className="font-medium mb-2">Video Production Pipeline:</h3>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                        <span>AI Structure Generation</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        <span>Voice Synthesis</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                        <span>Media Integration</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                        <span>Slide Generation</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                        <span>Video Assembly</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={handlePrevious} variant="outline">Previous</Button>
                    <Button onClick={handleNext} className="flex-1">
                      Start Video Production
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        );

      case "review-publish":
        return (
          <div className="max-w-4xl mx-auto space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Video className="h-5 w-5 text-indigo-600" />
                  Review & Publish
                </CardTitle>
                <CardDescription>
                  Review your course, create quizzes, and publish to distribution platforms
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Tabs defaultValue="overview" className="space-y-4">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="videos">Videos</TabsTrigger>
                    <TabsTrigger value="quizzes">Quizzes</TabsTrigger>
                    <TabsTrigger value="publish">Publish</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="overview" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {generatedCourse?.modules.length || 0}
                          </div>
                          <div className="text-sm text-muted-foreground">Modules</div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {generatedVideos.length}
                          </div>
                          <div className="text-sm text-muted-foreground">Videos</div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-4 text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {generatedCourse?.totalDuration || 0}min
                          </div>
                          <div className="text-sm text-muted-foreground">Total Duration</div>
                        </CardContent>
                      </Card>
                    </div>
                    
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h3 className="font-medium mb-2">Course Summary</h3>
                      <div className="space-y-2 text-sm">
                        <div><strong>Title:</strong> {courseData.title}</div>
                        <div><strong>Category:</strong> {courseData.category}</div>
                        <div><strong>Difficulty:</strong> {courseData.difficulty}</div>
                        <div><strong>Target Audience:</strong> {courseData.targetAudience}</div>
                        <div><strong>Voice:</strong> {voiceSettings.provider} - {voiceSettings.voiceId}</div>
                        <div><strong>Media Quality:</strong> {mediaSettings.quality}</div>
                        <div><strong>Slide Style:</strong> {mediaSettings.slideStyle}</div>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="videos" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {generatedVideos.map((video, index) => (
                        <Card key={video.lessonId}>
                          <div className="aspect-video bg-gray-100 relative">
                            {video.thumbnailUrl ? (
                              <img 
                                src={video.thumbnailUrl} 
                                alt={video.lessonTitle}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <Video className="h-12 w-12 text-gray-400" />
                              </div>
                            )}
                            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                              <Button variant="secondary" size="sm">
                                <Play className="h-4 w-4 mr-2" />
                                Preview
                              </Button>
                            </div>
                          </div>
                          <CardContent className="p-4">
                            <h4 className="font-medium mb-2">{video.lessonTitle}</h4>
                            <div className="flex items-center justify-between text-sm text-muted-foreground">
                              <span>Duration: {video.duration}</span>
                              <div className="flex gap-2">
                                <Button variant="outline" size="sm">
                                  <Eye className="h-3 w-3" />
                                </Button>
                                <Button variant="outline" size="sm">
                                  <Download className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="quizzes" className="space-y-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Create Quizzes</CardTitle>
                        <CardDescription>
                          Add interactive quizzes to enhance learning engagement
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="text-center py-8">
                          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                          <h3 className="font-medium mb-2">No Quizzes Created</h3>
                          <p className="text-muted-foreground mb-4">
                            Create quizzes to test student knowledge and improve engagement
                          </p>
                          <Button>
                            <Sparkles className="h-4 w-4 mr-2" />
                            Generate Quiz with AI
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                  
                  <TabsContent value="publish" className="space-y-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Distribution Platforms</CardTitle>
                        <CardDescription>
                          Choose where to publish your course
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {['YouTube', 'Vimeo', 'Udemy', 'Coursera', 'Teachable', 'Thinkific'].map((platform) => (
                            <div key={platform} className="flex items-center justify-between p-3 border rounded-lg">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                  <Upload className="h-4 w-4 text-blue-600" />
                                </div>
                                <span className="font-medium">{platform}</span>
                              </div>
                              <Button variant="outline" size="sm">
                                Connect
                              </Button>
                            </div>
                          ))}
                        </div>
                        
                        <Separator />
                        
                        <div className="bg-green-50 p-4 rounded-lg">
                          <h3 className="font-medium mb-2 flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            Ready to Publish
                          </h3>
                          <p className="text-sm text-muted-foreground mb-4">
                            Your course is complete and ready to be published to your selected platforms
                          </p>
                          <div className="flex gap-2">
                            <Button>
                              <Upload className="h-4 w-4 mr-2" />
                              Publish Course
                            </Button>
                            <Button variant="outline">
                              Save as Draft
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
                
                <div className="flex gap-2">
                  <Button onClick={handlePrevious} variant="outline">Previous</Button>
                  <Button 
                    onClick={() => {
                      toast({
                        title: "Course Published!",
                        description: "Your traditional course has been successfully created and published.",
                      });
                      setLocation('/dashboard');
                    }}
                    className="flex-1"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Complete & Publish Course
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return null;
    }
  };

  const StepIcon = stepConfig.icon;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              onClick={() => setLocation('/dashboard')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
                <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-600 to-indigo-600 flex items-center justify-center">
                  <Video className="h-5 w-5 text-white" />
                </div>
                Enhanced Traditional Course
              </h1>
              <p className="text-gray-600 mt-1">
                Professional video production with AI-powered workflow
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <StepIcon className={`h-5 w-5 text-${stepConfig.color}-600`} />
            <span className="text-sm font-medium">{stepConfig.title}</span>
          </div>
        </div>

        {/* Step Indicator */}
        <div className="mb-8">
          <HorizontalStepIndicator
            steps={stepIndicatorData}
            activeStep={currentStepIndex}
            completedSteps={[]}
            onStepClick={(stepIndex) => {
              if (stepIndex >= 0 && stepIndex < steps.length) {
                setCurrentStep(steps[stepIndex]);
              }
            }}
          />
        </div>

        {/* Step Content */}
        <div className="mb-8">
          {renderStepContent()}
        </div>

        {/* Workflow Info */}
        <Card className="bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-sm">Enhanced Traditional Course Pipeline</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <span>Mistral AI</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span>Coqui TTS</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                <span>Pexels/Pixabay</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                <span>Marp</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                <span>FFmpeg</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}