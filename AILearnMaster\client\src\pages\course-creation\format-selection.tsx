import React, { useState } from "react";
import { useLocation } from "wouter";
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  ChevronRight, 
  Lightbulb, 
  Video, 
  FileText, 
  FileVideo,
  ArrowLeft,
  CheckCircle,
  Info
} from "lucide-react";
import { motion } from "framer-motion";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export default function FormatSelection() {
  const [_, setLocation] = useLocation();
  const [selectedFormat, setSelectedFormat] = useState<string | null>(null);

  const handleFormatSelect = (format: string) => {
    // Store the selected format in localStorage to skip this step next time
    localStorage.setItem('preferredCourseFormat', format);
    
    // Navigate to the appropriate course creation page
    switch (format) {
      case 'smart':
        setLocation('/course-creation/smart-course');
        break;
      case 'avatar':
        setLocation('/course-creation/avatar-course');
        break;
      case 'traditional':
        setLocation('/course-creation/traditional-course');
        break;
      case 'video-based':
        setLocation('/course-creation/video-course');
        break;
      default:
        setLocation('/course-creation/traditional-course');
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        staggerChildren: 0.1 
      } 
    }
  };

  const cardVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { 
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    },
    hover: { 
      y: -5, 
      boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)"
    }
  };

  const selectCard = (format: string) => {
    setSelectedFormat(format);
  };

  const continueWithSelection = () => {
    if (selectedFormat) {
      handleFormatSelect(selectedFormat);
    }
  };

  return (
    <div className="container mx-auto py-12 px-4 max-w-5xl">
      <div className="text-center mb-12">
        <Button 
          variant="ghost" 
          className="mb-2 absolute top-4 left-4"
          onClick={() => setLocation('/my-courses')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Courses
        </Button>
        
        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Choose Your Course Format
        </h1>
        <p className="text-muted-foreground mt-3 max-w-lg mx-auto">
          Select the format that best suits your content and teaching style. Each format offers unique benefits for different learning experiences.
        </p>
      </div>
      
      <motion.div 
        className="grid gap-8 md:grid-cols-2 lg:grid-cols-3"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Avatar Course Format */}
        <motion.div
          variants={cardVariants}
          whileHover="hover"
          onClick={() => selectCard('avatar')}
        >
          <Card className={`overflow-hidden cursor-pointer transition-all h-full flex flex-col ${
            selectedFormat === 'avatar' ? 'ring-2 ring-primary border-primary' : 'border-2 hover:border-primary/30'
          }`}>
            <CardHeader className="pb-2">
              <div className="bg-gradient-to-br from-violet-500 to-indigo-600 -mx-6 -mt-6 p-6 mb-4 text-white flex justify-between items-start">
                <Video className="h-12 w-12" />
                <span className="bg-white/20 px-2 py-0.5 rounded-full text-xs backdrop-blur-sm">Popular</span>
              </div>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center text-xl">
                    Talking Avatar
                  </CardTitle>
                  <CardDescription className="mt-1">
                    AI-powered video presenter
                  </CardDescription>
                </div>
                {selectedFormat === 'avatar' && (
                  <CheckCircle className="h-6 w-6 text-primary" />
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-3 text-sm flex-grow">
              <div className="flex items-start gap-2">
                <div className="bg-violet-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-violet-600" />
                </div>
                <span>Professional avatar presents your content</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-violet-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-violet-600" />
                </div>
                <span>Text to speech narration in multiple voices</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-violet-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-violet-600" />
                </div>
                <span>Quick to create with minimal equipment</span>
              </div>
            </CardContent>
            <CardFooter className="pt-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-xs text-muted-foreground flex items-center gap-1 mb-2">
                      <Info className="h-3 w-3" />
                      <span>Best for quick, professional courses</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-64 text-xs">
                      Perfect for instructors who want to create professional-looking courses quickly
                      without needing video equipment or editing skills.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </CardFooter>
          </Card>
        </motion.div>
        
        {/* Smart Course Builder Format */}
        <motion.div
          variants={cardVariants}
          whileHover="hover"
          onClick={() => selectCard('smart')}
        >
          <Card className={`overflow-hidden cursor-pointer transition-all h-full flex flex-col ${
            selectedFormat === 'smart' ? 'ring-2 ring-primary border-primary' : 'border-2 hover:border-primary/30'
          }`}>
            <CardHeader className="pb-2">
              <div className="bg-gradient-to-br from-emerald-500 to-teal-600 -mx-6 -mt-6 p-6 mb-4 text-white flex justify-between items-start">
                <Lightbulb className="h-12 w-12" />
                <span className="bg-white/20 px-2 py-0.5 rounded-full text-xs backdrop-blur-sm">New</span>
              </div>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center text-xl">
                    Smart Course Builder
                  </CardTitle>
                  <CardDescription className="mt-1">
                    AI-powered complete automation
                  </CardDescription>
                </div>
                {selectedFormat === 'smart' && (
                  <CheckCircle className="h-6 w-6 text-primary" />
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-3 text-sm flex-grow">
              <div className="flex items-start gap-2">
                <div className="bg-emerald-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-emerald-600" />
                </div>
                <span>Just enter a title - AI does everything else</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-emerald-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-emerald-600" />
                </div>
                <span>4-scene videos with Kokoro TTS voiceovers</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-emerald-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-emerald-600" />
                </div>
                <span>Auto-generated captions and stock media</span>
              </div>
            </CardContent>
            <CardFooter className="pt-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-xs text-muted-foreground flex items-center gap-1 mb-2">
                      <Info className="h-3 w-3" />
                      <span>Complete hands-off course creation</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-64 text-xs">
                      Revolutionary AI-powered course builder that creates complete video courses
                      with professional narration, captions, and stock media from just a title.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </CardFooter>
          </Card>
        </motion.div>

        {/* Traditional Course Format */}
        <motion.div
          variants={cardVariants}
          whileHover="hover"
          onClick={() => selectCard('traditional')}
        >
          <Card className={`overflow-hidden cursor-pointer transition-all h-full flex flex-col ${
            selectedFormat === 'traditional' ? 'ring-2 ring-primary border-primary' : 'border-2 hover:border-primary/30'
          }`}>
            <CardHeader className="pb-2">
              <div className="bg-gradient-to-br from-blue-500 to-cyan-600 -mx-6 -mt-6 p-6 mb-4 text-white">
                <FileText className="h-12 w-12" />
              </div>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center text-xl">
                    Traditional Course
                  </CardTitle>
                  <CardDescription className="mt-1">
                    Structured learning experience
                  </CardDescription>
                </div>
                {selectedFormat === 'traditional' && (
                  <CheckCircle className="h-6 w-6 text-primary" />
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-3 text-sm flex-grow">
              <div className="flex items-start gap-2">
                <div className="bg-blue-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-blue-600" />
                </div>
                <span>Organize content into sections and lessons</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-blue-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-blue-600" />
                </div>
                <span>Include various content types (video, text, etc.)</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-blue-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-blue-600" />
                </div>
                <span>Add quizzes and assessments for learners</span>
              </div>
            </CardContent>
            <CardFooter className="pt-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-xs text-muted-foreground flex items-center gap-1 mb-2">
                      <Info className="h-3 w-3" />
                      <span>Best for comprehensive learning</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-64 text-xs">
                      Ideal for creating in-depth courses with a mix of content types and organized structure.
                      Perfect for comprehensive topics requiring detailed explanations.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </CardFooter>
          </Card>
        </motion.div>
        
        {/* Video Course Format */}
        <motion.div
          variants={cardVariants}
          whileHover="hover"
          onClick={() => selectCard('video-based')}
        >
          <Card className={`overflow-hidden cursor-pointer transition-all h-full flex flex-col ${
            selectedFormat === 'video-based' ? 'ring-2 ring-primary border-primary' : 'border-2 hover:border-primary/30'
          }`}>
            <CardHeader className="pb-2">
              <div className="bg-gradient-to-br from-rose-500 to-orange-600 -mx-6 -mt-6 p-6 mb-4 text-white">
                <FileVideo className="h-12 w-12" />
              </div>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="flex items-center text-xl">
                    Video Course
                  </CardTitle>
                  <CardDescription className="mt-1">
                    Video-centric learning
                  </CardDescription>
                </div>
                {selectedFormat === 'video-based' && (
                  <CheckCircle className="h-6 w-6 text-primary" />
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-3 text-sm flex-grow">
              <div className="flex items-start gap-2">
                <div className="bg-rose-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-rose-600" />
                </div>
                <span>Use stock videos or upload your own</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-rose-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-rose-600" />
                </div>
                <span>Add AI-generated captions and transcripts</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="bg-rose-100 p-1 rounded-full mt-0.5">
                  <Lightbulb className="h-3.5 w-3.5 text-rose-600" />
                </div>
                <span>Organize into chapters with timestamps</span>
              </div>
            </CardContent>
            <CardFooter className="pt-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-xs text-muted-foreground flex items-center gap-1 mb-2">
                      <Info className="h-3 w-3" />
                      <span>Best for demonstrations and tutorials</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-64 text-xs">
                      Perfect for showing hands-on demonstrations, step-by-step tutorials, 
                      or when you already have pre-recorded video content to use.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </CardFooter>
          </Card>
        </motion.div>
      </motion.div>
      
      <div className="mt-12 flex flex-col items-center justify-center">
        <Button 
          className="w-64 h-12 text-lg"
          disabled={!selectedFormat}
          onClick={continueWithSelection}
        >
          <span>Continue</span>
          <ChevronRight className="ml-2 h-5 w-5" />
        </Button>
        
        <div className="mt-8 p-5 bg-muted/50 rounded-lg max-w-3xl">
          <div className="flex gap-2 items-center mb-2">
            <Lightbulb className="h-5 w-5 text-amber-500" />
            <h3 className="font-medium">Need help choosing?</h3>
          </div>
          <p className="text-sm text-muted-foreground">
            <strong>Talking Avatar courses</strong> are perfect for quick, professional-looking content without needing video equipment. 
            <strong> Traditional courses</strong> offer more structure and organization for comprehensive learning experiences.
            <strong> Video-based courses</strong> are ideal for demonstrations or when you already have video content.
          </p>
        </div>
      </div>
    </div>
  );
}