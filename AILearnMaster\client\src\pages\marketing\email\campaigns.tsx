import React, { useState } from 'react';
import EmailLayout from './layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { PaginationButton } from '@/components/ui/pagination-button';
import { useMediaQuery } from '@/hooks/use-media-query';
import {
  Calendar,
  Clock,
  Copy,
  Edit,
  ExternalLink,
  Eye,
  LineChart,
  Mail,
  MailCheck,
  MoreVertical,
  PauseCircle,
  Play,
  PlayCircle,
  Plus,
  Trash2,
  Users,
} from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const EmailCampaignsPage = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [campaignDialogOpen, setCampaignDialogOpen] = useState(false);
  const [currentCampaign, setCurrentCampaign] = useState<any>(null);
  const [previewCampaign, setPreviewCampaign] = useState<any>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { isMobile } = useMediaQuery();

  // Form schema for creating/editing campaigns
  const campaignSchema = z.object({
    name: z.string().min(1, { message: "Campaign name is required" }),
    subject: z.string().min(1, { message: "Email subject is required" }),
    templateId: z.number({ required_error: "Please select a template" }),
    listId: z.number({ required_error: "Please select a subscriber list" }),
    sendAt: z.string().optional(),
    status: z.enum(['draft', 'scheduled', 'sending', 'sent', 'paused']).default('draft'),
  });

  // Create form
  const campaignForm = useForm<z.infer<typeof campaignSchema>>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      name: "",
      subject: "",
      status: "draft",
    },
  });

  // Fetch campaigns with pagination and filtering
  const { data: campaignsData, isLoading: campaignsLoading } = useQuery({
    queryKey: ['/api/email-campaigns', page, limit, statusFilter, searchQuery],
    queryFn: async () => {
      // This is mocked data since we don't have the actual API endpoint yet
      return {
        data: [
          {
            id: 1,
            name: "May 2025 Newsletter",
            subject: "Your May Newsletter is Here!",
            status: "sent",
            listId: 1,
            listName: "Newsletter Subscribers",
            templateId: 2,
            recipientCount: 1250,
            openRate: 38.4,
            clickRate: 12.1,
            createdAt: "2025-05-01T09:30:00Z",
            sentAt: "2025-05-01T10:00:00Z",
          },
          {
            id: 2,
            name: "Summer Sale Promotion",
            subject: "Don't Miss Our Amazing Summer Deals!",
            status: "scheduled",
            listId: 2,
            listName: "All Customers",
            templateId: 3,
            recipientCount: 5320,
            openRate: null,
            clickRate: null,
            createdAt: "2025-05-05T14:20:00Z",
            sendAt: "2025-05-15T08:00:00Z",
          },
          {
            id: 3,
            name: "New Product Launch",
            subject: "Introducing Our Newest Innovation",
            status: "draft",
            listId: 3,
            listName: "Product Interest Group",
            templateId: 1,
            recipientCount: 0,
            openRate: null,
            clickRate: null,
            createdAt: "2025-05-06T11:45:00Z",
          },
          {
            id: 4,
            name: "Customer Feedback Survey",
            subject: "We Value Your Opinion",
            status: "paused",
            listId: 2,
            listName: "All Customers",
            templateId: 4,
            recipientCount: 5320,
            openRate: 12.3,
            clickRate: 7.8,
            createdAt: "2025-05-03T16:20:00Z",
            sentAt: "2025-05-03T16:30:00Z",
            pausedAt: "2025-05-03T16:45:00Z",
          },
          {
            id: 5,
            name: "Weekly Tips & Tricks",
            subject: "This Week's Top Tips for Success",
            status: "sending",
            listId: 1,
            listName: "Newsletter Subscribers",
            templateId: 2,
            recipientCount: 1250,
            sendProgress: 42,
            openRate: null,
            clickRate: null,
            createdAt: "2025-05-07T08:15:00Z",
            startedAt: "2025-05-07T09:00:00Z",
          },
        ],
        pagination: {
          page: 1,
          limit: 10,
          totalItems: 5,
          totalPages: 1,
        }
      };
    },
  });

  // Fetch templates for dropdown
  const { data: templatesData } = useQuery({
    queryKey: ['/api/email-templates', 'all'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/email-templates?limit=100');
      return res.json();
    },
  });

  // Fetch subscriber lists for dropdown
  const { data: listsData } = useQuery({
    queryKey: ['/api/subscribers/lists', 'all'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/subscribers/lists');
      return res.json();
    },
  });

  // Create campaign mutation (would connect to actual API)
  const createCampaignMutation = useMutation({
    mutationFn: async (values: z.infer<typeof campaignSchema>) => {
      // This would be an actual API call in production
      // const res = await apiRequest('POST', '/api/email-campaigns', values);
      // return res.json();
      
      // For now, just simulate a response
      return {
        id: Math.floor(Math.random() * 1000),
        ...values,
        createdAt: new Date().toISOString()
      };
    },
    onSuccess: () => {
      // In production, invalidate the actual query
      // queryClient.invalidateQueries({ queryKey: ['/api/email-campaigns'] });
      setCampaignDialogOpen(false);
      campaignForm.reset();
      toast({
        title: 'Campaign created',
        description: 'Email campaign has been created successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to create campaign: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Update campaign mutation (would connect to actual API)
  const updateCampaignMutation = useMutation({
    mutationFn: async (values: z.infer<typeof campaignSchema> & { id: number }) => {
      // This would be an actual API call in production
      // const { id, ...data } = values;
      // const res = await apiRequest('PUT', `/api/email-campaigns/${id}`, data);
      // return res.json();
      
      // For now, just simulate a response
      return values;
    },
    onSuccess: () => {
      // In production, invalidate the actual query
      // queryClient.invalidateQueries({ queryKey: ['/api/email-campaigns'] });
      setCampaignDialogOpen(false);
      setCurrentCampaign(null);
      campaignForm.reset();
      toast({
        title: 'Campaign updated',
        description: 'Email campaign has been updated successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to update campaign: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Delete campaign mutation (would connect to actual API)
  const deleteCampaignMutation = useMutation({
    mutationFn: async (id: number) => {
      // This would be an actual API call in production
      // await apiRequest('DELETE', `/api/email-campaigns/${id}`);
      console.log(`Deleting campaign ${id}`);
    },
    onSuccess: () => {
      // In production, invalidate the actual query
      // queryClient.invalidateQueries({ queryKey: ['/api/email-campaigns'] });
      toast({
        title: 'Campaign deleted',
        description: 'Email campaign has been deleted successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to delete campaign: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Toggle campaign status (would connect to actual API)
  const toggleCampaignStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: number; status: string }) => {
      // This would be an actual API call in production
      // const res = await apiRequest('PATCH', `/api/email-campaigns/${id}/status`, { status });
      // return res.json();
      
      // For now, just log and simulate
      console.log(`Changing campaign ${id} status to ${status}`);
      return { id, status };
    },
    onSuccess: (data) => {
      // In production, invalidate the actual query
      // queryClient.invalidateQueries({ queryKey: ['/api/email-campaigns'] });
      
      const statusMessages = {
        sending: 'Campaign is now sending',
        paused: 'Campaign has been paused',
        scheduled: 'Campaign has been scheduled',
        draft: 'Campaign saved as draft',
      };
      
      toast({
        title: 'Status updated',
        description: statusMessages[data.status as keyof typeof statusMessages] || 'Status updated successfully',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to update campaign status: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Handle opening the dialog for editing
  const handleEditCampaign = (campaign: any) => {
    setCurrentCampaign(campaign);
    campaignForm.reset({
      name: campaign.name,
      subject: campaign.subject,
      templateId: campaign.templateId,
      listId: campaign.listId,
      sendAt: campaign.sendAt ? new Date(campaign.sendAt).toISOString().slice(0, 16) : undefined,
      status: campaign.status,
    });
    setCampaignDialogOpen(true);
  };

  // Handle opening the dialog for creating
  const handleCreateCampaign = () => {
    setCurrentCampaign(null);
    campaignForm.reset({
      name: "",
      subject: "",
      status: "draft",
    });
    setCampaignDialogOpen(true);
  };

  // Handle form submission
  const onSubmit = (values: z.infer<typeof campaignSchema>) => {
    if (currentCampaign) {
      updateCampaignMutation.mutate({ ...values, id: currentCampaign.id });
    } else {
      createCampaignMutation.mutate(values);
    }
  };

  // Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'sent': return 'default';
      case 'sending': return 'default';
      case 'scheduled': return 'secondary';
      case 'draft': return 'outline';
      case 'paused': return 'destructive';
      default: return 'outline';
    }
  };

  // Render campaign table
  const renderCampaigns = () => {
    if (campaignsLoading) {
      return (
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="pb-2">
                <div className="flex justify-between">
                  <div className="space-y-1">
                    <Skeleton className="h-5 w-[200px]" />
                    <Skeleton className="h-4 w-[150px]" />
                  </div>
                  <Skeleton className="h-8 w-[100px]" />
                </div>
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
              <CardFooter>
                <div className="flex justify-between items-center w-full">
                  <Skeleton className="h-4 w-[100px]" />
                  <Skeleton className="h-8 w-[120px]" />
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      );
    }

    if (!campaignsData?.data?.length) {
      return (
        <Card className="flex flex-col items-center justify-center py-12">
          <Mail className="h-16 w-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No Email Campaigns Yet</h3>
          <p className="text-muted-foreground text-center max-w-md mt-2">
            Create your first email campaign to start engaging with your subscribers.
          </p>
          <Button className="mt-4" onClick={handleCreateCampaign}>
            <Plus className="h-4 w-4 mr-2" />
            Create Campaign
          </Button>
        </Card>
      );
    }

    return (
      <div className="space-y-4">
        {campaignsData.data.map((campaign: any) => (
          <Card key={campaign.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex flex-wrap justify-between items-start gap-2">
                <div>
                  <CardTitle className="text-lg">{campaign.name}</CardTitle>
                  <CardDescription className="line-clamp-1">
                    {campaign.subject}
                  </CardDescription>
                </div>
                <Badge variant={getStatusVariant(campaign.status)}>
                  {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{campaign.listName} ({campaign.recipientCount})</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {campaign.status === 'scheduled' 
                      ? `Scheduled for ${new Date(campaign.sendAt).toLocaleDateString()} ${new Date(campaign.sendAt).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`
                      : campaign.status === 'sent'
                      ? `Sent on ${new Date(campaign.sentAt).toLocaleDateString()}`
                      : campaign.status === 'sending'
                      ? `Started on ${new Date(campaign.startedAt).toLocaleDateString()}`
                      : `Created on ${new Date(campaign.createdAt).toLocaleDateString()}`
                    }
                  </span>
                </div>
                {campaign.status === 'sending' && (
                  <div className="flex flex-col gap-1">
                    <div className="flex items-center justify-between text-sm">
                      <span>Sending progress</span>
                      <span>{campaign.sendProgress}%</span>
                    </div>
                    <Progress value={campaign.sendProgress} className="h-2" />
                  </div>
                )}
                {(campaign.status === 'sent' || campaign.status === 'paused') && (
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <MailCheck className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{campaign.openRate || 0}% opens</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <ExternalLink className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{campaign.clickRate || 0}% clicks</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between items-center pt-2">
              <div className="text-sm text-muted-foreground">
                {campaign.createdAt && `Created ${new Date(campaign.createdAt).toLocaleDateString()}`}
              </div>
              <div className="flex gap-2">
                {campaign.status === 'draft' && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => toggleCampaignStatusMutation.mutate({ id: campaign.id, status: 'scheduled' })}
                  >
                    <Clock className="h-4 w-4 mr-2" />
                    Schedule
                  </Button>
                )}
                {campaign.status === 'draft' && (
                  <Button 
                    size="sm"
                    onClick={() => toggleCampaignStatusMutation.mutate({ id: campaign.id, status: 'sending' })}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Send Now
                  </Button>
                )}
                {campaign.status === 'scheduled' && (
                  <Button 
                    size="sm"
                    onClick={() => toggleCampaignStatusMutation.mutate({ id: campaign.id, status: 'sending' })}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Send Now
                  </Button>
                )}
                {campaign.status === 'sending' && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => toggleCampaignStatusMutation.mutate({ id: campaign.id, status: 'paused' })}
                  >
                    <PauseCircle className="h-4 w-4 mr-2" />
                    Pause
                  </Button>
                )}
                {campaign.status === 'paused' && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => toggleCampaignStatusMutation.mutate({ id: campaign.id, status: 'sending' })}
                  >
                    <PlayCircle className="h-4 w-4 mr-2" />
                    Resume
                  </Button>
                )}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {campaign.status === 'sent' && (
                      <DropdownMenuItem onClick={() => console.log('View report')}>
                        <LineChart className="mr-2 h-4 w-4" />
                        View Report
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem onClick={() => setPreviewCampaign(campaign)}>
                      <Eye className="mr-2 h-4 w-4" />
                      Preview
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleEditCampaign(campaign)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => console.log('Duplicate')}>
                      <Copy className="mr-2 h-4 w-4" />
                      Duplicate
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={() => {
                        if (window.confirm("Are you sure you want to delete this campaign?")) {
                          deleteCampaignMutation.mutate(campaign.id);
                        }
                      }}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardFooter>
          </Card>
        ))}
        
        {campaignsData.pagination.totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <PaginationButton
              currentPage={page}
              totalPages={campaignsData.pagination.totalPages}
              onPageChange={setPage}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <EmailLayout>
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Email Campaigns</h2>
          <p className="text-muted-foreground">Create and manage your email marketing campaigns</p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button onClick={handleCreateCampaign}>
            <Plus className="h-4 w-4 mr-2" />
            New Campaign
          </Button>
        </div>
      </div>

      {/* Tabs for filtering by status */}
      <Tabs 
        defaultValue="all" 
        value={statusFilter || 'all'} 
        onValueChange={value => setStatusFilter(value === 'all' ? null : value)}
        className="mt-6"
      >
        <TabsList className="grid grid-cols-3 md:grid-cols-6 mb-4">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="draft">Drafts</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="sending">Sending</TabsTrigger>
          <TabsTrigger value="sent">Sent</TabsTrigger>
          <TabsTrigger value="paused">Paused</TabsTrigger>
        </TabsList>
        
        {/* Search box */}
        <div className="relative mb-4">
          <div className="relative">
            <Input
              placeholder="Search campaigns..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <div className="absolute left-2 top-2.5">
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                className="text-muted-foreground"
              >
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.3-4.3"></path>
              </svg>
            </div>
          </div>
        </div>
        
        {/* Campaign list */}
        <TabsContent value={statusFilter || 'all'} className="mt-0">
          {renderCampaigns()}
        </TabsContent>
      </Tabs>

      {/* Create/Edit Campaign Dialog */}
      <Dialog open={campaignDialogOpen} onOpenChange={setCampaignDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{currentCampaign ? 'Edit Campaign' : 'Create Campaign'}</DialogTitle>
            <DialogDescription>
              {currentCampaign
                ? 'Update your email campaign details below.'
                : 'Fill out the form below to create a new email campaign.'}
            </DialogDescription>
          </DialogHeader>
          <Form {...campaignForm}>
            <form onSubmit={campaignForm.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={campaignForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Campaign Name</FormLabel>
                    <FormControl>
                      <Input placeholder="May Newsletter" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={campaignForm.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Subject</FormLabel>
                    <FormControl>
                      <Input placeholder="Your May Newsletter is Here!" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={campaignForm.control}
                  name="templateId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Template</FormLabel>
                      <Select 
                        value={field.value?.toString()} 
                        onValueChange={(value) => field.onChange(parseInt(value))}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a template" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {templatesData?.data?.map((template: any) => (
                            <SelectItem key={template.id} value={template.id.toString()}>
                              {template.name}
                            </SelectItem>
                          )) || (
                            <SelectItem value="1">Newsletter Template</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={campaignForm.control}
                  name="listId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Subscriber List</FormLabel>
                      <Select 
                        value={field.value?.toString()} 
                        onValueChange={(value) => field.onChange(parseInt(value))}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a list" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {listsData?.map((list: any) => (
                            <SelectItem key={list.id} value={list.id.toString()}>
                              {list.name} ({list.subscriberCount || 0})
                            </SelectItem>
                          )) || (
                            <SelectItem value="1">Newsletter Subscribers</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={campaignForm.control}
                name="sendAt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Schedule Send Time (Optional)</FormLabel>
                    <FormControl>
                      <Input 
                        type="datetime-local" 
                        {...field} 
                        value={field.value || ''} 
                      />
                    </FormControl>
                    <FormDescription>
                      Leave blank to save as draft. You can schedule or send it later.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  type="submit"
                  disabled={createCampaignMutation.isPending || updateCampaignMutation.isPending}
                >
                  {currentCampaign 
                    ? (updateCampaignMutation.isPending ? 'Saving...' : 'Save Changes') 
                    : (createCampaignMutation.isPending ? 'Creating...' : 'Create Campaign')}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Preview Campaign Dialog */}
      {previewCampaign && (
        <Dialog open={!!previewCampaign} onOpenChange={() => setPreviewCampaign(null)}>
          <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
              <DialogTitle>Campaign Preview: {previewCampaign.name}</DialogTitle>
              <DialogDescription>
                Preview how your email campaign will appear to recipients.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="border-b pb-2">
                <div><strong>Subject:</strong> {previewCampaign.subject}</div>
                <div className="mt-1"><strong>Recipient List:</strong> {previewCampaign.listName}</div>
                <div className="mt-1">
                  <Badge variant={getStatusVariant(previewCampaign.status)}>
                    {previewCampaign.status.charAt(0).toUpperCase() + previewCampaign.status.slice(1)}
                  </Badge>
                </div>
              </div>
              <div className="border rounded-md p-4 bg-background">
                <div className="p-6 text-center">
                  <Mail className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">Campaign Preview</h3>
                  <p className="text-muted-foreground mt-2">
                    Preview content would be displayed here, based on the selected template.
                  </p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => handleEditCampaign(previewCampaign)}
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit Campaign
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </EmailLayout>
  );
};

export default EmailCampaignsPage;