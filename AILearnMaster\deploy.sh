#!/bin/bash

# AI<PERSON>earnMaster Quick Deployment Script
# One-command production deployment

set -e

echo "🚀 AILearnMaster Quick Deployment"
echo "================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
NC='\033[0m'

print_header() {
    echo -e "${MAGENTA}$1${NC}"
    echo -e "${MAGENTA}$(echo "$1" | sed 's/./=/g')${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running in correct directory
check_directory() {
    if [ ! -f "package.json" ] || [ ! -d "server" ] || [ ! -d "client" ]; then
        print_error "Please run this script from the AILearnMaster root directory"
        exit 1
    fi
}

# Function to display deployment options
show_deployment_options() {
    print_header "Deployment Options"
    echo ""
    echo "1. 🔒 Security Validation Only"
    echo "2. 🏗️  Infrastructure Setup Only"
    echo "3. 🚀 Complete Production Deployment"
    echo "4. 📊 Setup Monitoring Only"
    echo "5. 🔍 Validate Existing Deployment"
    echo "6. 📋 Show Deployment Checklist"
    echo ""
    read -p "Select deployment option (1-6): " choice
    echo ""
}

# Function to run security validation
run_security_validation() {
    print_header "🔒 Security Validation"
    
    print_status "Installing dependencies..."
    npm ci >/dev/null 2>&1
    
    print_status "Running security tests..."
    if npm run security:production-ready; then
        print_success "Security validation passed!"
        return 0
    else
        print_error "Security validation failed!"
        return 1
    fi
}

# Function to setup infrastructure
setup_infrastructure() {
    print_header "🏗️ Infrastructure Setup"
    
    print_status "Setting up AWS infrastructure..."
    chmod +x deployment/setup-aws-infrastructure.sh
    chmod +x deployment/setup-secrets.sh
    
    if ./deployment/setup-aws-infrastructure.sh; then
        print_success "Infrastructure setup completed!"
        
        print_warning "Now setting up secrets..."
        if ./deployment/setup-secrets.sh; then
            print_success "Secrets configuration completed!"
            return 0
        else
            print_error "Secrets configuration failed!"
            return 1
        fi
    else
        print_error "Infrastructure setup failed!"
        return 1
    fi
}

# Function to run complete deployment
run_complete_deployment() {
    print_header "🚀 Complete Production Deployment"
    
    print_status "Starting complete deployment process..."
    chmod +x deployment/deploy-to-production.sh
    
    if ./deployment/deploy-to-production.sh; then
        print_success "Complete deployment finished!"
        return 0
    else
        print_error "Deployment failed!"
        return 1
    fi
}

# Function to setup monitoring
setup_monitoring() {
    print_header "📊 Monitoring Setup"
    
    print_status "Setting up production monitoring..."
    chmod +x deployment/setup-monitoring.sh
    
    if ./deployment/setup-monitoring.sh; then
        print_success "Monitoring setup completed!"
        return 0
    else
        print_error "Monitoring setup failed!"
        return 1
    fi
}

# Function to validate deployment
validate_deployment() {
    print_header "🔍 Deployment Validation"
    
    print_status "Validating production deployment..."
    chmod +x deployment/validate-production.sh
    
    if ./deployment/validate-production.sh; then
        print_success "Deployment validation passed!"
        return 0
    else
        print_warning "Some validation checks failed - review output above"
        return 1
    fi
}

# Function to show checklist
show_checklist() {
    print_header "📋 Deployment Checklist"
    
    if [ -f "deployment/PRODUCTION-DEPLOYMENT-CHECKLIST.md" ]; then
        echo "Opening deployment checklist..."
        if command -v code >/dev/null 2>&1; then
            code deployment/PRODUCTION-DEPLOYMENT-CHECKLIST.md
        elif command -v cat >/dev/null 2>&1; then
            cat deployment/PRODUCTION-DEPLOYMENT-CHECKLIST.md
        else
            print_status "Checklist available at: deployment/PRODUCTION-DEPLOYMENT-CHECKLIST.md"
        fi
    else
        print_error "Deployment checklist not found!"
        return 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    local missing_tools=()
    local required_tools=("aws" "node" "npm" "git" "curl")
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_error "Missing required tools: ${missing_tools[*]}"
        echo ""
        echo "Please install the missing tools and try again:"
        echo "• AWS CLI: https://aws.amazon.com/cli/"
        echo "• Node.js: https://nodejs.org/"
        echo "• Git: https://git-scm.com/"
        echo "• curl: Usually pre-installed"
        return 1
    fi
    
    # Check AWS configuration
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS CLI not configured. Please run 'aws configure' first."
        return 1
    fi
    
    # Check Node.js version
    local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 18 ]; then
        print_error "Node.js 18+ required. Current: $(node --version)"
        return 1
    fi
    
    print_success "All prerequisites met!"
    return 0
}

# Function to display final summary
display_summary() {
    print_header "🎉 Deployment Summary"
    
    echo "AILearnMaster deployment process completed!"
    echo ""
    echo "🌐 Production URLs:"
    echo "   Frontend: https://ailearn.com"
    echo "   API: https://api.ailearn.com"
    echo "   Admin: https://ailearn.com/admin"
    echo ""
    echo "📊 Monitoring:"
    echo "   CloudWatch Dashboard: AWS Console > CloudWatch > Dashboards"
    echo "   Application Logs: /aws/amplify/ailearn-master-prod"
    echo ""
    echo "🔧 Next Steps:"
    echo "   1. Monitor application performance"
    echo "   2. Test all functionality thoroughly"
    echo "   3. Set up user feedback collection"
    echo "   4. Plan for scaling and optimization"
    echo ""
    echo "📋 Documentation:"
    echo "   • deployment/PRODUCTION-DEPLOYMENT-CHECKLIST.md"
    echo "   • deployment/DEPLOYMENT-GUIDE.md"
    echo "   • monitoring-guide.md"
    echo ""
    print_success "AILearnMaster is now live in production! 🚀"
}

# Main function
main() {
    # Check if in correct directory
    check_directory
    
    # Show banner
    print_header "🚀 AILearnMaster Production Deployment"
    echo "Automated deployment script for AWS infrastructure"
    echo ""
    
    # Check prerequisites
    if ! check_prerequisites; then
        exit 1
    fi
    
    # Show deployment options
    show_deployment_options
    
    # Execute based on choice
    case $choice in
        1)
            if run_security_validation; then
                print_success "✅ Security validation completed successfully!"
            else
                print_error "❌ Security validation failed!"
                exit 1
            fi
            ;;
        2)
            if setup_infrastructure; then
                print_success "✅ Infrastructure setup completed successfully!"
                echo ""
                print_warning "Next steps:"
                echo "1. Configure environment variables"
                echo "2. Run complete deployment (option 3)"
            else
                print_error "❌ Infrastructure setup failed!"
                exit 1
            fi
            ;;
        3)
            if run_complete_deployment; then
                print_success "✅ Complete deployment finished successfully!"
                display_summary
            else
                print_error "❌ Complete deployment failed!"
                exit 1
            fi
            ;;
        4)
            if setup_monitoring; then
                print_success "✅ Monitoring setup completed successfully!"
            else
                print_error "❌ Monitoring setup failed!"
                exit 1
            fi
            ;;
        5)
            if validate_deployment; then
                print_success "✅ Deployment validation completed successfully!"
            else
                print_warning "⚠️ Some validation checks failed!"
                exit 1
            fi
            ;;
        6)
            show_checklist
            ;;
        *)
            print_error "Invalid option selected!"
            exit 1
            ;;
    esac
    
    echo ""
    print_success "Deployment operation completed!"
}

# Handle script interruption
trap 'print_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main "$@"
