name: Release

on:
  push:
    branches:
    - main
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions-ecosystem/action-regex-match@v2
      id: regex-match
      with:
        text: ${{ github.event.head_commit.message }}
        regex: '^Release ([^ ]+)'
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.8'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install setuptools wheel twine
    - name: Release
      if: ${{ steps.regex-match.outputs.match != '' }}
      uses: softprops/action-gh-release@v2
      with:
        tag_name: v${{ steps.regex-match.outputs.group1 }}
    - name: Build and publish
      if: ${{ steps.regex-match.outputs.match != '' }}
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        python -m build --sdist
        twine upload dist/*
