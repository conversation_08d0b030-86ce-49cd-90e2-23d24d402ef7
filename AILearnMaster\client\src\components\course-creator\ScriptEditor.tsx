import React, { useState, useEffect } from "react";
import { useMutation } from "@tanstack/react-query";
import { Loader2, Wand2, ChevronDown, ChevronUp, Sparkles, Book, MessageSquare, ArrowLeft, ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface ScriptEditorProps {
  courseTitle: string;
  courseDescription?: string;
  moduleTitle?: string;
  moduleDescription?: string;
  lessonTitle?: string;
  lessonDescription?: string;
  defaultValues?: {
    content: string;
    segments: Array<{
      id: string;
      content: string;
      type: "intro" | "body" | "conclusion" | "callout" | "explanation";
    }>;
  };
  onNext: (data: {
    content: string;
    segments: Array<{
      id: string;
      content: string;
      type: "intro" | "body" | "conclusion" | "callout" | "explanation";
    }>;
  }) => void;
  onBack: () => void;
}

export function ScriptEditor({
  courseTitle = "New Course",
  courseDescription = "",
  moduleTitle = "",
  moduleDescription = "",
  lessonTitle = "",
  lessonDescription = "",
  defaultValues = { content: "", segments: [] },
  onNext,
  onBack
}: ScriptEditorProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>("editor");
  const [advancedOptionsOpen, setAdvancedOptionsOpen] = useState(false);
  const [scriptContent, setScriptContent] = useState(defaultValues.content || "");
  const [generationProgress, setGenerationProgress] = useState(0);
  const [isShowingGuide, setIsShowingGuide] = useState(false);
  const [segments, setSegments] = useState(defaultValues.segments || []);
  
  // Generation options
  const [selectedTone, setSelectedTone] = useState("conversational");
  const [targetAudience, setTargetAudience] = useState("beginners");
  const [contentLength, setContentLength] = useState(600);
  const [includeExamples, setIncludeExamples] = useState(true);
  const [keyPoints, setKeyPoints] = useState("");

  // Help complete partial script
  const helpCompleteMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/ai/complete-script", {
        partialScript: scriptContent,
        courseTitle,
        lessonTitle: lessonTitle || "New Lesson",
        tone: selectedTone,
        targetAudience,
      });

      if (!response.ok) {
        throw new Error("Failed to complete script");
      }

      return response.json();
    },
    onMutate: () => {
      toast({
        title: "Helping complete your script",
        description: "We're using AI to help finish your script based on what you've written.",
      });

      // Simulate progress
      setGenerationProgress(0);
      const interval = setInterval(() => {
        setGenerationProgress((prev) => {
          if (prev >= 95) {
            clearInterval(interval);
            return 95;
          }
          return prev + 5;
        });
      }, 300);

      return () => clearInterval(interval);
    },
    onSuccess: (data) => {
      setGenerationProgress(100);
      
      if (data.script) {
        setScriptContent(data.script);
        
        // Reset progress after a short delay
        setTimeout(() => {
          setGenerationProgress(0);
        }, 1000);
      }
    },
    onError: (error) => {
      setGenerationProgress(0);
      toast({
        title: "Failed to complete script",
        description: "There was an error completing your script. Please try again or continue writing manually.",
        variant: "destructive",
      });
    },
  });

  // Generate full script
  const generateScriptMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/ai/generate-lesson-script", {
        courseTitle,
        courseDescription,
        moduleTitle,
        moduleDescription,
        lessonTitle: lessonTitle || "New Lesson",
        lessonDescription: lessonDescription || "",
        targetAudience,
        tone: selectedTone,
        wordCount: contentLength,
        includeExamples,
        keyPoints: keyPoints.split('\n').filter(point => point.trim() !== '')
      });

      if (!response.ok) {
        throw new Error("Failed to generate script");
      }

      return response.json();
    },
    onMutate: () => {
      toast({
        title: "Generating script",
        description: "We're creating a professional script based on your course details.",
      });

      // Simulate progress
      setGenerationProgress(0);
      const interval = setInterval(() => {
        setGenerationProgress((prev) => {
          if (prev >= 95) {
            clearInterval(interval);
            return 95;
          }
          return prev + 3;
        });
      }, 250);

      return () => clearInterval(interval);
    },
    onSuccess: (data) => {
      setGenerationProgress(100);
      
      if (data.script) {
        setScriptContent(data.script);
        
        // Process script into segments for the segments tab
        const processedSegments = processScriptIntoSegments(data.script);
        setSegments(processedSegments);
        
        // Reset progress after a short delay
        setTimeout(() => {
          setGenerationProgress(0);
        }, 1000);

        // Switch to editor tab to show the result
        setActiveTab("editor");
      }
    },
    onError: (error) => {
      setGenerationProgress(0);
      toast({
        title: "Failed to generate script",
        description: "There was an error generating your script. Please try again or write your script manually.",
        variant: "destructive",
      });
    },
  });

  // Generate script for specific segment
  const generateSegmentMutation = useMutation({
    mutationFn: async ({ segmentType, existingContent }: { segmentType: string; existingContent?: string }) => {
      const response = await apiRequest("POST", "/api/ai/generate-script-segment", {
        courseTitle,
        lessonTitle: lessonTitle || "New Lesson",
        segmentType,
        existingContent,
        targetAudience,
        tone: selectedTone
      });

      if (!response.ok) {
        throw new Error(`Failed to generate ${segmentType} segment`);
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      if (data.segment) {
        // Add the new segment or update existing one
        const newSegment = {
          id: Date.now().toString(),
          content: data.segment,
          type: variables.segmentType as any
        };
        
        setSegments(current => [...current, newSegment]);
        
        toast({
          title: "Segment generated",
          description: `Your new ${variables.segmentType} segment is ready to use.`,
        });
      }
    },
    onError: (error) => {
      toast({
        title: "Failed to generate segment",
        description: "There was an error generating this segment. Please try again or write it manually.",
        variant: "destructive",
      });
    },
  });

  // Process a full script into logical segments and clean it for TTS
  const processScriptIntoSegments = (script: string) => {
    // Clean script by removing markdown formatting and other non-TTS content
    let cleanedScript = script
      // Remove markdown headers, bold, italics, etc.
      .replace(/#+\s+/g, '')
      .replace(/\*\*/g, '')
      .replace(/\*/g, '')
      .replace(/\_\_/g, '')
      .replace(/\_/g, '')
      // Remove non-TTS friendly characters
      .replace(/\[.*?\]/g, '') // Remove square brackets with content
      .replace(/\(.*?\)/g, '') // Remove parentheses with content that might be links
      .replace(/https?:\/\/\S+/g, '') // Remove URLs
      .replace(/\n\s*\n/g, '\n\n'); // Normalize spacing between paragraphs
    
    // Split into lines for segment processing
    const lines = cleanedScript.split("\n").filter(line => line.trim() !== "");
    
    // Find potential section headers
    const processedSegments = [];
    let currentContent = "";
    
    // Create intro segment (first 10-15% of content)
    const introSize = Math.max(1, Math.ceil(lines.length * 0.15));
    const introContent = lines.slice(0, introSize).join("\n");
    
    processedSegments.push({
      id: Date.now().toString() + "-intro",
      content: introContent,
      type: "intro" as const
    });
    
    // Process main content (middle 70-80%)
    const mainContentSize = Math.ceil(lines.length * 0.7);
    const mainContentEnd = introSize + mainContentSize;
    
    // Group main content into logical segments
    let segmentStartIdx = introSize;
    
    // Look for natural breaks in content to create segments
    for (let i = introSize; i < mainContentEnd; i++) {
      const line = lines[i];
      const isShortLine = line.length < 50; 
      const isEndOfParagraph = i < lines.length - 1 && lines[i+1].trim() === "";
      
      // Conditions for breaking into a new segment:
      // 1. Short line that might be a header
      // 2. Current segment is getting too long (more than ~5 paragraphs)
      // 3. Natural paragraph break + already have substantial content
      if ((isShortLine && i > segmentStartIdx + 3) || 
          (i - segmentStartIdx > 10) ||
          (isEndOfParagraph && i - segmentStartIdx > 5)) {
        
        // Complete current segment
        const segmentContent = lines.slice(segmentStartIdx, i + 1).join("\n");
        if (segmentContent.trim()) {
          processedSegments.push({
            id: Date.now().toString() + "-" + processedSegments.length,
            content: segmentContent,
            type: "body" as const
          });
        }
        
        // Start new segment
        segmentStartIdx = i + 1;
      }
    }
    
    // Add any remaining main content
    if (segmentStartIdx < mainContentEnd) {
      const remainingContent = lines.slice(segmentStartIdx, mainContentEnd).join("\n");
      if (remainingContent.trim()) {
        processedSegments.push({
          id: Date.now().toString() + "-" + processedSegments.length,
          content: remainingContent,
          type: "body" as const
        });
      }
    }
    
    // Create conclusion (last 10-15% of content)
    const conclusionContent = lines.slice(mainContentEnd).join("\n");
    if (conclusionContent.trim()) {
      processedSegments.push({
        id: Date.now().toString() + "-conclusion",
        content: conclusionContent,
        type: "conclusion" as const
      });
    }
    
    return processedSegments;
  };

  // Handle saving the script
  const handleSave = () => {
    onNext({
      content: scriptContent,
      segments
    });
  };

  // Add an empty segment
  const addEmptySegment = (type: "intro" | "body" | "conclusion" | "callout" | "explanation") => {
    const newSegment = {
      id: Date.now().toString(),
      content: "",
      type
    };
    
    setSegments(current => [...current, newSegment]);
  };

  // Update segment content
  const updateSegmentContent = (id: string, content: string) => {
    setSegments(current => 
      current.map(segment => 
        segment.id === id ? { ...segment, content } : segment
      )
    );
  };

  // Remove a segment
  const removeSegment = (id: string) => {
    setSegments(current => current.filter(segment => segment.id !== id));
  };

  // Update segment type
  const updateSegmentType = (id: string, type: "intro" | "body" | "conclusion" | "callout" | "explanation") => {
    setSegments(current => 
      current.map(segment => 
        segment.id === id ? { ...segment, type } : segment
      )
    );
  };

  // Rebuild full script from segments when in segments mode
  useEffect(() => {
    if (activeTab === "segments" && segments.length > 0) {
      const fullScript = segments.map(segment => segment.content).join("\n\n");
      setScriptContent(fullScript);
    }
  }, [segments, activeTab]);

  return (
    <div className="flex flex-col space-y-4 w-full max-w-5xl mx-auto">
      <div className="flex flex-col space-y-2">
        <h2 className="text-3xl font-bold">Script Creation</h2>
        <p className="text-muted-foreground">
          Write or generate scripts for your video content
        </p>
      </div>

      {/* Content tabs: Editor and Segments */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="editor" className="flex items-center gap-2">
              <Book className="h-4 w-4" />
              Full Script
            </TabsTrigger>
            <TabsTrigger value="segments" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Segments
            </TabsTrigger>
          </TabsList>
          
          {/* Generation progress */}
          {generationProgress > 0 && (
            <div className="flex items-center gap-2 w-48">
              <Progress value={generationProgress} className="h-2" />
              <span className="text-xs text-muted-foreground">{generationProgress}%</span>
            </div>
          )}
        </div>

        {/* Full script editor */}
        <TabsContent value="editor" className="mt-0">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between">
                <div>
                  <CardTitle>{lessonTitle || "New Lesson"}</CardTitle>
                  <CardDescription>
                    {lessonDescription || "Write your script content below"}
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    disabled={helpCompleteMutation.isPending}
                    onClick={() => helpCompleteMutation.mutate()}
                  >
                    {helpCompleteMutation.isPending ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Sparkles className="mr-2 h-4 w-4" />
                    )}
                    Help Complete
                  </Button>
                  <Button 
                    variant="default" 
                    size="sm"
                    disabled={generateScriptMutation.isPending}
                    onClick={() => generateScriptMutation.mutate()}
                  >
                    {generateScriptMutation.isPending ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Wand2 className="mr-2 h-4 w-4" />
                    )}
                    Generate Full Script
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Start writing your script here, or use the 'Generate' button to create one automatically..."
                value={scriptContent}
                onChange={(e) => setScriptContent(e.target.value)}
                className="min-h-[400px] font-mono text-sm resize-y"
              />
            </CardContent>
            <CardFooter className="flex justify-between pt-2">
              <Button 
                variant="outline" 
                onClick={() => setAdvancedOptionsOpen(!advancedOptionsOpen)}
                className="text-xs"
              >
                {advancedOptionsOpen ? (
                  <>
                    <ChevronUp className="h-4 w-4 mr-1" />
                    Hide Advanced Options
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4 mr-1" />
                    Show Advanced Options
                  </>
                )}
              </Button>
              <div className="flex items-center gap-2">
                <Button 
                  variant="ghost" 
                  onClick={() => setIsShowingGuide(!isShowingGuide)}
                  className="text-xs"
                >
                  {isShowingGuide ? "Hide Guide" : "Writing Guide"}
                </Button>
              </div>
            </CardFooter>
          </Card>

          {/* Advanced generation options */}
          {advancedOptionsOpen && (
            <Card className="mt-4">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Advanced Script Generation Options</CardTitle>
                <CardDescription>
                  Customize how your script will be generated
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="tone">Tone</Label>
                      <Select value={selectedTone} onValueChange={setSelectedTone}>
                        <SelectTrigger id="tone">
                          <SelectValue placeholder="Select tone" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="conversational">Conversational</SelectItem>
                          <SelectItem value="professional">Professional</SelectItem>
                          <SelectItem value="academic">Academic</SelectItem>
                          <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
                          <SelectItem value="humorous">Humorous</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="targetAudience">Target Audience</Label>
                      <Select value={targetAudience} onValueChange={setTargetAudience}>
                        <SelectTrigger id="targetAudience">
                          <SelectValue placeholder="Select audience" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="beginners">Beginners</SelectItem>
                          <SelectItem value="intermediate">Intermediate</SelectItem>
                          <SelectItem value="advanced">Advanced</SelectItem>
                          <SelectItem value="mixed">Mixed Levels</SelectItem>
                          <SelectItem value="professionals">Professionals</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label htmlFor="includeExamples">Include practical examples</Label>
                      <Switch 
                        id="includeExamples"
                        checked={includeExamples}
                        onCheckedChange={setIncludeExamples}
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <Label htmlFor="contentLength">Content Length (words)</Label>
                        <span className="text-sm text-muted-foreground">{contentLength}</span>
                      </div>
                      <Slider
                        id="contentLength"
                        min={200}
                        max={2000}
                        step={100}
                        value={[contentLength]}
                        onValueChange={(value) => setContentLength(value[0])}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="keyPoints">Key Points to Include (one per line)</Label>
                      <Textarea
                        id="keyPoints"
                        placeholder="Enter key points to cover in the script..."
                        value={keyPoints}
                        onChange={(e) => setKeyPoints(e.target.value)}
                        className="h-24 text-sm"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Script writing guide */}
          {isShowingGuide && (
            <Card className="mt-4">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Script Writing Guide</CardTitle>
                <CardDescription>
                  Tips to help you write effective educational scripts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="structure">
                    <AccordionTrigger>Effective Script Structure</AccordionTrigger>
                    <AccordionContent>
                      <ul className="list-disc pl-6 space-y-2">
                        <li><strong>Introduction (10%):</strong> Hook, introduce yourself, preview the lesson</li>
                        <li><strong>Main Content (80%):</strong> Teach in logical segments, use transitions between points</li>
                        <li><strong>Conclusion (10%):</strong> Summarize key points, give next steps</li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="tips">
                    <AccordionTrigger>Writing Tips</AccordionTrigger>
                    <AccordionContent>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Write like you speak - use conversational language</li>
                        <li>Keep sentences short (15-20 words maximum)</li>
                        <li>Avoid jargon unless you explain it</li>
                        <li>Use active voice rather than passive voice</li>
                        <li>Include pauses and breathers with [pause] notation</li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                  
                  <AccordionItem value="engagement">
                    <AccordionTrigger>Keeping Viewers Engaged</AccordionTrigger>
                    <AccordionContent>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Ask rhetorical questions to maintain interest</li>
                        <li>Use relatable examples and stories</li>
                        <li>Employ the "bridge" technique: problem → solution → benefit</li>
                        <li>Add emphasis cues: [emphasize], [enthusiastic], etc.</li>
                        <li>Include visual cues: [show diagram], [on-screen text], etc.</li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Segments editor */}
        <TabsContent value="segments" className="mt-0">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Script Segments</CardTitle>
                  <CardDescription>
                    Break your script into manageable segments
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Select 
                    value="add" 
                    onValueChange={(value) => {
                      if (value !== "add") {
                        addEmptySegment(value as any);
                      }
                    }}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Add segment" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="add">Add segment...</SelectItem>
                      <SelectItem value="intro">Introduction</SelectItem>
                      <SelectItem value="body">Main content</SelectItem>
                      <SelectItem value="conclusion">Conclusion</SelectItem>
                      <SelectItem value="callout">Callout / Important point</SelectItem>
                      <SelectItem value="explanation">Detailed explanation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {segments.length === 0 ? (
                <div className="text-center py-8 border rounded-md bg-muted/20">
                  <p className="text-muted-foreground">No segments yet. Add a segment or generate a script first.</p>
                  <Button 
                    variant="outline" 
                    className="mt-4" 
                    onClick={() => generateScriptMutation.mutate()}
                    disabled={generateScriptMutation.isPending}
                  >
                    {generateScriptMutation.isPending ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Wand2 className="mr-2 h-4 w-4" />
                    )}
                    Generate Script
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {segments.map((segment, index) => (
                    <Card key={segment.id} className={`
                      border-l-4 
                      ${segment.type === 'intro' ? 'border-l-blue-500' : ''} 
                      ${segment.type === 'body' ? 'border-l-slate-500' : ''} 
                      ${segment.type === 'conclusion' ? 'border-l-green-500' : ''} 
                      ${segment.type === 'callout' ? 'border-l-amber-500' : ''} 
                      ${segment.type === 'explanation' ? 'border-l-purple-500' : ''} 
                    `}>
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-2">
                            <Badge 
                              variant="outline" 
                              className={`
                                ${segment.type === 'intro' ? 'bg-blue-100 text-blue-800' : ''} 
                                ${segment.type === 'body' ? 'bg-slate-100 text-slate-800' : ''} 
                                ${segment.type === 'conclusion' ? 'bg-green-100 text-green-800' : ''} 
                                ${segment.type === 'callout' ? 'bg-amber-100 text-amber-800' : ''} 
                                ${segment.type === 'explanation' ? 'bg-purple-100 text-purple-800' : ''} 
                              `}
                            >
                              {segment.type.charAt(0).toUpperCase() + segment.type.slice(1)}
                            </Badge>
                            <Select 
                              value={segment.type} 
                              onValueChange={(value) => updateSegmentType(segment.id, value as any)}
                            >
                              <SelectTrigger className="h-7 w-32">
                                <SelectValue placeholder="Type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="intro">Introduction</SelectItem>
                                <SelectItem value="body">Main content</SelectItem>
                                <SelectItem value="conclusion">Conclusion</SelectItem>
                                <SelectItem value="callout">Callout</SelectItem>
                                <SelectItem value="explanation">Explanation</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div className="flex gap-2">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => removeSegment(segment.id)}
                              className="h-7 px-2 text-xs text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              Remove
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => generateSegmentMutation.mutate({
                                segmentType: segment.type,
                                existingContent: segment.content
                              })}
                              disabled={generateSegmentMutation.isPending}
                              className="h-7 px-2 text-xs flex items-center gap-1"
                            >
                              {generateSegmentMutation.isPending ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <Wand2 className="h-3 w-3" />
                              )}
                              Regenerate
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <Textarea
                          value={segment.content}
                          onChange={(e) => updateSegmentContent(segment.id, e.target.value)}
                          className="min-h-[100px] text-sm"
                          placeholder={`Write ${segment.type} content here...`}
                        />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Navigation buttons */}
      <div className="flex justify-between pt-4">
        <Button 
          variant="outline" 
          onClick={onBack} 
          className="gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <Button 
          onClick={handleSave} 
          className="gap-2"
        >
          Continue
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}