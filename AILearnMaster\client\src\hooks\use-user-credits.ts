import { useMutation, useQuery } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export interface UserCredits {
  balance: number;
  lifetimeUsage: number;
  lastPurchaseDate: string | null;
  totalPurchases: number;
}

export interface CreditUpdateResponse {
  success: boolean;
  message: string;
  credits: UserCredits;
}

interface UseCreditsParams {
  amount: number;
  description?: string;
  resourceId?: number;
  resourceType?: string;
}

interface PurchaseCreditsParams {
  amount: number;
  paymentMethodId: string;
}

/**
 * Custom hook to manage user credits
 * Provides functionality to get, purchase, and use credits
 */
export function useUserCredits() {
  const { toast } = useToast();

  // Query to fetch user's current credit balance
  const { 
    data: credits,
    isLoading: isLoadingCredits,
    error: creditsError,
    refetch: refetchCredits
  } = useQuery<UserCredits>({
    queryKey: ['/api/user/credits'],
    refetchOnWindowFocus: false,
  });

  // Mutation to use credits for a feature
  const useCredits = useMutation({
    mutationFn: async (params: UseCreditsParams) => {
      const response = await apiRequest('POST', '/api/user/credits/use', params);
      const data = await response.json();
      return data as CreditUpdateResponse;
    },
    onSuccess: () => {
      // Invalidate and refetch credits after usage
      queryClient.invalidateQueries({ queryKey: ['/api/user/credits'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error using credits",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Mutation to purchase new credits
  const purchaseCredits = useMutation({
    mutationFn: async (params: PurchaseCreditsParams) => {
      const response = await apiRequest('POST', '/api/user/credits/purchase', params);
      const data = await response.json();
      return data as CreditUpdateResponse;
    },
    onSuccess: (data) => {
      toast({
        title: "Credits purchased!",
        description: data.message || `Successfully purchased ${data.credits.balance} credits`,
      });
      
      // Invalidate and refetch credits after purchase
      queryClient.invalidateQueries({ queryKey: ['/api/user/credits'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error purchasing credits",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Function to check if a user has enough credits for an operation
  const hasEnoughCredits = (requiredAmount: number): boolean => {
    if (!credits) return false;
    return credits.balance >= requiredAmount;
  };

  return {
    credits,
    isLoadingCredits,
    creditsError,
    refetchCredits,
    useCredits,
    purchaseCredits,
    hasEnoughCredits,
  };
}