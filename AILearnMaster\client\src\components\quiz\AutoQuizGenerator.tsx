import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Brain, 
  Sparkles, 
  CheckCircle2, 
  AlertCircle,
  RefreshCw,
  BookOpen,
  Target,
  Zap,
  ArrowRight
} from 'lucide-react';
import QuizDisplay from './QuizDisplay';

interface Module {
  id: string;
  title: string;
  lessons: Array<{
    id: string;
    title: string;
  }>;
}

interface CourseScripts {
  [moduleId: string]: {
    [lessonId: string]: string;
  };
}

interface Quiz {
  id: string;
  moduleId: string;
  moduleTitle: string;
  questions: any[];
  generatedAt: Date;
}

interface AutoQuizGeneratorProps {
  modules: Module[];
  courseScripts: CourseScripts;
  onQuizzesGenerated?: (quizzes: Quiz[]) => void;
}

export default function AutoQuizGenerator({ 
  modules, 
  courseScripts, 
  onQuizzesGenerated 
}: AutoQuizGeneratorProps) {
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentModule, setCurrentModule] = useState<string>('');
  const [generatedQuizzes, setGeneratedQuizzes] = useState<Quiz[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  const generateQuizForModule = async (module: Module): Promise<Quiz | null> => {
    try {
      // Collect all content from the module's lessons
      const moduleContent = module.lessons
        .map(lesson => {
          const lessonScript = courseScripts[module.id]?.[lesson.id];
          return lessonScript ? `${lesson.title}: ${lessonScript}` : '';
        })
        .filter(content => content.length > 0)
        .join('\n\n');

      if (!moduleContent || moduleContent.length < 50) {
        throw new Error(`Insufficient content for module: ${module.title}`);
      }

      const response = await fetch('/api/quiz-generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: `Module: ${module.title}\n\n${moduleContent}`,
          questionCount: Math.min(Math.max(3, Math.floor(module.lessons.length * 2)), 10),
          difficulty: 'medium'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = `Failed to generate quiz for ${module.title}`;
        
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorMessage;
        } catch {
          if (errorText.includes('API key')) {
            errorMessage = 'API service needs configuration. Please provide your OpenAI or Gemini API keys.';
          } else if (errorText.includes('<!DOCTYPE html>')) {
            errorMessage = 'Service connection issue. Please try again.';
          }
        }
        
        throw new Error(errorMessage);
      }

      const result = await response.json();
      
      if (!result.questions || !Array.isArray(result.questions) || result.questions.length === 0) {
        throw new Error(`No questions generated for ${module.title}`);
      }

      return {
        id: `quiz-${module.id}-${Date.now()}`,
        moduleId: module.id,
        moduleTitle: module.title,
        questions: result.questions,
        generatedAt: new Date()
      };

    } catch (error: any) {
      console.error(`Quiz generation failed for module ${module.title}:`, error);
      throw error;
    }
  };

  const generateAllQuizzes = async () => {
    setIsGenerating(true);
    setProgress(0);
    setGeneratedQuizzes([]);
    setErrors([]);

    const quizzes: Quiz[] = [];
    const moduleErrors: string[] = [];
    const totalModules = modules.length;

    try {
      for (let i = 0; i < modules.length; i++) {
        const module = modules[i];
        setCurrentModule(module.title);
        setProgress((i / totalModules) * 100);

        try {
          const quiz = await generateQuizForModule(module);
          if (quiz) {
            quizzes.push(quiz);
            console.log(`Generated quiz for ${module.title}: ${quiz.questions.length} questions`);
          }
        } catch (error: any) {
          const errorMsg = `${module.title}: ${error.message}`;
          moduleErrors.push(errorMsg);
          console.error(errorMsg);
        }

        // Add a small delay to prevent overwhelming the API
        if (i < modules.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      setProgress(100);
      setGeneratedQuizzes(quizzes);
      setErrors(moduleErrors);

      if (quizzes.length > 0) {
        onQuizzesGenerated?.(quizzes);
        
        toast({
          title: "Quizzes Generated Successfully",
          description: `Created ${quizzes.length} quizzes with ${quizzes.reduce((sum, q) => sum + q.questions.length, 0)} total questions`,
        });
      }

      if (moduleErrors.length > 0) {
        toast({
          title: `${quizzes.length} Quizzes Created, ${moduleErrors.length} Failed`,
          description: "Some modules couldn't be processed. Check the details below.",
          variant: "destructive",
        });
      }

    } catch (error: any) {
      toast({
        title: "Quiz Generation Failed",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
      setCurrentModule('');
    }
  };

  const hasValidContent = modules.some(module => 
    module.lessons.some(lesson => 
      courseScripts[module.id]?.[lesson.id] && 
      courseScripts[module.id][lesson.id].length > 50
    )
  );

  const totalQuestions = generatedQuizzes.reduce((sum, quiz) => sum + quiz.questions.length, 0);

  return (
    <div className="space-y-6">
      {/* Header Card */}
      <Card className="border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Brain className="h-6 w-6" />
            Auto Quiz Generator
          </CardTitle>
          <CardDescription className="text-blue-600">
            Generate intelligent quizzes for all your course modules with one click using AI
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-700">{modules.length}</div>
                <div className="text-sm text-blue-600">Modules</div>
              </div>
              <ArrowRight className="h-4 w-4 text-blue-400" />
              <div className="text-center">
                <div className="text-2xl font-bold text-green-700">{generatedQuizzes.length}</div>
                <div className="text-sm text-green-600">Quizzes Created</div>
              </div>
              <ArrowRight className="h-4 w-4 text-green-400" />
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-700">{totalQuestions}</div>
                <div className="text-sm text-purple-600">Questions</div>
              </div>
            </div>
            
            <Button
              onClick={generateAllQuizzes}
              disabled={!hasValidContent || isGenerating}
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Generate All Quizzes
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Progress Card */}
      {isGenerating && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Generating Quizzes...</h3>
                <span className="text-sm text-muted-foreground">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
              {currentModule && (
                <p className="text-sm text-muted-foreground">
                  Current: {currentModule}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      {(generatedQuizzes.length > 0 || errors.length > 0) && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Successful Quizzes */}
          {generatedQuizzes.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-700">
                  <CheckCircle2 className="h-5 w-5" />
                  Generated Quizzes ({generatedQuizzes.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {generatedQuizzes.map((quiz) => (
                    <div
                      key={quiz.id}
                      className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200"
                    >
                      <div>
                        <h4 className="font-medium text-green-800">{quiz.moduleTitle}</h4>
                        <p className="text-sm text-green-600">
                          {quiz.questions.length} questions generated
                        </p>
                      </div>
                      <Badge variant="outline" className="text-green-700 border-green-300">
                        <Target className="h-3 w-3 mr-1" />
                        Ready
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Errors */}
          {errors.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <AlertCircle className="h-5 w-5" />
                  Failed Modules ({errors.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {errors.map((error, index) => (
                    <div
                      key={index}
                      className="p-3 bg-red-50 rounded-lg border border-red-200"
                    >
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Prerequisites Check */}
      {!hasValidContent && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-6">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-yellow-800 mb-1">
                  Content Required for Quiz Generation
                </h3>
                <p className="text-sm text-yellow-700 mb-3">
                  To generate quizzes automatically, your modules need lesson content (scripts). 
                  Please complete the script generation step first.
                </p>
                <div className="space-y-1">
                  {modules.map(module => {
                    const hasContent = module.lessons.some(lesson => 
                      courseScripts[module.id]?.[lesson.id] && 
                      courseScripts[module.id][lesson.id].length > 50
                    );
                    return (
                      <div key={module.id} className="flex items-center gap-2 text-sm">
                        {hasContent ? (
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                        ) : (
                          <AlertCircle className="h-4 w-4 text-yellow-600" />
                        )}
                        <span className={hasContent ? 'text-green-700' : 'text-yellow-700'}>
                          {module.title}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI Features Info */}
      {generatedQuizzes.length === 0 && (
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-start gap-3">
              <Sparkles className="h-5 w-5 text-purple-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-purple-800 mb-2">
                  AI-Powered Quiz Generation Features
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-purple-700">
                      <BookOpen className="h-4 w-4" />
                      Content Analysis
                    </div>
                    <p className="text-purple-600 ml-6">
                      AI analyzes your lesson content to create relevant questions
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-purple-700">
                      <Target className="h-4 w-4" />
                      Smart Question Types
                    </div>
                    <p className="text-purple-600 ml-6">
                      Multiple choice, true/false, and fill-in-the-blank questions
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-purple-700">
                      <Brain className="h-4 w-4" />
                      Adaptive Difficulty
                    </div>
                    <p className="text-purple-600 ml-6">
                      Question difficulty adapts to content complexity
                    </p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-purple-700">
                      <CheckCircle2 className="h-4 w-4" />
                      Instant Results
                    </div>
                    <p className="text-purple-600 ml-6">
                      Complete quiz generation in minutes, not hours
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generated Quizzes Display */}
      {generatedQuizzes.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">Generated Quizzes</h2>
            <Badge variant="secondary" className="text-sm">
              {totalQuestions} Questions Created
            </Badge>
          </div>
          <QuizDisplay 
            quizzes={generatedQuizzes}
            onEditQuiz={(quiz) => {
              console.log('Edit quiz:', quiz);
              // Handle quiz editing
            }}
            onPreviewQuiz={(quiz) => {
              console.log('Preview quiz:', quiz);
              // Handle quiz preview
            }}
          />
        </div>
      )}
    </div>
  );
}