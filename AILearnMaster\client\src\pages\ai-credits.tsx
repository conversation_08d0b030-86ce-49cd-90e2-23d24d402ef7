import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  CreditCard, 
  Zap, 
  ArrowUpRight, 
  Info, 
  TrendingUp, 
  Users, 
  Crown, 
  Star,
  Clock,
  Image,
  MessageSquare,
  Mic
} from 'lucide-react';
import { apiRequest } from '@/lib/queryClient';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

interface User {
  id: number;
  username: string;
  email: string;
  aiCredits: number;
  subscription?: string;
}

interface CreditBundle {
  id: string;
  name: string;
  credits: number;
  price: number;
  popular?: boolean;
  bonus?: number;
}

interface CreditUsage {
  id: number;
  type: 'text' | 'image' | 'voice';
  action: string;
  creditsUsed: number;
  createdAt: string;
}

const creditBundles: CreditBundle[] = [
  {
    id: 'bundle_10',
    name: 'Starter Pack',
    credits: 10,
    price: 10,
  },
  {
    id: 'bundle_25',
    name: 'Popular Choice',
    credits: 25,
    price: 25,
    popular: true,
    bonus: 5
  },
  {
    id: 'bundle_50',
    name: 'Power User',
    credits: 50,
    price: 50,
    bonus: 10
  }
];

const planLimits = {
  starter: { credits: 5, name: 'Starter' },
  pro: { credits: 15, name: 'Pro' },
  creator: { credits: 50, name: 'Creator' },
  enterprise: { credits: 200, name: 'Enterprise' }
};

const creditCosts = {
  text: 1,
  image: 1,
  voice: 2
};

function CreditPurchaseForm({ bundle, onSuccess }: { bundle: CreditBundle; onSuccess: () => void }) {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Create payment intent
      const response = await apiRequest('POST', '/api/payments/create-credit-purchase', {
        bundleId: bundle.id,
        amount: bundle.price * 100 // Convert to cents
      });

      const { clientSecret } = await response.json();

      // Confirm payment
      const result = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: elements.getElement(CardElement)!,
        }
      });

      if (result.error) {
        setError(result.error.message || 'Payment failed');
      } else {
        onSuccess();
      }
    } catch (err) {
      setError('Failed to process payment');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="p-4 border rounded-lg bg-gray-50">
        <CardElement
          options={{
            style: {
              base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                  color: '#aab7c4',
                },
              },
            },
          }}
        />
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <Button 
        type="submit" 
        disabled={!stripe || isProcessing} 
        className="w-full"
      >
        {isProcessing ? 'Processing...' : `Purchase ${bundle.credits} Credits for $${bundle.price}`}
      </Button>
    </form>
  );
}

function CreditUsageChart({ usage }: { usage: CreditUsage[] }) {
  const textUsage = usage.filter(u => u.type === 'text').length;
  const imageUsage = usage.filter(u => u.type === 'image').length;
  const voiceUsage = usage.filter(u => u.type === 'voice').length;
  
  const totalCreditsUsed = textUsage * creditCosts.text + imageUsage * creditCosts.image + voiceUsage * creditCosts.voice;

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4 text-blue-500" />
            <div>
              <div className="text-sm font-medium">Text Generation</div>
              <div className="text-2xl font-bold">{textUsage}</div>
              <div className="text-xs text-muted-foreground">{textUsage} credits used</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-2">
            <Image className="h-4 w-4 text-green-500" />
            <div>
              <div className="text-sm font-medium">Image Generation</div>
              <div className="text-2xl font-bold">{imageUsage}</div>
              <div className="text-xs text-muted-foreground">{imageUsage} credits used</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-2">
            <Mic className="h-4 w-4 text-purple-500" />
            <div>
              <div className="text-sm font-medium">Voice Generation</div>
              <div className="text-2xl font-bold">{voiceUsage}</div>
              <div className="text-xs text-muted-foreground">{voiceUsage * 2} credits used</div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}

export default function AICreditsPage() {
  const [selectedBundle, setSelectedBundle] = useState<CreditBundle | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  const { data: user, isLoading: userLoading } = useQuery({
    queryKey: ['/api/auth/me'],
    queryFn: () => apiRequest('GET', '/api/auth/me').then(res => res.json()),
  });

  const { data: creditUsage = [], isLoading: usageLoading } = useQuery({
    queryKey: ['/api/ai-credits/usage'],
    queryFn: () => apiRequest('GET', '/api/ai-credits/usage').then(res => res.json()),
  });

  const purchaseSuccess = () => {
    setIsDialogOpen(false);
    setSelectedBundle(null);
    queryClient.invalidateQueries({ queryKey: ['/api/auth/me'] });
    queryClient.invalidateQueries({ queryKey: ['/api/ai-credits/usage'] });
  };

  if (userLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Please log in to view your AI credits information.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const currentPlan = user.subscription || 'starter';
  const planInfo = planLimits[currentPlan as keyof typeof planLimits] || planLimits.starter;
  const creditsUsed = creditUsage.reduce((total: number, usage: CreditUsage) => total + usage.creditsUsed, 0);
  const remainingCredits = Math.max(0, user.aiCredits);
  const usagePercentage = planInfo.credits > 0 ? Math.min(100, (creditsUsed / planInfo.credits) * 100) : 0;

  return (
    <TooltipProvider>
      <div className="max-w-6xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">AI Credits</h1>
            <p className="text-muted-foreground">Manage your AI usage and purchase additional credits</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <a href="/pricing">View Pricing Plans</a>
            </Button>
            <Button>
              <Crown className="h-4 w-4 mr-2" />
              Upgrade Plan
            </Button>
          </div>
        </div>

        {/* Current Plan & Credits Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Plan</CardTitle>
              <Badge variant={currentPlan === 'enterprise' ? 'default' : 'secondary'}>
                {planInfo.name}
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{planInfo.credits} credits/month</div>
              <p className="text-xs text-muted-foreground">
                Monthly allowance
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Available Credits</CardTitle>
              <Zap className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{remainingCredits}</div>
              <p className="text-xs text-muted-foreground">
                Ready to use
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Credits Used</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{creditsUsed || 0}</div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Usage Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Monthly Usage
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-4 w-4 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-sm space-y-1">
                    <p>Credit costs:</p>
                    <p>• Text generation: 1 credit</p>
                    <p>• Image generation: 1 credit</p>
                    <p>• Voice generation: 2 credits</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </CardTitle>
            <CardDescription>
              Track your AI usage across different features
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Monthly Credits Used</span>
                <span>{creditsUsed} / {planInfo.credits}</span>
              </div>
              <Progress value={usagePercentage} className="h-2" />
            </div>
            
            {!usageLoading && <CreditUsageChart usage={creditUsage} />}
          </CardContent>
        </Card>

        {/* Buy More Credits */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Buy Additional Credits
            </CardTitle>
            <CardDescription>
              Purchase extra credits when you need them most
              <Tooltip>
                <TooltipTrigger className="ml-2">
                  <Info className="h-4 w-4 text-muted-foreground inline" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>1 credit = $1. Credits never expire and roll over each month.</p>
                </TooltipContent>
              </Tooltip>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {creditBundles.map((bundle) => (
                <Card 
                  key={bundle.id} 
                  className={`relative cursor-pointer transition-all hover:shadow-md ${
                    bundle.popular ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => {
                    setSelectedBundle(bundle);
                    setIsDialogOpen(true);
                  }}
                >
                  {bundle.popular && (
                    <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                      Most Popular
                    </Badge>
                  )}
                  <CardContent className="p-6 text-center">
                    <div className="text-2xl font-bold">{bundle.credits} Credits</div>
                    {bundle.bonus && (
                      <div className="text-sm text-green-600">+{bundle.bonus} bonus credits</div>
                    )}
                    <div className="text-3xl font-bold text-primary mt-2">${bundle.price}</div>
                    <div className="text-sm text-muted-foreground">
                      ${(bundle.price / (bundle.credits + (bundle.bonus || 0))).toFixed(2)} per credit
                    </div>
                    <Button className="w-full mt-4" variant={bundle.popular ? 'default' : 'outline'}>
                      Purchase Now
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>Your latest AI credit usage</CardDescription>
          </CardHeader>
          <CardContent>
            {usageLoading ? (
              <div className="space-y-2">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center space-x-4">
                    <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
                    <div className="flex-1 space-y-1">
                      <div className="h-4 bg-gray-200 rounded animate-pulse" />
                      <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse" />
                    </div>
                  </div>
                ))}
              </div>
            ) : !creditUsage || creditUsage.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No AI activity yet. Start creating to see your usage here!
              </div>
            ) : (
              <div className="space-y-3">
                {creditUsage?.slice(0, 10).map((usage: CreditUsage) => (
                  <div key={usage.id} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-3">
                      {usage.type === 'text' && <MessageSquare className="h-4 w-4 text-blue-500" />}
                      {usage.type === 'image' && <Image className="h-4 w-4 text-green-500" />}
                      {usage.type === 'voice' && <Mic className="h-4 w-4 text-purple-500" />}
                      <div>
                        <div className="text-sm font-medium">{usage.action}</div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(usage.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    <Badge variant="outline">-{usage.creditsUsed} credits</Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Purchase Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Purchase Credits</DialogTitle>
              <DialogDescription>
                {selectedBundle && (
                  <>Add {selectedBundle.credits} credits to your account for ${selectedBundle.price}</>
                )}
              </DialogDescription>
            </DialogHeader>
            
            {selectedBundle && (
              <Elements stripe={stripePromise}>
                <CreditPurchaseForm 
                  bundle={selectedBundle} 
                  onSuccess={purchaseSuccess}
                />
              </Elements>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
}