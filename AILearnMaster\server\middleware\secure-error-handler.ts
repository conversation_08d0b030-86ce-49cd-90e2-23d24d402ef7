/**
 * Secure Error Handler Middleware
 * Sanitizes error messages for production to prevent information disclosure
 */

import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';

interface SecurityError extends Error {
  status?: number;
  statusCode?: number;
  code?: string;
  expose?: boolean;
}

interface ErrorLogEntry {
  errorId: string;
  timestamp: Date;
  message: string;
  stack?: string;
  url: string;
  method: string;
  ip: string;
  userAgent?: string;
  userId?: number;
  statusCode: number;
}

class SecureErrorHandler {
  private errorLog: ErrorLogEntry[] = [];
  private isProduction = process.env.NODE_ENV === 'production';

  /**
   * Main error handling middleware
   */
  public handleError = (err: SecurityError, req: Request, res: Response, next: NextFunction) => {
    // Generate unique error ID for tracking
    const errorId = crypto.randomUUID();
    
    // Determine status code
    const statusCode = err.status || err.statusCode || 500;
    
    // Log the error securely
    this.logError(err, req, errorId, statusCode);
    
    // Send sanitized response
    const response = this.createSanitizedResponse(err, errorId, statusCode);
    
    res.status(statusCode).json(response);
  };

  /**
   * Log error securely without exposing sensitive information
   */
  private logError(err: SecurityError, req: Request, errorId: string, statusCode: number): void {
    const logEntry: ErrorLogEntry = {
      errorId,
      timestamp: new Date(),
      message: this.sanitizeErrorMessage(err.message),
      stack: this.isProduction ? undefined : err.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: (req as any).user?.id,
      statusCode
    };

    // Add to in-memory log (in production, this should go to a proper logging service)
    this.errorLog.push(logEntry);
    
    // Keep only last 1000 errors in memory
    if (this.errorLog.length > 1000) {
      this.errorLog = this.errorLog.slice(-1000);
    }

    // Log to console with appropriate level
    if (statusCode >= 500) {
      console.error(`🚨 ERROR ${errorId}:`, {
        message: logEntry.message,
        url: logEntry.url,
        method: logEntry.method,
        userId: logEntry.userId,
        stack: this.isProduction ? '[REDACTED]' : err.stack
      });
    } else if (statusCode >= 400) {
      console.warn(`⚠️ CLIENT ERROR ${errorId}:`, {
        message: logEntry.message,
        url: logEntry.url,
        method: logEntry.method,
        userId: logEntry.userId
      });
    }

    // Check for security-related errors
    this.checkSecurityImplications(err, req, errorId);
  }

  /**
   * Create sanitized error response for client
   */
  private createSanitizedResponse(err: SecurityError, errorId: string, statusCode: number) {
    const baseResponse = {
      error: true,
      errorId,
      timestamp: new Date().toISOString()
    };

    if (this.isProduction) {
      // Production: Generic messages only
      return {
        ...baseResponse,
        message: this.getGenericErrorMessage(statusCode),
        details: statusCode < 500 ? this.sanitizeErrorMessage(err.message) : undefined
      };
    } else {
      // Development: More detailed errors
      return {
        ...baseResponse,
        message: this.sanitizeErrorMessage(err.message),
        details: err.code ? `Error code: ${err.code}` : undefined,
        stack: err.stack?.split('\n').slice(0, 10) // Limit stack trace
      };
    }
  }

  /**
   * Sanitize error messages to remove sensitive information
   */
  private sanitizeErrorMessage(message: string): string {
    if (!message) return 'An error occurred';

    // Remove sensitive patterns
    const sensitivePatterns = [
      /password[=:]\s*[^\s&]*/gi,
      /token[=:]\s*[^\s&]*/gi,
      /key[=:]\s*[^\s&]*/gi,
      /secret[=:]\s*[^\s&]*/gi,
      /postgresql:\/\/[^@]*@[^\/]*/gi,
      /mongodb:\/\/[^@]*@[^\/]*/gi,
      /mysql:\/\/[^@]*@[^\/]*/gi,
      /sk-[a-zA-Z0-9]{32,}/g, // OpenAI API keys
      /xoxb-[a-zA-Z0-9-]+/g, // Slack tokens
      /\/[a-zA-Z]:[\\\/][^\\\/\s]*/g, // Windows file paths
      /\/[^\/\s]*\/[^\/\s]*\/[^\/\s]*/g // Unix file paths (partial)
    ];

    let sanitized = message;
    sensitivePatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });

    // Limit message length
    if (sanitized.length > 200) {
      sanitized = sanitized.substring(0, 200) + '...';
    }

    return sanitized;
  }

  /**
   * Get generic error message based on status code
   */
  private getGenericErrorMessage(statusCode: number): string {
    const messages: { [key: number]: string } = {
      400: 'Bad Request',
      401: 'Authentication Required',
      403: 'Access Forbidden',
      404: 'Resource Not Found',
      405: 'Method Not Allowed',
      409: 'Conflict',
      422: 'Validation Failed',
      429: 'Too Many Requests',
      500: 'Internal Server Error',
      502: 'Bad Gateway',
      503: 'Service Unavailable',
      504: 'Gateway Timeout'
    };

    return messages[statusCode] || 'An error occurred';
  }

  /**
   * Check for security implications in errors
   */
  private checkSecurityImplications(err: SecurityError, req: Request, errorId: string): void {
    const message = err.message.toLowerCase();
    const securityKeywords = [
      'permission', 'access', 'authentication', 'authorization',
      'token', 'session', 'csrf', 'xss', 'injection',
      'unauthorized', 'forbidden', 'invalid signature'
    ];

    const hasSecurityImplication = securityKeywords.some(keyword => 
      message.includes(keyword)
    );

    if (hasSecurityImplication) {
      console.error(`🔒 SECURITY ALERT ${errorId}:`, {
        type: 'SECURITY_ERROR',
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: (req as any).user?.id,
        errorType: err.constructor.name
      });
    }

    // Check for potential attack patterns
    const suspiciousPatterns = [
      /union\s+select/gi,
      /<script/gi,
      /javascript:/gi,
      /\.\.\/\.\.\//gi,
      /etc\/passwd/gi,
      /cmd\.exe/gi
    ];

    const requestData = JSON.stringify({
      url: req.url,
      query: req.query,
      body: req.body
    });

    const hasSuspiciousPattern = suspiciousPatterns.some(pattern => 
      pattern.test(requestData)
    );

    if (hasSuspiciousPattern) {
      console.error(`🚨 ATTACK ATTEMPT ${errorId}:`, {
        type: 'SUSPICIOUS_REQUEST',
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
    }
  }

  /**
   * Get error statistics for monitoring
   */
  public getErrorStatistics() {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const recentErrors = this.errorLog.filter(
      entry => now - entry.timestamp.getTime() < oneHour
    );

    const errorsByStatus = recentErrors.reduce((acc, entry) => {
      acc[entry.statusCode] = (acc[entry.statusCode] || 0) + 1;
      return acc;
    }, {} as { [key: number]: number });

    return {
      totalErrors: this.errorLog.length,
      recentErrors: recentErrors.length,
      errorsByStatus,
      lastError: this.errorLog[this.errorLog.length - 1]?.timestamp
    };
  }

  /**
   * Clear error log (for testing or maintenance)
   */
  public clearErrorLog(): void {
    this.errorLog = [];
  }
}

// Create singleton instance
const secureErrorHandler = new SecureErrorHandler();

// Export middleware function
export const handleSecureError = secureErrorHandler.handleError;
export const getErrorStatistics = secureErrorHandler.getErrorStatistics.bind(secureErrorHandler);
export const clearErrorLog = secureErrorHandler.clearErrorLog.bind(secureErrorHandler);

// Export validation error handler for API endpoints
export const handleValidationError = (req: Request, res: Response, next: NextFunction) => {
  // This will be called for validation errors specifically
  const error = new Error('Validation failed') as SecurityError;
  error.status = 400;
  secureErrorHandler.handleError(error, req, res, next);
};

export default secureErrorHandler;
