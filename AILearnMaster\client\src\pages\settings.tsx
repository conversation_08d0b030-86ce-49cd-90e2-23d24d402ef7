import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { UserRound, CreditCard, Sliders, Bot, Store, LayoutGrid } from "lucide-react";
import { apiRequest } from '@/lib/queryClient';
import UserAccountSettings from '@/components/settings/UserAccountSettings';
import SubscriptionSettings from '@/components/settings/SubscriptionSettings';
import CourseManagementSettings from '@/components/settings/CourseManagementSettings';
import AIIntegrationSettings from '@/components/settings/AIIntegrationSettings';
import { useAuth } from '@/hooks/use-auth';

const TABS = [
  {
    id: "account",
    label: "Account",
    icon: <UserRound className="h-4 w-4 mr-2" />,
  },
  {
    id: "subscription",
    label: "Subscription",
    icon: <CreditCard className="h-4 w-4 mr-2" />,
  },
  {
    id: "course",
    label: "Course Management",
    icon: <LayoutGrid className="h-4 w-4 mr-2" />,
  },
  {
    id: "ai",
    label: "AI & Integrations",
    icon: <Bot className="h-4 w-4 mr-2" />,
  },
  {
    id: "marketplace",
    label: "Marketplace",
    icon: <Store className="h-4 w-4 mr-2" />,
  },
];

export default function SettingsPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("account");

  // For mobile navigation
  const [mobileNav, setMobileNav] = useState(false);

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-6 pb-16">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground mt-1">
            Manage your account settings and preferences.
          </p>
        </div>

        <Separator />

        {/* Mobile tabs */}
        <div className="md:hidden">
          <Button 
            variant="outline"
            className="w-full flex items-center justify-between"
            onClick={() => setMobileNav(!mobileNav)}
          >
            <span className="flex items-center">
              {TABS.find(tab => tab.id === activeTab)?.icon}
              {TABS.find(tab => tab.id === activeTab)?.label}
            </span>
            <Sliders className="h-4 w-4" />
          </Button>
          {mobileNav && (
            <Card className="mt-2">
              <CardContent className="pt-4">
                <div className="space-y-1">
                  {TABS.map((tab) => (
                    <Button 
                      key={tab.id}
                      variant={activeTab === tab.id ? "secondary" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => {
                        setActiveTab(tab.id);
                        setMobileNav(false);
                      }}
                    >
                      {tab.icon}
                      {tab.label}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Desktop layout */}
        <div className="flex flex-col md:flex-row gap-8">
          {/* Sidebar navigation for desktop */}
          <div className="hidden md:flex md:w-64 md:flex-col md:shrink-0">
            <Card className="sticky top-8">
              <CardContent className="p-4">
                <div className="space-y-1">
                  {TABS.map((tab) => (
                    <Button 
                      key={tab.id}
                      variant={activeTab === tab.id ? "secondary" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => setActiveTab(tab.id)}
                    >
                      {tab.icon}
                      {tab.label}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main content area */}
          <div className="flex-1">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsContent value="account">
                <UserAccountSettings user={user} />
              </TabsContent>
              
              <TabsContent value="subscription">
                <SubscriptionSettings user={user} />
              </TabsContent>
              
              <TabsContent value="course">
                <CourseManagementSettings user={user} />
              </TabsContent>
              
              <TabsContent value="ai">
                <AIIntegrationSettings user={user} />
              </TabsContent>
              
              <TabsContent value="marketplace">
                <MarketplaceSettings />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}

// Placeholder for Marketplace settings that can be expanded later
function MarketplaceSettings() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Marketplace Settings</CardTitle>
        <CardDescription>
          Configure your marketplace and store preferences
        </CardDescription>
      </CardHeader>
      <CardContent className="text-center py-12">
        <Store className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-xl font-medium mb-2">Marketplace Coming Soon</h3>
        <p className="text-muted-foreground max-w-md mx-auto mb-6">
          Our marketplace feature is currently in development. You'll soon be able to sell your courses and purchase content from other creators.
        </p>
        <Button variant="outline" disabled>
          Join Waitlist
        </Button>
      </CardContent>
    </Card>
  );
}