import subprocess
import sys
import os

# Install modal using pip
try:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "modal"])
    print("Modal installed successfully!")
except Exception as e:
    print(f"Installation failed: {e}")
    
# Test the import
try:
    import modal
    print("Modal import successful!")
except ImportError as e:
    print(f"Modal import still failing: {e}")