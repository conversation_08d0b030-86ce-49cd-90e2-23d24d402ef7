import { Route, Switch, useLocation } from "wouter";
import { MainLayout } from "./layouts/MainLayout";
import { PublicLayout } from "./layouts/PublicLayout";
import { useUser } from "@/hooks/use-user";
import { AuthenticatedRoute } from "./authenticated-route";
import { CourseCreator } from "./course-creator/CourseCreator";
import { CourseCreationFlow } from "./course-creator/CourseCreationFlow";
// We'll use the direct imports below
import React from "react";

// Import your page components
import LandingPage from "@/pages/landing-page";
import Dashboard from "@/pages/dashboard";
import AuthPage from "@/pages/auth-page";
import NotFound from "@/pages/not-found";
import SettingsPage from "@/pages/settings";
import { ProtectedRoute } from "./protected-route";
import PricingPage from "@/pages/pricing";
import EnhancedCourseCreatorPage from "@/pages/enhanced-course-creator";
import ModernCourseCreatorPage from "@/pages/modern-course-creator";
import ProfilePage from "@/pages/profile";
import VideoGeneratorPage from "@/pages/video-generator";
import AIVoiceGenerator from "@/pages/ai-voice-generator";
import PlatformIntegrationsPage from "@/pages/platform-integrations/index";
import PlatformMarketplacesPage from "@/pages/platform-integrations/marketplaces";
import VideoPlatformsPage from "@/pages/platform-integrations/video";
import PlatformIntegrationsSettings from "@/pages/platform-integrations-settings";
// Removed LMS Platforms - functionality consolidated into other distribution channels
import AICreditsPage from "@/pages/ai-credits";
// Core pages
import MediaLibraryPage from "@/pages/media-library";
import AITemplatesPage from "@/pages/ai-templates";
import MyCoursesPage from "@/pages/my-courses";
import CourseView from "@/pages/course-view";
import MarketplacePage from "@/pages/marketplace-page";

// Product pages
import FeaturesPage from "@/pages/product/features";
import HowItWorksPage from "@/pages/product/how-it-works";
import TestimonialsPage from "@/pages/product/testimonials";

// Support pages
import ContactUsPage from "@/pages/support/contact-us";
import AboutUsPage from "@/pages/company/about-us";
import HelpPage from "@/pages/help-page";

// Admin and Teams
import AdminDashboard from "@/pages/admin-dashboard";
import TeamsPage from "@/pages/teams/teams-page";
import TeamDetailsPage from "@/pages/teams/team-details-page";
import CollaborationsPage from "@/pages/teams/collaborations-page";
import SharedCoursesPage from "@/pages/collaborate/shared-courses-page";

// Tools and utilities
import AITools from "@/pages/ai-tools";

import MeetingsPage from "@/pages/meetings";
import CollaborationPage from "@/pages/collaborations";
import NotificationsPage from "@/pages/notifications-page";
import NotificationSettingsPage from "@/pages/notification-settings";
import EmailSettings from "@/pages/settings/email-settings";

// Marketing
import MarketingLandingPage from "@/pages/marketing/landing-page";
import MarketingPage from "@/pages/marketing/index";
import CreatePage from "@/pages/create";
import DistributePage from "@/pages/distribute";
import Gamification from "@/pages/Gamification";
import AIToolsSettings from "@/pages/ai-tools-settings";
import CollaborationHub from "@/pages/collaboration-hub";

// Use lazy loading for email marketing pages
const lazy = {
  EmailMarketing: React.lazy(() => import('@/pages/marketing/email')),
  EmailTemplates: React.lazy(() => import('@/pages/marketing/email/templates')),
  EmailCampaigns: React.lazy(() => import('@/pages/marketing/email/campaigns')),
  EmailReports: React.lazy(() => import('@/pages/marketing/email/reports')),
  EmailSettings: React.lazy(() => import('@/pages/marketing/email/settings')),
  MarketingAnalytics: React.lazy(() => import('@/pages/marketing/analytics')),
};


export function AppContent() {
  const { user, isLoading } = useUser();
  const [_, navigate] = useLocation();

  return (
    <Switch>
      {/* Public routes with PublicLayout */}
      <Route path="/">
        <PublicLayout hideHeader hideFooter>
          <AuthenticatedRoute>
            <LandingPage />
          </AuthenticatedRoute>
        </PublicLayout>
      </Route>
      <Route path="/auth">
        <PublicLayout>
          <AuthPage />
        </PublicLayout>
      </Route>
      <Route path="/product/features">
        <PublicLayout>
          <FeaturesPage />
        </PublicLayout>
      </Route>
      <Route path="/product/how-it-works">
        <PublicLayout>
          <HowItWorksPage />
        </PublicLayout>
      </Route>
      <Route path="/product/testimonials">
        <PublicLayout>
          <TestimonialsPage />
        </PublicLayout>
      </Route>
      {/* Both routes for pricing to ensure compatibility */}
      <Route path="/product/pricing">
        <PublicLayout>
          <PricingPage />
        </PublicLayout>
      </Route>
      <Route path="/pricing">
        <PublicLayout>
          <PricingPage />
        </PublicLayout>
      </Route>
      <Route path="/company/about-us">
        <PublicLayout>
          <AboutUsPage />
        </PublicLayout>
      </Route>
      <Route path="/support/contact-us">
        <PublicLayout>
          <ContactUsPage />
        </PublicLayout>
      </Route>


      {/* Protected routes with MainLayout */}
      <Route path="/dashboard">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <Dashboard />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/create">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <CreatePage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/my-courses">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <MyCoursesPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/gamification">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <Gamification />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/course-creation/traditional-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div>Loading...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/course-creation/traditional-course')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/course-creation/avatar-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div>Loading...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/course-creation/avatar-course')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/course-creation/smart-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div>Loading...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/course-creation/smart-course')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/settings">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <SettingsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/profile">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <ProfilePage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/video-generator">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <VideoGeneratorPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/ai-voice-generator">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <AIVoiceGenerator />
          </MainLayout>
        </ProtectedRoute>
      </Route>

      <Route path="/media-library">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <MediaLibraryPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/marketplace">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <MarketplacePage />
          </MainLayout>
        </ProtectedRoute>
      </Route>

      <Route path="/ai-templates">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <AITemplatesPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/ai-credits">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <AICreditsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/my-courses">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <MyCoursesPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/teams">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <TeamsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/teams/:id">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <TeamDetailsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/collaborations">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <CollaborationsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/collaborate/shared-courses">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <SharedCoursesPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      {/* Course creation routes */}
      <Route path="/course-creation/traditional-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="p-8 flex justify-center">Loading traditional course creator...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/course-creation/traditional-course')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/course-progress">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="p-8 flex justify-center">Loading progress tracker...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/course-progress')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/course-creation/avatar-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="p-8 flex justify-center">Loading avatar course creator...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/course-creation/avatar-course')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/avatar-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="p-8 flex justify-center">Loading avatar course creator...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/course-creation/avatar-course')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/course-creation/enhanced-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="p-8 flex justify-center">Loading enhanced course creator...</div>}>
              <EnhancedCourseCreatorPage />
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/course-creation/modern-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <ModernCourseCreatorPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      {/* Edit routes */}
      <Route path="/courses/:id/edit">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <CourseCreator mode="edit" onClose={() => navigate('/my-courses')} />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      {/* Format-specific edit routes - using dynamic imports */}
      <Route path="/courses/:id/edit/traditional-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="p-8 flex justify-center">Loading editor...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/course-creation/traditional-course')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/courses/:id/edit/avatar-course">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="p-8 flex justify-center">Loading editor...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/course-creation/avatar-course')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/courses/:id/preview">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <CourseView preview={true} />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/courses/:id">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <CourseView />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/admin-dashboard">
        <ProtectedRoute adminOnly>
          <MainLayout user={user} isLoading={isLoading}>
            <AdminDashboard />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/help">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <HelpPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      <Route path="/platform-integrations">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <PlatformIntegrationsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/platform-integrations/marketplaces">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <PlatformMarketplacesPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/platform-integrations/video">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <VideoPlatformsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>

      {/* LMS Platforms removed - functionality consolidated into other distribution channels */}
      
      <Route path="/ai-credits">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <AICreditsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/ai-tools">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <AIToolsSettings />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/collaboration">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <CollaborationHub />
          </MainLayout>
        </ProtectedRoute>
      </Route>

      
      <Route path="/ai-image-generator">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="p-8 flex justify-center">Loading AI Image Generator...</div>}>
              {React.createElement(React.lazy(() => import('@/pages/ai-image-generator')))}
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      
      <Route path="/meetings">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <MeetingsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/collaboration">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <CollaborationPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/notifications">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <NotificationsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/notification-settings">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <NotificationSettingsPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/settings/email">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <EmailSettings />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/marketing">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <MarketingPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/distribute">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <DistributePage />
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      <Route path="/marketing/landing-page">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <MarketingLandingPage />
          </MainLayout>
        </ProtectedRoute>
      </Route>

      {/* Marketing Analytics */}
      <Route path="/marketing/analytics">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="flex items-center justify-center h-[calc(100vh-120px)]">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" /> Loading...
            </div>}>
              <lazy.MarketingAnalytics />
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      {/* Email marketing main route - displays subscribers page */}
      <Route path="/marketing/email">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="flex items-center justify-center h-[calc(100vh-120px)]">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" /> Loading...
            </div>}>
              <lazy.EmailMarketing />
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      {/* Email templates */}
      <Route path="/marketing/email/templates">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="flex items-center justify-center h-[calc(100vh-120px)]">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" /> Loading...
            </div>}>
              <lazy.EmailTemplates />
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      {/* Email campaigns */}
      <Route path="/marketing/email/campaigns">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="flex items-center justify-center h-[calc(100vh-120px)]">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" /> Loading...
            </div>}>
              <lazy.EmailCampaigns />
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      {/* Email reports */}
      <Route path="/marketing/email/reports">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="flex items-center justify-center h-[calc(100vh-120px)]">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" /> Loading...
            </div>}>
              <lazy.EmailReports />
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>
      
      {/* Email settings */}
      <Route path="/marketing/email/settings">
        <ProtectedRoute>
          <MainLayout user={user} isLoading={isLoading}>
            <React.Suspense fallback={<div className="flex items-center justify-center h-[calc(100vh-120px)]">
              <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" /> Loading...
            </div>}>
              <lazy.EmailSettings />
            </React.Suspense>
          </MainLayout>
        </ProtectedRoute>
      </Route>

      {/* 404 page */}
      <Route>
        <PublicLayout>
          <NotFound />
        </PublicLayout>
      </Route>
    </Switch>
  );
}