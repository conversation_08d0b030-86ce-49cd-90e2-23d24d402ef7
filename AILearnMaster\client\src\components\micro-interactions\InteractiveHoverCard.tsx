import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BookOpen, 
  Clock, 
  Star, 
  Users, 
  TrendingUp, 
  PlayCircle,
  CheckCircle2,
  Lock
} from 'lucide-react';

interface InteractiveHoverCardProps {
  title: string;
  description: string;
  progress?: number;
  duration?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  rating?: number;
  enrolled?: number;
  status?: 'available' | 'locked' | 'completed' | 'in_progress';
  thumbnail?: string;
  tags?: string[];
  onClick?: () => void;
  className?: string;
}

const difficultyColors = {
  beginner: 'bg-green-100 text-green-700 border-green-200',
  intermediate: 'bg-yellow-100 text-yellow-700 border-yellow-200',
  advanced: 'bg-red-100 text-red-700 border-red-200',
};

const statusIcons = {
  available: PlayCircle,
  locked: Lock,
  completed: CheckCircle2,
  in_progress: BookOpen,
};

const statusColors = {
  available: 'text-blue-500',
  locked: 'text-gray-400',
  completed: 'text-green-500',
  in_progress: 'text-orange-500',
};

export default function InteractiveHoverCard({
  title,
  description,
  progress,
  duration,
  difficulty = 'beginner',
  rating,
  enrolled,
  status = 'available',
  thumbnail,
  tags = [],
  onClick,
  className = ''
}: InteractiveHoverCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const StatusIcon = statusIcons[status];

  return (
    <motion.div
      className={`cursor-pointer ${className}`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ y: -4 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
    >
      <Card className="relative overflow-hidden transition-all duration-300 hover:shadow-xl group">
        {/* Hover Glow Effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={false}
          animate={isHovered ? { opacity: 1 } : { opacity: 0 }}
        />

        {/* Thumbnail Section */}
        <div className="relative h-40 overflow-hidden">
          {thumbnail ? (
            <motion.img
              src={thumbnail}
              alt={title}
              className="w-full h-full object-cover"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            />
          ) : (
            <motion.div
              className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <BookOpen className="w-12 h-12 text-white/80" />
            </motion.div>
          )}

          {/* Status Overlay */}
          <div className="absolute top-3 left-3">
            <motion.div
              className={`p-2 rounded-full bg-white/90 backdrop-blur-sm ${statusColors[status]}`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <StatusIcon className="w-4 h-4" />
            </motion.div>
          </div>

          {/* Progress Overlay for in-progress items */}
          {status === 'in_progress' && progress !== undefined && (
            <div className="absolute bottom-0 left-0 right-0 p-3">
              <div className="bg-white/90 backdrop-blur-sm rounded-lg p-2">
                <div className="flex items-center justify-between text-xs mb-1">
                  <span className="text-gray-600">Progress</span>
                  <span className="font-medium text-gray-800">{progress}%</span>
                </div>
                <Progress value={progress} className="h-1" />
              </div>
            </div>
          )}
        </div>

        {/* Content Section */}
        <div className="p-4 space-y-3">
          {/* Title and Rating */}
          <div className="space-y-2">
            <motion.h3
              className="font-semibold text-gray-900 line-clamp-2 leading-tight"
              animate={isHovered ? { x: 2 } : { x: 0 }}
              transition={{ duration: 0.2 }}
            >
              {title}
            </motion.h3>
            
            {rating && (
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0.3 }}
                      animate={{ 
                        opacity: i < Math.floor(rating) ? 1 : 0.3,
                        scale: isHovered && i < Math.floor(rating) ? 1.1 : 1
                      }}
                      transition={{ delay: i * 0.1 }}
                    >
                      <Star className={`w-4 h-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} />
                    </motion.div>
                  ))}
                </div>
                <span className="text-sm text-gray-600">({rating})</span>
              </div>
            )}
          </div>

          {/* Description */}
          <motion.p
            className="text-sm text-gray-600 line-clamp-2"
            animate={isHovered ? { opacity: 1 } : { opacity: 0.8 }}
            transition={{ duration: 0.2 }}
          >
            {description}
          </motion.p>

          {/* Tags */}
          <AnimatePresence>
            {tags.length > 0 && (
              <motion.div
                className="flex flex-wrap gap-1"
                initial={{ opacity: 0, height: 0 }}
                animate={isHovered ? { opacity: 1, height: 'auto' } : { opacity: 0, height: 0 }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                {tags.slice(0, 3).map((tag, index) => (
                  <motion.div
                    key={tag}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Badge variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  </motion.div>
                ))}
                {tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{tags.length - 3}
                  </Badge>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Footer Metadata */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
            <div className="flex items-center gap-4 text-xs text-gray-500">
              {duration && (
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span>{duration}</span>
                </div>
              )}
              
              {enrolled && (
                <div className="flex items-center gap-1">
                  <Users className="w-3 h-3" />
                  <span>{enrolled.toLocaleString()}</span>
                </div>
              )}
            </div>

            {difficulty && (
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Badge 
                  variant="outline" 
                  className={`text-xs ${difficultyColors[difficulty]}`}
                >
                  {difficulty}
                </Badge>
              </motion.div>
            )}
          </div>
        </div>

        {/* Shimmer Effect on Hover */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              initial={{ x: '-100%' }}
              animate={{ x: '100%' }}
              exit={{ x: '100%' }}
              transition={{ duration: 0.6, ease: "easeInOut" }}
              style={{ 
                background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                transform: 'skewX(-25deg)'
              }}
            />
          )}
        </AnimatePresence>

        {/* Interactive Border Glow */}
        <motion.div
          className="absolute inset-0 rounded-lg border-2 border-transparent"
          animate={isHovered ? {
            borderColor: ['rgba(59, 130, 246, 0)', 'rgba(59, 130, 246, 0.5)', 'rgba(59, 130, 246, 0)']
          } : {}}
          transition={{ duration: 1.5, repeat: isHovered ? Infinity : 0 }}
        />
      </Card>
    </motion.div>
  );
}