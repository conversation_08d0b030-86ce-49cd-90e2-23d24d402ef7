import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  Volume2, 
  Download, 
  Play, 
  Pause, 
  RotateCcw, 
  Sparkles, 
  Mic, 
  Clock,
  CheckCircle2,
  AlertCircle
} from 'lucide-react';

interface Voice {
  id: string;
  name: string;
  gender: 'male' | 'female';
  description: string;
  quality: 'standard' | 'hd';
  preview_url?: string;
}

interface OpenAITTSPanelProps {
  script: string;
  moduleId: string;
  lessonId: string;
  moduleTitle: string;
  lessonTitle: string;
  onVoiceGenerated: (audioUrl: string, audioData: any) => void;
  className?: string;
}

const OPENAI_VOICES: Voice[] = [
  { id: 'alloy', name: 'Alloy', gender: 'female', description: 'Balanced, warm', quality: 'standard' },
  { id: 'echo', name: 'Echo', gender: 'male', description: 'Clear, professional', quality: 'standard' },
  { id: 'fable', name: 'Fable', gender: 'male', description: 'Storytelling, engaging', quality: 'standard' },
  { id: 'onyx', name: 'Onyx', gender: 'male', description: 'Deep, authoritative', quality: 'standard' },
  { id: 'nova', name: 'Nova', gender: 'female', description: 'Friendly, conversational', quality: 'standard' },
  { id: 'shimmer', name: 'Shimmer', gender: 'female', description: 'Energetic, bright', quality: 'standard' }
];

export function OpenAITTSPanel({
  script,
  moduleId,
  lessonId,
  moduleTitle,
  lessonTitle,
  onVoiceGenerated,
  className = ''
}: OpenAITTSPanelProps) {
  const { toast } = useToast();
  const [selectedVoice, setSelectedVoice] = useState<string>('nova');
  const [selectedModel, setSelectedModel] = useState<'tts-1' | 'tts-1-hd'>('tts-1');
  const [speed, setSpeed] = useState<number[]>([1.0]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [generationProgress, setGenerationProgress] = useState(0);

  // Voice preview mutation
  const previewMutation = useMutation({
    mutationFn: async (voiceId: string) => {
      const previewText = script.substring(0, 200) + (script.length > 200 ? '...' : '');
      
      const response = await fetch('/api/ai/text-to-speech', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: previewText,
          voice: voiceId,
          model: 'tts-1',
          speed: speed[0]
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate voice preview');
      }

      const audioBlob = await response.blob();
      return URL.createObjectURL(audioBlob);
    },
    onSuccess: (audioUrl) => {
      const audio = new Audio(audioUrl);
      audio.play();
      setCurrentAudio(audio);
      setIsPlaying(true);
      
      audio.onended = () => {
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);
      };
    },
    onError: () => {
      toast({
        title: 'Preview Failed',
        description: 'Could not generate voice preview. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Main voice generation mutation
  const generateMutation = useMutation({
    mutationFn: async () => {
      setGenerationProgress(0);
      
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => Math.min(prev + 15, 85));
      }, 1000);

      try {
        const response = await fetch('/api/ai/text-to-speech', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: script,
            voice: selectedVoice,
            model: selectedModel,
            speed: speed[0],
            moduleId,
            lessonId,
            moduleTitle,
            lessonTitle
          })
        });

        clearInterval(progressInterval);
        setGenerationProgress(100);

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Failed to generate voice');
        }

        const result = await response.json();
        return result;
      } catch (error) {
        clearInterval(progressInterval);
        throw error;
      }
    },
    onSuccess: (result) => {
      toast({
        title: 'Voice Generated Successfully!',
        description: `${lessonTitle} narration is ready for download and playback.`
      });
      
      onVoiceGenerated(result.audioUrl, {
        voiceId: selectedVoice,
        model: selectedModel,
        speed: speed[0],
        duration: result.duration,
        moduleId,
        lessonId,
        moduleTitle,
        lessonTitle
      });
      
      setGenerationProgress(0);
    },
    onError: (error: any) => {
      toast({
        title: 'Generation Failed',
        description: error.message || 'Could not generate voice. Please try again.',
        variant: 'destructive'
      });
      setGenerationProgress(0);
    }
  });

  // Stop current audio playback
  const stopPlayback = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setIsPlaying(false);
    }
  };

  // Calculate estimated duration and cost
  const estimatedDuration = Math.ceil(script.length / 150); // ~150 chars per minute for speech
  const estimatedCost = selectedModel === 'tts-1-hd' 
    ? (script.length / 1000 * 0.030).toFixed(3)
    : (script.length / 1000 * 0.015).toFixed(3);

  const selectedVoiceData = OPENAI_VOICES.find(v => v.id === selectedVoice);

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Mic className="h-5 w-5 text-blue-500" />
              AI Voice Generation
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Convert "{lessonTitle}" to professional narration using OpenAI's TTS
            </p>
          </div>
          <Badge variant="secondary" className="flex items-center gap-1">
            <CheckCircle2 className="h-3 w-3" />
            OpenAI TTS
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Model Selection */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Quality Model</label>
          <Tabs value={selectedModel} onValueChange={(value) => setSelectedModel(value as 'tts-1' | 'tts-1-hd')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="tts-1" className="flex flex-col gap-1 h-16">
                <span className="font-medium">Standard</span>
                <span className="text-xs text-muted-foreground">Fast & Efficient</span>
              </TabsTrigger>
              <TabsTrigger value="tts-1-hd" className="flex flex-col gap-1 h-16">
                <span className="font-medium">HD Quality</span>
                <span className="text-xs text-muted-foreground">Premium Audio</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Voice Selection */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Voice Character</label>
          <Select value={selectedVoice} onValueChange={setSelectedVoice}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {OPENAI_VOICES.map((voice) => (
                <SelectItem key={voice.id} value={voice.id}>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex flex-col items-start">
                      <span className="font-medium">{voice.name}</span>
                      <span className="text-xs text-muted-foreground">{voice.description}</span>
                    </div>
                    <Badge variant="outline" className="ml-2 text-xs">
                      {voice.gender}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Voice Preview */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => previewMutation.mutate(selectedVoice)}
            disabled={previewMutation.isPending || !script.trim()}
            className="w-full"
          >
            {previewMutation.isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-gray-900 mr-2" />
                Generating Preview...
              </>
            ) : isPlaying ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Playing Preview
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                Preview {selectedVoiceData?.name} Voice
              </>
            )}
          </Button>
        </div>

        {/* Speech Settings */}
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Speech Speed</label>
            <div className="px-3">
              <Slider
                value={speed}
                onValueChange={setSpeed}
                max={2.0}
                min={0.5}
                step={0.1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>0.5x</span>
                <span className="font-medium">{speed[0]}x</span>
                <span>2.0x</span>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Script Info & Estimation */}
        <div className="space-y-3">
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Script Length</span>
              <span className="font-medium">{script.length.toLocaleString()} characters</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground flex items-center gap-1">
                <Clock className="h-3 w-3" />
                Estimated Duration
              </span>
              <span className="font-medium">~{estimatedDuration} minutes</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Estimated Cost</span>
              <span className="font-medium">${estimatedCost}</span>
            </div>
          </div>

          {/* Script Preview */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Script Preview</label>
            <div className="bg-muted p-3 rounded-md max-h-24 overflow-y-auto">
              <p className="text-sm text-muted-foreground">
                {script.substring(0, 200)}
                {script.length > 200 && "..."}
              </p>
            </div>
          </div>
        </div>

        {/* Generation Progress */}
        {generateMutation.isPending && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Generating voice narration...</span>
              <span className="font-medium">{generationProgress}%</span>
            </div>
            <Progress value={generationProgress} className="h-2" />
          </div>
        )}

        {/* Generate Button */}
        <Button
          onClick={() => generateMutation.mutate()}
          disabled={!script.trim() || generateMutation.isPending}
          className="w-full h-12"
          size="lg"
        >
          {generateMutation.isPending ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2" />
              Generating {selectedModel === 'tts-1-hd' ? 'HD' : 'Standard'} Voice...
            </>
          ) : (
            <>
              <Sparkles className="h-5 w-5 mr-2" />
              Generate {selectedModel === 'tts-1-hd' ? 'HD' : 'Standard'} Voice Narration
            </>
          )}
        </Button>

        {/* Warning for empty script */}
        {!script.trim() && (
          <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <AlertCircle className="h-4 w-4 text-yellow-600" />
            <span className="text-sm text-yellow-800">
              Please generate or enter a script first before creating voice narration.
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}