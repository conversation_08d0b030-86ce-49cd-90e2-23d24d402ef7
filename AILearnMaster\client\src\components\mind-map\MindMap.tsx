import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { 
  Plus, 
  Wand2, 
  Download, 
  Upload, 
  Share2, 
  Save,
  Trash2,
  Edit,
  HelpCircle,
  Info,
  Zap,
  Minus,
  Loader2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useHints } from '@/components/hints/HintProvider';
import { Module, Lesson } from '@/components/course-creator/ContentStructure';
import { apiRequest } from '@/lib/queryClient';

// Node types for the mind map
type NodeType = 'central' | 'main' | 'subtopic' | 'detail';

// Node styles for different types
const nodeStyles: Record<NodeType, string> = {
  central: 'bg-primary text-primary-foreground border-2 border-primary',
  main: 'bg-blue-100 border-blue-500 border-2 text-blue-900',
  subtopic: 'bg-green-100 border-green-500 border text-green-900',
  detail: 'bg-amber-50 border-amber-300 border text-amber-900'
};

// Mind map node interface
interface MindMapNode {
  id: string;
  type: NodeType;
  text: string;
  description?: string;
  x: number;
  y: number;
  parentId?: string;
  childIds: string[];
}

// Connection interface
interface Connection {
  sourceId: string;
  targetId: string;
  color: string;
}

// Mind map data structure
interface MindMapData {
  nodes: Record<string, MindMapNode>;
  connections: Connection[];
}

// Props for the MindMap component
interface MindMapProps {
  courseTitle: string;
  courseDescription: string;
  initialNodes?: MindMapData;
  onSave?: (data: MindMapData) => void;
  onConvertToCourseStructure?: (modules: Module[]) => void;
}

export function MindMap({ 
  courseTitle, 
  courseDescription, 
  initialNodes,
  onSave,
  onConvertToCourseStructure
}: MindMapProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [viewBox, setViewBox] = useState({ x: 0, y: 0, width: 1000, height: 600 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [connectionStart, setConnectionStart] = useState<string | null>(null);
  const [nodeText, setNodeText] = useState('');
  const [nodeDescription, setNodeDescription] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [zoom, setZoom] = useState(1);
  const { toast } = useToast();
  const { showHint } = useHints();

  // Initialize mind map with default central node or load from initialNodes
  const [mindMap, setMindMap] = useState<MindMapData>(() => {
    if (initialNodes) return initialNodes;
    
    // Default central node based on course title
    const centralId = 'node-1';
    return {
      nodes: {
        [centralId]: {
          id: centralId,
          type: 'central',
          text: courseTitle || 'Main Topic',
          description: courseDescription || 'Central concept',
          x: 500,
          y: 300,
          childIds: []
        }
      },
      connections: []
    };
  });

  // Update viewBox dimensions when container size changes
  useEffect(() => {
    const updateViewBox = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setViewBox(prev => ({ ...prev, width, height }));
      }
    };

    const observer = new ResizeObserver(updateViewBox);
    if (containerRef.current) {
      observer.observe(containerRef.current);
    }
    
    updateViewBox();
    return () => observer.disconnect();
  }, []);

  // Generate UUID for new nodes
  const generateId = () => `node-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  // Add a new node connected to the selected node
  const addNode = (type: NodeType = 'main') => {
    if (!selectedNodeId) {
      toast({
        title: "No node selected",
        description: "Please select a node first to add a connected node.",
        variant: "destructive"
      });
      return;
    }

    const parentNode = mindMap.nodes[selectedNodeId];
    if (!parentNode) return;

    const offset = 150;
    let angleOffset = 0;

    // Position based on number of existing children
    const childCount = parentNode.childIds.length;
    if (childCount > 0) {
      angleOffset = (Math.PI * 2) / (childCount + 1);
    }

    // Calculate position
    const angle = (childCount * angleOffset) + Math.PI/4;
    const newX = parentNode.x + Math.cos(angle) * offset;
    const newY = parentNode.y + Math.sin(angle) * offset;

    const newNodeId = generateId();
    const defaultText = type === 'main' ? 'Main Topic' : 
                        type === 'subtopic' ? 'Subtopic' : 'Detail';

    // Create new node
    const newNode: MindMapNode = {
      id: newNodeId,
      type,
      text: nodeText || defaultText,
      description: nodeDescription || '',
      x: newX,
      y: newY,
      parentId: selectedNodeId,
      childIds: []
    };

    // Add connection
    const newConnection: Connection = {
      sourceId: selectedNodeId,
      targetId: newNodeId,
      color: type === 'main' ? '#3b82f6' : 
              type === 'subtopic' ? '#10b981' : 
              '#f59e0b'
    };

    // Update the parent node to reference this child
    const updatedParentNode = {
      ...parentNode,
      childIds: [...parentNode.childIds, newNodeId]
    };

    // Update mind map
    setMindMap(prev => ({
      nodes: {
        ...prev.nodes,
        [selectedNodeId]: updatedParentNode,
        [newNodeId]: newNode
      },
      connections: [...prev.connections, newConnection]
    }));

    // Clear input fields
    setNodeText('');
    setNodeDescription('');

    // Select the new node
    setSelectedNodeId(newNodeId);

    toast({
      title: "Node added",
      description: `Added new ${type} node.`
    });
  };

  // Update an existing node
  const updateNode = (nodeId: string, updates: Partial<MindMapNode>) => {
    if (!mindMap.nodes[nodeId]) return;

    setMindMap(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [nodeId]: {
          ...prev.nodes[nodeId],
          ...updates
        }
      }
    }));
  };

  // Delete a node and its connections
  const deleteNode = (nodeId: string) => {
    if (!mindMap.nodes[nodeId] || nodeId === 'node-1') return; // Prevent deleting the central node

    const nodeToDelete = mindMap.nodes[nodeId];
    const updatedNodes = { ...mindMap.nodes };
    const updatedConnections = mindMap.connections.filter(
      conn => conn.sourceId !== nodeId && conn.targetId !== nodeId
    );

    // Update parent node to remove reference to this child
    if (nodeToDelete.parentId && updatedNodes[nodeToDelete.parentId]) {
      updatedNodes[nodeToDelete.parentId] = {
        ...updatedNodes[nodeToDelete.parentId],
        childIds: updatedNodes[nodeToDelete.parentId].childIds.filter(id => id !== nodeId)
      };
    }

    // Recursively delete all children
    const deleteChildren = (parentId: string) => {
      const node = updatedNodes[parentId];
      if (!node) return;
      
      [...node.childIds].forEach(childId => {
        deleteChildren(childId);
        delete updatedNodes[childId];
      });
    };

    deleteChildren(nodeId);
    delete updatedNodes[nodeId];

    setMindMap({
      nodes: updatedNodes,
      connections: updatedConnections
    });

    setSelectedNodeId(null);
    
    toast({
      title: "Node deleted",
      description: "Node and its children have been removed."
    });
  };

  // Handle node drag start
  const handleNodeMouseDown = (e: React.MouseEvent, nodeId: string) => {
    if (isAddingConnection) {
      // If we're adding a connection, set the start node
      if (!connectionStart) {
        setConnectionStart(nodeId);
      } else if (connectionStart !== nodeId) {
        // If we already have a start node, create the connection
        const sourceNode = mindMap.nodes[connectionStart];
        const targetNode = mindMap.nodes[nodeId];
        
        // Don't create connections to existing children or parents (avoid cycles)
        if (sourceNode.childIds.includes(nodeId) || 
            (targetNode.parentId === connectionStart)) {
          toast({
            title: "Invalid connection",
            description: "This connection already exists or would create a cycle.",
            variant: "destructive"
          });
          setConnectionStart(null);
          return;
        }

        // Create the new connection
        const newConnection: Connection = {
          sourceId: connectionStart,
          targetId: nodeId,
          color: '#6b7280' // Default gray color
        };

        // Update the parent node to reference this child
        const updatedSourceNode = {
          ...sourceNode,
          childIds: [...sourceNode.childIds, nodeId]
        };

        // Update the child to reference its parent
        const updatedTargetNode = {
          ...targetNode,
          parentId: connectionStart
        };

        // Update mind map
        setMindMap(prev => ({
          nodes: {
            ...prev.nodes,
            [connectionStart]: updatedSourceNode,
            [nodeId]: updatedTargetNode
          },
          connections: [...prev.connections, newConnection]
        }));

        setConnectionStart(null);
        toast({
          title: "Connection created",
          description: "New connection has been added."
        });
      }
      return;
    }

    // Normal node dragging
    setIsDragging(true);
    setDraggedNodeId(nodeId);
    setDragStart({ x: e.clientX, y: e.clientY });

    // Set this as the selected node
    setSelectedNodeId(nodeId);
  };

  // Handle mouse move for dragging nodes
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !draggedNodeId) return;

    const dx = (e.clientX - dragStart.x) / zoom;
    const dy = (e.clientY - dragStart.y) / zoom;

    const updatedNode = {
      ...mindMap.nodes[draggedNodeId],
      x: mindMap.nodes[draggedNodeId].x + dx,
      y: mindMap.nodes[draggedNodeId].y + dy
    };

    setMindMap(prev => ({
      ...prev,
      nodes: {
        ...prev.nodes,
        [draggedNodeId]: updatedNode
      }
    }));

    setDragStart({ x: e.clientX, y: e.clientY });
  };

  // Handle mouse up to end dragging
  const handleMouseUp = () => {
    setIsDragging(false);
    setDraggedNodeId(null);
  };

  // Generate a course structure from the mind map
  const generateCourseStructure = () => {
    if (!mindMap.nodes['node-1']) return [];

    // Start with the central node
    const centralNode = mindMap.nodes['node-1'];
    const modules: Module[] = [];

    // Convert main topics (direct children of central node) to modules
    centralNode.childIds.forEach((mainTopicId, index) => {
      const mainTopic = mindMap.nodes[mainTopicId];
      if (!mainTopic) return;

      const lessons: Lesson[] = [];

      // Convert subtopics to lessons
      mainTopic.childIds.forEach(subtopicId => {
        const subtopic = mindMap.nodes[subtopicId];
        if (!subtopic) return;

        lessons.push({
          title: subtopic.text,
          description: subtopic.description || 'Auto-generated from mind map'
        });
      });

      // If no subtopics, create at least one lesson
      if (lessons.length === 0) {
        lessons.push({
          title: mainTopic.text,
          description: mainTopic.description || 'Auto-generated from mind map'
        });
      }

      modules.push({
        title: mainTopic.text,
        description: mainTopic.description,
        lessons,
        expanded: index === 0 // Expand the first module by default
      });
    });

    // If no main topics, create a default module
    if (modules.length === 0) {
      modules.push({
        title: centralNode.text,
        description: centralNode.description,
        lessons: [
          {
            title: 'Introduction to ' + centralNode.text,
            description: 'Auto-generated from mind map'
          }
        ],
        expanded: true
      });
    }

    if (onConvertToCourseStructure) {
      onConvertToCourseStructure(modules);
      toast({
        title: "Structure generated",
        description: "Course structure has been created from your mind map."
      });
    }

    return modules;
  };

  // Save the mind map
  const handleSave = () => {
    if (onSave) {
      onSave(mindMap);
      toast({
        title: "Mind map saved",
        description: "Your mind map has been saved successfully."
      });
    }
  };

  // Export the mind map as PNG
  const handleExportImage = () => {
    if (!svgRef.current) return;

    const svg = svgRef.current;
    const serializer = new XMLSerializer();
    let source = serializer.serializeToString(svg);
    
    source = '<?xml version="1.0" standalone="no"?>\r\n' + source;
    
    const url = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(source);
    const link = document.createElement('a');
    link.download = `mind-map-${new Date().toISOString()}.svg`;
    link.href = url;
    link.click();

    toast({
      title: "Export successful",
      description: "Mind map exported as SVG file."
    });
  };

  // Generate a mind map structure using AI
  const generateMindMap = async () => {
    setIsGenerating(true);
    
    try {
      // AI generation would be implemented on the server
      // For now, we'll simulate with a simplified AI-like structure
      
      // This is a temporary mock implementation
      // In a real implementation, we would call an API endpoint
      const centralNode = mindMap.nodes['node-1'];
      
      // Create 4-6 main topics based on the course title/description
      const topics = [
        "Core Concepts",
        "Practical Applications",
        "Advanced Techniques",
        "Case Studies",
        "Best Practices"
      ];
      
      const newMindMap: MindMapData = {
        nodes: {
          'node-1': {
            ...centralNode,
            childIds: []
          }
        },
        connections: []
      };
      
      // Add main topics
      topics.forEach((topic, index) => {
        const mainTopicId = `main-${index+1}`;
        const angle = (index * Math.PI * 2) / topics.length;
        const distance = 200;
        
        // Add main topic node
        newMindMap.nodes[mainTopicId] = {
          id: mainTopicId,
          type: 'main',
          text: topic,
          description: `Key aspects of ${topic.toLowerCase()} for ${courseTitle}`,
          x: centralNode.x + Math.cos(angle) * distance,
          y: centralNode.y + Math.sin(angle) * distance,
          parentId: 'node-1',
          childIds: []
        };
        
        // Add connection from central to main topic
        newMindMap.connections.push({
          sourceId: 'node-1',
          targetId: mainTopicId,
          color: '#3b82f6'
        });
        
        // Update central node to reference this child
        newMindMap.nodes['node-1'].childIds.push(mainTopicId);
        
        // Add 2-3 subtopics for each main topic
        const subtopicCount = 2 + Math.floor(Math.random() * 2); // 2-3 subtopics
        for (let i = 0; i < subtopicCount; i++) {
          const subtopicId = `sub-${index+1}-${i+1}`;
          const subAngle = angle + ((i - subtopicCount/2) * 0.5);
          const subDistance = 350;
          
          // Generate a relevant subtopic based on the main topic
          let subtopicText = "";
          switch(topic) {
            case "Core Concepts":
              subtopicText = ["Fundamentals", "Key Principles", "Basic Theories"][i % 3];
              break;
            case "Practical Applications":
              subtopicText = ["Real-world Examples", "Hands-on Exercises", "Implementation Strategies"][i % 3];
              break;
            case "Advanced Techniques":
              subtopicText = ["Specialized Methods", "Cutting-edge Approaches", "Expert Strategies"][i % 3];
              break;
            case "Case Studies":
              subtopicText = ["Success Stories", "Problem-Solution Analysis", "Industry Examples"][i % 3];
              break;
            case "Best Practices":
              subtopicText = ["Guidelines", "Standards", "Optimization Techniques"][i % 3];
              break;
            default:
              subtopicText = `Subtopic ${i+1}`;
          }
          
          // Add subtopic node
          newMindMap.nodes[subtopicId] = {
            id: subtopicId,
            type: 'subtopic',
            text: subtopicText,
            description: `Details about ${subtopicText.toLowerCase()} for ${topic.toLowerCase()}`,
            x: centralNode.x + Math.cos(subAngle) * subDistance,
            y: centralNode.y + Math.sin(subAngle) * subDistance,
            parentId: mainTopicId,
            childIds: []
          };
          
          // Add connection from main topic to subtopic
          newMindMap.connections.push({
            sourceId: mainTopicId,
            targetId: subtopicId,
            color: '#10b981'
          });
          
          // Update main topic to reference this child
          newMindMap.nodes[mainTopicId].childIds.push(subtopicId);
        }
      });
      
      setMindMap(newMindMap);
      
      toast({
        title: "Mind map generated",
        description: "AI has created a mind map based on your course topic."
      });
    } catch (error) {
      console.error("Error generating mind map:", error);
      toast({
        title: "Generation failed",
        description: "Failed to generate mind map. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle zoom in
  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  };

  // Handle zoom out
  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  };

  // Render connections
  const renderConnections = () => {
    return mindMap.connections.map((conn, index) => {
      const sourceNode = mindMap.nodes[conn.sourceId];
      const targetNode = mindMap.nodes[conn.targetId];
      
      if (!sourceNode || !targetNode) return null;
      
      return (
        <path
          key={`conn-${index}`}
          d={`M${sourceNode.x},${sourceNode.y} L${targetNode.x},${targetNode.y}`}
          stroke={conn.color}
          strokeWidth="2"
          markerEnd="url(#arrowhead)"
        />
      );
    });
  };

  // Render nodes
  const renderNodes = () => {
    return Object.values(mindMap.nodes).map(node => {
      const isSelected = selectedNodeId === node.id;
      const nodeWidth = Math.max(node.text.length * 8 + 40, 100);
      const nodeHeight = node.type === 'central' ? 80 : 50;
      
      return (
        <g
          key={node.id}
          transform={`translate(${node.x - nodeWidth/2}, ${node.y - nodeHeight/2})`}
          onMouseDown={(e) => handleNodeMouseDown(e, node.id)}
          style={{ cursor: 'pointer' }}
        >
          <rect
            width={nodeWidth}
            height={nodeHeight}
            rx={8}
            className={`${nodeStyles[node.type]} ${isSelected ? 'ring-2 ring-offset-2 ring-primary' : ''}`}
          />
          <text
            x={nodeWidth / 2}
            y={nodeHeight / 2}
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize={node.type === 'central' ? 16 : 14}
            fontWeight={node.type === 'central' || node.type === 'main' ? 'bold' : 'normal'}
            fill="currentColor"
            className="pointer-events-none"
          >
            {node.text}
          </text>
        </g>
      );
    });
  };

  // Display the hint when the component mounts
  useEffect(() => {
    showHint("The Concept Mind Map helps you visualize course topics and structure. Add topics, drag nodes to organize, and easily convert your visual map into a structured course outline.");
  }, [showHint]);

  return (
    <div className="flex flex-col h-full">
      
      <div className="bg-slate-50 p-3 border-b border-slate-200 rounded-t-lg flex flex-wrap gap-2 items-center">
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => addNode('main')}
            disabled={!selectedNodeId}
            title="Add a main topic"
          >
            <Plus className="h-4 w-4 mr-1" />
            Main Topic
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => addNode('subtopic')}
            disabled={!selectedNodeId}
            title="Add a subtopic"
          >
            <Plus className="h-4 w-4 mr-1" />
            Subtopic
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => addNode('detail')}
            disabled={!selectedNodeId}
            title="Add a detail"
          >
            <Plus className="h-4 w-4 mr-1" />
            Detail
          </Button>
        </div>
        
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setIsAddingConnection(!isAddingConnection)}
            className={isAddingConnection ? 'bg-primary text-primary-foreground' : ''}
            title="Add a connection between nodes"
          >
            <Share2 className="h-4 w-4 mr-1" />
            {isAddingConnection ? 'Cancel' : 'Connect'}
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => selectedNodeId && deleteNode(selectedNodeId)}
            disabled={!selectedNodeId || selectedNodeId === 'node-1'}
            title="Delete selected node and all its children"
          >
            <Trash2 className="h-4 w-4 mr-1" />
            Delete
          </Button>
        </div>
        
        <div className="flex space-x-2 ml-auto">
          <Button 
            variant="default" 
            size="sm"
            onClick={generateMindMap}
            disabled={isGenerating}
            title="Generate mind map using AI"
          >
            {isGenerating ? (
              <>
                <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Wand2 className="h-4 w-4 mr-1" />
                Generate with AI
              </>
            )}
          </Button>
          <Button 
            variant="secondary" 
            size="sm"
            onClick={generateCourseStructure}
            title="Convert mind map to course structure"
          >
            <Zap className="h-4 w-4 mr-1" />
            Convert to Structure
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleSave}
            title="Save mind map"
          >
            <Save className="h-4 w-4 mr-1" />
            Save
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleExportImage}
            title="Export as image"
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
        </div>
      </div>
      
      <div className="flex">
        <div className="p-4 w-3/4 h-[600px] border-r border-slate-200 relative" ref={containerRef}>
          <div className="absolute right-4 top-4 flex flex-col bg-white border rounded-md shadow-sm">
            <Button variant="ghost" size="icon" onClick={handleZoomIn}>
              <Plus className="h-4 w-4" />
            </Button>
            <div className="border-t border-slate-200"></div>
            <Button variant="ghost" size="icon" onClick={handleZoomOut}>
              <Minus className="h-4 w-4" />
            </Button>
          </div>
          <svg 
            ref={svgRef}
            viewBox={`${viewBox.x} ${viewBox.y} ${viewBox.width} ${viewBox.height}`}
            width="100%" 
            height="100%"
            style={{ transform: `scale(${zoom})`, transformOrigin: 'center' }}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon points="0 0, 10 3.5, 0 7" />
              </marker>
            </defs>
            {renderConnections()}
            {renderNodes()}
            {/* If we're adding a connection and have a start node, show the in-progress connection */}
            {isAddingConnection && connectionStart && mindMap.nodes[connectionStart] && (
              <line
                x1={mindMap.nodes[connectionStart].x}
                y1={mindMap.nodes[connectionStart].y}
                x2={dragStart.x}
                y2={dragStart.y}
                stroke="#6b7280"
                strokeWidth="2"
                strokeDasharray="5,5"
              />
            )}
          </svg>
        </div>
        
        <div className="w-1/4 p-4 bg-slate-50 min-h-[600px]">
          {selectedNodeId ? (
            <div className="space-y-4">
              <h3 className="font-medium text-slate-900">Edit Node</h3>
              <div className="space-y-2">
                <Label htmlFor="node-text">Text</Label>
                <Input
                  id="node-text"
                  value={mindMap.nodes[selectedNodeId].text}
                  onChange={(e) => updateNode(selectedNodeId, { text: e.target.value })}
                  placeholder="Node text..."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="node-description">Description (optional)</Label>
                <Textarea
                  id="node-description"
                  value={mindMap.nodes[selectedNodeId].description || ''}
                  onChange={(e) => updateNode(selectedNodeId, { description: e.target.value })}
                  placeholder="Add more details about this node..."
                  rows={4}
                />
              </div>
              <div className="pt-2">
                <Badge variant="outline" className="mr-2">
                  Type: {mindMap.nodes[selectedNodeId].type}
                </Badge>
                <Badge variant="outline">
                  Children: {mindMap.nodes[selectedNodeId].childIds.length}
                </Badge>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 space-y-4">
              <HelpCircle className="h-12 w-12 mx-auto text-slate-400" />
              <div className="space-y-2">
                <h3 className="font-medium text-slate-900">Node Properties</h3>
                <p className="text-slate-500 text-sm">
                  Select a node in the mind map to view and edit its properties.
                </p>
              </div>
            </div>
          )}
          
          <div className="mt-8 pt-4 border-t border-slate-200">
            <h3 className="font-medium text-slate-900 mb-3">Add New Node</h3>
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="new-node-text">Text</Label>
                <Input
                  id="new-node-text"
                  value={nodeText}
                  onChange={(e) => setNodeText(e.target.value)}
                  placeholder="New node text..."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-node-description">Description (optional)</Label>
                <Input
                  id="new-node-description"
                  value={nodeDescription}
                  onChange={(e) => setNodeDescription(e.target.value)}
                  placeholder="Brief description..."
                />
              </div>
              <div className="flex space-x-2 pt-2">
                <Button 
                  variant="secondary" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => addNode('main')}
                  disabled={!selectedNodeId}
                >
                  <Plus className="h-3.5 w-3.5 mr-1" />
                  Add Main Topic
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => addNode('subtopic')}
                  disabled={!selectedNodeId}
                >
                  <Plus className="h-3.5 w-3.5 mr-1" />
                  Add Subtopic
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-4 bg-slate-50 p-3 border-t border-slate-200 rounded-b-lg flex justify-between">
        <div className="text-sm text-slate-500 flex items-center">
          <Info className="h-4 w-4 mr-1 text-slate-400" />
          {isAddingConnection 
            ? "Click on two nodes to create a connection between them" 
            : "Drag nodes to reposition them; add parent/child relationships to organize concepts"}
        </div>
        <div>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm">
                <HelpCircle className="h-4 w-4 mr-1" />
                Help
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-2">
                <h4 className="font-medium">Mind Map Tips</h4>
                <div className="text-sm space-y-1">
                  <p>• <b>Central node</b>: Your main course topic (can't be deleted)</p>
                  <p>• <b>Main topics</b>: Primary modules or themes (blue)</p>
                  <p>• <b>Subtopics</b>: Specific aspects or lessons (green)</p>
                  <p>• <b>Details</b>: Additional information or notes (amber)</p>
                  <p>• <b>Convert</b>: Transform your mind map into a course structure</p>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );
}