import React from "react";
import { useLocation } from "wouter";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  ChevronRight, 
  BookOpen,
  Video, 
  UserCircle, 
  FileVideo,
  Plus,
  LayoutDashboard,
  Presentation,
  Layers,
  Settings,
  Sparkles
} from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export default function CreatePage() {
  const [_, navigate] = useLocation();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        staggerChildren: 0.1 
      } 
    }
  };

  const cardVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { 
      y: 0, 
      opacity: 1,
      transition: { 
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    },
    hover: { 
      y: -5, 
      boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)"
    }
  };

  return (
    <div className="container py-8 px-4 max-w-7xl mx-auto">
      <header className="mb-12">
        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent mb-3">
          Create Courses
        </h1>
        <p className="text-muted-foreground text-lg max-w-3xl">
          Choose a course creation method that fits your needs and teaching style
        </p>
      </header>

      {/* Course Type Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        {/* Category 1: Full Courses */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-2">
            <BookOpen className="h-5 w-5 text-primary" />
            <h2 className="text-2xl font-semibold">Traditional Courses</h2>
          </div>
          <p className="text-muted-foreground mb-6">
            Create comprehensive, structured learning experiences with multiple sections,
            lessons, and assessments for in-depth education.
          </p>

          <motion.div 
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-4"
          >
            {/* Traditional Course */}
            <motion.div 
              variants={cardVariants}
              whileHover="hover"
              onClick={() => navigate('/course-creation/traditional-course')}
              className="cursor-pointer"
            >
              <Card className="overflow-hidden border-2 hover:border-primary/50 transition-colors">
                <CardContent className="p-0">
                  <div className="grid md:grid-cols-3 items-stretch">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 flex flex-col justify-center">
                      <div className="rounded-full bg-blue-200/50 w-16 h-16 flex items-center justify-center mb-4">
                        <Presentation className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="text-xl font-semibold mb-2">Traditional Course</h3>
                      <p className="text-sm text-muted-foreground">
                        Multi-section courses with various content types
                      </p>
                    </div>
                    <div className="md:col-span-2 p-6">
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <LayoutDashboard className="h-4 w-4 text-primary" />
                        Structured Learning
                      </h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        Create a structured course with organized sections, multiple lesson types,
                        and assessment tools. Perfect for comprehensive topics and professional training.
                      </p>
                      <div className="grid grid-cols-2 gap-4 mt-auto">
                        <div className="border rounded-md p-3 flex items-start gap-2">
                          <Layers className="h-4 w-4 text-primary mt-0.5" />
                          <span className="text-xs">Multiple sections & lessons</span>
                        </div>
                        <div className="border rounded-md p-3 flex items-start gap-2">
                          <Video className="h-4 w-4 text-primary mt-0.5" />
                          <span className="text-xs">Video, text & quiz content</span>
                        </div>
                      </div>
                      <Button className="w-full mt-4 gap-2">
                        <Plus className="h-4 w-4" />
                        Create Traditional Course
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>

        {/* Category 2: Avatar-Based Courses */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-2">
            <UserCircle className="h-5 w-5 text-primary" />
            <h2 className="text-2xl font-semibold">Avatar Courses</h2>
          </div>
          <p className="text-muted-foreground mb-6">
            Quickly create engaging video courses with AI-powered avatars that present your content
            without needing recording equipment or editing skills.
          </p>

          <motion.div 
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-4"
          >
            {/* Talking Avatar */}
            <motion.div 
              variants={cardVariants}
              whileHover="hover"
              onClick={() => navigate('/course-creation/avatar-course')}
              className="cursor-pointer"
            >
              <Card className="overflow-hidden border-2 hover:border-primary/50 transition-colors">
                <CardContent className="p-0">
                  <div className="grid md:grid-cols-3 items-stretch">
                    <div className="bg-gradient-to-br from-violet-50 to-violet-100 p-6 flex flex-col justify-center">
                      <div className="rounded-full bg-violet-200/50 w-16 h-16 flex items-center justify-center mb-4">
                        <UserCircle className="h-8 w-8 text-violet-600" />
                      </div>
                      <h3 className="text-xl font-semibold mb-2">Talking Avatar</h3>
                      <p className="text-sm text-muted-foreground">
                        AI-powered video presenter for your content
                      </p>
                    </div>
                    <div className="md:col-span-2 p-6">
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Sparkles className="h-4 w-4 text-primary" />
                        AI-Powered Creation
                      </h4>
                      <p className="text-sm text-muted-foreground mb-4">
                        Create professional-looking video courses without recording equipment.
                        Just write your script, select an avatar, and our AI will generate the video.
                      </p>
                      <div className="grid grid-cols-2 gap-4 mt-auto">
                        <div className="border rounded-md p-3 flex items-start gap-2">
                          <UserCircle className="h-4 w-4 text-primary mt-0.5" />
                          <span className="text-xs">Professional avatars</span>
                        </div>
                        <div className="border rounded-md p-3 flex items-start gap-2">
                          <Settings className="h-4 w-4 text-primary mt-0.5" />
                          <span className="text-xs">Customizable voices & styles</span>
                        </div>
                      </div>
                      <Button className="w-full mt-4 gap-2">
                        <Plus className="h-4 w-4" />
                        Create Avatar Course
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Quick Help Section */}
      <div className="bg-muted/50 rounded-lg p-6 max-w-3xl mx-auto">
        <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-amber-500" />
          How to Choose the Right Course Format
        </h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium mb-2">Choose Traditional Course if you need:</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-start gap-2">
                <ChevronRight className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                <span>Comprehensive structure with multiple sections and lessons</span>
              </li>
              <li className="flex items-start gap-2">
                <ChevronRight className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                <span>Various content types (video, text, quizzes, etc.)</span>
              </li>
              <li className="flex items-start gap-2">
                <ChevronRight className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                <span>Assessment capabilities for learners</span>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Choose Avatar Course if you need:</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li className="flex items-start gap-2">
                <ChevronRight className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                <span>Quick course creation without video equipment</span>
              </li>
              <li className="flex items-start gap-2">
                <ChevronRight className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                <span>Professional-looking presenter for your content</span>
              </li>
              <li className="flex items-start gap-2">
                <ChevronRight className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                <span>Flexibility to create courses from just a script</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}