/**
 * Comprehensive Voice Integration Test Script
 * Tests the complete Coqui TTS voice discovery and integration implementation
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api`;

class VoiceIntegrationTester {
  constructor() {
    this.results = {
      discovery: { passed: 0, failed: 0, tests: [] },
      api: { passed: 0, failed: 0, tests: [] },
      voices: { passed: 0, failed: 0, tests: [] },
      performance: { passed: 0, failed: 0, tests: [] }
    };
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Voice Integration Tests\n');
    
    try {
      await this.testVoiceDiscovery();
      await this.testAPIEndpoints();
      await this.testVoiceFunctionality();
      await this.testPerformance();
      
      this.printResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  async testVoiceDiscovery() {
    console.log('📡 Testing Voice Discovery...');
    
    // Test 1: TTS Models Endpoint
    await this.test('discovery', 'TTS Models Endpoint', async () => {
      const response = await fetch(`${API_BASE}/tts/tts-models`);
      const data = await response.json();
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      if (!data.voices || !Array.isArray(data.voices)) throw new Error('Invalid voices array');
      if (!data.statistics) throw new Error('Missing statistics');
      
      console.log(`  ✓ Found ${data.voices.length} voices`);
      console.log(`  ✓ Services: ${Object.keys(data.services).join(', ')}`);
      
      return { voiceCount: data.voices.length, services: data.services };
    });

    // Test 2: Voice Statistics
    await this.test('discovery', 'Voice Statistics', async () => {
      const response = await fetch(`${API_BASE}/tts/voice-statistics`);
      const data = await response.json();
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      if (!data.statistics) throw new Error('Missing statistics');
      
      console.log(`  ✓ Total voices: ${data.statistics.total}`);
      console.log(`  ✓ Languages: ${Object.keys(data.statistics.byLanguage).length}`);
      
      return data.statistics;
    });

    // Test 3: Service-specific voices
    await this.test('discovery', 'Coqui Service Voices', async () => {
      const response = await fetch(`${API_BASE}/tts/voices/coqui`);
      const data = await response.json();
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      if (!data.voices || !Array.isArray(data.voices)) throw new Error('Invalid voices array');
      
      console.log(`  ✓ Coqui voices: ${data.voices.length}`);
      
      return { count: data.voices.length };
    });
  }

  async testAPIEndpoints() {
    console.log('\n🔌 Testing API Endpoints...');
    
    // Test 1: Refresh Coqui Voices
    await this.test('api', 'Refresh Coqui Voices', async () => {
      const response = await fetch(`${API_BASE}/tts/refresh-coqui-voices`, {
        method: 'POST'
      });
      const data = await response.json();
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      if (!data.success) throw new Error('Refresh failed');
      
      console.log(`  ✓ Refreshed ${data.voiceCount} voices`);
      
      return { voiceCount: data.voiceCount };
    });

    // Test 2: Voice Testing
    await this.test('api', 'Voice Testing Endpoint', async () => {
      const testVoiceId = 'tts_models/en/ljspeech/tacotron2-DDC';
      const response = await fetch(`${API_BASE}/tts/test-voice`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ voiceId: testVoiceId })
      });
      const data = await response.json();
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      if (!data.testResult) throw new Error('Missing test result');
      
      console.log(`  ✓ Voice test: ${data.testResult.available ? 'PASS' : 'FAIL'}`);
      if (data.testResult.error) {
        console.log(`  ⚠️ Error: ${data.testResult.error}`);
      }
      
      return data.testResult;
    });
  }

  async testVoiceFunctionality() {
    console.log('\n🎤 Testing Voice Functionality...');
    
    // Get available voices first
    const voicesResponse = await fetch(`${API_BASE}/tts/tts-models`);
    const voicesData = await voicesResponse.json();
    const coquiVoices = voicesData.voices.filter(v => v.service === 'coqui_tts');
    
    if (coquiVoices.length === 0) {
      console.log('  ⚠️ No Coqui voices available for testing');
      return;
    }

    // Test a sample of voices (max 5 to avoid overwhelming)
    const testVoices = coquiVoices.slice(0, Math.min(5, coquiVoices.length));
    
    for (const voice of testVoices) {
      await this.test('voices', `Voice: ${voice.name}`, async () => {
        const response = await fetch(`${API_BASE}/tts/test-voice`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ voiceId: voice.id })
        });
        const data = await response.json();
        
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        
        const result = {
          available: data.testResult?.available || false,
          error: data.testResult?.error,
          metadata: {
            language: voice.language,
            quality: voice.quality,
            category: voice.category
          }
        };
        
        if (result.available) {
          console.log(`  ✓ ${voice.name} (${voice.language}) - Working`);
        } else {
          console.log(`  ❌ ${voice.name} (${voice.language}) - Failed: ${result.error}`);
        }
        
        return result;
      });
    }
  }

  async testPerformance() {
    console.log('\n⚡ Testing Performance...');
    
    // Test 1: API Response Time
    await this.test('performance', 'API Response Time', async () => {
      const startTime = Date.now();
      const response = await fetch(`${API_BASE}/tts/tts-models`);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      console.log(`  ✓ Response time: ${responseTime}ms`);
      
      if (responseTime > 5000) {
        throw new Error(`Response too slow: ${responseTime}ms`);
      }
      
      return { responseTime };
    });

    // Test 2: Cache Performance
    await this.test('performance', 'Cache Performance', async () => {
      // First request (should hit server)
      const start1 = Date.now();
      await fetch(`${API_BASE}/tts/tts-models`);
      const time1 = Date.now() - start1;
      
      // Second request (should be faster due to caching)
      const start2 = Date.now();
      await fetch(`${API_BASE}/tts/tts-models`);
      const time2 = Date.now() - start2;
      
      console.log(`  ✓ First request: ${time1}ms`);
      console.log(`  ✓ Second request: ${time2}ms`);
      
      // Second request should be significantly faster (allowing some variance)
      const improvement = ((time1 - time2) / time1) * 100;
      console.log(`  ✓ Performance improvement: ${improvement.toFixed(1)}%`);
      
      return { firstRequest: time1, secondRequest: time2, improvement };
    });

    // Test 3: Large Dataset Handling
    await this.test('performance', 'Large Dataset Handling', async () => {
      const response = await fetch(`${API_BASE}/tts/voice-statistics`);
      const data = await response.json();
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      const totalVoices = data.statistics.total;
      console.log(`  ✓ Handling ${totalVoices} voices`);
      
      if (totalVoices < 10) {
        console.log('  ⚠️ Low voice count - may indicate discovery issues');
      }
      
      return { totalVoices };
    });
  }

  async test(category, name, testFn) {
    try {
      const result = await testFn();
      this.results[category].passed++;
      this.results[category].tests.push({ name, status: 'PASS', result });
    } catch (error) {
      this.results[category].failed++;
      this.results[category].tests.push({ name, status: 'FAIL', error: error.message });
      console.log(`  ❌ ${name}: ${error.message}`);
    }
  }

  printResults() {
    console.log('\n📊 Test Results Summary');
    console.log('========================');
    
    let totalPassed = 0;
    let totalFailed = 0;
    
    Object.entries(this.results).forEach(([category, results]) => {
      console.log(`\n${category.toUpperCase()}:`);
      console.log(`  ✅ Passed: ${results.passed}`);
      console.log(`  ❌ Failed: ${results.failed}`);
      
      totalPassed += results.passed;
      totalFailed += results.failed;
      
      if (results.failed > 0) {
        console.log('  Failed tests:');
        results.tests
          .filter(t => t.status === 'FAIL')
          .forEach(t => console.log(`    - ${t.name}: ${t.error}`));
      }
    });
    
    console.log('\n========================');
    console.log(`TOTAL: ${totalPassed} passed, ${totalFailed} failed`);
    
    if (totalFailed === 0) {
      console.log('🎉 All tests passed! Voice integration is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please review the implementation.');
    }
    
    // Voice discovery summary
    const discoveryResults = this.results.discovery.tests.find(t => t.name === 'TTS Models Endpoint');
    if (discoveryResults && discoveryResults.result) {
      console.log(`\n🎤 Voice Discovery Summary:`);
      console.log(`   Total voices discovered: ${discoveryResults.result.voiceCount}`);
      console.log(`   Services available: ${Object.keys(discoveryResults.result.services).join(', ')}`);
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new VoiceIntegrationTester();
  tester.runAllTests().catch(console.error);
}

module.exports = VoiceIntegrationTester;
