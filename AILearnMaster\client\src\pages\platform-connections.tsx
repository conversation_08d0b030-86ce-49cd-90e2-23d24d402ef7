import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Link } from 'wouter';
import {
  ArrowLeft,
  Filter,
  Search,
  Plus,
  RefreshCw,
  CheckCircle2,
  ExternalLink
} from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import IntegrationCard from '@/components/integrations/integration-card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { PlatformIcon } from '@/components/integrations/platform-logos';
import { useToast } from '@/hooks/use-toast';
import { PlatformIntegration } from '@/types/platform-integration';
import { getPlatformsByCategory, platformCategories, defaultPlatforms, getConnectedPlatformsCount } from '@/lib/platform-utils';
import { apiRequest } from '@/lib/queryClient';

export default function PlatformConnections() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Load platforms from the API
  const {
    data: rawPlatforms,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['/api/platform-integrations'],
    retry: 1,
  });
  
  // Use default platforms when the API returns an empty object or nothing
  const platforms: PlatformIntegration[] = (rawPlatforms && Array.isArray(rawPlatforms) && rawPlatforms.length > 0) 
    ? rawPlatforms as PlatformIntegration[]
    : defaultPlatforms;
  
  // Connect platform mutation
  const connectMutation = useMutation({
    mutationFn: async (platformSlug: string) => {
      const response = await apiRequest('POST', `/api/platform-integrations/${platformSlug}/connect`, {});
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to connect platform");
      }
      
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/platform-integrations'] });
      toast({
        title: "Platform connected",
        description: "You have successfully connected the platform.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Connection failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Disconnect platform mutation
  const disconnectMutation = useMutation({
    mutationFn: async (platformId: number) => {
      const response = await apiRequest('DELETE', `/api/platform-integrations/${platformId}/disconnect`);
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to disconnect platform");
      }
      
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/platform-integrations'] });
      toast({
        title: "Platform disconnected",
        description: "You have successfully disconnected the platform.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Disconnection failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Manually refresh the platforms list
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await queryClient.invalidateQueries({ queryKey: ['/api/platform-integrations'] });
      toast({
        title: "Platforms refreshed",
        description: "The platform list has been refreshed.",
      });
    } catch (error) {
      toast({
        title: "Refresh failed",
        description: "Could not refresh the platforms list.",
        variant: "destructive",
      });
    } finally {
      setIsRefreshing(false);
    }
  };
  
  // Connect a platform
  const handleConnect = (platform: PlatformIntegration) => {
    connectMutation.mutate(platform.slug);
  };
  
  // Disconnect a platform
  const handleDisconnect = (platform: PlatformIntegration) => {
    if (platform.connectionId) {
      disconnectMutation.mutate(platform.connectionId);
    } else {
      toast({
        title: "Disconnection failed",
        description: "Could not find connection ID.",
        variant: "destructive",
      });
    }
  };
  
  // Filter platforms by search query and category
  const filteredPlatforms = platforms.filter(platform => {
    const matchesSearch = searchQuery === '' || 
      platform.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
      platform.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = activeCategory === 'all' || 
      (activeCategory === 'connected' && platform.isConnected) ||
      (activeCategory !== 'all' && activeCategory !== 'connected' && platform.category === activeCategory);
    
    return matchesSearch && matchesCategory;
  });
  
  // Group platforms by category
  const platformsByCategory = getPlatformsByCategory(platforms);
  
  // Count connected platforms
  const connectedCount = getConnectedPlatformsCount(platforms);
  
  // Generate skeleton UI during loading
  const renderSkeletons = () => {
    return Array(6).fill(0).map((_, index) => (
      <div key={index} className="flex flex-col h-full">
        <div className="space-y-3 p-4 border rounded-lg">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-10 w-10 rounded-md" />
            <Skeleton className="h-6 w-28" />
          </div>
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <div className="space-y-2 mt-4">
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-4/5" />
          </div>
          <Skeleton className="h-9 w-full mt-4" />
        </div>
      </div>
    ));
  };
  
  return (
    <div className="container py-6 space-y-6 max-w-6xl">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link to="/dashboard">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Platform Connections</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="hidden md:flex" 
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button size="sm" className="hidden md:flex">
            <Plus className="h-4 w-4 mr-2" />
            Request Integration
          </Button>
        </div>
      </div>
      
      <Alert className="bg-muted">
        <AlertTitle className="text-base">Connect your platforms</AlertTitle>
        <AlertDescription>
          Connect to your preferred platforms to automatically publish and sync your courses. Once connected, you'll be able to select these platforms when publishing.
        </AlertDescription>
      </Alert>
      
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative w-full sm:w-80">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search platforms..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button variant="outline" size="sm" className="gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </Button>
          {activeCategory !== 'all' && (
            <Badge variant="secondary" className="gap-2">
              {activeCategory === 'connected' ? 'Connected' : activeCategory}
              <button 
                onClick={() => setActiveCategory('all')}
                className="text-xs font-medium rounded-full h-4 w-4 inline-flex items-center justify-center bg-muted hover:bg-muted/80"
              >
                ×
              </button>
            </Badge>
          )}
        </div>
      </div>
      
      <Tabs defaultValue="all" value={activeCategory} onValueChange={setActiveCategory}>
        <TabsList>
          <TabsTrigger value="all">
            All
          </TabsTrigger>
          <TabsTrigger value="connected">
            Connected 
            {connectedCount > 0 && <Badge variant="secondary" className="ml-1">{connectedCount}</Badge>}
          </TabsTrigger>
          <TabsTrigger value={platformCategories.EDUCATION}>Education</TabsTrigger>
          <TabsTrigger value={platformCategories.SOCIAL}>Social Media</TabsTrigger>
          <TabsTrigger value={platformCategories.VIDEO}>Video</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="pt-4">
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {renderSkeletons()}
            </div>
          ) : isError ? (
            <div className="p-8 text-center">
              <p className="text-red-500">Error loading platforms: {error?.toString()}</p>
              <Button 
                onClick={handleRefresh} 
                variant="outline" 
                className="mt-4"
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Try Again
              </Button>
            </div>
          ) : filteredPlatforms.length === 0 ? (
            <div className="p-8 text-center">
              <p className="text-muted-foreground">No platforms match your search criteria.</p>
              {searchQuery && (
                <Button 
                  onClick={() => setSearchQuery('')} 
                  variant="outline" 
                  className="mt-4"
                >
                  Clear Search
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filteredPlatforms.map((platform) => (
                <IntegrationCard
                  key={platform.id}
                  platform={platform}
                  onConnect={() => handleConnect(platform)}
                  onDisconnect={() => handleDisconnect(platform)}
                />
              ))}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="connected" className="pt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {isLoading ? (
              renderSkeletons()
            ) : (
              platforms
                .filter(platform => platform.isConnected)
                .map(platform => (
                  <IntegrationCard
                    key={platform.id}
                    platform={platform}
                    onConnect={() => handleConnect(platform)}
                    onDisconnect={() => handleDisconnect(platform)}
                  />
                ))
            )}
            {!isLoading && platforms.filter(platform => platform.isConnected).length === 0 && (
              <div className="col-span-full p-8 text-center">
                <p className="text-muted-foreground">You haven't connected any platforms yet.</p>
              </div>
            )}
          </div>
        </TabsContent>
        
        {/* Category tabs content */}
        {Object.entries(platformCategories).map(([_, category]) => (
          <TabsContent key={category} value={category} className="pt-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {isLoading ? (
                renderSkeletons()
              ) : (
                platformsByCategory[category]?.map(platform => (
                  <IntegrationCard
                    key={platform.id}
                    platform={platform}
                    onConnect={() => handleConnect(platform)}
                    onDisconnect={() => handleDisconnect(platform)}
                  />
                ))
              )}
              {!isLoading && (!platformsByCategory[category] || platformsByCategory[category]?.length === 0) && (
                <div className="col-span-full p-8 text-center">
                  <p className="text-muted-foreground">No platforms in this category.</p>
                </div>
              )}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}