import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Loader2, Wand2, AlertCircle } from "lucide-react";
import { generateLessonScript } from "@/lib/ai";
import { ScriptExporter } from "@/components/script/ScriptExporter";
import { ScriptFormatter } from "@/components/script/ScriptFormatter";
import { useAuth } from "@/hooks/use-auth";

interface ScriptGenerationProps {
  onNext: () => void;
  onPrevious: () => void;
  courseDetails: {
    title: string;
    description: string;
    category: string;
    targetAudience?: string;
    useAI: boolean;
    moduleCount?: number;
  };
  courseStructure: {
    modules: Array<{
      title: string;
      description?: string;
      script?: string;
      lessons: Array<{
        title: string;
        description: string;
      }>;
    }>;
  };
  courseScripts?: {
    [moduleKey: string]: string;
  };
  onUpdateScripts?: React.Dispatch<React.SetStateAction<{
    [moduleKey: string]: string;
  }>>;
}

interface Script {
  moduleIndex: number;
  moduleKey: string;
  content: string;
  isGenerating: boolean;
  error: string | null;
}

// Define CourseScripts interface to match the type used in props
type CourseScripts = {
  [moduleKey: string]: string;
};

export function ScriptGeneration({ 
  onNext, 
  onPrevious, 
  courseDetails, 
  courseStructure,
  courseScripts = {},
  onUpdateScripts
}: ScriptGenerationProps) {
  const [activeModuleIndex, setActiveModuleIndex] = useState<number>(0);
  const [scripts, setScripts] = useState<Script[]>([]);
  
  // Initialize scripts array based on course structure and populate from courseScripts
  useEffect(() => {
    if (courseStructure.modules.length > 0) {
      const initialScripts: Script[] = [];
      
      courseStructure.modules.forEach((module, moduleIndex) => {
        // Check if there's existing script content in the courseScripts prop
        const moduleKey = `module-${moduleIndex}`;
        const existingContent = courseScripts[moduleKey] || "";
        
        initialScripts.push({
          moduleIndex,
          moduleKey,
          content: existingContent,
          isGenerating: false,
          error: null
        });
      });
      
      setScripts(initialScripts);
    }
  }, [courseStructure, courseScripts]);
  
  const findScript = (moduleIndex: number): Script | undefined => {
    return scripts.find(script => script.moduleIndex === moduleIndex);
  };
  
  const updateScript = (moduleIndex: number, updates: Partial<Script>) => {
    // Compute key for moduleIndex
    const moduleKey = `module-${moduleIndex}`;
    
    setScripts(prevScripts => 
      prevScripts.map(script => 
        script.moduleIndex === moduleIndex
          ? { 
              ...script, 
              ...updates,
              moduleKey  // Ensure key is always set
            }
          : script
      )
    );
    
    // Update the parent component's script state if onUpdateScripts is provided
    // and there's content to update (only update when we have non-empty content)
    if (onUpdateScripts && 'content' in updates && updates.content) {
      onUpdateScripts(prevScripts => {
        // Create a deep copy to avoid direct state mutation
        const newScripts = { ...prevScripts };
        
        // Update the script content
        newScripts[moduleKey] = updates.content as string;
        
        return newScripts;
      });
    }
  };
  
  const handleGenerateScript = async (moduleIndex: number) => {
    // Mark script as generating
    updateScript(moduleIndex, { isGenerating: true, error: null });
    
    try {
      const module = courseStructure.modules[moduleIndex];
      
      const result = await generateLessonScript({
        courseTitle: courseDetails.title,
        courseDescription: courseDetails.description,
        moduleTitle: module.title,
        moduleDescription: module.description || "",
        lessonTitle: `Module ${moduleIndex + 1}`, // Just pass the module number as lesson title
        lessonDescription: "This is a comprehensive module script", // Generic description for module script
        targetAudience: courseDetails.targetAudience || "general audience"
      });
      
      if (result && result.script) {
        updateScript(moduleIndex, { 
          content: result.script,
          isGenerating: false 
        });
      } else {
        throw new Error("Failed to generate script");
      }
    } catch (error) {
      console.error("Error generating script:", error);
      updateScript(moduleIndex, { 
        isGenerating: false,
        error: error instanceof Error ? error.message : "Failed to generate script"
      });
    }
  };
  
  const handleScriptChange = (moduleIndex: number, content: string) => {
    updateScript(moduleIndex, { content });
  };
  
  const handleGenerateAllScripts = async () => {
    // Generate scripts for all modules
    for (let moduleIndex = 0; moduleIndex < courseStructure.modules.length; moduleIndex++) {
      await handleGenerateScript(moduleIndex);
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold">Module Scripts</h3>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => {
            // Save scripts to parent component state before navigating back
            if (onUpdateScripts) {
              // Make sure all local script changes are synced to parent
              const updatedScripts: CourseScripts = {};
              
              scripts.forEach(script => {
                updatedScripts[script.moduleKey] = script.content;
              });
              
              onUpdateScripts(updatedScripts);
            }
            onPrevious();
          }}>Back</Button>
          <Button onClick={() => {
            // Save scripts to parent component state before navigating forward
            if (onUpdateScripts) {
              // Make sure all local script changes are synced to parent
              const updatedScripts: CourseScripts = {};
              
              scripts.forEach(script => {
                updatedScripts[script.moduleKey] = script.content;
              });
              
              onUpdateScripts(updatedScripts);
            }
            onNext();
          }}>Continue</Button>
        </div>
      </div>
      
      <div className="bg-slate-50 p-4 rounded-md border border-slate-200">
        <p className="text-sm text-slate-600">
          In this step, you'll create a script for each module in your course. Each module has its own dedicated script for a cohesive learning experience.
        </p>
      </div>
      
      <Tabs 
        defaultValue={`module-${activeModuleIndex}`} 
        className="w-full"
        onValueChange={(value) => {
          const moduleIndex = parseInt(value.split('-')[1]);
          setActiveModuleIndex(moduleIndex);
        }}
      >
        <TabsList className="mb-4">
          {courseStructure.modules.map((module, index) => (
            <TabsTrigger key={index} value={`module-${index}`}>
              {module.title}
            </TabsTrigger>
          ))}
        </TabsList>
        
        {courseStructure.modules.map((module, moduleIndex) => {
          const script = findScript(moduleIndex);
          
          return (
            <TabsContent key={moduleIndex} value={`module-${moduleIndex}`} className="space-y-4">
              <div className="flex justify-between items-center">
                <h4 className="font-medium">{module.title}</h4>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleGenerateAllScripts}
                  disabled={scripts.some(s => s.isGenerating)}
                >
                  <Wand2 className="mr-2 h-4 w-4" />
                  Generate All Scripts
                </Button>
              </div>
              
              {module.description && (
                <p className="text-sm text-slate-600 mb-4">{module.description}</p>
              )}
              
              <Card className="border">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium">Module Script</h3>
                    <div className="flex space-x-2">
                      {script && script.content && script.content.trim().length > 0 && (
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            // Export the script
                            const blob = new Blob([script.content], { type: 'text/plain' });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `${module.title.replace(/\s+/g, '_')}_script.txt`;
                            a.click();
                            URL.revokeObjectURL(url);
                          }}
                        >
                          Export Script
                        </Button>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleGenerateScript(moduleIndex)}
                        disabled={script?.isGenerating}
                      >
                        {script?.isGenerating ? (
                          <>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Generating...
                          </>
                        ) : (
                          <>
                            <Wand2 className="h-4 w-4 mr-2" />
                            Generate Script
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  {script?.error && (
                    <Alert variant="destructive" className="mb-4">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Error</AlertTitle>
                      <AlertDescription>
                        {script.error}
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  {script?.isGenerating ? (
                    <div className="flex items-center justify-center py-16 bg-slate-50 rounded-md border border-slate-200">
                      <Loader2 className="h-8 w-8 animate-spin text-primary mr-3" />
                      <span className="text-slate-600">Generating comprehensive script...</span>
                    </div>
                  ) : (
                    <Textarea
                      value={script?.content || ""}
                      onChange={(e) => handleScriptChange(moduleIndex, e.target.value)}
                      className="min-h-[400px] font-mono text-sm resize-y"
                      placeholder="Write your module script here or click 'Generate Script' to create one automatically..."
                      disabled={script?.isGenerating}
                    />
                  )}
                  
                  {/* Mini Lessons Overview */}
                  {module.lessons && module.lessons.length > 0 && (
                    <div className="mt-6 pt-4 border-t">
                      <h4 className="text-sm font-medium mb-2">Lessons in this module:</h4>
                      <ul className="space-y-1 text-sm text-muted-foreground">
                        {module.lessons.map((lesson, idx) => (
                          <li key={idx} className="flex items-center">
                            <span className="w-6 text-center">{idx + 1}.</span>
                            <span>{lesson.title}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
  );
}