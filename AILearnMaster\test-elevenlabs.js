import { ElevenLabsClient } from 'elevenlabs';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Check for API key
if (!process.env.ELEVENLABS_API_KEY) {
  console.error('Error: ELEVENLABS_API_KEY environment variable is required');
  process.exit(1);
}

// Initialize ElevenLabs client
const client = new ElevenLabsClient({
  apiKey: process.env.ELEVENLABS_API_KEY,
});

// Get all available methods on the client object
console.log('Client properties:', Object.getOwnPropertyNames(client));
console.log('Client prototype properties:', Object.getOwnPropertyNames(Object.getPrototypeOf(client)));

// Get voices
async function getVoices() {
  try {
    const voices = await client.voices.getAll();
    console.log('Voices:', JSON.stringify(voices, null, 2));
  } catch (error) {
    console.error('Error getting voices:', error);
  }
}

// Test text-to-speech
async function testTextToSpeech() {
  try {
    console.log('Generating speech...');
    const audioStream = await client.generate({
      voice: 'Adam',
      text: 'Hello, this is a test of the ElevenLabs API. I hope this works correctly!',
      model_id: 'eleven_multilingual_v2',
    });
    
    console.log('Got audio stream, writing to file...');
    const outputFile = path.join(__dirname, 'test-output.mp3');
    const writeStream = fs.createWriteStream(outputFile);
    
    await new Promise((resolve, reject) => {
      audioStream.pipe(writeStream);
      writeStream.on('finish', () => {
        console.log(`Audio saved to ${outputFile}`);
        resolve();
      });
      writeStream.on('error', reject);
    });
  } catch (error) {
    console.error('Error generating speech:', error);
  }
}

// Run tests
async function runTests() {
  await getVoices();
  await testTextToSpeech();
}

runTests().catch(console.error);