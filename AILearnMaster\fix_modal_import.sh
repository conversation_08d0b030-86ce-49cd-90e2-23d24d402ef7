#!/bin/bash

# Modal Import Fix Script
# This script resolves the Modal import issue by setting up the proper Python environment

echo "Modal Import Fix - Diagnostic and Resolution"
echo "=============================================="

# Check if we're in a Nix environment
if [ -n "$NIX_STORE" ]; then
    echo "✓ Nix environment detected"
    
    # Find Python in Nix store
    PYTHON_PATH=$(find /nix/store -name "python3" -type f -executable 2>/dev/null | head -1)
    
    if [ -n "$PYTHON_PATH" ]; then
        echo "✓ Found Python at: $PYTHON_PATH"
        
        # Test Modal import
        echo "Testing Modal import..."
        $PYTHON_PATH -c "import modal; print('✓ Modal successfully imported!')" 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo "✓ Modal is working correctly!"
            echo "Running your modal_example.py..."
            $PYTHON_PATH modal_example.py
        else
            echo "✗ Modal import failed"
            echo "Installing Modal via pip..."
            $PYTHON_PATH -m pip install modal --user
            
            # Test again
            $PYTHON_PATH -c "import modal; print('✓ Modal successfully imported after installation!')"
            if [ $? -eq 0 ]; then
                echo "✓ Modal now working! Running your example..."
                $PYTHON_PATH modal_example.py
            else
                echo "✗ Modal still not working. Please check your environment configuration."
            fi
        fi
    else
        echo "✗ Python not found in Nix store"
    fi
else
    echo "✗ Not in Nix environment"
    
    # Try system Python
    if command -v python3 &> /dev/null; then
        echo "✓ System Python3 found"
        python3 -c "import modal; print('✓ Modal works with system Python!')" 2>/dev/null || {
            echo "Installing Modal with system Python..."
            python3 -m pip install modal --user
            python3 -c "import modal; print('✓ Modal now works!')"
        }
        python3 modal_example.py
    else
        echo "✗ No Python found"
    fi
fi

echo "=============================================="
echo "Fix complete!"