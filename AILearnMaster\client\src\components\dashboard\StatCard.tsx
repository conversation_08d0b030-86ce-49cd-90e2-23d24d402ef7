interface StatCardProps {
  title: string;
  value: string | number;
  icon: string;
  iconBgClass: string;
  iconColorClass: string;
}

export function StatCard({ title, value, icon, iconBgClass, iconColorClass }: StatCardProps) {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-5">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-slate-500">{title}</p>
          <h3 className="text-2xl font-bold text-slate-900 mt-1">{value}</h3>
        </div>
        <div className={`h-12 w-12 rounded-full ${iconBgClass} flex items-center justify-center`}>
          <i className={`${icon} text-xl ${iconColorClass}`}></i>
        </div>
      </div>
    </div>
  );
}
