import express, { type Express, type Request, type Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import session from "express-session";
import path from 'path';
import fs from 'fs';
import { storage } from "./storage";

// Extend Request to include authentication methods
declare global {
  namespace Express {
    interface Request {
      isAuthenticated(): boolean;
      user?: any;
    }
  }
}
import { 
  insertCourseSchema, 
  insertLessonSchema, 
  insertMediaSchema, 
  insertUserSchema,
  insertTeamSchema,
  insertTeamMemberSchema,
  insertCourseCollaboratorSchema,
  insertTeamCourseSchema,
  Lesson,
  InsertAnalyticsEvent
} from "@shared/schema";
import { z } from "zod";
import * as geminiService from "./services/gemini";
import { AuthService } from "./services/auth";
import stripe, { stripeService } from "./services/stripe";
import { analyticsService } from "./services/analytics";
import { ElevenlabsService } from "./services/elevenlabs";
import { pexelsService } from "./services/pexels";
import { pixabayService } from "./services/pixabay";
import { stabilityService } from "./services/stability";
import upload from './middleware/upload';
// HeyGen routes removed
import * as openai from './services/openai';
import * as openAIFallbackService from './services/openAIFallbackService';

// Extend Express Request to include session
declare module "express-session" {
  interface SessionData {
    userId?: number;
  }
};

export async function registerRoutes(app: Express): Promise<Server> {
  // Serve static files from uploads directory
  app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));
  
  // API routes prefix
  const api = express.Router();
  app.use("/api", api);
  
  // HeyGen routes removed

  // Authentication routes
  api.post("/auth/login", async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;
      
      if (!username || !password) {
        return res.status(400).json({ message: "Username and password are required" });
      }
      
      const user = await storage.getUserByUsername(username);
      
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }
      
      // Verify password using secure comparison
      const isValidPassword = await AuthService.comparePassword(password, user.password);
      
      if (!isValidPassword) {
        return res.status(401).json({ message: "Invalid credentials" });
      }
      
      // Set user in session
      if (req.session) {
        req.session.userId = user.id;
      }
      
      // Create safe user object (excluding password)
      const safeUser = AuthService.createSafeUser(user);
      
      return res.status(200).json(safeUser);
    } catch (error) {
      console.error("Login error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.post("/auth/register", async (req: Request, res: Response) => {
    try {
      const result = insertUserSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      const existingUser = await storage.getUserByUsername(result.data.username);
      
      if (existingUser) {
        return res.status(409).json({ message: "Username already exists" });
      }
      
      // Hash password before storing
      const hashedPassword = await AuthService.hashPassword(result.data.password);
      
      const user = await storage.createUser({
        ...result.data,
        password: hashedPassword
      });
      
      // Create default user stats
      await storage.createUserStats({
        userId: user.id,
        activeCourses: 0,
        publishedCourses: 0,
        aiCredits: 500,
        storageUsed: 0,
        storageLimit: 1000
      });
      
      // Set user in session
      if (req.session) {
        req.session.userId = user.id;
      }
      
      // Create safe user object (excluding password)
      const safeUser = AuthService.createSafeUser(user);
      
      return res.status(201).json(safeUser);
    } catch (error) {
      console.error("Registration error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.get("/auth/me", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const user = await storage.getUser(req.session.userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Create safe user object (excluding password)
      const safeUser = AuthService.createSafeUser(user);
      
      return res.status(200).json(safeUser);
    } catch (error) {
      console.error("Auth/me error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.patch("/users/profile", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const user = await storage.getUser(req.session.userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Define validation schema for profile updates
      const updateProfileSchema = z.object({
        name: z.string().optional(),
        email: z.string().email().optional(),
        avatarUrl: z.string().optional().nullable(),
      });
      
      const result = updateProfileSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Update user profile
      const updatedUser = await storage.updateUser(req.session.userId, result.data);
      
      if (!updatedUser) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Create safe user object (excluding password)
      const safeUser = AuthService.createSafeUser(updatedUser);
      
      return res.status(200).json(safeUser);
    } catch (error) {
      console.error("Update profile error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.post("/users/change-password", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const user = await storage.getUser(req.session.userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      // Define validation schema for password change
      const changePasswordSchema = z.object({
        currentPassword: z.string(),
        newPassword: z.string().min(6, "Password must be at least 6 characters long"),
      });
      
      const result = changePasswordSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Verify current password
      const isPasswordValid = await AuthService.comparePassword(
        result.data.currentPassword, user.password
      );
      
      if (!isPasswordValid) {
        return res.status(400).json({ message: "Current password is incorrect" });
      }
      
      // Hash new password
      const hashedPassword = await AuthService.hashPassword(result.data.newPassword);
      
      // Update user with new password
      const updatedUser = await storage.updateUser(user.id, { password: hashedPassword });
      
      if (!updatedUser) {
        return res.status(404).json({ message: "User not found" });
      }
      
      return res.status(200).json({ message: "Password changed successfully" });
    } catch (error) {
      console.error("Change password error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.post("/auth/logout", (req: Request, res: Response) => {
    if (req.session) {
      req.session.destroy((err) => {
        if (err) {
          return res.status(500).json({ message: "Failed to logout" });
        }
        res.status(200).json({ message: "Logged out successfully" });
      });
    } else {
      res.status(200).json({ message: "Already logged out" });
    }
  });
  
  // Course routes
  api.get("/courses", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const courses = await storage.getCoursesByUserId(req.session.userId);
      return res.status(200).json(courses);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.get("/courses/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid course ID" });
      }
      
      const course = await storage.getCourse(id);
      
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      if (course.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      return res.status(200).json(course);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.post("/courses", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      // Handle both simple course creation and full publishing with structure and platforms
      const isPublishingRequest = req.body.structure && req.body.platforms;
      
      if (isPublishingRequest) {
        const publishSchema = z.object({
          title: z.string(),
          description: z.string(),
          category: z.string(),
          targetAudience: z.string().optional(),
          structure: z.object({
            modules: z.array(z.object({
              title: z.string(),
              description: z.string().optional(),
              lessons: z.array(z.object({
                title: z.string(),
                description: z.string()
              }))
            }))
          }),
          scripts: z.record(z.string(), z.record(z.string(), z.string())).optional(),
          platforms: z.array(z.string())
        });
        
        const result = publishSchema.safeParse(req.body);
        
        if (!result.success) {
          return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
        }
        
        // Create the course
        const course = await storage.createCourse({
          title: result.data.title,
          description: result.data.description,
          category: result.data.category,
          targetAudience: result.data.targetAudience || "General audience",
          status: "published",
          userId: req.session.userId
        });
        
        // Store course structure
        const moduleData = result.data.structure.modules.map((moduleItem: any, moduleIndex: number) => ({
          courseId: course.id,
          title: moduleItem.title,
          description: moduleItem.description || "",
          order: moduleIndex + 1 // Use 'order' instead of 'position' to match schema
        }));
        
        // Create lessons for each module directly since we don't have a separate modules table
        const modules = moduleData;
        
        // Store lessons for each module
        for (let i = 0; i < modules.length; i++) {
          const module = modules[i];
          const moduleLessons = result.data.structure.modules[i].lessons;
          
          await Promise.all(moduleLessons.map((lesson: any, lessonIndex: number) => {
            const lessonData = {
              courseId: course.id,
              title: lesson.title,
              description: lesson.description || null,
              script: result.data.scripts?.[`module-${i}`]?.[`lesson-${lessonIndex}`] || null,
              order: lessonIndex + 1
            };
            return storage.createLesson(lessonData);
          }));
        }
        
        // Track publishing platforms
        const platforms = result.data.platforms;
        const now = new Date();
        
        // Create publishing records for each platform
        if (platforms.length > 0) {
          await Promise.all(platforms.map(platform => {
            return storage.createPublishing({
              courseId: course.id,
              platform,
              status: "processing",
              publishedAt: now,
              analyticsData: {}
            });
          }));
          
          // Trigger async publishing process in background
          // This would be handled by a separate service in production
          setTimeout(() => {
            platforms.forEach(async platform => {
              try {
                // First get the publishing record for this course and platform
                const publishingRecords = await storage.getPublishingByCourseId(course.id);
                const publishing = publishingRecords.find(p => p.platform === platform);
                
                if (publishing) {
                  await storage.updatePublishing(publishing.id, {
                    status: "completed",
                    platformUrl: `https://example.com/${platform}/course/${course.id}`,
                    analyticsData: {
                      views: 0,
                      enrollments: 0,
                      rating: 0
                    }
                  });
                }
              } catch (error) {
                console.error(`Error publishing to ${platform}:`, error);
              }
            });
          }, 5000);
        }
        
        return res.status(201).json({
          course,
          modules: modules.map((module, moduleIndex) => ({
            ...module,
            lessons: result.data.structure.modules[moduleIndex].lessons
          })),
          publishingStatus: platforms.map(platform => ({
            platform,
            status: "processing",
            message: `Publishing to ${platform} in progress`
          }))
        });
      } else {
        // Handle regular course creation without publishing
        const result = insertCourseSchema
          .omit({ userId: true })
          .safeParse(req.body);
        
        if (!result.success) {
          return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
        }
        
        const course = await storage.createCourse({
          ...result.data,
          userId: req.session.userId
        });
        
        return res.status(201).json(course);
      }
    } catch (error) {
      console.error("Course creation/publishing error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.patch("/courses/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid course ID" });
      }
      
      const course = await storage.getCourse(id);
      
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      if (course.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      // Omit fields that shouldn't be updated directly
      const { id: _, userId: __, createdAt: ___, ...updateData } = req.body;
      
      const updatedCourse = await storage.updateCourse(id, updateData);
      return res.status(200).json(updatedCourse);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.delete("/courses/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const id = parseInt(req.params.id);
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid course ID" });
      }
      
      const course = await storage.getCourse(id);
      
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      if (course.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      await storage.deleteCourse(id);
      return res.status(200).json({ message: "Course deleted successfully" });
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Lesson routes
  api.get("/courses/:courseId/lessons", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const courseId = parseInt(req.params.courseId);
      if (isNaN(courseId)) {
        return res.status(400).json({ message: "Invalid course ID" });
      }
      
      const course = await storage.getCourse(courseId);
      
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      if (course.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      const lessons = await storage.getLessonsByCourseId(courseId);
      return res.status(200).json(lessons);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.post("/courses/:courseId/lessons", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const courseId = parseInt(req.params.courseId);
      if (isNaN(courseId)) {
        return res.status(400).json({ message: "Invalid course ID" });
      }
      
      const course = await storage.getCourse(courseId);
      
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      if (course.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      const result = insertLessonSchema
        .omit({ courseId: true })
        .safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      const lesson = await storage.createLesson({
        ...result.data,
        courseId
      });
      
      return res.status(201).json(lesson);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Get a single lesson
  api.get("/lessons/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const lessonId = parseInt(req.params.id);
      if (isNaN(lessonId)) {
        return res.status(400).json({ message: "Invalid lesson ID" });
      }
      
      const lesson = await storage.getLesson(lessonId);
      
      if (!lesson) {
        return res.status(404).json({ message: "Lesson not found" });
      }
      
      // Verify user has access to the course this lesson belongs to
      const course = await storage.getCourse(lesson.courseId);
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      if (course.userId !== req.session.userId) {
        // Check if user is a collaborator
        const collaborators = await storage.getCourseCollaborators(lesson.courseId);
        if (!collaborators.some(c => c.userId === req.session.userId)) {
          return res.status(403).json({ message: "Forbidden" });
        }
      }
      
      return res.status(200).json(lesson);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Update a lesson
  api.patch("/lessons/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const lessonId = parseInt(req.params.id);
      if (isNaN(lessonId)) {
        return res.status(400).json({ message: "Invalid lesson ID" });
      }
      
      const lesson = await storage.getLesson(lessonId);
      
      if (!lesson) {
        return res.status(404).json({ message: "Lesson not found" });
      }
      
      // Verify user has access to the course this lesson belongs to
      const course = await storage.getCourse(lesson.courseId);
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      // Check if user is owner or has edit permission
      let hasEditPermission = course.userId === req.session.userId;
      
      if (!hasEditPermission) {
        // Check if user is a collaborator with edit permission
        const collaborators = await storage.getCourseCollaborators(lesson.courseId);
        const collaborator = collaborators.find(c => c.userId === req.session.userId);
        hasEditPermission = collaborator?.canEdit || false;
      }
      
      if (!hasEditPermission) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      const updatedLesson = await storage.updateLesson(lessonId, req.body);
      
      return res.status(200).json(updatedLesson);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Delete a lesson
  api.delete("/lessons/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const lessonId = parseInt(req.params.id);
      if (isNaN(lessonId)) {
        return res.status(400).json({ message: "Invalid lesson ID" });
      }
      
      const lesson = await storage.getLesson(lessonId);
      
      if (!lesson) {
        return res.status(404).json({ message: "Lesson not found" });
      }
      
      // Verify user has access to the course this lesson belongs to
      const course = await storage.getCourse(lesson.courseId);
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      if (course.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: Only course owner can delete lessons" });
      }
      
      await storage.deleteLesson(lessonId);
      
      return res.status(200).json({ success: true });
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Update micro-learning settings for a lesson
  api.patch("/lessons/:id/micro-learning", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const lessonId = parseInt(req.params.id);
      if (isNaN(lessonId)) {
        return res.status(400).json({ message: "Invalid lesson ID" });
      }
      
      const lesson = await storage.getLesson(lessonId);
      
      if (!lesson) {
        return res.status(404).json({ message: "Lesson not found" });
      }
      
      // Verify user has access to the course this lesson belongs to
      const course = await storage.getCourse(lesson.courseId);
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      // Check if user is owner or has edit permission
      let hasEditPermission = course.userId === req.session.userId;
      
      if (!hasEditPermission) {
        // Check if user is a collaborator with edit permission
        const collaborators = await storage.getCourseCollaborators(lesson.courseId);
        const collaborator = collaborators.find(c => c.userId === req.session.userId);
        hasEditPermission = collaborator?.canEdit || false;
      }
      
      if (!hasEditPermission) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      // Validate micro-learning settings
      const { microLearningEnabled, microLearningSegmentCount, microLearningBreakInterval, microLearningBreakDuration } = req.body;
      
      const microLearningSettings: Partial<Lesson> = {};
      
      if (typeof microLearningEnabled === 'boolean') {
        microLearningSettings.microLearningEnabled = microLearningEnabled;
      }
      
      if (typeof microLearningSegmentCount === 'number' && microLearningSegmentCount > 0) {
        microLearningSettings.microLearningSegmentCount = microLearningSegmentCount;
      }
      
      if (typeof microLearningBreakInterval === 'number' && microLearningBreakInterval > 0) {
        microLearningSettings.microLearningBreakInterval = microLearningBreakInterval;
      }
      
      if (typeof microLearningBreakDuration === 'number' && microLearningBreakDuration > 0) {
        microLearningSettings.microLearningBreakDuration = microLearningBreakDuration;
      }
      
      const updatedLesson = await storage.updateLesson(lessonId, microLearningSettings);
      
      return res.status(200).json(updatedLesson);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Media library routes
  api.get("/media", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const media = await storage.getMediaByUserId(req.session.userId);
      return res.status(200).json(media);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Pexels API integration
  api.get("/pexels/photos", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const query = req.query.query as string;
      const page = parseInt(req.query.page as string) || 1;
      const perPage = parseInt(req.query.perPage as string) || 20;
      const orientation = req.query.orientation as 'landscape' | 'portrait' | 'square' | undefined;
      const size = req.query.size as 'large' | 'medium' | 'small' | undefined;
      
      if (!query) {
        return res.status(400).json({ message: "Search query is required" });
      }
      
      const results = await pexelsService.searchPhotos(
        query, 
        page, 
        perPage, 
        orientation, 
        size
      );
      
      // Transform Pexels data to standardized format for frontend
      const transformedResults = results.photos?.map((photo: any) => ({
        id: photo.id,
        title: photo.alt || `Photo by ${photo.photographer}`,
        url: photo.src.original,
        thumbnail: photo.src.medium,
        source: 'pexels',
        width: photo.width,
        height: photo.height,
        photographer: photo.photographer
      })) || [];
      
      console.log(`Pexels photos: Found ${transformedResults.length} results for "${query}"`);
      return res.status(200).json(transformedResults);
    } catch (error: any) {
      console.error("Pexels photos search error:", error);
      return res.status(500).json({ message: "Failed to search Pexels photos", error: error.message });
    }
  });
  
  api.get("/pexels/videos", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const query = req.query.query as string;
      const page = parseInt(req.query.page as string) || 1;
      const perPage = parseInt(req.query.perPage as string) || 20;
      const size = req.query.size as 'large' | 'medium' | 'small' | undefined;
      
      if (!query) {
        return res.status(400).json({ message: "Search query is required" });
      }
      
      const results = await pexelsService.searchVideos(
        query, 
        page,
        perPage,
        size
      );
      
      // Transform Pexels video data to standardized format
      const transformedResults = results.videos?.map((video: any) => ({
        id: video.id,
        title: `Video by ${video.user.name}`,
        url: video.video_files[0]?.link || video.url,
        thumbnail: video.image,
        source: 'pexels',
        width: video.width,
        height: video.height,
        duration: video.duration,
        user: video.user.name
      })) || [];
      
      console.log(`Pexels videos: Found ${transformedResults.length} results for "${query}"`);
      return res.status(200).json(transformedResults);
    } catch (error: any) {
      console.error("Pexels videos search error:", error);
      return res.status(500).json({ message: "Failed to search Pexels videos", error: error.message });
    }
  });
  
  // Import media from Pexels
  api.post("/pexels/import", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const importSchema = z.object({
        type: z.enum(["photo", "video"]),
        id: z.number(),
        mediaType: z.string(),
        name: z.string(),
        fileSize: z.number()
      });
      
      const result = importSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid import data", errors: result.error.format() });
      }
      
      const { type, id, mediaType, name, fileSize } = result.data;
      
      let importedMedia;
      let sourceData;
      let url;
      
      if (type === "photo") {
        const photo = await pexelsService.getPhoto(id);
        url = photo.src.original;
        sourceData = photo;
      } else {
        const video = await pexelsService.getVideo(id);
        // Get the highest quality video file
        const videoFile = video.video_files.reduce((highest: any, current: any) => {
          return !highest || (current.width || 0) > (highest.width || 0) ? current : highest;
        });
        url = videoFile.link;
        sourceData = video;
      }
      
      // Save the imported media to the database
      const media = await storage.createMedia({
        userId: req.session.userId,
        name,
        type: mediaType,
        mimeType: type === "photo" ? "image/jpeg" : "video/mp4",
        fileSize,
        url,
        originalFilename: name,
        source: "pexels",
        sourceId: id.toString(),
        sourceData
      });
      
      return res.status(201).json(media);
    } catch (error: any) {
      console.error("Pexels import error:", error);
      return res.status(500).json({ message: "Failed to import from Pexels", error: error.message });
    }
  });
  
  // Pixabay API routes
  api.get("/pixabay/photos", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const { query, page = "1", perPage = "20", orientation = "all", imageType = "all", category = "" } = req.query;
      
      if (!query) {
        return res.status(400).json({ message: "Query parameter is required" });
      }
      
      const results = await pixabayService.searchPhotos(
        query as string,
        parseInt(page as string),
        parseInt(perPage as string),
        orientation as string,
        imageType as string,
        category as string
      );
      
      // Transform Pixabay data to standardized format
      const transformedResults = results.hits?.map((photo: any) => ({
        id: photo.id,
        title: photo.tags || `Photo by ${photo.user}`,
        url: photo.largeImageURL,
        thumbnail: photo.webformatURL,
        source: 'pixabay',
        width: photo.imageWidth,
        height: photo.imageHeight,
        photographer: photo.user
      })) || [];
      
      console.log(`Pixabay photos: Found ${transformedResults.length} results for "${query}"`);
      return res.status(200).json(transformedResults);
    } catch (error) {
      console.error("Pixabay photos search error:", error);
      return res.status(500).json({ message: "Failed to search Pixabay photos", error: (error as Error).message });
    }
  });
  
  api.get("/pixabay/videos", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const { query, page = "1", perPage = "20", videoType = "all" } = req.query;
      
      if (!query) {
        return res.status(400).json({ message: "Query parameter is required" });
      }
      
      const results = await pixabayService.searchVideos(
        query as string,
        parseInt(page as string),
        parseInt(perPage as string),
        videoType as string
      );
      
      // Transform Pixabay video data to standardized format
      const transformedResults = results.hits?.map((video: any) => ({
        id: video.id,
        title: video.tags || `Video by ${video.user}`,
        url: video.videos?.large?.url || video.videos?.medium?.url || video.pageURL,
        thumbnail: video.picture_id,
        source: 'pixabay',
        width: video.videos?.large?.width || video.videos?.medium?.width,
        height: video.videos?.large?.height || video.videos?.medium?.height,
        duration: video.duration,
        user: video.user
      })) || [];
      
      console.log(`Pixabay videos: Found ${transformedResults.length} results for "${query}"`);
      return res.status(200).json(transformedResults);
    } catch (error) {
      console.error("Pixabay videos search error:", error);
      return res.status(500).json({ message: "Failed to search Pixabay videos", error: (error as Error).message });
    }
  });
  
  // Enhanced Stock Media API - Unified search for photos
  api.get("/stock/photos", async (req: Request, res: Response) => {
    try {
      const { query, page = 1, per_page = 20 } = req.query;
      
      if (!query || typeof query !== 'string') {
        return res.status(400).json({ error: 'Search query is required' });
      }

      const { stockMediaService } = await import('./services/stockMediaService');
      const results = await stockMediaService.searchPhotos(
        query,
        parseInt(page as string),
        parseInt(per_page as string)
      );

      console.log(`Stock photos: Found ${results.photos?.length || 0} results for "${query}"`);
      return res.json(results);
    } catch (error: any) {
      console.error('Error searching photos:', error);
      return res.status(500).json({ error: 'Failed to search photos' });
    }
  });

  // Enhanced Stock Media API - Unified search for videos
  api.get("/stock/videos", async (req: Request, res: Response) => {
    try {
      const { query, page = 1, per_page = 20 } = req.query;
      
      if (!query || typeof query !== 'string') {
        return res.status(400).json({ error: 'Search query is required' });
      }

      const { stockMediaService } = await import('./services/stockMediaService');
      const results = await stockMediaService.searchVideos(
        query,
        parseInt(page as string),
        parseInt(per_page as string)
      );

      console.log(`Stock videos: Found ${results.videos?.length || 0} results for "${query}"`);
      return res.json(results);
    } catch (error: any) {
      console.error('Error searching videos:', error);
      return res.status(500).json({ error: 'Failed to search videos' });
    }
  });

  // Enhanced Stock Media API - Import to library
  api.post("/stock/import", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const schema = z.object({
        mediaData: z.object({
          id: z.string(),
          url: z.string(),
          width: z.number().optional(),
          height: z.number().optional(),
          duration: z.number().optional(),
          alt: z.string().optional(),
          tags: z.array(z.string()).optional(),
          photographer: z.string().optional(),
          photographer_url: z.string().optional()
        }),
        type: z.enum(['photo', 'video'])
      });

      const validation = schema.safeParse(req.body);
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid data', 
          details: validation.error.format() 
        });
      }

      const { mediaData, type } = validation.data;
      const userId = req.session.userId;

      const { stockMediaService } = await import('./services/stockMediaService');
      const imported = await stockMediaService.importToLibrary(userId, mediaData, type);
      
      return res.json({ 
        success: true, 
        media: imported,
        message: `${type === 'photo' ? 'Photo' : 'Video'} imported to your library successfully` 
      });
    } catch (error: any) {
      console.error('Error importing media:', error);
      return res.status(500).json({ error: 'Failed to import media' });
    }
  });

  // Enhanced Stock Media API - Get trending media
  api.get("/stock/trending/:type", async (req: Request, res: Response) => {
    try {
      const { type } = req.params;
      const { limit = 20 } = req.query;

      if (type !== 'photo' && type !== 'video') {
        return res.status(400).json({ error: 'Type must be "photo" or "video"' });
      }

      const { stockMediaService } = await import('./services/stockMediaService');
      const results = await stockMediaService.getTrendingMedia(
        type, 
        parseInt(limit as string)
      );

      return res.json(results);
    } catch (error: any) {
      console.error('Error getting trending media:', error);
      return res.status(500).json({ error: 'Failed to get trending media' });
    }
  });

  // Enhanced Stock Media API - Get user's imported stock media
  api.get("/stock/imported", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      const { type } = req.query;
      const userId = req.session.userId;

      const { stockMediaService } = await import('./services/stockMediaService');
      const imported = await stockMediaService.getUserImportedMedia(
        userId, 
        type as 'photo' | 'video' | undefined
      );

      return res.json({ media: imported });
    } catch (error: any) {
      console.error('Error getting imported media:', error);
      return res.status(500).json({ error: 'Failed to get imported media' });
    }
  });

  // Enhanced Stock Media API - Get categories/suggestions
  api.get("/stock/categories", async (req: Request, res: Response) => {
    try {
      const categories = {
        photos: [
          'business', 'technology', 'education', 'nature', 'people', 'abstract',
          'workplace', 'teamwork', 'success', 'innovation', 'creativity',
          'presentation', 'meeting', 'computer', 'data', 'growth'
        ],
        videos: [
          'business', 'technology', 'nature', 'abstract', 'motion', 'presentation',
          'teamwork', 'workplace', 'animation', 'background', 'loop',
          'corporate', 'professional', 'modern', 'dynamic'
        ]
      };

      return res.json(categories);
    } catch (error: any) {
      console.error('Error getting categories:', error);
      return res.status(500).json({ error: 'Failed to get categories' });
    }
  });

  api.post("/pixabay/import", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      // Validate import data
      const importSchema = z.object({
        id: z.number(),
        type: z.enum(["photo", "video"]),
        title: z.string(),
        description: z.string().optional(),
      });
      
      const result = importSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid import data", errors: result.error.format() });
      }
      
      const { id, type, title, description } = result.data;
      let mediaUrl, thumbnailUrl, width, height;
      
      // Retrieve media details from Pixabay
      try {
        if (type === "photo") {
          const photo = await pixabayService.getPhoto(id);
          mediaUrl = photo.largeImageURL;
          thumbnailUrl = photo.webformatURL;
          width = photo.imageWidth;
          height = photo.imageHeight;
        } else {
          const video = await pixabayService.getVideo(id);
          // Get the highest quality video available
          mediaUrl = video.videos.large.url;
          thumbnailUrl = `https://i.vimeocdn.com/video/${video.picture_id}_640x360.jpg`;
          width = video.videos.large.width;
          height = video.videos.large.height;
        }
      } catch (error) {
        console.error("Pixabay API error:", error);
        return res.status(500).json({ message: "Failed to retrieve media from Pixabay" });
      }
      
      // Save media to the database
      try {
        const media = await storage.createMedia({
          userId: req.session.userId,
          title,
          description: description || "",
          type,
          source: "pixabay",
          sourceId: id.toString(),
          url: mediaUrl,
          thumbnailUrl,
          width,
          height,
          fileSize: 0, // We don't know the file size from Pixabay API
          createdAt: new Date()
        });
        
        return res.status(201).json(media);
      } catch (error) {
        console.error("Pixabay import error:", error);
        return res.status(500).json({ message: "Failed to import from Pixabay", error: (error as Error).message });
      }
    } catch (error) {
      console.error("Pixabay import error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Create route to get file by name
  api.get("/uploads/:filename", (req, res) => {
    const filePath = path.join(process.cwd(), 'uploads', req.params.filename);
    if (fs.existsSync(filePath)) {
      return res.sendFile(filePath);
    } else {
      return res.status(404).json({ message: "File not found" });
    }
  });
  
  // Regular media submission without file
  api.post("/media", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const result = insertMediaSchema
        .omit({ userId: true })
        .safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      const media = await storage.createMedia({
        ...result.data,
        userId: req.session.userId
      });
      
      return res.status(201).json(media);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Delete media file
  api.delete("/media/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const mediaId = parseInt(req.params.id);
      if (isNaN(mediaId)) {
        return res.status(400).json({ message: "Invalid media ID" });
      }
      
      // Get the media to verify ownership and check file path
      const media = await storage.getMedia(mediaId);
      
      if (!media) {
        return res.status(404).json({ message: "Media not found" });
      }
      
      if (media.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: You don't own this media" });
      }
      
      // Delete from storage
      const result = await storage.deleteMedia(mediaId);
      
      if (!result) {
        return res.status(500).json({ message: "Failed to delete media" });
      }
      
      // If it's a file in our uploads directory, try to delete the physical file
      if (media.url.includes('/uploads/')) {
        try {
          const filename = media.url.split('/uploads/').pop();
          if (filename) {
            const filePath = path.join(process.cwd(), 'uploads', filename);
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
            }
          }
        } catch (fileError) {
          console.error("Error deleting physical file:", fileError);
          // Continue even if physical file deletion fails
        }
      }
      
      return res.status(200).json({ message: "Media deleted successfully" });
    } catch (error) {
      console.error("Delete media error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // File upload media submission
  api.post("/media/upload", upload.single('file'), async (req: Request, res: Response) => {
    try {
      // For now, use a default user ID to fix the upload issue
      // In production, this should be properly authenticated
      const userId = req.session?.userId || 1;
      
      if (!req.file) {
        return res.status(400).json({ message: "No file provided" });
      }
      
      // Generate file type based on mimetype
      let fileType = "document";
      const mimetype = req.file.mimetype;
      
      if (mimetype.startsWith('image/')) {
        fileType = "image";
      } else if (mimetype.startsWith('audio/')) {
        fileType = "audio";
      } else if (mimetype === 'application/pdf') {
        fileType = "document";
      } else if (mimetype.includes('spreadsheet') || mimetype.includes('excel')) {
        fileType = "spreadsheet";
      } else if (mimetype.includes('wordprocessing') || mimetype.includes('msword')) {
        fileType = "document";
      } else if (mimetype.includes('tex') || mimetype.includes('mathml')) {
        fileType = "formula";
      } else {
        fileType = "other";
      }
      
      // Create public URL for the file
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      const fileUrl = `${baseUrl}/api/uploads/${req.file.filename}`;
      
      // Store file info in database
      const media = await storage.createMedia({
        userId: userId,
        name: req.body.name || req.file.originalname,
        type: fileType,
        mimeType: req.file.mimetype,
        fileSize: req.file.size,
        url: fileUrl,
        originalFilename: req.file.originalname,
        courseId: req.body.courseId ? parseInt(req.body.courseId) : undefined,
        lessonId: req.body.lessonId ? parseInt(req.body.lessonId) : undefined
      });
      
      // Update user's storage usage
      if (req.file.size) {
        try {
          const userStats = await storage.getUserStats(req.session.userId);
          if (userStats && userStats.storageUsed !== null) {
            const updatedStorageUsed = userStats.storageUsed + Math.ceil(req.file.size / 1024); // Convert bytes to KB
            await storage.updateUserStats(req.session.userId, { storageUsed: updatedStorageUsed });
          } else if (userStats) {
            // If storageUsed is null, initialize it with the current file size
            await storage.updateUserStats(req.session.userId, { storageUsed: Math.ceil(req.file.size / 1024) });
          }
        } catch (err) {
          console.error("Failed to update user storage stats", err);
        }
      }
      
      return res.status(201).json(media);
    } catch (error: unknown) {
      console.error("Error uploading file:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      return res.status(500).json({ message: "Server error", error: errorMessage });
    }
  });
  
  // Template routes
  api.get("/templates", async (req: Request, res: Response) => {
    try {
      const templates = await storage.getAllTemplates();
      return res.status(200).json(templates);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Get templates by type (course_template, course_generator, etc)
  api.get("/templates/type/:type", async (req: Request, res: Response) => {
    try {
      const { type } = req.params;
      if (!type) {
        return res.status(400).json({ message: "Template type is required" });
      }
      
      const templates = await storage.getAllTemplates();
      console.log("All templates:", templates); // Debug log
      const filteredTemplates = templates.filter(template => template.type === type);
      console.log("Filtered templates by type:", filteredTemplates); // Debug log
      
      return res.status(200).json(filteredTemplates);
    } catch (error) {
      console.error("Error getting templates by type:", error); // Log the actual error
      return res.status(500).json({ message: "Server error", error: error instanceof Error ? error.message : String(error) });
    }
  });
  
  // Get course templates by category
  api.get("/templates/category/:category", async (req: Request, res: Response) => {
    try {
      const { category } = req.params;
      if (!category) {
        return res.status(400).json({ message: "Category is required" });
      }
      
      const templates = await storage.getAllTemplates();
      const filteredTemplates = templates.filter(
        template => template.type === "course_template" && template.category === category
      );
      
      return res.status(200).json(filteredTemplates);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Template execution endpoint
  api.post("/templates/execute", async (req: Request, res: Response) => {
    try {
      const userId = req.session?.userId || 1; // Use placeholder for unauthenticated users
      
      const { templateId, name, prompt } = req.body;
      
      if (!templateId || !name || !prompt) {
        return res.status(400).json({ error: "Missing required fields" });
      }
      
      // Get template details
      const template = await storage.getTemplate(parseInt(templateId));
      
      if (!template) {
        return res.status(404).json({ error: "Template not found" });
      }
      
      // Get user stats for AI credits
      const stats = await storage.getUserStats(userId);
      
      let aiCreditsUsed = 0;
      let result = "";
      
      // Process the template based on type
      try {
        if (template.type === "course_generator") {
          // Import service here to avoid circular dependencies
          const geminiService = await import('./services/gemini');
          
          // Calculate AI credits - course generation is more expensive
          aiCreditsUsed = 25;
          
          if (stats && stats.aiCredits !== null && stats.aiCredits < aiCreditsUsed) {
            return res.status(403).json({ message: "Insufficient AI credits" });
          }
          
          const courseData = {
            title: name,
            description: prompt,
            targetAudience: "General audience", // Default value
            category: "General", // Default category
          };
          
          // Generate course structure
          const courseStructure = await geminiService.generateCourseStructure(courseData);
          result = JSON.stringify(courseStructure, null, 2);
        } 
        else if (template.type === "script_generator") {
          // Import service here to avoid circular dependencies
          const geminiService = await import('./services/gemini');
          
          // Calculate AI credits
          aiCreditsUsed = 10;
          
          if (stats && stats.aiCredits !== null && stats.aiCredits < aiCreditsUsed) {
            return res.status(403).json({ message: "Insufficient AI credits" });
          }
          
          const scriptData = {
            title: name,
            description: prompt,
            targetAudience: "General audience", // Default value
          };
          
          // Generate script
          result = await geminiService.generateScript(scriptData);
        } 
        else if (template.type === "voice_generator") {
          // Import service here to avoid circular dependencies
          const elevenLabsService = await import('./services/elevenlabs');
          
          // Calculate AI credits based on word count (approx 1 credit per 100 words)
          const wordCount = prompt.split(/\s+/).length;
          aiCreditsUsed = Math.max(1, Math.ceil(wordCount / 100));
          
          if (stats && stats.aiCredits !== null && stats.aiCredits < aiCreditsUsed) {
            return res.status(403).json({ message: "Insufficient AI credits" });
          }
          
          // For voice generation, we'll return a mock audio URL
          // In a real implementation, this would call the voice API and store the file
          const voiceId = "EXAVITQu4vr4xnSDxMaL"; // Default voice
          const filename = `voice_${Date.now()}.mp3`;
          
          // Generate voice
          const audioData = await elevenLabsService.textToSpeech(prompt, voiceId);
          
          if (!audioData) {
            return res.status(500).json({ error: "Failed to generate voice" });
          }
          
          // In production, would save this to a file and return URL
          result = `Text-to-speech conversion successful. ${wordCount} words processed using voice ID: ${voiceId}. Output: ${filename}`;
        }
        
        // Deduct AI credits
        if (stats && stats.aiCredits !== null) {
          await storage.updateUserStats(userId, {
            aiCredits: Math.max(0, stats.aiCredits - aiCreditsUsed)
          });
        }
        
        // Create template history entry
        const templateHistory = await storage.createTemplateHistory({
          userId,
          templateId: parseInt(templateId),
          name,
          prompt,
          result,
          aiCreditsUsed,
          favorited: false
        });
        
        return res.status(200).json({
          id: templateHistory.id,
          result,
          aiCreditsUsed
        });
      } catch (aiError: any) {
        console.error("AI processing error:", aiError);
        return res.status(500).json({ error: "AI processing failed", message: aiError.message });
      }
    } catch (error) {
      console.error("Template execution error:", error);
      return res.status(500).json({ error: "Failed to execute template" });
    }
  });
  
  // Template history routes
  api.get("/template-history", async (req: Request, res: Response) => {
    try {
      // Allow access even if not authenticated, just return empty array
      if (!req.session.userId) {
        return res.status(200).json([]);
      }
      
      const userId = req.session.userId;
      const history = await storage.getTemplateHistoryByUserId(userId);
      
      return res.status(200).json(history);
    } catch (error) {
      console.error("Error fetching template history:", error);
      return res.status(500).json({ error: "Failed to fetch template history" });
    }
  });
  
  api.post("/template-history", async (req: Request, res: Response) => {
    try {
      // Allow unauthenticated users to create template history entries
      // Use a placeholder userId of 1 if not authenticated
      const userId = req.session?.userId || 1;
      const { templateId, name, prompt, result, aiCreditsUsed } = req.body;
      
      if (!templateId || !name || !prompt || !result) {
        return res.status(400).json({ error: "Missing required fields" });
      }
      
      const templateHistory = await storage.createTemplateHistory({
        userId,
        templateId,
        name,
        prompt,
        result,
        aiCreditsUsed: aiCreditsUsed || 0,
        favorited: false
      });
      
      return res.status(201).json(templateHistory);
    } catch (error) {
      console.error("Error creating template history:", error);
      return res.status(500).json({ error: "Failed to create template history" });
    }
  });
  
  api.patch("/template-history/:id/favorite", async (req: Request, res: Response) => {
    try {
      // Allow favoriting for all users (even unauthenticated ones)
      const historyId = parseInt(req.params.id);
      const { favorited } = req.body;
      
      if (favorited === undefined) {
        return res.status(400).json({ error: "Missing favorited field" });
      }
      
      const updatedHistory = await storage.updateTemplateHistoryFavorite(historyId, favorited);
      
      if (!updatedHistory) {
        return res.status(404).json({ error: "Template history not found" });
      }
      
      return res.status(200).json(updatedHistory);
    } catch (error) {
      console.error("Error updating template history favorite status:", error);
      return res.status(500).json({ error: "Failed to update template history" });
    }
  });
  
  // Integration routes
  api.get("/integrations", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const integrations = await storage.getIntegrationsByUserId(req.session.userId);
      return res.status(200).json(integrations);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // User stats routes
  api.get("/user-stats", async (req: Request, res: Response) => {
    try {
      // If user is not authenticated, return default stats
      if (!req.session?.userId) {
        return res.status(200).json({
          id: 0,
          userId: 0,
          aiCredits: 50,
          activeCourses: 0,
          publishedCourses: 0,
          storageUsed: 0,
          storageLimit: 100
        });
      }
      
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats) {
        return res.status(200).json({
          id: 0,
          userId: req.session.userId,
          aiCredits: 50,
          activeCourses: 0,
          publishedCourses: 0,
          storageUsed: 0,
          storageLimit: 100
        });
      }
      
      return res.status(200).json(stats);
    } catch (error) {
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Analytics routes
  api.get("/analytics/user-engagement", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const engagementStats = await analyticsService.getUserEngagement(req.session.userId);
      
      return res.status(200).json(engagementStats);
    } catch (error) {
      console.error("Error getting user engagement stats:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.get("/analytics/course/:courseId", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const courseId = parseInt(req.params.courseId, 10);
      if (isNaN(courseId)) {
        return res.status(400).json({ message: "Invalid course ID" });
      }
      
      // Check if user has access to this course
      const course = await storage.getCourse(courseId);
      
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      // Check if user is owner or has view permission
      if (course.userId !== req.session.userId) {
        // Check if user is a collaborator
        const collaborators = await storage.getCourseCollaborators(courseId);
        if (!collaborators.some(c => c.userId === req.session.userId)) {
          return res.status(403).json({ message: "Forbidden" });
        }
      }
      
      const coursePerformance = await analyticsService.getCoursePerformance(courseId);
      
      return res.status(200).json(coursePerformance);
    } catch (error) {
      console.error("Error getting course performance:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.get("/analytics/popular-courses", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 5;
      
      if (isNaN(limit) || limit < 1) {
        return res.status(400).json({ message: "Invalid limit parameter" });
      }
      
      const popularCourses = await analyticsService.getPopularCourses(limit);
      
      return res.status(200).json(popularCourses);
    } catch (error) {
      console.error("Error getting popular courses:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.get("/analytics/published-courses", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      // Example implementation - replace with actual data from storage
      const publishedCourses = [
        {
          id: 1,
          title: "JavaScript Fundamentals",
          platforms: [
            {
              name: "youtube",
              status: "completed",
              url: "https://youtube.com/playlist/abc123",
              analytics: {
                views: 1245,
                likes: 87,
                comments: 32
              }
            },
            {
              name: "udemy",
              status: "completed",
              url: "https://udemy.com/course/js-fundamentals",
              analytics: {
                enrollments: 156,
                reviews: 24,
                rating: 4.7
              }
            }
          ],
          publishedAt: new Date().toISOString()
        },
        {
          id: 2,
          title: "Python for Data Science",
          platforms: [
            {
              name: "youtube",
              status: "processing",
              url: null,
              analytics: null
            },
            {
              name: "teachable",
              status: "completed",
              url: "https://yourschool.teachable.com/course/python",
              analytics: {
                enrollments: 89,
                reviews: 12,
                rating: 4.5
              }
            }
          ],
          publishedAt: new Date().toISOString()
        }
      ];
      
      return res.status(200).json(publishedCourses);
    } catch (error) {
      console.error("Error fetching published courses:", error);
      return res.status(500).json({ message: "Failed to fetch published courses" });
    }
  });

  api.get("/analytics/platform-performance", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      // Example implementation - replace with actual data from storage
      const platformPerformance = {
        youtube: {
          totalViews: 24500,
          totalSubscribers: 1250,
          averageViewDuration: 456, // in seconds
          topPerformingVideos: [
            { id: 1, title: "JavaScript Arrays Explained", views: 4520 },
            { id: 2, title: "Async/Await Tutorial", views: 3840 }
          ]
        },
        udemy: {
          totalEnrollments: 872,
          averageRating: 4.6,
          totalRevenue: 26160,
          topPerformingCourses: [
            { id: 1, title: "JavaScript Fundamentals", enrollments: 245 },
            { id: 2, title: "React for Beginners", enrollments: 176 }
          ]
        },
        kajabi: {
          totalEnrollments: 432,
          averageRating: 4.8,
          totalRevenue: 38880,
          topPerformingCourses: [
            { id: 1, title: "Advanced JavaScript Patterns", enrollments: 132 },
            { id: 2, title: "Complete Web Development Bootcamp", enrollments: 98 }
          ]
        },
        teachable: {
          totalEnrollments: 567,
          averageRating: 4.7,
          totalRevenue: 31880,
          topPerformingCourses: [
            { id: 1, title: "Python for Data Science", enrollments: 189 },
            { id: 2, title: "Machine Learning Basics", enrollments: 132 }
          ]
        }
      };
      
      return res.status(200).json(platformPerformance);
    } catch (error) {
      console.error("Error fetching platform performance analytics:", error);
      return res.status(500).json({ message: "Failed to fetch platform performance analytics" });
    }
  });
  
  api.post("/analytics/record-event", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const eventSchema = z.object({
        courseId: z.number().optional(),
        lessonId: z.number().optional(),
        eventType: z.string(),
        eventData: z.record(z.any()).optional(),
      });
      
      const result = eventSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Add user ID to the event
      const event: InsertAnalyticsEvent = {
        ...result.data,
        userId: req.session.userId,
        eventData: result.data.eventData || {},
        ipAddress: req.ip || '',
        userAgent: req.headers['user-agent'] || '',
      };
      
      const savedEvent = await analyticsService.recordEvent(event);
      
      return res.status(201).json(savedEvent);
    } catch (error) {
      console.error("Error recording analytics event:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // AI content generation routes
  api.post("/ai/generate-script", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const schema = z.object({
        title: z.string(),
        description: z.string(),
        targetAudience: z.string().optional(),
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Check AI credits
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits <= 0) {
        return res.status(403).json({ message: "Insufficient AI credits" });
      }
      
      try {
        // Generate script using Gemini
        const script = await geminiService.generateScript(result.data);
        
        // Deduct AI credits
        await storage.updateUserStats(req.session.userId, {
          aiCredits: stats.aiCredits !== null ? stats.aiCredits - 10 : 0
        });
        
        return res.status(200).json({ script });
      } catch (aiError: unknown) {
        console.error("Gemini API error:", aiError);
        return res.status(500).json({ message: "Failed to generate script with AI" });
      }
    } catch (error) {
      console.error("Server error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Generate script for a module (Traditional Course Creator)
  api.post("/ai/generate-module-script", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const schema = z.object({
        moduleId: z.string(),
        moduleTitle: z.string(),
        moduleDescription: z.string().optional(),
        lessonTitles: z.array(z.string())
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      const { moduleTitle, moduleDescription, lessonTitles } = result.data;
      
      // Check AI credits
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits <= 0) {
        return res.status(403).json({ message: "Insufficient AI credits" });
      }
      
      try {
        // Import service here to avoid circular dependencies
        const geminiService = await import('./services/gemini');
        
        // Generate script using Gemini
        const scriptData = {
          title: moduleTitle,
          description: `Module covering: ${lessonTitles.join(', ')}. ${moduleDescription || ''}`,
          targetAudience: "Course learners"
        };
        
        const script = await geminiService.generateScript(scriptData);
        
        // Deduct AI credits
        await storage.updateUserStats(req.session.userId, {
          aiCredits: stats.aiCredits !== null ? stats.aiCredits - 10 : 0
        });
        
        return res.status(200).json({ script });
      } catch (aiError: unknown) {
        console.error("Gemini API error:", aiError);
        return res.status(500).json({ message: "Failed to generate script with AI" });
      }
    } catch (error) {
      console.error("Server error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  // Generate script for a specific lesson within a module
  api.post("/ai/generate-lesson-script", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const schema = z.object({
        courseTitle: z.string(),
        courseDescription: z.string(),
        moduleTitle: z.string(),
        moduleDescription: z.string(),
        lessonTitle: z.string(),
        lessonDescription: z.string(),
        targetAudience: z.string(),
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Check AI credits
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits <= 0) {
        // Try to use the OpenAI fallback if no credits
        try {
          // Use the imported openAIFallbackService
const generateLessonScriptWithOpenAI = openAIFallbackService.generateLessonScriptWithOpenAI;
          
          const fallbackScript = await generateLessonScriptWithOpenAI(
            result.data.courseTitle,
            result.data.courseDescription,
            result.data.moduleTitle,
            result.data.moduleDescription,
            result.data.lessonTitle,
            result.data.lessonDescription,
            result.data.targetAudience
          );
          
          console.log("Using OpenAI fallback for lesson script due to insufficient credits");
          return res.status(200).json({ script: fallbackScript });
        } catch (fallbackError) {
          console.error("OpenAI fallback also failed:", fallbackError);
          return res.status(403).json({ message: "Insufficient AI credits and fallback generation failed" });
        }
      }
      
      try {
        // Generate lesson script using Gemini
        // We're reusing the same service but with more specific context
        const scriptData = {
          title: `${result.data.moduleTitle}: ${result.data.lessonTitle}`,
          description: `Course: ${result.data.courseTitle} - ${result.data.courseDescription}\nModule: ${result.data.moduleTitle} - ${result.data.moduleDescription}\nLesson: ${result.data.lessonTitle} - ${result.data.lessonDescription}`,
          targetAudience: result.data.targetAudience
        };
        
        const script = await geminiService.generateScript(scriptData);
        
        // Deduct AI credits
        await storage.updateUserStats(req.session.userId, {
          aiCredits: stats.aiCredits !== null ? stats.aiCredits - 10 : 0
        });
        
        return res.status(200).json({ script });
      } catch (aiError: unknown) {
        console.error("Gemini API error:", aiError);
        
        // Try to use the OpenAI fallback if Gemini fails
        try {
          // Use the imported openAIFallbackService
const generateLessonScriptWithOpenAI = openAIFallbackService.generateLessonScriptWithOpenAI;
          
          const fallbackScript = await generateLessonScriptWithOpenAI(
            result.data.courseTitle,
            result.data.courseDescription,
            result.data.moduleTitle,
            result.data.moduleDescription,
            result.data.lessonTitle,
            result.data.lessonDescription,
            result.data.targetAudience
          );
          
          console.log("Using OpenAI fallback for lesson script due to Gemini API failure");
          
          // Still deduct credits since we're using a different service
          await storage.updateUserStats(req.session.userId, {
            aiCredits: stats.aiCredits !== null ? stats.aiCredits - 10 : 0
          });
          
          return res.status(200).json({ script: fallbackScript });
        } catch (fallbackError) {
          console.error("OpenAI fallback also failed:", fallbackError);
          return res.status(500).json({ message: "Failed to generate lesson script with AI and fallback also failed" });
        }
      }
    } catch (error) {
      console.error("Server error:", error);
      
      // Try the OpenAI fallback for any server error
      try {
        // Use the imported openAIFallbackService
const generateLessonScriptWithOpenAI = openAIFallbackService.generateLessonScriptWithOpenAI;
        
        const fallbackScript = await generateLessonScriptWithOpenAI(
          req.body.courseTitle,
          req.body.courseDescription,
          req.body.moduleTitle,
          req.body.moduleDescription,
          req.body.lessonTitle,
          req.body.lessonDescription,
          req.body.targetAudience
        );
        
        console.log("Using OpenAI fallback for lesson script due to server error");
        return res.status(200).json({ script: fallbackScript });
      } catch (fallbackError) {
        console.error("OpenAI fallback also failed:", fallbackError);
        return res.status(500).json({ message: "Server error and fallback generation failed" });
      }
    }
  });
  
  api.post("/ai/generate-course-structure", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const schema = z.object({
        title: z.string(),
        description: z.string(),
        category: z.string(),
        targetAudience: z.string().optional(),
        keyTopics: z.string().optional(),
        contentNotes: z.string().optional()
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Check AI credits
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits <= 0) {
        // Try to use the OpenAI fallback if no credits
        try {
          // Use the imported openAIFallbackService
const generateCourseStructureWithOpenAI = openAIFallbackService.generateCourseStructureWithOpenAI;
          const fallbackStructure = await generateCourseStructureWithOpenAI(
            result.data.title,
            result.data.description,
            result.data.category
          );
          console.log("Using OpenAI fallback for course structure due to insufficient credits");
          return res.status(200).json(fallbackStructure);
        } catch (fallbackError) {
          console.error("OpenAI fallback also failed:", fallbackError);
          return res.status(403).json({ message: "Insufficient AI credits and fallback generation failed" });
        }
      }
      
      try {
        console.log("Generating course structure with data:", {
          title: result.data.title,
          category: result.data.category,
          keyTopicsProvided: result.data.keyTopics ? 'yes' : 'no',
          contentNotesProvided: result.data.contentNotes ? 'yes' : 'no'
        });
        
        // Generate course structure using Gemini
        const courseStructure = await geminiService.generateCourseStructure(result.data);
        
        // Deduct AI credits (use more credits if extra content is provided)
        const creditCost = (result.data.keyTopics || result.data.contentNotes) ? 30 : 25;
        await storage.updateUserStats(req.session.userId, {
          aiCredits: stats.aiCredits !== null ? Math.max(0, stats.aiCredits - creditCost) : 0
        });
        
        return res.status(200).json(courseStructure);
      } catch (aiError: unknown) {
        console.error("Gemini API error:", aiError);
        // Try to use the OpenAI fallback if Gemini fails
        try {
          // Use the imported openAIFallbackService
const generateCourseStructureWithOpenAI = openAIFallbackService.generateCourseStructureWithOpenAI;
          const fallbackStructure = await generateCourseStructureWithOpenAI(
            result.data.title,
            result.data.description,
            result.data.category
          );
          console.log("Using OpenAI fallback for course structure due to Gemini API failure");
          
          // Still deduct credits since we're using a different service
          const creditCost = (result.data.keyTopics || result.data.contentNotes) ? 30 : 25;
          await storage.updateUserStats(req.session.userId, {
            aiCredits: stats.aiCredits !== null ? Math.max(0, stats.aiCredits - creditCost) : 0
          });
          
          return res.status(200).json(fallbackStructure);
        } catch (fallbackError) {
          console.error("OpenAI fallback also failed:", fallbackError);
          return res.status(500).json({ message: "Failed to generate course structure with AI and fallback also failed" });
        }
      }
    } catch (error) {
      console.error("Server error:", error);
      // Try the OpenAI fallback for any server error
      try {
        // Use the imported openAIFallbackService
const generateCourseStructureWithOpenAI = openAIFallbackService.generateCourseStructureWithOpenAI;
        const fallbackStructure = await generateCourseStructureWithOpenAI(
          req.body.title,
          req.body.description,
          req.body.category
        );
        console.log("Using OpenAI fallback for course structure due to server error");
        return res.status(200).json(fallbackStructure);
      } catch (fallbackError) {
        console.error("OpenAI fallback also failed:", fallbackError);
        return res.status(500).json({ message: "Server error and fallback generation failed" });
      }
    }
  });
  
  api.post("/ai/generate-quiz", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const schema = z.object({
        courseId: z.number(),
        moduleId: z.number().optional(),
        lessonId: z.number().optional(),
        courseTitle: z.string(),
        courseDescription: z.string(),
        lessonScript: z.string().optional(),
        questionTypes: z.array(z.string()),
        numQuestions: z.number().default(10),
        difficulty: z.string().default("medium"),
        includeFlashcards: z.boolean().default(true),
        includeSummary: z.boolean().default(true)
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Check AI credits
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits < 25) {
        // Try to use the OpenAI fallback if no credits
        try {
          // Use the imported openAIFallbackService
const generateQuizWithOpenAI = openAIFallbackService.generateQuizWithOpenAI;
          
          const fallbackQuiz = await generateQuizWithOpenAI(
            result.data.courseTitle,
            result.data.courseDescription,
            result.data.lessonScript || "",
            result.data.numQuestions,
            result.data.includeFlashcards,
            result.data.includeSummary,
            result.data.difficulty
          );
          
          console.log("Using OpenAI fallback for quiz generation due to insufficient credits");
          return res.status(200).json(fallbackQuiz);
        } catch (fallbackError) {
          console.error("OpenAI fallback also failed:", fallbackError);
          return res.status(403).json({ 
            message: "Insufficient AI credits for quiz generation and fallback generation failed.",
            creditsAvailable: stats?.aiCredits || 0,
            creditsRequired: 25
          });
        }
      }
      
      try {
        console.log("Generating quiz with data:", {
          courseId: result.data.courseId,
          courseTitle: result.data.courseTitle,
          questionTypes: result.data.questionTypes,
          numQuestions: result.data.numQuestions,
          difficulty: result.data.difficulty
        });
        
        // Import quiz generator service
        const { generateQuiz } = require("./services/quizGenerator");
        
        // Generate quiz using OpenAI
        const generatedQuiz = await generateQuiz(result.data);
        
        // Deduct AI credits - cost: 25 credits
        await storage.updateUserStats(req.session.userId, {
          aiCredits: stats.aiCredits !== null ? Math.max(0, stats.aiCredits - 25) : 0
        });
        
        return res.status(200).json(generatedQuiz);
      } catch (aiError: unknown) {
        console.error("Quiz generation error:", aiError);
        
        // Try to use the OpenAI fallback for any errors
        try {
          // Use the imported openAIFallbackService
const generateQuizWithOpenAI = openAIFallbackService.generateQuizWithOpenAI;
          
          const fallbackQuiz = await generateQuizWithOpenAI(
            result.data.courseTitle,
            result.data.courseDescription,
            result.data.lessonScript || "",
            result.data.numQuestions,
            result.data.includeFlashcards,
            result.data.includeSummary,
            result.data.difficulty
          );
          
          console.log("Using OpenAI fallback for quiz generation due to primary service failure");
          
          // Still deduct credits since we're using a different service
          await storage.updateUserStats(req.session.userId, {
            aiCredits: stats.aiCredits !== null ? Math.max(0, stats.aiCredits - 25) : 0
          });
          
          return res.status(200).json(fallbackQuiz);
        } catch (fallbackError) {
          console.error("OpenAI fallback also failed:", fallbackError);
          return res.status(500).json({ 
            message: "Failed to generate quiz with AI and fallback also failed", 
            error: aiError instanceof Error ? aiError.message : "Unknown error" 
          });
        }
      }
    } catch (error) {
      console.error("Server error:", error);
      
      // Try the OpenAI fallback for any server error
      try {
        // Use the imported openAIFallbackService
const generateQuizWithOpenAI = openAIFallbackService.generateQuizWithOpenAI;
        
        const fallbackQuiz = await generateQuizWithOpenAI(
          req.body.courseTitle,
          req.body.courseDescription,
          req.body.lessonScript || "",
          req.body.numQuestions || 10,
          req.body.includeFlashcards !== false,
          req.body.includeSummary !== false,
          req.body.difficulty || "medium"
        );
        
        console.log("Using OpenAI fallback for quiz generation due to server error");
        return res.status(200).json(fallbackQuiz);
      } catch (fallbackError) {
        console.error("OpenAI fallback also failed:", fallbackError);
        return res.status(500).json({ message: "Server error and fallback generation failed" });
      }
    }
  });
  
  // Collaboration Routes: Teams
  api.get("/teams", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teams = await storage.getTeamsByUserId(req.session.userId);
      return res.status(200).json(teams);
    } catch (error) {
      console.error("Get teams error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.get("/teams/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.id);
      if (isNaN(teamId)) {
        return res.status(400).json({ message: "Invalid team ID" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Check if user is a member of this team
      const teamMembers = await storage.getTeamMembers(teamId);
      const isTeamMember = teamMembers.some(member => member.userId === req.session.userId);
      
      if (!isTeamMember && team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      return res.status(200).json(team);
    } catch (error) {
      console.error("Get team error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.post("/teams", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const result = insertTeamSchema
        .omit({ ownerId: true })
        .safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      const team = await storage.createTeam({
        ...result.data,
        ownerId: req.session.userId
      });
      
      return res.status(201).json(team);
    } catch (error) {
      console.error("Create team error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.patch("/teams/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.id);
      if (isNaN(teamId)) {
        return res.status(400).json({ message: "Invalid team ID" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Only the owner can update team details
      if (team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: Only team owner can update team details" });
      }
      
      // Determine which fields can be updated
      const { id: _, ownerId: __, createdAt: ___, ...updateData } = req.body;
      
      const updatedTeam = await storage.updateTeam(teamId, updateData);
      return res.status(200).json(updatedTeam);
    } catch (error) {
      console.error("Update team error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.delete("/teams/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.id);
      if (isNaN(teamId)) {
        return res.status(400).json({ message: "Invalid team ID" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Only the owner can delete a team
      if (team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: Only team owner can delete the team" });
      }
      
      await storage.deleteTeam(teamId);
      return res.status(200).json({ message: "Team deleted successfully" });
    } catch (error) {
      console.error("Delete team error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Team Members Routes
  api.get("/teams/:teamId/members", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.teamId);
      if (isNaN(teamId)) {
        return res.status(400).json({ message: "Invalid team ID" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Check if user is a member of this team
      const teamMembers = await storage.getTeamMembers(teamId);
      const isTeamMember = teamMembers.some(member => member.userId === req.session.userId);
      
      if (!isTeamMember && team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      return res.status(200).json(teamMembers);
    } catch (error) {
      console.error("Get team members error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.post("/teams/:teamId/members", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.teamId);
      if (isNaN(teamId)) {
        return res.status(400).json({ message: "Invalid team ID" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Check if user has admin rights
      const teamMembers = await storage.getTeamMembers(teamId);
      const userMember = teamMembers.find(member => member.userId === req.session.userId);
      
      if (!userMember && team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      if (userMember && userMember.role !== 'admin' && userMember.role !== 'owner' && team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: Only team admins or owners can add members" });
      }
      
      // Validate the member to add
      const memberSchema = z.object({
        userId: z.number(),
        role: z.string().optional()
      });
      
      const result = memberSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Confirm the user exists
      const userToAdd = await storage.getUser(result.data.userId);
      if (!userToAdd) {
        return res.status(404).json({ message: "User not found" });
      }
      
      const member = await storage.addTeamMember({
        teamId,
        userId: result.data.userId,
        role: result.data.role || "member",
        status: "active"
      });
      
      return res.status(201).json(member);
    } catch (error) {
      console.error("Add team member error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.patch("/teams/:teamId/members/:userId", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.teamId);
      const memberUserId = parseInt(req.params.userId);
      
      if (isNaN(teamId) || isNaN(memberUserId)) {
        return res.status(400).json({ message: "Invalid IDs" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Only the owner can update member roles
      if (team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: Only team owner can update member roles" });
      }
      
      // Validate the role
      const roleSchema = z.object({
        role: z.string()
      });
      
      const result = roleSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      const updatedMember = await storage.updateTeamMemberRole(teamId, memberUserId, result.data.role);
      
      if (!updatedMember) {
        return res.status(404).json({ message: "Team member not found" });
      }
      
      return res.status(200).json(updatedMember);
    } catch (error) {
      console.error("Update team member error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.delete("/teams/:teamId/members/:userId", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.teamId);
      const memberUserId = parseInt(req.params.userId);
      
      if (isNaN(teamId) || isNaN(memberUserId)) {
        return res.status(400).json({ message: "Invalid IDs" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Admins can remove members, owner can remove anyone
      const teamMembers = await storage.getTeamMembers(teamId);
      const userMember = teamMembers.find(member => member.userId === req.session.userId);
      
      // Special case: Users can remove themselves (leave team)
      if (memberUserId === req.session.userId) {
        // But owner cannot leave their own team
        if (team.ownerId === req.session.userId) {
          return res.status(400).json({ message: "Team owner cannot leave the team. Transfer ownership first." });
        }
        
        await storage.removeTeamMember(teamId, memberUserId);
        return res.status(200).json({ message: "Successfully left the team" });
      }
      
      // Otherwise, check permissions
      if (team.ownerId !== req.session.userId && (!userMember || userMember.role !== 'admin')) {
        return res.status(403).json({ message: "Forbidden: Only team owner or admins can remove members" });
      }
      
      // Admins cannot remove other admins or the owner
      if (userMember && userMember.role === 'admin') {
        const targetMember = teamMembers.find(member => member.userId === memberUserId);
        if (targetMember && (targetMember.role === 'admin' || targetMember.role === 'owner')) {
          return res.status(403).json({ message: "Forbidden: Admins cannot remove other admins or the owner" });
        }
      }
      
      const success = await storage.removeTeamMember(teamId, memberUserId);
      
      if (!success) {
        return res.status(404).json({ message: "Team member not found" });
      }
      
      return res.status(200).json({ message: "Team member removed successfully" });
    } catch (error) {
      console.error("Remove team member error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Course Collaborators Routes
  api.get("/courses/:courseId/collaborators", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const courseId = parseInt(req.params.courseId);
      if (isNaN(courseId)) {
        return res.status(400).json({ message: "Invalid course ID" });
      }
      
      const course = await storage.getCourse(courseId);
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      // Check if user is the course owner or a collaborator
      if (course.userId !== req.session.userId) {
        const collaborators = await storage.getCourseCollaborators(courseId);
        const isCollaborator = collaborators.some(collab => collab.userId === req.session.userId);
        
        if (!isCollaborator) {
          return res.status(403).json({ message: "Forbidden" });
        }
      }
      
      const collaborators = await storage.getCourseCollaborators(courseId);
      return res.status(200).json(collaborators);
    } catch (error) {
      console.error("Get course collaborators error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.post("/courses/:courseId/collaborators", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const courseId = parseInt(req.params.courseId);
      if (isNaN(courseId)) {
        return res.status(400).json({ message: "Invalid course ID" });
      }
      
      const course = await storage.getCourse(courseId);
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      // Only the course owner can add collaborators
      if (course.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: Only course owner can add collaborators" });
      }
      
      // Validate the collaborator to add
      const collaboratorSchema = z.object({
        userId: z.number(),
        role: z.string().optional(),
        canEdit: z.boolean().optional()
      });
      
      const result = collaboratorSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Confirm the user exists
      const userToAdd = await storage.getUser(result.data.userId);
      if (!userToAdd) {
        return res.status(404).json({ message: "User not found" });
      }
      
      const collaborator = await storage.addCourseCollaborator({
        courseId,
        userId: result.data.userId,
        role: result.data.role || "editor",
        canEdit: result.data.canEdit !== undefined ? result.data.canEdit : true,
        addedById: req.session.userId
      });
      
      return res.status(201).json(collaborator);
    } catch (error) {
      console.error("Add course collaborator error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.patch("/courses/:courseId/collaborators/:userId", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const courseId = parseInt(req.params.courseId);
      const collaboratorUserId = parseInt(req.params.userId);
      
      if (isNaN(courseId) || isNaN(collaboratorUserId)) {
        return res.status(400).json({ message: "Invalid IDs" });
      }
      
      const course = await storage.getCourse(courseId);
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      // Only the course owner can update collaborator permissions
      if (course.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: Only course owner can update collaborator permissions" });
      }
      
      // Validate the update
      const updateSchema = z.object({
        role: z.string().optional(),
        canEdit: z.boolean().optional()
      });
      
      const result = updateSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      const updatedCollaborator = await storage.updateCourseCollaborator(courseId, collaboratorUserId, result.data);
      
      if (!updatedCollaborator) {
        return res.status(404).json({ message: "Collaborator not found" });
      }
      
      return res.status(200).json(updatedCollaborator);
    } catch (error) {
      console.error("Update course collaborator error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.delete("/courses/:courseId/collaborators/:userId", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const courseId = parseInt(req.params.courseId);
      const collaboratorUserId = parseInt(req.params.userId);
      
      if (isNaN(courseId) || isNaN(collaboratorUserId)) {
        return res.status(400).json({ message: "Invalid IDs" });
      }
      
      const course = await storage.getCourse(courseId);
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      // Special case: A collaborator can remove themselves
      if (collaboratorUserId === req.session.userId) {
        await storage.removeCourseCollaborator(courseId, collaboratorUserId);
        return res.status(200).json({ message: "Successfully removed from course collaborators" });
      }
      
      // Otherwise, only course owner can remove collaborators
      if (course.userId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: Only course owner can remove collaborators" });
      }
      
      const success = await storage.removeCourseCollaborator(courseId, collaboratorUserId);
      
      if (!success) {
        return res.status(404).json({ message: "Collaborator not found" });
      }
      
      return res.status(200).json({ message: "Collaborator removed successfully" });
    } catch (error) {
      console.error("Remove course collaborator error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Team Courses Routes
  api.get("/teams/:teamId/courses", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.teamId);
      if (isNaN(teamId)) {
        return res.status(400).json({ message: "Invalid team ID" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Check if user is a member of this team
      const teamMembers = await storage.getTeamMembers(teamId);
      const isTeamMember = teamMembers.some(member => member.userId === req.session.userId);
      
      if (!isTeamMember && team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      const courses = await storage.getTeamCourses(teamId);
      return res.status(200).json(courses);
    } catch (error) {
      console.error("Get team courses error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.post("/teams/:teamId/courses", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.teamId);
      if (isNaN(teamId)) {
        return res.status(400).json({ message: "Invalid team ID" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Check if user has admin rights
      const teamMembers = await storage.getTeamMembers(teamId);
      const userMember = teamMembers.find(member => member.userId === req.session.userId);
      
      if (!userMember && team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden" });
      }
      
      if (userMember && userMember.role !== 'admin' && userMember.role !== 'owner' && team.ownerId !== req.session.userId) {
        return res.status(403).json({ message: "Forbidden: Only team admins or owners can add courses" });
      }
      
      // Validate the course to add
      const courseSchema = z.object({
        courseId: z.number()
      });
      
      const result = courseSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Confirm the course exists
      const courseToAdd = await storage.getCourse(result.data.courseId);
      if (!courseToAdd) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      // Check if the user is the course owner or a collaborator
      if (courseToAdd.userId !== req.session.userId) {
        const collaborators = await storage.getCourseCollaborators(result.data.courseId);
        const isCollaborator = collaborators.some(collab => collab.userId === req.session.userId);
        
        if (!isCollaborator) {
          return res.status(403).json({ message: "Forbidden: You must be the course owner or a collaborator to add it to a team" });
        }
      }
      
      const teamCourse = await storage.addCourseToTeam({
        teamId,
        courseId: result.data.courseId,
        addedById: req.session.userId
      });
      
      return res.status(201).json(teamCourse);
    } catch (error) {
      console.error("Add team course error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  api.delete("/teams/:teamId/courses/:courseId", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const teamId = parseInt(req.params.teamId);
      const courseId = parseInt(req.params.courseId);
      
      if (isNaN(teamId) || isNaN(courseId)) {
        return res.status(400).json({ message: "Invalid IDs" });
      }
      
      const team = await storage.getTeam(teamId);
      if (!team) {
        return res.status(404).json({ message: "Team not found" });
      }
      
      // Check user permissions
      if (team.ownerId !== req.session.userId) {
        const teamMembers = await storage.getTeamMembers(teamId);
        const userMember = teamMembers.find(member => member.userId === req.session.userId);
        
        if (!userMember || (userMember.role !== 'admin' && userMember.role !== 'owner')) {
          return res.status(403).json({ message: "Forbidden: Only team admins or owners can remove courses" });
        }
      }
      
      const course = await storage.getCourse(courseId);
      if (!course) {
        return res.status(404).json({ message: "Course not found" });
      }
      
      const success = await storage.removeCourseFromTeam(teamId, courseId);
      
      if (!success) {
        return res.status(404).json({ message: "Course not found in team" });
      }
      
      return res.status(200).json({ message: "Course removed from team successfully" });
    } catch (error) {
      console.error("Remove team course error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // My Collaborations Route - Get courses where the user is a collaborator
  api.get("/my-collaborations", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const collaboratedCourses = await storage.getUserCollaboratedCourses(req.session.userId);
      return res.status(200).json(collaboratedCourses);
    } catch (error) {
      console.error("Get user collaborations error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });

  api.post("/debug/generate-course-structure", async (req: Request, res: Response) => {
    try {
      const schema = z.object({
        title: z.string(),
        description: z.string(),
        category: z.string(),
        targetAudience: z.string().optional(),
        keyTopics: z.string().optional(),
        contentNotes: z.string().optional()
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      try {
        console.log("Debug: Generating course structure using Gemini with data:", {
          title: result.data.title,
          category: result.data.category,
          keyTopicsProvided: result.data.keyTopics ? 'yes' : 'no',
          contentNotesProvided: result.data.contentNotes ? 'yes' : 'no'
        });
        const courseStructure = await geminiService.generateCourseStructure(result.data);
        console.log("Debug: Course structure generated successfully");
        return res.status(200).json(courseStructure);
      } catch (aiError: unknown) {
        console.error("Debug Gemini API error:", aiError);
        return res.status(500).json({ 
          message: "Failed to generate course structure with AI",
          error: aiError instanceof Error ? aiError.message : String(aiError)
        });
      }
    } catch (error) {
      console.error("Debug server error:", error);
      return res.status(500).json({ 
        message: "Server error",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });
  
  // Text-to-speech for voiceovers
  api.post("/ai/text-to-speech", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const schema = z.object({
        text: z.string(),
        voiceId: z.string(),
        modelId: z.string().optional(),
        stability: z.number().optional(),
        similarityBoost: z.number().optional(),
        style: z.number().optional(),
        speakerBoost: z.boolean().optional(),
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Check AI credits
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits <= 0) {
        return res.status(403).json({ message: "Insufficient AI credits" });
      }
      
      try {
        // Generate speech using ElevenLabs API
        const speechResult = await ElevenlabsService.generateSpeech(req.session.userId, {
          text: result.data.text,
          voiceId: result.data.voiceId,
          modelId: result.data.modelId,
          stability: result.data.stability,
          similarityBoost: result.data.similarityBoost,
          style: result.data.style,
          speakerBoost: result.data.speakerBoost
        });
        
        // Deduct AI credits based on text length (1 credit per 100 words)
        const wordCount = result.data.text.split(/\s+/).length;
        const creditsToDeduct = Math.max(1, Math.ceil(wordCount / 100));
        
        await storage.updateUserStats(req.session.userId, {
          aiCredits: stats.aiCredits !== null ? Math.max(0, stats.aiCredits - creditsToDeduct) : 0
        });
        
        return res.status(200).json(speechResult);
      } catch (aiError: unknown) {
        console.error("Text-to-speech API error:", aiError);
        return res.status(500).json({ 
          message: "Failed to generate speech. Please check your API keys and try again.", 
          error: aiError instanceof Error ? aiError.message : String(aiError)
        });
      }
    } catch (error) {
      console.error("Server error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Get available voices from ElevenLabs
  api.get("/ai/voices", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const voices = await ElevenlabsService.getVoices();
      return res.status(200).json(voices);
    } catch (error) {
      console.error("Error fetching voices:", error);
      return res.status(500).json({ 
        message: "Failed to fetch voices. Please check your API keys and try again.",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });
  
  // Get available TTS models from ElevenLabs
  api.get("/ai/tts-models", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const models = await ElevenlabsService.getModels();
      return res.status(200).json(models);
    } catch (error) {
      console.error("Error fetching TTS models:", error);
      return res.status(500).json({ 
        message: "Failed to fetch TTS models. Please check your API keys and try again.",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });
  
  // Image analysis with Gemini Vision
  api.post("/ai/analyze-image", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const schema = z.object({
        imageUrl: z.string().url(),
        prompt: z.string(),
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Check AI credits
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits <= 0) {
        return res.status(403).json({ message: "Insufficient AI credits" });
      }
      
      try {
        // Analyze image using Gemini Vision
        const analysis = await geminiService.analyzeImage(result.data.imageUrl, result.data.prompt);
        
        // Deduct AI credits
        await storage.updateUserStats(req.session.userId, {
          aiCredits: stats.aiCredits !== null ? stats.aiCredits - 15 : 0
        });
        
        return res.status(200).json({ analysis });
      } catch (aiError: unknown) {
        console.error("Gemini Vision API error:", aiError);
        return res.status(500).json({ message: "Failed to analyze image with AI" });
      }
    } catch (error) {
      console.error("Server error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Image generation 
  api.post("/ai/generate-image", async (req: Request, res: Response) => {
    try {
      // Check if user is authenticated (using the same pattern as other routes)
      if (!req.isAuthenticated() || !req.user) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const userId = req.user?.id || req.user?.claims?.sub;
      
      const schema = z.object({
        prompt: z.string(),
        style: z.string().optional(),
        size: z.string().optional(),
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Check AI credits
      const stats = await storage.getUserStats(userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits <= 0) {
        return res.status(403).json({ message: "Insufficient AI credits" });
      }
      
      try {
        // Use OpenAI's DALL-E for image generation
        const { prompt, style, size } = result.data;
        
        // Import the OpenAI service
        const openaiService = await import('./services/openai');
        
        // Generate image using OpenAI DALL-E
        const imageResult = await openaiService.generateImage(prompt, size || "1024x1024");
        
        // Deduct AI credits (images cost more credits)
        await storage.updateUserStats(userId, {
          aiCredits: stats.aiCredits !== null ? stats.aiCredits - 50 : 0
        });
        
        return res.status(200).json({ imageUrl: imageResult.url });
      } catch (aiError: unknown) {
        console.error("Image generation API error:", aiError);
        
        // If OpenAI fails, return a more specific error
        if (aiError instanceof Error && aiError.message.includes("API")) {
          return res.status(503).json({ 
            message: "Image generation service temporarily unavailable",
            error: aiError.message
          });
        }
        
        return res.status(500).json({ message: "Failed to generate image" });
      }
    } catch (error) {
      console.error("Server error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // StabilityAI image generation
  api.post("/ai/stability/generate-image", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const schema = z.object({
        prompt: z.string(),
        negativePrompt: z.string().optional(),
        width: z.number().optional(),
        height: z.number().optional(),
        steps: z.number().optional(),
        cfgScale: z.number().optional(),
        seed: z.number().optional(),
        stylePreset: z.string().optional(),
        engineId: z.string().optional(),
        courseId: z.number().optional(),
        lessonId: z.number().optional(),
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Check AI credits
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits < 5) {
        return res.status(403).json({ message: "Insufficient AI credits (need at least 5 credits)" });
      }
      
      try {
        // Generate the image using Stability AI
        const generationResponse = await stabilityService.generateImage(result.data);
        
        // Create a media library entry for the generated image
        const media = await storage.createMedia({
          userId: req.session.userId,
          name: `AI Generated: ${result.data.prompt.substring(0, 30)}...`,
          type: "image",
          mimeType: "image/png",
          fileSize: 0, // We don't have this information from the StabilityAI API
          url: generationResponse.imageUrl,
          originalFilename: generationResponse.fileName,
          courseId: result.data.courseId || null,
          lessonId: result.data.lessonId || null,
          source: "stability-ai",
          sourceId: generationResponse.seed?.toString() || "",
          sourceData: {
            prompt: result.data.prompt,
            negativePrompt: result.data.negativePrompt,
            stylePreset: generationResponse.stylePreset,
            dimensions: `${generationResponse.width}x${generationResponse.height}`
          }
        });
        
        // Save the generated image in our database
        const aiImage = await storage.createAiGeneratedImage({
          userId: req.session.userId,
          prompt: result.data.prompt,
          negativePrompt: result.data.negativePrompt || null,
          imageUrl: generationResponse.imageUrl,
          fileName: generationResponse.fileName,
          width: generationResponse.width,
          height: generationResponse.height,
          seed: generationResponse.seed || null,
          stylePreset: generationResponse.stylePreset || null,
          engineId: result.data.engineId || null,
          mediaId: media.id,
          courseId: result.data.courseId || null,
          lessonId: result.data.lessonId || null
        });
        
        return res.status(200).json({
          ...aiImage,
          media
        });
      } catch (aiError: unknown) {
        console.error("Stability AI image generation error:", aiError);
        
        if (aiError instanceof Error) {
          return res.status(503).json({ 
            message: "Image generation service temporarily unavailable",
            error: aiError.message
          });
        }
        
        return res.status(500).json({ message: "Failed to generate image" });
      }
    } catch (error) {
      console.error("Server error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Get style presets for Stability AI
  api.get("/ai/stability/style-presets", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const stylePresets = await stabilityService.getStylePresets();
      return res.status(200).json(stylePresets);
    } catch (error) {
      console.error("Error fetching style presets:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Get user's AI-generated images
  api.get("/ai/images", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const images = await storage.getAiGeneratedImagesByUserId(req.session.userId);
      return res.status(200).json(images);
    } catch (error) {
      console.error("Error fetching AI images:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Video generation API
  api.post("/ai/generate-video", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const schema = z.object({
        script: z.string().min(50), // Ensure we have meaningful script content
        title: z.string().min(3),
        style: z.string().optional(),
        animationPreference: z.enum(['minimal', 'moderate', 'dynamic']).optional(),
        narrationVoice: z.string().optional(),
      });
      
      const result = schema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }
      
      // Check AI credits - video generation costs more
      const stats = await storage.getUserStats(req.session.userId);
      
      if (!stats || stats.aiCredits === null || stats.aiCredits < 200) {
        return res.status(403).json({ message: "Insufficient AI credits. Video generation requires at least 200 AI credits." });
      }
      
      try {
        // Import the OpenAI service
        const openaiService = await import('./services/openai');
        
        // Start video generation process
        const videoJob = await openaiService.startVideoGeneration({
          script: result.data.script,
          title: result.data.title,
          style: result.data.style,
          animationPreference: result.data.animationPreference,
          narrationVoice: result.data.narrationVoice,
        });
        
        // Deduct AI credits (video generation costs more)
        await storage.updateUserStats(req.session.userId, {
          aiCredits: stats.aiCredits !== null ? stats.aiCredits - 200 : 0
        });
        
        return res.status(202).json({
          id: videoJob.id,
          status: videoJob.status,
          estimatedCompletionTime: videoJob.estimatedCompletionTime,
          message: "Video generation started. Check the status endpoint for updates."
        });
      } catch (aiError: unknown) {
        console.error("Video generation API error:", aiError);
        
        if (aiError instanceof Error && aiError.message.includes("API")) {
          return res.status(503).json({ 
            message: "Video generation service temporarily unavailable",
            error: aiError.message
          });
        }
        
        return res.status(500).json({ message: "Failed to start video generation" });
      }
    } catch (error) {
      console.error("Server error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // Video status API endpoint
  api.get("/ai/video-status/:id", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const videoId = req.params.id;
      
      if (!videoId) {
        return res.status(400).json({ message: "Video ID is required" });
      }
      
      // Import the OpenAI service
      const openaiService = await import('./services/openai');
      
      // Get video job status
      const videoJob = openaiService.getVideoStatus(videoId);
      
      if (!videoJob) {
        return res.status(404).json({ message: "Video job not found" });
      }
      
      // Return the status
      return res.status(200).json({
        id: videoJob.id,
        title: videoJob.title,
        processingStatus: videoJob.status,
        thumbnailUrl: videoJob.thumbnailUrl,
        videoUrl: videoJob.videoUrl,
        duration: videoJob.duration,
        estimatedCompletionTime: videoJob.estimatedCompletionTime || Date.now() + 300000, // Ensure completion time is always set
        animationKeyframes: videoJob.animationKeyframes,
        error: videoJob.error
      });
    } catch (error) {
      console.error("Server error:", error);
      return res.status(500).json({ message: "Server error" });
    }
  });
  
  // AI Image Generation with Gemini API
  api.post("/ai/generate-image", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const { prompt } = req.body;
      
      if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
        return res.status(400).json({ message: "Prompt is required" });
      }

      // Import the Gemini service
      const geminiService = await import('./services/gemini');
      
      // Use Gemini to enhance the prompt for better image generation
      const enhancedPrompt = await geminiService.generateScript({
        title: "Image Generation",
        description: `Create a detailed, artistic image description based on this request: "${prompt}". Make it specific, vivid, and suitable for image generation. Focus on visual details, style, lighting, and composition. Keep it under 200 words.`
      });

      // Import OpenAI service for actual image generation
      const openaiService = await import('./services/openai');
      
      // Generate the actual image using OpenAI DALL-E with the enhanced prompt
      const imageResult = await openaiService.generateImage(
        enhancedPrompt || prompt, // Use enhanced prompt from Gemini, fallback to original
        "1024x1024"
      );

      // Create a media record for the generated image
      const mediaRecord = await storage.createMedia({
        userId: req.session.userId,
        name: `AI Generated: ${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : ''}`,
        type: 'image',
        url: imageResult.url,
        mimeType: 'image/png',
        fileSize: 1024000, // Approximate size
      });

      return res.status(200).json({
        success: true,
        message: "Image generated successfully",
        media: mediaRecord,
        enhancedPrompt: enhancedPrompt,
        originalPrompt: prompt
      });

    } catch (error) {
      console.error("AI image generation error:", error);
      return res.status(500).json({ 
        message: "Failed to generate image",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Payment routes
  api.post("/payments/create-checkout-session", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const { planId, billingInterval } = req.body;
      
      if (!planId || !billingInterval) {
        return res.status(400).json({ message: "Plan ID and billing interval are required" });
      }
      
      // Get user data
      const user = await storage.getUser(req.session.userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Get current host for success and cancel URLs
      const protocol = req.headers['x-forwarded-proto'] || 'http';
      const host = req.headers.host;
      const baseUrl = `${protocol}://${host}`;
      
      // Create checkout session using the enhanced stripe service
      // Add the plan ID to the success URL so it can be used to update the user's plan on success
      const successUrl = `${baseUrl}/payment-success?session_id={CHECKOUT_SESSION_ID}&plan=${planId}`;
      const cancelUrl = `${baseUrl}/pricing`;
      
      const checkoutSession = await stripeService.createCheckoutSession({
        planId,
        billingInterval,
        user,
        successUrl,
        cancelUrl
      });
      
      return res.status(200).json(checkoutSession);
    } catch (error: any) {
      console.error("Create checkout session error:", error);
      return res.status(500).json({ message: error.message || "Server error" });
    }
  });
  
  api.get("/payments/session-status", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const { sessionId } = req.query;
      
      if (!sessionId || typeof sessionId !== 'string') {
        return res.status(400).json({ message: "Session ID is required" });
      }
      
      // Get session details using the enhanced stripe service
      const session = await stripeService.getSessionInfo(sessionId);
      
      // Extract payment intent if available
      const paymentIntent = typeof session.payment_intent === 'object' ? session.payment_intent : null;
      
      return res.status(200).json({ 
        status: session.status, 
        subscription: session.subscription,
        client_secret: paymentIntent?.client_secret || null
      });
    } catch (error: any) {
      console.error("Get session status error:", error);
      return res.status(500).json({ message: error.message || "Server error" });
    }
  });
  
  api.post("/payments/create-portal-session", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      const { customerId } = req.body;
      
      if (!customerId) {
        return res.status(400).json({ message: "Customer ID is required" });
      }
      
      // Get current host for return URL
      const protocol = req.headers['x-forwarded-proto'] || 'http';
      const host = req.headers.host;
      const baseUrl = `${protocol}://${host}`;
      
      const url = await stripeService.createCustomerPortalSession({
        customerId,
        returnUrl: `${baseUrl}/profile`,
      });
      
      return res.status(200).json({ url });
    } catch (error: any) {
      console.error("Create portal session error:", error);
      return res.status(500).json({ message: error.message || "Server error" });
    }
  });
  
  // Route to create a Stripe setup intent for adding a payment method
  api.post("/billing/create-setup-intent", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const user = await storage.getUser(req.session.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Create a setup intent using the user's Stripe customer ID if available
      const setupIntent = await stripeService.createSetupIntent(user.stripeCustomerId);
      
      res.status(200).json(setupIntent);
    } catch (error: any) {
      console.error("Error creating setup intent:", error);
      res.status(500).json({ message: error.message || "Internal server error" });
    }
  });

  // Route to save a payment method to user's account
  api.post("/billing/payment-methods", async (req: Request, res: Response) => {
    try {
      const { paymentMethodId } = req.body;

      if (!paymentMethodId) {
        return res.status(400).json({ message: "Payment method ID is required" });
      }

      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const user = await storage.getUser(req.session.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      let customerId = user.stripeCustomerId;

      // If user doesn't have a Stripe customer ID, create one
      if (!customerId) {
        const customer = await stripeService.getOrCreateCustomer(
          user.email, 
          user.name || user.username
        );
        customerId = customer.id;
        
        // Save the customer ID to the user record
        await storage.updateUserStripeInfo(req.session.userId, { 
          customerId: customerId as string, 
          subscriptionId: '' // Empty string as placeholder for now
        });
      }

      // Attach the payment method to the customer
      await stripeService.attachPaymentMethodToCustomer(customerId, paymentMethodId);

      // Return success response
      res.status(200).json({ success: true });
    } catch (error: any) {
      console.error("Error saving payment method:", error);
      res.status(500).json({ message: error.message || "Internal server error" });
    }
  });

  // Route to get user's payment methods
  api.get("/billing/payment-methods", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const user = await storage.getUser(req.session.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // If user doesn't have a Stripe customer ID, they have no payment methods
      if (!user.stripeCustomerId) {
        return res.status(200).json([]);
      }

      // Get the customer's payment methods
      const paymentMethods = await stripeService.getCustomerPaymentMethods(user.stripeCustomerId);

      // Format the payment methods for the client
      const formattedPaymentMethods = paymentMethods.map((method: any) => {
        if (method.type !== 'card' || !method.card) {
          return null; // Skip non-card payment methods
        }

        return {
          id: method.id,
          userId: user.id,
          type: method.card.brand,
          lastFour: method.card.last4,
          expiryDate: `${method.card.exp_month}/${method.card.exp_year}`,
          isDefault: method.isDefault
        };
      }).filter(Boolean); // Remove any null entries

      res.status(200).json(formattedPaymentMethods);
    } catch (error: any) {
      console.error("Error fetching payment methods:", error);
      res.status(500).json({ message: error.message || "Internal server error" });
    }
  });

  // Route to get user's billing history
  api.get("/billing/history", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const user = await storage.getUser(req.session.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // If user doesn't have a Stripe customer ID, they have no billing history
      if (!user.stripeCustomerId) {
        return res.status(200).json([]);
      }

      // Get the customer's invoices
      const invoices = await stripeService.getCustomerInvoices(user.stripeCustomerId);

      // Format the invoices for the client
      const formattedInvoices = invoices.map((invoice: any, index: number) => {
        return {
          id: invoice.id,
          userId: user.id,
          amount: invoice.total / 100, // Convert from cents to dollars
          currency: invoice.currency,
          description: invoice.description || `Invoice #${invoice.number}`,
          status: invoice.status,
          transactionDate: new Date(invoice.created * 1000) // Convert from Unix timestamp
        };
      });

      res.status(200).json(formattedInvoices);
    } catch (error: any) {
      console.error("Error fetching billing history:", error);
      res.status(500).json({ message: error.message || "Internal server error" });
    }
  });

  // Route to cancel a subscription
  api.post("/billing/cancel-subscription", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const user = await storage.getUser(req.session.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // If user doesn't have a subscription, there's nothing to cancel
      if (!user.stripeSubscriptionId) {
        return res.status(400).json({ message: "No active subscription found" });
      }

      // Cancel the subscription
      const subscription = await stripeService.cancelSubscription(user.stripeSubscriptionId);

      res.status(200).json({ 
        success: true,
        cancelAt: new Date(subscription.cancel_at * 1000) // Convert from Unix timestamp
      });
    } catch (error: any) {
      console.error("Error canceling subscription:", error);
      res.status(500).json({ message: error.message || "Internal server error" });
    }
  });

  // Route to change subscription plan
  api.post("/billing/change-plan", async (req: Request, res: Response) => {
    try {
      const { planId } = req.body;

      if (!planId) {
        return res.status(400).json({ message: "Plan ID is required" });
      }

      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const user = await storage.getUser(req.session.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // For now, just update the user's plan without actual Stripe interaction
      await storage.updateUser(user.id, { plan: planId });

      res.status(200).json({ success: true });
    } catch (error: any) {
      console.error("Error changing subscription plan:", error);
      res.status(500).json({ message: error.message || "Internal server error" });
    }
  });
  
  // Stripe webhook handler
  // This needs to be raw body since we need to verify its signature
  // Test endpoint for OpenAI API
  api.get("/ai/test-openai", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }
      
      // Import the OpenAI service
      const openaiService = await import('./services/openai');
      
      // Test a simple completion to verify the API key works
      const prompt = "Explain the benefits of online learning in one paragraph";
      const completion = await openaiService.generateScript({
        title: "Online Learning",
        description: prompt
      });
      
      return res.status(200).json({ 
        success: true, 
        message: "OpenAI integration is working!",
        completion
      });
    } catch (error: any) {
      console.error("OpenAI test error:", error);
      return res.status(500).json({ 
        success: false, 
        message: "OpenAI integration test failed", 
        error: error.message 
      });
    }
  });

  // GAMIFICATION API ENDPOINTS
  
  // Get user's gamification progress summary
  api.get("/gamification/progress", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const { gamificationService } = await import('./services/gamification');
      const progressSummary = await gamificationService.getUserProgressSummary(req.session.userId);
      
      return res.status(200).json(progressSummary);
    } catch (error) {
      console.error("Error getting gamification progress:", error);
      return res.status(500).json({ message: "Failed to get gamification progress" });
    }
  });

  // Get all available badges
  api.get("/gamification/badges", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const { gamificationService } = await import('./services/gamification');
      const badges = await gamificationService.getAllBadges();
      
      return res.status(200).json(badges);
    } catch (error) {
      console.error("Error getting badges:", error);
      return res.status(500).json({ message: "Failed to get badges" });
    }
  });

  // Get user's earned badges
  api.get("/gamification/user-badges", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const { gamificationService } = await import('./services/gamification');
      const userBadges = await gamificationService.getUserBadges(req.session.userId);
      
      return res.status(200).json(userBadges);
    } catch (error) {
      console.error("Error getting user badges:", error);
      return res.status(500).json({ message: "Failed to get user badges" });
    }
  });

  // Record a learning activity (for XP and badge tracking)
  api.post("/gamification/activity", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const activitySchema = z.object({
        activityType: z.string(),
        courseId: z.number().optional(),
        lessonId: z.number().optional(),
        xpEarned: z.number().default(0),
        metadata: z.object({}).optional().default({})
      });

      const result = activitySchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
      }

      const { gamificationService } = await import('./services/gamification');
      const activity = await gamificationService.recordActivity({
        userId: req.session.userId,
        ...result.data
      });
      
      return res.status(200).json(activity);
    } catch (error) {
      console.error("Error recording activity:", error);
      return res.status(500).json({ message: "Failed to record activity" });
    }
  });

  // Get today's challenges
  api.get("/gamification/challenges/today", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const { gamificationService } = await import('./services/gamification');
      const challenges = await gamificationService.getTodaysChallenges();
      const userProgress = await gamificationService.getUserChallengeProgress(req.session.userId);
      
      // Combine challenges with user progress
      const challengesWithProgress = challenges.map(challenge => {
        const progress = userProgress.find(p => p.challengeId === challenge.id);
        return {
          ...challenge,
          userProgress: progress ? {
            progress: progress.progress,
            completed: progress.completed,
            completedAt: progress.completedAt
          } : {
            progress: 0,
            completed: false,
            completedAt: null
          }
        };
      });
      
      return res.status(200).json(challengesWithProgress);
    } catch (error) {
      console.error("Error getting today's challenges:", error);
      return res.status(500).json({ message: "Failed to get today's challenges" });
    }
  });

  // Get user's learning activities
  api.get("/gamification/activities", async (req: Request, res: Response) => {
    try {
      if (!req.session?.userId) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const limit = parseInt(req.query.limit as string) || 50;
      
      const { gamificationService } = await import('./services/gamification');
      const activities = await gamificationService.getUserActivities(req.session.userId, limit);
      
      return res.status(200).json(activities);
    } catch (error) {
      console.error("Error getting user activities:", error);
      return res.status(500).json({ message: "Failed to get user activities" });
    }
  });

  app.post("/webhook", express.raw({ type: 'application/json' }), async (req: Request, res: Response) => {
    try {
      // Get the webhook signature from the Stripe-Signature header
      const signature = req.headers['stripe-signature'] as string;
      
      if (!signature) {
        return res.status(400).json({ message: "Webhook signature missing" });
      }
      
      // Process the webhook event using our enhanced Stripe service
      const event = await stripeService.handleWebhookEvent(
        req.body, 
        signature, 
        async (eventType: string, data: any) => {
          // Custom webhook event handler
          switch (eventType) {
            case 'checkout.session.completed':
              const session = data;
              console.log(`Checkout session completed: ${session.id}`);
              
              // If this is a subscription, update the user's plan in the database
              if (session.subscription && session.client_reference_id) {
                const userId = parseInt(session.client_reference_id);
                if (!isNaN(userId)) {
                  // Update user subscription details in database
                  const user = await storage.getUser(userId);
                  if (user) {
                    // Update the user's plan based on the subscription
                    await storage.updateUser(userId, {
                      plan: session.metadata?.planId || 'starter',
                      stripeCustomerId: session.customer as string,
                      stripeSubscriptionId: session.subscription as string
                    });
                    console.log(`Updated user ${userId} with subscription ID: ${session.subscription}`);
                  }
                }
              }
              break;
              
            case 'invoice.paid':
              const invoice = data;
              console.log(`Invoice paid: ${invoice.id}`);
              
              // If customer and subscription are available, maybe update billing status
              if (invoice.customer && invoice.subscription) {
                console.log(`Customer ${invoice.customer} paid invoice for subscription ${invoice.subscription}`);
              }
              break;
              
            case 'invoice.payment_failed':
              const failedInvoice = data;
              console.log(`Payment failed for invoice: ${failedInvoice.id}`);
              
              // Could notify customer or mark the subscription as past_due
              break;
              
            case 'customer.subscription.updated':
              const subscription = data;
              console.log(`Subscription updated: ${subscription.id}`);
              
              // Update user subscription details based on the subscription status
              if (subscription.customer) {
                // Find user by Stripe customer ID
                // Then update their subscription status
                console.log(`Customer ${subscription.customer} updated subscription ${subscription.id}`);
              }
              break;
              
            case 'customer.subscription.deleted':
              const canceledSubscription = data;
              console.log(`Subscription canceled: ${canceledSubscription.id}`);
              
              // Find user with this subscription ID and update their plan
              if (canceledSubscription.customer) {
                // Update user plan to free or remove subscription details
                console.log(`Customer ${canceledSubscription.customer} canceled subscription ${canceledSubscription.id}`);
              }
              break;
              
            default:
              console.log(`Unhandled event type: ${eventType}`);
          }
        }
      );
      
      // Return success response
      return res.status(200).json({ received: true, type: event.type ? event.type : 'unknown' });
    } catch (error: any) {
      console.error("Webhook error:", error);
      return res.status(400).json({ message: error.message || "Webhook error" });
    }
  });
  
  const httpServer = createServer(app);
  return httpServer;
}
