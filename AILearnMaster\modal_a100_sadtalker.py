#!/usr/bin/env python3
"""
Modal A100 SadTalker Integration for Avatar Course Creation
High-performance talking head video generation using A100 80G GPU
"""

import os
import sys
import json
import base64
import subprocess
from typing import List, Dict, Any, Optional

# Modal configuration with SadTalker support
MODAL_TOKEN_ID = os.getenv('MODAL_TOKEN_ID')
MODAL_TOKEN_SECRET = os.getenv('MODAL_TOKEN_SECRET')

if not MODAL_TOKEN_ID or not MODAL_TOKEN_SECRET:
    print("ERROR: Modal credentials not found in environment")
    sys.exit(1)

# Configure Modal authentication
os.environ['MODAL_TOKEN_ID'] = MODAL_TOKEN_ID
os.environ['MODAL_TOKEN_SECRET'] = MODAL_TOKEN_SECRET

try:
    import modal
except ImportError:
    print("Installing Modal...")
    subprocess.run([sys.executable, "-m", "pip", "install", "modal"], check=True)
    import modal

# Create Modal app for Sad<PERSON>alker
app = modal.App("courseai-sadtalker-a100")

# A100 GPU image with Sad<PERSON>alker dependencies
sadtalker_image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install([
        "ffmpeg",
        "git",
        "git-lfs", 
        "wget",
        "curl",
        "build-essential",
        "cmake",
        "pkg-config",
        "libgtk-3-dev",
        "libavcodec-dev",
        "libavformat-dev",
        "libswscale-dev",
        "libv4l-dev",
        "libxvidcore-dev",
        "libx264-dev",
        "libjpeg-dev",
        "libpng-dev",
        "libtiff-dev",
        "gfortran",
        "openexr",
        "libatlas-base-dev",
        "libssl-dev",
        "libffi-dev"
    ])
    .pip_install([
        # Core ML stack for A100
        "torch>=2.1.0",
        "torchvision>=0.16.0", 
        "torchaudio>=2.1.0",
        "transformers>=4.35.0",
        "accelerate>=0.24.0",
        
        # SadTalker core dependencies
        "numpy>=1.23.4",
        "face-alignment>=1.3.5",
        "imageio>=2.19.3",
        "imageio-ffmpeg>=0.4.7",
        "librosa>=0.9.2",
        "numba>=0.56.0",
        "resampy>=0.3.1",
        "pydub>=0.25.1",
        "scipy>=1.10.1",
        "kornia>=0.6.8",
        "tqdm>=4.64.0",
        "yacs>=0.1.8",
        "pyyaml>=6.0",
        "joblib>=1.1.0",
        "scikit-image>=0.19.3",
        "basicsr>=1.4.2",
        "facexlib>=0.3.0",
        "gfpgan>=1.3.8",
        "av>=10.0.0",
        "safetensors>=0.3.0",
        
        # Additional processing
        "pillow>=10.0.0",
        "opencv-python>=4.8.0",
        "matplotlib>=3.6.0",
        "seaborn>=0.12.0",
        
        # API utilities
        "fastapi>=0.104.0",
        "uvicorn>=0.24.0",
        "pydantic>=2.4.0",
        "httpx>=0.25.0",
        "python-multipart>=0.0.6",
    ])
    .run_commands([
        "git lfs install",
        # Clone SadTalker repository
        "cd /tmp && git clone https://github.com/OpenTalker/SadTalker.git",
        "cd /tmp/SadTalker && pip install -r requirements.txt",
        # Download pre-trained models
        "cd /tmp/SadTalker && bash scripts/download_models.sh",
        # Install SadTalker
        "cd /tmp/SadTalker && pip install -e .",
        # Copy SadTalker to working directory
        "cp -r /tmp/SadTalker /opt/sadtalker",
    ])
    .env({"SADTALKER_CHECKPOINTS": "/opt/sadtalker/checkpoints"})
)

@app.function(
    image=sadtalker_image,
    gpu="A100:1",
    timeout=1800,  # 30 minutes for video generation
    memory=40960,  # 40GB RAM
    cpu=8,
)
def generate_talking_avatar_a100(
    source_image_b64: str,
    audio_data_b64: str,
    avatar_settings: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Generate talking avatar video using SadTalker on A100 GPU
    
    Args:
        source_image_b64: Base64 encoded source image (avatar face)
        audio_data_b64: Base64 encoded audio file (narration)
        avatar_settings: Configuration for avatar generation
    
    Returns:
        Dictionary with generated video data and metadata
    """
    import torch
    import sys
    import os
    import tempfile
    import shutil
    from pathlib import Path
    import base64
    import subprocess
    
    try:
        # A100 optimizations
        torch.backends.cudnn.benchmark = True
        torch.set_float32_matmul_precision('high')
        
        # Default settings
        settings = avatar_settings or {}
        pose_style = settings.get('pose_style', 0)
        expression_scale = settings.get('expression_scale', 1.0)
        still_mode = settings.get('still_mode', False)
        preprocess = settings.get('preprocess', 'crop')
        size = settings.get('size', 256)
        enhancer = settings.get('enhancer', 'gfpgan')
        
        # Create temporary workspace
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Save input files
            image_path = temp_path / "source_image.jpg"
            audio_path = temp_path / "audio.wav"
            output_dir = temp_path / "output"
            output_dir.mkdir(exist_ok=True)
            
            # Decode and save source image
            image_data = base64.b64decode(source_image_b64)
            with open(image_path, 'wb') as f:
                f.write(image_data)
            
            # Decode and save audio
            audio_data = base64.b64decode(audio_data_b64)
            with open(audio_path, 'wb') as f:
                f.write(audio_data)
            
            # Add SadTalker to Python path
            sys.path.insert(0, '/opt/sadtalker')
            
            # Import SadTalker modules
            from src.utils.preprocess import CropAndExtract
            from src.test_audio2coeff import Audio2Coeff
            from src.facerender.animate import AnimateFromCoeff
            from src.utils.init_path import init_path
            
            # Initialize SadTalker paths
            checkpoint_dir = '/opt/sadtalker/checkpoints'
            config_dir = '/opt/sadtalker/src/config'
            
            sadtalker_paths = init_path(
                checkpoint_dir, config_dir, size, False, preprocess
            )
            
            # Initialize models on GPU
            device = 'cuda'
            preprocess_model = CropAndExtract(sadtalker_paths, device)
            audio_to_coeff = Audio2Coeff(sadtalker_paths, device)
            animate_from_coeff = AnimateFromCoeff(sadtalker_paths, device)
            
            # Process source image
            first_frame_dir = output_dir / 'first_frame_dir'
            first_frame_dir.mkdir(exist_ok=True)
            
            print('Extracting 3DMM from source image...')
            first_coeff_path, crop_pic_path, crop_info = preprocess_model.generate(
                str(image_path), str(first_frame_dir), preprocess,
                source_image_flag=True, pic_size=size
            )
            
            if first_coeff_path is None:
                raise ValueError("Failed to extract coefficients from source image")
            
            # Process audio
            batch_size = 2 if size == 256 else 1
            
            print('Extracting coefficients from audio...')
            coeff_path = audio_to_coeff.generate(
                str(audio_path), str(first_frame_dir), str(first_coeff_path),
                still_mode, pose_style, expression_scale
            )
            
            # Generate talking head video
            print('Generating talking head video...')
            data = {
                'coeff_path': coeff_path,
                'crop_pic_path': crop_pic_path,
                'first_coeff_path': first_coeff_path,
                'crop_info': crop_info,
                'pic_path': str(image_path),
                'audio_path': str(audio_path),
                'batch_size': batch_size
            }
            
            video_path = animate_from_coeff.generate(
                data, str(output_dir), str(image_path), crop_info,
                enhancer=enhancer, background_enhancer=None, 
                preprocess=preprocess, img_size=size
            )
            
            # Read generated video
            if video_path and os.path.exists(video_path):
                with open(video_path, 'rb') as f:
                    video_data = f.read()
                
                video_b64 = base64.b64encode(video_data).decode()
                
                # Get video metadata
                video_size = len(video_data)
                
                # Get video duration using ffprobe
                try:
                    result = subprocess.run([
                        'ffprobe', '-v', 'quiet', '-show_entries',
                        'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1',
                        video_path
                    ], capture_output=True, text=True)
                    duration = float(result.stdout.strip()) if result.stdout.strip() else 0
                except:
                    duration = 0
                
                return {
                    "success": True,
                    "videoData": video_b64,
                    "format": "mp4",
                    "durationSeconds": duration,
                    "sizeBytes": video_size,
                    "resolution": f"{size}x{size}",
                    "enhancer": enhancer,
                    "pose_style": pose_style,
                    "expression_scale": expression_scale
                }
            else:
                raise ValueError("Video generation failed - no output file created")
                
    except Exception as e:
        print(f"SadTalker generation error: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@app.function(
    image=sadtalker_image,
    gpu="A100:1",
    timeout=3600,  # 1 hour for batch processing
    memory=65536,  # 64GB RAM for batch
    cpu=12,
)
def batch_avatar_course_generation_a100(
    course_data: Dict[str, Any],
    avatar_image_b64: str,
    avatar_settings: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Generate complete avatar course with multiple lessons
    
    Args:
        course_data: Course structure with lessons and audio
        avatar_image_b64: Base64 encoded avatar image
        avatar_settings: Avatar generation settings
    
    Returns:
        Complete course with avatar videos for each lesson
    """
    import torch
    
    try:
        # A100 optimizations for batch processing
        torch.backends.cudnn.benchmark = True
        torch.set_float32_matmul_precision('high')
        
        course_title = course_data.get('title', 'Avatar Course')
        modules = course_data.get('modules', [])
        
        results = {
            "courseTitle": course_title,
            "totalModules": len(modules),
            "modules": [],
            "totalDuration": 0,
            "totalSize": 0
        }
        
        for module_idx, module in enumerate(modules):
            module_title = module.get('title', f'Module {module_idx + 1}')
            lessons = module.get('lessons', [])
            
            module_result = {
                "moduleTitle": module_title,
                "moduleId": module.get('id', f'module-{module_idx}'),
                "lessons": [],
                "moduleDuration": 0,
                "moduleSize": 0
            }
            
            for lesson_idx, lesson in enumerate(lessons):
                lesson_title = lesson.get('title', f'Lesson {lesson_idx + 1}')
                audio_b64 = lesson.get('audioData', '')
                
                if not audio_b64:
                    print(f"Skipping {lesson_title} - no audio data")
                    continue
                
                print(f"Generating avatar video for: {lesson_title}")
                
                # Generate avatar video for this lesson
                video_result = generate_talking_avatar_a100.remote(
                    avatar_image_b64, audio_b64, avatar_settings
                )
                
                if video_result.get("success"):
                    lesson_result = {
                        "lessonTitle": lesson_title,
                        "lessonId": lesson.get('id', f'lesson-{lesson_idx}'),
                        "videoData": video_result["videoData"],
                        "format": video_result["format"],
                        "duration": video_result["durationSeconds"],
                        "size": video_result["sizeBytes"],
                        "resolution": video_result["resolution"]
                    }
                    
                    module_result["lessons"].append(lesson_result)
                    module_result["moduleDuration"] += video_result["durationSeconds"]
                    module_result["moduleSize"] += video_result["sizeBytes"]
                else:
                    print(f"Failed to generate video for {lesson_title}: {video_result.get('error')}")
            
            if module_result["lessons"]:
                results["modules"].append(module_result)
                results["totalDuration"] += module_result["moduleDuration"]
                results["totalSize"] += module_result["moduleSize"]
        
        return results
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "courseTitle": course_data.get('title', 'Unknown Course')
        }

@app.function(
    image=sadtalker_image,
    gpu="A100:1",
    timeout=1200,  # 20 minutes
    memory=24576,  # 24GB RAM
    cpu=6,
)
def custom_avatar_training_a100(
    training_images_b64: List[str],
    training_videos_b64: List[str] = None,
    avatar_name: str = "custom_avatar"
) -> Dict[str, Any]:
    """
    Train custom avatar model using user-provided images/videos
    
    Args:
        training_images_b64: List of base64 encoded training images
        training_videos_b64: Optional training videos for better results
        avatar_name: Name for the custom avatar
    
    Returns:
        Custom avatar model information and usage instructions
    """
    import torch
    import tempfile
    import base64
    from pathlib import Path
    
    try:
        # A100 optimizations
        torch.backends.cudnn.benchmark = True
        
        if len(training_images_b64) < 5:
            return {
                "success": False,
                "error": "At least 5 training images required for custom avatar"
            }
        
        # Create temporary workspace
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            training_dir = temp_path / "training_data"
            training_dir.mkdir(exist_ok=True)
            
            # Save training images
            for idx, img_b64 in enumerate(training_images_b64[:20]):  # Limit to 20 images
                img_data = base64.b64decode(img_b64)
                img_path = training_dir / f"image_{idx:03d}.jpg"
                with open(img_path, 'wb') as f:
                    f.write(img_data)
            
            # Save training videos if provided
            if training_videos_b64:
                for idx, vid_b64 in enumerate(training_videos_b64[:5]):  # Limit to 5 videos
                    vid_data = base64.b64decode(vid_b64)
                    vid_path = training_dir / f"video_{idx:03d}.mp4"
                    with open(vid_path, 'wb') as f:
                        f.write(vid_data)
            
            # For now, return configuration for using images with SadTalker
            # Full training would require more extensive setup
            
            return {
                "success": True,
                "avatarName": avatar_name,
                "trainingImages": len(training_images_b64),
                "trainingVideos": len(training_videos_b64) if training_videos_b64 else 0,
                "status": "ready_for_generation",
                "usage": "Use the best quality training image as source for avatar generation",
                "recommendations": [
                    "Use high-resolution frontal face images",
                    "Ensure consistent lighting across training images", 
                    "Include various expressions for better results",
                    "Videos should show natural head movements"
                ]
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "avatarName": avatar_name
        }

@app.function(image=sadtalker_image, timeout=300)
def health_check_sadtalker():
    """Health check for SadTalker A100 setup"""
    import torch
    import os
    
    health_info = {
        "timestamp": __import__("datetime").datetime.now().isoformat(),
        "service": "SadTalker-A100",
        "gpu_available": torch.cuda.is_available(),
        "sadtalker_installed": os.path.exists('/opt/sadtalker'),
        "checkpoints_available": os.path.exists('/opt/sadtalker/checkpoints')
    }
    
    if torch.cuda.is_available():
        health_info.update({
            "gpu_name": torch.cuda.get_device_name(0),
            "gpu_memory_total": torch.cuda.get_device_properties(0).total_memory,
            "cuda_version": torch.version.cuda,
            "pytorch_version": torch.__version__
        })
    
    # Check SadTalker models
    if os.path.exists('/opt/sadtalker/checkpoints'):
        checkpoints = os.listdir('/opt/sadtalker/checkpoints')
        health_info["available_models"] = len(checkpoints)
        health_info["model_files"] = checkpoints[:5]  # List first 5 models
    
    return health_info

# Web API endpoints for Course AI Platform integration
@app.function(image=sadtalker_image, timeout=1800)
@modal.web_endpoint(method="POST")
def api_generate_avatar_video(item):
    """REST API for avatar video generation"""
    try:
        source_image = item.get("source_image_b64", "")
        audio_data = item.get("audio_data_b64", "")
        settings = item.get("avatar_settings", {})
        
        if not source_image or not audio_data:
            return {"error": "Both source_image_b64 and audio_data_b64 are required"}
        
        result = generate_talking_avatar_a100.remote(source_image, audio_data, settings)
        
        return result
        
    except Exception as e:
        return {"error": str(e)}

@app.function(image=sadtalker_image, timeout=3600)
@modal.web_endpoint(method="POST") 
def api_generate_avatar_course(item):
    """REST API for complete avatar course generation"""
    try:
        course_data = item.get("course_data", {})
        avatar_image = item.get("avatar_image_b64", "")
        settings = item.get("avatar_settings", {})
        
        if not course_data or not avatar_image:
            return {"error": "Both course_data and avatar_image_b64 are required"}
        
        result = batch_avatar_course_generation_a100.remote(course_data, avatar_image, settings)
        
        return result
        
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    print("Modal SadTalker A100 Application")
    print("Ready for avatar course generation")
    
    # Test basic functionality
    try:
        with app.run():
            print("Testing SadTalker A100 setup...")
            
            # Health check
            health = health_check_sadtalker.remote()
            print(f"Health status: {health}")
            
            if health.get("gpu_available") and health.get("sadtalker_installed"):
                print("SadTalker A100 is ready for avatar generation")
            else:
                print("Warning: SadTalker setup incomplete")
                
    except Exception as e:
        print(f"Initialization test failed: {e}")
        print("App created but requires manual deployment")