"""
Modal A100 Marp Slide Generation Service
High-performance slide deck creation from course scripts using AI and Marp
Integrated with Course AI Platform for Traditional and Avatar workflows
"""

import modal
import os
import base64
import json
import re
import time
from typing import Dict, List, Any, Optional, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Modal app for slide generation
app = modal.App("courseai-marp-slidegen")

# Define comprehensive image with Marp, Node.js, and AI dependencies
slide_image = modal.Image.debian_slim().apt_install([
    "nodejs",
    "npm", 
    "git",
    "curl",
    "wget",
    "chromium",
    "fonts-liberation",
    "fonts-dejavu-core",
    "fontconfig",
    "imagemagick",
    "pdftk",
    "python3-pip"
]).run_commands([
    # Install Marp CLI and dependencies
    "npm install -g @marp-team/marp-cli",
    "npm install -g @marp-team/marp-core",
    "npm install -g puppeteer",
    
    # Install additional fonts for better presentation quality
    "fc-cache -fv",
    
    # Set up Chrome/Chromium for Marp rendering
    "export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true",
    "export PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium"
]).pip_install([
    # AI and text processing
    "openai>=1.0.0",
    "anthropic>=0.5.0",
    "transformers>=4.30.0",
    "torch>=2.0.0",
    "sentence-transformers>=2.2.0",
    
    # Text processing and analysis
    "nltk>=3.8.0",
    "spacy>=3.6.0",
    "textstat>=0.7.0",
    "markdown>=3.4.0",
    "beautifulsoup4>=4.12.0",
    
    # Image and media processing
    "Pillow>=9.5.0",
    "opencv-python>=4.7.0",
    "moviepy>=1.0.3",
    
    # Utilities
    "requests>=2.30.0",
    "pydantic>=2.0.0",
    "jinja2>=3.1.0",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0"
]).run_commands([
    # Download NLTK data
    "python3 -c 'import nltk; nltk.download(\"punkt\"); nltk.download(\"stopwords\"); nltk.download(\"vader_lexicon\")'",
    
    # Download spaCy model
    "python3 -m spacy download en_core_web_sm"
])

# GPU configuration for AI processing
A100_GPU_CONFIG = modal.gpu.A100(count=1, memory=80)

# Cost optimization settings
MAX_CONCURRENT_REQUESTS = 2
IDLE_TIMEOUT = 600  # 10 minutes for slide generation
KEEP_WARM = 0

@app.function(
    image=slide_image,
    gpu=A100_GPU_CONFIG,
    timeout=1200,  # 20 minutes for complex presentations
    concurrency_limit=MAX_CONCURRENT_REQUESTS,
    keep_warm=KEEP_WARM,
    container_idle_timeout=IDLE_TIMEOUT
)
def generate_course_slides(
    script_content: str,
    course_title: str,
    lesson_title: str = "",
    style_theme: str = "default",
    slide_count_target: int = 10,
    include_animations: bool = True,
    custom_branding: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """
    Generate comprehensive slide deck from course script using AI analysis and Marp
    
    Args:
        script_content: Full lesson/course script text
        course_title: Title of the course
        lesson_title: Specific lesson title (optional)
        style_theme: Marp theme (default, gaia, uncover, etc.)
        slide_count_target: Target number of slides to generate
        include_animations: Whether to include slide animations
        custom_branding: Optional branding elements (logo, colors, etc.)
    
    Returns:
        Dictionary with generated slides, Marp markdown, and metadata
    """
    import torch
    import openai
    import nltk
    import spacy
    import re
    import subprocess
    import tempfile
    from pathlib import Path
    import json
    import time
    
    start_time = time.time()
    
    try:
        logger.info(f"Starting slide generation for: {course_title} - {lesson_title}")
        
        # Initialize AI models and processors
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        nlp = spacy.load("en_core_web_sm")
        
        # Step 1: Analyze script content with AI
        script_analysis = analyze_script_content(script_content, nlp, device)
        
        # Step 2: Generate slide structure using AI
        slide_structure = generate_slide_structure(
            script_analysis, 
            course_title, 
            lesson_title, 
            slide_count_target
        )
        
        # Step 3: Create detailed slide content
        detailed_slides = create_detailed_slides(slide_structure, script_analysis)
        
        # Step 4: Generate Marp markdown
        marp_markdown = generate_marp_markdown(
            detailed_slides, 
            style_theme, 
            include_animations, 
            custom_branding
        )
        
        # Step 5: Render slides to multiple formats
        rendered_outputs = render_slides_multiple_formats(marp_markdown, lesson_title or course_title)
        
        # Step 6: Generate slide thumbnails and previews
        slide_previews = generate_slide_previews(rendered_outputs["html_path"])
        
        generation_time = time.time() - start_time
        
        return {
            "success": True,
            "slide_data": {
                "marp_markdown": marp_markdown,
                "slide_structure": detailed_slides,
                "slide_count": len(detailed_slides),
                "analysis": script_analysis,
                "theme": style_theme
            },
            "rendered_outputs": {
                "html_base64": rendered_outputs["html_base64"],
                "pdf_base64": rendered_outputs["pdf_base64"],
                "pptx_base64": rendered_outputs.get("pptx_base64"),
                "slides_png": slide_previews
            },
            "metadata": {
                "course_title": course_title,
                "lesson_title": lesson_title,
                "generation_time_seconds": round(generation_time, 3),
                "script_length": len(script_content),
                "target_slide_count": slide_count_target,
                "actual_slide_count": len(detailed_slides),
                "theme_used": style_theme,
                "gpu_used": device.type == "cuda",
                "timestamp": time.time()
            }
        }
        
    except Exception as e:
        logger.error(f"Slide generation failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "generation_time_seconds": round(time.time() - start_time, 3),
            "timestamp": time.time()
        }

def analyze_script_content(script_content: str, nlp, device) -> Dict[str, Any]:
    """Analyze script content to extract key themes, concepts, and structure"""
    import textstat
    from collections import Counter
    
    # Basic text analysis
    doc = nlp(script_content)
    
    # Extract key information
    sentences = [sent.text.strip() for sent in doc.sents if len(sent.text.strip()) > 10]
    
    # Extract entities and key concepts
    entities = [(ent.text, ent.label_) for ent in doc.ents]
    key_phrases = [chunk.text for chunk in doc.noun_chunks if len(chunk.text.split()) > 1]
    
    # Identify main topics using keyword frequency
    words = [token.lemma_.lower() for token in doc if token.is_alpha and not token.is_stop and len(token.text) > 3]
    word_freq = Counter(words).most_common(20)
    
    # Structure analysis - identify sections
    paragraphs = [p.strip() for p in script_content.split('\n\n') if len(p.strip()) > 50]
    
    # Readability and complexity
    readability_score = textstat.flesch_reading_ease(script_content)
    grade_level = textstat.flesch_kincaid_grade(script_content)
    
    return {
        "total_sentences": len(sentences),
        "total_paragraphs": len(paragraphs),
        "key_entities": entities[:15],
        "key_phrases": key_phrases[:20],
        "top_keywords": word_freq,
        "readability_score": readability_score,
        "grade_level": grade_level,
        "content_sections": paragraphs,
        "estimated_reading_time": len(script_content.split()) / 200  # words per minute
    }

def generate_slide_structure(analysis: Dict[str, Any], course_title: str, lesson_title: str, target_count: int) -> List[Dict[str, Any]]:
    """Generate slide structure using AI analysis of content"""
    
    # Create slide outline based on content analysis
    slides = []
    
    # Title slide
    slides.append({
        "type": "title",
        "title": lesson_title or course_title,
        "subtitle": f"Course: {course_title}" if lesson_title else "",
        "content": [],
        "slide_number": 1
    })
    
    # Learning objectives slide
    objectives = extract_learning_objectives(analysis)
    if objectives:
        slides.append({
            "type": "objectives",
            "title": "Learning Objectives",
            "content": objectives,
            "slide_number": len(slides) + 1
        })
    
    # Content slides based on paragraphs and key concepts
    content_sections = analysis["content_sections"]
    sections_per_slide = max(1, len(content_sections) // (target_count - 3))  # Reserve slides for intro/outro
    
    for i in range(0, len(content_sections), sections_per_slide):
        section_group = content_sections[i:i + sections_per_slide]
        
        # Extract main topic for this slide
        combined_text = " ".join(section_group)
        slide_title = extract_slide_title(combined_text, analysis["key_phrases"])
        
        # Create bullet points from content
        bullet_points = create_bullet_points(section_group)
        
        slides.append({
            "type": "content",
            "title": slide_title,
            "content": bullet_points,
            "slide_number": len(slides) + 1,
            "source_text": combined_text[:500] + "..." if len(combined_text) > 500 else combined_text
        })
        
        # Stop if we're approaching target count
        if len(slides) >= target_count - 1:
            break
    
    # Summary/conclusion slide
    key_points = [item[0] for item in analysis["top_keywords"][:5]]
    slides.append({
        "type": "summary",
        "title": "Key Takeaways",
        "content": [f"Understanding {point}" for point in key_points],
        "slide_number": len(slides) + 1
    })
    
    return slides

def extract_learning_objectives(analysis: Dict[str, Any]) -> List[str]:
    """Extract learning objectives from content analysis"""
    objectives = []
    
    # Look for common objective patterns in the content
    key_phrases = [phrase for phrase, _ in analysis.get("key_phrases", [])]
    keywords = [word for word, _ in analysis.get("top_keywords", [])]
    
    # Generate objectives based on key concepts
    for i, keyword in enumerate(keywords[:4]):
        if keyword.lower() not in ['course', 'lesson', 'student', 'learn']:
            objectives.append(f"Understand the fundamentals of {keyword}")
    
    return objectives[:3] if objectives else ["Master the key concepts covered in this lesson"]

def extract_slide_title(text: str, key_phrases: List[str]) -> str:
    """Extract appropriate slide title from text content"""
    import re
    
    # Look for sentences that could be titles
    sentences = re.split(r'[.!?]+', text)
    
    # Check if any key phrases appear in the first sentence
    first_sentence = sentences[0].strip() if sentences else ""
    
    for phrase in key_phrases:
        if phrase.lower() in first_sentence.lower():
            # Use the phrase as title, cleaned up
            title = phrase.title()
            return title if len(title) < 60 else title[:57] + "..."
    
    # Fallback: use first few words of content
    words = first_sentence.split()[:6]
    title = " ".join(words).title()
    return title if len(title) < 60 else title[:57] + "..."

def create_bullet_points(sections: List[str]) -> List[str]:
    """Create bullet points from content sections"""
    bullet_points = []
    
    for section in sections:
        # Split into sentences
        sentences = re.split(r'[.!?]+', section)
        
        # Take key sentences as bullet points
        for sentence in sentences[:3]:  # Max 3 bullets per section
            cleaned = sentence.strip()
            if len(cleaned) > 20 and len(cleaned) < 150:  # Reasonable length
                bullet_points.append(cleaned)
    
    return bullet_points[:6]  # Max 6 bullets per slide

def create_detailed_slides(slide_structure: List[Dict[str, Any]], analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Enhance slide structure with detailed content and formatting"""
    
    detailed_slides = []
    
    for slide in slide_structure:
        detailed_slide = slide.copy()
        
        # Add enhanced formatting based on slide type
        if slide["type"] == "title":
            detailed_slide["marp_directives"] = [
                "<!-- _class: title -->",
                "<!-- _paginate: false -->"
            ]
            detailed_slide["background_color"] = "#1e3a8a"
            detailed_slide["text_color"] = "#ffffff"
            
        elif slide["type"] == "objectives":
            detailed_slide["marp_directives"] = [
                "<!-- _class: objectives -->",
                "<!-- _header: 'Learning Objectives' -->"
            ]
            detailed_slide["icon"] = "🎯"
            
        elif slide["type"] == "content":
            detailed_slide["marp_directives"] = [
                "<!-- _class: content -->",
                f"<!-- _header: '{slide['title']}' -->"
            ]
            detailed_slide["layout"] = "bullets"
            
        elif slide["type"] == "summary":
            detailed_slide["marp_directives"] = [
                "<!-- _class: summary -->",
                "<!-- _footer: 'Thank you!' -->"
            ]
            detailed_slide["background_color"] = "#059669"
            detailed_slide["text_color"] = "#ffffff"
        
        detailed_slides.append(detailed_slide)
    
    return detailed_slides

def generate_marp_markdown(
    slides: List[Dict[str, Any]], 
    theme: str, 
    include_animations: bool, 
    custom_branding: Optional[Dict[str, str]]
) -> str:
    """Generate Marp markdown from slide structure"""
    
    # Marp header with theme and settings
    marp_content = f"""---
marp: true
theme: {theme}
size: 16:9
paginate: true
backgroundColor: #ffffff
color: #333333
"""
    
    # Add custom styling if branding provided
    if custom_branding:
        marp_content += f"""
style: |
  .title {{
    background-color: {custom_branding.get('primary_color', '#1e3a8a')};
    color: {custom_branding.get('text_color', '#ffffff')};
  }}
  .content {{
    font-family: {custom_branding.get('font_family', 'Arial, sans-serif')};
  }}
"""
    
    marp_content += "---\n\n"
    
    # Generate slides
    for i, slide in enumerate(slides):
        # Add Marp directives if specified
        if "marp_directives" in slide:
            for directive in slide["marp_directives"]:
                marp_content += f"{directive}\n"
        
        # Slide title
        if slide["type"] == "title":
            marp_content += f"# {slide['title']}\n\n"
            if slide.get("subtitle"):
                marp_content += f"## {slide['subtitle']}\n\n"
        else:
            marp_content += f"## {slide['title']}\n\n"
        
        # Slide content
        if slide["content"]:
            for item in slide["content"]:
                if slide["type"] == "objectives":
                    marp_content += f"- 🎯 {item}\n"
                elif slide["type"] == "summary":
                    marp_content += f"- ✅ {item}\n"
                else:
                    marp_content += f"- {item}\n"
            marp_content += "\n"
        
        # Add slide separator (except for last slide)
        if i < len(slides) - 1:
            marp_content += "---\n\n"
    
    return marp_content

def render_slides_multiple_formats(marp_markdown: str, filename_base: str) -> Dict[str, str]:
    """Render Marp markdown to multiple output formats"""
    import subprocess
    import tempfile
    from pathlib import Path
    import base64
    
    # Create temporary directory for rendering
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Write Marp markdown to file
        markdown_file = temp_path / f"{filename_base}.md"
        with open(markdown_file, 'w', encoding='utf-8') as f:
            f.write(marp_markdown)
        
        outputs = {}
        
        # Render to HTML
        html_file = temp_path / f"{filename_base}.html"
        subprocess.run([
            "marp", str(markdown_file), 
            "--html", 
            "--output", str(html_file),
            "--allow-local-files"
        ], check=True)
        
        # Read and encode HTML
        with open(html_file, 'rb') as f:
            outputs["html_base64"] = base64.b64encode(f.read()).decode('utf-8')
            outputs["html_path"] = str(html_file)
        
        # Render to PDF
        pdf_file = temp_path / f"{filename_base}.pdf"
        subprocess.run([
            "marp", str(markdown_file),
            "--pdf",
            "--output", str(pdf_file),
            "--allow-local-files"
        ], check=True)
        
        # Read and encode PDF
        with open(pdf_file, 'rb') as f:
            outputs["pdf_base64"] = base64.b64encode(f.read()).decode('utf-8')
        
        # Try to render to PPTX (if available)
        try:
            pptx_file = temp_path / f"{filename_base}.pptx"
            subprocess.run([
                "marp", str(markdown_file),
                "--pptx",
                "--output", str(pptx_file),
                "--allow-local-files"
            ], check=True)
            
            with open(pptx_file, 'rb') as f:
                outputs["pptx_base64"] = base64.b64encode(f.read()).decode('utf-8')
                
        except subprocess.CalledProcessError:
            logger.warning("PPTX rendering not available")
    
    return outputs

def generate_slide_previews(html_path: str) -> List[str]:
    """Generate PNG thumbnails of individual slides"""
    import subprocess
    import tempfile
    from pathlib import Path
    import base64
    
    previews = []
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Use Chromium to capture slide screenshots
            output_pattern = Path(temp_dir) / "slide_%d.png"
            
            # Capture slides as PNG images
            subprocess.run([
                "chromium", "--headless", "--disable-gpu", 
                "--virtual-time-budget=1000",
                "--screenshot", f"--window-size=1920,1080",
                f"--screenshot={output_pattern}",
                f"file://{html_path}"
            ], check=True)
            
            # Read generated PNG files
            for png_file in sorted(Path(temp_dir).glob("slide_*.png")):
                with open(png_file, 'rb') as f:
                    png_b64 = base64.b64encode(f.read()).decode('utf-8')
                    previews.append(png_b64)
    
    except Exception as e:
        logger.warning(f"Slide preview generation failed: {e}")
    
    return previews

@app.function(
    image=slide_image,
    gpu=A100_GPU_CONFIG,
    timeout=900,
    concurrency_limit=MAX_CONCURRENT_REQUESTS,
    keep_warm=KEEP_WARM,
    container_idle_timeout=IDLE_TIMEOUT
)
def batch_course_slides_generation(
    course_modules: List[Dict[str, Any]],
    course_title: str,
    global_theme: str = "default",
    custom_branding: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """Generate slide decks for multiple course modules/lessons in batch"""
    
    start_time = time.time()
    results = []
    
    logger.info(f"Starting batch slide generation for {len(course_modules)} modules")
    
    try:
        for i, module in enumerate(course_modules):
            module_start = time.time()
            
            module_id = module.get('id', f'module_{i}')
            module_title = module.get('title', f'Module {i+1}')
            script_content = module.get('script', module.get('content', ''))
            
            if not script_content:
                results.append({
                    "module_id": module_id,
                    "module_title": module_title,
                    "success": False,
                    "error": "No script content provided",
                    "processing_time": 0
                })
                continue
            
            logger.info(f"Processing module {i+1}/{len(course_modules)}: {module_title}")
            
            # Generate slides for this module
            slide_result = generate_course_slides.local(
                script_content=script_content,
                course_title=course_title,
                lesson_title=module_title,
                style_theme=global_theme,
                slide_count_target=module.get('target_slides', 10),
                include_animations=True,
                custom_branding=custom_branding
            )
            
            # Prepare module result
            module_result = {
                "module_id": module_id,
                "module_title": module_title,
                "success": slide_result.get("success", False),
                "processing_time": round(time.time() - module_start, 3),
                "progress": round((i + 1) / len(course_modules) * 100, 1)
            }
            
            if slide_result.get("success"):
                module_result.update({
                    "slide_data": slide_result["slide_data"],
                    "rendered_outputs": slide_result["rendered_outputs"],
                    "metadata": slide_result["metadata"]
                })
            else:
                module_result["error"] = slide_result.get("error", "Unknown error")
            
            results.append(module_result)
            
            # Progress logging
            if (i + 1) % 3 == 0 or i == len(course_modules) - 1:
                logger.info(f"Completed {i + 1}/{len(course_modules)} modules")
        
        total_time = time.time() - start_time
        successful_count = sum(1 for r in results if r["success"])
        
        return {
            "batch_results": results,
            "summary": {
                "total_modules": len(course_modules),
                "successful_modules": successful_count,
                "failed_modules": len(course_modules) - successful_count,
                "total_processing_time": round(total_time, 3),
                "average_time_per_module": round(total_time / len(course_modules), 3) if course_modules else 0,
                "course_title": course_title,
                "theme_used": global_theme
            }
        }
        
    except Exception as e:
        logger.error(f"Batch slide generation error: {e}")
        return {
            "batch_results": results,
            "error": str(e),
            "summary": {
                "total_modules": len(course_modules),
                "successful_modules": sum(1 for r in results if r.get("success", False)),
                "failed_modules": len(course_modules) - sum(1 for r in results if r.get("success", False)),
                "total_processing_time": round(time.time() - start_time, 3),
                "error_occurred": True
            }
        }

@app.function(
    image=slide_image,
    timeout=300,
    concurrency_limit=5
)
def health_check_marp() -> Dict[str, Any]:
    """Health check for Marp slide generation service"""
    import subprocess
    import time
    
    start_time = time.time()
    
    try:
        # Check Marp CLI availability
        marp_version = subprocess.check_output(["marp", "--version"], text=True).strip()
        
        # Check Node.js
        node_version = subprocess.check_output(["node", "--version"], text=True).strip()
        
        # Check Chromium
        chromium_version = subprocess.check_output(["chromium", "--version"], text=True).strip()
        
        # Test basic slide generation
        test_markdown = """---
marp: true
theme: default
---
# Test Slide
This is a test slide for health check.
"""
        
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
            f.write(test_markdown)
            test_file = f.name
        
        # Test HTML rendering
        subprocess.run(["marp", test_file, "--html"], check=True, capture_output=True)
        
        return {
            "status": "healthy",
            "service": "Marp Slide Generation",
            "marp_version": marp_version,
            "node_version": node_version,
            "chromium_version": chromium_version,
            "test_generation": "passed",
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "timestamp": time.time()
        }
        
    except Exception as e:
        return {
            "status": "error",
            "service": "Marp Slide Generation",
            "error": str(e),
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "timestamp": time.time()
        }

# REST API endpoints
@app.web_endpoint(method="GET", docs=True)
def api_marp_health():
    """Health check endpoint for Marp service"""
    return health_check_marp.remote()

@app.web_endpoint(method="POST", docs=True)
def api_generate_slides(item: Dict[str, Any]):
    """Generate slides from script content"""
    return generate_course_slides.remote(
        script_content=item.get("script_content", ""),
        course_title=item.get("course_title", ""),
        lesson_title=item.get("lesson_title", ""),
        style_theme=item.get("style_theme", "default"),
        slide_count_target=item.get("slide_count_target", 10),
        include_animations=item.get("include_animations", True),
        custom_branding=item.get("custom_branding")
    )

@app.web_endpoint(method="POST", docs=True)
def api_batch_slides(item: Dict[str, Any]):
    """Batch slide generation for course modules"""
    return batch_course_slides_generation.remote(
        course_modules=item.get("course_modules", []),
        course_title=item.get("course_title", ""),
        global_theme=item.get("global_theme", "default"),
        custom_branding=item.get("custom_branding")
    )

if __name__ == "__main__":
    print("🎨 Koursia Marp Slide Generation Service")
    print("Features:")
    print("- AI-powered slide structure analysis")
    print("- Marp markdown generation with themes")
    print("- Multi-format output (HTML, PDF, PPTX)")
    print("- Batch processing for course modules")
    print("- A100 GPU acceleration for AI analysis")
    print("- Slide preview generation")
    print("\nDeploy with: modal deploy modal_marp_slidegen.py")