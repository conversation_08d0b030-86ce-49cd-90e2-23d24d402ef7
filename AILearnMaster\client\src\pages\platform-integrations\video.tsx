import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'wouter';
import { 
  VideoIcon, 
  ExternalLink, 
  Info, 
  Plus, 
  Settings, 
  CheckCircle, 
  AlertTriangle, 
  Upload, 
  BarChart3, 
  Users, 
  Clock, 
  DollarSign, 
  TrendingUp, 
  Play, 
  Pause, 
  Eye, 
  ThumbsUp, 
  Share2, 
  Link2, 
  Calendar, 
  Globe,
  Loader2,
  ArrowLeft,
  RefreshCw,
  Power,
  Activity,
  Zap,
  Star,
  Download,
  Film,
  Wifi,
  Shield,
  Sparkles
} from 'lucide-react';

interface VideoPlatform {
  id: number;
  name: string;
  slug: string;
  status: 'connected' | 'disconnected' | 'error' | 'pending';
  description: string;
  logo: string;
  category: 'hosting' | 'streaming' | 'social' | 'enterprise';
  features: string[];
  pricing: string;
  setupDifficulty: 'easy' | 'medium' | 'advanced';
  estimatedSetupTime: string;
  maxFileSize: string;
  supportedFormats: string[];
  monthlyUploads?: number;
  totalVideos?: number;
  lastSync?: string;
  popularity: number;
  apiDocUrl: string;
  limitations?: string[];
}

interface VideoStats {
  totalViews: number;
  totalVideos: number;
  totalDuration: string;
  monthlyUploads: number;
  topPerformingVideo: {
    title: string;
    views: number;
    platform: string;
  };
}

export default function VideoPlatformsPage() {
  const [selectedPlatform, setSelectedPlatform] = useState<VideoPlatform | null>(null);
  const [showConnectionDialog, setShowConnectionDialog] = useState(false);
  const [connectionForm, setConnectionForm] = useState({
    apiKey: '',
    clientId: '',
    clientSecret: '',
    accessToken: '',
    channelId: ''
  });
  const [refreshing, setRefreshing] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const videoPlatforms: VideoPlatform[] = [
    {
      id: 1,
      name: 'YouTube',
      slug: 'youtube',
      status: 'connected',
      description: 'World\'s largest video platform with 2+ billion monthly users',
      logo: '📺',
      category: 'social',
      features: ['Global reach', 'Monetization', 'Analytics', 'Live streaming', 'Community posts'],
      pricing: 'Free + revenue share',
      setupDifficulty: 'medium',
      estimatedSetupTime: '30 minutes',
      maxFileSize: '256GB',
      supportedFormats: ['MP4', 'MOV', 'AVI', 'WMV', 'FLV', 'WebM'],
      monthlyUploads: 24,
      totalVideos: 156,
      lastSync: '2025-06-24T15:30:00Z',
      popularity: 98,
      apiDocUrl: 'https://developers.google.com/youtube/v3',
      limitations: ['Content ID claims', 'Algorithm dependency', 'Demonetization risks']
    },
    {
      id: 2,
      name: 'Vimeo',
      slug: 'vimeo',
      status: 'connected',
      description: 'Professional video hosting with advanced privacy and customization',
      logo: '🎬',
      category: 'hosting',
      features: ['High quality', 'No ads', 'Custom players', 'Privacy controls', 'Analytics'],
      pricing: '$7/month+',
      setupDifficulty: 'easy',
      estimatedSetupTime: '15 minutes',
      maxFileSize: '8GB (Pro+)',
      supportedFormats: ['MP4', 'MOV', 'AVI', 'WMV', 'FLV'],
      monthlyUploads: 12,
      totalVideos: 89,
      lastSync: '2025-06-24T14:45:00Z',
      popularity: 75,
      apiDocUrl: 'https://developer.vimeo.com',
      limitations: ['Storage limits on lower plans', 'Limited social reach']
    },
    {
      id: 3,
      name: 'Wistia',
      slug: 'wistia',
      status: 'disconnected',
      description: 'Business-focused video hosting with powerful marketing tools',
      logo: '📊',
      category: 'enterprise',
      features: ['Lead generation', 'Detailed analytics', 'Customizable players', 'A/B testing'],
      pricing: '$19/month+',
      setupDifficulty: 'easy',
      estimatedSetupTime: '20 minutes',
      maxFileSize: '8GB',
      supportedFormats: ['MP4', 'MOV', 'AVI', 'WMV'],
      popularity: 65,
      apiDocUrl: 'https://wistia.com/support/developers',
      limitations: ['Higher cost', 'Limited free tier']
    },
    {
      id: 4,
      name: 'Twitch',
      slug: 'twitch',
      status: 'disconnected',
      description: 'Live streaming platform with interactive community features',
      logo: '🎮',
      category: 'streaming',
      features: ['Live streaming', 'Chat interaction', 'Clips', 'VOD storage', 'Monetization'],
      pricing: 'Free + revenue share',
      setupDifficulty: 'medium',
      estimatedSetupTime: '25 minutes',
      maxFileSize: 'Live streaming',
      supportedFormats: ['Live stream', 'MP4 for highlights'],
      popularity: 85,
      apiDocUrl: 'https://dev.twitch.tv',
      limitations: ['Primarily gaming focused', 'Live streaming focus']
    },
    {
      id: 5,
      name: 'JW Player',
      slug: 'jwplayer',
      status: 'error',
      description: 'Enterprise video platform with global CDN and analytics',
      logo: '⚡',
      category: 'enterprise',
      features: ['Global CDN', 'Live streaming', 'Video analytics', 'DRM protection'],
      pricing: 'Contact for pricing',
      setupDifficulty: 'advanced',
      estimatedSetupTime: '1-2 hours',
      maxFileSize: 'No limit',
      supportedFormats: ['MP4', 'HLS', 'DASH', 'WebM'],
      popularity: 70,
      apiDocUrl: 'https://developer.jwplayer.com',
      limitations: ['Complex setup', 'Enterprise pricing']
    },
    {
      id: 6,
      name: 'Brightcove',
      slug: 'brightcove',
      status: 'pending',
      description: 'Enterprise video cloud platform with advanced security',
      logo: '🔒',
      category: 'enterprise',
      features: ['Enterprise security', 'Global delivery', 'Live streaming', 'Analytics'],
      pricing: 'Contact for pricing',
      setupDifficulty: 'advanced',
      estimatedSetupTime: '2-3 hours',
      maxFileSize: 'No limit',
      supportedFormats: ['MP4', 'HLS', 'DASH', 'WebM'],
      popularity: 60,
      apiDocUrl: 'https://docs.brightcove.com',
      limitations: ['Enterprise only', 'Complex integration']
    }
  ];

  const connectedPlatforms = videoPlatforms.filter(p => p.status === 'connected');
  const disconnectedPlatforms = videoPlatforms.filter(p => p.status === 'disconnected');
  const errorPlatforms = videoPlatforms.filter(p => p.status === 'error');

  const videoStats: VideoStats = {
    totalViews: 125847,
    totalVideos: 245,
    totalDuration: '48h 32m',
    monthlyUploads: 36,
    topPerformingVideo: {
      title: 'Complete Web Development Course',
      views: 15432,
      platform: 'YouTube'
    }
  };

  // Fetch video platforms from API (optional - we have static data)
  const { data: apiPlatforms, isLoading } = useQuery({
    queryKey: ['/api/video-platforms'],
    queryFn: () => apiRequest('/api/video-platforms'),
    retry: false,
    staleTime: 5 * 60 * 1000
  });

  // Connect platform mutation
  const connectPlatformMutation = useMutation({
    mutationFn: async (data: { platformId: number; credentials: any }) => {
      return apiRequest(`/api/video-platforms/${data.platformId}/connect`, {
        method: 'POST',
        body: JSON.stringify(data.credentials)
      });
    },
    onSuccess: () => {
      toast({
        title: "Platform Connected",
        description: "Successfully connected to the video platform",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/video-platforms'] });
      setShowConnectionDialog(false);
      setConnectionForm({ apiKey: '', clientId: '', clientSecret: '', accessToken: '', channelId: '' });
    },
    onError: (error: any) => {
      toast({
        title: "Connection Failed",
        description: error.message || "Failed to connect to platform",
        variant: "destructive",
      });
    }
  });

  // Disconnect platform mutation
  const disconnectPlatformMutation = useMutation({
    mutationFn: async (platformId: number) => {
      return apiRequest(`/api/video-platforms/${platformId}/disconnect`, {
        method: 'POST'
      });
    },
    onSuccess: () => {
      toast({
        title: "Platform Disconnected",
        description: "Successfully disconnected from the platform",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/video-platforms'] });
    },
    onError: (error: any) => {
      toast({
        title: "Disconnect Failed",
        description: error.message || "Failed to disconnect from platform",
        variant: "destructive",
      });
    }
  });

  const handleConnect = (platform: VideoPlatform) => {
    setSelectedPlatform(platform);
    setShowConnectionDialog(true);
  };

  const handleSubmitConnection = () => {
    if (!selectedPlatform || !connectionForm.apiKey) {
      toast({
        title: "Missing Information",
        description: "Please enter your API credentials to connect",
        variant: "destructive",
      });
      return;
    }

    connectPlatformMutation.mutate({
      platformId: selectedPlatform.id,
      credentials: connectionForm
    });
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'hosting': return <VideoIcon className="h-4 w-4" />;
      case 'streaming': return <Wifi className="h-4 w-4" />;
      case 'social': return <Share2 className="h-4 w-4" />;
      case 'enterprise': return <Shield className="h-4 w-4" />;
      default: return <Film className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSetupDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'advanced': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    queryClient.invalidateQueries({ queryKey: ['/api/video-platforms'] });
    setTimeout(() => setRefreshing(false), 2000);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/platform-integrations">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Integrations
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-br from-red-500 to-pink-600">
                <VideoIcon className="h-6 w-6 text-white" />
              </div>
              Video Platforms
            </h1>
            <p className="text-muted-foreground mt-2">
              Connect your video hosting platforms to publish and manage your course videos
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh Status
          </Button>
          <Button onClick={() => setShowConnectionDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Platform
          </Button>
        </div>
      </div>

      {/* Video Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
            <Eye className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{videoStats.totalViews.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Across all platforms</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Videos</CardTitle>
            <Film className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{videoStats.totalVideos}</div>
            <p className="text-xs text-muted-foreground">{videoStats.totalDuration} total duration</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{videoStats.monthlyUploads}</div>
            <p className="text-xs text-muted-foreground">Videos uploaded</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Video</CardTitle>
            <Star className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{videoStats.topPerformingVideo.views.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">{videoStats.topPerformingVideo.title}</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="connected">Connected ({connectedPlatforms.length})</TabsTrigger>
          <TabsTrigger value="available">Available ({disconnectedPlatforms.length})</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {videoPlatforms.map((platform) => (
              <Card key={platform.id} className="relative hover:shadow-lg transition-all duration-200">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-3xl">{platform.logo}</span>
                      <div>
                        <CardTitle className="text-xl">{platform.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          {getCategoryIcon(platform.category)}
                          <span className="text-sm text-muted-foreground capitalize">{platform.category}</span>
                          <Badge className={getStatusColor(platform.status)}>
                            {platform.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {platform.popularity}% popular
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{platform.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Pricing:</span>
                      <p className="text-muted-foreground">{platform.pricing}</p>
                    </div>
                    <div>
                      <span className="font-medium">Max File:</span>
                      <p className="text-muted-foreground">{platform.maxFileSize}</p>
                    </div>
                  </div>

                  {platform.status === 'connected' && platform.monthlyUploads && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Monthly usage</span>
                        <span className="font-medium">{platform.monthlyUploads} videos</span>
                      </div>
                      <Progress value={(platform.monthlyUploads / 50) * 100} className="h-2" />
                    </div>
                  )}

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Key Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {platform.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {platform.features.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{platform.features.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  {platform.status === 'connected' ? (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => disconnectPlatformMutation.mutate(platform.id)}
                    >
                      <Power className="h-4 w-4 mr-2" />
                      Disconnect
                    </Button>
                  ) : (
                    <Button 
                      size="sm"
                      onClick={() => handleConnect(platform)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Connect
                    </Button>
                  )}
                  <Button variant="outline" size="sm" asChild>
                    <a href={platform.apiDocUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      API Docs
                    </a>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="connected" className="space-y-4">
          {connectedPlatforms.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <VideoIcon className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Connected Platforms</h3>
                <p className="text-muted-foreground text-center mb-4">
                  Connect to video platforms to start uploading and managing your course videos
                </p>
                <Button onClick={() => setShowConnectionDialog(true)}>
                  Connect Your First Platform
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {connectedPlatforms.map((platform) => (
                <Card key={platform.id} className="border-l-4 border-l-green-500">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-3xl">{platform.logo}</span>
                        <div>
                          <CardTitle className="text-xl">{platform.name}</CardTitle>
                          <div className="flex items-center space-x-2 mt-1">
                            {getCategoryIcon(platform.category)}
                            <Badge className="bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Connected
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Videos:</span>
                        <p className="text-muted-foreground">{platform.totalVideos || 0}</p>
                      </div>
                      <div>
                        <span className="font-medium">This Month:</span>
                        <p className="text-muted-foreground">{platform.monthlyUploads || 0}</p>
                      </div>
                    </div>

                    {platform.lastSync && (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Activity className="h-4 w-4" />
                        <span>Last sync: {new Date(platform.lastSync).toLocaleString()}</span>
                      </div>
                    )}

                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Supported Formats</h4>
                      <div className="flex flex-wrap gap-1">
                        {platform.supportedFormats.map((format, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {format}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4 mr-2" />
                      Manage
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => disconnectPlatformMutation.mutate(platform.id)}
                    >
                      <Power className="h-4 w-4 mr-2" />
                      Disconnect
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="available" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {disconnectedPlatforms.map((platform) => (
              <Card key={platform.id} className="hover:shadow-lg transition-all duration-200">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-3xl">{platform.logo}</span>
                      <div>
                        <CardTitle className="text-xl">{platform.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          {getCategoryIcon(platform.category)}
                          <span className="text-sm text-muted-foreground capitalize">{platform.category}</span>
                        </div>
                      </div>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {platform.popularity}% popular
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{platform.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Pricing:</span>
                      <p className="text-muted-foreground">{platform.pricing}</p>
                    </div>
                    <div>
                      <span className="font-medium">Setup:</span>
                      <p className={`${getSetupDifficultyColor(platform.setupDifficulty)} capitalize`}>
                        {platform.setupDifficulty}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    <span>{platform.estimatedSetupTime}</span>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Key Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {platform.features.slice(0, 4).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {platform.limitations && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm text-orange-600">Considerations</h4>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        {platform.limitations.map((limitation, index) => (
                          <li key={index}>• {limitation}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    size="sm"
                    onClick={() => handleConnect(platform)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Connect
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <a href={platform.apiDocUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Learn More
                    </a>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Platform Performance</CardTitle>
                <CardDescription>View and upload statistics by platform</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {connectedPlatforms.map((platform) => (
                    <div key={platform.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-xl">{platform.logo}</span>
                        <div>
                          <p className="font-medium">{platform.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {platform.totalVideos} videos
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{(platform.monthlyUploads || 0 * 1200).toLocaleString()} views</p>
                        <p className="text-sm text-muted-foreground">
                          {platform.monthlyUploads || 0} this month
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upload Trends</CardTitle>
                <CardDescription>Monthly upload activity across platforms</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span>This Month</span>
                    <span className="font-medium">{videoStats.monthlyUploads} uploads</span>
                  </div>
                  <Progress value={75} className="h-2" />
                  
                  <div className="flex justify-between text-sm">
                    <span>Average Duration</span>
                    <span className="font-medium">12m 34s</span>
                  </div>
                  <Progress value={65} className="h-2" />
                  
                  <div className="flex justify-between text-sm">
                    <span>Success Rate</span>
                    <span className="font-medium">98.5%</span>
                  </div>
                  <Progress value={98.5} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest video uploads and platform interactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { action: 'Video uploaded', platform: 'YouTube', title: 'React Hooks Deep Dive', time: '2 hours ago' },
                  { action: 'Video published', platform: 'Vimeo', title: 'CSS Grid Masterclass', time: '5 hours ago' },
                  { action: 'Analytics sync', platform: 'YouTube', title: 'Weekly performance data', time: '1 day ago' },
                  { action: 'Video uploaded', platform: 'Vimeo', title: 'JavaScript ES2024 Features', time: '2 days ago' }
                ].map((activity, index) => (
                  <div key={index} className="flex items-center gap-4 p-3 border rounded-lg">
                    <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                      <Upload className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{activity.action}</p>
                      <p className="text-sm text-muted-foreground">
                        {activity.title} • {activity.platform}
                      </p>
                    </div>
                    <span className="text-sm text-muted-foreground">{activity.time}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Connection Dialog */}
      <Dialog open={showConnectionDialog} onOpenChange={setShowConnectionDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedPlatform && (
                <>
                  <span className="text-2xl">{selectedPlatform.logo}</span>
                  Connect to {selectedPlatform.name}
                </>
              )}
            </DialogTitle>
            <DialogDescription>
              {selectedPlatform?.description}
            </DialogDescription>
          </DialogHeader>
          
          {selectedPlatform && (
            <div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Setup Difficulty:</span>
                  <span className={`font-medium ${getSetupDifficultyColor(selectedPlatform.setupDifficulty)} capitalize`}>
                    {selectedPlatform.setupDifficulty}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Estimated Time:</span>
                  <span className="font-medium">{selectedPlatform.estimatedSetupTime}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Max File Size:</span>
                  <span className="font-medium">{selectedPlatform.maxFileSize}</span>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="api-key">API Key *</Label>
                  <Input
                    id="api-key"
                    type="password"
                    placeholder="Enter your API key"
                    value={connectionForm.apiKey}
                    onChange={(e) => setConnectionForm(prev => ({ ...prev, apiKey: e.target.value }))}
                  />
                </div>

                {selectedPlatform.name === 'YouTube' && (
                  <>
                    <div>
                      <Label htmlFor="client-id">Client ID</Label>
                      <Input
                        id="client-id"
                        placeholder="YouTube OAuth Client ID"
                        value={connectionForm.clientId}
                        onChange={(e) => setConnectionForm(prev => ({ ...prev, clientId: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="client-secret">Client Secret</Label>
                      <Input
                        id="client-secret"
                        type="password"
                        placeholder="YouTube OAuth Client Secret"
                        value={connectionForm.clientSecret}
                        onChange={(e) => setConnectionForm(prev => ({ ...prev, clientSecret: e.target.value }))}
                      />
                    </div>
                  </>
                )}

                <div>
                  <Label>Supported Formats:</Label>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedPlatform.supportedFormats.map((format, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {format}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowConnectionDialog(false)}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleSubmitConnection}
                  disabled={connectPlatformMutation.isPending || !connectionForm.apiKey}
                >
                  {connectPlatformMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Connecting...
                    </>
                  ) : (
                    <>
                      <Link2 className="h-4 w-4 mr-2" />
                      Connect Platform
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Platform Integration Tips */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Platform Integration Tips</strong>
          <ul className="mt-2 space-y-1 text-sm">
            <li>• Ensure your API keys have the appropriate permissions for video upload and management.</li>
            <li>• For YouTube, use an OAuth 2.0 application with YouTube Data API v3 access.</li>
            <li>• For Vimeo, create an API app in your developer dashboard and request upload access.</li>
            <li>• Integration allows automatic publishing of course videos directly to these platforms.</li>
          </ul>
        </AlertDescription>
      </Alert>
    </div>
  );
}