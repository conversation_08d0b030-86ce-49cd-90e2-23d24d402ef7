
#!/usr/bin/env python3
"""
Final Modal A100 Credentials Fix and Deployment
This script will authenticate with <PERSON><PERSON> and deploy your A100 GPU services
"""

import os
import sys
import subprocess
import json

def check_modal_installation():
    """Check if Modal is properly installed"""
    try:
        result = subprocess.run(['modal', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Modal CLI installed: {result.stdout.strip()}")
            return True
        else:
            print("❌ Modal CLI not working properly")
            return False
    except FileNotFoundError:
        print("❌ Modal CLI not found")
        return False

def install_modal():
    """Install Modal CLI"""
    print("📦 Installing Modal CLI...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'modal'], check=True)
        print("✓ Modal installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install Modal: {e}")
        return False

def authenticate_modal():
    """Authenticate with Modal using browser"""
    print("🔑 Starting Modal authentication...")
    print("This will open your browser to authenticate with Modal")
    
    try:
        subprocess.run(['modal', 'token', 'new'], check=True)
        print("✓ Modal authentication completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Authentication failed: {e}")
        return False

def test_modal_auth():
    """Test if Modal authentication is working"""
    try:
        result = subprocess.run(['modal', 'token', 'verify'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Modal authentication verified")
            return True
        else:
            print(f"❌ Authentication verification failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error testing authentication: {e}")
        return False

def deploy_a100_app():
    """Deploy the A100 GPU application"""
    print("🚀 Deploying A100 GPU application...")
    
    # Choose the best production app
    app_file = 'modal_a100_production.py'
    if not os.path.exists(app_file):
        print(f"❌ App file {app_file} not found")
        return False
    
    try:
        result = subprocess.run(['modal', 'deploy', app_file], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ A100 GPU application deployed successfully!")
            print(result.stdout)
            return True
        else:
            print(f"❌ Deployment failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ Deployment timed out - this may be normal for first deployment")
        return True
    except Exception as e:
        print(f"❌ Deployment error: {e}")
        return False

def test_a100_functions():
    """Test A100 GPU functions"""
    print("🧪 Testing A100 GPU functions...")
    
    try:
        # Test health check
        result = subprocess.run([
            'modal', 'run', 'modal_a100_production.py::health_check_production'
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✓ A100 GPU health check passed")
            print(f"GPU Status: {result.stdout}")
            return True
        else:
            print(f"❌ Health check failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ Health check timed out - GPU may be starting up")
        return True
    except Exception as e:
        print(f"❌ Testing error: {e}")
        return False

def update_environment_vars():
    """Update environment variables for A100 GPU"""
    print("🔧 Updating environment for A100 GPU...")
    
    # Set environment flag to enable A100 GPU
    env_content = """
# A100 GPU Configuration
A100_GPU_ENABLED=true
MODAL_GPU_ENABLED=true

# Add your existing environment variables here
"""
    
    try:
        with open('.env', 'a') as f:
            f.write(env_content)
        print("✓ Environment variables updated")
        return True
    except Exception as e:
        print(f"❌ Failed to update environment: {e}")
        return False

def main():
    """Main deployment process"""
    print("🚀 Modal A100 GPU Deployment for Course AI Platform")
    print("=" * 60)
    
    # Step 1: Check Modal installation
    if not check_modal_installation():
        if not install_modal():
            print("❌ Failed to install Modal CLI")
            return False
    
    # Step 2: Authenticate with Modal
    if not test_modal_auth():
        if not authenticate_modal():
            print("❌ Failed to authenticate with Modal")
            print("\nManual steps:")
            print("1. Visit https://modal.com")
            print("2. Sign up or log in")
            print("3. Run: modal token new")
            return False
    
    # Step 3: Deploy A100 application
    if not deploy_a100_app():
        print("❌ Failed to deploy A100 application")
        return False
    
    # Step 4: Test GPU functions
    if not test_a100_functions():
        print("⚠️ GPU functions test had issues, but deployment may still work")
    
    # Step 5: Update environment
    update_environment_vars()
    
    print("\n🎉 A100 GPU Setup Complete!")
    print("=" * 40)
    print("Your Course AI Platform now has access to:")
    print("• A100 80GB GPU for high-quality TTS")
    print("• SadTalker avatar video generation")
    print("• SDXL image generation")
    print("• Large model inference")
    print("• Automatic fallback systems")
    print("\nNext steps:")
    print("1. Restart your application")
    print("2. GPU services will activate automatically when needed")
    print("3. Monitor usage at https://modal.com/apps")
    print("4. Set up billing alerts to control costs")
    
    return True

if __name__ == "__main__":
    if not main():
        sys.exit(1)
