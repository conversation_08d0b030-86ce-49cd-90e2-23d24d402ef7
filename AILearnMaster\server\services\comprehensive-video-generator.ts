import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { exec } from 'child_process';
import fetch from 'cross-fetch';

const execPromise = promisify(exec);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// Define directories
const uploadsDir = path.join(process.cwd(), 'uploads');
const audioDir = path.join(uploadsDir, 'audio');
const videoDir = path.join(uploadsDir, 'videos');
const tempDir = path.join(process.cwd(), 'temp');

// Ensure directories exist
async function ensureDirectoriesExist() {
  for (const dir of [uploadsDir, audioDir, videoDir, tempDir]) {
    try {
      await mkdir(dir, { recursive: true });
    } catch (err) {
      if ((err as NodeJS.ErrnoException).code !== 'EEXIST') {
        throw err;
      }
    }
  }
}

interface VideoGenerationParams {
  script: string;
  voiceSettings: {
    voice: string;
    speed: number;
    service: string;
  };
  mediaUrls: string[];
  outputFileName: string;
  includeSubtitles?: boolean;
}

interface VideoGenerationResult {
  success: boolean;
  videoUrl?: string;
  audioUrl?: string;
  error?: string;
  progress: number;
}

export class ComprehensiveVideoGenerator {
  private progressCallbacks: Map<string, (progress: number) => void> = new Map();

  // Generate text-to-speech audio
  async generateVoice(text: string, voiceSettings: any): Promise<string> {
    try {
      const response = await fetch('http://localhost:5000/api/ai/text-to-speech', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text,
          voiceId: voiceSettings.voice || voiceSettings.voiceId || 'alloy',
          speed: voiceSettings.speed || 1.0,
          service: voiceSettings.service || 'neural',
          quality: voiceSettings.quality || 'standard'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Voice generation failed:', errorData);
        throw new Error(`Voice generation failed: ${errorData.error || response.statusText}`);
      }

      const result = await response.json();
      return result.audioUrl;
    } catch (error) {
      console.error('Voice generation error:', error);
      throw new Error(`Failed to generate voice: ${error.message}`);
    }
  }

  // Download media files from URLs
  async downloadMedia(mediaUrls: string[]): Promise<string[]> {
    const downloadedPaths: string[] = [];
    
    for (let i = 0; i < mediaUrls.length; i++) {
      const url = mediaUrls[i];
      const fileName = `media_${Date.now()}_${i}${path.extname(url) || '.jpg'}`;
      const filePath = path.join(tempDir, fileName);
      
      try {
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to download media: ${response.statusText}`);
        }
        
        const buffer = await response.arrayBuffer();
        await writeFile(filePath, Buffer.from(buffer));
        downloadedPaths.push(filePath);
      } catch (error) {
        console.error(`Error downloading media ${url}:`, error);
        // Continue with other media files
      }
    }
    
    return downloadedPaths;
  }

  // Generate subtitles in SRT format
  generateSubtitles(script: string, duration: number): string {
    const words = script.split(/\s+/);
    const chunks: string[] = [];
    let currentChunk = '';
    
    for (const word of words) {
      if (currentChunk.length + word.length > 50) {
        chunks.push(currentChunk.trim());
        currentChunk = word;
      } else {
        currentChunk += ' ' + word;
      }
    }
    
    if (currentChunk.trim().length > 0) {
      chunks.push(currentChunk.trim());
    }
    
    const timePerChunk = duration / chunks.length;
    let srt = '';
    
    chunks.forEach((chunk, index) => {
      const startTime = index * timePerChunk;
      const endTime = (index + 1) * timePerChunk;
      
      srt += `${index + 1}\n`;
      srt += `${this.formatSRTTime(startTime)} --> ${this.formatSRTTime(endTime)}\n`;
      srt += `${chunk}\n\n`;
    });
    
    return srt;
  }

  // Format time for SRT subtitles
  private formatSRTTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const milliseconds = Math.floor((seconds - Math.floor(seconds)) * 1000);
    
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')},${String(milliseconds).padStart(3, '0')}`;
  }

  // Get audio duration using ffprobe
  async getAudioDuration(audioPath: string): Promise<number> {
    try {
      const ffprobeCmd = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${audioPath}"`;
      const { stdout } = await execPromise(ffprobeCmd);
      const duration = parseFloat(stdout.trim());
      
      if (isNaN(duration)) {
        throw new Error('Failed to determine audio duration');
      }
      
      return duration;
    } catch (error) {
      console.error('Error getting audio duration:', error);
      throw new Error('Failed to get audio duration');
    }
  }

  // Create video with synchronized audio and media
  async createVideoWithAudio(
    audioPath: string,
    mediaPaths: string[],
    outputPath: string,
    includeSubtitles: boolean = false,
    script?: string
  ): Promise<void> {
    try {
      // Get audio duration
      const audioDuration = await this.getAudioDuration(audioPath);
      
      // Create image sequence for video
      const imageListPath = path.join(tempDir, `${Date.now()}_images.txt`);
      const durationPerImage = audioDuration / mediaPaths.length;
      
      let imageListContent = '';
      for (const mediaPath of mediaPaths) {
        imageListContent += `file '${mediaPath}'\nduration ${durationPerImage}\n`;
      }
      
      // Add last image again (required by ffmpeg)
      if (mediaPaths.length > 0) {
        imageListContent += `file '${mediaPaths[mediaPaths.length - 1]}'\n`;
      }
      
      await writeFile(imageListPath, imageListContent);
      
      // Create video with audio
      let ffmpegCmd = `ffmpeg -y -f concat -safe 0 -i "${imageListPath}" -i "${audioPath}" -vsync vfr -pix_fmt yuv420p -r 30 -c:a aac -b:a 192k -shortest`;
      
      // Add subtitles if requested
      if (includeSubtitles && script) {
        const subtitlesPath = path.join(tempDir, `${Date.now()}_subtitles.srt`);
        const subtitleContent = this.generateSubtitles(script, audioDuration);
        await writeFile(subtitlesPath, subtitleContent);
        
        ffmpegCmd += ` -vf "subtitles='${subtitlesPath.replace(/\\/g, '\\\\')}'"`; 
      }
      
      ffmpegCmd += ` "${outputPath}"`;
      
      console.log(`Executing FFmpeg command: ${ffmpegCmd}`);
      const { stdout, stderr } = await execPromise(ffmpegCmd);
      
      if (stderr && !stderr.includes('frame=')) {
        console.error('FFmpeg stderr:', stderr);
      }
      
      // Clean up temporary files
      fs.unlink(imageListPath, (err) => {
        if (err) console.error('Error deleting image list:', err);
      });
      
    } catch (error) {
      console.error('Error creating video with audio:', error);
      throw new Error('Failed to create video with audio');
    }
  }

  // Main video generation function
  async generateLessonVideo(params: VideoGenerationParams): Promise<VideoGenerationResult> {
    await ensureDirectoriesExist();
    
    const jobId = `video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const outputPath = path.join(videoDir, `${params.outputFileName}.mp4`);
    
    try {
      // Step 1: Generate voice (20% progress)
      this.updateProgress(jobId, 20);
      const audioUrl = await this.generateVoice(params.script, params.voiceSettings);
      const audioPath = path.join(process.cwd(), audioUrl.replace(/^\//, ''));
      
      // Step 2: Download media files (40% progress)
      this.updateProgress(jobId, 40);
      const mediaPaths = await this.downloadMedia(params.mediaUrls);
      
      if (mediaPaths.length === 0) {
        throw new Error('No media files could be downloaded');
      }
      
      // Step 3: Create video (80% progress)
      this.updateProgress(jobId, 80);
      await this.createVideoWithAudio(
        audioPath,
        mediaPaths,
        outputPath,
        params.includeSubtitles,
        params.script
      );
      
      // Step 4: Complete (100% progress)
      this.updateProgress(jobId, 100);
      
      // Clean up downloaded media files
      mediaPaths.forEach(mediaPath => {
        fs.unlink(mediaPath, (err) => {
          if (err) console.error('Error deleting media file:', err);
        });
      });
      
      return {
        success: true,
        videoUrl: `/uploads/videos/${path.basename(outputPath)}`,
        audioUrl,
        progress: 100
      };
      
    } catch (error) {
      console.error('Video generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        progress: 0
      };
    }
  }

  // Progress tracking
  private updateProgress(jobId: string, progress: number) {
    const callback = this.progressCallbacks.get(jobId);
    if (callback) {
      callback(progress);
    }
  }

  // Set progress callback
  setProgressCallback(jobId: string, callback: (progress: number) => void) {
    this.progressCallbacks.set(jobId, callback);
  }

  // Remove progress callback
  removeProgressCallback(jobId: string) {
    this.progressCallbacks.delete(jobId);
  }
}

// Export singleton instance
export const videoGenerator = new ComprehensiveVideoGenerator();