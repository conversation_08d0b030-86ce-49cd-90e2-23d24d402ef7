import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { 
  ShoppingBag, 
  TrendingUp, 
  DollarSign, 
  Users, 
  ExternalLink,
  Upload,
  Eye,
  Star,
  Loader2,
  CheckCircle,
  AlertCircle,
  BarChart3,
  Globe,
  Target
} from 'lucide-react';

interface Marketplace {
  id: string;
  name: string;
  description: string;
  logo: string;
  category: 'premium' | 'open' | 'corporate';
  commission: number;
  features: string[];
  requirements: string[];
  supportedFormats: string[];
  avgEarnings: string;
  students: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  processingTime: string;
  status: 'available' | 'connected' | 'pending';
}

interface PublishedCourse {
  id: number;
  title: string;
  marketplace: string;
  status: 'published' | 'pending' | 'rejected' | 'draft';
  students: number;
  revenue: number;
  rating: number;
  lastUpdated: string;
}

export default function CourseMarketplaces() {
  const [selectedMarketplace, setSelectedMarketplace] = useState<Marketplace | null>(null);
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch published courses
  const { data: publishedCourses = [], isLoading: coursesLoading } = useQuery({
    queryKey: ['/api/publishing/courses'],
    queryFn: () => apiRequest('/api/publishing/courses')
  });

  // Fetch marketplace analytics
  const { data: analytics, isLoading: analyticsLoading } = useQuery({
    queryKey: ['/api/analytics/marketplaces'],
    queryFn: () => apiRequest('/api/analytics/marketplaces')
  });

  // Fetch available marketplaces
  const { data: marketplaces = [], isLoading: marketplacesLoading } = useQuery({
    queryKey: ['/api/platforms/marketplaces'],
    queryFn: () => apiRequest('/api/platforms/marketplaces')
  });

  // Publish course mutation
  const publishCourseMutation = useMutation({
    mutationFn: async (data: { courseId: string; marketplaceId: string; settings?: any }) => {
      return apiRequest('/api/publishing/publish', {
        method: 'POST',
        body: JSON.stringify(data)
      });
    },
    onSuccess: () => {
      toast({
        title: "Course Published",
        description: "Your course has been submitted for review",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/publishing/courses'] });
      setSelectedMarketplace(null);
    },
    onError: (error: any) => {
      toast({
        title: "Publishing Failed",
        description: error.message || "Failed to publish course",
        variant: "destructive",
      });
    }
  });

  const availableMarketplaces: Marketplace[] = [
    {
      id: 'udemy',
      name: 'Udemy',
      description: 'World\'s largest marketplace for online courses',
      logo: '🎓',
      category: 'premium',
      commission: 50,
      features: ['Global reach', 'Marketing support', 'Mobile app', 'Certificates'],
      requirements: ['HD video quality', 'English subtitles', 'Course length > 30 min'],
      supportedFormats: ['MP4', 'PDF', 'Quiz', 'Assignments'],
      avgEarnings: '$1,200/month',
      students: '57M+',
      difficulty: 'Medium',
      processingTime: '2-3 business days',
      status: 'available'
    },
    {
      id: 'coursera',
      name: 'Coursera',
      description: 'Partner with universities and top companies',
      logo: '🏛️',
      category: 'corporate',
      commission: 30,
      features: ['University partnerships', 'Degrees', 'Professional certificates', 'High prestige'],
      requirements: ['Expert credentials', 'Academic quality', 'Extensive review process'],
      supportedFormats: ['MP4', 'PDF', 'Peer review', 'Projects'],
      avgEarnings: '$2,500/month',
      students: '118M+',
      difficulty: 'Hard',
      processingTime: '4-6 weeks',
      status: 'available'
    },
    {
      id: 'skillshare',
      name: 'Skillshare',
      description: 'Creative and business skills community',
      logo: '🎨',
      category: 'open',
      commission: 40,
      features: ['Creative focus', 'Project-based', 'Community', 'Royalties'],
      requirements: ['Creative content', 'Project component', 'Minimum 10 minutes'],
      supportedFormats: ['MP4', 'Project files', 'Resources'],
      avgEarnings: '$800/month',
      students: '12M+',
      difficulty: 'Easy',
      processingTime: '1-2 business days',
      status: 'connected'
    },
    {
      id: 'linkedin',
      name: 'LinkedIn Learning',
      description: 'Professional development and business skills',
      logo: '💼',
      category: 'corporate',
      commission: 25,
      features: ['Professional network', 'Enterprise sales', 'LinkedIn integration', 'High value'],
      requirements: ['Industry expertise', 'Professional quality', 'Business focus'],
      supportedFormats: ['MP4', 'Exercise files', 'Transcripts'],
      avgEarnings: '$3,200/month',
      students: '27M+',
      difficulty: 'Hard',
      processingTime: '3-4 weeks',
      status: 'pending'
    },
    {
      id: 'teachable',
      name: 'Teachable',
      description: 'Create your own branded course website',
      logo: '🏫',
      category: 'open',
      commission: 5,
      features: ['No commission', 'Full control', 'Custom branding', 'Direct payments'],
      requirements: ['Own marketing', 'Course completion'],
      supportedFormats: ['MP4', 'PDF', 'Audio', 'Text'],
      avgEarnings: '$1,800/month',
      students: '1M+',
      difficulty: 'Medium',
      processingTime: 'Instant',
      status: 'available'
    },
    {
      id: 'pluralsight',
      name: 'Pluralsight',
      description: 'Technology and creative skills platform',
      logo: '💻',
      category: 'corporate',
      commission: 35,
      features: ['Tech focus', 'Skill assessments', 'Learning paths', 'Analytics'],
      requirements: ['Technical expertise', 'Industry experience', 'Quality standards'],
      supportedFormats: ['MP4', 'Code demos', 'Assessments'],
      avgEarnings: '$2,800/month',
      students: '17M+',
      difficulty: 'Hard',
      processingTime: '2-4 weeks',
      status: 'available'
    }
  ];

  const mockPublishedCourses: PublishedCourse[] = [
    {
      id: 1,
      title: 'Complete Web Development Bootcamp',
      marketplace: 'Udemy',
      status: 'published',
      students: 12450,
      revenue: 24900,
      rating: 4.7,
      lastUpdated: '2025-06-20'
    },
    {
      id: 2,
      title: 'Digital Marketing Masterclass',
      marketplace: 'Skillshare',
      status: 'published',
      students: 3200,
      revenue: 8500,
      rating: 4.5,
      lastUpdated: '2025-06-22'
    },
    {
      id: 3,
      title: 'Data Science with Python',
      marketplace: 'LinkedIn Learning',
      status: 'pending',
      students: 0,
      revenue: 0,
      rating: 0,
      lastUpdated: '2025-06-24'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'connected': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'premium': return 'bg-purple-100 text-purple-800';
      case 'corporate': return 'bg-blue-100 text-blue-800';
      case 'open': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handlePublishCourse = () => {
    if (!selectedMarketplace || !selectedCourse) return;

    publishCourseMutation.mutate({
      courseId: selectedCourse,
      marketplaceId: selectedMarketplace.id
    });
  };

  if (coursesLoading || analyticsLoading || marketplacesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Course Marketplaces</h1>
          <p className="text-muted-foreground mt-2">
            Publish your courses to top marketplaces and maximize your reach
          </p>
        </div>
        <Button onClick={() => setSelectedMarketplace({} as Marketplace)}>
          <Upload className="h-4 w-4 mr-2" />
          Publish Course
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="marketplaces">Marketplaces</TabsTrigger>
          <TabsTrigger value="published">Published Courses</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Revenue Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$33,400</div>
                <p className="text-xs text-muted-foreground">+12.5% from last month</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">15,650</div>
                <p className="text-xs text-muted-foreground">+25.2% from last month</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Published Courses</CardTitle>
                <ShoppingBag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{mockPublishedCourses.length}</div>
                <p className="text-xs text-muted-foreground">Across 3 platforms</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.6</div>
                <p className="text-xs text-muted-foreground">Based on 450+ reviews</p>
              </CardContent>
            </Card>
          </div>

          {/* Top Performing Courses */}
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Courses</CardTitle>
              <CardDescription>Your best performing courses across all marketplaces</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockPublishedCourses.filter(c => c.status === 'published').map((course) => (
                  <div key={course.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                        {course.title.charAt(0)}
                      </div>
                      <div>
                        <h3 className="font-semibold">{course.title}</h3>
                        <p className="text-sm text-muted-foreground">{course.marketplace}</p>
                      </div>
                    </div>
                    <div className="text-right space-y-1">
                      <div className="flex items-center space-x-4">
                        <span className="text-sm font-medium">{course.students.toLocaleString()} students</span>
                        <span className="text-sm font-medium">${course.revenue.toLocaleString()}</span>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-500 mr-1" />
                          <span className="text-sm">{course.rating}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="marketplaces" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableMarketplaces.map((marketplace) => (
              <Card key={marketplace.id} className="relative hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-3xl">{marketplace.logo}</span>
                      <div>
                        <CardTitle className="text-xl">{marketplace.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className={getCategoryColor(marketplace.category)}>
                            {marketplace.category}
                          </Badge>
                          <Badge className={getStatusColor(marketplace.status)}>
                            {marketplace.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{marketplace.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Commission:</span>
                      <p className="text-muted-foreground">{marketplace.commission}%</p>
                    </div>
                    <div>
                      <span className="font-medium">Students:</span>
                      <p className="text-muted-foreground">{marketplace.students}</p>
                    </div>
                    <div>
                      <span className="font-medium">Avg Earnings:</span>
                      <p className="text-muted-foreground">{marketplace.avgEarnings}</p>
                    </div>
                    <div>
                      <span className="font-medium">Difficulty:</span>
                      <p className="text-muted-foreground">{marketplace.difficulty}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Key Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {marketplace.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {marketplace.features.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{marketplace.features.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Requirements</h4>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      {marketplace.requirements.slice(0, 2).map((req, index) => (
                        <li key={index} className="flex items-center">
                          <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                          {req}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      className="flex-1"
                      onClick={() => setSelectedMarketplace(marketplace)}
                      disabled={marketplace.status === 'pending'}
                    >
                      {marketplace.status === 'connected' ? (
                        <>
                          <Upload className="h-4 w-4 mr-1" />
                          Publish Course
                        </>
                      ) : marketplace.status === 'pending' ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          Pending
                        </>
                      ) : (
                        <>
                          <ExternalLink className="h-4 w-4 mr-1" />
                          Learn More
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="published" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Published Courses</CardTitle>
              <CardDescription>Manage your courses across all marketplaces</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockPublishedCourses.map((course) => (
                  <div key={course.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold">
                        {course.title.charAt(0)}
                      </div>
                      <div>
                        <h3 className="font-semibold">{course.title}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-sm text-muted-foreground">{course.marketplace}</span>
                          <Badge className={getStatusColor(course.status)}>
                            {course.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <p className="text-sm font-medium">{course.students.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">Students</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium">${course.revenue.toLocaleString()}</p>
                        <p className="text-xs text-muted-foreground">Revenue</p>
                      </div>
                      {course.rating > 0 && (
                        <div className="text-center">
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-500 mr-1" />
                            <span className="text-sm font-medium">{course.rating}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">Rating</p>
                        </div>
                      )}
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue by Platform</CardTitle>
                <CardDescription>Monthly revenue breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { platform: 'Udemy', revenue: 24900, percentage: 74 },
                    { platform: 'Skillshare', revenue: 8500, percentage: 26 },
                    { platform: 'LinkedIn Learning', revenue: 0, percentage: 0 }
                  ].map((item) => (
                    <div key={item.platform} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{item.platform}</span>
                        <span className="font-medium">${item.revenue.toLocaleString()}</span>
                      </div>
                      <Progress value={item.percentage} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Student Enrollment</CardTitle>
                <CardDescription>Student growth across platforms</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { platform: 'Udemy', students: 12450, growth: '+15%' },
                    { platform: 'Skillshare', students: 3200, growth: '+8%' },
                    { platform: 'LinkedIn Learning', students: 0, growth: '0%' }
                  ].map((item) => (
                    <div key={item.platform} className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">{item.platform}</p>
                        <p className="text-sm text-muted-foreground">{item.students.toLocaleString()} students</p>
                      </div>
                      <Badge variant="secondary" className="text-green-600">
                        {item.growth}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Publish Course Modal */}
      {selectedMarketplace && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Publish to {selectedMarketplace.name}</CardTitle>
              <CardDescription>Select a course to publish to this marketplace</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="course">Select Course</Label>
                <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a course to publish" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="course1">Complete Web Development Bootcamp</SelectItem>
                    <SelectItem value="course2">Digital Marketing Masterclass</SelectItem>
                    <SelectItem value="course3">Data Science with Python</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="bg-muted p-3 rounded-lg">
                <h4 className="font-medium text-sm mb-2">Marketplace Requirements</h4>
                <ul className="text-xs space-y-1">
                  {selectedMarketplace.requirements?.map((req, index) => (
                    <li key={index} className="flex items-center">
                      <CheckCircle className="h-3 w-3 mr-2 text-green-500" />
                      {req}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setSelectedMarketplace(null)}
                >
                  Cancel
                </Button>
                <Button 
                  className="flex-1"
                  onClick={handlePublishCourse}
                  disabled={!selectedCourse || publishCourseMutation.isPending}
                >
                  {publishCourseMutation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Publish
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}