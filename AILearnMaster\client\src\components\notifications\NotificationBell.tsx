import { useState } from 'react';
import { Bell } from 'lucide-react';
import { Badge } from '../../components/ui/badge';
import NotificationsList from '../../components/notifications/NotificationsList';
import { useNotifications } from '../../hooks/use-notifications';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../components/ui/popover';
import { Button } from '../../components/ui/button';

interface NotificationBellProps {
  className?: string;
}

export default function NotificationBell({ className }: NotificationBellProps) {
  const [open, setOpen] = useState(false);
  const { 
    notificationCount, 
    notifications, 
    isLoading, 
    markAsReadMutation, 
    markAllAsReadMutation,
    deleteNotificationMutation
  } = useNotifications();
  
  // Handle marking a notification as read
  const handleMarkAsRead = (id: number) => {
    markAsReadMutation.mutate(id);
  };
  
  // <PERSON>le marking all notifications as read
  const handleMarkAllAsRead = () => {
    markAllAsReadMutation.mutate();
  };
  
  // Handle deleting a notification
  const handleDelete = (id: number) => {
    deleteNotificationMutation.mutate(id);
  };
  
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className={className}>
          <Bell className="h-5 w-5" />
          {notificationCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 min-w-[18px] h-[18px] flex items-center justify-center text-[10px] px-[5px] py-0"
            >
              {notificationCount > 99 ? '99+' : notificationCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0 mr-4">
        <div className="flex items-center justify-between p-3 border-b">
          <h3 className="text-sm font-medium">Notifications</h3>
          {notificationCount > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="text-xs text-blue-500 hover:text-blue-700"
              disabled={markAllAsReadMutation.isPending}
            >
              Mark all as read
            </button>
          )}
        </div>
        <NotificationsList
          notifications={notifications || []}
          onMarkAsRead={handleMarkAsRead}
          onDelete={handleDelete}
          isLoading={isLoading || markAllAsReadMutation.isPending}
          maxHeight="400px"
        />
        <div className="p-2 border-t text-center">
          <a 
            href="/notifications"
            className="text-xs text-primary hover:underline"
            onClick={() => setOpen(false)}
          >
            View all notifications
          </a>
        </div>
      </PopoverContent>
    </Popover>
  );
}
