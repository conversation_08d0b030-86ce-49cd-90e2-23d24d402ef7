import React, { useState } from "react";
import { useLocation } from "wouter";
import { HorizontalStepIndicator } from "@/components/course-creator/HorizontalStepIndicator";
import { CourseDetailsForm } from "@/components/course-creator/CourseDetailsForm";
import { CoursePublishForm } from "@/components/course-creator/CoursePublishForm";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

// Import avatar-specific components
import { AvatarSelection } from "@/components/course-creator/AvatarSelection";
import { AvatarScriptCreator } from "@/components/course-creator/AvatarScriptCreator";
import { SimpleVoiceSelector } from "@/components/avatar/SimpleVoiceSelector";
import { CoursePreviewGenerator } from "@/components/avatar/CoursePreviewGenerator";
import { Layout, User, FileText, Mic, Play, Globe } from "lucide-react";

// Define the possible steps in the avatar course creation flow
type Step = 'details' | 'avatar' | 'script' | 'voice' | 'preview' | 'publish';

// Define the steps for our horizontal step indicator
const steps = [
  {
    id: 'details',
    title: 'Course Details',
    description: 'Basic information about your course',
    icon: <Layout className="h-4 w-4" />
  },
  {
    id: 'avatar',
    title: 'Select Avatar',
    description: 'Choose your AI presenter',
    icon: <User className="h-4 w-4" />
  },
  {
    id: 'script',
    title: 'Write Script',
    description: 'Create engaging content',
    icon: <FileText className="h-4 w-4" />
  },
  {
    id: 'voice',
    title: 'Voice Settings',
    description: 'Configure audio narration',
    icon: <Mic className="h-4 w-4" />
  },
  {
    id: 'preview',
    title: 'Preview',
    description: 'Review your avatar course',
    icon: <Play className="h-4 w-4" />
  },
  {
    id: 'publish',
    title: 'Publish',
    description: 'Launch your course',
    icon: <Globe className="h-4 w-4" />
  },
];

export default function AvatarCoursePage() {
  const [_, navigate] = useLocation();
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState<Step>('details');

  // Function to determine which steps are completed
  const getCompletedSteps = (): number[] => {
    const completed: number[] = [];
    const currentIndex = steps.findIndex(step => step.id === currentStep);
    
    // All steps before current are considered completed
    for (let i = 0; i < currentIndex; i++) {
      completed.push(i);
    }
    
    return completed;
  };

  const [courseData, setCourseData] = useState({
    title: '',
    description: '',
    category: '',
    format: 'avatar',
    level: 'beginner',
    microLearningEnabled: false,
    courseImage: null,
  });
  
  const [avatarData, setAvatarData] = useState({
    avatarId: '',
    avatarName: '',
    avatarImageUrl: '',
  });
  
  const [scriptData, setScriptData] = useState({
    content: '',
    segments: [],
  });
  
  const [voiceData, setVoiceData] = useState({
    voiceId: '',
    voiceName: '',
    speed: 1,
    pitch: 1,
  });
  
  const [previewData, setPreviewData] = useState({
    videoUrl: '',
    thumbnailUrl: '',
  });
  
  const [publishData, setPublishData] = useState({});

  // Simple course structure for avatar courses (single section, single lesson)
  const courseStructure = {
    sections: [
      {
        id: 1,
        title: "Main Content",
        lessons: [
          {
            id: 1,
            title: "Video Presentation",
            type: "video",
            content: "",
            duration: 0,
          }
        ]
      }
    ]
  };

  // Function to handle moving to the next step
  const handleNext = () => {
    const currentIndex = steps.findIndex(step => step.id === currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1].id as Step);
      window.scrollTo(0, 0);
    }
  };

  // Function to handle moving to the previous step
  const handleBack = () => {
    const currentIndex = steps.findIndex(step => step.id === currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1].id as Step);
      window.scrollTo(0, 0);
    } else {
      // If we're at the first step, go back to the course creation selection page
      navigate('/create');
    }
  };

  // Handle submission of the course details form
  const handleCourseDetailsSubmit = (data: any) => {
    setCourseData({
      ...courseData,
      ...data,
      format: 'avatar' // Ensure format is set to avatar
    });
    handleNext();
  };

  // Handle avatar selection
  const handleAvatarSubmit = (data: any) => {
    setAvatarData(data);
    handleNext();
  };

  // Handle script creation
  const handleScriptSubmit = (data: any) => {
    setScriptData(data);
    handleNext();
  };

  // Handle voice settings
  const handleVoiceSubmit = (data: any) => {
    setVoiceData(data);
    handleNext();
  };

  // Handle preview generation and review
  const handlePreviewSubmit = (data: any) => {
    setPreviewData(data);
    handleNext();
  };

  // Handle final submission of the course
  const handlePublishSubmit = async (data: any) => {
    try {
      setPublishData(data);
      
      // Combine all data for the avatar course
      const finalCourseData = {
        ...courseData,
        format: 'avatar',
        avatar: avatarData,
        script: scriptData,
        voice: voiceData,
        preview: previewData,
        ...data
      };
      
      // Submit to the API
      const response = await apiRequest('POST', '/api/courses', finalCourseData);
      
      if (response.ok) {
        const result = await response.json();
        
        toast({
          title: "Success!",
          description: data.isPublished 
            ? "Your avatar course has been published successfully." 
            : "Your avatar course has been saved as a draft.",
        });
        
        // Navigate to course view
        navigate(`/my-courses/${result.id}`);
      } else {
        throw new Error('Failed to create course');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save your course. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="px-6 py-8">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={handleBack}
              className="h-8 w-8 rounded-full"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-3xl font-bold tracking-tight text-foreground">Create Avatar Course</h1>
          </div>
          <p className="text-muted-foreground">
            Create an engaging video course with an AI-powered avatar presenter
          </p>
        </div>
      </div>

      <div className="mb-8">
        <HorizontalStepIndicator 
          steps={steps} 
          activeStep={steps.findIndex(step => step.id === currentStep)}
          completedSteps={getCompletedSteps()}
          onStepClick={(stepIndex) => {
            // Only allow clicking on completed steps
            const currentIndex = steps.findIndex(s => s.id === currentStep);
            if (stepIndex < currentIndex) {
              setCurrentStep(steps[stepIndex].id as Step);
              window.scrollTo(0, 0);
            }
          }} 
        />
      </div>



      <div className="bg-white rounded-lg shadow-sm border p-6">
        {/* Render the appropriate component based on the current step */}
        {currentStep === 'details' && (
          <CourseDetailsForm 
            onSubmit={handleCourseDetailsSubmit}
            defaultValues={courseData}
          />
        )}
        {currentStep === 'avatar' && (
          <div>
            <Button 
              variant="ghost" 
              className="mb-4"
              onClick={handleBack}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <AvatarSelection 
              onAvatarSelect={(avatarData) => {
                handleAvatarSubmit({
                  avatarId: avatarData.avatarImageUrl || 'generated-avatar',
                  avatarName: 'AI Generated Avatar',
                  avatarImageUrl: avatarData.avatarImageUrl,
                  avatarType: avatarData.avatarType,
                  characteristics: avatarData.characteristics,
                  sourcePhoto: avatarData.sourcePhoto
                });
              }}
              initialData={avatarData}
            />
          </div>
        )}
        {currentStep === 'script' && (
          <AvatarScriptCreator 
            onSubmit={handleScriptSubmit}
            onBack={handleBack}
            courseData={courseData}
            avatarData={avatarData}
            defaultValues={scriptData}
          />
        )}
        {currentStep === 'voice' && (
          <SimpleVoiceSelector 
            onSubmit={handleVoiceSubmit}
            onBack={handleBack}
            defaultValues={voiceData}
            scriptData={scriptData}
          />
        )}
        {currentStep === 'preview' && (
          <CoursePreviewGenerator
            onSubmit={handlePreviewSubmit}
            onBack={handleBack}
            courseData={courseData}
            avatarData={avatarData}
            scriptData={scriptData}
            voiceData={voiceData}
          />
        )}
        {currentStep === 'publish' && (
          <CoursePublishForm 
            onSubmit={handlePublishSubmit}
            onBack={handleBack}
            courseStructure={courseStructure}
            courseDetails={courseData}
          />
        )}
      </div>
    </div>
  );
}