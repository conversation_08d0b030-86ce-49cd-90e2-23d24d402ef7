/**
 * Modal A100 GPU Integration Service
 * Connects Course AI Platform with Modal A100 GPU functions
 */

import { spawn } from 'child_process';
import { join } from 'path';

export interface A100GenerationOptions {
  voice: string;
  temperature: number;
  silenceDuration: number;
  format: 'wav' | 'mp3';
}

export interface A100BatchRequest {
  lessons: Array<{
    title: string;
    text: string;
    moduleId?: string;
    lessonId?: string;
  }>;
  voicePreset: string;
}

class ModalA100Service {
  private async executeModalFunction(functionName: string, params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const modalProcess = spawn('modal', [
        'run',
        'modal_a100_app.py::' + functionName,
        '--data', JSON.stringify(params)
      ]);

      let stdout = '';
      let stderr = '';

      modalProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      modalProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      modalProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(stdout.trim());
            resolve(result);
          } catch (parseError) {
            reject(new Error(`Failed to parse Modal response: ${parseError}`));
          }
        } else {
          reject(new Error(`Modal function failed: ${stderr || stdout}`));
        }
      });

      modalProcess.on('error', (error) => {
        reject(new Error(`Modal process error: ${error.message}`));
      });
    });
  }

  async generateHighQualityTTS(
    text: string,
    options: A100GenerationOptions
  ): Promise<Buffer> {
    try {
      const params = {
        text,
        voice_preset: options.voice,
        temperature: options.temperature,
        silence_duration: options.silenceDuration
      };

      const audioBase64 = await this.executeModalFunction('high_quality_tts_a100', params);
      return Buffer.from(audioBase64, 'base64');
    } catch (error) {
      console.error('A100 TTS generation failed:', error);
      throw new Error('Failed to generate high-quality speech');
    }
  }

  async generateBatchTTS(request: A100BatchRequest): Promise<Array<{
    title: string;
    audioData: string;
    format: string;
    sampleRate: number;
    durationSeconds: number;
    sizeBytes: number;
    moduleId?: string;
    lessonId?: string;
  }>> {
    try {
      const params = {
        lesson_texts: request.lessons,
        voice_preset: request.voicePreset
      };

      const results = await this.executeModalFunction('batch_tts_a100', params);
      return results;
    } catch (error) {
      console.error('A100 batch TTS failed:', error);
      throw new Error('Failed to generate batch narration');
    }
  }

  async generateLargeModelResponse(
    modelName: string,
    prompt: string,
    maxTokens: number = 512,
    temperature: number = 0.7
  ): Promise<string> {
    try {
      const params = {
        model_name: modelName,
        prompt,
        max_tokens: maxTokens,
        temperature
      };

      const response = await this.executeModalFunction('large_model_inference_a100', params);
      return response;
    } catch (error) {
      console.error('A100 model inference failed:', error);
      throw new Error('Failed to generate model response');
    }
  }

  async generateImage(
    prompt: string,
    width: number = 1024,
    height: number = 1024,
    steps: number = 50
  ): Promise<string> {
    try {
      const params = {
        prompt,
        width,
        height,
        num_inference_steps: steps,
        guidance_scale: 7.5
      };

      const imageBase64 = await this.executeModalFunction('image_generation_a100', params);
      return imageBase64;
    } catch (error) {
      console.error('A100 image generation failed:', error);
      throw new Error('Failed to generate image');
    }
  }

  async cloneVoice(
    sampleAudioBase64: string,
    targetText: string,
    voiceName: string = 'custom_voice'
  ): Promise<Buffer> {
    try {
      const params = {
        sample_audio_base64: sampleAudioBase64,
        target_text: targetText,
        voice_name: voiceName
      };

      const audioBase64 = await this.executeModalFunction('voice_cloning_a100', params);
      return Buffer.from(audioBase64, 'base64');
    } catch (error) {
      console.error('A100 voice cloning failed:', error);
      throw new Error('Failed to clone voice');
    }
  }

  async checkA100Health(): Promise<{
    gpu_available: boolean;
    gpu_name?: string;
    gpu_memory_total?: number;
    cuda_version?: string;
  }> {
    try {
      const health = await this.executeModalFunction('health_check_a100', {});
      return health;
    } catch (error) {
      console.error('A100 health check failed:', error);
      return { gpu_available: false };
    }
  }

  async getAvailableVoices(): Promise<Array<{
    id: string;
    name: string;
    gender: string;
    language: string;
  }>> {
    try {
      const voices = await this.executeModalFunction('list_available_voices_a100', {});
      return voices;
    } catch (error) {
      console.error('Failed to get A100 voices:', error);
      return [];
    }
  }
}

export const modalA100Service = new ModalA100Service();
