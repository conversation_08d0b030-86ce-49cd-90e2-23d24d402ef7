import { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  CardDescription, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>eader, 
  CardTitle 
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle2, 
  Clock, 
  ExternalLink, 
  Link, 
  Unlink,
  Settings,
  Bell
} from 'lucide-react';
import { PlatformIcon } from './platform-logos';
import { PlatformIntegration } from '@/types/platform-integration';
import PlatformConnectDialog from './platform-connect-dialog';
import FacebookConnectDialog from './facebook-connect-dialog';
import { formatLastSyncedTime } from '@/lib/platform-utils';

interface IntegrationCardProps {
  platform: PlatformIntegration;
  onConnect: () => void;
  onDisconnect: () => void;
}

export const PlatformFeatureItem = ({ icon, name, description }: { 
  icon: string, 
  name: string, 
  description: string 
}) => {
  // Map icon strings to Lucide icons or use a fallback
  const getIcon = () => {
    switch (icon) {
      case 'share':
        return <ExternalLink className="h-4 w-4 text-muted-foreground" />;
      case 'users':
        return <Link className="h-4 w-4 text-muted-foreground" />;
      case 'bar-chart':
      case 'line-chart':
        return <Settings className="h-4 w-4 text-muted-foreground" />;
      case 'upload':
        return <ExternalLink className="h-4 w-4 text-muted-foreground" />;
      case 'dollar-sign':
        return <Link className="h-4 w-4 text-muted-foreground" />;
      case 'video':
        return <ExternalLink className="h-4 w-4 text-muted-foreground" />;
      case 'trending-up':
        return <Link className="h-4 w-4 text-muted-foreground" />;
      case 'bell':
        return <Bell className="h-4 w-4 text-muted-foreground" />;
      default:
        return <Settings className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <div className="flex items-start space-x-2 py-1">
      {getIcon()}
      <div>
        <p className="text-sm font-medium">{name}</p>
        <p className="text-xs text-muted-foreground">{description}</p>
      </div>
    </div>
  );
};

export default function IntegrationCard({ platform, onConnect, onDisconnect }: IntegrationCardProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isFacebookDialogOpen, setIsFacebookDialogOpen] = useState(false);
  
  const handleConnect = () => {
    // Handle Facebook separately with our custom OAuth dialog
    if (platform.slug === 'facebook') {
      setIsFacebookDialogOpen(true);
    } else {
      setIsDialogOpen(true);
    }
  };
  
  const handleConnectComplete = () => {
    setIsDialogOpen(false);
    setIsFacebookDialogOpen(false);
    onConnect();
  };
  
  const handleDisconnect = () => {
    onDisconnect();
  };

  return (
    <>
      <Card className="flex flex-col h-full">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <div className="bg-muted p-2 rounded-md">
                <PlatformIcon platform={platform.icon} size={20} />
              </div>
              <CardTitle className="text-lg">{platform.name}</CardTitle>
            </div>
            {platform.isConnected && (
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle2 className="h-3 w-3 mr-1" /> Connected
              </Badge>
            )}
          </div>
          <CardDescription className="mt-2">{platform.description}</CardDescription>
        </CardHeader>
        <CardContent className="flex-grow pb-0">
          <div className="space-y-2">
            {platform.isConnected && platform.lastSyncedAt && (
              <div className="text-xs text-muted-foreground flex items-center mb-3">
                <Clock className="h-3 w-3 mr-1" /> Last synced: {formatLastSyncedTime(platform.lastSyncedAt)}
              </div>
            )}
            
            <div className="space-y-1">
              {platform.features.map((feature, index) => (
                <PlatformFeatureItem 
                  key={index}
                  icon={feature.icon}
                  name={feature.name}
                  description={feature.description}
                />
              ))}
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-4">
          {platform.isConnected ? (
            <Button 
              variant="outline" 
              className="w-full border-red-200 text-red-700 hover:bg-red-50"
              onClick={handleDisconnect}
            >
              <Unlink className="h-4 w-4 mr-2" /> Disconnect
            </Button>
          ) : (
            <Button 
              variant="default" 
              className="w-full"
              onClick={handleConnect}
            >
              <Link className="h-4 w-4 mr-2" /> Connect
            </Button>
          )}
        </CardFooter>
      </Card>
      
      <PlatformConnectDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        platform={platform}
        onConnect={handleConnectComplete}
      />
      
      {/* Facebook-specific connect dialog */}
      {platform.slug === 'facebook' && (
        <FacebookConnectDialog
          isOpen={isFacebookDialogOpen}
          onClose={() => setIsFacebookDialogOpen(false)}
          onConnect={handleConnectComplete}
        />
      )}
    </>
  );
}

// Utility function to determine the appropriate description for a feature based on platform type
export function getFeatureDescriptionByType(platformType: string, feature: string): string {
  const descriptions: Record<string, Record<string, string>> = {
    social: {
      share: 'Share your courses on this platform',
      engage: 'Interact with your audience',
      analytics: 'Track engagement and performance',
    },
    education: {
      publish: 'Publish full courses on this platform',
      monetize: 'Sell your courses and earn revenue',
      analytics: 'Track student progress and ratings',
    },
    video: {
      publish: 'Upload your course videos',
      monetize: 'Earn revenue from your videos',
      grow: 'Build your audience with video content',
    },
    communication: {
      notifications: 'Send automatic course notifications',
      engagement: 'Create community spaces for discussions',
      alerts: 'Receive real-time alerts about your courses'
    },
  };
  
  return descriptions[platformType]?.[feature] || 'Feature not available';
}