{"timestamp": **********.2485127, "diagnosis_time": 111.55073595046997, "working_services": [{"service": "connectivity", "message": "App is reachable", "timestamp": **********.696055}], "issues": [{"service": "gpu", "issue": "GPU not available", "severity": "ERROR", "timestamp": **********.1379707}, {"service": "health", "issue": "'list' object has no attribute 'items'", "severity": "ERROR", "timestamp": **********.1381428}, {"service": "api-mistral", "issue": "Unknown error", "severity": "ERROR", "timestamp": **********.2157671}, {"service": "api-tts", "issue": "HTTPSConnectionPool(host='trade-digital--courseai-a100-simple-api-tts.modal.run', port=443): Read timed out. (read timeout=30)", "severity": "ERROR", "timestamp": **********.5345926}, {"service": "api-voices", "issue": "Unknown error", "severity": "ERROR", "timestamp": **********.4651065}, {"service": "api-slides", "issue": "Unknown error", "severity": "ERROR", "timestamp": **********.7377577}, {"service": "api-avatar", "issue": "cannot identify image file '/tmp/tmpkyb7cnux/ref_image.png'", "severity": "ERROR", "timestamp": **********.8023968}, {"service": "api-course-avatar", "issue": "HTTPSConnectionPool(host='trade-digital--courseai-a100-simple-api-course-avatar.modal.run', port=443): Read timed out. (read timeout=30)", "severity": "ERROR", "timestamp": **********.2007768}], "success_rate": 0.0}