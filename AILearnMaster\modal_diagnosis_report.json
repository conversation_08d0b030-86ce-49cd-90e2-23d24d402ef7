{"timestamp": **********.344174, "diagnosis_time": 71.89728116989136, "working_services": [{"service": "connectivity", "message": "App is reachable", "timestamp": **********.6667788}], "issues": [{"service": "gpu", "issue": "GPU not available", "severity": "ERROR", "timestamp": **********.103376}, {"service": "health", "issue": "'list' object has no attribute 'items'", "severity": "ERROR", "timestamp": **********.1035225}, {"service": "api-mistral", "issue": "Unknown error", "severity": "ERROR", "timestamp": **********.66075}, {"service": "api-tts", "issue": "Unknown error", "severity": "ERROR", "timestamp": **********.0975435}, {"service": "api-voices", "issue": "HTTP 405", "severity": "ERROR", "timestamp": **********.0416226}, {"service": "api-slides", "issue": "Markdown content is required", "severity": "ERROR", "timestamp": **********.6841226}, {"service": "api-avatar", "issue": "Reference image (ref_image_base64) is required", "severity": "ERROR", "timestamp": **********.5067635}, {"service": "api-course-avatar", "issue": "Text prompt is required", "severity": "ERROR", "timestamp": **********.3293319}], "success_rate": 0.0}