#!/usr/bin/env python3
"""
Comprehensive Final Test of Modal A100 GPU Deployment
Tests all available services and provides detailed analysis
"""

import requests
import json
import time
import base64
from typing import Dict, Any

# Modal endpoints
BASE_URL = "https://trade-digital--courseai-a100-simple"
ENDPOINTS = {
    "health": f"{BASE_URL}-health.modal.run",
    "mistral": f"{BASE_URL}-api-mistral.modal.run",
    "tts": f"{BASE_URL}-api-tts.modal.run",
    "voices": f"{BASE_URL}-api-voices.modal.run",
    "slides": f"{BASE_URL}-api-slides.modal.run",
    "avatar": f"{BASE_URL}-api-avatar.modal.run",
    "course_avatar": f"{BASE_URL}-api-course-avatar.modal.run"
}

class ModalTester:
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
    
    def test_health(self) -> bool:
        """Test health endpoint and GPU availability"""
        print("🔍 Testing Health & GPU Availability...")
        try:
            response = requests.get(ENDPOINTS["health"], timeout=30)
            if response.status_code == 200:
                data = response.json()
                self.results["health"] = {
                    "status": "success",
                    "data": data,
                    "gpu_available": data.get('gpu_available', False),
                    "gpu_name": data.get('gpu_name', 'Unknown'),
                    "response_time": response.elapsed.total_seconds()
                }
                
                gpu_available = data.get('gpu_available', False)
                print(f"✅ Health endpoint working")
                print(f"   Status: {data.get('status', 'unknown')}")
                print(f"   GPU Available: {gpu_available}")
                print(f"   GPU Name: {data.get('gpu_name', 'Unknown')}")
                
                if not gpu_available:
                    print("⚠️  WARNING: GPU not available - this will affect AI services")
                
                return True
            else:
                self.results["health"] = {"status": "error", "error": f"HTTP {response.status_code}"}
                print(f"❌ Health endpoint failed: {response.status_code}")
                return False
        except Exception as e:
            self.results["health"] = {"status": "error", "error": str(e)}
            print(f"❌ Health endpoint error: {e}")
            return False
    
    def test_mistral(self) -> bool:
        """Test Mistral LLM endpoint"""
        print("\n🔍 Testing Mistral LLM...")
        try:
            payload = {
                "prompt": "Hello! Please respond with exactly: 'Mistral test successful'",
                "max_tokens": 20,
                "temperature": 0.1
            }
            response = requests.post(ENDPOINTS["mistral"], json=payload, timeout=120)
            if response.status_code == 200:
                data = response.json()
                self.results["mistral"] = {
                    "status": data.get('status', 'unknown'),
                    "data": data,
                    "response_time": response.elapsed.total_seconds()
                }
                
                if data.get('status') == 'success':
                    generated = data.get('generated_text', '')
                    print(f"✅ Mistral LLM working")
                    print(f"   Generated: {generated[:100]}...")
                    print(f"   GPU Used: {data.get('gpu_used', False)}")
                    return True
                else:
                    error = data.get('error', 'Unknown error')
                    print(f"❌ Mistral error: {error}")
                    return False
            else:
                self.results["mistral"] = {"status": "error", "error": f"HTTP {response.status_code}"}
                print(f"❌ Mistral failed: {response.status_code}")
                return False
        except Exception as e:
            self.results["mistral"] = {"status": "error", "error": str(e)}
            print(f"❌ Mistral error: {e}")
            return False
    
    def test_tts(self) -> bool:
        """Test TTS endpoint"""
        print("\n🔍 Testing TTS...")
        try:
            payload = {
                "text": "Hello, this is a test of the text to speech system."
            }
            response = requests.post(ENDPOINTS["tts"], json=payload, timeout=120)
            if response.status_code == 200:
                data = response.json()
                self.results["tts"] = {
                    "status": data.get('status', 'unknown'),
                    "data": data,
                    "response_time": response.elapsed.total_seconds()
                }
                
                if data.get('status') == 'success':
                    audio_data = data.get('audio_base64', '')
                    print(f"✅ TTS working")
                    print(f"   Audio generated: {len(audio_data)} characters")
                    print(f"   Voice: {data.get('voice_id', 'Unknown')}")
                    return True
                else:
                    error = data.get('error', 'Unknown error')
                    print(f"❌ TTS error: {error}")
                    return False
            else:
                self.results["tts"] = {"status": "error", "error": f"HTTP {response.status_code}"}
                print(f"❌ TTS failed: {response.status_code}")
                return False
        except Exception as e:
            self.results["tts"] = {"status": "error", "error": str(e)}
            print(f"❌ TTS error: {e}")
            return False
    
    def test_voices(self) -> bool:
        """Test voice discovery endpoint"""
        print("\n🔍 Testing Voice Discovery...")
        try:
            response = requests.get(ENDPOINTS["voices"], timeout=60)
            if response.status_code == 200:
                data = response.json()
                self.results["voices"] = {
                    "status": data.get('status', 'unknown'),
                    "data": data,
                    "response_time": response.elapsed.total_seconds()
                }
                
                if data.get('status') == 'success':
                    voices = data.get('voices', [])
                    print(f"✅ Voice discovery working")
                    print(f"   Found {len(voices)} voices")
                    if voices:
                        print(f"   Sample voices: {voices[:3]}")
                    return True
                else:
                    error = data.get('error', 'Unknown error')
                    print(f"❌ Voice discovery error: {error}")
                    return False
            else:
                self.results["voices"] = {"status": "error", "error": f"HTTP {response.status_code}"}
                print(f"❌ Voice discovery failed: {response.status_code}")
                return False
        except Exception as e:
            self.results["voices"] = {"status": "error", "error": str(e)}
            print(f"❌ Voice discovery error: {e}")
            return False
    
    def test_slides(self) -> bool:
        """Test slide generation endpoint"""
        print("\n🔍 Testing Slide Generation...")
        try:
            payload = {
                "markdown": "# Test Slide\n\n## Introduction\n\nThis is a test slide for Modal deployment.\n\n- Point 1\n- Point 2\n- Point 3"
            }
            response = requests.post(ENDPOINTS["slides"], json=payload, timeout=120)
            if response.status_code == 200:
                data = response.json()
                self.results["slides"] = {
                    "status": data.get('status', 'unknown'),
                    "data": data,
                    "response_time": response.elapsed.total_seconds()
                }
                
                if data.get('status') == 'success':
                    slides_data = data.get('slides_base64', '')
                    print(f"✅ Slide generation working")
                    print(f"   Slides generated: {len(slides_data)} characters")
                    print(f"   Format: {data.get('format', 'Unknown')}")
                    return True
                else:
                    error = data.get('error', 'Unknown error')
                    print(f"❌ Slides error: {error}")
                    return False
            else:
                self.results["slides"] = {"status": "error", "error": f"HTTP {response.status_code}"}
                print(f"❌ Slides failed: {response.status_code}")
                return False
        except Exception as e:
            self.results["slides"] = {"status": "error", "error": str(e)}
            print(f"❌ Slides error: {e}")
            return False
    
    def generate_report(self):
        """Generate comprehensive test report"""
        end_time = time.time()
        duration = end_time - self.start_time
        
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE MODAL A100 TEST REPORT")
        print("=" * 60)
        
        # Count successes
        working_services = sum(1 for result in self.results.values() 
                             if result.get('status') == 'success')
        total_services = len(self.results)
        success_rate = (working_services / total_services * 100) if total_services > 0 else 0
        
        print(f"Test Duration: {duration:.1f} seconds")
        print(f"Services Tested: {total_services}")
        print(f"Working Services: {working_services}")
        print(f"Success Rate: {success_rate:.1f}%")
        
        # GPU Analysis
        gpu_available = self.results.get('health', {}).get('gpu_available', False)
        gpu_name = self.results.get('health', {}).get('gpu_name', 'Unknown')
        
        print(f"\n🔧 GPU STATUS:")
        if gpu_available:
            print(f"   ✅ GPU Available: {gpu_name}")
        else:
            print(f"   ❌ GPU Not Available: {gpu_name}")
        
        # Service Analysis
        print(f"\n📋 SERVICE STATUS:")
        for service, result in self.results.items():
            status = result.get('status', 'unknown')
            response_time = result.get('response_time', 0)
            
            if status == 'success':
                print(f"   ✅ {service.upper()}: Working ({response_time:.1f}s)")
            else:
                error = result.get('error', 'Unknown error')
                print(f"   ❌ {service.upper()}: Failed - {error}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        
        if not gpu_available:
            print("   🔧 GPU ISSUES:")
            print("      - Check Modal A100 GPU allocation")
            print("      - Verify function GPU configuration")
            print("      - Consider redeploying with explicit GPU settings")
        
        if working_services < total_services:
            print("   🔧 SERVICE ISSUES:")
            print("      - Review failed service logs")
            print("      - Check dependency installations")
            print("      - Verify endpoint configurations")
        
        if success_rate >= 80:
            print("   🎉 DEPLOYMENT STATUS: Good - Most services working")
        elif success_rate >= 50:
            print("   ⚠️  DEPLOYMENT STATUS: Partial - Some services need attention")
        else:
            print("   ❌ DEPLOYMENT STATUS: Poor - Major issues need fixing")
        
        # Save detailed report
        report_data = {
            "timestamp": int(time.time()),
            "duration": duration,
            "success_rate": success_rate,
            "gpu_available": gpu_available,
            "gpu_name": gpu_name,
            "services": self.results
        }
        
        with open("modal_comprehensive_test_report.json", "w") as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: modal_comprehensive_test_report.json")

def main():
    """Run comprehensive Modal A100 tests"""
    print("🚀 Modal A100 GPU Comprehensive Testing")
    print("=" * 60)
    
    tester = ModalTester()
    
    # Run all tests
    tests = [
        tester.test_health,
        tester.test_mistral,
        tester.test_tts,
        tester.test_voices,
        tester.test_slides
    ]
    
    for test in tests:
        try:
            test()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    # Generate final report
    tester.generate_report()

if __name__ == "__main__":
    main()
