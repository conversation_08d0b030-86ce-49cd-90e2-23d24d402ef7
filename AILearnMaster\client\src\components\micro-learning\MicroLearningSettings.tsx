import React from 'react';
import { Card, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ChevronDown, ChevronUp, HelpCircle, SplitSquareVertical, FileQuestion, Clock, Hourglass } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const microLearningSchema = z.object({
  microLearningEnabled: z.boolean().default(false),
  microLearningSegmentCount: z.number().min(2).max(10).default(4),
  microLearningBreakInterval: z.number().min(60).max(600).default(300),
  microLearningBreakDuration: z.number().min(15).max(120).default(30),
  microLearningKnowledgeChecks: z.boolean().default(true),
});

type MicroLearningFormValues = z.infer<typeof microLearningSchema>;

interface MicroLearningSettingsProps {
  initialValues?: Partial<MicroLearningFormValues>;
  onSave: (values: MicroLearningFormValues) => void;
  readOnly?: boolean;
}

export function MicroLearningSettings({ 
  initialValues, 
  onSave,
  readOnly = false
}: MicroLearningSettingsProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const { toast } = useToast();
  
  const defaultValues: MicroLearningFormValues = {
    microLearningEnabled: initialValues?.microLearningEnabled ?? false,
    microLearningSegmentCount: initialValues?.microLearningSegmentCount ?? 4,
    microLearningBreakInterval: initialValues?.microLearningBreakInterval ?? 300,
    microLearningBreakDuration: initialValues?.microLearningBreakDuration ?? 30,
    microLearningKnowledgeChecks: initialValues?.microLearningKnowledgeChecks ?? true,
  };
  
  const form = useForm<MicroLearningFormValues>({
    resolver: zodResolver(microLearningSchema),
    defaultValues,
  });
  
  const handleSubmit = (values: MicroLearningFormValues) => {
    onSave(values);
    toast({
      title: "Micro-learning settings saved",
      description: "Your micro-learning configuration has been updated.",
    });
  };
  
  const microLearningEnabled = form.watch("microLearningEnabled");
  
  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <SplitSquareVertical size={18} className="text-primary" />
            <CardTitle className="text-lg">Micro-Learning Mode</CardTitle>
          </div>
          <FormField
            control={form.control}
            name="microLearningEnabled"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between space-x-2 space-y-0">
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={readOnly}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>
        <CardDescription>
          Break content into bite-sized segments to improve learner retention and engagement.
        </CardDescription>
      </CardHeader>
      
      <Collapsible open={isOpen && microLearningEnabled} onOpenChange={setIsOpen} disabled={!microLearningEnabled}>
        <CollapsibleTrigger asChild disabled={!microLearningEnabled}>
          <Button 
            variant="ghost" 
            size="sm" 
            className={`w-full flex justify-between items-center ${!microLearningEnabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={!microLearningEnabled || readOnly}
          >
            Configure Micro-Learning
            {isOpen ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="px-4 pb-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <Alert className="bg-primary/10 border-primary/20">
                <SplitSquareVertical className="h-4 w-4 text-primary" />
                <AlertDescription>
                  Micro-learning divides content into smaller, focused segments with strategic breaks to improve retention.
                </AlertDescription>
              </Alert>
              
              <div className="grid sm:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <SplitSquareVertical size={16} className="text-primary" />
                    <h3 className="text-sm font-medium">Content Segmentation</h3>
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="microLearningSegmentCount"
                    render={({ field }) => (
                      <FormItem className="space-y-1">
                        <div className="flex items-center justify-between">
                          <FormLabel className="text-xs">Number of Segments</FormLabel>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle size={14} className="text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <p>Number of sections to divide the content into. More segments mean smaller, more digestible pieces.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <div className="flex items-center space-x-2">
                          <FormControl>
                            <Slider
                              value={[field.value]}
                              min={2}
                              max={10}
                              step={1}
                              onValueChange={(values: number[]) => field.onChange(values[0])}
                              disabled={readOnly}
                              className="w-[180px]"
                            />
                          </FormControl>
                          <span className="w-6 text-center text-sm">{field.value}</span>
                        </div>
                        <FormDescription className="text-xs">
                          Recommended: 3-5 segments for 10-15 minute lessons
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="microLearningKnowledgeChecks"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between space-x-2 space-y-0 border rounded-md p-2">
                        <div className="space-y-0.5">
                          <div className="flex items-center space-x-2">
                            <FileQuestion size={14} className="text-primary" />
                            <FormLabel className="text-sm cursor-pointer">Knowledge Checks</FormLabel>
                          </div>
                          <FormDescription className="text-xs">
                            Add quick quizzes between segments
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={readOnly}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Clock size={16} className="text-primary" />
                    <h3 className="text-sm font-medium">Break Settings</h3>
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="microLearningBreakInterval"
                    render={({ field }) => (
                      <FormItem className="space-y-1">
                        <div className="flex items-center justify-between">
                          <FormLabel className="text-xs">Break Interval (seconds)</FormLabel>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle size={14} className="text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <p>How often to pause the content for a quick break. For example, 300 seconds (5 minutes) means a break every 5 minutes.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <div className="flex items-center space-x-2">
                          <FormControl>
                            <Slider
                              value={[field.value]}
                              min={60}
                              max={600}
                              step={30}
                              onValueChange={(values: number[]) => field.onChange(values[0])}
                              disabled={readOnly}
                              className="w-[180px]"
                            />
                          </FormControl>
                          <span className="w-16 text-center text-sm">
                            {Math.floor(field.value / 60)}:{String(field.value % 60).padStart(2, '0')}
                          </span>
                        </div>
                        <FormDescription className="text-xs">
                          Recommended: Every 3-5 minutes
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="microLearningBreakDuration"
                    render={({ field }) => (
                      <FormItem className="space-y-1">
                        <div className="flex items-center justify-between">
                          <FormLabel className="text-xs">Break Duration (seconds)</FormLabel>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle size={14} className="text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <p>How long each break should last. This gives learners time to reflect on what they've learned.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <div className="flex items-center space-x-2">
                          <FormControl>
                            <Slider
                              value={[field.value]}
                              min={15}
                              max={120}
                              step={5}
                              onValueChange={(values: number[]) => field.onChange(values[0])}
                              disabled={readOnly}
                              className="w-[180px]"
                            />
                          </FormControl>
                          <span className="w-16 text-center text-sm">
                            {Math.floor(field.value / 60)}:{String(field.value % 60).padStart(2, '0')}
                          </span>
                        </div>
                        <FormDescription className="text-xs">
                          Recommended: 20-30 seconds per break
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-2 pt-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  type="button"
                  onClick={() => form.reset(defaultValues)}
                  disabled={readOnly}
                >
                  Reset
                </Button>
                <Button 
                  type="submit" 
                  size="sm"
                  disabled={!form.formState.isDirty || readOnly}
                >
                  Save Settings
                </Button>
              </div>
            </form>
          </Form>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
}