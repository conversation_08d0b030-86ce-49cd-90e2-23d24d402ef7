import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { 
  Users, 
  Search as SearchIcon, 
  Calendar, 
  MessagesSquare, 
  Video, 
  FileEdit, 
  Folder,
  Eye,
  Clock,
  Filter,
  Plus,
  Share2,
  Crown
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Card, 
  CardContent, 
  CardFooter, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { queryClient, apiRequest } from '@/lib/queryClient';
import EnterpriseUpgradeModal from '@/components/stripe/enterprise-upgrade-modal';
import AddCollaboratorDialog from '@/components/collaboration/add-collaborator-dialog';

// Collaboration type
type Collaborator = {
  id: number;
  name: string;
  username: string;
  avatarUrl: string | null;
  role: string;
};

type Course = {
  id: number;
  title: string;
  description: string;
  status: string;
  category: string;
  thumbnailUrl: string | null;
  userId: number;
  completion: number;
  createdAt: Date;
  updatedAt: Date;
  // Collaboration-specific properties
  collaborationRole?: string;
  collaborationCanEdit?: boolean;
  collaborationAddedAt?: Date;
  owner?: {
    id: number;
    name: string;
    username: string;
    avatarUrl: string | null;
  };
  collaborators?: Collaborator[];
  lastActivity?: Date;
};

// Create a component for the colorful collaboration status badge
function CollaborationRoleBadge({ role, canEdit }: { role: string | undefined, canEdit: boolean | undefined }) {
  let color = "bg-slate-100 text-slate-700";
  let label = "Viewer";
  
  if (role === 'editor' && canEdit) {
    color = "bg-emerald-100 text-emerald-700";
    label = "Editor";
  } else if (role === 'reviewer') {
    color = "bg-amber-100 text-amber-700";
    label = "Reviewer";
  } else if (role === 'admin') {
    color = "bg-blue-100 text-blue-700";
    label = "Admin";
  }
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}>
      {label}
    </span>
  );
}

function CollaborationCard({ course }: { course: Course }) {
  const [_, setLocation] = useLocation();
  
  // Format the date to a readable string
  const formatDate = (date: Date | undefined) => {
    if (!date) return 'Unknown date';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Handle clicking on the course to view it
  const handleViewCourse = () => {
    setLocation(`/courses/${course.id}`);
  };
  
  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative">
        {course.thumbnailUrl ? (
          <div 
            className="h-40 bg-cover bg-center" 
            style={{ backgroundImage: `url(${course.thumbnailUrl})` }}
          />
        ) : (
          <div className="h-40 bg-gradient-to-r from-slate-100 to-slate-200 flex items-center justify-center">
            <Folder className="h-16 w-16 text-slate-400" />
          </div>
        )}
        <div className="absolute top-3 right-3">
          <CollaborationRoleBadge
            role={course.collaborationRole}
            canEdit={course.collaborationCanEdit}
          />
        </div>
      </div>
      
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg font-semibold line-clamp-1">
            {course.title}
          </CardTitle>
        </div>
        <CardDescription className="line-clamp-2">
          {course.description}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="pb-2">
        <div className="flex items-center mb-3">
          <div className="flex items-center space-x-1">
            <Clock className="h-4 w-4 text-slate-400" />
            <span className="text-xs text-slate-500">
              Updated {formatDate(course.updatedAt)}
            </span>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between items-center text-sm">
            <span>Progress</span>
            <span className="font-medium">{course.completion || 0}%</span>
          </div>
          <Progress value={course.completion || 0} className="h-2" />
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between pt-2">
        <div className="flex items-center space-x-2">
          {course.owner && (
            <Avatar className="h-6 w-6">
              <AvatarImage src={course.owner.avatarUrl || ''} alt={course.owner.name} />
              <AvatarFallback>{course.owner.name.charAt(0)}</AvatarFallback>
            </Avatar>
          )}
          <span className="text-sm text-slate-600">
            {course.owner?.name || 'Unknown'}
          </span>
        </div>
        
        <Button size="sm" onClick={handleViewCourse}>
          {course.collaborationCanEdit ? (
            <>
              <FileEdit className="mr-1 h-4 w-4" /> Edit
            </>
          ) : (
            <>
              <Eye className="mr-1 h-4 w-4" /> View
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

export default function CollaborationsPage() {
  const [_, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);
  const [isAddCollaboratorOpen, setIsAddCollaboratorOpen] = useState(false);
  const { toast } = useToast();

  // Check user authentication and plan
  const { data: user } = useQuery({
    queryKey: ['/api/auth/me'],
  });

  // Check if user has Enterprise subscription
  const userTyped = user as any;
  const isEnterprise = userTyped?.subscription === 'enterprise';

  // Show Enterprise upgrade notice if not Enterprise user
  if (user && !isEnterprise) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Crown className="w-8 h-8 text-yellow-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Enterprise Feature</h2>
            <p className="text-gray-600 mb-6">
              Shared Courses are available exclusively for Enterprise plan users. Upgrade your subscription to access collaborative course management, shared editing capabilities, and advanced team coordination features.
            </p>
            <Button 
              onClick={() => setIsUpgradeModalOpen(true)}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              Upgrade to Enterprise
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Fetch collaborations where the user is a collaborator
  const { 
    data: collaborations,
    isLoading, 
    error 
  } = useQuery<Course[]>({
    queryKey: ["/api/my-collaborations"],
    enabled: isEnterprise
  });

  // Filter collaborations based on search query, active tab, and status filter
  const filteredCollaborations = collaborations?.filter((course) => {
    const matchesSearch = searchQuery === "" || 
      course.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      course.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesTab = 
      (activeTab === "all") || 
      (activeTab === "edit" && course.collaborationCanEdit) || 
      (activeTab === "view" && !course.collaborationCanEdit);
    
    const matchesStatus = 
      !statusFilter || 
      course.status === statusFilter;
    
    return matchesSearch && matchesTab && matchesStatus;
  });

  // Function to navigate to the create meeting page
  const navigateToMeetings = () => {
    setLocation('/meetings');
  };

  return (
    <div className="container max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold gradient-heading">My Collaborations</h1>
          <p className="text-slate-600 mt-1">Courses shared with you by other users</p>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={() => setIsAddCollaboratorOpen(true)}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
          >
            <Share2 className="w-4 h-4 mr-2" />
            Share Course
          </Button>
          <Button
            variant="outline"
            onClick={() => setLocation('/pricing')}
            className="border-blue-200 text-blue-600 hover:bg-blue-50"
          >
            View Pricing
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Tabs and Filters */}
        <div className="flex flex-col space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">All Collaborations</TabsTrigger>
              <TabsTrigger value="edit">Can Edit</TabsTrigger>
              <TabsTrigger value="view">View Only</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative flex-grow">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search collaborations..."
                className="pl-10"
              />
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="shrink-0">
                  <Filter className="mr-2 h-4 w-4" />
                  {statusFilter ? `Status: ${statusFilter}` : 'Filter Status'}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => setStatusFilter(null)}>
                  All Statuses
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('draft')}>
                  Draft
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('published')}>
                  Published
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('archived')}>
                  Archived
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Collaboration Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="bg-blue-100 p-2 rounded-full mr-3">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-500">Total Collaborations</p>
                  <h3 className="text-2xl font-bold">{collaborations?.length || 0}</h3>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="bg-emerald-100 p-2 rounded-full mr-3">
                  <FileEdit className="h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-500">Can Edit</p>
                  <h3 className="text-2xl font-bold">
                    {collaborations?.filter(c => c.collaborationCanEdit)?.length || 0}
                  </h3>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="bg-amber-100 p-2 rounded-full mr-3">
                  <Eye className="h-5 w-5 text-amber-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-500">View Only</p>
                  <h3 className="text-2xl font-bold">
                    {collaborations?.filter(c => !c.collaborationCanEdit)?.length || 0}
                  </h3>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="bg-purple-100 p-2 rounded-full mr-3">
                  <Calendar className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-500">Recent Activity</p>
                  <h3 className="text-2xl font-bold">
                    {collaborations?.filter(c => {
                      const oneWeekAgo = new Date();
                      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
                      return new Date(c.updatedAt) > oneWeekAgo;
                    })?.length || 0}
                  </h3>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Access Section */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">Collaboration Tools</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6 flex items-start space-x-4">
                <div>
                  <div className="p-3 rounded-full bg-gradient-to-br from-blue-500 to-blue-600">
                    <Video className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">Video Meetings</h3>
                  <p className="text-sm text-slate-500">Create or join video conferences to collaborate in real-time</p>
                  <Button variant="outline" size="sm" onClick={navigateToMeetings}>
                    Create Meeting
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6 flex items-start space-x-4">
                <div>
                  <div className="p-3 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600">
                    <MessagesSquare className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">Discussion Board</h3>
                  <p className="text-sm text-slate-500">Communicate with team members through threaded discussions</p>
                  <Button variant="outline" size="sm" disabled>
                    Coming Soon
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6 flex items-start space-x-4">
                <div>
                  <div className="p-3 rounded-full bg-gradient-to-br from-purple-500 to-purple-600">
                    <Share2 className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold">Shared Resources</h3>
                  <p className="text-sm text-slate-500">Access and manage resources shared across your collaborations</p>
                  <Button variant="outline" size="sm" disabled>
                    Coming Soon
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <Separator />

        {/* Collaborations Grid */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-bold">Your Collaborations</h2>
          </div>
          
          {isLoading ? (
            <div className="flex justify-center items-center py-10">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : error ? (
            <div className="text-center py-10">
              <p className="text-red-500">
                Error loading collaborations. Please try again later.
              </p>
            </div>
          ) : !collaborations || collaborations.length === 0 ? (
            <div className="text-center py-16 bg-slate-50 rounded-lg border-2 border-dashed border-slate-200">
              <Users className="h-16 w-16 mx-auto text-slate-300 mb-4" />
              <h3 className="text-xl font-medium text-slate-900 mb-2">
                No collaborations found
              </h3>
              <p className="text-slate-500 max-w-md mx-auto mb-6">
                You are not collaborating on any courses yet. When someone invites you to collaborate, you'll see their courses here.
              </p>
              <Button variant="outline" onClick={() => setLocation("/dashboard")}>
                Return to Dashboard
              </Button>
            </div>
          ) : !filteredCollaborations || filteredCollaborations.length === 0 ? (
            <div className="text-center py-10">
              <SearchIcon className="h-12 w-12 mx-auto text-slate-300" />
              <h3 className="mt-4 text-lg font-medium text-slate-900">
                No matching collaborations
              </h3>
              <p className="mt-1 text-sm text-slate-500">
                No collaborations match your search criteria.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCollaborations.map((course) => (
                <CollaborationCard key={course.id} course={course} />
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Enterprise Upgrade Modal */}
      <EnterpriseUpgradeModal
        isOpen={isUpgradeModalOpen}
        onClose={() => setIsUpgradeModalOpen(false)}
        onSuccess={() => {
          toast({
            title: "Welcome to Enterprise!",
            description: "Your account has been upgraded. Refreshing page...",
          });
          // Refresh page to update user plan
          setTimeout(() => window.location.reload(), 2000);
        }}
      />

      {/* Add Collaborator Dialog */}
      <AddCollaboratorDialog
        isOpen={isAddCollaboratorOpen}
        onClose={() => setIsAddCollaboratorOpen(false)}
      />
    </div>
  );
}