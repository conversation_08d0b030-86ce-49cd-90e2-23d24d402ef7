import { createRoot } from "react-dom/client";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { AccessibilityProvider } from "./components/accessibility/AccessibilityProvider";
import App from "./App";
import "./index.css";

createRoot(document.getElementById("root")!).render(
  <QueryClientProvider client={queryClient}>
    <AccessibilityProvider>
      <App />
    </AccessibilityProvider>
  </QueryClientProvider>
);
