#!/usr/bin/env python3
"""
Modal integration example for Koursia Platform
This demonstrates how to use Modal for AI-powered course generation tasks
"""

from modal_working_solution import setup_modal
import json

# Set up Modal
modal = setup_modal()

# Create a Modal app for course AI tasks
app = modal.App("koursia-platform")

# Define the Python environment with required packages
image = modal.Image.debian_slim().pip_install(
    "openai",
    "anthropic", 
    "google-generativeai",
    "requests",
    "numpy"
)

@app.function(image=image)
def generate_course_outline(topic: str, difficulty: str = "beginner", modules: int = 5):
    """Generate a course outline using AI"""
    
    # This would normally call OpenAI or other AI services
    # For demonstration, returning a structured outline
    
    outline = {
        "title": f"Complete {topic} Course",
        "difficulty": difficulty,
        "estimated_hours": modules * 2,
        "modules": []
    }
    
    for i in range(modules):
        module = {
            "module_number": i + 1,
            "title": f"{topic} Fundamentals - Part {i + 1}",
            "lessons": [
                f"Introduction to {topic} concepts",
                f"Practical {topic} applications",
                f"Hands-on {topic} exercises"
            ],
            "duration_minutes": 45,
            "learning_objectives": [
                f"Understand core {topic} principles",
                f"Apply {topic} in real scenarios",
                f"Practice {topic} skills"
            ]
        }
        outline["modules"].append(module)
    
    return outline

@app.function(image=image)
def generate_lesson_content(lesson_title: str, duration: int = 30):
    """Generate detailed lesson content"""
    
    content = {
        "title": lesson_title,
        "duration_minutes": duration,
        "introduction": f"Welcome to {lesson_title}. In this lesson, you'll learn essential concepts and practical applications.",
        "main_content": [
            {
                "section": "Theory",
                "content": f"Core concepts of {lesson_title}",
                "time_allocation": duration * 0.4
            },
            {
                "section": "Practice",
                "content": f"Hands-on exercises for {lesson_title}",
                "time_allocation": duration * 0.5
            },
            {
                "section": "Summary",
                "content": f"Key takeaways from {lesson_title}",
                "time_allocation": duration * 0.1
            }
        ],
        "assessment": {
            "quiz_questions": 5,
            "practical_exercises": 2
        }
    }
    
    return content

@app.function(image=image)
def process_course_batch(course_requests: list):
    """Process multiple course generation requests in parallel"""
    
    results = []
    for request in course_requests:
        topic = request.get("topic", "General")
        difficulty = request.get("difficulty", "beginner")
        
        # Generate course outline
        outline = generate_course_outline.remote(topic, difficulty)
        
        # Generate content for first lesson of each module
        lesson_contents = []
        for module in outline["modules"]:
            first_lesson = module["lessons"][0]
            content = generate_lesson_content.remote(first_lesson)
            lesson_contents.append(content)
        
        results.append({
            "topic": topic,
            "outline": outline,
            "sample_lessons": lesson_contents
        })
    
    return results

def main():
    """Demonstrate Modal functionality for Course AI Platform"""
    
    print("Koursia Platform - Modal Integration Demo")
    print("=" * 50)
    
    # Example 1: Generate a single course outline
    print("Generating course outline for 'Python Programming'...")
    outline = generate_course_outline("Python Programming", "intermediate", 4)
    print(f"Course: {outline['title']}")
    print(f"Modules: {len(outline['modules'])}")
    print(f"Estimated Duration: {outline['estimated_hours']} hours")
    
    # Example 2: Generate lesson content
    print("\nGenerating lesson content...")
    lesson = generate_lesson_content("Python Data Structures", 45)
    print(f"Lesson: {lesson['title']}")
    print(f"Duration: {lesson['duration_minutes']} minutes")
    print(f"Sections: {len(lesson['main_content'])}")
    
    # Example 3: Batch processing
    print("\nProcessing batch course requests...")
    batch_requests = [
        {"topic": "Machine Learning", "difficulty": "advanced"},
        {"topic": "Web Development", "difficulty": "beginner"},
        {"topic": "Data Science", "difficulty": "intermediate"}
    ]
    
    batch_results = process_course_batch(batch_requests)
    print(f"Processed {len(batch_results)} course requests")
    
    for result in batch_results:
        print(f"- {result['topic']}: {len(result['outline']['modules'])} modules generated")
    
    print("\nModal integration successful! Ready for production use.")

if __name__ == "__main__":
    main()