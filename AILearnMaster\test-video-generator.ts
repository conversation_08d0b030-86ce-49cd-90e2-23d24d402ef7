import * as videoGenerator from './server/services/video-generator';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { exec, execSync } from 'child_process';

const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Create test directories
const TEMP_DIR = path.join(process.cwd(), 'temp');
const UPLOADS_DIR = path.join(process.cwd(), 'uploads');
const AUDIO_DIR = path.join(UPLOADS_DIR, 'audio');
const IMAGES_DIR = path.join(UPLOADS_DIR, 'images');
const VIDEO_DIR = path.join(UPLOADS_DIR, 'videos');

// Text for the test script
const TEST_SCRIPT = `
This is a test video generated using FFmpeg.
The video combines images and audio with automatically generated subtitles.
This demonstrates the video generation capability of our platform.
`;

async function setup() {
  // Ensure directories exist
  for (const dir of [TEMP_DIR, UPLOADS_DIR, AUDIO_DIR, IMAGES_DIR, VIDEO_DIR]) {
    if (!await existsAsync(dir)) {
      await mkdirAsync(dir, { recursive: true });
    }
  }
  
  // Create a test audio file (1 second of silence) if it doesn't exist
  const placeholderAudioPath = path.join(AUDIO_DIR, 'placeholder-audio.mp3');
  if (!await existsAsync(placeholderAudioPath)) {
    console.log('Creating placeholder audio file...');
    const ffmpegCmd = 'ffmpeg -f lavfi -i anullsrc=r=44100:cl=mono -t 10 -q:a 9 -acodec libmp3lame';
    const cmd = `${ffmpegCmd} "${placeholderAudioPath}"`;
    console.log(`Running command: ${cmd}`);
    try {
      execSync(cmd);
      console.log('Placeholder audio created successfully');
    } catch (error) {
      console.error('Failed to create placeholder audio:', error);
    }
  }
  
  // Create a test image with text if it doesn't exist
  const placeholderImagePath = path.join(IMAGES_DIR, 'placeholder-image.jpg');
  if (!await existsAsync(placeholderImagePath)) {
    console.log('Creating placeholder image...');
    const ffmpegCmd = 'ffmpeg -f lavfi -i color=c=blue:s=1280x720 -frames:v 1';
    const cmd = `${ffmpegCmd} "${placeholderImagePath}"`;
    console.log(`Running command: ${cmd}`);
    try {
      execSync(cmd);
      console.log('Placeholder image created successfully');
    } catch (error) {
      console.error('Failed to create placeholder image:', error);
    }
  }
}

async function testVideoGeneration() {
  try {
    await setup();
    
    // Paths for the test
    const audioPath = path.join(AUDIO_DIR, 'placeholder-audio.mp3');
    const imagePath = path.join(IMAGES_DIR, 'placeholder-image.jpg');
    
    if (!await existsAsync(audioPath)) {
      console.error('Audio file does not exist:', audioPath);
      return;
    }
    
    if (!await existsAsync(imagePath)) {
      console.error('Image file does not exist:', imagePath);
      return;
    }
    
    console.log('Generating test video...');
    const result = await videoGenerator.createScriptedVideo({
      script: TEST_SCRIPT,
      imagePaths: [imagePath],
      outputFileName: 'test_video',
      audioPath: audioPath
    });
    
    console.log('Video generated successfully!');
    console.log('Path:', result.path);
    console.log('URL:', result.url);
  } catch (error) {
    console.error('Error generating test video:', error);
  }
}

// Run the test
testVideoGeneration();