/**
 * Slide Generator Service
 * Converts course content into presentation slides
 */

import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../db';
import { courses, modules, lessons, Media } from '@shared/schema';
import { and, eq } from 'drizzle-orm';
import { storage } from '../storage';

// Job status tracking
interface SlideGenerationJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  courseId: number;
  userId: number;
  outputUrl?: string;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
  config: SlideGenerationConfig;
}

export interface SlideGenerationConfig {
  theme: 'professional' | 'creative' | 'minimal' | 'dark' | 'light';
  layout: 'standard' | 'title-content' | 'title-split' | 'full-image';
  options: {
    includeModuleTitles: boolean;
    includeLessonTitles: boolean;
    includeMedia: boolean;
    includeNotes: boolean;
    autoPaginate: boolean;
    includeTableOfContents: boolean;
  };
}

// In-memory job storage (would be database in production)
const activeJobs = new Map<string, SlideGenerationJob>();

// Directory for temporary files
const TEMP_DIR = path.join(process.cwd(), 'temp');
const UPLOAD_DIR = path.join(process.cwd(), 'uploads');

if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * Start a new slide generation job
 */
export async function startSlideGeneration(
  userId: number,
  courseId: number,
  config: SlideGenerationConfig
): Promise<{ jobId: string }> {
  const jobId = uuidv4();
  
  const job: SlideGenerationJob = {
    id: jobId,
    status: 'pending',
    progress: 0,
    courseId,
    userId,
    createdAt: new Date(),
    updatedAt: new Date(),
    config
  };
  
  activeJobs.set(jobId, job);
  
  // Start async generation process
  setTimeout(() => generateSlidesAsync(jobId), 100);
  
  return { jobId };
}

/**
 * Generate the slides asynchronously
 */
async function generateSlidesAsync(jobId: string): Promise<void> {
  const job = activeJobs.get(jobId);
  if (!job) return;
  
  try {
    // Mark job as processing
    job.status = 'processing';
    job.progress = 5;
    job.updatedAt = new Date();
    activeJobs.set(jobId, job);
    
    // Fetch course data
    const course = await db.query.courses.findFirst({
      where: and(
        eq(courses.id, job.courseId),
        eq(courses.userId, job.userId)
      ),
      with: {
        modules: {
          orderBy: (modules, { asc }) => [asc(modules.order)],
          with: {
            lessons: {
              orderBy: (lessons, { asc }) => [asc(lessons.order)]
            }
          }
        }
      }
    });
    
    if (!course) {
      throw new Error('Course not found');
    }
    
    job.progress = 10;
    job.updatedAt = new Date();
    activeJobs.set(jobId, job);
    
    // Fetch media for course  
    let mediaItems: any[] = [];
    
    if (job.config.options.includeMedia) {
      try {
        // Use the storage interface to get media
        mediaItems = await storage.getMediaByUserId(job.userId) || [];
        // Filter to only include media for this course
        mediaItems = mediaItems.filter(media => media.courseId === job.courseId);
      } catch (error) {
        console.error("Error fetching media:", error);
      }
    }
    
    job.progress = 15;
    job.updatedAt = new Date();
    activeJobs.set(jobId, job);
    
    // Generate slide content
    job.progress = 20;
    job.updatedAt = new Date();
    activeJobs.set(jobId, job);
    
    // Generate the slide data using our function
    const slideContent = await generateSlidesWithPPTXGenJS(course, mediaItems, job.config);
    
    // Write to temporary file
    const outputFileName = `course-${job.courseId}-slides-${Date.now()}.json`;
    const outputFilePath = path.join(TEMP_DIR, outputFileName);
    fs.writeFileSync(outputFilePath, slideContent);
    
    // Progress updates at different stages
    for (let progress = 20; progress <= 90; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 500));
      job.progress = progress;
      job.updatedAt = new Date();
      activeJobs.set(jobId, job);
    }
    
    // Move file to public directory for download
    const publicFileName = `slides_${job.courseId}_${Date.now()}.json`;
    const publicFilePath = path.join(UPLOAD_DIR, publicFileName);
    fs.copyFileSync(outputFilePath, publicFilePath);
    fs.unlinkSync(outputFilePath);
    
    // Complete the job
    job.status = 'completed';
    job.progress = 100;
    job.outputUrl = `/api/uploads/${publicFileName}`;
    job.updatedAt = new Date();
    activeJobs.set(jobId, job);
    
    // Cleanup job after 1 hour
    setTimeout(() => {
      activeJobs.delete(jobId);
    }, 60 * 60 * 1000);
    
  } catch (error) {
    console.error('Error generating slides:', error);
    
    job.status = 'failed';
    job.error = error instanceof Error ? error.message : 'Unknown error occurred';
    job.updatedAt = new Date();
    activeJobs.set(jobId, job);
    
    // Cleanup failed job after 15 minutes
    setTimeout(() => {
      activeJobs.delete(jobId);
    }, 15 * 60 * 1000);
  }
}

/**
 * Get job status
 */
export function getJobStatus(jobId: string): SlideGenerationJob | undefined {
  return activeJobs.get(jobId);
}

/**
 * Generates a simplified slides file in JSON format, which will be served as a downloadable file.
 * In a production implementation, we would use pptxgenjs to create actual PowerPoint slides,
 * but this simpler approach works for now.
 */
export async function generateSlidesWithPPTXGenJS(
  course: any, 
  mediaItems: any[], 
  config: SlideGenerationConfig
): Promise<Buffer> {
  try {
    // Create a detailed JSON representation of what would be in slides
    const slidesData = {
      presentationTitle: course.title,
      createdAt: new Date().toISOString(),
      theme: config.theme,
      layout: config.layout,
      slideCount: 0,
      slides: [] as any[],
    };
    
    // Add title slide
    slidesData.slides.push({
      type: 'title',
      title: course.title,
      subtitle: 'Generated Course Slides',
      notes: 'Title slide for the course'
    });
    
    // Add table of contents if requested
    if (config.options.includeTableOfContents && Array.isArray(course.modules)) {
      const tocSlide = {
        type: 'toc',
        title: 'Table of Contents',
        content: course.modules.map((module: any) => module.title || 'Unnamed Module')
      };
      slidesData.slides.push(tocSlide);
    }
    
    // Process modules and lessons
    if (Array.isArray(course.modules)) {
      for (const module of course.modules) {
        // Add module title slide if requested
        if (config.options.includeModuleTitles) {
          slidesData.slides.push({
            type: 'section',
            title: module.title || 'Unnamed Module',
            level: 'module'
          });
        }
        
        // Process lessons within module
        if (Array.isArray(module.lessons)) {
          for (const lesson of module.lessons) {
            // Add lesson title slide if requested
            if (config.options.includeLessonTitles) {
              slidesData.slides.push({
                type: 'section',
                title: lesson.title || 'Unnamed Lesson',
                level: 'lesson'
              });
            }
            
            // Process lesson content into slides
            if (lesson.script) {
              // Break script into chunks for slides
              const paragraphs = lesson.script.split(/\\n\\n+/).filter(Boolean);
              
              // Create content slides from paragraphs
              if (config.options.autoPaginate) {
                // Group paragraphs into slides with 2-3 paragraphs per slide
                for (let i = 0; i < paragraphs.length; i += 3) {
                  const slideContent = paragraphs.slice(i, i + 3).join('\\n\\n');
                  slidesData.slides.push({
                    type: 'content',
                    title: lesson.title || 'Lesson Content',
                    content: slideContent,
                    notes: config.options.includeNotes ? slideContent : undefined
                  });
                }
              } else {
                // Just one slide with all content
                slidesData.slides.push({
                  type: 'content',
                  title: lesson.title || 'Lesson Content',
                  content: lesson.script,
                  notes: config.options.includeNotes ? lesson.script : undefined
                });
              }
            }
            
            // Add media if requested and available
            if (config.options.includeMedia && Array.isArray(mediaItems)) {
              const lessonMedia = mediaItems.filter(media => 
                media.lessonId === lesson.id || 
                (media.type && (media.type.includes('image') || media.type.includes('photo')))
              );
              
              if (lessonMedia.length > 0) {
                for (const media of lessonMedia) {
                  slidesData.slides.push({
                    type: 'media',
                    title: media.name || 'Media Content',
                    mediaUrl: media.url,
                    mediaType: media.type || 'image',
                    description: media.name || ''
                  });
                }
              }
            }
          }
        }
      }
    }
    
    // Update slide count
    slidesData.slideCount = slidesData.slides.length;
    
    // Convert to pretty JSON
    const jsonContent = JSON.stringify(slidesData, null, 2);
    
    // Return as buffer that will be served as a downloadable file
    return Buffer.from(jsonContent);
  } catch (error) {
    console.error('Error generating slides content:', error);
    return Buffer.from('Error generating slides: ' + (error instanceof Error ? error.message : String(error)));
  }
}