import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { 
  BookOpen, 
  MessageSquare, 
  Edit, 
  FileText, 
  Clock, 
  PlusCircle, 
  Trash, 
  Video, 
  Image,
  UserPlus,
  RotateCw
} from 'lucide-react';

// Types
interface ActivityItem {
  id: string;
  type: 'edit' | 'comment' | 'join' | 'leave' | 'add' | 'delete' | 'upload';
  userId: number;
  userName: string;
  userAvatarUrl?: string;
  target: string;
  targetType: 'course' | 'lesson' | 'module' | 'media' | 'team';
  targetId: number;
  timestamp: Date;
  details?: string;
}

interface ActivityFeedProps {
  courseId?: number;
  teamId?: number;
  maxHeight?: string;
}

export function ActivityFeed({ courseId, teamId, maxHeight = '400px' }: ActivityFeedProps) {
  const [activeTab, setActiveTab] = useState<string>('all');

  // In a real app, this would fetch from an API
  const activities: ActivityItem[] = [
    {
      id: 'activity-1',
      type: 'edit',
      userId: 1,
      userName: 'John Smith',
      userAvatarUrl: '/avatars/john.jpg',
      target: 'AI Fundamentals Module',
      targetType: 'module',
      targetId: 1,
      timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
      details: 'Updated module content and learning objectives'
    },
    {
      id: 'activity-2',
      type: 'comment',
      userId: 2,
      userName: 'Sarah Johnson',
      userAvatarUrl: '/avatars/sarah.jpg',
      target: 'Machine Learning Basics',
      targetType: 'lesson',
      targetId: 2,
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      details: 'I think we should simplify this section for beginners'
    },
    {
      id: 'activity-3',
      type: 'add',
      userId: 3,
      userName: 'Michael Chen',
      target: 'Neural Networks Advanced Concepts',
      targetType: 'lesson',
      targetId: 3,
      timestamp: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago
    },
    {
      id: 'activity-4',
      type: 'upload',
      userId: 1,
      userName: 'John Smith',
      userAvatarUrl: '/avatars/john.jpg',
      target: 'Deep Learning Diagram',
      targetType: 'media',
      targetId: 4,
      timestamp: new Date(Date.now() - 1000 * 60 * 180), // 3 hours ago
    },
    {
      id: 'activity-5',
      type: 'join',
      userId: 4,
      userName: 'Emma Wilson',
      userAvatarUrl: '/avatars/emma.jpg',
      target: 'AI Course Team',
      targetType: 'team',
      targetId: 1,
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
    },
    {
      id: 'activity-6',
      type: 'delete',
      userId: 2,
      userName: 'Sarah Johnson',
      userAvatarUrl: '/avatars/sarah.jpg',
      target: 'Outdated AI Techniques',
      targetType: 'lesson',
      targetId: 5,
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 36), // 1.5 days ago
    }
  ];

  // Filter activities based on the active tab
  const filteredActivities = activities.filter(activity => {
    if (activeTab === 'all') return true;
    if (activeTab === 'edits') return ['edit', 'add', 'delete'].includes(activity.type);
    if (activeTab === 'comments') return activity.type === 'comment';
    if (activeTab === 'uploads') return activity.type === 'upload';
    if (activeTab === 'members') return ['join', 'leave'].includes(activity.type);
    return true;
  });

  // Get icon based on activity type
  const getActivityIcon = (activity: ActivityItem) => {
    switch (activity.type) {
      case 'edit':
        return <Edit className="h-4 w-4 text-blue-500" />;
      case 'comment':
        return <MessageSquare className="h-4 w-4 text-violet-500" />;
      case 'join':
      case 'leave':
        return <UserPlus className="h-4 w-4 text-green-500" />;
      case 'add':
        return <PlusCircle className="h-4 w-4 text-green-500" />;
      case 'delete':
        return <Trash className="h-4 w-4 text-red-500" />;
      case 'upload':
        if (activity.target.toLowerCase().includes('video')) {
          return <Video className="h-4 w-4 text-blue-500" />;
        }
        return <Image className="h-4 w-4 text-blue-500" />;
      default:
        return <FileText className="h-4 w-4 text-slate-500" />;
    }
  };

  // Get color for activity time
  const getActivityTimeColor = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) return 'text-green-500';
    if (diffInHours < 24) return 'text-slate-500';
    return 'text-slate-400';
  };

  // Format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return date.toLocaleDateString();
  };

  // Get activity description based on type
  const getActivityDescription = (activity: ActivityItem) => {
    switch (activity.type) {
      case 'edit':
        return (
          <>
            edited <span className="font-medium">{activity.target}</span>
          </>
        );
      case 'comment':
        return (
          <>
            commented on <span className="font-medium">{activity.target}</span>
          </>
        );
      case 'join':
        return (
          <>
            joined <span className="font-medium">{activity.target}</span>
          </>
        );
      case 'leave':
        return (
          <>
            left <span className="font-medium">{activity.target}</span>
          </>
        );
      case 'add':
        return (
          <>
            added <span className="font-medium">{activity.target}</span>
          </>
        );
      case 'delete':
        return (
          <>
            deleted <span className="font-medium">{activity.target}</span>
          </>
        );
      case 'upload':
        return (
          <>
            uploaded <span className="font-medium">{activity.target}</span>
          </>
        );
      default:
        return (
          <>
            modified <span className="font-medium">{activity.target}</span>
          </>
        );
    }
  };

  return (
    <div className="border rounded-lg bg-card">
      <div className="p-4 pb-2">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-sm">Activity Feed</h3>
          <Button variant="ghost" size="icon" className="h-7 w-7">
            <RotateCw className="h-4 w-4" />
          </Button>
        </div>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 h-8">
            <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
            <TabsTrigger value="edits" className="text-xs">Edits</TabsTrigger>
            <TabsTrigger value="comments" className="text-xs">Comments</TabsTrigger>
            <TabsTrigger value="uploads" className="text-xs">Uploads</TabsTrigger>
            <TabsTrigger value="members" className="text-xs">Members</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <Separator />
      <ScrollArea className="p-0" style={{ maxHeight }}>
        {filteredActivities.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <Clock className="h-8 w-8 text-slate-300 mb-2" />
            <p className="text-sm text-slate-500">No recent activity</p>
            <p className="text-xs text-slate-400 mt-1">
              Activities will appear here as team members make changes
            </p>
          </div>
        ) : (
          <div className="divide-y">
            {filteredActivities.map((activity) => (
              <div key={activity.id} className="p-3 hover:bg-accent/50 transition-colors">
                <div className="flex">
                  <div className="mr-3 mt-0.5">
                    <Avatar className="h-8 w-8">
                      {activity.userAvatarUrl ? (
                        <AvatarImage src={activity.userAvatarUrl} alt={activity.userName} />
                      ) : (
                        <AvatarFallback>
                          {activity.userName.charAt(0)}
                        </AvatarFallback>
                      )}
                    </Avatar>
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm">
                        <span className="font-medium">{activity.userName}</span>{' '}
                        {getActivityDescription(activity)}
                      </p>
                      <div className="flex items-center">
                        <div className="mr-2">{getActivityIcon(activity)}</div>
                        <span className={`text-xs ${getActivityTimeColor(activity.timestamp)}`}>
                          {formatTimeAgo(activity.timestamp)}
                        </span>
                      </div>
                    </div>
                    {activity.details && (
                      <p className="text-xs text-slate-500">{activity.details}</p>
                    )}
                    {activity.type === 'comment' && (
                      <div className="mt-2 bg-accent/50 text-sm p-2 rounded-md">
                        {activity.details}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}