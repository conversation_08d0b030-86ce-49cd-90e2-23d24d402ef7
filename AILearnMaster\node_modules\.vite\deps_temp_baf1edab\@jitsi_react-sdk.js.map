{"version": 3, "sources": ["../../@jitsi/react-sdk/lib/components/JitsiMeeting.js", "../../@jitsi/react-sdk/lib/constants/index.js", "../../@jitsi/react-sdk/lib/init.js", "../../@jitsi/react-sdk/lib/utils/index.js", "../../@jitsi/react-sdk/lib/components/JaaSMeeting.js"], "sourcesContent": ["import { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport { DEFAULT_DOMAIN } from '../constants';\nimport { fetchExternalApi } from '../init';\nimport { generateComponentId, getAppId } from '../utils';\n/**\n * Returns the JitsiMeeting Component with access to a custom External API\n * to be used as-it-is in React projects\n *\n * @param {IJitsiMeetingProps} props the component's props\n * @returns {ReactElement} the `JitsiMeeting` Component\n * @example\n  ```js\n    <JitsiMeeting\n        domain='meet.jit.si'\n        roomName: 'TestingJitsiMeetingComponent'\n        spinner={CustomSpinner}\n        onApiReady={(externalApi) => console.log(externalApi)}\n    />\n  ```\n */\nconst JitsiMeeting = ({ domain = DEFAULT_DOMAIN, roomName, configOverwrite, interfaceConfigOverwrite, jwt, invitees, devices, userInfo, release, lang, spinner: Spinner, onApiReady, onReadyToClose, getIFrameRef }) => {\n    const [loading, setLoading] = useState(true);\n    const [apiLoaded, setApiLoaded] = useState(false);\n    const externalApi = useRef();\n    const apiRef = useRef();\n    const meetingRef = useRef(null);\n    const componentId = useMemo(() => generateComponentId('jitsiMeeting'), [generateComponentId]);\n    useEffect(() => {\n        fetchExternalApi(domain, release, getAppId(roomName))\n            .then((api) => {\n            externalApi.current = api;\n            setApiLoaded(true);\n        })\n            .catch((e) => console.error(e.message));\n    }, []);\n    const loadIFrame = useCallback((JitsiMeetExternalAPI) => {\n        apiRef.current = new JitsiMeetExternalAPI(domain, {\n            roomName,\n            configOverwrite,\n            interfaceConfigOverwrite,\n            jwt,\n            invitees,\n            devices,\n            userInfo,\n            release,\n            lang,\n            parentNode: meetingRef.current\n        });\n        setLoading(false);\n        if (apiRef.current) {\n            typeof onApiReady === 'function' && onApiReady(apiRef.current);\n            apiRef.current.on('readyToClose', () => {\n                typeof onReadyToClose === 'function' && onReadyToClose();\n            });\n            if (meetingRef.current && typeof getIFrameRef === 'function') {\n                getIFrameRef(meetingRef.current);\n            }\n        }\n    }, [\n        apiRef,\n        meetingRef,\n        onApiReady,\n        onReadyToClose,\n        getIFrameRef,\n        domain,\n        roomName,\n        configOverwrite,\n        interfaceConfigOverwrite,\n        jwt,\n        invitees,\n        devices,\n        userInfo,\n        release,\n        lang\n    ]);\n    useEffect(() => {\n        if (apiLoaded && !apiRef.current) {\n            if (externalApi.current) {\n                loadIFrame(externalApi.current);\n            }\n        }\n    }, [apiLoaded, loadIFrame]);\n    const renderLoadingSpinner = useCallback(() => {\n        if (!Spinner) {\n            return null;\n        }\n        if (!loading || apiRef.current) {\n            return null;\n        }\n        return _jsx(Spinner, {}, void 0);\n    }, [Spinner, apiRef.current]);\n    return (_jsxs(_Fragment, { children: [renderLoadingSpinner(), _jsx(\"div\", { id: componentId, ref: meetingRef }, componentId)] }, void 0));\n};\nexport default JitsiMeeting;\n", "export const DEFAULT_DOMAIN = 'meet.jit.si';\nexport const JAAS_STAGING_DOMAIN = 'stage.8x8.vc';\nexport const JAAS_PROD_DOMAIN = '8x8.vc';\n", "var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nimport { DEFAULT_DOMAIN } from './constants';\nconst loadExternalApi = (domain, release, appId) => __awaiter(void 0, void 0, void 0, function* () {\n    return new Promise((resolve, reject) => {\n        if (window.JitsiMeetExternalAPI) {\n            return resolve(window.JitsiMeetExternalAPI);\n        }\n        const script = document.createElement('script');\n        const releaseParam = release ? `?release=${release}` : '';\n        const appIdPath = appId ? `${appId}/` : '';\n        script.async = true;\n        script.src = `https://${domain}/${appIdPath}external_api.js${releaseParam}`;\n        script.onload = () => resolve(window.JitsiMeetExternalAPI);\n        script.onerror = () => reject(new Error(`Script load error: ${script.src}`));\n        document.head.appendChild(script);\n    });\n});\nlet scriptPromise;\n/**\n * Injects the external_api.js script for the corresponding domain in DOM\n * and resolves with either the `JitsiMeetExternalApi` class definition or an error.\n *\n * Only the first script will be injected, therefore avoid using multiple instances\n * with mixed domains and release version at the same time.\n *\n * @param {string} domain - The domain of the external API\n * @param {string} release - The Jitsi Meet release. Expected format: 'release-1234'\n * @param {string} appId - The tenant for JaaS Meetings\n * @returns {Promise<JitsiMeetExternalApi>} - The JitsiMeetExternalAPI or an error\n */\nexport const fetchExternalApi = (domain = DEFAULT_DOMAIN, release, appId) => {\n    if (scriptPromise) {\n        return scriptPromise;\n    }\n    scriptPromise = loadExternalApi(domain, release, appId);\n    return scriptPromise;\n};\n", "import { JAAS_PROD_DOMAIN, JAAS_STAGING_DOMAIN } from '../constants';\n/**\n * Returns the complete room name\n *\n * @param {string} roomName\n * @param {string} tenant\n * @returns {string} the complete room name\n */\nexport const getRoomName = (roomName, tenant) => {\n    if (tenant) {\n        return `${tenant}/${roomName}`;\n    }\n    return roomName;\n};\n/**\n * Returns the appId or tenant value\n *\n * @param {string} roomName\n * @returns {string|undefined}\n */\nexport const getAppId = (roomName) => {\n    const roomParts = roomName.split('/');\n    if (roomParts.length <= 1) {\n        return;\n    }\n    return roomParts[0];\n};\n/**\n * Returns the JaaS domain\n *\n * @param {boolean|undefined} useStaging\n * @returns {string} the JaaS domain\n */\nexport const getJaaSDomain = (useStaging) => {\n    if (useStaging) {\n        return JAAS_STAGING_DOMAIN;\n    }\n    return JAAS_PROD_DOMAIN;\n};\nlet instancesCounter = 0;\n/**\n * Generates an unique id\n * @param {string} prefix\n * @returns {string} the component id\n */\nexport const generateComponentId = (prefix) => `${prefix}-${instancesCounter++}`;\n", "var __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { getJaaSDomain, getRoomName } from '../utils';\nimport { JitsiMeeting } from '.';\n/**\n * Returns the JaaSMeeting Component with access to the 8x8.vc External API\n * to be used as-it-is in React projects\n *\n * @param {IJaaSMeetingProps} props the component's props\n * @returns {ReactElement} the `JaaSMeeting` Component\n * @example\n  ```js\n      <JaaSMeeting\n        roomName: 'TestingJaaSMeetingComponent'\n        appId='exampleAppId'\n        spinner={CustomSpinner}\n        onApiReady={(externalApi) => console.log(externalApi)}\n      />\n  ```\n */\nconst JaaSMeeting = (_a) => {\n    var { appId, roomName, useStaging, release } = _a, rest = __rest(_a, [\"appId\", \"roomName\", \"useStaging\", \"release\"]);\n    return (_jsx(JitsiMeeting, Object.assign({ domain: getJaaSDomain(useStaging), roomName: getRoomName(roomName, appId), release: release }, rest), void 0));\n};\nexport default JaaSMeeting;\n"], "mappings": ";;;;;;;;;;;AAAA,yBAAkE;AAClE,mBAAkE;;;ACD3D,IAAM,iBAAiB;AACvB,IAAM,sBAAsB;AAC5B,IAAM,mBAAmB;;;ACFhC,IAAI,YAAwC,SAAU,SAAS,YAAY,GAAG,WAAW;AACrF,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACL;AAEA,IAAM,kBAAkB,CAAC,QAAQ,SAAS,UAAU,UAAU,QAAQ,QAAQ,QAAQ,aAAa;AAC/F,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,QAAI,OAAO,sBAAsB;AAC7B,aAAO,QAAQ,OAAO,oBAAoB;AAAA,IAC9C;AACA,UAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,UAAM,eAAe,UAAU,YAAY,OAAO,KAAK;AACvD,UAAM,YAAY,QAAQ,GAAG,KAAK,MAAM;AACxC,WAAO,QAAQ;AACf,WAAO,MAAM,WAAW,MAAM,IAAI,SAAS,kBAAkB,YAAY;AACzE,WAAO,SAAS,MAAM,QAAQ,OAAO,oBAAoB;AACzD,WAAO,UAAU,MAAM,OAAO,IAAI,MAAM,sBAAsB,OAAO,GAAG,EAAE,CAAC;AAC3E,aAAS,KAAK,YAAY,MAAM;AAAA,EACpC,CAAC;AACL,CAAC;AACD,IAAI;AAaG,IAAM,mBAAmB,CAAC,SAAS,gBAAgB,SAAS,UAAU;AACzE,MAAI,eAAe;AACf,WAAO;AAAA,EACX;AACA,kBAAgB,gBAAgB,QAAQ,SAAS,KAAK;AACtD,SAAO;AACX;;;ACpCO,IAAM,cAAc,CAAC,UAAU,WAAW;AAC7C,MAAI,QAAQ;AACR,WAAO,GAAG,MAAM,IAAI,QAAQ;AAAA,EAChC;AACA,SAAO;AACX;AAOO,IAAM,WAAW,CAAC,aAAa;AAClC,QAAM,YAAY,SAAS,MAAM,GAAG;AACpC,MAAI,UAAU,UAAU,GAAG;AACvB;AAAA,EACJ;AACA,SAAO,UAAU,CAAC;AACtB;AAOO,IAAM,gBAAgB,CAAC,eAAe;AACzC,MAAI,YAAY;AACZ,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,IAAI,mBAAmB;AAMhB,IAAM,sBAAsB,CAAC,WAAW,GAAG,MAAM,IAAI,kBAAkB;;;AHxB9E,IAAM,eAAe,CAAC,EAAE,SAAS,gBAAgB,UAAU,iBAAiB,0BAA0B,KAAK,UAAU,SAAS,UAAU,SAAS,MAAM,SAAS,SAAS,YAAY,gBAAgB,aAAa,MAAM;AACpN,QAAM,CAAC,SAAS,UAAU,QAAI,uBAAS,IAAI;AAC3C,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,KAAK;AAChD,QAAM,kBAAc,qBAAO;AAC3B,QAAM,aAAS,qBAAO;AACtB,QAAM,iBAAa,qBAAO,IAAI;AAC9B,QAAM,kBAAc,sBAAQ,MAAM,oBAAoB,cAAc,GAAG,CAAC,mBAAmB,CAAC;AAC5F,8BAAU,MAAM;AACZ,qBAAiB,QAAQ,SAAS,SAAS,QAAQ,CAAC,EAC/C,KAAK,CAAC,QAAQ;AACf,kBAAY,UAAU;AACtB,mBAAa,IAAI;AAAA,IACrB,CAAC,EACI,MAAM,CAAC,MAAM,QAAQ,MAAM,EAAE,OAAO,CAAC;AAAA,EAC9C,GAAG,CAAC,CAAC;AACL,QAAM,iBAAa,0BAAY,CAAC,yBAAyB;AACrD,WAAO,UAAU,IAAI,qBAAqB,QAAQ;AAAA,MAC9C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY,WAAW;AAAA,IAC3B,CAAC;AACD,eAAW,KAAK;AAChB,QAAI,OAAO,SAAS;AAChB,aAAO,eAAe,cAAc,WAAW,OAAO,OAAO;AAC7D,aAAO,QAAQ,GAAG,gBAAgB,MAAM;AACpC,eAAO,mBAAmB,cAAc,eAAe;AAAA,MAC3D,CAAC;AACD,UAAI,WAAW,WAAW,OAAO,iBAAiB,YAAY;AAC1D,qBAAa,WAAW,OAAO;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,8BAAU,MAAM;AACZ,QAAI,aAAa,CAAC,OAAO,SAAS;AAC9B,UAAI,YAAY,SAAS;AACrB,mBAAW,YAAY,OAAO;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,WAAW,UAAU,CAAC;AAC1B,QAAM,2BAAuB,0BAAY,MAAM;AAC3C,QAAI,CAAC,SAAS;AACV,aAAO;AAAA,IACX;AACA,QAAI,CAAC,WAAW,OAAO,SAAS;AAC5B,aAAO;AAAA,IACX;AACA,eAAO,mBAAAA,KAAK,SAAS,CAAC,GAAG,MAAM;AAAA,EACnC,GAAG,CAAC,SAAS,OAAO,OAAO,CAAC;AAC5B,aAAQ,mBAAAC,MAAM,mBAAAC,UAAW,EAAE,UAAU,CAAC,qBAAqB,OAAG,mBAAAF,KAAK,OAAO,EAAE,IAAI,aAAa,KAAK,WAAW,GAAG,WAAW,CAAC,EAAE,GAAG,MAAM;AAC3I;AACA,IAAO,uBAAQ;;;AInFf,IAAAG,sBAA4B;AAX5B,IAAI,SAAkC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAoBA,IAAM,cAAc,CAAC,OAAO;AACxB,MAAI,EAAE,OAAO,UAAU,YAAY,QAAQ,IAAI,IAAI,OAAO,OAAO,IAAI,CAAC,SAAS,YAAY,cAAc,SAAS,CAAC;AACnH,aAAQ,oBAAAC,KAAK,sBAAc,OAAO,OAAO,EAAE,QAAQ,cAAc,UAAU,GAAG,UAAU,YAAY,UAAU,KAAK,GAAG,QAAiB,GAAG,IAAI,GAAG,MAAM;AAC3J;AACA,IAAO,sBAAQ;", "names": ["_jsx", "_jsxs", "_Fragment", "import_jsx_runtime", "_jsx"]}