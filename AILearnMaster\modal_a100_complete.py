"""
Complete A100 GPU Setup for Course AI Platform
Production-ready deployment with SadTalker, Chatterbox TTS, and Marp
"""

import modal
import os
import base64
import json
import logging
import tempfile
import subprocess
from typing import Dict, List, Any, Optional
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Complete GPU image with all dependencies
gpu_image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install([
        # System dependencies
        "git", "wget", "curl", "ffmpeg", "libsm6", "libxext6", 
        "libxrender-dev", "libglib2.0-0", "libssl-dev", "libffi-dev",
        "build-essential", "cmake", "pkg-config", "libjpeg-dev", 
        "libpng-dev", "libtiff-dev", "libavcodec-dev", "libavformat-dev",
        "libswscale-dev", "libv4l-dev", "libxvidcore-dev", "libx264-dev",
        "libgtk-3-dev", "libatlas-base-dev", "gfortran", "python3-dev",
        "libnode-dev", "npm", "nodejs", "unzip"
    ])
    .pip_install([
        # Core ML libraries
        "torch==2.1.0",
        "torchvision==0.16.0", 
        "torchaudio==2.1.0",
        "diffusers==0.24.0",
        "transformers==4.35.0",
        "accelerate==0.24.0",
        
        # Computer vision and media
        "opencv-python==********",
        "pillow==10.0.1",
        "imageio==2.31.5",
        "imageio-ffmpeg==0.4.9",
        "scikit-image==0.21.0",
        "face-alignment==1.3.5",
        
        # Audio processing
        "librosa==0.10.1",
        "soundfile==0.12.1",
        "resampy==0.4.2",
        
        # Scientific computing
        "numpy==1.24.3",
        "scipy==1.11.3",
        "matplotlib==3.7.2",
        "tqdm==4.66.1",
        
        # Configuration and utilities
        "omegaconf==2.3.0",
        "safetensors==0.4.0",
        "huggingface-hub==0.17.3",
        "requests==2.31.0",
        "pydantic==2.4.2",
        
        # TTS libraries
        "TTS==0.20.6",
        "espeak-ng",
        "phonemizer",
        
        # Web framework
        "fastapi==0.104.1",
        "uvicorn==0.24.0"
    ])
    .run_commands([
        # Install Node.js dependencies
        "npm install -g @marp-team/marp-cli",
        "npm install -g puppeteer",
        
        # Clone and setup SadTalker
        "cd /opt && git clone https://github.com/OpenTalker/SadTalker.git",
        "cd /opt/SadTalker && pip install -r requirements.txt",
        
        # Download SadTalker models
        "mkdir -p /opt/SadTalker/checkpoints",
        "cd /opt/SadTalker/checkpoints && wget -q https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00109-model.pth.tar",
        "cd /opt/SadTalker/checkpoints && wget -q https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00229-model.pth.tar",
        "cd /opt/SadTalker/checkpoints && wget -q https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/SadTalker_V0.0.2_256.safetensors",
        "cd /opt/SadTalker/checkpoints && wget -q https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/SadTalker_V0.0.2_512.safetensors",
        
        # Setup GFPGAN for enhancement
        "cd /opt && git clone https://github.com/TencentARC/GFPGAN.git",
        "cd /opt/GFPGAN && pip install -r requirements.txt",
        "cd /opt/GFPGAN && python setup.py develop",
        
        # Download GFPGAN models
        "mkdir -p /opt/GFPGAN/experiments/pretrained_models",
        "cd /opt/GFPGAN/experiments/pretrained_models && wget -q https://github.com/TencentARC/GFPGAN/releases/download/v1.3.0/GFPGANv1.4.pth",
        
        # Verify installations
        "python -c 'import torch; print(f\"PyTorch: {torch.__version__}\")'",
        "python -c 'import TTS; print(f\"TTS: {TTS.__version__}\")'",
        "marp --version",
        "ffmpeg -version | head -1"
    ])
)

# Create Modal App
app = modal.App("courseai-a100-production", image=gpu_image)

@app.function(
    gpu="A100-80GB",
    timeout=1800,
    memory=32768,
    cpu=8.0,
    image=gpu_image,
    min_containers=0,
    max_containers=5
)
def initialize_gpu_environment():
    """Initialize and verify complete GPU environment"""
    import torch
    import sys
    import subprocess
    
    results = {
        "gpu_status": {},
        "software_versions": {},
        "sadtalker_ready": False,
        "tts_ready": False,
        "marp_ready": False,
        "status": "initializing"
    }
    
    try:
        # GPU Status
        gpu_available = torch.cuda.is_available()
        if gpu_available:
            results["gpu_status"] = {
                "available": True,
                "count": torch.cuda.device_count(),
                "name": torch.cuda.get_device_name(0),
                "memory_gb": torch.cuda.get_device_properties(0).total_memory / 1024**3,
                "compute_capability": torch.cuda.get_device_capability(0)
            }
            
            # Test GPU computation
            test_tensor = torch.randn(1000, 1000, device='cuda')
            result = torch.mm(test_tensor, test_tensor.T)
            results["gpu_status"]["computation_test"] = result.shape == (1000, 1000)
        
        # Software versions
        results["software_versions"] = {
            "python": sys.version.split()[0],
            "torch": torch.__version__,
            "cuda": torch.version.cuda if torch.cuda.is_available() else "N/A"
        }
        
        # Check TTS
        try:
            from TTS.api import TTS
            results["software_versions"]["tts"] = "0.20.6"
            results["tts_ready"] = True
        except Exception as e:
            logger.error(f"TTS check failed: {e}")
        
        # Check SadTalker
        if os.path.exists("/opt/SadTalker"):
            results["sadtalker_ready"] = True
            results["software_versions"]["sadtalker"] = "v0.0.2"
        
        # Check Marp
        try:
            marp_result = subprocess.run(['marp', '--version'], capture_output=True, text=True)
            if marp_result.returncode == 0:
                results["marp_ready"] = True
                results["software_versions"]["marp"] = marp_result.stdout.strip()
        except Exception as e:
            logger.error(f"Marp check failed: {e}")
        
        results["status"] = "ready" if all([
            results["gpu_status"].get("available", False),
            results["tts_ready"],
            results["sadtalker_ready"],
            results["marp_ready"]
        ]) else "partial"
        
    except Exception as e:
        logger.error(f"Environment initialization failed: {e}")
        results["status"] = "error"
        results["error"] = str(e)
    
    return results

@app.function(
    gpu="A100-80GB",
    timeout=600,
    memory=16384,
    image=gpu_image
)
def chatterbox_tts_a100(
    text: str,
    voice_preset: str = "tts_models/en/ljspeech/tacotron2-DDC",
    output_format: str = "wav"
) -> Dict[str, Any]:
    """High-quality Chatterbox TTS using A100 GPU"""
    import torch
    import tempfile
    import base64
    from TTS.api import TTS
    
    try:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Chatterbox TTS using device: {device}")
        
        # Initialize TTS model
        tts = TTS(voice_preset).to(device)
        
        # Generate speech
        with tempfile.NamedTemporaryFile(suffix=f".{output_format}", delete=False) as tmp_file:
            tmp_path = tmp_file.name
        
        tts.tts_to_file(text=text, file_path=tmp_path)
        
        # Read and encode
        with open(tmp_path, 'rb') as audio_file:
            audio_data = audio_file.read()
        
        audio_base64 = base64.b64encode(audio_data).decode()
        os.unlink(tmp_path)
        
        return {
            "status": "success",
            "audio_base64": audio_base64,
            "text": text,
            "voice_preset": voice_preset,
            "format": output_format,
            "gpu_used": str(device),
            "audio_size_bytes": len(audio_data)
        }
        
    except Exception as e:
        logger.error(f"Chatterbox TTS failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "text": text
        }

@app.function(
    gpu="A100-80GB",
    timeout=1200,
    memory=24576,
    image=gpu_image
)
def sadtalker_avatar_generation(
    image_base64: str,
    audio_base64: str,
    enhancer: str = "gfpgan",
    size: int = 512
) -> Dict[str, Any]:
    """Generate SadTalker avatar video using A100 GPU"""
    import torch
    import tempfile
    import base64
    import subprocess
    import shutil
    
    try:
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"SadTalker using device: {device}")
        
        # Create temp files
        with tempfile.TemporaryDirectory() as temp_dir:
            # Decode inputs
            img_path = os.path.join(temp_dir, "source.jpg")
            audio_path = os.path.join(temp_dir, "audio.wav")
            result_dir = os.path.join(temp_dir, "results")
            
            # Save input files
            with open(img_path, 'wb') as f:
                f.write(base64.b64decode(image_base64))
            
            with open(audio_path, 'wb') as f:
                f.write(base64.b64decode(audio_base64))
            
            os.makedirs(result_dir, exist_ok=True)
            
            # Run SadTalker
            cmd = [
                "python", "/opt/SadTalker/inference.py",
                "--driven_audio", audio_path,
                "--source_image", img_path,
                "--result_dir", result_dir,
                "--enhancer", enhancer,
                "--size", str(size),
                "--facerender", "facevid2vid",
                "--verbose"
            ]
            
            logger.info(f"Running SadTalker command: {' '.join(cmd)}")
            
            # Change to SadTalker directory
            os.chdir("/opt/SadTalker")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1000)
            
            if result.returncode == 0:
                # Find generated video
                video_files = list(Path(result_dir).rglob("*.mp4"))
                
                if video_files:
                    video_path = str(video_files[0])
                    
                    with open(video_path, 'rb') as video_file:
                        video_data = video_file.read()
                    
                    video_base64 = base64.b64encode(video_data).decode()
                    
                    return {
                        "status": "success",
                        "video_base64": video_base64,
                        "enhancer": enhancer,
                        "size": size,
                        "gpu_used": str(device),
                        "video_size_bytes": len(video_data)
                    }
                else:
                    return {
                        "status": "error",
                        "error": "No video output generated",
                        "stdout": result.stdout,
                        "stderr": result.stderr
                    }
            else:
                return {
                    "status": "error",
                    "error": f"SadTalker failed with return code {result.returncode}",
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
                
    except Exception as e:
        logger.error(f"SadTalker generation failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.function(
    gpu="A100-80GB",
    timeout=300,
    memory=8192,
    image=gpu_image
)
def marp_slide_generation(
    markdown_content: str,
    theme: str = "default",
    output_format: str = "pdf"
) -> Dict[str, Any]:
    """Generate presentation slides using Marp CLI"""
    import subprocess
    import tempfile
    import base64
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create markdown file
            md_path = os.path.join(temp_dir, "slides.md")
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            # Output file
            output_ext = "pdf" if output_format == "pdf" else "html"
            output_path = os.path.join(temp_dir, f"slides.{output_ext}")
            
            # Build Marp command
            cmd = [
                "marp",
                md_path,
                "--output", output_path,
                "--theme", theme,
                "--allow-local-files"
            ]
            
            if output_format == "pdf":
                cmd.extend(["--pdf"])
            
            # Run Marp
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=240)
            
            if result.returncode == 0 and os.path.exists(output_path):
                with open(output_path, 'rb') as f:
                    file_data = f.read()
                
                file_base64 = base64.b64encode(file_data).decode()
                
                return {
                    "status": "success",
                    "slides_base64": file_base64,
                    "format": output_format,
                    "theme": theme,
                    "file_size_bytes": len(file_data)
                }
            else:
                return {
                    "status": "error",
                    "error": f"Marp failed: {result.stderr}",
                    "stdout": result.stdout
                }
                
    except Exception as e:
        logger.error(f"Marp generation failed: {e}")
        return {
            "status": "error",
            "error": str(e)
        }

@app.function(
    gpu="A100-80GB",
    timeout=1800,
    memory=32768,
    image=gpu_image
)
def complete_avatar_course_workflow(
    course_title: str,
    lesson_scripts: List[Dict[str, str]],
    avatar_image_base64: str,
    voice_preset: str = "tts_models/en/ljspeech/tacotron2-DDC"
) -> Dict[str, Any]:
    """Complete Avatar Course Creation workflow using A100 GPU"""
    import asyncio
    
    try:
        results = {
            "course_title": course_title,
            "total_lessons": len(lesson_scripts),
            "lessons": [],
            "slides": None,
            "status": "processing"
        }
        
        # Generate TTS and videos for each lesson
        for i, lesson in enumerate(lesson_scripts):
            logger.info(f"Processing lesson {i+1}/{len(lesson_scripts)}: {lesson.get('title', 'Untitled')}")
            
            lesson_result = {
                "lesson_id": i + 1,
                "title": lesson.get("title", f"Lesson {i+1}"),
                "script": lesson.get("script", ""),
                "tts_status": "pending",
                "video_status": "pending"
            }
            
            # Generate TTS
            tts_result = chatterbox_tts_a100.local(
                text=lesson["script"],
                voice_preset=voice_preset
            )
            
            if tts_result["status"] == "success":
                lesson_result["tts_status"] = "success"
                lesson_result["audio_base64"] = tts_result["audio_base64"]
                
                # Generate avatar video
                video_result = sadtalker_avatar_generation.local(
                    image_base64=avatar_image_base64,
                    audio_base64=tts_result["audio_base64"]
                )
                
                if video_result["status"] == "success":
                    lesson_result["video_status"] = "success"
                    lesson_result["video_base64"] = video_result["video_base64"]
                    lesson_result["video_size_bytes"] = video_result["video_size_bytes"]
                else:
                    lesson_result["video_status"] = "error"
                    lesson_result["video_error"] = video_result.get("error", "Unknown error")
            else:
                lesson_result["tts_status"] = "error"
                lesson_result["tts_error"] = tts_result.get("error", "Unknown error")
            
            results["lessons"].append(lesson_result)
        
        # Generate course slides
        slide_content = f"---\nmarp: true\ntheme: default\n---\n\n# {course_title}\n\n"
        for lesson in lesson_scripts:
            slide_content += f"## {lesson.get('title', 'Lesson')}\n\n{lesson.get('script', '')[:200]}...\n\n---\n\n"
        
        slides_result = marp_slide_generation.local(
            markdown_content=slide_content,
            theme="default",
            output_format="pdf"
        )
        
        if slides_result["status"] == "success":
            results["slides"] = {
                "status": "success",
                "slides_base64": slides_result["slides_base64"],
                "file_size_bytes": slides_result["file_size_bytes"]
            }
        else:
            results["slides"] = {
                "status": "error",
                "error": slides_result.get("error", "Unknown error")
            }
        
        # Final status
        successful_lessons = sum(1 for lesson in results["lessons"] if lesson.get("video_status") == "success")
        results["status"] = "completed"
        results["success_rate"] = f"{successful_lessons}/{len(lesson_scripts)}"
        
        return results
        
    except Exception as e:
        logger.error(f"Complete workflow failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "course_title": course_title
        }

@app.function(
    gpu="A100-80GB",
    timeout=60,
    memory=4096,
    image=gpu_image
)
def health_check():
    """Comprehensive health check for A100 GPU services"""
    return initialize_gpu_environment.local()

# Create local test entry point
@app.local_entrypoint()
def test_complete_setup():
    """Test the complete A100 GPU setup"""
    print("Testing Complete A100 GPU Setup for Course AI Platform")
    print("=" * 60)
    
    # Initialize environment
    print("🔧 Initializing GPU environment...")
    init_result = initialize_gpu_environment.remote()
    print(f"Initialization: {init_result}")
    
    if init_result.get("status") == "ready":
        print("✅ All systems ready!")
        
        # Test TTS
        print("\n🎤 Testing Chatterbox TTS...")
        tts_result = chatterbox_tts_a100.remote("Hello from A100 GPU! This is Chatterbox TTS.")
        print(f"TTS Status: {tts_result.get('status')}")
        
        # Test Marp
        print("\n📊 Testing Marp slide generation...")
        test_markdown = """---
marp: true
theme: default
---

# Test Presentation
## Generated by A100 GPU

This is a test slide created using Marp CLI on A100 GPU.
"""
        slides_result = marp_slide_generation.remote(test_markdown)
        print(f"Marp Status: {slides_result.get('status')}")
        
        print("\n🎯 A100 GPU setup complete and ready for Avatar Course Creation!")
        
    else:
        print("❌ Setup incomplete")
        print(f"Status: {init_result.get('status')}")
        print(f"Issues: {init_result}")
    
    return init_result

if __name__ == "__main__":
    test_complete_setup()