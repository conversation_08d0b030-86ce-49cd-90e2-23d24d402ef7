import React, { useState, useRef } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Loader2, Upload, Wand2, User, Camera, Sparkles, Download, Eye, ImageIcon, Library } from 'lucide-react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface AvatarSelectionProps {
  onAvatarSelect: (avatarData: any) => void;
  initialData?: any;
}

interface AvatarCharacteristics {
  gender: string;
  ethnicity: string;
  ageRange: string;
  hairColor: string;
  hairStyle: string;
  eyeColor: string;
  facialFeatures: string[];
  clothingStyle: string;
}

export function AvatarSelection({ onAvatarSelect, initialData }: AvatarSelectionProps) {
  const [activeTab, setActiveTab] = useState('generate');
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [generatedAvatars, setGeneratedAvatars] = useState<any[]>([]);
  const [selectedAvatar, setSelectedAvatar] = useState<any>(null);
  const [progress, setProgress] = useState(0);
  const [mediaImages, setMediaImages] = useState<any[]>([]);
  const [selectedMediaImage, setSelectedMediaImage] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Fetch media library images
  const { data: mediaLibrary } = useQuery({
    queryKey: ['/api/media'],
    queryFn: () => fetch('/api/media').then(res => res.json()),
    select: (data) => data?.filter((item: any) => item.type === 'image') || []
  });

  const [characteristics, setCharacteristics] = useState<AvatarCharacteristics>({
    gender: '',
    ethnicity: '',
    ageRange: '',
    hairColor: '',
    hairStyle: '',
    eyeColor: '',
    facialFeatures: [],
    clothingStyle: ''
  });

  // AI Avatar Generation Mutation
  const generateAvatarMutation = useMutation({
    mutationFn: async (characteristics: AvatarCharacteristics) => {
      const response = await fetch('/api/ai/generate-avatar', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(characteristics)
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate avatar');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      setGeneratedAvatars(data.variations || []);
      setProgress(100);
      toast({
        title: "Avatar Variations Generated",
        description: `${data.variations?.length || 1} realistic avatar variations created! Choose your favorite.`
      });
    },
    onError: (error: any) => {
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate avatar",
        variant: "destructive"
      });
      setProgress(0);
    }
  });

  // Photo Upload and Processing Mutation
  const processPhotoMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('photo', file);
      
      const response = await fetch('/api/ai/process-avatar-photo', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error('Failed to process photo');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      setGeneratedAvatars([{
        id: 'photo-avatar',
        imageUrl: data.avatarUrl,
        source: 'Photo Processing',
        style: 'From Your Photo',
        description: 'Avatar created from your uploaded photo'
      }]);
      setProgress(100);
      toast({
        title: "Avatar Created from Photo",
        description: "Your photo has been processed into an avatar!"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Processing Failed",
        description: error.message || "Failed to process photo",
        variant: "destructive"
      });
      setProgress(0);
    }
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type and size
      if (!file.type.match(/^image\/(jpeg|jpg|png)$/)) {
        toast({
          title: "Invalid File Type",
          description: "Please upload a JPG or PNG image",
          variant: "destructive"
        });
        return;
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast({
          title: "File Too Large",
          description: "Please upload an image smaller than 5MB",
          variant: "destructive"
        });
        return;
      }

      setUploadedImage(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerateAvatar = () => {
    // Validate required fields
    if (!characteristics.gender || !characteristics.ethnicity || !characteristics.ageRange) {
      toast({
        title: "Missing Information",
        description: "Please fill in gender, ethnicity, and age range",
        variant: "destructive"
      });
      return;
    }

    setProgress(20);
    generateAvatarMutation.mutate(characteristics);
  };

  const handleProcessPhoto = () => {
    if (!uploadedImage) {
      toast({
        title: "No Photo Selected",
        description: "Please upload a photo first",
        variant: "destructive"
      });
      return;
    }

    setProgress(20);
    processPhotoMutation.mutate(uploadedImage);
  };

  const handleFacialFeatureToggle = (feature: string) => {
    setCharacteristics(prev => ({
      ...prev,
      facialFeatures: prev.facialFeatures.includes(feature)
        ? prev.facialFeatures.filter(f => f !== feature)
        : [...prev.facialFeatures, feature]
    }));
  };

  const handleSelectAvatar = (avatar: any) => {
    if (avatar) {
      setSelectedAvatar(avatar);
      onAvatarSelect({
        avatarImageUrl: avatar.imageUrl,
        avatarType: activeTab,
        avatarVariation: avatar,
        characteristics: activeTab === 'generate' ? characteristics : null,
        sourcePhoto: activeTab === 'upload' ? uploadedImage?.name : null
      });
    }
  };

  const handleUseUploadedPhoto = () => {
    if (uploadedImage && previewUrl) {
      onAvatarSelect({
        avatarImageUrl: previewUrl,
        avatarType: 'direct-upload',
        sourcePhoto: uploadedImage.name,
        isDirectPhoto: true
      });
      toast({
        title: "Photo Selected as Avatar",
        description: "Your uploaded photo will be used directly as the avatar"
      });
    }
  };

  const handleSelectMediaImage = (mediaItem: any) => {
    setSelectedMediaImage(mediaItem);
    onAvatarSelect({
      avatarImageUrl: mediaItem.url,
      avatarType: 'media-library',
      mediaId: mediaItem.id,
      mediaName: mediaItem.title || mediaItem.filename,
      isDirectPhoto: true
    });
    toast({
      title: "Media Image Selected",
      description: "Selected image from your media library as avatar"
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Create Your Avatar</h2>
        <p className="text-muted-foreground">
          Design a realistic human avatar to represent the face of your AI-powered course content
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generate" className="gap-2">
            <Wand2 className="h-4 w-4" />
            AI Generate
          </TabsTrigger>
          <TabsTrigger value="upload" className="gap-2">
            <Camera className="h-4 w-4" />
            Process Photo
          </TabsTrigger>
          <TabsTrigger value="direct-photo" className="gap-2">
            <ImageIcon className="h-4 w-4" />
            Use Photo
          </TabsTrigger>
          <TabsTrigger value="media-library" className="gap-2">
            <Library className="h-4 w-4" />
            From Library
          </TabsTrigger>
        </TabsList>

        {/* AI-Powered Avatar Generation */}
        <TabsContent value="generate" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-primary" />
                Customize Your Avatar
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Gender */}
                <div className="space-y-2">
                  <Label htmlFor="gender">Gender *</Label>
                  <Select onValueChange={(value) => setCharacteristics(prev => ({ ...prev, gender: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="non-binary">Non-binary</SelectItem>
                      <SelectItem value="prefer-not-to-specify">Prefer not to specify</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Ethnicity */}
                <div className="space-y-2">
                  <Label htmlFor="ethnicity">Ethnicity/Origin *</Label>
                  <Select onValueChange={(value) => setCharacteristics(prev => ({ ...prev, ethnicity: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select ethnicity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="east-asian">East Asian</SelectItem>
                      <SelectItem value="african">African</SelectItem>
                      <SelectItem value="middle-eastern">Middle Eastern</SelectItem>
                      <SelectItem value="european">European</SelectItem>
                      <SelectItem value="hispanic">Hispanic/Latino</SelectItem>
                      <SelectItem value="south-asian">South Asian</SelectItem>
                      <SelectItem value="mixed">Mixed Heritage</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Age Range */}
                <div className="space-y-2">
                  <Label htmlFor="ageRange">Age Range *</Label>
                  <Select onValueChange={(value) => setCharacteristics(prev => ({ ...prev, ageRange: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select age range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="20-30">20-30 years</SelectItem>
                      <SelectItem value="30-40">30-40 years</SelectItem>
                      <SelectItem value="40-50">40-50 years</SelectItem>
                      <SelectItem value="50-60">50-60 years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Hair Color */}
                <div className="space-y-2">
                  <Label htmlFor="hairColor">Hair Color</Label>
                  <Select onValueChange={(value) => setCharacteristics(prev => ({ ...prev, hairColor: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select hair color" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="black">Black</SelectItem>
                      <SelectItem value="brown">Brown</SelectItem>
                      <SelectItem value="blonde">Blonde</SelectItem>
                      <SelectItem value="red">Red</SelectItem>
                      <SelectItem value="gray">Gray</SelectItem>
                      <SelectItem value="white">White</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Hair Style */}
                <div className="space-y-2">
                  <Label htmlFor="hairStyle">Hair Style</Label>
                  <Select onValueChange={(value) => setCharacteristics(prev => ({ ...prev, hairStyle: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select hair style" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="short">Short</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="long">Long</SelectItem>
                      <SelectItem value="curly">Curly</SelectItem>
                      <SelectItem value="straight">Straight</SelectItem>
                      <SelectItem value="wavy">Wavy</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Eye Color */}
                <div className="space-y-2">
                  <Label htmlFor="eyeColor">Eye Color</Label>
                  <Select onValueChange={(value) => setCharacteristics(prev => ({ ...prev, eyeColor: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select eye color" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="brown">Brown</SelectItem>
                      <SelectItem value="blue">Blue</SelectItem>
                      <SelectItem value="green">Green</SelectItem>
                      <SelectItem value="hazel">Hazel</SelectItem>
                      <SelectItem value="gray">Gray</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Facial Features */}
              <div className="space-y-2">
                <Label>Facial Features (Optional)</Label>
                <div className="flex flex-wrap gap-2">
                  {['glasses', 'beard', 'mustache', 'freckles', 'dimples', 'smile'].map((feature) => (
                    <Badge
                      key={feature}
                      variant={characteristics.facialFeatures.includes(feature) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => handleFacialFeatureToggle(feature)}
                    >
                      {feature.charAt(0).toUpperCase() + feature.slice(1)}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Clothing Style */}
              <div className="space-y-2">
                <Label htmlFor="clothingStyle">Clothing Style</Label>
                <Select onValueChange={(value) => setCharacteristics(prev => ({ ...prev, clothingStyle: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select clothing style" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="business">Business Professional</SelectItem>
                    <SelectItem value="casual">Casual</SelectItem>
                    <SelectItem value="smart-casual">Smart Casual</SelectItem>
                    <SelectItem value="academic">Academic</SelectItem>
                    <SelectItem value="creative">Creative</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Progress Bar */}
              {progress > 0 && progress < 100 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Generating Avatar...</Label>
                    <span className="text-sm text-muted-foreground">{progress}%</span>
                  </div>
                  <Progress value={progress} className="w-full" />
                </div>
              )}

              {/* Generate Button */}
              <Button 
                onClick={handleGenerateAvatar}
                disabled={generateAvatarMutation.isPending}
                className="w-full gap-2"
              >
                {generateAvatarMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Wand2 className="h-4 w-4" />
                )}
                Generate AI Avatar
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Avatar from Photo */}
        <TabsContent value="upload" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="h-5 w-5 text-primary" />
                Upload Your Photo
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground space-y-2">
                <p>Upload a clear, front-facing photo (JPG/PNG, max 5MB)</p>
                <p>For best results, use a high-quality photo with good lighting and minimal background</p>
              </div>

              {/* File Upload Area */}
              <div 
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center cursor-pointer hover:border-primary/50 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/jpeg,image/jpg,image/png"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                
                {previewUrl ? (
                  <div className="space-y-4">
                    <img 
                      src={previewUrl} 
                      alt="Upload preview"
                      className="max-w-48 max-h-48 mx-auto rounded-lg object-cover"
                    />
                    <p className="text-sm text-muted-foreground">Click to change photo</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                    <div>
                      <p className="text-lg font-medium">Click to upload photo</p>
                      <p className="text-sm text-muted-foreground">or drag and drop</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Progress Bar */}
              {progress > 0 && progress < 100 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label>Processing Photo...</Label>
                    <span className="text-sm text-muted-foreground">{progress}%</span>
                  </div>
                  <Progress value={progress} className="w-full" />
                </div>
              )}

              {/* Process Button */}
              <Button 
                onClick={handleProcessPhoto}
                disabled={!uploadedImage || processPhotoMutation.isPending}
                className="w-full gap-2"
              >
                {processPhotoMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Camera className="h-4 w-4" />
                )}
                Create Avatar from Photo
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Use Uploaded Photo Directly */}
        <TabsContent value="direct-photo" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5 text-primary" />
                Use Your Photo as Avatar
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center p-6 border-2 border-dashed border-primary/20 rounded-lg bg-gradient-to-br from-primary/5 to-purple/5">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/jpeg,image/jpg,image/png"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                
                {previewUrl ? (
                  <div className="space-y-4">
                    <img 
                      src={previewUrl} 
                      alt="Photo to use as avatar"
                      className="max-w-64 max-h-64 mx-auto rounded-lg object-cover border-2 border-primary/20"
                    />
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">
                        This photo will be used directly as your avatar
                      </p>
                      <div className="flex gap-2 justify-center">
                        <Button onClick={handleUseUploadedPhoto} className="gap-2">
                          <User className="h-4 w-4" />
                          Use This Photo as Avatar
                        </Button>
                        <Button 
                          variant="outline" 
                          onClick={() => fileInputRef.current?.click()}
                          className="gap-2"
                        >
                          <Upload className="h-4 w-4" />
                          Change Photo
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div 
                    className="space-y-4 cursor-pointer"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="h-12 w-12 mx-auto text-primary" />
                    <div>
                      <p className="text-lg font-medium">Upload Photo to Use as Avatar</p>
                      <p className="text-sm text-muted-foreground">
                        Your photo will be used directly without AI processing
                      </p>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="text-xs text-muted-foreground text-center space-y-1">
                <p>• For best results, use a clear, front-facing photo</p>
                <p>• Supports JPG, PNG formats up to 5MB</p>
                <p>• Photo will be used as-is without modification</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Media Library Selection */}
        <TabsContent value="media-library" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Library className="h-5 w-5 text-primary" />
                Select from Media Library
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {mediaLibrary && mediaLibrary.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
                  {mediaLibrary.map((item: any) => (
                    <div
                      key={item.id}
                      className={`relative cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                        selectedMediaImage?.id === item.id
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => handleSelectMediaImage(item)}
                    >
                      <img
                        src={item.url}
                        alt={item.title || 'Media item'}
                        className="w-full h-32 object-cover"
                      />
                      <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors" />
                      {selectedMediaImage?.id === item.id && (
                        <div className="absolute top-2 right-2">
                          <div className="bg-primary text-primary-foreground rounded-full p-1">
                            <Eye className="h-3 w-3" />
                          </div>
                        </div>
                      )}
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                        <p className="text-white text-xs truncate">
                          {item.title || item.filename}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 space-y-4">
                  <Library className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-lg font-medium">No Images in Media Library</p>
                    <p className="text-sm text-muted-foreground">
                      Upload images to your media library first to use them as avatars
                    </p>
                  </div>
                  <Button variant="outline" onClick={() => window.open('/media', '_blank')}>
                    Go to Media Library
                  </Button>
                </div>
              )}
              
              {selectedMediaImage && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <img
                      src={selectedMediaImage.url}
                      alt={selectedMediaImage.title}
                      className="w-12 h-12 rounded object-cover"
                    />
                    <div className="flex-1">
                      <p className="font-medium">{selectedMediaImage.title || selectedMediaImage.filename}</p>
                      <p className="text-sm text-blue-600">Selected as avatar</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Generated Avatar Variations */}
      {generatedAvatars.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-primary" />
              Choose Your Avatar ({generatedAvatars.length} variations)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {generatedAvatars.map((avatar, index) => (
                <div 
                  key={avatar.id || index}
                  className={`border rounded-lg p-4 cursor-pointer transition-all ${
                    selectedAvatar?.id === avatar.id 
                      ? 'border-primary bg-primary/5' 
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => setSelectedAvatar(avatar)}
                >
                  <div className="space-y-3">
                    <img 
                      src={avatar.imageUrl} 
                      alt={`Avatar variation ${index + 1}`}
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary" className="text-xs">
                          {avatar.source}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {avatar.style}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {avatar.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {selectedAvatar && (
              <div className="flex gap-2 pt-4 border-t">
                <Button 
                  onClick={() => handleSelectAvatar(selectedAvatar)} 
                  className="flex-1 gap-2"
                >
                  <User className="h-4 w-4" />
                  Use Selected Avatar
                </Button>
                <Button 
                  variant="outline" 
                  className="gap-2"
                  onClick={() => window.open(selectedAvatar.imageUrl, '_blank')}
                >
                  <Download className="h-4 w-4" />
                  Download
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}