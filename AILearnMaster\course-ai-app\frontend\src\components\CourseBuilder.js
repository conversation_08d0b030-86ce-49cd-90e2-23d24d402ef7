import React, { useState, useRef } from 'react';
import { Card, CardContent, TextField, Button, Grid, Typography, CircularProgress, Stepper, Step, StepLabel, Box, Dialog, DialogTitle, DialogContent, DialogActions, Chip } from '@mui/material';
import { School, Add, Download, PlayArrow, Stop, Save } from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import apiService from '../services/apiService';

const CourseBuilder = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [courseData, setCourseData] = useState({
    title: '',
    description: '',
    lessons: []
  });
  const [currentLesson, setCurrentLesson] = useState({
    title: '',
    script: '',
    image: null,
    imagePreview: '',
    generatedAssets: {
      audio: null,
      video: null,
      slides: null
    }
  });
  
  const [loading, setLoading] = useState(false);
  const [processingStep, setProcessingStep] = useState('');
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');
  const [previewDialog, setPreviewDialog] = useState(false);
  const [selectedPreview, setSelectedPreview] = useState(null);

  const steps = [
    'Course Setup',
    'Lesson Creation',
    'Asset Generation',
    'Review & Export'
  ];

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: (acceptedFiles) => {
      const file = acceptedFiles[0];
      if (file) {
        setCurrentLesson(prev => ({
          ...prev,
          image: file
        }));
        const reader = new FileReader();
        reader.onload = () => {
          setCurrentLesson(prev => ({
            ...prev,
            imagePreview: reader.result
          }));
        };
        reader.readAsDataURL(file);
      }
    },
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp']
    },
    maxFiles: 1
  });

  const sampleCourses = [
    {
      title: "Introduction to Machine Learning",
      description: "A comprehensive guide to ML fundamentals, algorithms, and practical applications.",
      lessons: [
        {
          title: "What is Machine Learning?",
          script: "Machine learning is a subset of artificial intelligence that focuses on creating systems that can learn and improve from experience without being explicitly programmed. In this lesson, we'll explore the core concepts and types of machine learning."
        },
        {
          title: "Types of Learning Algorithms",
          script: "There are three main types of machine learning: supervised learning, unsupervised learning, and reinforcement learning. Each approach has distinct characteristics and use cases that we'll examine in detail."
        },
        {
          title: "Data Preprocessing",
          script: "Before training any model, data must be cleaned, transformed, and prepared. This crucial step involves handling missing values, scaling features, and splitting datasets for training and testing."
        }
      ]
    },
    {
      title: "Web Development Fundamentals",
      description: "Learn the essential skills for modern web development including HTML, CSS, and JavaScript.",
      lessons: [
        {
          title: "HTML Structure and Semantics",
          script: "HTML provides the backbone of web pages through semantic markup. We'll learn how to structure content properly using elements like headers, paragraphs, lists, and sections."
        },
        {
          title: "CSS Styling and Layout",
          script: "Cascading Style Sheets control the visual presentation of web content. This lesson covers selectors, properties, flexbox, and grid systems for responsive design."
        },
        {
          title: "JavaScript Fundamentals",
          script: "JavaScript brings interactivity to web pages. We'll explore variables, functions, events, and DOM manipulation to create dynamic user experiences."
        }
      ]
    }
  ];

  const handleInputChange = (field, value) => {
    setCourseData(prev => ({ ...prev, [field]: value }));
  };

  const handleLessonChange = (field, value) => {
    setCurrentLesson(prev => ({ ...prev, [field]: value }));
  };

  const addLesson = () => {
    if (currentLesson.title && currentLesson.script) {
      setCourseData(prev => ({
        ...prev,
        lessons: [...prev.lessons, { ...currentLesson }]
      }));
      setCurrentLesson({
        title: '',
        script: '',
        image: null,
        imagePreview: '',
        generatedAssets: {
          audio: null,
          video: null,
          slides: null
        }
      });
    }
  };

  const removeLesson = (index) => {
    setCourseData(prev => ({
      ...prev,
      lessons: prev.lessons.filter((_, i) => i !== index)
    }));
  };

  const loadSampleCourse = (sample) => {
    setCourseData(sample);
    setActiveStep(1);
  };

  const generateAllAssets = async () => {
    if (courseData.lessons.length === 0) {
      setError('Please add at least one lesson before generating assets');
      return;
    }

    setLoading(true);
    setError('');
    const newResults = [];

    try {
      for (let i = 0; i < courseData.lessons.length; i++) {
        const lesson = courseData.lessons[i];
        setProcessingStep(`Processing lesson ${i + 1}: ${lesson.title}`);

        const lessonResult = {
          lessonIndex: i,
          title: lesson.title,
          assets: {}
        };

        // Generate TTS audio
        try {
          setProcessingStep(`Generating audio for lesson ${i + 1}...`);
          const audioResult = await apiService.generateChatterboxTTS({
            text: lesson.script,
            voice: 'v2/en_speaker_6'
          });
          
          if (audioResult.status === 'success') {
            lessonResult.assets.audio = audioResult.results[0];
          }
        } catch (audioError) {
          console.warn(`Audio generation failed for lesson ${i + 1}:`, audioError);
        }

        // Generate avatar video if image is provided
        if (lesson.image && lessonResult.assets.audio) {
          try {
            setProcessingStep(`Generating avatar video for lesson ${i + 1}...`);
            const imageBase64 = await apiService.fileToBase64(lesson.image);
            
            const videoResult = await apiService.generateVideo({
              imageBase64,
              audioBase64: lessonResult.assets.audio.audio_base64,
              enhancer: 'gfpgan',
              size: 512
            });
            
            if (videoResult.status === 'success') {
              lessonResult.assets.video = videoResult;
            }
          } catch (videoError) {
            console.warn(`Video generation failed for lesson ${i + 1}:`, videoError);
          }
        }

        // Generate slides
        try {
          setProcessingStep(`Generating slides for lesson ${i + 1}...`);
          const slideMarkdown = `---
marp: true
theme: default
---

# ${lesson.title}

${lesson.script.split('.').slice(0, 3).join('.\n\n')}

---

# Key Points

${lesson.script.split('.').slice(3, 6).map(point => `- ${point.trim()}`).join('\n')}

---

# Summary

${lesson.script.split('.').slice(-2).join('. ')}`;

          const slidesResult = await apiService.generateSlides({
            markdownContent: slideMarkdown,
            theme: 'default',
            outputFormat: 'pdf'
          });
          
          if (slidesResult.status === 'success') {
            lessonResult.assets.slides = slidesResult.results[0];
          }
        } catch (slidesError) {
          console.warn(`Slides generation failed for lesson ${i + 1}:`, slidesError);
        }

        newResults.push(lessonResult);
      }

      setResults(newResults);
      setActiveStep(3);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
      setProcessingStep('');
    }
  };

  const downloadAsset = (asset, type, lessonIndex) => {
    const lesson = courseData.lessons[lessonIndex];
    const timestamp = Date.now();
    
    switch (type) {
      case 'audio':
        apiService.downloadBase64File(
          asset.audio_base64,
          `${lesson.title.replace(/[^a-zA-Z0-9]/g, '_')}_audio_${timestamp}.wav`,
          'audio/wav'
        );
        break;
      case 'video':
        apiService.downloadBase64File(
          asset.video_base64,
          `${lesson.title.replace(/[^a-zA-Z0-9]/g, '_')}_video_${timestamp}.mp4`,
          'video/mp4'
        );
        break;
      case 'slides':
        apiService.downloadBase64File(
          asset.slides_base64,
          `${lesson.title.replace(/[^a-zA-Z0-9]/g, '_')}_slides_${timestamp}.pdf`,
          'application/pdf'
        );
        break;
    }
  };

  const exportCourse = () => {
    const courseExport = {
      ...courseData,
      generatedAt: new Date().toISOString(),
      results: results
    };
    
    const blob = new Blob([JSON.stringify(courseExport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${courseData.title.replace(/[^a-zA-Z0-9]/g, '_')}_course_${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const openPreview = (asset, type) => {
    setSelectedPreview({ asset, type });
    setPreviewDialog(true);
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Course Information</Typography>
                  
                  <TextField
                    fullWidth
                    label="Course Title"
                    value={courseData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    margin="normal"
                    placeholder="Enter your course title..."
                  />
                  
                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Course Description"
                    value={courseData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    margin="normal"
                    placeholder="Describe what students will learn..."
                  />
                  
                  <Button
                    variant="contained"
                    onClick={() => setActiveStep(1)}
                    disabled={!courseData.title || !courseData.description}
                    sx={{ mt: 2 }}
                    fullWidth
                  >
                    Continue to Lesson Creation
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Quick Start Templates</Typography>
                  
                  {sampleCourses.map((sample, index) => (
                    <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          {sample.title}
                        </Typography>
                        <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                          {sample.description}
                        </Typography>
                        <Typography variant="caption" display="block" sx={{ mb: 1 }}>
                          {sample.lessons.length} lessons included
                        </Typography>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => loadSampleCourse(sample)}
                          fullWidth
                        >
                          Use This Template
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Add New Lesson</Typography>
                  
                  <TextField
                    fullWidth
                    label="Lesson Title"
                    value={currentLesson.title}
                    onChange={(e) => handleLessonChange('title', e.target.value)}
                    margin="normal"
                  />
                  
                  <TextField
                    fullWidth
                    multiline
                    rows={6}
                    label="Lesson Script"
                    value={currentLesson.script}
                    onChange={(e) => handleLessonChange('script', e.target.value)}
                    margin="normal"
                    placeholder="Enter the lesson content and narration script..."
                  />
                  
                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                    Avatar Image (Optional):
                  </Typography>
                  <div {...getRootProps()} style={{
                    border: '2px dashed #ccc',
                    borderRadius: '8px',
                    padding: '20px',
                    textAlign: 'center',
                    cursor: 'pointer',
                    marginBottom: '16px',
                    backgroundColor: isDragActive ? '#f0f0f0' : 'white'
                  }}>
                    <input {...getInputProps()} />
                    {currentLesson.imagePreview ? (
                      <img 
                        src={currentLesson.imagePreview} 
                        alt="Preview" 
                        style={{ maxWidth: '150px', maxHeight: '150px', objectFit: 'cover' }}
                      />
                    ) : (
                      <Typography>
                        Drop an avatar image here for video generation
                      </Typography>
                    )}
                  </div>
                  
                  <Button
                    variant="contained"
                    startIcon={<Add />}
                    onClick={addLesson}
                    disabled={!currentLesson.title || !currentLesson.script}
                    fullWidth
                  >
                    Add Lesson
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Course Lessons ({courseData.lessons.length})
                  </Typography>
                  
                  {courseData.lessons.map((lesson, index) => (
                    <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                      <CardContent>
                        <Typography variant="subtitle1" gutterBottom>
                          {lesson.title}
                        </Typography>
                        <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                          {lesson.script.substring(0, 100)}...
                        </Typography>
                        {lesson.image && (
                          <Chip label="Has Avatar Image" size="small" sx={{ mr: 1 }} />
                        )}
                        <Button
                          size="small"
                          color="error"
                          onClick={() => removeLesson(index)}
                        >
                          Remove
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                  
                  {courseData.lessons.length > 0 && (
                    <Button
                      variant="contained"
                      onClick={() => setActiveStep(2)}
                      fullWidth
                      sx={{ mt: 2 }}
                    >
                      Continue to Asset Generation
                    </Button>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Generate Course Assets</Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                    This will generate audio narration, avatar videos (if images provided), and presentation slides for all lessons.
                  </Typography>
                  
                  {processingStep && (
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" gutterBottom>
                        {processingStep}
                      </Typography>
                      <CircularProgress size={24} />
                    </Box>
                  )}
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant="contained"
                        startIcon={loading ? <CircularProgress size={20} /> : <PlayArrow />}
                        onClick={generateAllAssets}
                        disabled={loading}
                        fullWidth
                        size="large"
                      >
                        {loading ? 'Generating...' : 'Generate All Assets'}
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant="outlined"
                        onClick={() => setActiveStep(1)}
                        disabled={loading}
                        fullWidth
                        size="large"
                      >
                        Back to Lessons
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Button
                        variant="outlined"
                        onClick={() => setActiveStep(3)}
                        disabled={results.length === 0}
                        fullWidth
                        size="large"
                      >
                        Skip to Review
                      </Button>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Course Review & Export</Typography>
                  
                  {results.length > 0 ? (
                    <>
                      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
                        Review your generated course assets and download individual files or export the complete course.
                      </Typography>
                      
                      {results.map((result, index) => (
                        <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                          <CardContent>
                            <Typography variant="subtitle1" gutterBottom>
                              Lesson {index + 1}: {result.title}
                            </Typography>
                            
                            <Grid container spacing={2}>
                              {result.assets.audio && (
                                <Grid item xs={12} sm={4}>
                                  <Card variant="outlined">
                                    <CardContent>
                                      <Typography variant="subtitle2">Audio Narration</Typography>
                                      <audio 
                                        controls 
                                        style={{ width: '100%', marginTop: '8px' }}
                                        src={`data:audio/wav;base64,${result.assets.audio.audio_base64}`}
                                      />
                                      <Button
                                        size="small"
                                        startIcon={<Download />}
                                        onClick={() => downloadAsset(result.assets.audio, 'audio', index)}
                                        fullWidth
                                        sx={{ mt: 1 }}
                                      >
                                        Download Audio
                                      </Button>
                                    </CardContent>
                                  </Card>
                                </Grid>
                              )}
                              
                              {result.assets.video && (
                                <Grid item xs={12} sm={4}>
                                  <Card variant="outlined">
                                    <CardContent>
                                      <Typography variant="subtitle2">Avatar Video</Typography>
                                      <video 
                                        controls 
                                        style={{ width: '100%', maxHeight: '150px', marginTop: '8px' }}
                                        src={`data:video/mp4;base64,${result.assets.video.video_base64}`}
                                      />
                                      <Button
                                        size="small"
                                        startIcon={<Download />}
                                        onClick={() => downloadAsset(result.assets.video, 'video', index)}
                                        fullWidth
                                        sx={{ mt: 1 }}
                                      >
                                        Download Video
                                      </Button>
                                    </CardContent>
                                  </Card>
                                </Grid>
                              )}
                              
                              {result.assets.slides && (
                                <Grid item xs={12} sm={4}>
                                  <Card variant="outlined">
                                    <CardContent>
                                      <Typography variant="subtitle2">Presentation Slides</Typography>
                                      <Box sx={{ 
                                        height: '100px', 
                                        backgroundColor: '#f5f5f5', 
                                        display: 'flex', 
                                        alignItems: 'center', 
                                        justifyContent: 'center',
                                        mt: 1,
                                        borderRadius: 1
                                      }}>
                                        <Typography color="textSecondary">PDF Slides</Typography>
                                      </Box>
                                      <Button
                                        size="small"
                                        startIcon={<Download />}
                                        onClick={() => downloadAsset(result.assets.slides, 'slides', index)}
                                        fullWidth
                                        sx={{ mt: 1 }}
                                      >
                                        Download Slides
                                      </Button>
                                    </CardContent>
                                  </Card>
                                </Grid>
                              )}
                            </Grid>
                          </CardContent>
                        </Card>
                      ))}
                      
                      <Button
                        variant="contained"
                        startIcon={<Save />}
                        onClick={exportCourse}
                        size="large"
                        sx={{ mt: 3 }}
                      >
                        Export Complete Course
                      </Button>
                    </>
                  ) : (
                    <Typography color="textSecondary">
                      No generated assets yet. Go back to generate course assets.
                    </Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <div className="course-builder">
      <Typography variant="h4" gutterBottom>
        <School /> AI Course Builder
      </Typography>
      
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {error && (
        <Card sx={{ backgroundColor: '#ffebee', mb: 3 }}>
          <CardContent>
            <Typography color="error">{error}</Typography>
          </CardContent>
        </Card>
      )}

      {renderStepContent()}

      <Dialog open={previewDialog} onClose={() => setPreviewDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Asset Preview</DialogTitle>
        <DialogContent>
          {selectedPreview && (
            <Box>
              {selectedPreview.type === 'audio' && (
                <audio controls style={{ width: '100%' }}>
                  <source src={`data:audio/wav;base64,${selectedPreview.asset.audio_base64}`} />
                </audio>
              )}
              {selectedPreview.type === 'video' && (
                <video controls style={{ width: '100%' }}>
                  <source src={`data:video/mp4;base64,${selectedPreview.asset.video_base64}`} />
                </video>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default CourseBuilder;