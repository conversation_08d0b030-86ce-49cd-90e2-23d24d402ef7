import { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  File, 
  FileImage, 
  FileText, 
  FileSpreadsheet, 
  FolderOpen,
  Upload,
  Download,
  Search,
  Share2,
  MoreVertical,
  Trash2,
  FolderPlus,
  Link,
  ExternalLink,
  Star,
  Pencil,
  Eye,
  Clock,
  Image
} from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { format } from 'date-fns';
import { apiRequest } from '@/lib/queryClient';

// Types
type User = {
  id: number;
  name: string;
  username: string;
  avatarUrl?: string;
};

type DocumentType = 'file' | 'image' | 'document' | 'spreadsheet' | 'folder';

type SharedDocument = {
  id: string;
  name: string;
  type: DocumentType;
  size: number;
  owner: User;
  lastModified: Date;
  shared: boolean;
  starred: boolean;
  thumbnailUrl?: string;
  folder?: string;
  tags?: string[];
};

interface SharedDocumentsProps {
  courseId?: number;
  teamId?: number;
}

// Sample data
const sampleUsers: User[] = [
  { id: 1, name: 'Current User', username: 'currentuser' },
  { id: 2, name: 'Alex Johnson', username: 'alexj' },
  { id: 3, name: 'Maria Garcia', username: 'mariag' },
  { id: 4, name: 'James Wilson', username: 'jamesw' },
];

const sampleDocuments: SharedDocument[] = [
  {
    id: '1',
    name: 'Course Materials',
    type: 'folder',
    size: 0,
    owner: sampleUsers[0],
    lastModified: new Date(2025, 3, 25),
    shared: true,
    starred: true,
    folder: '/'
  },
  {
    id: '2',
    name: 'Module 1 - Introduction.docx',
    type: 'document',
    size: 2500000,
    owner: sampleUsers[0],
    lastModified: new Date(2025, 3, 27),
    shared: true,
    starred: false,
    folder: '/'
  },
  {
    id: '3',
    name: 'Course Schedule.xlsx',
    type: 'spreadsheet',
    size: 1250000,
    owner: sampleUsers[1],
    lastModified: new Date(2025, 3, 26),
    shared: true,
    starred: true,
    folder: '/'
  },
  {
    id: '4',
    name: 'Project Timeline.pdf',
    type: 'file',
    size: 3500000,
    owner: sampleUsers[2],
    lastModified: new Date(2025, 3, 24),
    shared: true,
    starred: false,
    folder: '/'
  },
  {
    id: '5',
    name: 'Cover Image.jpg',
    type: 'image',
    size: 4200000,
    owner: sampleUsers[3],
    lastModified: new Date(2025, 3, 23),
    shared: true,
    starred: false,
    thumbnailUrl: 'https://via.placeholder.com/200x150',
    folder: '/',
    tags: ['image', 'cover']
  },
  {
    id: '6',
    name: 'References',
    type: 'folder',
    size: 0,
    owner: sampleUsers[0],
    lastModified: new Date(2025, 3, 22),
    shared: true,
    starred: false,
    folder: '/'
  },
  {
    id: '7',
    name: 'Draft Outline.docx',
    type: 'document',
    size: 1800000,
    owner: sampleUsers[1],
    lastModified: new Date(2025, 3, 28),
    shared: true,
    starred: false,
    folder: 'Course Materials'
  },
];

// Helper functions
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileIcon(type: DocumentType) {
  switch (type) {
    case 'file':
      return <File className="h-5 w-5 text-blue-500" />;
    case 'image':
      return <FileImage className="h-5 w-5 text-emerald-500" />;
    case 'document':
      return <FileText className="h-5 w-5 text-indigo-500" />;
    case 'spreadsheet':
      return <FileSpreadsheet className="h-5 w-5 text-green-500" />;
    case 'folder':
      return <FolderOpen className="h-5 w-5 text-amber-500" />;
    default:
      return <File className="h-5 w-5 text-slate-500" />;
  }
}

export default function SharedDocuments({ courseId, teamId }: SharedDocumentsProps) {
  const { toast } = useToast();
  const [documents, setDocuments] = useState<SharedDocument[]>(sampleDocuments);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentFolder, setCurrentFolder] = useState('/');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  
  // Filter documents based on search query and current folder
  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = searchQuery === '' || 
      doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (doc.tags && doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase())));
    
    const matchesFolder = doc.folder === currentFolder;
    
    return matchesSearch && matchesFolder;
  });
  
  // Extract unique folders for breadcrumb navigation
  const folderPath = currentFolder.split('/').filter(Boolean);
  
  // Handle folder navigation
  const handleFolderClick = (folder: string) => {
    setCurrentFolder(folder);
  };
  
  // Handle navigation via breadcrumbs
  const navigateToFolder = (index: number) => {
    if (index === -1) {
      setCurrentFolder('/');
    } else {
      const path = ['', ...folderPath.slice(0, index + 1)].join('/');
      setCurrentFolder(path);
    }
  };
  
  // Handle starring a document
  const handleToggleStar = (id: string) => {
    setDocuments(documents.map(doc => 
      doc.id === id ? { ...doc, starred: !doc.starred } : doc
    ));
    
    const doc = documents.find(d => d.id === id);
    toast({
      title: doc?.starred ? 'Removed from favorites' : 'Added to favorites',
      description: `"${doc?.name}" has been ${doc?.starred ? 'removed from' : 'added to'} your favorites`,
    });
  };
  
  // Handle document deletion
  const handleDeleteDocument = (id: string) => {
    const doc = documents.find(d => d.id === id);
    setDocuments(documents.filter(d => d.id !== id));
    
    toast({
      title: 'Document Deleted',
      description: `"${doc?.name}" has been deleted`,
    });
  };
  
  // Handle file upload
  const handleUpload = () => {
    if (!selectedFile) {
      toast({
        title: 'No file selected',
        description: 'Please select a file to upload',
        variant: 'destructive',
      });
      return;
    }
    
    // In a real implementation, this would use the API to upload the file
    const fileType: DocumentType = selectedFile.type.includes('image') 
      ? 'image' 
      : selectedFile.type.includes('sheet') 
        ? 'spreadsheet' 
        : selectedFile.type.includes('document') || selectedFile.name.endsWith('.docx') || selectedFile.name.endsWith('.doc')
          ? 'document' 
          : 'file';
    
    const newDoc: SharedDocument = {
      id: Date.now().toString(),
      name: selectedFile.name,
      type: fileType,
      size: selectedFile.size,
      owner: sampleUsers[0], // Current user
      lastModified: new Date(),
      shared: true,
      starred: false,
      folder: currentFolder,
    };
    
    setDocuments([...documents, newDoc]);
    setSelectedFile(null);
    setShowUploadDialog(false);
    
    toast({
      title: 'File Uploaded',
      description: `"${selectedFile.name}" has been uploaded successfully`,
    });
  };
  
  // Handle new folder creation
  const handleCreateFolder = () => {
    const folderName = prompt('Enter folder name:');
    if (!folderName) return;
    
    const newFolder: SharedDocument = {
      id: Date.now().toString(),
      name: folderName,
      type: 'folder',
      size: 0,
      owner: sampleUsers[0], // Current user
      lastModified: new Date(),
      shared: true,
      starred: false,
      folder: currentFolder,
    };
    
    setDocuments([...documents, newFolder]);
    
    toast({
      title: 'Folder Created',
      description: `Folder "${folderName}" has been created successfully`,
    });
  };
  
  // Render document grid view
  const renderGridView = () => (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
      {filteredDocuments.map(doc => (
        <Card key={doc.id} className="overflow-hidden hover:shadow-md">
          <div 
            className="h-32 flex items-center justify-center bg-slate-50 border-b"
            onClick={() => doc.type === 'folder' && handleFolderClick(doc.name)}
          >
            {doc.type === 'image' && doc.thumbnailUrl ? (
              <div 
                className="w-full h-full bg-cover bg-center" 
                style={{ backgroundImage: `url(${doc.thumbnailUrl})` }}
              />
            ) : (
              <div className="text-4xl">
                {getFileIcon(doc.type)}
              </div>
            )}
          </div>
          
          <CardContent className="p-3">
            <div className="flex items-start justify-between mb-1">
              <div className="truncate flex-grow" title={doc.name}>
                <span className="font-medium text-sm">{doc.name}</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 shrink-0 text-slate-400 hover:text-amber-500"
                onClick={() => handleToggleStar(doc.id)}
              >
                <Star className={`h-4 w-4 ${doc.starred ? 'fill-amber-500 text-amber-500' : ''}`} />
              </Button>
            </div>
            
            <div className="text-xs text-slate-500 flex justify-between items-center">
              <span>{doc.type !== 'folder' ? formatFileSize(doc.size) : '--'}</span>
              <span>{format(new Date(doc.lastModified), 'MMM d')}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
  
  // Render document list view
  const renderListView = () => (
    <Card>
      <CardContent className="p-0">
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left p-3 text-sm font-medium text-slate-500">Name</th>
              <th className="text-left p-3 text-sm font-medium text-slate-500 hidden sm:table-cell">Owner</th>
              <th className="text-left p-3 text-sm font-medium text-slate-500 hidden lg:table-cell">Last Modified</th>
              <th className="text-left p-3 text-sm font-medium text-slate-500 hidden md:table-cell">Size</th>
              <th className="text-right p-3 text-sm font-medium text-slate-500">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredDocuments.map(doc => (
              <tr key={doc.id} className="border-b hover:bg-slate-50">
                <td className="p-3">
                  <div 
                    className="flex items-center cursor-pointer"
                    onClick={() => doc.type === 'folder' && handleFolderClick(doc.name)}
                  >
                    <div className="mr-2">
                      {getFileIcon(doc.type)}
                    </div>
                    <div className="truncate max-w-[200px] sm:max-w-[300px]" title={doc.name}>
                      {doc.name}
                    </div>
                    {doc.starred && (
                      <Star className="h-4 w-4 ml-2 fill-amber-500 text-amber-500" />
                    )}
                  </div>
                </td>
                <td className="p-3 hidden sm:table-cell">
                  <div className="flex items-center">
                    <Avatar className="h-6 w-6 mr-2">
                      {doc.owner.avatarUrl ? (
                        <AvatarImage src={doc.owner.avatarUrl} alt={doc.owner.name} />
                      ) : (
                        <AvatarFallback>{doc.owner.name.charAt(0)}</AvatarFallback>
                      )}
                    </Avatar>
                    <span className="text-sm">{doc.owner.name}</span>
                  </div>
                </td>
                <td className="p-3 text-sm text-slate-600 hidden lg:table-cell">
                  {format(new Date(doc.lastModified), 'MMM d, yyyy')}
                </td>
                <td className="p-3 text-sm text-slate-600 hidden md:table-cell">
                  {doc.type !== 'folder' ? formatFileSize(doc.size) : '--'}
                </td>
                <td className="p-3 text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {doc.type !== 'folder' && (
                        <DropdownMenuItem onClick={() => {
                          toast({
                            title: 'Downloading File',
                            description: `Downloading "${doc.name}"`,
                          });
                        }}>
                          <Download className="h-4 w-4 mr-2" /> Download
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => {
                        toast({
                          title: 'Sharing Options',
                          description: 'Sharing options will be available in a future update',
                        });
                      }}>
                        <Share2 className="h-4 w-4 mr-2" /> Share
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleToggleStar(doc.id)}>
                        <Star className={`h-4 w-4 mr-2 ${doc.starred ? 'fill-amber-500 text-amber-500' : ''}`} />
                        {doc.starred ? 'Remove from Favorites' : 'Add to Favorites'}
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDeleteDocument(doc.id)}>
                        <Trash2 className="h-4 w-4 mr-2 text-red-500" /> Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </CardContent>
    </Card>
  );
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Shared Documents</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleCreateFolder}>
            <FolderPlus className="h-4 w-4 mr-2" /> New Folder
          </Button>
          <Button onClick={() => setShowUploadDialog(true)}>
            <Upload className="h-4 w-4 mr-2" /> Upload File
          </Button>
        </div>
      </div>
      
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        {/* Breadcrumb Navigation */}
        <div className="flex items-center overflow-auto">
          <Button
            variant="ghost"
            size="sm"
            className={`px-2 h-7 ${currentFolder === '/' ? 'bg-slate-100' : ''}`}
            onClick={() => navigateToFolder(-1)}
          >
            Home
          </Button>
          
          {folderPath.map((folder, index) => (
            <div key={index} className="flex items-center">
              <span className="mx-1 text-slate-400">/</span>
              <Button
                variant="ghost"
                size="sm"
                className="px-2 h-7"
                onClick={() => navigateToFolder(index)}
              >
                {folder}
              </Button>
            </div>
          ))}
        </div>
        
        <div className="flex gap-2">
          <div className="relative flex-grow">
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search files..."
              className="pl-9"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          </div>
          
          <div className="flex border rounded-md">
            <Button
              variant="ghost"
              size="icon"
              className={`rounded-r-none ${viewMode === 'grid' ? 'bg-slate-100' : ''}`}
              onClick={() => setViewMode('grid')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                <rect x="3" y="3" width="7" height="7"></rect>
                <rect x="14" y="3" width="7" height="7"></rect>
                <rect x="3" y="14" width="7" height="7"></rect>
                <rect x="14" y="14" width="7" height="7"></rect>
              </svg>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className={`rounded-l-none ${viewMode === 'list' ? 'bg-slate-100' : ''}`}
              onClick={() => setViewMode('list')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                <line x1="8" y1="6" x2="21" y2="6"></line>
                <line x1="8" y1="12" x2="21" y2="12"></line>
                <line x1="8" y1="18" x2="21" y2="18"></line>
                <line x1="3" y1="6" x2="3.01" y2="6"></line>
                <line x1="3" y1="12" x2="3.01" y2="12"></line>
                <line x1="3" y1="18" x2="3.01" y2="18"></line>
              </svg>
            </Button>
          </div>
        </div>
      </div>
      
      {/* File Grid or List View */}
      {filteredDocuments.length === 0 ? (
        <div className="text-center py-10 bg-slate-50 rounded-lg border-2 border-dashed border-slate-200">
          <FolderOpen className="h-12 w-12 mx-auto text-slate-300 mb-3" />
          <h3 className="text-lg font-medium text-slate-900 mb-1">
            No documents found
          </h3>
          <p className="text-slate-500 mb-4">
            {searchQuery 
              ? 'No documents match your search criteria' 
              : 'This folder is empty'
            }
          </p>
          {!searchQuery && (
            <div className="flex justify-center gap-3">
              <Button variant="outline" onClick={handleCreateFolder}>
                <FolderPlus className="h-4 w-4 mr-2" /> New Folder
              </Button>
              <Button onClick={() => setShowUploadDialog(true)}>
                <Upload className="h-4 w-4 mr-2" /> Upload Files
              </Button>
            </div>
          )}
        </div>
      ) : viewMode === 'grid' ? renderGridView() : renderListView()}
      
      {/* File Stats */}
      <div className="text-sm text-slate-500 pt-2">
        {filteredDocuments.length} item{filteredDocuments.length !== 1 ? 's' : ''} •
        {' '}
        {formatFileSize(
          filteredDocuments
            .filter(doc => doc.type !== 'folder')
            .reduce((total, doc) => total + doc.size, 0)
        )}
      </div>
      
      {/* Upload Dialog */}
      <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Upload File</DialogTitle>
            <DialogDescription>
              Upload a file to the current folder.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="border-2 border-dashed rounded-lg p-6 text-center">
              {selectedFile ? (
                <div className="flex flex-col items-center">
                  <div className="text-4xl mb-2">
                    {selectedFile.type.includes('image') 
                      ? <Image className="h-12 w-12 text-emerald-500" />
                      : <File className="h-12 w-12 text-blue-500" />
                    }
                  </div>
                  <p className="text-sm font-medium mb-1">{selectedFile.name}</p>
                  <p className="text-xs text-slate-500">{formatFileSize(selectedFile.size)}</p>
                  <Button 
                    variant="link" 
                    size="sm" 
                    className="mt-2" 
                    onClick={() => setSelectedFile(null)}
                  >
                    Remove
                  </Button>
                </div>
              ) : (
                <>
                  <Upload className="h-10 w-10 text-slate-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-600 mb-2">
                    Drag and drop files here or click to browse
                  </p>
                  <p className="text-xs text-slate-500">
                    Supported file formats: JPG, PNG, PDF, DOC, DOCX, XLS, XLSX
                  </p>
                  <Input
                    type="file"
                    className="hidden"
                    id="file-upload"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        setSelectedFile(e.target.files[0]);
                      }
                    }}
                  />
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => document.getElementById('file-upload')?.click()}
                  >
                    Browse Files
                  </Button>
                </>
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowUploadDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpload} disabled={!selectedFile}>
              Upload
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}