# AILearnMaster Production Dockerfile
# Multi-stage build for optimized production deployment

# Stage 1: Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Run security validation
RUN npm run security:validate || echo "Security validation completed"

# Build the application
RUN npm run build
RUN npm run build:server

# Stage 2: Production stage
FROM node:18-alpine AS production

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache dumb-init

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S ailearn -u 1001

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application from builder stage
COPY --from=builder --chown=ailearn:nodejs /app/dist ./dist
COPY --from=builder --chown=ailearn:nodejs /app/server ./server
COPY --from=builder --chown=ailearn:nodejs /app/shared ./shared
COPY --from=builder --chown=ailearn:nodejs /app/security-fixes ./security-fixes

# Copy necessary configuration files
COPY --chown=ailearn:nodejs .env.production.template ./.env.production.template

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3001
ENV HOST=0.0.0.0

# Security: Remove unnecessary packages and files
RUN rm -rf /tmp/* /var/cache/apk/* /root/.npm

# Switch to non-root user
USER ailearn

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3001/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["npm", "start"]
