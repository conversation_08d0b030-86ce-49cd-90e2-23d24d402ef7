import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ExternalLink, Info } from 'lucide-react';
import { PlatformIcon } from './platform-logos';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

interface FacebookConnectDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConnect: () => void;
}

export default function FacebookConnectDialog({
  isOpen,
  onClose,
  onConnect,
}: FacebookConnectDialogProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [popup, setPopup] = useState<Window | null>(null);
  const { toast } = useToast();
  
  useEffect(() => {
    // Function to check connection status periodically
    let checkInterval: NodeJS.Timeout | null = null;
    
    // Handle popup window closing
    const checkPopupClosed = () => {
      if (popup && popup.closed) {
        // Check if the connection was successful by fetching integrations
        apiRequest('GET', '/api/integrations')
          .then(res => res.json())
          .then(integrations => {
            const fbIntegration = integrations.find((i: any) => i.platform === 'facebook');
            if (fbIntegration) {
              onConnect();
              if (checkInterval) clearInterval(checkInterval);
              setIsConnecting(false);
              toast({
                title: "Facebook connected",
                description: "Your Facebook account has been successfully connected."
              });
            } else {
              throw new Error("Facebook connection failed or was cancelled");
            }
          })
          .catch(err => {
            setError("Connection failed. Please try again.");
            setIsConnecting(false);
            if (checkInterval) clearInterval(checkInterval);
          });
      }
    };
    
    if (popup) {
      checkInterval = setInterval(checkPopupClosed, 1000);
    }
    
    return () => {
      if (checkInterval) {
        clearInterval(checkInterval);
      }
    };
  }, [popup, onConnect, toast]);
  
  const handleConnect = async () => {
    setError(null);
    setIsConnecting(true);
    
    try {
      // Initiate Facebook OAuth flow
      const response = await apiRequest('GET', '/api/platform-integrations/facebook/oauth');
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to initialize connection");
      }
      
      const data = await response.json();
      
      if (data.authUrl) {
        // Open the auth URL in a popup window
        const width = 600;
        const height = 700;
        const left = window.innerWidth / 2 - width / 2;
        const top = window.innerHeight / 2 - height / 2;
        
        const newPopup = window.open(
          data.authUrl,
          "Connect Facebook",
          `width=${width},height=${height},left=${left},top=${top}`
        );
        
        if (!newPopup || newPopup.closed) {
          setError("Popup blocked. Please allow popups for this site.");
          setIsConnecting(false);
        } else {
          setPopup(newPopup);
        }
      } else {
        throw new Error("No authorization URL returned");
      }
    } catch (err: any) {
      setError(err.message || "Failed to connect");
      setIsConnecting(false);
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={() => !isConnecting && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-muted rounded-md">
              <PlatformIcon platform="SiFacebook" size={24} />
            </div>
            <DialogTitle>Connect to Facebook</DialogTitle>
          </div>
          <DialogDescription>
            Connect your Facebook account to enable publishing and sharing your courses.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-2">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2 mb-4">
            <h4 className="font-medium text-sm">Instructions:</h4>
            <ul className="space-y-1 text-sm text-muted-foreground">
              <li className="flex items-start gap-2">
                <span className="text-foreground font-medium">1.</span>
                <span>Click the Connect button to authorize access.</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-foreground font-medium">2.</span>
                <span>You'll be redirected to Facebook to sign in.</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-foreground font-medium">3.</span>
                <span>Grant permission to access your Facebook account.</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-foreground font-medium">4.</span>
                <span>After authorizing, you'll be returned here automatically.</span>
              </li>
            </ul>
          </div>
          
          <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
            <div className="flex gap-2">
              <Info className="h-5 w-5 text-blue-600 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">What will be shared?</p>
                <p>This integration allows you to post course content to your Facebook pages and groups. We'll never post without your explicit permission.</p>
              </div>
            </div>
          </div>
          
          <div className="flex gap-2 text-xs items-center text-muted-foreground">
            <ExternalLink className="h-3 w-3" /> 
            <span>
              Need help? <a href="https://facebook.com/help" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline inline-flex items-center">
                View Facebook documentation
              </a>
            </span>
          </div>
        </div>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={onClose}
            disabled={isConnecting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleConnect}
            disabled={isConnecting}
          >
            {isConnecting ? "Connecting..." : "Connect to Facebook"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}