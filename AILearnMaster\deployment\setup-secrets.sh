#!/bin/bash

# AWS Secrets Manager Setup for AILearnMaster
# This script creates and configures all production secrets

set -e

echo "🔐 Setting up AWS Secrets Manager for AILearnMaster"
echo "=================================================="

# Configuration
REGION="us-east-1"
SECRET_PREFIX="ailearn-master/production"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to generate secure random string
generate_secret() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Function to create or update secret
create_or_update_secret() {
    local secret_name=$1
    local secret_value=$2
    local description=$3
    
    print_status "Creating/updating secret: $secret_name"
    
    # Check if secret exists
    if aws secretsmanager describe-secret --secret-id "$secret_name" --region $REGION >/dev/null 2>&1; then
        # Update existing secret
        aws secretsmanager update-secret \
            --secret-id "$secret_name" \
            --secret-string "$secret_value" \
            --region $REGION >/dev/null
        print_success "Updated existing secret: $secret_name"
    else
        # Create new secret
        aws secretsmanager create-secret \
            --name "$secret_name" \
            --description "$description" \
            --secret-string "$secret_value" \
            --region $REGION >/dev/null
        print_success "Created new secret: $secret_name"
    fi
}

# Function to prompt for user input
prompt_for_value() {
    local prompt_text=$1
    local default_value=$2
    local is_secret=${3:-false}
    
    if [ "$is_secret" = true ]; then
        echo -n "$prompt_text: "
        read -s user_input
        echo
    else
        echo -n "$prompt_text [$default_value]: "
        read user_input
    fi
    
    if [ -z "$user_input" ]; then
        echo "$default_value"
    else
        echo "$user_input"
    fi
}

# Main setup function
setup_secrets() {
    print_status "Starting secrets setup..."
    
    # Generate secure secrets
    print_status "Generating secure secrets..."
    SESSION_SECRET=$(generate_secret 32)
    JWT_SECRET=$(generate_secret 32)
    ENCRYPTION_KEY=$(generate_secret 32)
    
    print_success "Generated secure secrets"
    
    # Collect database information
    print_warning "Please provide your production database information:"
    DATABASE_URL=$(prompt_for_value "Database URL (with sslmode=require)" "postgresql://user:pass@host:port/db?sslmode=require")
    
    # Collect AI service keys
    print_warning "Please provide your AI service API keys:"
    OPENAI_API_KEY=$(prompt_for_value "OpenAI API Key" "" true)
    MODAL_TOKEN_ID=$(prompt_for_value "Modal Token ID" "" true)
    MODAL_TOKEN_SECRET=$(prompt_for_value "Modal Token Secret" "" true)
    ELEVENLABS_API_KEY=$(prompt_for_value "ElevenLabs API Key (optional)" "" true)
    
    # Collect AWS service information
    print_warning "Please provide your AWS service information:"
    AWS_ACCESS_KEY_ID=$(prompt_for_value "AWS Access Key ID" "" true)
    AWS_SECRET_ACCESS_KEY=$(prompt_for_value "AWS Secret Access Key" "" true)
    AWS_S3_BUCKET=$(prompt_for_value "S3 Bucket Name" "ailearn-master-storage-prod")
    AWS_CLOUDFRONT_DOMAIN=$(prompt_for_value "CloudFront Domain (optional)" "")
    
    # Create database secrets
    DATABASE_SECRET=$(cat <<EOF
{
    "DATABASE_URL": "$DATABASE_URL"
}
EOF
)
    
    create_or_update_secret \
        "$SECRET_PREFIX/database" \
        "$DATABASE_SECRET" \
        "Production database credentials for AILearnMaster"
    
    # Create session secrets
    SESSION_SECRET_JSON=$(cat <<EOF
{
    "SESSION_SECRET": "$SESSION_SECRET",
    "JWT_SECRET": "$JWT_SECRET",
    "SECRETS_ENCRYPTION_KEY": "$ENCRYPTION_KEY"
}
EOF
)
    
    create_or_update_secret \
        "$SECRET_PREFIX/session" \
        "$SESSION_SECRET_JSON" \
        "Session and JWT secrets for AILearnMaster"
    
    # Create AI service secrets
    AI_SERVICES_SECRET=$(cat <<EOF
{
    "OPENAI_API_KEY": "$OPENAI_API_KEY",
    "MODAL_TOKEN_ID": "$MODAL_TOKEN_ID",
    "MODAL_TOKEN_SECRET": "$MODAL_TOKEN_SECRET",
    "ELEVENLABS_API_KEY": "$ELEVENLABS_API_KEY"
}
EOF
)
    
    create_or_update_secret \
        "$SECRET_PREFIX/ai-services" \
        "$AI_SERVICES_SECRET" \
        "AI service API keys for AILearnMaster"
    
    # Create AWS service secrets
    AWS_SERVICES_SECRET=$(cat <<EOF
{
    "AWS_ACCESS_KEY_ID": "$AWS_ACCESS_KEY_ID",
    "AWS_SECRET_ACCESS_KEY": "$AWS_SECRET_ACCESS_KEY",
    "AWS_S3_BUCKET": "$AWS_S3_BUCKET",
    "AWS_CLOUDFRONT_DOMAIN": "$AWS_CLOUDFRONT_DOMAIN"
}
EOF
)
    
    create_or_update_secret \
        "$SECRET_PREFIX/aws-services" \
        "$AWS_SERVICES_SECRET" \
        "AWS service credentials for AILearnMaster"
    
    print_success "All secrets have been created/updated successfully!"
}

# Function to verify secrets
verify_secrets() {
    print_status "Verifying created secrets..."
    
    local secrets=(
        "$SECRET_PREFIX/database"
        "$SECRET_PREFIX/session"
        "$SECRET_PREFIX/ai-services"
        "$SECRET_PREFIX/aws-services"
    )
    
    for secret in "${secrets[@]}"; do
        if aws secretsmanager describe-secret --secret-id "$secret" --region $REGION >/dev/null 2>&1; then
            print_success "✓ $secret exists"
        else
            print_error "✗ $secret not found"
        fi
    done
}

# Function to test secret retrieval
test_secret_retrieval() {
    print_status "Testing secret retrieval..."
    
    # Test retrieving a secret (without displaying the value)
    if aws secretsmanager get-secret-value --secret-id "$SECRET_PREFIX/session" --region $REGION >/dev/null 2>&1; then
        print_success "Secret retrieval test passed"
    else
        print_error "Secret retrieval test failed"
    fi
}

# Function to display summary
display_summary() {
    echo ""
    print_success "🎉 Secrets setup completed successfully!"
    echo "======================================"
    echo ""
    echo "Created secrets:"
    echo "• $SECRET_PREFIX/database - Database connection string"
    echo "• $SECRET_PREFIX/session - Session and JWT secrets"
    echo "• $SECRET_PREFIX/ai-services - AI service API keys"
    echo "• $SECRET_PREFIX/aws-services - AWS service credentials"
    echo ""
    echo "Next steps:"
    echo "1. Run the environment setup script: node deployment/production-env-setup.js"
    echo "2. Configure Amplify environment variables"
    echo "3. Deploy the application"
    echo ""
    print_warning "Important: Keep your AWS credentials secure and rotate secrets regularly!"
}

# Main execution
main() {
    # Check if AWS CLI is configured
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    # Check if required tools are available
    if ! command -v openssl &> /dev/null; then
        print_error "OpenSSL is required but not installed."
        exit 1
    fi
    
    print_status "AWS CLI is configured and ready"
    
    # Run setup
    setup_secrets
    verify_secrets
    test_secret_retrieval
    display_summary
}

# Run main function
main "$@"
