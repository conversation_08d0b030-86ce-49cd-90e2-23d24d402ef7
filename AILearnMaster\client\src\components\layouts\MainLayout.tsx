import { ReactNode, useState, useEffect } from "react";
import { SidebarWithColorfulIcons } from "./SidebarWithColorfulIcons";
import { SmartSidebar } from "./SmartSidebar";
import { Header } from "./Header";
import { User } from "@/types";
import { cn } from "@/lib/utils";
import { FloatingChatAssistant } from "@/components/chat/FloatingChatAssistant";

interface MainLayoutProps {
  children: ReactNode;
  user: User | null;
  isLoading: boolean;
}

export function MainLayout({ children, user, isLoading }: MainLayoutProps) {
  const [showSmartSidebar, setShowSmartSidebar] = useState(true);
  const [smartSidebarCollapsed, setSmartSidebarCollapsed] = useState(false);

  // Load initial sidebar state from localStorage
  useEffect(() => {
    const savedSidebarState = localStorage.getItem('smart-sidebar-visible');
    if (savedSidebarState !== null) {
      setShowSmartSidebar(JSON.parse(savedSidebarState));
    }
    
    const savedCollapsedState = localStorage.getItem('smart-sidebar-collapsed');
    if (savedCollapsedState !== null) {
      setSmartSidebarCollapsed(JSON.parse(savedCollapsedState));
    }
  }, []);

  // Toggle smart sidebar visibility
  const toggleSmartSidebar = () => {
    const newState = !showSmartSidebar;
    setShowSmartSidebar(newState);
    localStorage.setItem('smart-sidebar-visible', JSON.stringify(newState));
  };

  // Update collapsed state
  const handleSidebarCollapsedChange = (collapsed: boolean) => {
    setSmartSidebarCollapsed(collapsed);
    localStorage.setItem('smart-sidebar-collapsed', JSON.stringify(collapsed));
  };

  // Calculate right padding based on sidebar state
  const contentRightPadding = showSmartSidebar 
    ? (smartSidebarCollapsed ? 'pr-16' : 'pr-64')
    : '';

  return (
    <div className="flex min-h-screen flex-col md:flex-row bg-slate-50/50">
      <SidebarWithColorfulIcons user={user} />
      
      {/* The main content area */}
      <main className="flex-1 md:ml-64 bg-gradient-to-b from-slate-50 to-white">
        <Header onToggleSmartSidebar={toggleSmartSidebar} showSmartSidebar={showSmartSidebar} />
        
        {/* Loading indicator */}
        {isLoading ? (
          <div className="fixed top-4 right-4 flex items-center gap-2 glass-effect rounded-full px-4 py-2 shadow-lg z-50 border border-primary/10">
            <div className="w-4 h-4 rounded-full bg-gradient-to-r from-primary to-[#4dabf7] animate-rotate-loading" />
            <span className="text-sm font-medium text-primary">Loading...</span>
          </div>
        ) : null}

        {/* Smart Sidebar */}
        {showSmartSidebar && user && (
          <SmartSidebar onCollapsedChange={handleSidebarCollapsedChange} />
        )}
        
        {/* Main content with adjusted right padding for smart sidebar */}
        <div className={cn(
          "transition-all duration-300 pb-16",
          contentRightPadding
        )}>
          {children}
        </div>
      </main>
      
      {/* Floating Chat Assistant */}
      {user && <FloatingChatAssistant />}
    </div>
  );
}
