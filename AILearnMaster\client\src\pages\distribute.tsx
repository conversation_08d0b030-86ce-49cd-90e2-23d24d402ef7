import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Megaphone, 
  Share2, 
  Store, 
  VideoIcon, 
  Mail, 
  BarChart, 
  Settings, 
  ExternalLink, 
  Upload, 
  CheckCircle, 
  Clock, 
  Users, 
  DollarSign,
  Target,
  TrendingUp,
  Zap,
  Globe,
  Rocket,
  AlertCircle,
  ArrowRight,
  Plus,
  Eye,
  Play,
  Calendar
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Link } from 'wouter';

interface DistributionChannel {
  id: string;
  name: string;
  description: string;
  category: 'marketplace' | 'social' | 'video' | 'email' | 'direct';
  icon: string;
  status: 'connected' | 'available' | 'premium';
  features: string[];
  audience: string;
  commission?: number;
  setupComplexity: 'easy' | 'medium' | 'advanced';
  estimatedReach: string;
  avgRevenue?: string;
}

interface DistributionCampaign {
  id: string;
  name: string;
  courseTitle: string;
  channels: string[];
  status: 'draft' | 'active' | 'paused' | 'completed';
  reach: number;
  clicks: number;
  conversions: number;
  revenue: number;
  startDate: string;
  endDate?: string;
}

export default function DistributePage() {
  const [selectedChannel, setSelectedChannel] = useState<DistributionChannel | null>(null);
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    courseId: '',
    channels: [] as string[],
    budget: '',
    targetAudience: '',
    message: ''
  });
  const [showCreateCampaign, setShowCreateCampaign] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch distribution data
  const { data: channels = [], isLoading: channelsLoading } = useQuery({
    queryKey: ['/api/distribution/channels'],
    queryFn: () => apiRequest('/api/distribution/channels')
  });

  const { data: campaigns = [], isLoading: campaignsLoading } = useQuery({
    queryKey: ['/api/distribution/campaigns'],
    queryFn: () => apiRequest('/api/distribution/campaigns')
  });

  const { data: analytics, isLoading: analyticsLoading } = useQuery({
    queryKey: ['/api/distribution/analytics'],
    queryFn: () => apiRequest('/api/distribution/analytics')
  });

  // Mock data for demo purposes
  const distributionChannels: DistributionChannel[] = [
    {
      id: 'udemy',
      name: 'Udemy',
      description: 'World\'s largest online learning marketplace',
      category: 'marketplace',
      icon: '🎓',
      status: 'available',
      features: ['Global reach', 'Built-in marketing', 'Student reviews', 'Mobile app'],
      audience: '57M+ students',
      commission: 50,
      setupComplexity: 'medium',
      estimatedReach: '10K-100K',
      avgRevenue: '$1,200/month'
    },
    {
      id: 'youtube',
      name: 'YouTube',
      description: 'Share course previews and build audience',
      category: 'video',
      icon: '📺',
      status: 'connected',
      features: ['Video hosting', 'Monetization', 'Analytics', 'Live streaming'],
      audience: '2B+ users',
      setupComplexity: 'easy',
      estimatedReach: '1K-50K',
      avgRevenue: '$800/month'
    },
    {
      id: 'linkedin',
      name: 'LinkedIn Learning',
      description: 'Professional development platform',
      category: 'marketplace',
      icon: '💼',
      status: 'premium',
      features: ['Professional audience', 'Enterprise sales', 'Certificates', 'High value'],
      audience: '27M+ professionals',
      commission: 25,
      setupComplexity: 'advanced',
      estimatedReach: '5K-25K',
      avgRevenue: '$3,200/month'
    },
    {
      id: 'teachable',
      name: 'Teachable',
      description: 'Create your own course website',
      category: 'direct',
      icon: '🏫',
      status: 'available',
      features: ['Custom branding', 'No commission', 'Direct payments', 'Full control'],
      audience: '1M+ creators',
      commission: 0,
      setupComplexity: 'medium',
      estimatedReach: '500-5K',
      avgRevenue: '$1,800/month'
    },
    {
      id: 'mailchimp',
      name: 'Email Marketing',
      description: 'Direct email campaigns to your audience',
      category: 'email',
      icon: '📧',
      status: 'connected',
      features: ['Automation', 'Segmentation', 'Analytics', 'Templates'],
      audience: 'Your subscribers',
      setupComplexity: 'easy',
      estimatedReach: 'Varies',
      avgRevenue: '$500/campaign'
    },
    {
      id: 'facebook',
      name: 'Facebook Ads',
      description: 'Targeted social media advertising',
      category: 'social',
      icon: '📘',
      status: 'available',
      features: ['Precise targeting', 'Video ads', 'Retargeting', 'Lookalike audiences'],
      audience: '2.9B+ users',
      setupComplexity: 'medium',
      estimatedReach: '10K-1M',
      avgRevenue: '$2,000/month'
    }
  ];

  const mockCampaigns: DistributionCampaign[] = [
    {
      id: '1',
      name: 'Web Development Course Launch',
      courseTitle: 'Complete Web Development Bootcamp',
      channels: ['udemy', 'youtube', 'mailchimp'],
      status: 'active',
      reach: 25420,
      clicks: 1840,
      conversions: 156,
      revenue: 7800,
      startDate: '2025-06-20'
    },
    {
      id: '2',
      name: 'React Course Promotion',
      courseTitle: 'React Masterclass 2025',
      channels: ['youtube', 'linkedin', 'facebook'],
      status: 'active',
      reach: 18650,
      clicks: 1230,
      conversions: 89,
      revenue: 4450,
      startDate: '2025-06-18'
    },
    {
      id: '3',
      name: 'Python for Beginners',
      courseTitle: 'Python Programming Fundamentals',
      channels: ['teachable', 'mailchimp'],
      status: 'draft',
      reach: 0,
      clicks: 0,
      conversions: 0,
      revenue: 0,
      startDate: '2025-06-25'
    }
  ];

  // Create campaign mutation
  const createCampaignMutation = useMutation({
    mutationFn: async (campaignData: any) => {
      return apiRequest('/api/distribution/campaigns', {
        method: 'POST',
        body: JSON.stringify(campaignData)
      });
    },
    onSuccess: () => {
      toast({
        title: "Campaign Created",
        description: "Your distribution campaign has been created successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/distribution/campaigns'] });
      setShowCreateCampaign(false);
      setCampaignForm({
        name: '',
        courseId: '',
        channels: [],
        budget: '',
        targetAudience: '',
        message: ''
      });
    },
    onError: (error: any) => {
      toast({
        title: "Creation Failed",
        description: error.message || "Failed to create campaign",
        variant: "destructive",
      });
    }
  });

  const getChannelIcon = (category: string) => {
    switch (category) {
      case 'marketplace': return <Store className="h-5 w-5" />;
      case 'video': return <VideoIcon className="h-5 w-5" />;
      case 'social': return <Share2 className="h-5 w-5" />;
      case 'email': return <Mail className="h-5 w-5" />;
      case 'direct': return <Globe className="h-5 w-5" />;
      default: return <Megaphone className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'premium': return 'bg-purple-100 text-purple-800';
      case 'available': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCampaignStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCreateCampaign = () => {
    if (!campaignForm.name || !campaignForm.courseId || campaignForm.channels.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    createCampaignMutation.mutate(campaignForm);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
              <Megaphone className="h-6 w-6 text-white" />
            </div>
            Distribute Your Courses
          </h1>
          <p className="text-muted-foreground mt-2">
            Reach more students by distributing your courses across multiple platforms and channels
          </p>
        </div>
        <Dialog open={showCreateCampaign} onOpenChange={setShowCreateCampaign}>
          <DialogTrigger asChild>
            <Button className="gap-2">
              <Rocket className="h-4 w-4" />
              Create Campaign
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Distribution Campaign</DialogTitle>
              <DialogDescription>
                Set up a new campaign to distribute your course across multiple channels
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="campaign-name">Campaign Name</Label>
                  <Input
                    id="campaign-name"
                    placeholder="Enter campaign name"
                    value={campaignForm.name}
                    onChange={(e) => setCampaignForm(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="course-select">Course</Label>
                  <Select value={campaignForm.courseId} onValueChange={(value) => setCampaignForm(prev => ({ ...prev, courseId: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select course" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="course1">Complete Web Development Bootcamp</SelectItem>
                      <SelectItem value="course2">React Masterclass 2025</SelectItem>
                      <SelectItem value="course3">Python Programming Fundamentals</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label>Distribution Channels</Label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {distributionChannels.slice(0, 6).map((channel) => (
                    <div key={channel.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={channel.id}
                        checked={campaignForm.channels.includes(channel.id)}
                        onChange={(e) => {
                          const channels = e.target.checked
                            ? [...campaignForm.channels, channel.id]
                            : campaignForm.channels.filter(c => c !== channel.id);
                          setCampaignForm(prev => ({ ...prev, channels }));
                        }}
                        className="rounded"
                      />
                      <label htmlFor={channel.id} className="text-sm flex items-center gap-2">
                        <span className="text-lg">{channel.icon}</span>
                        {channel.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="budget">Monthly Budget</Label>
                  <Input
                    id="budget"
                    placeholder="$500"
                    value={campaignForm.budget}
                    onChange={(e) => setCampaignForm(prev => ({ ...prev, budget: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="target">Target Audience</Label>
                  <Input
                    id="target"
                    placeholder="Web developers, beginners"
                    value={campaignForm.targetAudience}
                    onChange={(e) => setCampaignForm(prev => ({ ...prev, targetAudience: e.target.value }))}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="message">Campaign Message</Label>
                <Textarea
                  id="message"
                  placeholder="Describe your course and what makes it special..."
                  value={campaignForm.message}
                  onChange={(e) => setCampaignForm(prev => ({ ...prev, message: e.target.value }))}
                  rows={3}
                />
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateCampaign(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateCampaign} disabled={createCampaignMutation.isPending}>
                  {createCampaignMutation.isPending ? 'Creating...' : 'Create Campaign'}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Distribution Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Reach</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">44,070</div>
                <p className="text-xs text-muted-foreground">+18.2% from last month</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Conversions</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">245</div>
                <p className="text-xs text-muted-foreground">+12.3% from last month</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$12,250</div>
                <p className="text-xs text-muted-foreground">+25.1% from last month</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Channels</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4</div>
                <p className="text-xs text-muted-foreground">2 pending setup</p>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 rounded-full bg-blue-100">
                  <Store className="h-6 w-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold">Course Marketplaces</h3>
                  <p className="text-sm text-muted-foreground">Publish to Udemy, Coursera, and more</p>
                </div>
                <Link href="/platform-integrations/marketplaces">
                  <Button size="sm" variant="outline">
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 rounded-full bg-red-100">
                  <VideoIcon className="h-6 w-6 text-red-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold">Video Platforms</h3>
                  <p className="text-sm text-muted-foreground">Share on YouTube, Vimeo, TikTok</p>
                </div>
                <Link href="/platform-integrations/video">
                  <Button size="sm" variant="outline">
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-4">
                <div className="p-3 rounded-full bg-green-100">
                  <Mail className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold">Email Marketing</h3>
                  <p className="text-sm text-muted-foreground">Direct email campaigns</p>
                </div>
                <Link href="/marketing/email">
                  <Button size="sm" variant="outline">
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Distribution Activity</CardTitle>
              <CardDescription>Latest updates from your distribution channels</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { 
                    channel: 'Udemy', 
                    action: 'Course approved and published', 
                    time: '2 hours ago', 
                    status: 'success',
                    revenue: '+$450'
                  },
                  { 
                    channel: 'YouTube', 
                    action: 'Video uploaded: Course Preview', 
                    time: '5 hours ago', 
                    status: 'success',
                    views: '1.2K views'
                  },
                  { 
                    channel: 'Email Campaign', 
                    action: 'Newsletter sent to 2,400 subscribers', 
                    time: '1 day ago', 
                    status: 'success',
                    clicks: '180 clicks'
                  },
                  { 
                    channel: 'LinkedIn Learning', 
                    action: 'Course under review', 
                    time: '2 days ago', 
                    status: 'pending',
                    eta: 'Review ETA: 3-5 days'
                  }
                ].map((activity, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        activity.status === 'success' ? 'bg-green-500' :
                        activity.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
                      }`} />
                      <div>
                        <p className="font-medium">{activity.channel}</p>
                        <p className="text-sm text-muted-foreground">{activity.action}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-muted-foreground">{activity.time}</p>
                      {activity.revenue && <p className="text-sm font-medium text-green-600">{activity.revenue}</p>}
                      {activity.views && <p className="text-sm font-medium text-blue-600">{activity.views}</p>}
                      {activity.clicks && <p className="text-sm font-medium text-purple-600">{activity.clicks}</p>}
                      {activity.eta && <p className="text-sm font-medium text-yellow-600">{activity.eta}</p>}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="channels" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {distributionChannels.map((channel) => (
              <Card key={channel.id} className="relative hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-3xl">{channel.icon}</span>
                      <div>
                        <CardTitle className="text-xl">{channel.name}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          {getChannelIcon(channel.category)}
                          <span className="text-sm text-muted-foreground capitalize">{channel.category}</span>
                          <Badge className={getStatusColor(channel.status)}>
                            {channel.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{channel.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Audience:</span>
                      <p className="text-muted-foreground">{channel.audience}</p>
                    </div>
                    <div>
                      <span className="font-medium">Est. Revenue:</span>
                      <p className="text-muted-foreground">{channel.avgRevenue || 'Varies'}</p>
                    </div>
                    {channel.commission && (
                      <div>
                        <span className="font-medium">Commission:</span>
                        <p className="text-muted-foreground">{channel.commission}%</p>
                      </div>
                    )}
                    <div>
                      <span className="font-medium">Setup:</span>
                      <p className="text-muted-foreground capitalize">{channel.setupComplexity}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Key Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {channel.features.map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  {channel.status === 'connected' ? (
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4 mr-2" />
                      Manage
                    </Button>
                  ) : channel.status === 'premium' ? (
                    <Button variant="outline" size="sm" disabled>
                      <Zap className="h-4 w-4 mr-2" />
                      Upgrade Required
                    </Button>
                  ) : (
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Connect
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Learn More
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <div className="space-y-4">
            {mockCampaigns.map((campaign) => (
              <Card key={campaign.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {campaign.name}
                        <Badge className={getCampaignStatusColor(campaign.status)}>
                          {campaign.status}
                        </Badge>
                      </CardTitle>
                      <CardDescription>{campaign.courseTitle}</CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Settings className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        <BarChart className="h-4 w-4 mr-2" />
                        Analytics
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Eye className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Reach</span>
                      </div>
                      <p className="text-2xl font-bold">{campaign.reach.toLocaleString()}</p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Clicks</span>
                      </div>
                      <p className="text-2xl font-bold">{campaign.clicks.toLocaleString()}</p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Target className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Conversions</span>
                      </div>
                      <p className="text-2xl font-bold">{campaign.conversions}</p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Revenue</span>
                      </div>
                      <p className="text-2xl font-bold">${campaign.revenue.toLocaleString()}</p>
                    </div>
                  </div>
                  
                  <div className="mt-4 flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">Started: {campaign.startDate}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">Channels:</span>
                      <div className="flex gap-1">
                        {campaign.channels.map((channelId) => {
                          const channel = distributionChannels.find(c => c.id === channelId);
                          return channel ? (
                            <span key={channelId} className="text-lg" title={channel.name}>
                              {channel.icon}
                            </span>
                          ) : null;
                        })}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Channel Performance</CardTitle>
                <CardDescription>Revenue by distribution channel</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {distributionChannels.filter(c => c.status === 'connected').map((channel) => (
                    <div key={channel.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-lg">{channel.icon}</span>
                        <span className="font-medium">{channel.name}</span>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{channel.avgRevenue}</p>
                        <Progress value={Math.random() * 100} className="w-20 h-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Conversion Funnel</CardTitle>
                <CardDescription>How users move through your distribution funnel</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { stage: 'Impressions', count: 44070, rate: 100 },
                    { stage: 'Clicks', count: 3070, rate: 7.0 },
                    { stage: 'Landing Page Views', count: 2450, rate: 5.6 },
                    { stage: 'Course Preview Views', count: 1680, rate: 3.8 },
                    { stage: 'Enrollments', count: 245, rate: 0.6 }
                  ].map((item) => (
                    <div key={item.stage} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{item.stage}</span>
                        <span className="text-sm text-muted-foreground">
                          {item.count.toLocaleString()} ({item.rate}%)
                        </span>
                      </div>
                      <Progress value={item.rate * 10} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Distribution Insights</CardTitle>
              <CardDescription>Key insights and recommendations for your distribution strategy</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Alert>
                  <TrendingUp className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Opportunity:</strong> Your YouTube channel has high engagement. Consider creating more video content to drive traffic to your courses.
                  </AlertDescription>
                </Alert>
                <Alert>
                  <Target className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Tip:</strong> Email campaigns have the highest conversion rate (12.3%). Consider increasing your email marketing budget.
                  </AlertDescription>
                </Alert>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Action Needed:</strong> Your LinkedIn Learning application is still under review. Follow up if it's been more than 2 weeks.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}