import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { CalendarIcon, Check, ExternalLink, Layers, Info, ArrowLeft, ArrowRight, BookOpen, Tag, Eye, EyeOff, Users, GraduationCap, FileText, FileBadge } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { Section, CourseStructureData } from "./CourseSectionsBuilder";

interface CoursePublishFormProps {
  courseStructure: CourseStructureData;
  courseDetails: any;
  onSubmit: (data: any) => void;
  onBack: () => void;
  isEdit?: boolean;
  defaultValues?: any;
}

export function CoursePublishForm({
  courseStructure,
  courseDetails,
  onSubmit,
  onBack,
  isEdit = false,
  defaultValues = {
    price: "0",
    isPublic: true,
    isPublished: false,
    tagline: "",
    learningObjectives: "",
    prerequisites: "",
    tags: ""
  }
}: CoursePublishFormProps) {
  const [formData, setFormData] = useState(defaultValues);
  const [publishDate, setPublishDate] = useState<Date | undefined>(
    formData.publishDate ? new Date(formData.publishDate) : undefined
  );
  const { toast } = useToast();
  
  // Count lessons by type
  const lessonCounts = {
    total: 0,
    video: 0,
    text: 0,
    quiz: 0
  };
  
  courseStructure.sections.forEach(section => {
    section.lessons.forEach(lesson => {
      lessonCounts.total++;
      lessonCounts[lesson.type]++;
    });
  });
  
  // Calculate estimated duration
  const calculateDuration = () => {
    let totalMinutes = 0;
    
    courseStructure.sections.forEach(section => {
      section.lessons.forEach(lesson => {
        // Add duration if available
        if (lesson.duration) {
          totalMinutes += lesson.duration;
        } else {
          // Estimate durations based on content type
          if (lesson.type === 'video') {
            totalMinutes += 5; // Default 5 minutes per video
          } else if (lesson.type === 'text') {
            // Estimate based on text length (if available)
            if (lesson.content) {
              const wordCount = lesson.content.split(/\s+/).length;
              totalMinutes += Math.max(1, Math.round(wordCount / 200)); // Assuming 200 words per minute
            } else {
              totalMinutes += 3; // Default 3 minutes per text lesson
            }
          } else if (lesson.type === 'quiz') {
            totalMinutes += 10; // Default 10 minutes per quiz
          }
        }
      });
    });
    
    return totalMinutes;
  };
  
  const totalDuration = calculateDuration();
  
  // Format duration as hours and minutes
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''} ${mins > 0 ? `${mins} min${mins > 1 ? 's' : ''}` : ''}`;
    }
    
    return `${mins} minute${mins !== 1 ? 's' : ''}`;
  };
  
  // Form change handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate essential info
    if (!formData.tagline.trim()) {
      toast({
        title: "Tagline required",
        description: "Please provide a short tagline for your course.",
        variant: "destructive",
      });
      return;
    }
    
    // If publishing, validate price
    if (formData.isPublished) {
      const price = parseFloat(formData.price);
      
      if (isNaN(price) || price < 0) {
        toast({
          title: "Invalid price",
          description: "Please enter a valid price (0 or higher).",
          variant: "destructive",
        });
        return;
      }
    }
    
    // Prepare final data
    const finalData = {
      ...formData,
      publishDate: publishDate ? publishDate.toISOString() : undefined
    };
    
    onSubmit(finalData);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold">Publish Your Course</h2>
          <p className="text-muted-foreground">
            Finalize your course details and settings for publication
          </p>
        </div>
        <Badge variant={formData.isPublished ? "default" : "secondary"} className="px-3 py-1">
          {formData.isPublished ? "Published" : "Draft"}
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main form area */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Course Information</CardTitle>
                <CardDescription>
                  These details will be visible to potential students
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">Course Title</Label>
                  <Input 
                    id="title" 
                    value={courseDetails.title}
                    disabled
                    className="bg-muted/50"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    To change the course title, go back to the Course Details step
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="tagline">Tagline</Label>
                  <Input 
                    id="tagline" 
                    name="tagline"
                    placeholder="Enter a concise, catchy tagline for your course"
                    value={formData.tagline}
                    onChange={handleInputChange}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    A short, compelling description to appear under your course title (max 100 characters)
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="description">Full Description</Label>
                  <Textarea 
                    id="description" 
                    rows={3}
                    value={courseDetails.description}
                    disabled
                    className="bg-muted/50"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    To change the description, go back to the Course Details step
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="learningObjectives">Learning Objectives</Label>
                  <Textarea 
                    id="learningObjectives" 
                    name="learningObjectives"
                    placeholder="What will students learn from this course?"
                    rows={3}
                    value={formData.learningObjectives}
                    onChange={handleInputChange}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    List the key skills and knowledge students will gain from your course
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="prerequisites">Prerequisites</Label>
                  <Textarea 
                    id="prerequisites" 
                    name="prerequisites"
                    placeholder="Any requirements or prior knowledge needed?"
                    rows={2}
                    value={formData.prerequisites}
                    onChange={handleInputChange}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    List any requirements or prior knowledge students need before taking this course
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="tags">Tags</Label>
                  <Input 
                    id="tags" 
                    name="tags"
                    placeholder="e.g. javascript, beginners, coding, web development"
                    value={formData.tags}
                    onChange={handleInputChange}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Add relevant keywords separated by commas to help students find your course
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Publishing Settings</CardTitle>
                <CardDescription>
                  Configure how your course will be available to students
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col space-y-3">
                  <div className="flex items-center justify-between space-x-2">
                    <div className="space-y-0.5">
                      <Label htmlFor="isPublic">Public Visibility</Label>
                      <p className="text-xs text-muted-foreground">
                        When enabled, your course will be discoverable in search and browse
                      </p>
                    </div>
                    <Switch 
                      id="isPublic"
                      checked={formData.isPublic}
                      onCheckedChange={(checked) => handleSwitchChange('isPublic', checked)}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between space-x-2">
                    <div className="space-y-0.5">
                      <Label htmlFor="isPublished">Publish Status</Label>
                      <p className="text-xs text-muted-foreground">
                        Make your course available for enrollment
                      </p>
                    </div>
                    <Switch 
                      id="isPublished"
                      checked={formData.isPublished}
                      onCheckedChange={(checked) => handleSwitchChange('isPublished', checked)}
                    />
                  </div>
                  
                  {formData.isPublished && (
                    <div className="pl-6 border-l-2 border-primary/20 ml-2 mt-2">
                      <div className="space-y-2">
                        <Label htmlFor="publishDate">Publish Date</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !publishDate && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {publishDate ? format(publishDate, "PPP") : "Select a date"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={publishDate}
                              onSelect={setPublishDate}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <p className="text-xs text-muted-foreground">
                          Optional: Schedule your course to be published at a future date
                        </p>
                      </div>
                      
                      <div className="space-y-2 mt-4">
                        <Label htmlFor="price">Price (USD)</Label>
                        <Input 
                          id="price" 
                          name="price"
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          value={formData.price}
                          onChange={handleInputChange}
                        />
                        <p className="text-xs text-muted-foreground">
                          Set to 0 for a free course
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            
            <Separator className="my-6" />
            
            <div className="flex justify-between">
              <Button 
                type="button"
                variant="outline" 
                onClick={onBack}
                className="gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Quizzes
              </Button>
              
              <Button type="submit" className="gap-2">
                {isEdit 
                  ? 'Update Course' 
                  : formData.isPublished 
                    ? 'Publish Course' 
                    : 'Save as Draft'
                }
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </div>
        
        {/* Course summary sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Course Summary</CardTitle>
              <CardDescription>
                Overview of your course content
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Course stats */}
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <BookOpen className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="font-medium">Content Overview</h4>
                    <div className="text-sm text-muted-foreground space-y-1 mt-1">
                      <p className="flex justify-between">
                        <span>Sections:</span> 
                        <span className="font-medium text-foreground">{courseStructure.sections.length}</span>
                      </p>
                      <p className="flex justify-between">
                        <span>Lessons:</span> 
                        <span className="font-medium text-foreground">{lessonCounts.total}</span>
                      </p>
                      <p className="flex justify-between">
                        <span>Video lessons:</span> 
                        <span className="font-medium text-foreground">{lessonCounts.video}</span>
                      </p>
                      <p className="flex justify-between">
                        <span>Text lessons:</span> 
                        <span className="font-medium text-foreground">{lessonCounts.text}</span>
                      </p>
                      <p className="flex justify-between">
                        <span>Quizzes:</span> 
                        <span className="font-medium text-foreground">{lessonCounts.quiz}</span>
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <Tag className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="font-medium">Course Details</h4>
                    <div className="text-sm text-muted-foreground space-y-1 mt-1">
                      <p className="flex justify-between">
                        <span>Category:</span> 
                        <span className="font-medium text-foreground">{courseDetails.category}</span>
                      </p>
                      <p className="flex justify-between">
                        <span>Format:</span> 
                        <span className="font-medium text-foreground capitalize">{courseDetails.format}</span>
                      </p>
                      <p className="flex justify-between">
                        <span>Estimated duration:</span> 
                        <span className="font-medium text-foreground">{formatDuration(totalDuration)}</span>
                      </p>
                      <p className="flex justify-between">
                        <span>Price:</span> 
                        <span className="font-medium text-foreground">
                          {formData.price === "0" ? "Free" : `$${parseFloat(formData.price).toFixed(2)}`}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <FileBadge className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="font-medium">Access Settings</h4>
                    <div className="text-sm text-muted-foreground space-y-1 mt-1">
                      <p className="flex items-center">
                        {formData.isPublic ? (
                          <>
                            <Eye className="h-4 w-4 mr-1 text-green-600" />
                            <span>Publicly visible</span>
                          </>
                        ) : (
                          <>
                            <EyeOff className="h-4 w-4 mr-1 text-amber-600" />
                            <span>Private - only available with direct link</span>
                          </>
                        )}
                      </p>
                      <p className="flex items-center">
                        {formData.isPublished ? (
                          <>
                            <Users className="h-4 w-4 mr-1 text-green-600" />
                            <span>Open for enrollment</span>
                          </>
                        ) : (
                          <>
                            <FileText className="h-4 w-4 mr-1 text-slate-500" />
                            <span>Draft - not available for enrollment</span>
                          </>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Info box */}
              <div className="bg-blue-50 text-blue-800 p-4 rounded-md flex items-start space-x-3 text-sm">
                <Info className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">Ready to publish?</p>
                  <p className="mt-1">
                    You can save your course as a draft now and publish it later when it's ready.
                    {!formData.isPublished && " Toggle 'Publish Status' to make it available to students."}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}