#!/usr/bin/env python3
"""
Open-Source AI Pipeline Test Suite
Tests the complete restructured platform with Mistral, Coqui TTS, Kokoro TTS, EchoMimic V2, and Marp
"""

import requests
import json
import base64
import time
from typing import Dict, Any

def create_test_image_base64() -> str:
    """Create a simple test image in base64 format"""
    # Simple 1x1 pixel PNG
    return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

def create_test_audio_base64() -> str:
    """Create a simple test audio in base64 format"""
    # Minimal WAV header + silence
    return "UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA="

def test_modal_health():
    """Test Modal A100 GPU health"""
    print("🔍 Testing Modal A100 GPU Health...")
    
    try:
        response = requests.get(
            "https://trade-digital--courseai-opensource-health.modal.run",
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Modal A100 Status: HEALTHY")
            print(f"✅ GPU Available: {data.get('gpu_available')}")
            print(f"✅ Services: {data.get('services')}")
            return {"status": "success", "data": data}
        else:
            print(f"❌ Modal A100 Status: HTTP {response.status_code}")
            return {"status": "error", "error": f"HTTP {response.status_code}"}
    except Exception as e:
        print(f"❌ Modal A100 Status: Connection failed - {e}")
        return {"status": "error", "error": str(e)}

def test_mistral_text_generation():
    """Test Mistral 7B text generation"""
    print("\n🧠 Testing Mistral 7B Text Generation...")
    
    try:
        response = requests.post(
            "https://trade-digital--courseai-opensource-generate-text.modal.run",
            json={
                "prompt": "Create a brief course outline for 'Introduction to Python Programming' with 3 modules.",
                "max_length": 512,
                "temperature": 0.7,
                "use_quantization": True
            },
            timeout=120
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print("✅ Mistral 7B: Text generation successful")
                print(f"✅ Model: {data.get('model')}")
                print(f"✅ Quantized: {data.get('quantized')}")
                print(f"✅ Generated text preview: {data.get('generated_text', '')[:100]}...")
                return {"status": "success", "data": data}
            else:
                print(f"❌ Mistral 7B: Generation failed - {data.get('error')}")
                return {"status": "error", "error": data.get('error')}
        else:
            print(f"❌ Mistral 7B: HTTP {response.status_code}")
            return {"status": "error", "error": f"HTTP {response.status_code}"}
    except Exception as e:
        print(f"❌ Mistral 7B: Request failed - {e}")
        return {"status": "error", "error": str(e)}

def test_coqui_tts():
    """Test Coqui TTS speech generation"""
    print("\n🗣️ Testing Coqui TTS Speech Generation...")
    
    try:
        response = requests.post(
            "https://trade-digital--courseai-opensource-generate-speech.modal.run",
            json={
                "text": "Welcome to this AI-powered course. This is a test of our open-source voice generation.",
                "voice_name": "tts_models/en/ljspeech/tacotron2-DDC",
                "language": "en"
            },
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print("✅ Coqui TTS: Speech generation successful")
                print(f"✅ Engine: {data.get('engine')}")
                print(f"✅ Voice: {data.get('voice')}")
                print(f"✅ GPU Used: {data.get('gpu_used')}")
                return {"status": "success", "data": data}
            else:
                print(f"❌ Coqui TTS: Generation failed - {data.get('error')}")
                return {"status": "error", "error": data.get('error')}
        else:
            print(f"❌ Coqui TTS: HTTP {response.status_code}")
            return {"status": "error", "error": f"HTTP {response.status_code}"}
    except Exception as e:
        print(f"❌ Coqui TTS: Request failed - {e}")
        return {"status": "error", "error": str(e)}

def test_kokoro_tts_fallback():
    """Test Kokoro TTS fallback"""
    print("\n🔄 Testing Kokoro TTS Fallback...")
    
    try:
        response = requests.post(
            "https://trade-digital--courseai-opensource-generate-speech-fallback.modal.run",
            json={
                "text": "This is a test of the Kokoro TTS fallback system.",
                "voice": "af_sarah",
                "speed": 1.0
            },
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print("✅ Kokoro TTS: Fallback generation successful")
                print(f"✅ Engine: {data.get('engine')}")
                print(f"✅ Voice: {data.get('voice')}")
                return {"status": "success", "data": data}
            else:
                print(f"❌ Kokoro TTS: Fallback failed - {data.get('error')}")
                return {"status": "error", "error": data.get('error')}
        else:
            print(f"❌ Kokoro TTS: HTTP {response.status_code}")
            return {"status": "error", "error": f"HTTP {response.status_code}"}
    except Exception as e:
        print(f"❌ Kokoro TTS: Request failed - {e}")
        return {"status": "error", "error": str(e)}

def test_echomimic_avatar():
    """Test EchoMimic V2 avatar generation"""
    print("\n🎬 Testing EchoMimic V2 Avatar Generation...")
    
    try:
        response = requests.post(
            "https://trade-digital--courseai-opensource-generate-avatar.modal.run",
            json={
                "image_base64": create_test_image_base64(),
                "audio_base64": create_test_audio_base64(),
                "pose_style": 0,
                "expression_scale": 1.0,
                "output_format": "mp4"
            },
            timeout=300  # 5 minutes for video generation
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print("✅ EchoMimic V2: Avatar generation successful")
                print(f"✅ Duration: {data.get('duration_seconds')} seconds")
                print(f"✅ FPS: {data.get('fps')}")
                print(f"✅ Engine: {data.get('engine')}")
                return {"status": "success", "data": data}
            else:
                print(f"❌ EchoMimic V2: Generation failed - {data.get('error')}")
                return {"status": "error", "error": data.get('error')}
        else:
            print(f"❌ EchoMimic V2: HTTP {response.status_code}")
            return {"status": "error", "error": f"HTTP {response.status_code}"}
    except Exception as e:
        print(f"❌ EchoMimic V2: Request failed - {e}")
        return {"status": "error", "error": str(e)}

def test_marp_slides():
    """Test Marp slide generation"""
    print("\n📊 Testing Marp Slide Generation...")
    
    markdown_content = """# Open-Source AI Course Platform

## Overview
- Mistral 7B for text generation
- Coqui TTS for voice synthesis
- EchoMimic V2 for avatar videos
- Marp for slide generation

## Benefits
- Fully open-source
- Cost-effective
- High-quality output
- No vendor lock-in

## Next Steps
- Deploy to production
- Scale infrastructure
- Add more features"""
    
    try:
        response = requests.post(
            "https://trade-digital--courseai-opensource-generate-slides.modal.run",
            json={
                "markdown_content": markdown_content,
                "theme": "default",
                "output_format": "html"
            },
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print("✅ Marp CLI: Slide generation successful")
                print(f"✅ Format: {data.get('format')}")
                print(f"✅ Theme: {data.get('theme')}")
                print(f"✅ Engine: {data.get('engine')}")
                return {"status": "success", "data": data}
            else:
                print(f"❌ Marp CLI: Generation failed - {data.get('error')}")
                return {"status": "error", "error": data.get('error')}
        else:
            print(f"❌ Marp CLI: HTTP {response.status_code}")
            return {"status": "error", "error": f"HTTP {response.status_code}"}
    except Exception as e:
        print(f"❌ Marp CLI: Request failed - {e}")
        return {"status": "error", "error": str(e)}

def test_complete_workflow():
    """Test complete course creation workflow"""
    print("\n🚀 Testing Complete Open-Source Workflow...")
    
    # Step 1: Generate course content with Mistral
    print("Step 1: Generating course content...")
    content_result = test_mistral_text_generation()
    
    # Step 2: Generate voice with Coqui TTS
    print("Step 2: Generating voice narration...")
    voice_result = test_coqui_tts()
    
    # Step 3: Generate slides with Marp
    print("Step 3: Generating presentation slides...")
    slides_result = test_marp_slides()
    
    # Step 4: Generate avatar video (if previous steps successful)
    if all(r.get("status") == "success" for r in [content_result, voice_result]):
        print("Step 4: Generating avatar video...")
        avatar_result = test_echomimic_avatar()
    else:
        print("Step 4: Skipping avatar generation due to previous failures")
        avatar_result = {"status": "skipped"}
    
    # Summary
    results = {
        "content_generation": content_result.get("status"),
        "voice_synthesis": voice_result.get("status"),
        "slide_generation": slides_result.get("status"),
        "avatar_generation": avatar_result.get("status")
    }
    
    success_count = sum(1 for status in results.values() if status == "success")
    total_count = len([s for s in results.values() if s != "skipped"])
    
    print(f"\n📊 Workflow Results: {success_count}/{total_count} components working")
    
    if success_count == total_count:
        print("🎉 Complete open-source workflow is operational!")
    else:
        print("⚠️ Some components need attention")
    
    return results

def main():
    """Run complete open-source pipeline tests"""
    print("🚀 Open-Source AI Pipeline Test Suite")
    print("=" * 60)
    
    results = {}
    
    # Test individual components
    results["health"] = test_modal_health()
    results["mistral"] = test_mistral_text_generation()
    results["coqui_tts"] = test_coqui_tts()
    results["kokoro_tts"] = test_kokoro_tts_fallback()
    results["echomimic"] = test_echomimic_avatar()
    results["marp"] = test_marp_slides()
    
    # Test complete workflow
    print("\n" + "=" * 60)
    workflow_results = test_complete_workflow()
    
    # Generate summary report
    print("\n" + "=" * 60)
    print("📄 FINAL SUMMARY REPORT")
    print("=" * 60)
    
    working_services = sum(1 for r in results.values() if r.get("status") == "success")
    total_services = len(results)
    
    print(f"🔧 Individual Services: {working_services}/{total_services} operational")
    print(f"🚀 Complete Workflow: {workflow_results}")
    
    if working_services >= 4:  # At least core services working
        print("✅ PLATFORM STATUS: READY FOR PRODUCTION")
        print("🎯 RECOMMENDATION: Deploy open-source pipeline")
    else:
        print("⚠️ PLATFORM STATUS: NEEDS ATTENTION")
        print("🔧 RECOMMENDATION: Fix failing services before deployment")
    
    print("\n🎉 Open-source AI restructuring complete!")
    print("Platform now uses: Mistral + Coqui TTS + Kokoro + EchoMimic V2 + Marp")

if __name__ == "__main__":
    main()
