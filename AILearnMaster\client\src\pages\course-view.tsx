import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { Course, Lesson, Module } from '@shared/schema';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { CoursePlayer } from '@/components/course/CoursePlayer';
import CourseChatWidget from '@/components/chatbot/CourseChatWidget';
import { 
  Book, 
  CheckCircle, 
  FileText, 
  Layout, 
  Play, 
  ArrowLeft,
  ArrowRight,
  HelpCircle,
  MessageSquare,
  Users,
  Settings,
  Brain
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CourseViewProps {
  preview?: boolean;
}

export default function CourseView({ preview = false }: CourseViewProps) {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Extract course ID from URL - /courses/:id or /courses/:id/preview
  const path = window.location.pathname;
  let pathParts = path.split('/');
  // Check if the URL has a preview segment and handle accordingly
  const courseId = pathParts.includes('preview') 
    ? parseInt(pathParts[pathParts.indexOf('preview') - 1]) 
    : parseInt(pathParts.pop() || '0');
  
  // States for tracking current module and lesson
  const [activeModuleIndex, setActiveModuleIndex] = useState<number>(0);
  const [activeLessonIndex, setActiveLessonIndex] = useState<number>(0);
  const [selectedContentType, setSelectedContentType] = useState<string>('video');
  
  // Queries for course data
  const { 
    data: course, 
    isLoading: isLoadingCourse,
    error: courseError
  } = useQuery({
    queryKey: ['/api/courses', courseId],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/courses/${courseId}`);
      if (!res.ok) throw new Error('Failed to fetch course');
      return res.json();
    },
    enabled: !!courseId
  });
  
  // Queries for modules data
  const { 
    data: modules, 
    isLoading: isLoadingModules 
  } = useQuery({
    queryKey: ['/api/courses', courseId, 'modules'],
    queryFn: async () => {
      const res = await apiRequest('GET', `/api/courses/${courseId}/modules`);
      if (!res.ok) throw new Error('Failed to fetch modules');
      return res.json();
    },
    enabled: !!courseId
  });
  
  // Query for lessons of the active module
  const { 
    data: lessons, 
    isLoading: isLoadingLessons 
  } = useQuery({
    queryKey: ['/api/modules', modules?.[activeModuleIndex]?.id, 'lessons'],
    queryFn: async () => {
      if (!modules?.[activeModuleIndex]?.id) return [];
      const res = await apiRequest('GET', `/api/modules/${modules[activeModuleIndex].id}/lessons`);
      if (!res.ok) throw new Error('Failed to fetch lessons');
      return res.json();
    },
    enabled: !!modules?.[activeModuleIndex]?.id
  });
  
  // Current lesson based on active indices
  const currentLesson = lessons?.[activeLessonIndex];
  
  // Handle lesson completion
  const markLessonCompleted = useMutation({
    mutationFn: async (lessonId: number) => {
      const res = await apiRequest('POST', `/api/lessons/${lessonId}/complete`);
      if (!res.ok) throw new Error('Failed to mark lesson as completed');
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: 'Progress Saved',
        description: 'Lesson marked as completed.'
      });
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/user/progress'] });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update progress. Please try again.',
        variant: 'destructive'
      });
    }
  });
  
  // Navigation functions
  const goToNextLesson = () => {
    if (!lessons) return;
    
    if (activeLessonIndex < lessons.length - 1) {
      // Next lesson in the same module
      setActiveLessonIndex(activeLessonIndex + 1);
    } else if (modules && activeModuleIndex < modules.length - 1) {
      // First lesson in the next module
      setActiveModuleIndex(activeModuleIndex + 1);
      setActiveLessonIndex(0);
    }
  };
  
  const goToPreviousLesson = () => {
    if (!lessons) return;
    
    if (activeLessonIndex > 0) {
      // Previous lesson in the same module
      setActiveLessonIndex(activeLessonIndex - 1);
    } else if (modules && activeModuleIndex > 0) {
      // Last lesson in the previous module
      setActiveModuleIndex(activeModuleIndex - 1);
      // Need to fetch lessons for that module first
      apiRequest('GET', `/api/modules/${modules[activeModuleIndex - 1].id}/lessons`)
        .then(res => res.json())
        .then(prevModuleLessons => {
          setActiveLessonIndex(prevModuleLessons.length - 1);
        });
    }
  };
  
  // Calculate if there are next/previous lessons
  const hasNextLesson = 
    lessons && 
    ((activeLessonIndex < lessons.length - 1) || 
     (modules && activeModuleIndex < modules.length - 1));
  
  const hasPreviousLesson = 
    (activeLessonIndex > 0) || 
    (activeModuleIndex > 0);
  
  // Go back to courses page
  const goBack = () => {
    if (preview) {
      setLocation('/dashboard'); // For preview mode, go back to dashboard
    } else {
      setLocation('/my-courses'); // For normal viewing, go to my courses
    }
  };
  
  // Loading state
  if (isLoadingCourse || isLoadingModules) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center space-x-4 mb-8">
          <Button variant="ghost" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Courses
          </Button>
          <Skeleton className="h-10 w-1/3" />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-3">
            <Skeleton className="w-full aspect-video rounded-lg" />
            <Skeleton className="h-12 w-full mt-4" />
            <Skeleton className="h-40 w-full mt-4" />
          </div>
          
          <div>
            <Skeleton className="h-12 w-full mb-4" />
            <div className="space-y-2">
              {Array(5).fill(0).map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Error state
  if (courseError || !course) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex flex-col items-center justify-center text-center p-8">
          <div className="bg-red-100 text-red-800 p-6 rounded-lg mb-4">
            <h2 className="text-2xl font-bold mb-2">Error Loading Course</h2>
            <p>We couldn't load the course details. Please try again later.</p>
          </div>
          <Button onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to My Courses
          </Button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-8">
      {/* Top Navigation */}
      <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={goBack} className="p-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">{course.title}</h1>
              {preview && (
                <span className="bg-amber-100 text-amber-800 text-xs px-2 py-1 rounded-full">
                  Preview Mode
                </span>
              )}
            </div>
            <p className="text-muted-foreground">{course.category}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 mt-4 md:mt-0">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setSelectedContentType("discussion")}
          >
            <Users className="h-4 w-4 mr-2" />
            Discussion
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setLocation('/help')}
          >
            <HelpCircle className="h-4 w-4 mr-2" />
            Help
          </Button>
        </div>
      </div>
      
      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Video and Content Area */}
        <div className="lg:col-span-3 space-y-6">
          {/* Content Tabs */}
          <Tabs defaultValue="video" value={selectedContentType} onValueChange={setSelectedContentType}>
            <TabsList className="mb-4">
              <TabsTrigger value="video">
                <Play className="h-4 w-4 mr-2" />
                Video
              </TabsTrigger>
              <TabsTrigger value="transcript">
                <FileText className="h-4 w-4 mr-2" />
                Transcript
              </TabsTrigger>
              <TabsTrigger value="resources">
                <Book className="h-4 w-4 mr-2" />
                Resources
              </TabsTrigger>
              <TabsTrigger value="discussion">
                <MessageSquare className="h-4 w-4 mr-2" />
                Discussion
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="video" className="mt-0">
              {currentLesson ? (
                <CoursePlayer 
                  lesson={currentLesson}
                  onNextLesson={goToNextLesson}
                  onPreviousLesson={goToPreviousLesson}
                  hasNextLesson={hasNextLesson}
                  hasPreviousLesson={hasPreviousLesson}
                />
              ) : (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center h-64">
                    <p className="text-muted-foreground">No lesson content available.</p>
                  </CardContent>
                </Card>
              )}
              
              {/* Lesson Navigation */}
              <div className="flex justify-between mt-6">
                <Button 
                  variant="outline" 
                  onClick={goToPreviousLesson}
                  disabled={!hasPreviousLesson}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous Lesson
                </Button>
                
                {currentLesson && !preview && (
                  <Button onClick={() => markLessonCompleted.mutate(currentLesson.id)}>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Mark Completed
                  </Button>
                )}
                {currentLesson && preview && (
                  <Button disabled>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Mark Completed (Preview Mode)
                  </Button>
                )}
                
                <Button 
                  variant="outline" 
                  onClick={goToNextLesson}
                  disabled={!hasNextLesson}
                >
                  Next Lesson
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="transcript">
              <Card>
                <CardHeader>
                  <CardTitle>Lesson Transcript</CardTitle>
                  <CardDescription>
                    Complete written transcript of this lesson
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {currentLesson?.script ? (
                    <div className="prose prose-sm max-w-none">
                      <div dangerouslySetInnerHTML={{ __html: currentLesson.script }} />
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No transcript available for this lesson.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="resources">
              <Card>
                <CardHeader>
                  <CardTitle>Lesson Resources</CardTitle>
                  <CardDescription>
                    Additional materials for this lesson
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">No resources available for this lesson.</p>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="discussion">
              <Card>
                <CardHeader>
                  <CardTitle>Lesson Discussion</CardTitle>
                  <CardDescription>
                    Questions and comments about this lesson
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">Discussion feature coming soon.</p>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        
        {/* Course Outline / Sidebar */}
        <div>
          <Card className="mb-4">
            <CardHeader className="pb-3">
              <CardTitle>Course Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Progress value={course.completion || 0} className="h-2" />
                <p className="text-sm text-muted-foreground">
                  {course.completion || 0}% Complete
                </p>
              </div>
            </CardContent>
          </Card>
          
          <Card className="mb-4">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center">
                <Brain className="h-5 w-5 text-primary mr-2" />
                Learning Assistant
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Have questions about this course? Get instant help from our AI assistant.
              </p>
              <CourseChatWidget 
                courseId={courseId}
                lessonId={currentLesson?.id}
                title={`Chat about ${currentLesson ? currentLesson.title : course.title}`}
              />
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Course Content</CardTitle>
              <CardDescription>
                {modules?.length || 0} modules • {course.lessonsCount || 0} lessons
              </CardDescription>
            </CardHeader>
            <CardContent className="px-0 pb-1">
              <Accordion 
                type="multiple" 
                defaultValue={[`module-${activeModuleIndex}`]}
                className="w-full"
              >
                {modules?.map((module, moduleIndex) => (
                  <AccordionItem 
                    key={module.id} 
                    value={`module-${moduleIndex}`}
                    className={cn(
                      "px-6 py-0",
                      moduleIndex === activeModuleIndex && "bg-secondary/30"
                    )}
                  >
                    <AccordionTrigger className="py-3 hover:no-underline">
                      <div className="flex flex-col items-start text-left">
                        <span className="text-sm font-medium">Module {moduleIndex + 1}</span>
                        <span className="text-xs text-muted-foreground">{module.title}</span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="pb-3 pt-0">
                      {isLoadingLessons && moduleIndex === activeModuleIndex ? (
                        <div className="space-y-2 pl-4">
                          {Array(3).fill(0).map((_, i) => (
                            <Skeleton key={i} className="h-8 w-full" />
                          ))}
                        </div>
                      ) : (
                        <div className="space-y-1">
                          {moduleIndex === activeModuleIndex && lessons?.map((lesson, lessonIndex) => (
                            <Button
                              key={lesson.id}
                              variant="ghost"
                              size="sm"
                              className={cn(
                                "w-full justify-start h-auto py-2 px-4 font-normal text-left",
                                lessonIndex === activeLessonIndex && "bg-secondary/50 font-medium"
                              )}
                              onClick={() => setActiveLessonIndex(lessonIndex)}
                            >
                              <div className="flex items-center w-full">
                                <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full bg-muted mr-2">
                                  <span className="text-xs">{lessonIndex + 1}</span>
                                </div>
                                <span className="text-sm truncate">{lesson.title}</span>
                              </div>
                            </Button>
                          ))}
                        </div>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}