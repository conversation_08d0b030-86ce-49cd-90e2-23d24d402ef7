import { ReactNode } from 'react';
import { Redirect } from 'wouter';
import { useAuth } from '@/hooks/use-auth';
import { Loader2 } from 'lucide-react';

interface AuthenticatedRouteProps {
  children: ReactNode;
}

/**
 * AuthenticatedRoute redirects logged-in users away from public routes like the landing page
 * to their dashboard, preventing them from seeing sign-in prompts when already authenticated
 */
export function AuthenticatedRoute({ children }: AuthenticatedRouteProps) {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // If user is logged in, redirect to appropriate page based on plan
  if (user) {
    // If user has no plan selected, redirect to pricing page
    if (!user.plan || user.plan === 'none' || user.plan === 'free') {
      return <Redirect to="/pricing" />;
    } else {
      // Otherwise redirect to dashboard
      return <Redirect to="/dashboard" />;
    }
  }

  // Otherwise, show the children (public page)
  return <>{children}</>;
}