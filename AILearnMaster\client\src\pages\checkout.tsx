import { loadStripe } from '@stripe/stripe-js';
import { useEffect, useState } from 'react';
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from 'wouter';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { useUser } from '@/hooks/use-user';

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
const stripePublicKey = import.meta.env.VITE_STRIPE_PUBLIC_KEY;
if (!stripePublicKey) {
  console.warn('Stripe public key is missing. Payment functionality will not work properly.');
}
const stripePromise = stripePublicKey ? loadStripe(stripePublicKey) : null;

export default function Checkout() {
  const [location, navigate] = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const { user, isLoading: isUserLoading } = useUser();
  
  // Extract plan information from the URL query params
  const searchParams = new URLSearchParams(location.split('?')[1]);
  const planId = searchParams.get('plan') || 'starter';
  const billingInterval = searchParams.get('interval') || 'monthly';
  
  useEffect(() => {
    // Check if user is authenticated
    if (!isUserLoading && !user) {
      toast({
        title: "Authentication Required",
        description: "Please log in before continuing to checkout",
        variant: "destructive",
      });
      navigate('/auth?redirect=' + encodeURIComponent(location));
    }
  }, [user, isUserLoading, navigate, toast, location]);
  
  // Plan display information
  const planInfo = {
    starter: { name: "Starter", monthly: 49, yearly: 470 },
    pro: { name: "Pro", monthly: 129, yearly: 1238 },
    business: { name: "Business", monthly: 299, yearly: 2870 }
  };
  
  const plan = planInfo[planId as keyof typeof planInfo] || planInfo.starter;
  const amount = billingInterval === 'yearly' ? plan.yearly : plan.monthly;
  const intervalName = billingInterval === 'yearly' ? 'year' : 'month';

  useEffect(() => {
    // Create a direct checkout session with Stripe
    const createCheckoutSession = async () => {
      // Only proceed if user is authenticated and Stripe is properly configured
      if (isUserLoading) {
        return; // Wait until user data is loaded
      }
      
      if (!user) {
        // Don't try to create a checkout session if not logged in
        // We already handle redirect in the other useEffect
        setIsLoading(false);
        return;
      }
      
      if (!stripePromise) {
        console.log("Stripe not configured properly - missing public key");
        setError('Stripe is not properly configured. Please try again later.');
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      try {
        console.log("Creating checkout session with plan:", planId, "interval:", billingInterval);
        
        // Create a checkout session on the server
        const response = await apiRequest("POST", "/api/payments/create-checkout-session", { 
          planId, 
          billingInterval
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          console.error("Server error response:", errorData);
          throw new Error(errorData.message || 'Failed to create checkout session');
        }
        
        const data = await response.json();
        console.log("Checkout session created:", data);
        
        if (!data.sessionId) {
          throw new Error('No session ID returned from server');
        }
        
        // Check if this is a mock session (for development)
        if (data.sessionId.startsWith('mock_session_')) {
          console.log("Using mock session - redirecting directly to:", data.url);
          // For mock sessions, redirect directly to the success URL
          window.location.href = data.url;
          return;
        }
        
        // Real Stripe checkout flow
        const stripe = await stripePromise;
        if (!stripe) {
          throw new Error('Stripe not initialized properly');
        }
        
        console.log("Redirecting to checkout with session ID:", data.sessionId);
        
        const { error } = await stripe.redirectToCheckout({
          sessionId: data.sessionId
        });
        
        if (error) {
          console.error("Stripe redirect error:", error);
          throw new Error(error.message || 'Failed to redirect to Stripe checkout');
        }
      } catch (err: any) {
        console.error('Error creating checkout session:', err);
        setError(err.message || 'An unexpected error occurred');
        toast({
          title: "Checkout Error",
          description: err.message || 'Failed to initialize checkout',
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    // Only run this effect if user is authenticated (after user data is loaded)
    if (!isUserLoading && user) {
      createCheckoutSession();
    }
  }, [planId, billingInterval, toast, stripePromise, user, isUserLoading]);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" aria-label="Loading"/>
          <p className="text-lg font-medium">Preparing your checkout...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">Checkout Error</CardTitle>
            <CardDescription>We encountered a problem setting up your checkout</CardDescription>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => window.history.back()} variant="outline" className="w-full">
              Return to Pricing
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container max-w-5xl py-10">
      <div className="grid md:grid-cols-2 gap-8">
        {/* Order Summary */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
              <CardDescription>Review your subscription details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">{plan.name} Plan</h3>
                  <p className="text-sm text-muted-foreground">Billed {billingInterval}</p>
                </div>
                <span className="font-medium">
                  ${amount}/{intervalName.substring(0, 2)}
                </span>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>${amount}/{intervalName.substring(0, 2)}</span>
                </div>
              </div>
              
              <div className="rounded-lg bg-muted p-4 text-sm">
                <p className="font-medium mb-1">Subscription includes:</p>
                {planId === 'starter' && (
                  <ul className="list-disc list-inside space-y-1">
                    <li>Up to 5 courses</li>
                    <li>Basic AI content generation</li>
                    <li>10 languages for auto-narration</li>
                    <li>Standard video creation</li>
                    <li>Email support</li>
                  </ul>
                )}
                {planId === 'pro' && (
                  <ul className="list-disc list-inside space-y-1">
                    <li>Unlimited courses</li>
                    <li>Advanced AI content generation</li>
                    <li>25 languages for auto-narration</li>
                    <li>HD video creation with animations</li>
                    <li>Priority email & chat support</li>
                    <li>Team collaboration (up to 3 members)</li>
                  </ul>
                )}
                {planId === 'business' && (
                  <ul className="list-disc list-inside space-y-1">
                    <li>Everything in Pro plan</li>
                    <li>Team collaboration (up to 10 members)</li>
                    <li>White-label options</li>
                    <li>Advanced analytics</li>
                    <li>API access</li>
                    <li>Dedicated account manager</li>
                  </ul>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Payment Information */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Payment</CardTitle>
              <CardDescription>You will be redirected to Stripe's secure checkout</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <Loader2 className="h-5 w-5 animate-spin text-primary" />
                <span>Preparing secure checkout...</span>
              </div>
              
              <div className="rounded-lg bg-muted p-4 text-sm">
                <p className="font-medium">Secure Payment:</p>
                <p className="text-muted-foreground">
                  You'll be redirected to Stripe's secure payment platform to complete your purchase.
                  Your payment information is protected by industry-standard encryption.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}