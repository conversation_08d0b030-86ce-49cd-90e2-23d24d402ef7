# AILearnMaster Deployment Guide

## Overview

This guide covers deploying AILearnMaster with the enhanced PostgreSQL database setup, including local development, staging, and production environments.

## Prerequisites

### Required Services
- **PostgreSQL Database**: Neon Database (recommended) or self-hosted PostgreSQL
- **Node.js**: Version 18+ with npm/yarn
- **Modal A100 GPU**: For open-source AI services (Mistral, Coqui TTS, etc.)
- **AWS S3**: For file storage and media hosting
- **Stripe**: For payment processing (optional)

### Environment Variables
Copy `.env.example` to `.env` and configure all required variables:

```bash
# Core Configuration
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require
NODE_ENV=production
SESSION_SECRET=your-secure-session-secret

# AI Services
MODAL_TOKEN_ID=your-modal-token-id
MODAL_TOKEN_SECRET=your-modal-token-secret
OPENAI_API_KEY=your-openai-fallback-key

# Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name
```

## Local Development Setup

### 1. Clone and Install
```bash
git clone https://github.com/your-username/AILearnMaster.git
cd AILearnMaster
npm install
```

### 2. Database Setup
```bash
# Set up database (migrations + seeding)
npm run db:setup

# Verify database health
npm run db:health

# Run comprehensive functionality tests
npm run db:test-functionality
```

### 3. Start Development Server
```bash
# Start both frontend and backend
npm run dev

# Backend runs on http://localhost:3001
# Frontend runs on http://localhost:3000
```

### 4. Verify Installation
```bash
# Check database status
curl http://localhost:3001/api/database/health

# Test AI services
curl http://localhost:3001/api/ai/health

# Verify TTS services
curl http://localhost:3001/api/tts/models
```

## Production Deployment

### Option 1: Replit Deployment (Recommended)

#### 1. Replit Setup
```bash
# Fork the repository on Replit
# Configure environment variables in Replit Secrets

# Required Secrets:
DATABASE_URL=your-neon-database-url
MODAL_TOKEN_ID=your-modal-token
MODAL_TOKEN_SECRET=your-modal-secret
SESSION_SECRET=your-session-secret
AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_S3_BUCKET=your-bucket-name
```

#### 2. Deploy to Cloud Run
```bash
# Replit automatically deploys to Google Cloud Run
# Configure custom domain in Replit deployment settings
```

### Option 2: Docker Deployment

#### 1. Create Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3001
CMD ["npm", "start"]
```

#### 2. Build and Deploy
```bash
# Build Docker image
docker build -t ailearn-master .

# Run container
docker run -p 3001:3001 --env-file .env ailearn-master
```

### Option 3: VPS/Server Deployment

#### 1. Server Setup
```bash
# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
npm install -g pm2
```

#### 2. Application Setup
```bash
# Clone repository
git clone https://github.com/your-username/AILearnMaster.git
cd AILearnMaster

# Install dependencies
npm ci --only=production

# Build application
npm run build

# Set up database
npm run db:setup

# Start with PM2
pm2 start ecosystem.config.js
```

## Database Migration (Production)

### Pre-Migration Checklist
- [ ] Backup existing database
- [ ] Test migration on staging environment
- [ ] Verify all environment variables
- [ ] Schedule maintenance window
- [ ] Notify users of downtime

### Migration Steps
```bash
# 1. Backup current database
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. Run database setup
npm run db:setup

# 3. Verify migration
npm run db:test-functionality

# 4. Test application functionality
npm run test

# 5. Monitor for issues
npm run db:diagnostics
```

### Rollback Plan
```bash
# If migration fails, restore from backup
psql $DATABASE_URL < backup_YYYYMMDD_HHMMSS.sql

# Restart application
pm2 restart all
```

## Monitoring and Maintenance

### Health Monitoring
```bash
# Database health check
curl https://your-domain.com/api/database/health

# Application health
curl https://your-domain.com/api/health

# AI services status
curl https://your-domain.com/api/ai/health
```

### Performance Monitoring
```bash
# Database performance metrics
curl https://your-domain.com/api/database/metrics

# Query performance analysis
npm run db:diagnostics

# Connection pool status
npm run db:health
```

### Backup Strategy
```bash
# Daily automated backups
0 2 * * * pg_dump $DATABASE_URL > /backups/daily_$(date +\%Y\%m\%d).sql

# Weekly full backups
0 2 * * 0 pg_dump $DATABASE_URL > /backups/weekly_$(date +\%Y\%m\%d).sql

# Cleanup old backups (keep 30 days)
find /backups -name "daily_*.sql" -mtime +30 -delete
```

## Security Configuration

### Database Security
- Use SSL/TLS for all database connections
- Implement connection pooling limits
- Regular security updates for PostgreSQL
- Monitor for suspicious query patterns

### Application Security
- Secure session management with PostgreSQL store
- Input validation with Zod schemas
- Rate limiting on API endpoints
- CORS configuration for production domains

### Environment Security
- Store secrets in secure environment variables
- Use different databases for dev/staging/production
- Regular rotation of API keys and secrets
- Monitor access logs and unusual activity

## Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check environment variables
echo $DATABASE_URL

# Test connection manually
psql $DATABASE_URL -c "SELECT 1;"

# Check application logs
pm2 logs ailearn-master
```

#### Performance Issues
```bash
# Check database performance
npm run db:metrics

# Monitor connection pool
npm run db:diagnostics

# Analyze slow queries
npm run db:slow-queries
```

#### Migration Issues
```bash
# Check migration status
npm run db:status

# Force migration (development only)
npm run db:migrate --force

# Reset database (development only)
npm run db:reset
```

### Support Resources
- **Documentation**: `/docs/database-setup.md`
- **Health Endpoints**: `/api/database/*`
- **Logs**: PM2 logs or container logs
- **Community**: GitHub Issues and Discussions

## Performance Optimization

### Database Optimization
- Connection pooling with optimal settings
- Query optimization with proper indexes
- Regular VACUUM and ANALYZE operations
- Monitor and optimize slow queries

### Application Optimization
- Caching frequently accessed data
- Optimized API response times
- Efficient file upload handling
- CDN for static assets

### Infrastructure Optimization
- Use appropriate server sizing
- Configure load balancing if needed
- Monitor resource usage
- Scale based on demand

## Scaling Considerations

### Horizontal Scaling
- Multiple application instances behind load balancer
- Database read replicas for read-heavy workloads
- CDN for global content distribution
- Microservices architecture for specific components

### Vertical Scaling
- Increase server resources as needed
- Database connection pool tuning
- Memory optimization for Node.js
- CPU optimization for AI workloads

### Cost Optimization
- Use serverless PostgreSQL (Neon) for automatic scaling
- Optimize Modal A100 GPU usage with warm containers
- S3 storage class optimization
- Monitor and optimize resource usage
