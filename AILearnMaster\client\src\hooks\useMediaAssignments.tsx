import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

interface MediaItem {
  id: string;
  name: string;
  title?: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnailUrl?: string;
  size: number;
  uploadedAt: string;
  source: 'upload' | 'pexels' | 'pixabay';
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    author?: string;
  };
}

interface MediaAssignments {
  modules: Record<string, MediaItem>;
  lessons: Record<string, MediaItem>;
}

const STORAGE_KEY = 'course_media_assignments';
const AUTO_SAVE_INTERVAL = 2000; // 2 seconds

export const useMediaAssignments = (courseId?: string) => {
  const [assignments, setAssignments] = useState<MediaAssignments>({
    modules: {},
    lessons: {}
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Generate storage key with course ID for isolation
  const getStorageKey = useCallback(() => {
    return courseId ? `${STORAGE_KEY}_${courseId}` : STORAGE_KEY;
  }, [courseId]);

  // Load assignments from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(getStorageKey());
      if (stored) {
        const parsed = JSON.parse(stored);
        setAssignments(parsed);
      }
    } catch (error) {
      console.error('Failed to load media assignments:', error);
    } finally {
      setIsLoading(false);
    }
  }, [getStorageKey]);

  // Auto-save to localStorage with debouncing
  useEffect(() => {
    if (isLoading) return;

    const timeoutId = setTimeout(() => {
      try {
        localStorage.setItem(getStorageKey(), JSON.stringify(assignments));
      } catch (error) {
        console.error('Failed to save media assignments:', error);
      }
    }, AUTO_SAVE_INTERVAL);

    return () => clearTimeout(timeoutId);
  }, [assignments, getStorageKey, isLoading]);

  // Assign media to module
  const assignMediaToModule = useCallback((moduleId: string, media: MediaItem) => {
    setAssignments(prev => ({
      ...prev,
      modules: {
        ...prev.modules,
        [moduleId]: media
      }
    }));
  }, []);

  // Assign media to lesson
  const assignMediaToLesson = useCallback((moduleId: string, lessonId: string, media: MediaItem) => {
    const key = `${moduleId}-${lessonId}`;
    setAssignments(prev => ({
      ...prev,
      lessons: {
        ...prev.lessons,
        [key]: media
      }
    }));
  }, []);

  // Remove media from module
  const removeMediaFromModule = useCallback((moduleId: string) => {
    setAssignments(prev => {
      const { [moduleId]: removed, ...rest } = prev.modules;
      return {
        ...prev,
        modules: rest
      };
    });
  }, []);

  // Remove media from lesson
  const removeMediaFromLesson = useCallback((moduleId: string, lessonId: string) => {
    const key = `${moduleId}-${lessonId}`;
    setAssignments(prev => {
      const { [key]: removed, ...rest } = prev.lessons;
      return {
        ...prev,
        lessons: rest
      };
    });
  }, []);

  // Get assigned media for module
  const getModuleMedia = useCallback((moduleId: string): MediaItem | undefined => {
    return assignments.modules[moduleId];
  }, [assignments.modules]);

  // Get assigned media for lesson
  const getLessonMedia = useCallback((moduleId: string, lessonId: string): MediaItem | undefined => {
    const key = `${moduleId}-${lessonId}`;
    return assignments.lessons[key];
  }, [assignments.lessons]);

  // Clear all assignments
  const clearAllAssignments = useCallback(() => {
    setAssignments({
      modules: {},
      lessons: {}
    });
    try {
      localStorage.removeItem(getStorageKey());
      toast({
        title: "Assignments Cleared",
        description: "All media assignments have been cleared."
      });
    } catch (error) {
      console.error('Failed to clear assignments:', error);
    }
  }, [getStorageKey, toast]);

  // Export assignments for backup
  const exportAssignments = useCallback(() => {
    return {
      courseId,
      timestamp: new Date().toISOString(),
      assignments
    };
  }, [courseId, assignments]);

  // Import assignments from backup
  const importAssignments = useCallback((data: any) => {
    try {
      if (data.assignments) {
        setAssignments(data.assignments);
        toast({
          title: "Assignments Imported",
          description: "Media assignments have been restored."
        });
      }
    } catch (error) {
      console.error('Failed to import assignments:', error);
      toast({
        title: "Import Failed",
        description: "Failed to import media assignments.",
        variant: "destructive"
      });
    }
  }, [toast]);

  // Get assignment statistics
  const getStats = useCallback(() => {
    const moduleCount = Object.keys(assignments.modules).length;
    const lessonCount = Object.keys(assignments.lessons).length;
    return {
      totalAssignments: moduleCount + lessonCount,
      moduleAssignments: moduleCount,
      lessonAssignments: lessonCount
    };
  }, [assignments]);

  // Force save to localStorage
  const forceSave = useCallback(async () => {
    setIsSaving(true);
    try {
      localStorage.setItem(getStorageKey(), JSON.stringify(assignments));
      toast({
        title: "Assignments Saved",
        description: "All media assignments have been saved."
      });
    } catch (error) {
      console.error('Failed to save assignments:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save media assignments.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  }, [assignments, getStorageKey, toast]);

  return {
    // State
    assignments,
    isLoading,
    isSaving,
    
    // Actions
    assignMediaToModule,
    assignMediaToLesson,
    removeMediaFromModule,
    removeMediaFromLesson,
    
    // Getters
    getModuleMedia,
    getLessonMedia,
    getStats,
    
    // Utilities
    clearAllAssignments,
    exportAssignments,
    importAssignments,
    forceSave
  };
};