import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Loader2, PlayCircle, PauseCircle, Download } from 'lucide-react';
import { Slider } from '@/components/ui/slider';

interface TextToSpeechToolProps {
  onAudioGenerated?: (url: string) => void;
  defaultText?: string;
  defaultModel?: 'kokoro' | 'coqui';
  heading?: string;
  description?: string;
}

interface Voice {
  id: string;
  name: string;
  preview_url?: string;
}

export function TextToSpeechTool({
  onAudioGenerated,
  defaultText = '',
  defaultModel = 'kokoro',
  heading = 'Text-to-Speech',
  description = 'Generate speech using Kokoro or Coqui TTS open-source models running on RunPod H100.'
}: TextToSpeechToolProps) {
  const { toast } = useToast();
  const [text, setText] = useState(defaultText);
  const [model, setModel] = useState<'kokoro' | 'coqui'>(defaultModel);
  const [voice, setVoice] = useState<string>('');
  const [speed, setSpeed] = useState(1.0);
  const [generating, setGenerating] = useState(false);
  const [voices, setVoices] = useState<Voice[]>([]);
  const [generatedAudio, setGeneratedAudio] = useState<{ url: string, duration: number } | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  // Load available voices when component mounts
  useEffect(() => {
    const fetchVoices = async () => {
      try {
        // For now, we're using a static list of voices
        // In a real implementation, this would fetch from an API
        setVoices([
          { id: 'male_1', name: 'Male Voice 1' },
          { id: 'female_1', name: 'Female Voice 1' },
          { id: 'male_2', name: 'Male Voice 2' },
          { id: 'female_2', name: 'Female Voice 2' },
        ]);
        setVoice('male_1'); // Default voice
      } catch (error) {
        console.error('Failed to fetch voices:', error);
      }
    };
    
    fetchVoices();
  }, []);
  
  // Handle audio playback
  useEffect(() => {
    if (audioRef.current) {
      const audioElement = audioRef.current;
      
      const handleEnded = () => {
        setIsPlaying(false);
      };
      
      audioElement.addEventListener('ended', handleEnded);
      
      return () => {
        audioElement.removeEventListener('ended', handleEnded);
      };
    }
  }, [generatedAudio]);
  
  const handleGenerate = async () => {
    if (!text.trim()) {
      toast({
        title: 'Text is required',
        description: 'Please enter some text to convert to speech',
        variant: 'destructive',
      });
      return;
    }
    
    if (!voice) {
      toast({
        title: 'Voice is required',
        description: 'Please select a voice',
        variant: 'destructive',
      });
      return;
    }
    
    setGenerating(true);
    setGeneratedAudio(null);
    
    try {
      const response = await apiRequest(
        'POST', 
        '/api/ai-tools/text-to-speech', 
        {
          text,
          voice,
          model,
          speed
        }
      );
      
      const data = await response.json();
      
      if (data.url) {
        setGeneratedAudio({
          url: data.url,
          duration: data.duration || 0
        });
        
        if (onAudioGenerated) {
          onAudioGenerated(data.url);
        }
      } else {
        toast({
          title: 'Generation failed',
          description: data.message || 'Failed to generate speech',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Speech generation error:', error);
      toast({
        title: 'Error',
        description: 'Failed to connect to the speech generation service',
        variant: 'destructive',
      });
    } finally {
      setGenerating(false);
    }
  };
  
  const togglePlayPause = () => {
    if (!audioRef.current) return;
    
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      void audioRef.current.play();
    }
    
    setIsPlaying(!isPlaying);
  };
  
  // Format seconds to mm:ss
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{heading}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Textarea
            placeholder="Enter your text to convert to speech..."
            className="min-h-[100px]"
            value={text}
            onChange={(e) => setText(e.target.value)}
            disabled={generating}
          />
          <div className="text-xs text-muted-foreground mt-1">
            Word count: {text.trim().split(/\s+/).filter(Boolean).length}
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm font-medium">Model</div>
            <Select 
              value={model} 
              onValueChange={(value: 'kokoro' | 'coqui') => setModel(value)}
              disabled={generating}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="kokoro">Kokoro TTS (Primary)</SelectItem>
                <SelectItem value="coqui">Coqui TTS (Alternative)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm font-medium">Voice</div>
            <Select 
              value={voice} 
              onValueChange={setVoice}
              disabled={generating || voices.length === 0}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select voice" />
              </SelectTrigger>
              <SelectContent>
                {voices.map((v) => (
                  <SelectItem key={v.id} value={v.id}>
                    {v.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm font-medium">Speed: {speed.toFixed(1)}x</div>
            <Slider
              defaultValue={[speed]}
              min={0.5}
              max={2.0}
              step={0.1}
              onValueChange={(value) => setSpeed(value[0])}
              disabled={generating}
            />
            <div className="text-xs text-muted-foreground">
              Adjust the speech speed
            </div>
          </div>
        </div>
        
        <Button
          className="w-full"
          disabled={generating || !text.trim() || !voice}
          onClick={handleGenerate}
        >
          {generating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            'Generate Speech'
          )}
        </Button>
        
        {generatedAudio && (
          <div className="pt-4">
            <div className="text-sm font-medium mb-2">Generated Audio</div>
            <div className="p-4 bg-secondary rounded-md">
              <div className="flex items-center justify-between">
                <Button variant="ghost" size="icon" onClick={togglePlayPause}>
                  {isPlaying ? (
                    <PauseCircle className="h-8 w-8" />
                  ) : (
                    <PlayCircle className="h-8 w-8" />
                  )}
                </Button>
                <div className="text-sm">
                  Duration: {formatTime(generatedAudio.duration)}
                </div>
                <a 
                  href={generatedAudio.url} 
                  download="generated-speech.mp3"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Button variant="ghost" size="icon">
                    <Download className="h-5 w-5" />
                  </Button>
                </a>
              </div>
              <audio 
                ref={audioRef} 
                src={generatedAudio.url} 
                className="hidden" 
                preload="metadata" 
              />
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {generatedAudio && (
          <div className="text-xs text-muted-foreground">
            Model: {model}, Voice: {voices.find(v => v.id === voice)?.name || voice}
          </div>
        )}
      </CardFooter>
    </Card>
  );
}

export default TextToSpeechTool;