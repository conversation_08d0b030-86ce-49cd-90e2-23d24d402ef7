import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'wouter';
import { 
  Globe, 
  Settings, 
  CheckCircle, 
  XCircle, 
  Plus, 
  ExternalLink,
  Zap,
  Shield,
  Loader2,
  AlertTriangle,
  Sparkles,
  Link2,
  RefreshCw,
  Eye,
  Users,
  TrendingUp,
  DollarSign,
  BarChart3,
  Calendar,
  Info,
  ArrowRight,
  Store,
  VideoIcon,
  Mail,
  Share2,
  BookOpen,
  Wrench,
  Power,
  Activity
} from 'lucide-react';

interface PlatformIntegration {
  id: number;
  platform: string;
  status: 'connected' | 'disconnected' | 'error' | 'pending' | 'premium';
  apiKey?: string;
  webhookUrl?: string;
  lastSync?: string;
  features: string[];
  description: string;
  logo: string;
  category: 'social' | 'marketplace' | 'video' | 'analytics' | 'email' | 'learning';
  popularity?: number;
  pricing?: string;
  setupDifficulty?: 'easy' | 'medium' | 'advanced';
  estimatedSetupTime?: string;
  monthlyUsage?: number;
  lastActivity?: string;
  errorMessage?: string;
}

interface ConnectionFormData {
  apiKey: string;
  webhookUrl?: string;
  customSettings?: Record<string, any>;
}

export default function PlatformIntegrations() {
  const [selectedPlatform, setSelectedPlatform] = useState<PlatformIntegration | null>(null);
  const [connectionForm, setConnectionForm] = useState<ConnectionFormData>({
    apiKey: '',
    webhookUrl: '',
    customSettings: {}
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showConnectionDialog, setShowConnectionDialog] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch platform integrations
  const { data: integrations = [], isLoading } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: () => apiRequest('/api/integrations')
  });

  // Connect platform mutation
  const connectPlatformMutation = useMutation({
    mutationFn: async (data: { platformId: number; credentials: ConnectionFormData }) => {
      return apiRequest(`/api/integrations/${data.platformId}/connect`, {
        method: 'POST',
        body: JSON.stringify(data.credentials)
      });
    },
    onSuccess: () => {
      toast({
        title: "Platform Connected",
        description: "Successfully connected to the platform",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      setSelectedPlatform(null);
      setConnectionForm({ apiKey: '', webhookUrl: '', customSettings: {} });
    },
    onError: (error: any) => {
      toast({
        title: "Connection Failed",
        description: error.message || "Failed to connect to platform",
        variant: "destructive",
      });
    }
  });

  // Disconnect platform mutation
  const disconnectPlatformMutation = useMutation({
    mutationFn: async (platformId: number) => {
      return apiRequest(`/api/integrations/${platformId}/disconnect`, {
        method: 'POST'
      });
    },
    onSuccess: () => {
      toast({
        title: "Platform Disconnected",
        description: "Successfully disconnected from the platform",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
    }
  });

  // Test connection mutation
  const testConnectionMutation = useMutation({
    mutationFn: async (platformId: number) => {
      return apiRequest(`/api/integrations/${platformId}/test`, {
        method: 'POST'
      });
    },
    onSuccess: () => {
      toast({
        title: "Connection Successful",
        description: "Platform connection is working properly",
      });
    },
    onError: () => {
      toast({
        title: "Connection Failed",
        description: "Unable to connect to platform",
        variant: "destructive",
      });
    }
  });

  const availablePlatforms: PlatformIntegration[] = [
    {
      id: 1,
      platform: 'Udemy',
      status: 'connected',
      description: 'World\'s largest online learning marketplace with 57M+ students',
      logo: '🎓',
      category: 'marketplace',
      features: ['Global reach', 'Built-in marketing', 'Student reviews', 'Mobile app'],
      popularity: 95,
      pricing: '50% commission',
      setupDifficulty: 'medium',
      estimatedSetupTime: '30 minutes',
      monthlyUsage: 1250,
      lastActivity: '2 hours ago',
      lastSync: '2025-06-24T14:30:00Z'
    },
    {
      id: 2,
      platform: 'YouTube',
      status: 'connected',
      description: 'Share course previews and build your audience on the world\'s largest video platform',
      logo: '📺',
      category: 'video',
      features: ['Video hosting', 'Monetization', 'Analytics', 'Live streaming'],
      popularity: 98,
      pricing: 'Free + revenue share',
      setupDifficulty: 'easy',
      estimatedSetupTime: '15 minutes',
      monthlyUsage: 890,
      lastActivity: '1 hour ago',
      lastSync: '2025-06-24T15:45:00Z'
    },
    {
      id: 3,
      platform: 'Teachable',
      status: 'disconnected',
      description: 'Create your own branded course website with full control',
      logo: '🏫',
      category: 'marketplace',
      features: ['Custom branding', 'No commission', 'Direct payments', 'Full control'],
      popularity: 78,
      pricing: '$39/month + transaction fees',
      setupDifficulty: 'medium',
      estimatedSetupTime: '45 minutes'
    },
    {
      id: 4,
      platform: 'LinkedIn Learning',
      status: 'pending',
      description: 'Professional development platform with enterprise reach',
      logo: '💼',
      category: 'marketplace',
      features: ['Professional audience', 'Enterprise sales', 'Certificates', 'High value'],
      popularity: 85,
      pricing: '25% commission',
      setupDifficulty: 'advanced',
      estimatedSetupTime: '2-3 weeks (review process)'
    },
    {
      id: 5,
      platform: 'Mailchimp',
      status: 'connected',
      description: 'Automated email marketing campaigns for course promotion',
      logo: '📧',
      category: 'email',
      features: ['Email campaigns', 'Student segmentation', 'Automation', 'Analytics'],
      popularity: 82,
      pricing: 'Free up to 2000 contacts',
      setupDifficulty: 'easy',
      estimatedSetupTime: '20 minutes',
      monthlyUsage: 450,
      lastActivity: '3 hours ago',
      lastSync: '2025-06-24T13:15:00Z'
    },
    {
      id: 6,
      platform: 'Skillshare',
      status: 'disconnected',
      description: 'Creative community platform focused on project-based learning',
      logo: '🎨',
      category: 'marketplace',
      features: ['Creative focus', 'Project-based', 'Community', 'Royalties'],
      popularity: 70,
      pricing: '40% commission',
      setupDifficulty: 'easy',
      estimatedSetupTime: '25 minutes'
    },
    {
      id: 7,
      platform: 'Vimeo',
      status: 'premium',
      description: 'Professional video hosting with privacy controls and custom branding',
      logo: '🎬',
      category: 'video',
      features: ['High quality', 'No ads', 'Custom players', 'Privacy controls'],
      popularity: 65,
      pricing: '$7/month+',
      setupDifficulty: 'easy',
      estimatedSetupTime: '15 minutes'
    },
    {
      id: 8,
      platform: 'Facebook Ads',
      status: 'error',
      description: 'Targeted social media advertising to reach potential students',
      logo: '📘',
      category: 'social',
      features: ['Precise targeting', 'Video ads', 'Retargeting', 'Lookalike audiences'],
      popularity: 88,
      pricing: 'Pay per click',
      setupDifficulty: 'medium',
      estimatedSetupTime: '40 minutes',
      errorMessage: 'API key expired - reconnection required'
    },
    {
      id: 9,
      platform: 'Google Analytics',
      status: 'connected',
      description: 'Track course performance and student behavior with detailed analytics',
      logo: '📊',
      category: 'analytics',
      features: ['Traffic analysis', 'Conversion tracking', 'Custom events', 'Reports'],
      popularity: 92,
      pricing: 'Free',
      setupDifficulty: 'medium',
      estimatedSetupTime: '35 minutes',
      monthlyUsage: 320,
      lastActivity: '6 hours ago',
      lastSync: '2025-06-24T12:00:00Z'
    },
    {
      id: 10,
      platform: 'Discord',
      status: 'disconnected',
      description: 'Build a community around your courses with Discord integration',
      logo: '🎮',
      category: 'social',
      features: ['Community building', 'Real-time chat', 'Voice channels', 'Bots'],
      popularity: 75,
      pricing: 'Free',
      setupDifficulty: 'easy',
      estimatedSetupTime: '20 minutes'
    }
  ];

  // Refresh status mutation
  const refreshStatusMutation = useMutation({
    mutationFn: async () => {
      return apiRequest('/api/integrations/refresh', {
        method: 'POST'
      });
    },
    onSuccess: () => {
      toast({
        title: "Status Refreshed",
        description: "Platform connection status has been updated",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      setRefreshing(false);
    },
    onError: () => {
      toast({
        title: "Refresh Failed",
        description: "Could not refresh platform status",
        variant: "destructive",
      });
      setRefreshing(false);
    }
  });

  // Filter platforms based on search and category
  const filteredPlatforms = availablePlatforms.filter(platform => {
    const matchesSearch = platform.platform.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         platform.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || platform.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const connectedPlatforms = availablePlatforms.filter(p => p.status === 'connected');
  const disconnectedPlatforms = availablePlatforms.filter(p => p.status === 'disconnected');
  const pendingPlatforms = availablePlatforms.filter(p => p.status === 'pending');
  const errorPlatforms = availablePlatforms.filter(p => p.status === 'error');

  const handleConnect = (platform: PlatformIntegration) => {
    setSelectedPlatform(platform);
    setShowConnectionDialog(true);
  };

  const handleSubmitConnection = () => {
    if (!selectedPlatform || !connectionForm.apiKey) {
      toast({
        title: "Missing Information",
        description: "Please enter your API key to connect",
        variant: "destructive",
      });
      return;
    }

    connectPlatformMutation.mutate({
      platformId: selectedPlatform.id,
      credentials: connectionForm
    });
  };

  const handleRefreshStatus = () => {
    setRefreshing(true);
    refreshStatusMutation.mutate();
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'social': return <Share2 className="h-4 w-4" />;
      case 'marketplace': return <Store className="h-4 w-4" />;
      case 'video': return <VideoIcon className="h-4 w-4" />;
      case 'analytics': return <BarChart3 className="h-4 w-4" />;
      case 'email': return <Mail className="h-4 w-4" />;
      case 'learning': return <BookOpen className="h-4 w-4" />;
      default: return <Link2 className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'premium': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSetupDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'advanced': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
              <Link2 className="h-6 w-6 text-white" />
            </div>
            Platform Integrations
          </h1>
          <p className="text-muted-foreground mt-2">
            Connect your content to external platforms and marketplaces to expand your reach
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleRefreshStatus}
            disabled={refreshing}
            className="gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh Status
          </Button>
          <Button 
            onClick={() => setShowConnectionDialog(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Integration
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connected</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{connectedPlatforms.length}</div>
            <p className="text-xs text-muted-foreground">Active integrations</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Loader2 className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingPlatforms.length}</div>
            <p className="text-xs text-muted-foreground">Awaiting approval</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{errorPlatforms.length}</div>
            <p className="text-xs text-muted-foreground">Need attention</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Sync</CardTitle>
            <Activity className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {connectedPlatforms.reduce((sum, p) => sum + (p.monthlyUsage || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">API calls this month</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <Input
            placeholder="Search platforms..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="marketplace">Marketplaces</SelectItem>
            <SelectItem value="video">Video Platforms</SelectItem>
            <SelectItem value="social">Social Media</SelectItem>
            <SelectItem value="email">Email Marketing</SelectItem>
            <SelectItem value="analytics">Analytics</SelectItem>
            <SelectItem value="learning">Learning Management</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="connected">Connected ({connectedPlatforms.length})</TabsTrigger>
          <TabsTrigger value="available">Available ({disconnectedPlatforms.length})</TabsTrigger>
          <TabsTrigger value="marketplace">Marketplaces</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Integration Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Integrations</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{availablePlatforms.length}</div>
                <p className="text-xs text-muted-foreground">+2 from last month</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Connected</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{connectedPlatforms.length}</div>
                <p className="text-xs text-muted-foreground">Active connections</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Data Synced</CardTitle>
                <Zap className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.2k</div>
                <p className="text-xs text-muted-foreground">Records this month</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Automation</CardTitle>
                <Shield className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">98%</div>
                <p className="text-xs text-muted-foreground">Success rate</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Integration Activity</CardTitle>
              <CardDescription>Latest sync activities and platform updates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[
                  { platform: 'Zapier', action: 'Data sync completed', time: '2 minutes ago', status: 'success' },
                  { platform: 'YouTube', action: 'Video uploaded', time: '1 hour ago', status: 'success' },
                  { platform: 'Mailchimp', action: 'Campaign sent', time: '3 hours ago', status: 'success' },
                  { platform: 'Facebook', action: 'Post shared', time: '5 hours ago', status: 'pending' }
                ].map((activity, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        activity.status === 'success' ? 'bg-green-500' :
                        activity.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
                      }`} />
                      <div>
                        <p className="font-medium">{activity.platform}</p>
                        <p className="text-sm text-muted-foreground">{activity.action}</p>
                      </div>
                    </div>
                    <span className="text-sm text-muted-foreground">{activity.time}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="connected" className="space-y-4">
          {connectedPlatforms.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Link2 className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Connected Platforms</h3>
                <p className="text-muted-foreground text-center mb-4">
                  Connect to external platforms to expand your course distribution and automate workflows
                </p>
                <Button onClick={() => setShowConnectionDialog(true)}>
                  Connect Your First Platform
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {/* Connected platforms with enhanced info */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {connectedPlatforms.map((platform) => (
                  <Card key={platform.id} className="relative border-l-4 border-l-green-500">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-3xl">{platform.logo}</span>
                          <div>
                            <CardTitle className="text-xl">{platform.platform}</CardTitle>
                            <div className="flex items-center space-x-2 mt-1">
                              {getCategoryIcon(platform.category)}
                              <span className="text-sm text-muted-foreground capitalize">{platform.category}</span>
                              <Badge className="bg-green-100 text-green-800">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Connected
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-sm text-muted-foreground">{platform.description}</p>
                      
                      {/* Usage Statistics */}
                      {platform.monthlyUsage && (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>Monthly Usage</span>
                            <span className="font-medium">{platform.monthlyUsage} calls</span>
                          </div>
                          <Progress value={(platform.monthlyUsage / 2000) * 100} className="h-2" />
                        </div>
                      )}

                      {/* Last Activity */}
                      {platform.lastActivity && (
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Activity className="h-4 w-4" />
                          <span>Last activity: {platform.lastActivity}</span>
                        </div>
                      )}

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Active Features</h4>
                        <div className="flex flex-wrap gap-1">
                          {platform.features.slice(0, 3).map((feature, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                          {platform.features.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{platform.features.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => testConnectionMutation.mutate(platform.id)}
                        disabled={testConnectionMutation.isPending}
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        {testConnectionMutation.isPending ? 'Testing...' : 'Test'}
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => disconnectPlatformMutation.mutate(platform.id)}
                        disabled={disconnectPlatformMutation.isPending}
                      >
                        <Power className="h-4 w-4 mr-2" />
                        Disconnect
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>

              {/* Issues and Alerts */}
              {errorPlatforms.length > 0 && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    <strong>Attention needed:</strong> {errorPlatforms.length} platform{errorPlatforms.length > 1 ? 's' : ''} require{errorPlatforms.length === 1 ? 's' : ''} reconnection.
                    <div className="mt-2">
                      {errorPlatforms.map(platform => (
                        <div key={platform.id} className="flex items-center justify-between p-2 bg-red-100 rounded mt-1">
                          <div className="flex items-center gap-2">
                            <span>{platform.logo}</span>
                            <span className="font-medium">{platform.platform}</span>
                            <span className="text-sm">- {platform.errorMessage}</span>
                          </div>
                          <Button size="sm" variant="outline" onClick={() => handleConnect(platform)}>
                            Fix Now
                          </Button>
                        </div>
                      ))}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="available" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPlatforms.filter(p => p.status === 'disconnected' || p.status === 'premium').map((platform) => (
              <Card key={platform.id} className="relative hover:shadow-lg transition-all duration-200 border-2 hover:border-primary/20">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-3xl">{platform.logo}</span>
                      <div>
                        <CardTitle className="text-xl">{platform.platform}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          {getCategoryIcon(platform.category)}
                          <span className="text-sm text-muted-foreground capitalize">{platform.category}</span>
                          <Badge className={getStatusColor(platform.status)}>
                            {platform.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    {platform.popularity && (
                      <Badge variant="outline" className="text-xs">
                        {platform.popularity}% popular
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{platform.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Pricing:</span>
                      <p className="text-muted-foreground">{platform.pricing || 'Contact for pricing'}</p>
                    </div>
                    <div>
                      <span className="font-medium">Setup:</span>
                      <p className={`${getSetupDifficultyColor(platform.setupDifficulty || 'medium')} capitalize`}>
                        {platform.setupDifficulty || 'Medium'}
                      </p>
                    </div>
                  </div>
                  
                  {platform.estimatedSetupTime && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{platform.estimatedSetupTime}</span>
                    </div>
                  )}

                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Key Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {platform.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {platform.features.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{platform.features.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  {platform.status === 'premium' ? (
                    <Button variant="outline" size="sm" disabled>
                      <Zap className="h-4 w-4 mr-2" />
                      Upgrade Required
                    </Button>
                  ) : (
                    <Button 
                      size="sm"
                      onClick={() => handleConnect(platform)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Connect
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Learn More
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="marketplace" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Link href="/platform-integrations/marketplaces">
              <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-3 rounded-full bg-blue-100">
                      <Store className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle>Course Marketplaces</CardTitle>
                      <CardDescription>Udemy, Coursera, Skillshare</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Publish your courses to top online learning marketplaces and reach millions of students worldwide.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button className="w-full">
                    Explore Marketplaces
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>

            <Link href="/platform-integrations/video">
              <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-3 rounded-full bg-red-100">
                      <VideoIcon className="h-6 w-6 text-red-600" />
                    </div>
                    <div>
                      <CardTitle>Video Platforms</CardTitle>
                      <CardDescription>YouTube, Vimeo, TikTok</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Share your course content on video platforms to build your audience and drive traffic to your courses.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button className="w-full">
                    Explore Video Platforms
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>

            <Link href="/marketing/email">
              <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="p-3 rounded-full bg-green-100">
                      <Mail className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <CardTitle>Email Marketing</CardTitle>
                      <CardDescription>Mailchimp, ConvertKit</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Build and nurture your audience with automated email campaigns and course promotions.
                  </p>
                </CardContent>
                <CardFooter>
                  <Button className="w-full">
                    Setup Email Marketing
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardFooter>
              </Card>
            </Link>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>API Rate Limits</CardTitle>
                <CardDescription>Monitor your API usage across platforms</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {connectedPlatforms.map((platform) => (
                  <div key={platform.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span>{platform.logo}</span>
                        <span className="font-medium">{platform.platform}</span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {platform.monthlyUsage || 0}/2000
                      </span>
                    </div>
                    <Progress value={((platform.monthlyUsage || 0) / 2000) * 100} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sync Settings</CardTitle>
                <CardDescription>Configure how often platforms sync</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-sync">Auto-sync enabled</Label>
                  <Switch id="auto-sync" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="sync-frequency">Sync frequency</Label>
                  <Select defaultValue="hourly">
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="realtime">Real-time</SelectItem>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="error-notifications">Error notifications</Label>
                  <Switch id="error-notifications" defaultChecked />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Security</CardTitle>
                <CardDescription>Manage security settings for integrations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="two-factor">Require 2FA for new connections</Label>
                  <Switch id="two-factor" />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="webhook-verification">Webhook signature verification</Label>
                  <Switch id="webhook-verification" defaultChecked />
                </div>
                <Button variant="outline" className="w-full">
                  <Shield className="h-4 w-4 mr-2" />
                  Review Security Logs
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Data Export</CardTitle>
                <CardDescription>Export your integration data</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button variant="outline" className="w-full">
                  Export Connection History
                </Button>
                <Button variant="outline" className="w-full">
                  Export Usage Statistics
                </Button>
                <Button variant="outline" className="w-full">
                  Export Error Logs
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Connection Dialog */}
      <Dialog open={showConnectionDialog} onOpenChange={setShowConnectionDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedPlatform && (
                <>
                  <span className="text-2xl">{selectedPlatform.logo}</span>
                  Connect to {selectedPlatform.platform}
                </>
              )}
            </DialogTitle>
            <DialogDescription>
              {selectedPlatform?.description}
            </DialogDescription>
          </DialogHeader>
          
          {selectedPlatform && (
            <div className="space-y-4">
              {/* Setup Information */}
              <div className="p-4 bg-muted rounded-lg space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Setup Difficulty:</span>
                  <span className={`font-medium ${getSetupDifficultyColor(selectedPlatform.setupDifficulty || 'medium')} capitalize`}>
                    {selectedPlatform.setupDifficulty || 'Medium'}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Estimated Time:</span>
                  <span className="font-medium">{selectedPlatform.estimatedSetupTime || '30 minutes'}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Pricing:</span>
                  <span className="font-medium">{selectedPlatform.pricing || 'Contact for pricing'}</span>
                </div>
              </div>

              {/* Connection Form */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="api-key">API Key *</Label>
                  <Input
                    id="api-key"
                    type="password"
                    placeholder="Enter your API key"
                    value={connectionForm.apiKey}
                    onChange={(e) => setConnectionForm(prev => ({ ...prev, apiKey: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Get your API key from the {selectedPlatform.platform} developer console
                  </p>
                </div>

                <div>
                  <Label htmlFor="webhook-url">Webhook URL (Optional)</Label>
                  <Input
                    id="webhook-url"
                    placeholder="https://your-webhook-url.com"
                    value={connectionForm.webhookUrl}
                    onChange={(e) => setConnectionForm(prev => ({ ...prev, webhookUrl: e.target.value }))}
                  />
                </div>

                {/* Features Preview */}
                <div>
                  <Label>Features You'll Get:</Label>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {selectedPlatform.features.map((feature, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => {
                  setShowConnectionDialog(false);
                  setConnectionForm({ apiKey: '', webhookUrl: '', customSettings: {} });
                }}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleSubmitConnection}
                  disabled={connectPlatformMutation.isPending || !connectionForm.apiKey}
                >
                  {connectPlatformMutation.isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Connecting...
                    </>
                  ) : (
                    <>
                      <Link2 className="h-4 w-4 mr-2" />
                      Connect Platform
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

        <TabsContent value="available" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {disconnectedPlatforms.map((platform) => (
              <Card key={platform.id} className="relative hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{platform.logo}</span>
                      <div>
                        <CardTitle className="text-lg">{platform.platform}</CardTitle>
                        <div className="flex items-center space-x-2">
                          {getCategoryIcon(platform.category)}
                          <Badge variant="secondary">{platform.category}</Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{platform.description}</p>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Features</h4>
                    <div className="flex flex-wrap gap-1">
                      {platform.features.map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <Button 
                    className="w-full" 
                    onClick={() => handleConnect(platform)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Connect
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
              <CardDescription>Configure global settings for platform integrations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-medium">Auto-sync enabled</Label>
                  <p className="text-sm text-muted-foreground">Automatically sync data with connected platforms</p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-medium">Real-time notifications</Label>
                  <p className="text-sm text-muted-foreground">Get notified when sync activities complete</p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-medium">Error reporting</Label>
                  <p className="text-sm text-muted-foreground">Send error reports when integrations fail</p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Connection Modal */}
      {selectedPlatform && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Connect to {selectedPlatform.platform}</CardTitle>
              <CardDescription>Enter your credentials to connect this platform</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <Input
                  id="apiKey"
                  type="password"
                  placeholder="Enter your API key"
                  value={connectionForm.apiKey}
                  onChange={(e) => setConnectionForm(prev => ({ ...prev, apiKey: e.target.value }))}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="webhookUrl">Webhook URL (Optional)</Label>
                <Input
                  id="webhookUrl"
                  placeholder="https://your-webhook-url.com"
                  value={connectionForm.webhookUrl}
                  onChange={(e) => setConnectionForm(prev => ({ ...prev, webhookUrl: e.target.value }))}
                />
              </div>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Your credentials will be encrypted and stored securely. We never share your data with third parties.
                </AlertDescription>
              </Alert>

              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => setSelectedPlatform(null)}
                >
                  Cancel
                </Button>
                <Button 
                  className="flex-1"
                  onClick={handleSubmitConnection}
                  disabled={!connectionForm.apiKey || connectPlatformMutation.isPending}
                >
                  {connectPlatformMutation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  )}
                  Connect
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}