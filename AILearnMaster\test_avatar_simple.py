#!/usr/bin/env python3
"""
Simple Avatar Generation Testing Script
Tests the new avatar endpoints without complex dependencies
"""

import requests
import json
import time
import base64

def test_health():
    """Test health endpoint"""
    print("🏥 Testing Health Endpoint...")
    try:
        url = "https://trade-digital--courseai-a100-simple-health.modal.run"
        response = requests.get(url, timeout=30)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed")
            print(f"Services: {data.get('services', [])}")
            return True
        else:
            print(f"❌ Health check failed")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_avatar_endpoint():
    """Test avatar generation endpoint with simple data"""
    print("\n🎭 Testing Avatar Generation Endpoint...")
    
    # Create simple base64 test data
    test_image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="  # 1x1 pixel PNG
    test_audio_data = "UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA="  # Empty WAV header
    
    try:
        url = "https://trade-digital--courseai-a100-simple-api-avatar.modal.run"
        payload = {
            "ref_image_base64": test_image_data,
            "audio_base64": test_audio_data,
            "config": {
                "width": 256,
                "height": 256,
                "fps": 12,
                "max_frames": 24,
                "steps": 2
            }
        }
        
        print(f"Testing URL: {url}")
        start_time = time.time()
        response = requests.post(url, json=payload, timeout=300)
        response_time = time.time() - start_time
        
        print(f"Status Code: {response.status_code}")
        print(f"Response time: {response_time:.2f}s")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"✅ Avatar generation successful")
                print(f"Duration: {data.get('duration', 0):.2f}s")
                print(f"Frames: {data.get('frames', 0)}")
                print(f"Method: {data.get('method', 'unknown')}")
                video_size = len(data.get('video_base64', ''))
                print(f"Video size: {video_size} bytes (base64)")
                return True
            else:
                print(f"❌ Avatar generation failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_course_avatar_endpoint():
    """Test integrated course avatar generation"""
    print("\n🎓 Testing Course Avatar Integration...")
    
    test_image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    
    try:
        url = "https://trade-digital--courseai-a100-simple-api-course-avatar.modal.run"
        payload = {
            "prompt": "Welcome to our AI course. Today we'll learn about machine learning.",
            "ref_image_base64": test_image_data,
            "voice_id": "tts_models/en/ljspeech/tacotron2-DDC",
            "config": {
                "width": 256,
                "height": 256,
                "fps": 12,
                "max_frames": 36,
                "steps": 2
            }
        }
        
        print(f"Testing URL: {url}")
        start_time = time.time()
        response = requests.post(url, json=payload, timeout=600)
        response_time = time.time() - start_time
        
        print(f"Status Code: {response.status_code}")
        print(f"Response time: {response_time:.2f}s")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print(f"✅ Course avatar generation successful")
                print(f"Generated text: {data.get('generated_text', '')[:100]}...")
                video_info = data.get('video_info', {})
                print(f"Video duration: {video_info.get('duration', 0):.2f}s")
                print(f"Video frames: {video_info.get('frames', 0)}")
                print(f"Pipeline: {data.get('pipeline', 'unknown')}")
                return True
            else:
                print(f"❌ Course avatar generation failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Run avatar tests"""
    print("🚀 Testing Avatar Generation Integration\n")
    
    results = {}
    results["health"] = test_health()
    results["avatar"] = test_avatar_endpoint()
    results["course_avatar"] = test_course_avatar_endpoint()
    
    print("\n" + "="*50)
    print("📊 AVATAR TESTS SUMMARY")
    print("="*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test.upper()}: {status}")
    
    print(f"\nTOTAL: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All avatar tests passed! Integration successful!")
    else:
        print(f"⚠️ {total - passed} tests failed. Check the endpoints.")
    
    print("\n📋 New Avatar Endpoints:")
    print("  Avatar Generation: https://trade-digital--courseai-a100-simple-api-avatar.modal.run")
    print("  Course Avatar: https://trade-digital--courseai-a100-simple-api-course-avatar.modal.run")

if __name__ == "__main__":
    main()
