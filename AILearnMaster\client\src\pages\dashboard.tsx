import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { StatCard } from "@/components/dashboard/StatCard";
import { CourseTable } from "@/components/dashboard/CourseTable";
import { TemplateCard } from "@/components/dashboard/TemplateCard";
import { IntegrationCard } from "@/components/dashboard/IntegrationCard";
import { Course, Integration, Template, UserStats, UserEngagementAnalytics } from "@/types";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { useOnboarding } from "@/hooks/use-onboarding";
import { Link, useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { 
  <PERSON><PERSON><PERSON>, ChevronRight, Clock, DollarSign, Download, 
  BarChart2, TrendingUp, Users, Eye, Sparkles, Plus,
  Loader2, AlertTriangle, FileEdit, Trash2, PlayCircle,
  Info, Database, Filter
} from "lucide-react";
import CourseRecommendationCarousel from "@/components/recommendation/CourseRecommendationCarousel";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Redirect } from "wouter";
import { PDFExportButton } from "@/components/PDFExportButton";

// Chart components
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  LineChart,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

export default function Dashboard() {
  const [isConfirmingDelete, setIsConfirmingDelete] = useState<number | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();
  const { resetOnboarding } = useOnboarding();
  const [activeTab, setActiveTab] = useState("overview");
  
  // If user is admin, redirect to admin dashboard
  if (user?.role === 'admin') {
    return <Redirect to="/admin-dashboard" />;
  }

  // Fetch user's courses
  const { data: coursesData, isLoading: isLoadingCourses } = useQuery({
    queryKey: ['/api/courses'],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', '/api/courses');
        return await res.json();
      } catch (error) {
        console.error("Error fetching courses:", error);
        // Return mock data for demonstration
        return [
          {
            id: 1,
            userId: 1,
            title: "Digital Marketing Fundamentals",
            description: "Learn the basics of digital marketing in this comprehensive course.",
            category: "Marketing",
            status: "published",
            thumbnailUrl: "",
            completion: 100,
            lessonsCount: 12,
            targetAudience: "Beginners",
            createdAt: new Date(),
            updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
          },
          {
            id: 2,
            userId: 1,
            title: "AI for Business Leaders",
            description: "Understand how AI can transform your business operations.",
            category: "Business",
            status: "draft",
            thumbnailUrl: "",
            completion: 60,
            lessonsCount: 8,
            targetAudience: "Intermediate",
            createdAt: new Date(),
            updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
          },
          {
            id: 3,
            userId: 1,
            title: "Python Programming Basics",
            description: "Start your programming journey with Python fundamentals.",
            category: "Technology",
            status: "in_progress",
            thumbnailUrl: "",
            completion: 25,
            lessonsCount: 15,
            targetAudience: "Beginners",
            createdAt: new Date(),
            updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000)
          }
        ] as Course[];
      }
    },
  });

  // Fetch user stats
  const { data: userStats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['/api/user-stats'],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', '/api/user-stats');
        return await res.json();
      } catch (error) {
        console.error("Error fetching user stats:", error);
        // Return mock data for demonstration
        return {
          userId: 1,
          activeCourses: 3,
          publishedCourses: 2,
          aiCredits: 450,
          storageUsed: 24,
          storageLimit: 100
        } as UserStats;
      }
    },
  });

  // Fetch templates
  const { data: templates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['/api/templates'],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', '/api/templates');
        return await res.json();
      } catch (error) {
        console.error("Error fetching templates:", error);
        // Return mock data for demonstration
        return [
          {
            id: 1,
            name: "Quick Course Generator",
            description: "Generate a complete course structure from just a title and description",
            icon: "ri-robot-line",
            type: "course_generator"
          },
          {
            id: 2,
            name: "Script Writer",
            description: "Create engaging lesson scripts with AI assistance",
            icon: "ri-file-text-line",
            type: "script_generator"
          },
          {
            id: 3,
            name: "AI Voiceover",
            description: "Convert your scripts to natural-sounding voiceovers",
            icon: "ri-voice-recognition-line",
            type: "voice_generator"
          }
        ] as Template[];
      }
    },
  });

  // Fetch integrations
  const { data: integrations, isLoading: isLoadingIntegrations } = useQuery({
    queryKey: ['/api/integrations'],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', '/api/integrations');
        return await res.json();
      } catch (error) {
        console.error("Error fetching integrations:", error);
        // Return mock data for demonstration
        return [
          {
            id: 1,
            userId: 1,
            platform: "Udemy",
            status: "disconnected",
            platformUserId: null,
            accessToken: null,
            refreshToken: null,
            tokenExpiry: null,
            config: {},
            createdAt: new Date()
          },
          {
            id: 2,
            userId: 1,
            platform: "Teachable",
            status: "connected",
            platformUserId: "teacher123",
            accessToken: "mock-token",
            refreshToken: "mock-refresh",
            tokenExpiry: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            config: {},
            createdAt: new Date()
          },
          {
            id: 3,
            userId: 1,
            platform: "Kajabi",
            status: "disconnected",
            platformUserId: null,
            accessToken: null,
            refreshToken: null,
            tokenExpiry: null,
            config: {},
            createdAt: new Date()
          }
        ] as Integration[];
      }
    },
  });

  // Fetch course analytics
  const { data: courseAnalytics, isLoading: isLoadingAnalytics } = useQuery({
    queryKey: ['/api/analytics/user-engagement'],
    queryFn: async () => {
      try {
        const res = await apiRequest('GET', '/api/analytics/user-engagement');
        return await res.json();
      } catch (error) {
        console.error("Error fetching analytics:", error);
        // Return mock data for demonstration
        return {
          viewsByDay: [
            { date: "2025-03-29", count: 23 },
            { date: "2025-03-30", count: 18 },
            { date: "2025-03-31", count: 25 },
            { date: "2025-04-01", count: 32 },
            { date: "2025-04-02", count: 29 },
            { date: "2025-04-03", count: 41 },
            { date: "2025-04-04", count: 37 }
          ],
          enrollmentsByDay: [
            { date: "2025-03-29", count: 3 },
            { date: "2025-03-30", count: 2 },
            { date: "2025-03-31", count: 1 },
            { date: "2025-04-01", count: 5 },
            { date: "2025-04-02", count: 4 },
            { date: "2025-04-03", count: 7 },
            { date: "2025-04-04", count: 6 }
          ],
          totalEnrollments: 28,
          totalViews: 205,
          coursePerformance: [
            { 
              courseId: 1, 
              title: "Digital Marketing Fundamentals", 
              views: 120, 
              enrollments: 18, 
              averageRating: 4.7 
            },
            { 
              courseId: 2, 
              title: "AI for Business Leaders", 
              views: 55, 
              enrollments: 8, 
              averageRating: 4.5 
            },
            { 
              courseId: 3, 
              title: "Python Programming Basics", 
              views: 30, 
              enrollments: 2, 
              averageRating: 0 
            }
          ],
          earnings: {
            totalEarnings: 850,
            earningsByMonth: [
              { month: "Jan", amount: 0 },
              { month: "Feb", amount: 0 },
              { month: "Mar", amount: 350 },
              { month: "Apr", amount: 500 }
            ]
          }
        };
      }
    },
  });

  const handleCreateCourse = () => {
    // Redirect to the create page which contains the format selection
    setLocation('/create');
  };

  const [, setLocation] = useLocation();
  const { mutate: deleteMutation, isPending: isDeleting } = useMutation({
    mutationFn: async (courseId: number) => {
      const res = await apiRequest("DELETE", `/api/courses/${courseId}`);
      if (!res.ok) {
        throw new Error("Failed to delete course");
      }
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Course Deleted",
        description: "Your course has been deleted successfully.",
      });
      // Refresh courses data
      queryClient.invalidateQueries({ queryKey: ['/api/courses'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to delete course: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const handleEditCourse = (course: Course) => {
    // Navigate to course edit page
    setLocation(`/courses/${course.id}/edit`);
  };

  const handlePreviewCourse = (course: Course) => {
    // Navigate to course preview page
    setLocation(`/courses/${course.id}/preview`);
  };

  const handleDeleteCourse = (course: Course) => {
    // Set the course ID for confirmation
    setIsConfirmingDelete(course.id);
  };
  
  const confirmDelete = () => {
    if (isConfirmingDelete) {
      // Delete course after confirmation
      deleteMutation(isConfirmingDelete);
      setIsConfirmingDelete(null);
    }
  };
  
  const cancelDelete = () => {
    setIsConfirmingDelete(null);
  };

  const handleTemplateClick = (template: Template) => {
    // Redirect to AI Templates page with the template info
    setLocation(`/ai-templates?template=${template.type}&id=${template.id}`);
  };

  const handleConnectIntegration = (integration: Integration) => {
    // Navigate to platform integrations page with integration name as query parameter
    setLocation(`/platform-integrations?platform=${integration.platform.toLowerCase()}`);
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // The course creation flow is now handled via navigation to separate pages

  return (
    <div className="p-6 space-y-6 bg-slate-50">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">User Dashboard</h1>
        <p className="text-muted-foreground">
          Manage your courses, track performance, and access creation tools.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-3 lg:w-[600px]">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="tools">Creation Tools</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Welcome Section */}
          <Card>
            <CardContent className="pt-6">
              <div className="sm:flex items-start justify-between">
                <div className="mb-4 sm:mb-0">
                  <h2 className="text-2xl font-bold mb-2">
                    Welcome back, {user?.name || user?.username || 'User'}!
                  </h2>
                  <p className="text-muted-foreground">Ready to create your next awesome course with AI assistance?</p>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={resetOnboarding}
                    className="mt-2 text-xs text-purple-600 hover:text-purple-800 flex items-center gap-1"
                  >
                    <Sparkles className="h-3.5 w-3.5" />
                    <span>Show Onboarding Tour</span>
                  </Button>
                </div>
                <div className="flex items-center gap-3">
                  <Button 
                    className="flex items-center gap-2"
                    onClick={handleCreateCourse}
                    disabled={isDeleting} // Prevent creating while deleting operations are pending
                  >
                    <Plus className="h-4 w-4" />
                    <span>Create New Course</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Personalized Recommendations */}
          <Card>
            <CardContent className="pt-6 px-6">
              <CourseRecommendationCarousel />
            </CardContent>
          </Card>
          
          {/* Course Statistics */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Courses</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats?.activeCourses || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {userStats?.publishedCourses || 0} published
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{courseAnalytics?.totalViews || 0}</div>
                <p className="text-xs text-muted-foreground">
                  +{courseAnalytics?.viewsByDay?.[6]?.count || 0} today
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">AI Credits</CardTitle>
                <i className="ri-magic-line h-4 w-4 text-muted-foreground"></i>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats?.aiCredits || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Available for use
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
                <i className="ri-hard-drive-line h-4 w-4 text-muted-foreground"></i>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{userStats?.storageUsed || 0}%</div>
                <p className="text-xs text-muted-foreground">
                  of {userStats?.storageLimit || 100}GB limit
                </p>
              </CardContent>
            </Card>
          </div>
          
          {/* Personalized Recommendations */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recommended For You</CardTitle>
                  <CardDescription>
                    AI-powered course suggestions based on your interests and activity
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <CourseRecommendationCarousel />
            </CardContent>
          </Card>

          {/* Recent Courses */}
          <Card className="mt-6">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Your Courses</CardTitle>
                  <CardDescription>
                    Manage and track your created courses
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/my-courses">
                    View All
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingCourses ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4 animate-pulse">
                      <div className="h-12 w-12 bg-gray-200 rounded"></div>
                      <div className="space-y-2 flex-1">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                      <div className="flex space-x-2">
                        <div className="h-8 w-16 bg-gray-200 rounded"></div>
                        <div className="h-8 w-16 bg-gray-200 rounded"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : coursesData && coursesData.length > 0 ? (
                <CourseTable 
                  courses={coursesData} 
                  onEdit={handleEditCourse}
                  onPreview={handlePreviewCourse}
                  onDelete={handleDeleteCourse}
                />
              ) : (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 mb-4">No courses created yet</p>
                  <Button onClick={handleCreateCourse}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Course
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Quick Access */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Media Library</CardTitle>
                <CardDescription>
                  Access your uploaded media files
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="ri-image-line text-2xl text-blue-500 mr-2"></i>
                    <span>Images</span>
                  </div>
                  <span className="text-sm text-muted-foreground">42 files</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="ri-video-line text-2xl text-purple-500 mr-2"></i>
                    <span>Videos</span>
                  </div>
                  <span className="text-sm text-muted-foreground">18 files</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <i className="ri-file-music-line text-2xl text-green-500 mr-2"></i>
                    <span>Audio</span>
                  </div>
                  <span className="text-sm text-muted-foreground">35 files</span>
                </div>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/media-library">
                    Open Media Library
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Subscription</CardTitle>
                <CardDescription>
                  Current plan and usage
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className={`px-2 py-1 rounded text-sm font-medium inline-block ${
                  user?.plan === 'pro' ? 'bg-purple-100 text-purple-800' :
                  user?.plan === 'business' ? 'bg-indigo-100 text-indigo-800' :
                  user?.plan === 'enterprise' ? 'bg-slate-100 text-slate-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {user?.plan?.charAt(0).toUpperCase() + user?.plan?.slice(1) || 'Starter'} Plan
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Storage</span>
                    <span className="text-sm font-medium">{userStats?.storageUsed || 0}% used</span>
                  </div>
                  <div className="w-full h-2 bg-slate-200 rounded-full">
                    <div 
                      className="h-full bg-primary rounded-full" 
                      style={{ width: `${userStats?.storageUsed || 0}%` }}
                    ></div>
                  </div>
                </div>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/settings">
                    Manage Subscription
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Frequently used tools
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/video-generator">
                    <i className="ri-video-add-line mr-2"></i>
                    Create Video
                  </Link>
                </Button>
                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/micro-learning-demo">
                    <i className="ri-layout-grid-line mr-2"></i>
                    Micro-Learning Mode
                  </Link>
                </Button>
                <Button variant="outline" className="w-full justify-start" onClick={handleCreateCourse}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Course
                </Button>
                <Button variant="outline" className="w-full justify-start" asChild>
                  <Link href="/marketplace">
                    <i className="ri-store-line mr-2"></i>
                    Marketplace
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Course Views</CardTitle>
                <CardDescription>
                  Views across all your courses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={courseAnalytics?.viewsByDay}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <defs>
                        <linearGradient id="colorViews" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#6366F1" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#6366F1" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <XAxis 
                        dataKey="date" 
                        tickFormatter={(value) => {
                          const date = new Date(value);
                          return `${date.getMonth()+1}/${date.getDate()}`;
                        }}
                      />
                      <YAxis />
                      <CartesianGrid strokeDasharray="3 3" />
                      <Tooltip 
                        labelFormatter={(value) => {
                          const date = new Date(value);
                          return date.toLocaleDateString();
                        }}
                      />
                      <Area 
                        type="monotone" 
                        dataKey="count" 
                        stroke="#6366F1" 
                        fillOpacity={1} 
                        fill="url(#colorViews)" 
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Course Enrollments</CardTitle>
                <CardDescription>
                  New enrollments over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={courseAnalytics?.enrollmentsByDay}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                    >
                      <defs>
                        <linearGradient id="colorEnrollments" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#22C55E" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#22C55E" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <XAxis 
                        dataKey="date" 
                        tickFormatter={(value) => {
                          const date = new Date(value);
                          return `${date.getMonth()+1}/${date.getDate()}`;
                        }}
                      />
                      <YAxis />
                      <CartesianGrid strokeDasharray="3 3" />
                      <Tooltip 
                        labelFormatter={(value) => {
                          const date = new Date(value);
                          return date.toLocaleDateString();
                        }}
                      />
                      <Area 
                        type="monotone" 
                        dataKey="count" 
                        stroke="#22C55E" 
                        fillOpacity={1} 
                        fill="url(#colorEnrollments)" 
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Course Performance</CardTitle>
              <CardDescription>
                Compare metrics across your courses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={courseAnalytics?.coursePerformance}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="title" />
                    <YAxis yAxisId="left" orientation="left" stroke="#6366F1" />
                    <YAxis yAxisId="right" orientation="right" stroke="#22C55E" />
                    <Tooltip />
                    <Bar yAxisId="left" dataKey="views" name="Views" fill="#6366F1" />
                    <Bar yAxisId="right" dataKey="enrollments" name="Enrollments" fill="#22C55E" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {courseAnalytics?.earnings && (
            <Card>
              <CardHeader>
                <CardTitle>Earnings</CardTitle>
                <CardDescription>
                  Revenue from monetized courses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="p-4 bg-muted rounded-lg text-center">
                    <h3 className="text-muted-foreground text-sm font-medium mb-1">Total Earnings</h3>
                    <div className="text-2xl font-bold">{formatCurrency(courseAnalytics.earnings.totalEarnings)}</div>
                  </div>
                  <div className="md:col-span-3 h-[150px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={courseAnalytics.earnings.earningsByMonth}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis tickFormatter={(value) => `$${value}`} />
                        <Tooltip formatter={(value) => [`$${value}`, 'Earnings']} />
                        <Bar dataKey="amount" fill="#22C55E" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>
                <div className="flex justify-center">
                  <Button variant="outline" className="gap-2">
                    <Download className="h-4 w-4" />
                    Download Earnings Report
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Tools Tab */}
        <TabsContent value="tools" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* AI Templates */}
            <Card>
              <CardHeader>
                <CardTitle>AI Templates</CardTitle>
                <CardDescription>
                  Pre-built AI tools to accelerate your course creation
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {templates?.map(template => (
                  <div key={template.id} className="border rounded-lg p-4 hover:border-primary transition-colors">
                    <div className="flex items-start gap-3">
                      <div className="h-10 w-10 rounded bg-muted flex items-center justify-center">
                        <i className={`${template.icon} text-lg`}></i>
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{template.name}</h3>
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                      </div>
                      <Button 
                        size="sm" 
                        onClick={() => handleTemplateClick(template)}
                      >
                        Use
                      </Button>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
            
            {/* Marketplace Integrations */}
            <Card>
              <CardHeader>
                <CardTitle>Publishing Platforms</CardTitle>
                <CardDescription>
                  Connect and publish to external platforms
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {integrations?.map(integration => (
                  <div key={integration.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded bg-muted flex items-center justify-center">
                          <i className={`ri-${
                            integration.platform.toLowerCase() === 'udemy' ? 'book-line' : 
                            integration.platform.toLowerCase() === 'teachable' ? 'graduation-cap-line' :
                            'store-line'
                          } text-lg`}></i>
                        </div>
                        <div>
                          <h3 className="font-medium">{integration.platform}</h3>
                          <div className="text-xs">
                            {integration.status === 'connected' ? (
                              <span className="text-green-600 flex items-center gap-1">
                                <span className="h-2 w-2 rounded-full bg-green-600"></span> Connected
                              </span>
                            ) : (
                              <span className="text-orange-600 flex items-center gap-1">
                                <span className="h-2 w-2 rounded-full bg-orange-600"></span> Not connected
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <Button
                        variant={integration.status === 'connected' ? "outline" : "default"}
                        size="sm"
                        onClick={() => handleConnectIntegration(integration)}
                      >
                        {integration.status === 'connected' ? "Manage" : "Connect"}
                      </Button>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/platform-integrations">
                    Manage All Integrations
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Content Creation Tools</CardTitle>
              <CardDescription>
                Specialized tools to enhance your courses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Primary Course Creation Tools */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">Core Course Generators</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Enhanced Traditional Course Generator */}
                    <div className="border-2 border-blue-200 rounded-xl p-6 bg-gradient-to-br from-blue-50 to-indigo-50 hover:border-blue-300 transition-all duration-300 hover:shadow-lg">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-600 to-indigo-600 flex items-center justify-center shadow-md">
                          <i className="ri-video-line text-xl text-white"></i>
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">Enhanced Traditional Course</h3>
                          <p className="text-xs text-blue-600 font-medium">Professional Video Production</p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">Create professional courses with Mistral AI structure, Coqui TTS voices, dynamic media, and FFmpeg assembly</p>
                      <div className="text-xs text-gray-500 mb-4">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                          <span>Mistral AI Structure Generation</span>
                        </div>
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          <span>Coqui TTS Voice Synthesis</span>
                        </div>
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                          <span>Pexels/Pixabay Media & Marp Slides</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                          <span>FFmpeg Video Assembly</span>
                        </div>
                      </div>
                      <Button className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700" asChild>
                        <Link href="/course-creation/traditional-course">
                          Create Traditional Course
                        </Link>
                      </Button>
                    </div>

                    {/* Enhanced Avatar Course Generator */}
                    <div className="border-2 border-purple-200 rounded-xl p-6 bg-gradient-to-br from-purple-50 to-pink-50 hover:border-purple-300 transition-all duration-300 hover:shadow-lg">
                      <div className="flex items-center space-x-3 mb-4">
                        <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-purple-600 to-pink-600 flex items-center justify-center shadow-md">
                          <i className="ri-user-voice-line text-xl text-white"></i>
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">Enhanced Avatar Course</h3>
                          <p className="text-xs text-purple-600 font-medium">AI Avatar Presenter</p>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">Generate engaging avatar-based courses with EchoMimic V2 processing and optimized voice synthesis</p>
                      <div className="text-xs text-gray-500 mb-4">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                          <span>Mistral AI Structure Generation</span>
                        </div>
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                          <span>EchoMimic V2 Avatar Processing</span>
                        </div>
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          <span>Avatar-optimized TTS</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                          <span>Background Scene & Video Assembly</span>
                        </div>
                      </div>
                      <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700" asChild>
                        <Link href="/course-creation/avatar-course">
                          Create Avatar Course
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Supporting Tools */}
                <div>
                  <h4 className="text-sm font-semibold text-gray-700 mb-3 uppercase tracking-wide">Supporting Tools</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="border rounded-lg p-4 text-center space-y-3 hover:border-primary transition-colors">
                      <div className="mx-auto h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                        <i className="ri-voice-recognition-line text-lg text-green-600"></i>
                      </div>
                      <h3 className="font-medium text-sm">Text-to-Speech</h3>
                      <p className="text-xs text-muted-foreground">Convert content to natural voiceovers</p>
                      <Button size="sm" className="w-full" asChild>
                        <Link href="/ai-voice-generator">
                          Generate Audio
                        </Link>
                      </Button>
                    </div>



                    <div className="border rounded-lg p-4 text-center space-y-3 hover:border-primary transition-colors">
                      <div className="mx-auto h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                        <i className="ri-line-chart-line text-lg text-indigo-600"></i>
                      </div>
                      <h3 className="font-medium text-sm">Progress Tracking</h3>
                      <p className="text-xs text-muted-foreground">Monitor course generation status</p>
                      <Button size="sm" variant="outline" className="w-full" asChild>
                        <Link href="/course-progress">
                          View Progress
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
