import express, { Request, Response } from 'express';
import { z } from 'zod';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { TTSResponse } from '@shared/schema';
import { storage } from '../storage';

// Ensure uploads directory exists
const uploadsDir = path.join(process.cwd(), 'uploads', 'audio');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Create router
const router = express.Router();

// Open-Source TTS Services Only
import * as opensourceTTS from '../services/opensourceTTSService';

// Request validator
const textToSpeechSchema = z.object({
  text: z.string().min(1, "Text is required").max(5000, "Text is too long, maximum 5000 characters"),
  title: z.string().optional(),
  voiceId: z.string().optional(),
  model: z.string().optional(),
  rate: z.number().min(0.1).max(3.0).optional().default(1.0),
});

// OpenAI TTS schema
const openAITTSSchema = z.object({
  text: z.string().min(1, "Text is required").max(4096, "Text is too long for OpenAI TTS"),
  voice: z.enum(['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer']).default('nova'),
  model: z.enum(['tts-1', 'tts-1-hd']).default('tts-1'),
  speed: z.number().min(0.25).max(4.0).default(1.0),
  moduleId: z.string().optional(),
  lessonId: z.string().optional(),
  moduleTitle: z.string().optional(),
  lessonTitle: z.string().optional(),
});

// Open-source TTS only - no proprietary services

// Get available voices from open-source TTS services
router.get('/tts-models', async (req: Request, res: Response) => {
  try {
    const availableVoices = opensourceTTS.getAvailableVoices();

    // Format voices for frontend
    const voices = [
      ...availableVoices.coqui.map(v => ({
        id: v.id,
        name: v.name,
        language: v.language,
        gender: v.gender,
        style: v.style,
        quality: v.quality,
        description: v.description,
        service: 'coqui_tts'
      })),
      ...availableVoices.kokoro.map(v => ({
        id: v.id,
        name: v.name,
        language: v.language,
        gender: v.gender,
        description: v.description,
        service: 'kokoro_tts'
      }))
    ];

    return res.status(200).json({
      voices,
      total: availableVoices.total,
      services: {
        coqui: availableVoices.coqui.length,
        kokoro: availableVoices.kokoro.length
      }
    });
  } catch (error) {
    console.error('Error fetching TTS models:', error);
    return res.status(500).json({ error: 'Failed to fetch TTS models' });
  }
});

// Text-to-speech endpoint
router.post('/text-to-speech', async (req: Request, res: Response) => {
  try {
    // Validate request
    const validationResult = textToSpeechSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        error: 'Invalid request data', 
        details: validationResult.error.format() 
      });
    }

    const { text, title, voiceId, model, rate } = validationResult.data;

    // Check if user is authenticated
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const user = req.user;

    // Calculate AI credit cost (1 credit per 100 words)
    const wordCount = text.split(/\s+/).length;
    const creditCost = Math.max(1, Math.ceil(wordCount / 100));

    // Check if user has enough credits
    if (user.aiCredits < creditCost) {
      return res.status(403).json({ 
        error: 'Insufficient AI credits',
        requiredCredits: creditCost,
        availableCredits: user.aiCredits
      });
    }

    // Generate unique filename for the audio
    const fileName = `${uuidv4()}.mp3`;
    const outputPath = path.join(uploadsDir, fileName);

    // Use open-source TTS services only
    let success = false;
    let error = null;
    let serviceName = '';

    try {
      // Generate speech using unified open-source TTS service
      const result = await opensourceTTS.generateSpeech(text, {
        voice: voiceId,
        language: 'en',
        speed: rate,
        preferredEngine: 'coqui'
      });

      if (result.status === 'error') {
        throw new Error(result.error || 'TTS generation failed');
      }

      // Save audio to file system
      const audioBuffer = Buffer.from(result.audio_base64!, 'base64');
      fs.writeFileSync(outputPath, audioBuffer);

      success = true;
      serviceName = result.finalEngine;
    } catch (ttsError) {
      console.error('Open-source TTS failed:', ttsError);
      error = ttsError;

      return res.status(500).json({
        error: 'TTS service failed',
        details: `Open-source TTS: ${ttsError}`
      });
    }

    if (!success) {
      return res.status(500).json({ error: 'Failed to generate speech', details: error });
    }

    // Deduct AI credits
    await storage.updateUserAiCredits(user.id, -creditCost);

    // Save TTS record
    const ttsRecord: TTSResponse = {
      userId: user.id,
      text: text,
      fileName: fileName,
      filePath: `/uploads/audio/${fileName}`,
      service: serviceName,
      wordCount: wordCount,
      creditCost: creditCost,
      createdAt: new Date(),
      title: title || `TTS-${new Date().toISOString().slice(0, 10)}`,
      metadata: {
        voiceId,
        model,
        rate
      }
    };

    // Save to database
    const savedRecord = await storage.saveTTSRecord(ttsRecord);

    return res.status(200).json({
      id: savedRecord.id,
      title: savedRecord.title,
      audioUrl: savedRecord.filePath,
      wordCount: savedRecord.wordCount,
      creditCost: savedRecord.creditCost,
      service: savedRecord.service,
      createdAt: savedRecord.createdAt
    });

  } catch (error) {
    console.error('Error in text-to-speech:', error);
    return res.status(500).json({ error: 'Failed to process text-to-speech request' });
  }
});

// Get TTS history for a user
router.get('/tts-history', async (req: Request, res: Response) => {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.id;
    const history = await storage.getTTSHistory(userId);

    return res.status(200).json({ history });
  } catch (error) {
    console.error('Error fetching TTS history:', error);
    return res.status(500).json({ error: 'Failed to fetch TTS history' });
  }
});

// Delete a TTS record
router.delete('/tts/:id', async (req: Request, res: Response) => {
  try {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userId = req.user.id;
    const ttsId = parseInt(req.params.id);

    // Check if the TTS record exists and belongs to the user
    const tts = await storage.getTTSById(ttsId);
    if (!tts) {
      return res.status(404).json({ error: 'TTS record not found' });
    }

    if (tts.userId !== userId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Delete the file
    try {
      const filePath = path.join(process.cwd(), tts.filePath);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (fileError) {
      console.error('Error deleting TTS file:', fileError);
      // Continue with deletion from database even if file deletion fails
    }

    // Delete from database
    await storage.deleteTTSRecord(ttsId);

    return res.status(200).json({ message: 'TTS record deleted successfully' });
  } catch (error) {
    console.error('Error deleting TTS record:', error);
    return res.status(500).json({ error: 'Failed to delete TTS record' });
  }
});

export default router;