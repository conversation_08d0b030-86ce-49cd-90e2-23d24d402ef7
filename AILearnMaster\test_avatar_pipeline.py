#!/usr/bin/env python3
"""
Comprehensive Avatar Generation Pipeline Testing Script
Tests EchoMimic V2 integration with existing Modal GPU services
"""

import requests
import json
import time
import base64
from typing import Dict, List, Any
import sys
from PIL import Image
import io

class AvatarPipelineTester:
    def __init__(self):
        # Modal endpoints (update these after deployment)
        self.base_url = "https://trade-digital--courseai-a100-simple"
        self.endpoints = {
            "health": f"{self.base_url}-health.modal.run",
            "mistral": f"{self.base_url}-api-mistral.modal.run",
            "tts": f"{self.base_url}-api-tts.modal.run",
            "voices": f"{self.base_url}-api-voices.modal.run",
            "slides": f"{self.base_url}-api-slides.modal.run",
            "avatar": f"{self.base_url}-api-avatar.modal.run",
            "course_avatar": f"{self.base_url}-api-course-avatar.modal.run"
        }
        
        self.results = {
            "health": {"passed": 0, "failed": 0, "tests": []},
            "avatar": {"passed": 0, "failed": 0, "tests": []},
            "course_avatar": {"passed": 0, "failed": 0, "tests": []},
            "integration": {"passed": 0, "failed": 0, "tests": []}
        }

    def create_test_image(self) -> str:
        """Create a simple test image and return as base64"""
        try:
            # Create a simple 768x768 test image
            from PIL import Image, ImageDraw
            
            img = Image.new('RGB', (768, 768), color='lightblue')
            draw = ImageDraw.Draw(img)
            
            # Draw a simple face
            # Head circle
            draw.ellipse([200, 150, 568, 518], fill='peachpuff', outline='black', width=3)
            
            # Eyes
            draw.ellipse([280, 250, 320, 290], fill='white', outline='black', width=2)
            draw.ellipse([448, 250, 488, 290], fill='white', outline='black', width=2)
            draw.ellipse([290, 260, 310, 280], fill='black')
            draw.ellipse([458, 260, 478, 280], fill='black')
            
            # Nose
            draw.polygon([(384, 320), (374, 350), (394, 350)], fill='peachpuff', outline='black')
            
            # Mouth
            draw.arc([340, 380, 428, 420], 0, 180, fill='red', width=3)
            
            # Convert to base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            return img_base64
            
        except Exception as e:
            print(f"Error creating test image: {e}")
            return ""

    def create_test_audio(self) -> str:
        """Create a simple test audio and return as base64"""
        try:
            # Create a simple sine wave audio (placeholder)
            import numpy as np
            import wave
            import io
            
            # Generate 3 seconds of sine wave at 440Hz
            sample_rate = 16000
            duration = 3.0
            frequency = 440
            
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            audio_data = np.sin(2 * np.pi * frequency * t) * 0.3
            
            # Convert to 16-bit PCM
            audio_data = (audio_data * 32767).astype(np.int16)
            
            # Create WAV file in memory
            buffer = io.BytesIO()
            with wave.open(buffer, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())
            
            audio_base64 = base64.b64encode(buffer.getvalue()).decode()
            return audio_base64
            
        except Exception as e:
            print(f"Error creating test audio: {e}")
            return ""

    def test_health_endpoint(self):
        """Test GPU health check"""
        print("🏥 Testing Health Endpoint...")
        try:
            response = requests.get(self.endpoints["health"], timeout=30)
            data = response.json()
            
            if response.status_code == 200 and data.get("status") == "healthy":
                gpu_info = data.get("gpu_info", {})
                print(f"  ✅ Health check passed")
                print(f"  📊 GPU Available: {gpu_info.get('gpu_available', False)}")
                print(f"  🔧 GPU Name: {gpu_info.get('gpu_name', 'Unknown')}")
                print(f"  🎯 Services: {data.get('services', [])}")
                
                self.results["health"]["passed"] += 1
                self.results["health"]["tests"].append({
                    "name": "Health Check",
                    "status": "PASS",
                    "gpu_info": gpu_info
                })
                return True
            else:
                print(f"  ❌ Health check failed: {data}")
                self.results["health"]["failed"] += 1
                return False
                
        except Exception as e:
            print(f"  ❌ Health endpoint error: {e}")
            self.results["health"]["failed"] += 1
            return False

    def test_avatar_generation(self):
        """Test direct avatar generation endpoint"""
        print("\n🎭 Testing Avatar Generation Service...")
        
        # Create test data
        test_image_base64 = self.create_test_image()
        test_audio_base64 = self.create_test_audio()
        
        if not test_image_base64 or not test_audio_base64:
            print("  ❌ Failed to create test data")
            self.results["avatar"]["failed"] += 1
            return False
        
        test_configs = [
            {
                "name": "Quick Test",
                "config": {
                    "width": 512,
                    "height": 512,
                    "fps": 12,
                    "max_frames": 36,  # 3 seconds at 12fps
                    "steps": 4,
                    "cfg": 1.0
                }
            },
            {
                "name": "Standard Quality",
                "config": {
                    "width": 768,
                    "height": 768,
                    "fps": 24,
                    "max_frames": 72,  # 3 seconds at 24fps
                    "steps": 6,
                    "cfg": 1.0
                }
            }
        ]
        
        for test in test_configs:
            try:
                print(f"  🔄 Testing: {test['name']}")
                
                payload = {
                    "ref_image_base64": test_image_base64,
                    "audio_base64": test_audio_base64,
                    "config": test["config"]
                }
                
                start_time = time.time()
                response = requests.post(
                    self.endpoints["avatar"], 
                    json=payload, 
                    timeout=600  # 10 minutes for avatar generation
                )
                response_time = time.time() - start_time
                
                data = response.json()
                
                if response.status_code == 200 and data.get("success"):
                    video_base64 = data.get("video_base64", "")
                    duration = data.get("duration", 0)
                    frames = data.get("frames", 0)
                    
                    print(f"    ✅ {test['name']} - Success")
                    print(f"    ⏱️ Response time: {response_time:.2f}s")
                    print(f"    🎬 Duration: {duration:.2f}s")
                    print(f"    📽️ Frames: {frames}")
                    print(f"    📦 Video size: {len(video_base64)} bytes (base64)")
                    
                    self.results["avatar"]["passed"] += 1
                    self.results["avatar"]["tests"].append({
                        "name": test["name"],
                        "status": "PASS",
                        "response_time": response_time,
                        "duration": duration,
                        "frames": frames,
                        "video_size": len(video_base64)
                    })
                else:
                    print(f"    ❌ {test['name']} - Failed: {data}")
                    self.results["avatar"]["failed"] += 1
                    
            except Exception as e:
                print(f"    ❌ {test['name']} - Error: {e}")
                self.results["avatar"]["failed"] += 1

    def test_integrated_course_avatar(self):
        """Test integrated course avatar generation pipeline"""
        print("\n🎓 Testing Integrated Course Avatar Pipeline...")
        
        test_image_base64 = self.create_test_image()
        
        if not test_image_base64:
            print("  ❌ Failed to create test image")
            self.results["course_avatar"]["failed"] += 1
            return False
        
        test_prompts = [
            {
                "name": "Short Course Introduction",
                "prompt": "Welcome to our AI course. Today we'll learn about machine learning basics.",
                "config": {
                    "width": 512,
                    "height": 512,
                    "fps": 12,
                    "max_frames": 60,
                    "steps": 4
                }
            }
        ]
        
        for test in test_prompts:
            try:
                print(f"  🔄 Testing: {test['name']}")
                
                payload = {
                    "prompt": test["prompt"],
                    "ref_image_base64": test_image_base64,
                    "voice_id": "tts_models/en/ljspeech/tacotron2-DDC",
                    "config": test["config"]
                }
                
                start_time = time.time()
                response = requests.post(
                    self.endpoints["course_avatar"], 
                    json=payload, 
                    timeout=900  # 15 minutes for full pipeline
                )
                response_time = time.time() - start_time
                
                data = response.json()
                
                if response.status_code == 200 and data.get("success"):
                    generated_text = data.get("generated_text", "")
                    video_base64 = data.get("video_base64", "")
                    video_info = data.get("video_info", {})
                    
                    print(f"    ✅ {test['name']} - Success")
                    print(f"    ⏱️ Total pipeline time: {response_time:.2f}s")
                    print(f"    📝 Generated text: {generated_text[:100]}...")
                    print(f"    🎬 Video duration: {video_info.get('duration', 0):.2f}s")
                    print(f"    📦 Video size: {len(video_base64)} bytes")
                    
                    self.results["course_avatar"]["passed"] += 1
                    self.results["course_avatar"]["tests"].append({
                        "name": test["name"],
                        "status": "PASS",
                        "response_time": response_time,
                        "text_length": len(generated_text),
                        "video_info": video_info
                    })
                else:
                    print(f"    ❌ {test['name']} - Failed: {data}")
                    self.results["course_avatar"]["failed"] += 1
                    
            except Exception as e:
                print(f"    ❌ {test['name']} - Error: {e}")
                self.results["course_avatar"]["failed"] += 1

    def run_all_tests(self):
        """Run comprehensive avatar pipeline test suite"""
        print("🚀 Starting Avatar Generation Pipeline Testing\n")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test all services
        health_ok = self.test_health_endpoint()
        
        if health_ok:
            self.test_avatar_generation()
            self.test_integrated_course_avatar()
        else:
            print("❌ Health check failed - skipping avatar tests")
        
        total_time = time.time() - start_time
        
        # Print results summary
        self.print_results_summary(total_time)

    def print_results_summary(self, total_time):
        """Print comprehensive test results"""
        print("\n" + "=" * 60)
        print("📊 AVATAR PIPELINE TEST RESULTS")
        print("=" * 60)
        
        total_passed = 0
        total_failed = 0
        
        for category, results in self.results.items():
            passed = results["passed"]
            failed = results["failed"]
            total_passed += passed
            total_failed += failed
            
            status_icon = "✅" if failed == 0 else "⚠️" if passed > failed else "❌"
            print(f"{status_icon} {category.upper()}: {passed} passed, {failed} failed")
        
        print("\n" + "-" * 60)
        print(f"🎯 TOTAL: {total_passed} passed, {total_failed} failed")
        print(f"⏱️ Total test time: {total_time:.2f} seconds")
        
        if total_failed == 0:
            print("🎉 ALL AVATAR TESTS PASSED! EchoMimic V2 integration successful.")
        else:
            print(f"⚠️ {total_failed} tests failed. Please review the avatar pipeline.")
        
        print("\n📋 Avatar Endpoints:")
        print(f"  Avatar Generation: {self.endpoints['avatar']}")
        print(f"  Course Avatar: {self.endpoints['course_avatar']}")

if __name__ == "__main__":
    tester = AvatarPipelineTester()
    tester.run_all_tests()
