import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Chrome, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface GoogleOAuthButtonProps {
  onSuccess: (user: any) => void;
  disabled?: boolean;
  className?: string;
}

declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          prompt: () => void;
          renderButton: (element: HTMLElement, config: any) => void;
        };
      };
    };
  }
}

export function GoogleOAuthButton({ onSuccess, disabled = false, className = "" }: GoogleOAuthButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleGoogleLogin = async () => {
    setIsLoading(true);
    
    try {
      // Check if Google Identity Services is loaded
      if (!window.google) {
        // Load Google Identity Services script
        const script = document.createElement('script');
        script.src = 'https://accounts.google.com/gsi/client';
        script.async = true;
        script.defer = true;
        
        await new Promise((resolve, reject) => {
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        });
        
        // Wait a bit for the script to initialize
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      if (!window.google) {
        throw new Error('Google Identity Services failed to load');
      }

      // Initialize Google Sign-In
      window.google.accounts.id.initialize({
        client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
        use_fedcm_for_prompt: false,
        callback: async (response: any) => {
          try {
            // Send the credential to our backend for verification
            const verifyResponse = await fetch('/api/auth/google/verify', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                credential: response.credential,
              }),
            });

            const data = await verifyResponse.json();

            if (!verifyResponse.ok) {
              throw new Error(data.message || 'Google authentication failed');
            }

            toast({
              title: "Success!",
              description: "Successfully signed in with Google",
            });

            onSuccess(data);
          } catch (error: any) {
            console.error('Google OAuth verification error:', error);
            toast({
              title: "Authentication Failed",
              description: error.message || "Failed to authenticate with Google",
              variant: "destructive",
            });
          } finally {
            setIsLoading(false);
          }
        },
        auto_select: false,
        cancel_on_tap_outside: true,
      });

      // Trigger the Google Sign-In prompt
      window.google.accounts.id.prompt();
    } catch (error: any) {
      console.error('Google OAuth initialization error:', error);
      toast({
        title: "Error",
        description: "Failed to initialize Google Sign-In",
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      className={`w-full ${className}`}
      onClick={handleGoogleLogin}
      disabled={disabled || isLoading}
    >
      {isLoading ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <Chrome className="mr-2 h-4 w-4" />
      )}
      Continue with Google
    </Button>
  );
}