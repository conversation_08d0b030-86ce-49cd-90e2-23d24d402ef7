/**
 * Chatterbox TTS Router - A100 GPU powered text-to-speech
 * Integrates with Course AI Platform for high-quality narration
 */

import { Router, Request, Response } from 'express';
import { chatterboxModalTTS } from '../services/chatterboxModalTTS';
import { z } from 'zod';

const router = Router();

// Validation schemas
const generateSpeechSchema = z.object({
  text: z.string().min(1).max(5000),
  voice: z.string().optional(),
  temperature: z.number().min(0).max(1).optional(),
  silenceDuration: z.number().min(0).max(2).optional(),
  format: z.enum(['wav', 'mp3']).optional()
});

const batchNarrationSchema = z.object({
  lessons: z.array(z.object({
    title: z.string(),
    text: z.string().min(1),
    moduleId: z.string().optional(),
    lessonId: z.string().optional()
  })),
  voicePreset: z.string().optional()
});

const fullCourseSchema = z.object({
  courseData: z.object({
    title: z.string(),
    modules: z.array(z.object({
      id: z.string(),
      title: z.string(),
      lessons: z.array(z.object({
        id: z.string(),
        title: z.string(),
        content: z.string().min(1)
      }))
    }))
  }),
  voicePreset: z.string().optional()
});

// Get available voices
router.get('/voices', async (req: Request, res: Response) => {
  try {
    const voices = await chatterboxModalTTS.getAvailableVoices();
    res.json({ voices });
  } catch (error) {
    console.error('Error fetching Chatterbox voices:', error);
    res.status(500).json({ 
      message: 'Failed to fetch available voices',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate single speech audio
router.post('/generate', async (req: Request, res: Response) => {
  try {
    const validatedData = generateSpeechSchema.parse(req.body);
    
    const audioBuffer = await chatterboxModalTTS.generateSpeech(
      validatedData.text,
      {
        voice: validatedData.voice,
        temperature: validatedData.temperature,
        silenceDuration: validatedData.silenceDuration,
        format: validatedData.format
      }
    );

    // Set appropriate headers for audio response
    res.set({
      'Content-Type': 'audio/wav',
      'Content-Length': audioBuffer.length.toString(),
      'Content-Disposition': 'attachment; filename="generated_speech.wav"'
    });

    res.send(audioBuffer);
  } catch (error) {
    console.error('Error generating speech:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Invalid request data',
        errors: error.errors
      });
    }

    res.status(500).json({
      message: 'Failed to generate speech',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate batch narration for multiple lessons
router.post('/batch-narration', async (req: Request, res: Response) => {
  try {
    const validatedData = batchNarrationSchema.parse(req.body);
    
    const narrations = await chatterboxModalTTS.generateCourseNarrationBatch(
      validatedData.lessons,
      validatedData.voicePreset
    );

    res.json({
      success: true,
      totalLessons: narrations.length,
      totalDuration: narrations.reduce((sum, n) => sum + n.durationSeconds, 0),
      narrations
    });
  } catch (error) {
    console.error('Error generating batch narration:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Invalid request data',
        errors: error.errors
      });
    }

    res.status(500).json({
      message: 'Failed to generate batch narration',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate full course narration
router.post('/full-course', async (req: Request, res: Response) => {
  try {
    const validatedData = fullCourseSchema.parse(req.body);
    
    const courseNarration = await chatterboxModalTTS.generateFullCourseNarration(
      validatedData.courseData,
      validatedData.voicePreset
    );

    res.json({
      success: true,
      courseTitle: courseNarration.courseTitle,
      totalDuration: courseNarration.totalDuration,
      totalModules: courseNarration.modules.length,
      totalLessons: courseNarration.modules.reduce((sum, m) => sum + m.lessons.length, 0),
      modules: courseNarration.modules
    });
  } catch (error) {
    console.error('Error generating full course narration:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Invalid request data',
        errors: error.errors
      });
    }

    res.status(500).json({
      message: 'Failed to generate full course narration',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Voice cloning from sample
router.post('/clone-voice', async (req: Request, res: Response) => {
  try {
    const { sampleAudioPath, targetText } = req.body;
    
    if (!sampleAudioPath || !targetText) {
      return res.status(400).json({
        message: 'Sample audio path and target text are required'
      });
    }

    const audioBuffer = await chatterboxModalTTS.cloneVoiceFromSample(
      sampleAudioPath,
      targetText
    );

    res.set({
      'Content-Type': 'audio/wav',
      'Content-Length': audioBuffer.length.toString(),
      'Content-Disposition': 'attachment; filename="cloned_voice.wav"'
    });

    res.send(audioBuffer);
  } catch (error) {
    console.error('Error cloning voice:', error);
    res.status(500).json({
      message: 'Failed to clone voice',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check for Chatterbox TTS service
router.get('/health', async (req: Request, res: Response) => {
  try {
    // Simple health check - try to get voices
    const voices = await chatterboxModalTTS.getAvailableVoices();
    
    res.json({
      status: 'healthy',
      service: 'Chatterbox TTS Modal A100',
      availableVoices: voices.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      service: 'Chatterbox TTS Modal A100',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;