#!/usr/bin/env python3
"""
Basic Modal A100 GPU Testing Script
Tests core functionality of deployed Modal services
"""

import requests
import json
import time

# Modal endpoints
BASE_URL = "https://trade-digital--courseai-a100-simple"
ENDPOINTS = {
    "health": f"{BASE_URL}-health.modal.run",
    "mistral": f"{BASE_URL}-api-mistral.modal.run",
    "tts": f"{BASE_URL}-api-tts.modal.run",
    "voices": f"{BASE_URL}-api-voices.modal.run",
    "slides": f"{BASE_URL}-api-slides.modal.run"
}

def test_health():
    """Test health endpoint"""
    print("🔍 Testing Health Endpoint...")
    try:
        response = requests.get(ENDPOINTS["health"], timeout=30)
        if response.status_code == 200:
            data = response.json()
            print("✅ Health endpoint working")
            print(f"   Status: {data.get('status', 'unknown')}")
            print(f"   GPU Available: {data.get('gpu_available', False)}")
            print(f"   GPU Name: {data.get('gpu_name', 'Unknown')}")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_mistral():
    """Test Mistral LLM endpoint"""
    print("\n🔍 Testing Mistral LLM...")
    try:
        payload = {
            "prompt": "Hello, this is a test. Please respond with 'Test successful'.",
            "max_tokens": 20,
            "temperature": 0.1
        }
        response = requests.post(ENDPOINTS["mistral"], json=payload, timeout=60)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print("✅ Mistral LLM working")
                print(f"   Generated: {data.get('generated_text', '')[:100]}...")
                return True
            else:
                print(f"❌ Mistral error: {data.get('error', 'Unknown')}")
                return False
        else:
            print(f"❌ Mistral failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Mistral error: {e}")
        return False

def test_tts():
    """Test TTS endpoint"""
    print("\n🔍 Testing TTS...")
    try:
        payload = {
            "text": "Hello, this is a test."
        }
        response = requests.post(ENDPOINTS["tts"], json=payload, timeout=60)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print("✅ TTS working")
                audio_data = data.get('audio_base64', '')
                print(f"   Audio generated: {len(audio_data)} characters")
                return True
            else:
                print(f"❌ TTS error: {data.get('error', 'Unknown')}")
                return False
        else:
            print(f"❌ TTS failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ TTS error: {e}")
        return False

def test_voices():
    """Test voice discovery endpoint"""
    print("\n🔍 Testing Voice Discovery...")
    try:
        response = requests.get(ENDPOINTS["voices"], timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                voices = data.get('voices', [])
                print(f"✅ Voice discovery working")
                print(f"   Found {len(voices)} voices")
                return True
            else:
                print(f"❌ Voice discovery error: {data.get('error', 'Unknown')}")
                return False
        else:
            print(f"❌ Voice discovery failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Voice discovery error: {e}")
        return False

def test_slides():
    """Test slide generation endpoint"""
    print("\n🔍 Testing Slide Generation...")
    try:
        payload = {
            "markdown": "# Test Slide\n\nThis is a test slide."
        }
        response = requests.post(ENDPOINTS["slides"], json=payload, timeout=60)
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print("✅ Slide generation working")
                slides_data = data.get('slides_base64', '')
                print(f"   Slides generated: {len(slides_data)} characters")
                return True
            else:
                print(f"❌ Slides error: {data.get('error', 'Unknown')}")
                return False
        else:
            print(f"❌ Slides failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Slides error: {e}")
        return False

def main():
    """Run basic tests"""
    print("🚀 Modal A100 GPU Basic Testing")
    print("=" * 50)
    
    start_time = time.time()
    
    # Test all endpoints
    tests = [
        ("Health Check", test_health),
        ("Mistral LLM", test_mistral),
        ("TTS", test_tts),
        ("Voice Discovery", test_voices),
        ("Slide Generation", test_slides)
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    working = sum(1 for result in results.values() if result)
    total = len(results)
    
    print(f"Working Services: {working}/{total}")
    print(f"Success Rate: {working/total*100:.1f}%")
    print(f"Test Duration: {time.time() - start_time:.1f} seconds")
    
    print("\nDetailed Results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
    
    if working == total:
        print("\n🎉 All tests passed! Modal A100 deployment is working correctly.")
    elif working > 0:
        print(f"\n⚠️  Partial success: {working}/{total} services working.")
    else:
        print("\n❌ All tests failed. Please check Modal deployment.")

if __name__ == "__main__":
    main()
