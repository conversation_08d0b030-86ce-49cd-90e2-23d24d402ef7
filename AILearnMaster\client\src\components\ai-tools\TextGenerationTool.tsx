import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Loader2 } from 'lucide-react';
import { Slider } from '@/components/ui/slider';

interface TextGenerationToolProps {
  onTextGenerated?: (text: string) => void;
  defaultPrompt?: string;
  defaultModel?: 'mistral' | 'mixtral';
  heading?: string;
  description?: string;
}

export function TextGenerationTool({
  onTextGenerated,
  defaultPrompt = '',
  defaultModel = 'mixtral',
  heading = 'Text Generation',
  description = 'Generate text using Mistral or Mixtral open-source models running on RunPod H100.'
}: TextGenerationToolProps) {
  const { toast } = useToast();
  const [prompt, setPrompt] = useState(defaultPrompt);
  const [model, setModel] = useState<'mistral' | 'mixtral'>(defaultModel);
  const [temperature, setTemperature] = useState(0.7);
  const [maxTokens, setMaxTokens] = useState(1000);
  const [generating, setGenerating] = useState(false);
  const [generatedText, setGeneratedText] = useState('');
  
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: 'Prompt is required',
        description: 'Please enter a prompt for text generation',
        variant: 'destructive',
      });
      return;
    }
    
    setGenerating(true);
    setGeneratedText('');
    
    try {
      const response = await apiRequest(
        'POST', 
        '/api/ai-tools/text-generation', 
        {
          prompt,
          model,
          temperature,
          maxTokens
        }
      );
      
      const data = await response.json();
      
      if (data.text) {
        setGeneratedText(data.text);
        if (onTextGenerated) {
          onTextGenerated(data.text);
        }
      } else {
        toast({
          title: 'Generation failed',
          description: data.message || 'Failed to generate text',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Text generation error:', error);
      toast({
        title: 'Error',
        description: 'Failed to connect to the text generation service',
        variant: 'destructive',
      });
    } finally {
      setGenerating(false);
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{heading}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Textarea
            placeholder="Enter your prompt here..."
            className="min-h-[100px]"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            disabled={generating}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm font-medium">Model</div>
            <Select 
              value={model} 
              onValueChange={(value: 'mistral' | 'mixtral') => setModel(value)}
              disabled={generating}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="mistral">Mistral 7B (Faster)</SelectItem>
                <SelectItem value="mixtral">Mixtral 8x7B (Higher Quality)</SelectItem>
              </SelectContent>
            </Select>
            <div className="text-xs text-muted-foreground">
              Mixtral is generally higher quality but Mistral is faster
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm font-medium">Temperature: {temperature.toFixed(1)}</div>
            <Slider
              defaultValue={[temperature]}
              min={0.1}
              max={2.0}
              step={0.1}
              onValueChange={(value) => setTemperature(value[0])}
              disabled={generating}
            />
            <div className="text-xs text-muted-foreground">
              Lower values are more focused, higher values more creative
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm font-medium">Max tokens: {maxTokens}</div>
            <Slider
              defaultValue={[maxTokens]}
              min={100}
              max={4000}
              step={100}
              onValueChange={(value) => setMaxTokens(value[0])}
              disabled={generating}
            />
            <div className="text-xs text-muted-foreground">
              Maximum length of generated text (1000 tokens ≈ 750 words)
            </div>
          </div>
        </div>
        
        <Button
          className="w-full"
          disabled={generating || !prompt.trim()}
          onClick={handleGenerate}
        >
          {generating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            'Generate Text'
          )}
        </Button>
        
        {generatedText && (
          <div className="pt-4">
            <div className="text-sm font-medium mb-2">Generated Text</div>
            <div className="p-4 bg-secondary rounded-md whitespace-pre-wrap">
              {generatedText}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {generatedText && (
          <Button 
            variant="outline" 
            onClick={() => {
              navigator.clipboard.writeText(generatedText);
              toast({
                title: 'Copied',
                description: 'Generated text copied to clipboard',
              });
            }}
          >
            Copy to Clipboard
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}

export default TextGenerationTool;