import { generateAsync } from 'stability-client';
import { fetch as oFetch } from 'cross-fetch';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { config } from 'dotenv';

config();

interface GenerationOptions {
  prompt: string;
  negativePrompt?: string;
  width?: number;
  height?: number;
  steps?: number;
  cfgScale?: number;
  seed?: number;
  stylePreset?: string;
  engineId?: string;
}

interface GenerationResponse {
  imageUrl: string;
  fileName: string;
  prompt: string;
  width: number;
  height: number;
  seed?: number;
  stylePreset?: string;
}

class StabilityAIService {
  private apiKey: string;
  private apiHost: string;

  constructor() {
    const apiKey = process.env.STABILITY_API_KEY;
    
    if (!apiKey) {
      throw new Error('STABILITY_API_KEY environment variable is required');
    }
    
    this.apiKey = apiKey;
    this.apiHost = 'https://api.stability.ai';
  }

  /**
   * Generate an image using Stable Diffusion via Stability AI's API
   */
  async generateImage(options: GenerationOptions): Promise<GenerationResponse> {
    const {
      prompt, 
      negativePrompt = "", 
      width = 1024, 
      height = 1024, 
      steps = 30,
      cfgScale = 7,
      seed = Math.floor(Math.random() * 1000000),
      stylePreset = "photographic",
      engineId = "stable-diffusion-xl-1024-v1-0"
    } = options;

    const response = await fetch(
      `${this.apiHost}/v1/generation/${engineId}/text-to-image`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          text_prompts: [
            {
              text: prompt,
              weight: 1
            },
            {
              text: negativePrompt,
              weight: -1
            }
          ],
          cfg_scale: cfgScale,
          height,
          width,
          steps,
          samples: 1,
          seed,
          style_preset: stylePreset
        }),
      }
    );

    if (!response.ok) {
      let message = `Stability API error: ${response.status} ${response.statusText}`;
      try {
        const error = await response.json();
        message = error.message || message;
      } catch (e) {
        // If we can't parse the error, just use the default message
      }
      throw new Error(message);
    }

    const responseJSON = await response.json();
    
    // Save the image to a file
    const artifacts = responseJSON.artifacts;
    if (!artifacts || artifacts.length === 0) {
      throw new Error('No artifacts returned from Stability API');
    }
    
    const artifact = artifacts[0];
    const fileName = `stability-${Date.now()}-${uuidv4()}.png`;
    const filePath = path.join(process.cwd(), 'uploads', fileName);
    
    // Ensure uploads directory exists
    if (!fs.existsSync(path.join(process.cwd(), 'uploads'))) {
      fs.mkdirSync(path.join(process.cwd(), 'uploads'), { recursive: true });
    }
    
    // Write the base64 image to a file
    fs.writeFileSync(filePath, Buffer.from(artifact.base64, 'base64'));
    
    // Generate URL for the saved image
    const imageUrl = `/api/uploads/${fileName}`;
    
    return {
      imageUrl,
      fileName,
      prompt,
      width,
      height,
      seed: artifact.seed,
      stylePreset
    };
  }

  /**
   * Get available style presets
   */
  async getStylePresets(): Promise<string[]> {
    return [
      "photographic",
      "digital-art",
      "cinematic",
      "anime",
      "3d-model",
      "pixel-art",
      "oil-painting",
      "comic-book",
      "fantasy-art",
      "line-art",
      "analog-film",
      "neon-punk",
      "isometric",
      "low-poly",
      "origami",
      "watercolor"
    ];
  }
}

export const stabilityService = new StabilityAIService();