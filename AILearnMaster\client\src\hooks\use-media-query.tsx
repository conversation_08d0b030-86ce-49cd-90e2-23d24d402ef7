import { useState, useEffect } from 'react';

type MediaQueryProps = {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isHoverSupported: boolean;
};

/**
 * Custom hook that provides responsive design boolean values
 * @returns Object with boolean flags for different device types and capabilities
 */
export function useMediaQuery(): MediaQueryProps {
  // Default to desktop view when rendering on the server
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDesktop, setIsDesktop] = useState(true);
  const [isHoverSupported, setIsHoverSupported] = useState(true);

  useEffect(() => {
    // Function to check media queries
    const checkMedia = () => {
      const mobileQuery = window.matchMedia('(max-width: 640px)');
      const tabletQuery = window.matchMedia('(min-width: 641px) and (max-width: 1024px)');
      const desktopQuery = window.matchMedia('(min-width: 1025px)');
      const hoverQuery = window.matchMedia('(hover: hover)');

      setIsMobile(mobileQuery.matches);
      setIsTablet(tabletQuery.matches);
      setIsDesktop(desktopQuery.matches);
      setIsHoverSupported(hoverQuery.matches);
    };

    // Initial check
    checkMedia();

    // Set up listeners for each media query
    const mobileQuery = window.matchMedia('(max-width: 640px)');
    const tabletQuery = window.matchMedia('(min-width: 641px) and (max-width: 1024px)');
    const desktopQuery = window.matchMedia('(min-width: 1025px)');
    const hoverQuery = window.matchMedia('(hover: hover)');

    // Modern browsers use addEventListener
    if (mobileQuery.addEventListener) {
      mobileQuery.addEventListener('change', checkMedia);
      tabletQuery.addEventListener('change', checkMedia);
      desktopQuery.addEventListener('change', checkMedia);
      hoverQuery.addEventListener('change', checkMedia);
    } else {
      // Fallback for older browsers
      mobileQuery.addListener(checkMedia);
      tabletQuery.addListener(checkMedia);
      desktopQuery.addListener(checkMedia);
      hoverQuery.addListener(checkMedia);
    }

    // Clean up event listeners
    return () => {
      if (mobileQuery.removeEventListener) {
        mobileQuery.removeEventListener('change', checkMedia);
        tabletQuery.removeEventListener('change', checkMedia);
        desktopQuery.removeEventListener('change', checkMedia);
        hoverQuery.removeEventListener('change', checkMedia);
      } else {
        // Fallback for older browsers
        mobileQuery.removeListener(checkMedia);
        tabletQuery.removeListener(checkMedia);
        desktopQuery.removeListener(checkMedia);
        hoverQuery.removeListener(checkMedia);
      }
    };
  }, []);

  return { isMobile, isTablet, isDesktop, isHoverSupported };
}