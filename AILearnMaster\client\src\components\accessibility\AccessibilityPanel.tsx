import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetDes<PERSON> } from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { useAccessibility } from './AccessibilityProvider';
import { Accessibility, Eye, Volume2, Type } from 'lucide-react';

// Sample voice options (replace with actual voices from API)
const SAMPLE_VOICES = [
  { id: 'default', name: 'Default System Voice', language: 'en-US' },
  { id: 'voice1', name: '<PERSON>', language: 'en-US' },
  { id: 'voice2', name: '<PERSON>', language: 'en-US' },
  { id: 'voice3', name: '<PERSON>', language: 'es-ES' },
  { id: 'voice4', name: '<PERSON><PERSON>', language: 'de-DE' },
];

export function AccessibilityPanel() {
  const {
    highContrast,
    largeText,
    reducedMotion,
    screenReader,
    autoNarrate,
    narrationSpeed,
    preferredVoice,
    toggleHighContrast,
    toggleLargeText,
    toggleReducedMotion,
    toggleScreenReader,
    toggleAutoNarrate,
    setNarrationSpeed,
    setPreferredVoice
  } = useAccessibility();

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" aria-label="Accessibility Options">
          <Accessibility className="h-4 w-4" />
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[350px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>Accessibility Settings</SheetTitle>
          <SheetDescription>
            Customize your experience to make the platform more accessible.
          </SheetDescription>
        </SheetHeader>
        
        <Tabs defaultValue="general" className="mt-6">
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="general">
              <Eye className="mr-2 h-4 w-4" />
              General
            </TabsTrigger>
            <TabsTrigger value="text">
              <Type className="mr-2 h-4 w-4" />
              Text
            </TabsTrigger>
            <TabsTrigger value="voice">
              <Volume2 className="mr-2 h-4 w-4" />
              Voice
            </TabsTrigger>
          </TabsList>
          
          {/* General Accessibility Settings */}
          <TabsContent value="general" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="high-contrast">High Contrast Mode</Label>
                <div className="text-sm text-muted-foreground">
                  Enhances color contrast for better visibility
                </div>
              </div>
              <Switch
                id="high-contrast"
                checked={highContrast}
                onCheckedChange={toggleHighContrast}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="reduced-motion">Reduced Motion</Label>
                <div className="text-sm text-muted-foreground">
                  Minimizes animations and transitions
                </div>
              </div>
              <Switch
                id="reduced-motion"
                checked={reducedMotion}
                onCheckedChange={toggleReducedMotion}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="screen-reader">Screen Reader Optimization</Label>
                <div className="text-sm text-muted-foreground">
                  Improves compatibility with screen readers
                </div>
              </div>
              <Switch
                id="screen-reader"
                checked={screenReader}
                onCheckedChange={toggleScreenReader}
              />
            </div>
          </TabsContent>
          
          {/* Text Settings */}
          <TabsContent value="text" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="large-text">Larger Text</Label>
                <div className="text-sm text-muted-foreground">
                  Increases text size throughout the application
                </div>
              </div>
              <Switch
                id="large-text"
                checked={largeText}
                onCheckedChange={toggleLargeText}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="font-size">Text Size Adjustment</Label>
              <div className="flex items-center space-x-2">
                <span className="text-sm">A</span>
                <Slider
                  id="font-size"
                  defaultValue={[1]}
                  min={0.8}
                  max={1.5}
                  step={0.1}
                  className="flex-1"
                />
                <span className="text-lg font-bold">A</span>
              </div>
              <div className="text-sm text-muted-foreground">
                Adjust the base font size for the entire application
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="line-spacing">Line Spacing</Label>
              <Slider
                id="line-spacing"
                defaultValue={[1]}
                min={1}
                max={2}
                step={0.1}
                className="w-full"
              />
              <div className="text-sm text-muted-foreground">
                Adjust the spacing between lines of text
              </div>
            </div>
          </TabsContent>
          
          {/* Voice Settings */}
          <TabsContent value="voice" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="auto-narrate">Auto-Narrate Content</Label>
                <div className="text-sm text-muted-foreground">
                  Automatically read page content aloud
                </div>
              </div>
              <Switch
                id="auto-narrate"
                checked={autoNarrate}
                onCheckedChange={toggleAutoNarrate}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="voice-select">Preferred Voice</Label>
              <Select 
                value={preferredVoice || 'default'} 
                onValueChange={(value) => setPreferredVoice(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a voice" />
                </SelectTrigger>
                <SelectContent>
                  {SAMPLE_VOICES.map(voice => (
                    <SelectItem key={voice.id} value={voice.id}>
                      {voice.name} ({voice.language})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="text-sm text-muted-foreground">
                Choose your preferred voice for narration
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="speech-rate">Speech Rate</Label>
              <div className="flex items-center space-x-2">
                <span className="text-sm">Slow</span>
                <Slider
                  id="speech-rate"
                  value={[narrationSpeed]}
                  min={0.5}
                  max={2}
                  step={0.1}
                  onValueChange={(value) => setNarrationSpeed(value[0])}
                  className="flex-1"
                />
                <span className="text-sm">Fast</span>
              </div>
              <div className="text-sm text-center text-muted-foreground">
                Current: {narrationSpeed.toFixed(1)}x
              </div>
            </div>
            
            <div className="mt-4">
              <Button className="w-full" onClick={() => {
                // Play test narration
                const utterance = new SpeechSynthesisUtterance(
                  "This is a test of the voice narration feature. You can adjust the speech rate and choose your preferred voice."
                );
                utterance.rate = narrationSpeed;
                // In a real implementation, you would use the actual selected voice
                window.speechSynthesis.speak(utterance);
              }}>
                Test Voice Narration
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </SheetContent>
    </Sheet>
  );
}

export default AccessibilityPanel;