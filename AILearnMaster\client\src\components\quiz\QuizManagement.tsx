import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Play, 
  Users, 
  CheckCircle2, 
  Clock, 
  Target,
  Brain,
  BookOpen
} from 'lucide-react';
import QuizCreator from './QuizCreator';

interface Quiz {
  id?: string;
  title: string;
  description: string;
  moduleId?: string;
  lessonId?: string;
  questions: any[];
  settings: {
    timeLimit?: number;
    passingScore: number;
    randomizeQuestions: boolean;
    showCorrectAnswers: boolean;
    allowRetakes: boolean;
  };
  stats?: {
    totalAttempts: number;
    averageScore: number;
    completionRate: number;
  };
}

interface QuizManagementProps {
  courseId: string;
  modules: Array<{
    id: string;
    title: string;
    lessons: Array<{
      id: string;
      title: string;
    }>;
  }>;
}

export default function QuizManagement({ courseId, modules }: QuizManagementProps) {
  const { toast } = useToast();
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [showCreator, setShowCreator] = useState(false);
  const [editingQuiz, setEditingQuiz] = useState<Quiz | null>(null);

  const handleCreateQuiz = () => {
    setEditingQuiz(null);
    setShowCreator(true);
  };

  const handleEditQuiz = (quiz: Quiz) => {
    setEditingQuiz(quiz);
    setShowCreator(true);
  };

  const handleSaveQuiz = (quiz: Quiz) => {
    if (editingQuiz) {
      // Update existing quiz
      setQuizzes(prev => prev.map(q => q.id === editingQuiz.id ? { ...quiz, id: editingQuiz.id } : q));
      toast({
        title: "Quiz Updated",
        description: "Your quiz has been successfully updated.",
      });
    } else {
      // Create new quiz
      const newQuiz = { ...quiz, id: `quiz-${Date.now()}` };
      setQuizzes(prev => [...prev, newQuiz]);
      toast({
        title: "Quiz Created",
        description: "Your new quiz has been created successfully.",
      });
    }
    setShowCreator(false);
    setEditingQuiz(null);
  };

  const handleDeleteQuiz = (quizId: string) => {
    setQuizzes(prev => prev.filter(q => q.id !== quizId));
    toast({
      title: "Quiz Deleted",
      description: "The quiz has been permanently deleted.",
    });
  };

  const getModuleTitle = (moduleId?: string) => {
    const module = modules.find(m => m.id === moduleId);
    return module?.title || 'General';
  };

  const getLessonTitle = (moduleId?: string, lessonId?: string) => {
    const module = modules.find(m => m.id === moduleId);
    const lesson = module?.lessons.find(l => l.id === lessonId);
    return lesson?.title || '';
  };

  if (showCreator) {
    return (
      <QuizCreator
        courseId={courseId}
        moduleId={editingQuiz?.moduleId}
        lessonId={editingQuiz?.lessonId}
        onSave={handleSaveQuiz}
        onCancel={() => {
          setShowCreator(false);
          setEditingQuiz(null);
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Quiz Management</h2>
          <p className="text-muted-foreground">
            Create and manage quizzes for your course modules and lessons
          </p>
        </div>
        <Button onClick={handleCreateQuiz}>
          <Plus className="h-4 w-4 mr-2" />
          Create Quiz
        </Button>
      </div>

      {/* Quiz Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium">Total Quizzes</span>
            </div>
            <p className="text-2xl font-bold mt-2">{quizzes.length}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium">Total Questions</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {quizzes.reduce((sum, quiz) => sum + quiz.questions.length, 0)}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-purple-500" />
              <span className="text-sm font-medium">Total Attempts</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {quizzes.reduce((sum, quiz) => sum + (quiz.stats?.totalAttempts || 0), 0)}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-orange-500" />
              <span className="text-sm font-medium">Avg. Score</span>
            </div>
            <p className="text-2xl font-bold mt-2">
              {quizzes.length > 0 
                ? Math.round(quizzes.reduce((sum, quiz) => sum + (quiz.stats?.averageScore || 0), 0) / quizzes.length)
                : 0}%
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quizzes List */}
      {quizzes.length === 0 ? (
        <Card>
          <CardContent className="p-8">
            <div className="text-center">
              <Brain className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No quizzes created yet</h3>
              <p className="text-muted-foreground mb-6">
                Start by creating your first quiz to test student knowledge
              </p>
              <Button onClick={handleCreateQuiz}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Quiz
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {quizzes.map((quiz) => (
            <Card key={quiz.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{quiz.title}</CardTitle>
                    <CardDescription className="mt-1">
                      {quiz.description || 'No description provided'}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditQuiz(quiz)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => quiz.id && handleDeleteQuiz(quiz.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Module/Lesson Info */}
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline">
                    {getModuleTitle(quiz.moduleId)}
                  </Badge>
                  {quiz.lessonId && (
                    <Badge variant="secondary">
                      {getLessonTitle(quiz.moduleId, quiz.lessonId)}
                    </Badge>
                  )}
                </div>

                {/* Quiz Stats */}
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="font-semibold">{quiz.questions.length}</div>
                    <div className="text-muted-foreground">Questions</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">{quiz.settings.passingScore}%</div>
                    <div className="text-muted-foreground">Pass Score</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold">
                      {quiz.settings.timeLimit ? `${quiz.settings.timeLimit}m` : 'No limit'}
                    </div>
                    <div className="text-muted-foreground">Time Limit</div>
                  </div>
                </div>

                {/* Quiz Settings */}
                <div className="flex flex-wrap gap-2">
                  {quiz.settings.randomizeQuestions && (
                    <Badge variant="outline" className="text-xs">
                      Randomized
                    </Badge>
                  )}
                  {quiz.settings.showCorrectAnswers && (
                    <Badge variant="outline" className="text-xs">
                      Show Answers
                    </Badge>
                  )}
                  {quiz.settings.allowRetakes && (
                    <Badge variant="outline" className="text-xs">
                      Allow Retakes
                    </Badge>
                  )}
                </div>

                {/* Performance Stats */}
                {quiz.stats && (
                  <div className="border-t pt-4">
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="font-semibold">{quiz.stats.totalAttempts}</div>
                        <div className="text-muted-foreground">Attempts</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold">{quiz.stats.averageScore}%</div>
                        <div className="text-muted-foreground">Avg Score</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold">{quiz.stats.completionRate}%</div>
                        <div className="text-muted-foreground">Completion</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Play className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Users className="h-4 w-4 mr-2" />
                    Results
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}