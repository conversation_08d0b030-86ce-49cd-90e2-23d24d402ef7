import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useToast } from '@/hooks/use-toast';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { User } from '@/types';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Youtube, Upload, BookOpen, Presentation, Sliders, Settings2, Save, Share2 } from 'lucide-react';

interface CourseManagementSettingsProps {
  user: User | undefined;
}

// Form validation schema for default course settings
const courseSettingsSchema = z.object({
  defaultCategory: z.string().min(1, "Please select a category"),
  defaultTimeLimit: z.number().min(0).max(300),
  defaultVisibility: z.enum(["public", "private", "unlisted"]),
  microLearningEnabled: z.boolean().default(false),
  segmentCount: z.number().min(2).max(10),
  breakInterval: z.number().min(1).max(30),
  breakDuration: z.number().min(10).max(300),
  templateId: z.string().optional(),
});

// Form validation schema for media settings
const mediaSettingsSchema = z.object({
  defaultResolution: z.enum(["720p", "1080p", "1440p", "2160p"]),
  defaultAspectRatio: z.enum(["16:9", "4:3", "1:1", "9:16"]),
  defaultFrameRate: z.number().min(24).max(60),
  compressionQuality: z.number().min(1).max(10),
  audioQuality: z.enum(["low", "medium", "high"]),
  defaultVoiceId: z.string().optional(),
  enableAutoCaptions: z.boolean().default(true),
});

// Form validation schema for publishing settings
const publishingSettingsSchema = z.object({
  youtubeEnabled: z.boolean().default(false),
  youtubePrivacy: z.enum(["public", "unlisted", "private"]),
  udemyEnabled: z.boolean().default(false),
  udemyAutoDraft: z.boolean().default(true),
  kajabiEnabled: z.boolean().default(false),
  teachableEnabled: z.boolean().default(false),
  enableAutoPublish: z.boolean().default(false),
  publishScheduleEnabled: z.boolean().default(false),
});

type CourseSettingsValues = z.infer<typeof courseSettingsSchema>;
type MediaSettingsValues = z.infer<typeof mediaSettingsSchema>;
type PublishingSettingsValues = z.infer<typeof publishingSettingsSchema>;

export default function CourseManagementSettings({ user }: CourseManagementSettingsProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("course");

  // Default course settings form
  const courseSettingsForm = useForm<CourseSettingsValues>({
    resolver: zodResolver(courseSettingsSchema),
    defaultValues: {
      defaultCategory: "business",
      defaultTimeLimit: 60,
      defaultVisibility: "public",
      microLearningEnabled: true,
      segmentCount: 3,
      breakInterval: 10,
      breakDuration: 60,
      templateId: "none",
    },
  });

  // Media settings form
  const mediaSettingsForm = useForm<MediaSettingsValues>({
    resolver: zodResolver(mediaSettingsSchema),
    defaultValues: {
      defaultResolution: "1080p",
      defaultAspectRatio: "16:9",
      defaultFrameRate: 30,
      compressionQuality: 8,
      audioQuality: "high",
      defaultVoiceId: "none",
      enableAutoCaptions: true,
    },
  });

  // Publishing settings form
  const publishingSettingsForm = useForm<PublishingSettingsValues>({
    resolver: zodResolver(publishingSettingsSchema),
    defaultValues: {
      youtubeEnabled: false,
      youtubePrivacy: "unlisted",
      udemyEnabled: false,
      udemyAutoDraft: true,
      kajabiEnabled: false,
      teachableEnabled: false,
      enableAutoPublish: false,
      publishScheduleEnabled: false,
    },
  });

  // Save course settings mutation
  const saveCourseSettingsMutation = useMutation({
    mutationFn: async (data: CourseSettingsValues) => {
      const response = await apiRequest('POST', '/api/settings/course', data);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Course settings saved',
        description: 'Your course settings have been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error saving settings',
        description: error.message || 'Something went wrong. Please try again.',
      });
    }
  });

  // Save media settings mutation
  const saveMediaSettingsMutation = useMutation({
    mutationFn: async (data: MediaSettingsValues) => {
      const response = await apiRequest('POST', '/api/settings/media', data);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Media settings saved',
        description: 'Your media settings have been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error saving settings',
        description: error.message || 'Something went wrong. Please try again.',
      });
    }
  });

  // Save publishing settings mutation
  const savePublishingSettingsMutation = useMutation({
    mutationFn: async (data: PublishingSettingsValues) => {
      const response = await apiRequest('POST', '/api/settings/publishing', data);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Publishing settings saved',
        description: 'Your publishing settings have been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        variant: 'destructive',
        title: 'Error saving settings',
        description: error.message || 'Something went wrong. Please try again.',
      });
    }
  });

  // Handle course settings submission
  const onCourseSettingsSubmit = (data: CourseSettingsValues) => {
    saveCourseSettingsMutation.mutate(data);
  };

  // Handle media settings submission
  const onMediaSettingsSubmit = (data: MediaSettingsValues) => {
    saveMediaSettingsMutation.mutate(data);
  };

  // Handle publishing settings submission
  const onPublishingSettingsSubmit = (data: PublishingSettingsValues) => {
    savePublishingSettingsMutation.mutate(data);
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid grid-cols-3 w-full">
          <TabsTrigger value="course" className="flex items-center">
            <BookOpen className="h-4 w-4 mr-2" />
            <span>Course Defaults</span>
          </TabsTrigger>
          <TabsTrigger value="media" className="flex items-center">
            <Presentation className="h-4 w-4 mr-2" />
            <span>Media Creation</span>
          </TabsTrigger>
          <TabsTrigger value="publishing" className="flex items-center">
            <Share2 className="h-4 w-4 mr-2" />
            <span>Publishing</span>
          </TabsTrigger>
        </TabsList>

        {/* Course Default Settings */}
        <TabsContent value="course">
          <Card>
            <CardHeader>
              <CardTitle>Course Default Settings</CardTitle>
              <CardDescription>
                Configure default settings for your new courses
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...courseSettingsForm}>
                <form onSubmit={courseSettingsForm.handleSubmit(onCourseSettingsSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={courseSettingsForm.control}
                      name="defaultCategory"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Category</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectGroup>
                                <SelectLabel>Categories</SelectLabel>
                                <SelectItem value="business">Business</SelectItem>
                                <SelectItem value="technology">Technology</SelectItem>
                                <SelectItem value="design">Design</SelectItem>
                                <SelectItem value="marketing">Marketing</SelectItem>
                                <SelectItem value="personal-development">Personal Development</SelectItem>
                                <SelectItem value="health">Health & Wellness</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default category for your courses
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={courseSettingsForm.control}
                      name="defaultVisibility"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Visibility</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select visibility" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="public">Public</SelectItem>
                              <SelectItem value="private">Private</SelectItem>
                              <SelectItem value="unlisted">Unlisted</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default visibility for new courses
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={courseSettingsForm.control}
                    name="defaultTimeLimit"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Time Limit (minutes)</FormLabel>
                        <div className="flex items-center gap-4">
                          <FormControl>
                            <Slider
                              min={0}
                              max={300}
                              step={5}
                              value={[field.value]}
                              onValueChange={(value) => field.onChange(value[0])}
                              className="flex-1"
                            />
                          </FormControl>
                          <span className="w-12 text-center">{field.value}</span>
                        </div>
                        <FormDescription>
                          Default time limit for course completion (0 = unlimited)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Separator />

                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Micro-Learning Settings</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Configure micro-learning defaults for better retention and engagement
                      </p>
                    </div>

                    <FormField
                      control={courseSettingsForm.control}
                      name="microLearningEnabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Enable Micro-Learning By Default</FormLabel>
                            <FormDescription>
                              Automatically enable micro-learning for new courses
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {courseSettingsForm.watch("microLearningEnabled") && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-4">
                        <FormField
                          control={courseSettingsForm.control}
                          name="segmentCount"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Segments Per Lesson</FormLabel>
                              <div className="flex items-center gap-4">
                                <FormControl>
                                  <Slider
                                    min={2}
                                    max={10}
                                    step={1}
                                    value={[field.value]}
                                    onValueChange={(value) => field.onChange(value[0])}
                                    className="flex-1"
                                  />
                                </FormControl>
                                <span className="w-6 text-center">{field.value}</span>
                              </div>
                              <FormDescription>
                                Number of learning segments
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={courseSettingsForm.control}
                          name="breakInterval"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Break Interval (minutes)</FormLabel>
                              <div className="flex items-center gap-4">
                                <FormControl>
                                  <Slider
                                    min={1}
                                    max={30}
                                    step={1}
                                    value={[field.value]}
                                    onValueChange={(value) => field.onChange(value[0])}
                                    className="flex-1"
                                  />
                                </FormControl>
                                <span className="w-6 text-center">{field.value}</span>
                              </div>
                              <FormDescription>
                                Minutes between breaks
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={courseSettingsForm.control}
                          name="breakDuration"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Break Duration (seconds)</FormLabel>
                              <div className="flex items-center gap-4">
                                <FormControl>
                                  <Slider
                                    min={10}
                                    max={300}
                                    step={5}
                                    value={[field.value]}
                                    onValueChange={(value) => field.onChange(value[0])}
                                    className="flex-1"
                                  />
                                </FormControl>
                                <span className="w-8 text-center">{field.value}</span>
                              </div>
                              <FormDescription>
                                Duration of each break
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>

                  <Separator />

                  <FormField
                    control={courseSettingsForm.control}
                    name="templateId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Template</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a template" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="business-course">Business Course</SelectItem>
                            <SelectItem value="technical-tutorial">Technical Tutorial</SelectItem>
                            <SelectItem value="creative-workshop">Creative Workshop</SelectItem>
                            <SelectItem value="certification-prep">Certification Prep</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Default template for new courses (optional)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={saveCourseSettingsMutation.isPending}
                  >
                    {saveCourseSettingsMutation.isPending ? (
                      <span className="flex items-center">
                        <Settings2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Save className="mr-2 h-4 w-4" />
                        Save Course Settings
                      </span>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Media Creation Settings */}
        <TabsContent value="media">
          <Card>
            <CardHeader>
              <CardTitle>Media Creation Settings</CardTitle>
              <CardDescription>
                Configure default settings for video and audio creation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...mediaSettingsForm}>
                <form onSubmit={mediaSettingsForm.handleSubmit(onMediaSettingsSubmit)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={mediaSettingsForm.control}
                      name="defaultResolution"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Video Resolution</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select resolution" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="720p">720p HD</SelectItem>
                              <SelectItem value="1080p">1080p Full HD</SelectItem>
                              <SelectItem value="1440p">1440p Quad HD</SelectItem>
                              <SelectItem value="2160p">2160p 4K UHD</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default resolution for video content
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={mediaSettingsForm.control}
                      name="defaultAspectRatio"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Aspect Ratio</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select aspect ratio" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="16:9">16:9 (Landscape)</SelectItem>
                              <SelectItem value="4:3">4:3 (Standard)</SelectItem>
                              <SelectItem value="1:1">1:1 (Square)</SelectItem>
                              <SelectItem value="9:16">9:16 (Vertical)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default aspect ratio for videos
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={mediaSettingsForm.control}
                      name="defaultFrameRate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Frame Rate (FPS)</FormLabel>
                          <div className="flex items-center gap-4">
                            <FormControl>
                              <Slider
                                min={24}
                                max={60}
                                step={1}
                                value={[field.value]}
                                onValueChange={(value) => field.onChange(value[0])}
                                className="flex-1"
                              />
                            </FormControl>
                            <span className="w-8 text-center">{field.value}</span>
                          </div>
                          <FormDescription>
                            Default frames per second
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={mediaSettingsForm.control}
                      name="compressionQuality"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Compression Quality</FormLabel>
                          <div className="flex items-center gap-4">
                            <FormControl>
                              <Slider
                                min={1}
                                max={10}
                                step={1}
                                value={[field.value]}
                                onValueChange={(value) => field.onChange(value[0])}
                                className="flex-1"
                              />
                            </FormControl>
                            <span className="w-8 text-center">{field.value}</span>
                          </div>
                          <FormDescription>
                            Video compression quality (1-10)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Audio Settings</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Configure default audio settings for your courses
                      </p>
                    </div>

                    <FormField
                      control={mediaSettingsForm.control}
                      name="audioQuality"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Audio Quality</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select audio quality" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="low">Low (96kbps)</SelectItem>
                              <SelectItem value="medium">Medium (128kbps)</SelectItem>
                              <SelectItem value="high">High (256kbps)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default audio quality for course recordings
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={mediaSettingsForm.control}
                      name="defaultVoiceId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default AI Voice</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a voice" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="none">None (Use my recordings)</SelectItem>
                              <SelectItem value="adam">Adam (Male)</SelectItem>
                              <SelectItem value="emily">Emily (Female)</SelectItem>
                              <SelectItem value="daniel">Daniel (Male)</SelectItem>
                              <SelectItem value="sophia">Sophia (Female)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Default AI voice for text-to-speech (requires ElevenLabs)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={mediaSettingsForm.control}
                      name="enableAutoCaptions"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Auto-generate Captions</FormLabel>
                            <FormDescription>
                              Automatically generate captions for all videos
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={saveMediaSettingsMutation.isPending}
                  >
                    {saveMediaSettingsMutation.isPending ? (
                      <span className="flex items-center">
                        <Settings2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Save className="mr-2 h-4 w-4" />
                        Save Media Settings
                      </span>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Publishing Settings */}
        <TabsContent value="publishing">
          <Card>
            <CardHeader>
              <CardTitle>Publishing Settings</CardTitle>
              <CardDescription>
                Configure settings for publishing courses to external platforms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...publishingSettingsForm}>
                <form onSubmit={publishingSettingsForm.handleSubmit(onPublishingSettingsSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <div className="rounded-md border p-4">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="h-10 w-10 rounded-md bg-red-100 flex items-center justify-center">
                          <Youtube className="h-5 w-5 text-red-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium">YouTube Publishing</h3>
                          <p className="text-sm text-muted-foreground">
                            Configure your YouTube publishing preferences
                          </p>
                        </div>
                      </div>

                      <div className="space-y-4 pl-4">
                        <FormField
                          control={publishingSettingsForm.control}
                          name="youtubeEnabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                              <div className="space-y-0.5">
                                <FormLabel>Enable YouTube Publishing</FormLabel>
                                <FormDescription>
                                  Publish your courses to YouTube automatically
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        {publishingSettingsForm.watch("youtubeEnabled") && (
                          <FormField
                            control={publishingSettingsForm.control}
                            name="youtubePrivacy"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Default Privacy Setting</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select privacy setting" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="public">Public</SelectItem>
                                    <SelectItem value="unlisted">Unlisted</SelectItem>
                                    <SelectItem value="private">Private</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormDescription>
                                  Default privacy setting for YouTube videos
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )}
                      </div>
                    </div>

                    <div className="rounded-md border p-4">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="h-10 w-10 rounded-md bg-purple-100 flex items-center justify-center">
                          <BookOpen className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium">Udemy Publishing</h3>
                          <p className="text-sm text-muted-foreground">
                            Configure your Udemy publishing preferences
                          </p>
                        </div>
                      </div>

                      <div className="space-y-4 pl-4">
                        <FormField
                          control={publishingSettingsForm.control}
                          name="udemyEnabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                              <div className="space-y-0.5">
                                <FormLabel>Enable Udemy Publishing</FormLabel>
                                <FormDescription>
                                  Publish your courses to Udemy automatically
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        {publishingSettingsForm.watch("udemyEnabled") && (
                          <FormField
                            control={publishingSettingsForm.control}
                            name="udemyAutoDraft"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                                <div className="space-y-0.5">
                                  <FormLabel>Create as Draft</FormLabel>
                                  <FormDescription>
                                    Create courses as drafts on Udemy (recommended)
                                  </FormDescription>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="rounded-md border p-4">
                        <div className="flex items-center gap-3 mb-4">
                          <div className="h-10 w-10 rounded-md bg-teal-100 flex items-center justify-center">
                            <Upload className="h-5 w-5 text-teal-600" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium">Kajabi</h3>
                            <p className="text-sm text-muted-foreground">
                              Enable Kajabi publishing
                            </p>
                          </div>
                        </div>

                        <FormField
                          control={publishingSettingsForm.control}
                          name="kajabiEnabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                              <FormLabel>Enable Kajabi Publishing</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="rounded-md border p-4">
                        <div className="flex items-center gap-3 mb-4">
                          <div className="h-10 w-10 rounded-md bg-blue-100 flex items-center justify-center">
                            <Upload className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium">Teachable</h3>
                            <p className="text-sm text-muted-foreground">
                              Enable Teachable publishing
                            </p>
                          </div>
                        </div>

                        <FormField
                          control={publishingSettingsForm.control}
                          name="teachableEnabled"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                              <FormLabel>Enable Teachable Publishing</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-lg font-medium mb-2">General Publishing Settings</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Configure general publishing preferences for all platforms
                      </p>
                    </div>

                    <FormField
                      control={publishingSettingsForm.control}
                      name="enableAutoPublish"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Enable Auto-Publishing</FormLabel>
                            <FormDescription>
                              Automatically publish courses when they're completed
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={publishingSettingsForm.control}
                      name="publishScheduleEnabled"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Enable Schedule Publishing</FormLabel>
                            <FormDescription>
                              Allow scheduling publication of courses
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full"
                    disabled={savePublishingSettingsMutation.isPending}
                  >
                    {savePublishingSettingsMutation.isPending ? (
                      <span className="flex items-center">
                        <Settings2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </span>
                    ) : (
                      <span className="flex items-center">
                        <Save className="mr-2 h-4 w-4" />
                        Save Publishing Settings
                      </span>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}