import {
  require_jsx_runtime
} from "./chunk-KBTYAULA.js";
import {
  require_react
} from "./chunk-QCHXOAYK.js";
import {
  __toESM
} from "./chunk-WOOG5QLI.js";

// node_modules/@jitsi/react-sdk/lib/components/JitsiMeeting.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());

// node_modules/@jitsi/react-sdk/lib/constants/index.js
var DEFAULT_DOMAIN = "meet.jit.si";
var JAAS_STAGING_DOMAIN = "stage.8x8.vc";
var JAAS_PROD_DOMAIN = "8x8.vc";

// node_modules/@jitsi/react-sdk/lib/init.js
var __awaiter = function(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
};
var loadExternalApi = (domain, release, appId) => __awaiter(void 0, void 0, void 0, function* () {
  return new Promise((resolve, reject) => {
    if (window.JitsiMeetExternalAPI) {
      return resolve(window.JitsiMeetExternalAPI);
    }
    const script = document.createElement("script");
    const releaseParam = release ? `?release=${release}` : "";
    const appIdPath = appId ? `${appId}/` : "";
    script.async = true;
    script.src = `https://${domain}/${appIdPath}external_api.js${releaseParam}`;
    script.onload = () => resolve(window.JitsiMeetExternalAPI);
    script.onerror = () => reject(new Error(`Script load error: ${script.src}`));
    document.head.appendChild(script);
  });
});
var scriptPromise;
var fetchExternalApi = (domain = DEFAULT_DOMAIN, release, appId) => {
  if (scriptPromise) {
    return scriptPromise;
  }
  scriptPromise = loadExternalApi(domain, release, appId);
  return scriptPromise;
};

// node_modules/@jitsi/react-sdk/lib/utils/index.js
var getRoomName = (roomName, tenant) => {
  if (tenant) {
    return `${tenant}/${roomName}`;
  }
  return roomName;
};
var getAppId = (roomName) => {
  const roomParts = roomName.split("/");
  if (roomParts.length <= 1) {
    return;
  }
  return roomParts[0];
};
var getJaaSDomain = (useStaging) => {
  if (useStaging) {
    return JAAS_STAGING_DOMAIN;
  }
  return JAAS_PROD_DOMAIN;
};
var instancesCounter = 0;
var generateComponentId = (prefix) => `${prefix}-${instancesCounter++}`;

// node_modules/@jitsi/react-sdk/lib/components/JitsiMeeting.js
var JitsiMeeting = ({ domain = DEFAULT_DOMAIN, roomName, configOverwrite, interfaceConfigOverwrite, jwt, invitees, devices, userInfo, release, lang, spinner: Spinner, onApiReady, onReadyToClose, getIFrameRef }) => {
  const [loading, setLoading] = (0, import_react.useState)(true);
  const [apiLoaded, setApiLoaded] = (0, import_react.useState)(false);
  const externalApi = (0, import_react.useRef)();
  const apiRef = (0, import_react.useRef)();
  const meetingRef = (0, import_react.useRef)(null);
  const componentId = (0, import_react.useMemo)(() => generateComponentId("jitsiMeeting"), [generateComponentId]);
  (0, import_react.useEffect)(() => {
    fetchExternalApi(domain, release, getAppId(roomName)).then((api) => {
      externalApi.current = api;
      setApiLoaded(true);
    }).catch((e) => console.error(e.message));
  }, []);
  const loadIFrame = (0, import_react.useCallback)((JitsiMeetExternalAPI) => {
    apiRef.current = new JitsiMeetExternalAPI(domain, {
      roomName,
      configOverwrite,
      interfaceConfigOverwrite,
      jwt,
      invitees,
      devices,
      userInfo,
      release,
      lang,
      parentNode: meetingRef.current
    });
    setLoading(false);
    if (apiRef.current) {
      typeof onApiReady === "function" && onApiReady(apiRef.current);
      apiRef.current.on("readyToClose", () => {
        typeof onReadyToClose === "function" && onReadyToClose();
      });
      if (meetingRef.current && typeof getIFrameRef === "function") {
        getIFrameRef(meetingRef.current);
      }
    }
  }, [
    apiRef,
    meetingRef,
    onApiReady,
    onReadyToClose,
    getIFrameRef,
    domain,
    roomName,
    configOverwrite,
    interfaceConfigOverwrite,
    jwt,
    invitees,
    devices,
    userInfo,
    release,
    lang
  ]);
  (0, import_react.useEffect)(() => {
    if (apiLoaded && !apiRef.current) {
      if (externalApi.current) {
        loadIFrame(externalApi.current);
      }
    }
  }, [apiLoaded, loadIFrame]);
  const renderLoadingSpinner = (0, import_react.useCallback)(() => {
    if (!Spinner) {
      return null;
    }
    if (!loading || apiRef.current) {
      return null;
    }
    return (0, import_jsx_runtime.jsx)(Spinner, {}, void 0);
  }, [Spinner, apiRef.current]);
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [renderLoadingSpinner(), (0, import_jsx_runtime.jsx)("div", { id: componentId, ref: meetingRef }, componentId)] }, void 0);
};
var JitsiMeeting_default = JitsiMeeting;

// node_modules/@jitsi/react-sdk/lib/components/JaaSMeeting.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
    t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function")
    for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
      if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
        t[p[i]] = s[p[i]];
    }
  return t;
};
var JaaSMeeting = (_a) => {
  var { appId, roomName, useStaging, release } = _a, rest = __rest(_a, ["appId", "roomName", "useStaging", "release"]);
  return (0, import_jsx_runtime2.jsx)(JitsiMeeting_default, Object.assign({ domain: getJaaSDomain(useStaging), roomName: getRoomName(roomName, appId), release }, rest), void 0);
};
var JaaSMeeting_default = JaaSMeeting;
export {
  JaaSMeeting_default as JaaSMeeting,
  JitsiMeeting_default as JitsiMeeting
};
//# sourceMappingURL=@jitsi_react-sdk.js.map
