import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd";
import { 
  GripVertical, 
  Plus, 
  Trash2, 
  Edit, 
  RotateCw, 
  Sparkles, 
  Book, 
  FileText, 
  Film, 
  BrainCircuit, 
  ArrowRight,
  ArrowLeft
} from "lucide-react";
import { v4 as uuidv4 } from 'uuid';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription, 
  DialogFooter 
} from "@/components/ui/dialog";

// Define types for course structure
export interface Lesson {
  id: string;
  title: string;
  type: 'video' | 'text' | 'quiz';
  content?: string;
  videoUrl?: string;
  duration?: number;
}

export interface Section {
  id: string;
  title: string;
  lessons: Lesson[];
}

export interface CourseStructureData {
  sections: Section[];
}

interface CourseSectionsBuilderProps {
  onSubmit: (data: CourseStructureData) => void;
  onBack?: () => void;
  defaultValues?: CourseStructureData;
  useAI?: boolean;
}

export function CourseSectionsBuilder({ 
  onSubmit,
  onBack, 
  defaultValues = { sections: [] },
  useAI = true
}: CourseSectionsBuilderProps) {
  const [courseStructure, setCourseStructure] = useState<CourseStructureData>(defaultValues);
  const [isGenerating, setIsGenerating] = useState(false);
  const [editingSectionId, setEditingSectionId] = useState<string | null>(null);
  const [editingLessonId, setEditingLessonId] = useState<string | null>(null);
  const [newSectionTitle, setNewSectionTitle] = useState("");
  const { toast } = useToast();

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const { source, destination, type } = result;

    // If dropped in a different position
    if (source.droppableId !== destination.droppableId || source.index !== destination.index) {
      const newStructure = { ...courseStructure };

      // Handle section drag
      if (type === 'SECTION') {
        const sections = Array.from(newStructure.sections);
        const [movedSection] = sections.splice(source.index, 1);
        sections.splice(destination.index, 0, movedSection);
        newStructure.sections = sections;
      } 
      // Handle lesson drag
      else if (type === 'LESSON') {
        const sourceSectionIndex = parseInt(source.droppableId.split('-')[1]);
        const destSectionIndex = parseInt(destination.droppableId.split('-')[1]);
        
        // If moving within the same section
        if (sourceSectionIndex === destSectionIndex) {
          const lessons = Array.from(newStructure.sections[sourceSectionIndex].lessons);
          const [movedLesson] = lessons.splice(source.index, 1);
          lessons.splice(destination.index, 0, movedLesson);
          newStructure.sections[sourceSectionIndex].lessons = lessons;
        } 
        // If moving between different sections
        else {
          const sourceSection = newStructure.sections[sourceSectionIndex];
          const destSection = newStructure.sections[destSectionIndex];
          const [movedLesson] = sourceSection.lessons.splice(source.index, 1);
          destSection.lessons.splice(destination.index, 0, movedLesson);
        }
      }

      setCourseStructure(newStructure);
    }
  };

  // Add a new section
  const addSection = () => {
    if (!newSectionTitle.trim()) {
      toast({
        title: "Section title required",
        description: "Please enter a title for the new section",
        variant: "destructive",
      });
      return;
    }

    const newSection: Section = {
      id: uuidv4(),
      title: newSectionTitle,
      lessons: []
    };

    setCourseStructure(prev => ({
      sections: [...prev.sections, newSection]
    }));

    setNewSectionTitle("");
  };

  // Delete a section
  const deleteSection = (sectionId: string) => {
    setCourseStructure(prev => ({
      sections: prev.sections.filter(section => section.id !== sectionId)
    }));
  };

  // Edit a section title
  const editSection = (sectionId: string) => {
    setEditingSectionId(sectionId);
  };

  // Update a section title
  const updateSectionTitle = (sectionId: string, newTitle: string) => {
    if (!newTitle.trim()) {
      toast({
        title: "Section title required",
        description: "Section title cannot be empty",
        variant: "destructive",
      });
      return;
    }

    setCourseStructure(prev => ({
      sections: prev.sections.map(section => 
        section.id === sectionId ? { ...section, title: newTitle } : section
      )
    }));

    setEditingSectionId(null);
  };

  // Add a lesson to a section
  const [isAddLessonDialogOpen, setIsAddLessonDialogOpen] = useState(false);
  const [activeSectionForLesson, setActiveSectionForLesson] = useState<string | null>(null);
  const [newLesson, setNewLesson] = useState<Omit<Lesson, 'id'>>({
    title: '',
    type: 'video'
  });

  const openAddLessonDialog = (sectionId: string) => {
    setActiveSectionForLesson(sectionId);
    setNewLesson({
      title: '',
      type: 'video'
    });
    setIsAddLessonDialogOpen(true);
  };

  const addLesson = () => {
    if (!activeSectionForLesson) return;
    
    if (!newLesson.title.trim()) {
      toast({
        title: "Lesson title required",
        description: "Please enter a title for the new lesson",
        variant: "destructive",
      });
      return;
    }

    const lessonToAdd: Lesson = {
      id: uuidv4(),
      ...newLesson
    };

    setCourseStructure(prev => ({
      sections: prev.sections.map(section => 
        section.id === activeSectionForLesson 
          ? { ...section, lessons: [...section.lessons, lessonToAdd] } 
          : section
      )
    }));

    setIsAddLessonDialogOpen(false);
    setActiveSectionForLesson(null);
  };

  // Delete a lesson
  const deleteLesson = (sectionId: string, lessonId: string) => {
    setCourseStructure(prev => ({
      sections: prev.sections.map(section => 
        section.id === sectionId 
          ? { ...section, lessons: section.lessons.filter(lesson => lesson.id !== lessonId) } 
          : section
      )
    }));
  };

  // Edit a lesson
  const [isEditLessonDialogOpen, setIsEditLessonDialogOpen] = useState(false);
  const [lessonToEdit, setLessonToEdit] = useState<Lesson | null>(null);
  const [activeSectionForEditing, setActiveSectionForEditing] = useState<string | null>(null);

  const openEditLessonDialog = (sectionId: string, lesson: Lesson) => {
    setActiveSectionForEditing(sectionId);
    setLessonToEdit(lesson);
    setIsEditLessonDialogOpen(true);
  };

  const updateLesson = () => {
    if (!activeSectionForEditing || !lessonToEdit) return;
    
    if (!lessonToEdit.title.trim()) {
      toast({
        title: "Lesson title required",
        description: "Lesson title cannot be empty",
        variant: "destructive",
      });
      return;
    }

    setCourseStructure(prev => ({
      sections: prev.sections.map(section => 
        section.id === activeSectionForEditing 
          ? { 
              ...section, 
              lessons: section.lessons.map(lesson => 
                lesson.id === lessonToEdit.id 
                  ? lessonToEdit 
                  : lesson
              ) 
            } 
          : section
      )
    }));

    setIsEditLessonDialogOpen(false);
    setLessonToEdit(null);
    setActiveSectionForEditing(null);
  };

  // AI structure generation
  const { mutate: generateAIStructure } = useMutation({
    mutationFn: async () => {
      setIsGenerating(true);
      // Get course details from parent component if available
      const response = await apiRequest('POST', '/api/ai/generate-course-structure', {
        courseTitle: 'New Course',
        description: 'A comprehensive course on this subject',
        category: 'Education',
        level: 'beginner'
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate course structure');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      try {
        // Handle different response formats - some endpoints return different structures
        const modules = data.modules || data.sections || [];
        
        // Convert AI generated structure to our format with proper error handling
        const generatedSections: Section[] = modules.map((module: any) => ({
          id: uuidv4(),
          title: module.title || 'Untitled Section',
          lessons: Array.isArray(module.lessons) ? module.lessons.map((lesson: any) => ({
            id: uuidv4(),
            title: lesson.title || 'Untitled Lesson',
            type: lesson.type && ['video', 'text', 'quiz'].includes(lesson.type) ? lesson.type : 'video',
            content: lesson.content || '',
            duration: lesson.duration || 0
          })) : []
        }));
        
        setCourseStructure({ sections: generatedSections });
        
        toast({
          title: "Structure generated",
          description: "AI-generated structure has been created. Feel free to modify it as needed.",
        });
      } catch (err) {
        console.error("Error parsing AI response:", err);
        toast({
          title: "Error processing response",
          description: "The AI response format was unexpected. Please try creating sections manually.",
          variant: "destructive",
        });
      }
      setIsGenerating(false);
    },
    onError: (error) => {
      console.error("AI generation error:", error);
      setIsGenerating(false);
      toast({
        title: "Generation failed",
        description: "Failed to generate course structure. Please try again or create it manually.",
        variant: "destructive",
      });
    }
  });

  const handleSubmit = () => {
    // Validate before submission
    if (courseStructure.sections.length === 0) {
      toast({
        title: "No sections added",
        description: "Please add at least one section to your course",
        variant: "destructive",
      });
      return;
    }

    for (const section of courseStructure.sections) {
      if (section.lessons.length === 0) {
        toast({
          title: "Empty section",
          description: `Section "${section.title}" has no lessons. Please add at least one lesson to each section.`,
          variant: "destructive",
        });
        return;
      }
    }

    onSubmit(courseStructure);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Course Structure</h2>
        {useAI && (
          <Button 
            variant="outline" 
            onClick={() => generateAIStructure()} 
            disabled={isGenerating}
            className="gap-2"
          >
            {isGenerating ? (
              <>
                <RotateCw className="h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 text-primary" />
                Generate with AI
              </>
            )}
          </Button>
        )}
      </div>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="sections" type="SECTION">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-4"
            >
              {courseStructure.sections.length === 0 ? (
                <Card className="border-dashed">
                  <CardContent className="flex flex-col items-center justify-center py-10 text-center text-muted-foreground">
                    <Book className="h-10 w-10 mb-3 text-primary/70" />
                    <p className="text-lg font-medium">No sections yet</p>
                    <p className="text-sm max-w-md">
                      Add your first section or generate a structure with AI to get started
                    </p>
                  </CardContent>
                </Card>
              ) : (
                courseStructure.sections.map((section, index) => (
                  <Draggable
                    key={section.id}
                    draggableId={section.id}
                    index={index}
                  >
                    {(provided) => (
                      <Card
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className="border"
                      >
                        <CardHeader className="p-4 pb-0">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div
                                {...provided.dragHandleProps}
                                className="cursor-move p-1 rounded-sm hover:bg-muted"
                              >
                                <GripVertical className="h-5 w-5 text-muted-foreground" />
                              </div>

                              {editingSectionId === section.id ? (
                                <div className="flex gap-2 flex-grow">
                                  <Input
                                    defaultValue={section.title}
                                    className="h-9 max-w-sm"
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        updateSectionTitle(section.id, e.currentTarget.value);
                                      }
                                    }}
                                    onBlur={(e) => updateSectionTitle(section.id, e.target.value)}
                                    autoFocus
                                  />
                                </div>
                              ) : (
                                <CardTitle className="text-lg">{section.title}</CardTitle>
                              )}
                            </div>

                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => editSection(section.id)}
                                className="h-8 w-8"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => deleteSection(section.id)}
                                className="h-8 w-8 text-destructive"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>

                        <CardContent className="p-4">
                          <Droppable
                            droppableId={`section-${index}`}
                            type="LESSON"
                          >
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                                className="space-y-2 min-h-[50px]"
                              >
                                {section.lessons.length === 0 ? (
                                  <div className="text-center text-muted-foreground py-4 border border-dashed rounded-md">
                                    <p className="text-sm">No lessons yet</p>
                                  </div>
                                ) : (
                                  section.lessons.map((lesson, lessonIndex) => (
                                    <Draggable
                                      key={lesson.id}
                                      draggableId={lesson.id}
                                      index={lessonIndex}
                                    >
                                      {(provided) => (
                                        <div
                                          ref={provided.innerRef}
                                          {...provided.draggableProps}
                                          {...provided.dragHandleProps}
                                          className={cn(
                                            "flex items-center justify-between p-2 rounded-md border border-border bg-background",
                                            lesson.type === 'video' && "border-l-4 border-l-purple-500",
                                            lesson.type === 'text' && "border-l-4 border-l-blue-500",
                                            lesson.type === 'quiz' && "border-l-4 border-l-orange-500"
                                          )}
                                        >
                                          <div className="flex items-center gap-3">
                                            <div className="flex-shrink-0">
                                              {lesson.type === 'video' && <Film className="h-4 w-4 text-purple-500" />}
                                              {lesson.type === 'text' && <FileText className="h-4 w-4 text-blue-500" />}
                                              {lesson.type === 'quiz' && <BrainCircuit className="h-4 w-4 text-orange-500" />}
                                            </div>
                                            <span className="font-medium">{lesson.title}</span>
                                          </div>
                                          <div className="flex gap-1">
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              onClick={() => openEditLessonDialog(section.id, lesson)}
                                              className="h-7 w-7"
                                            >
                                              <Edit className="h-3.5 w-3.5" />
                                            </Button>
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              onClick={() => deleteLesson(section.id, lesson.id)}
                                              className="h-7 w-7 text-destructive"
                                            >
                                              <Trash2 className="h-3.5 w-3.5" />
                                            </Button>
                                          </div>
                                        </div>
                                      )}
                                    </Draggable>
                                  ))
                                )}
                                {provided.placeholder}

                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => openAddLessonDialog(section.id)}
                                  className="w-full mt-2 border border-dashed text-muted-foreground hover:text-foreground"
                                >
                                  <Plus className="h-4 w-4 mr-2" />
                                  Add Lesson
                                </Button>
                              </div>
                            )}
                          </Droppable>
                        </CardContent>
                      </Card>
                    )}
                  </Draggable>
                ))
              )}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Add section input */}
      <div className="mt-8 border-t pt-6">
        <Label htmlFor="newSectionTitle">Add New Section</Label>
        <div className="flex gap-2 mt-2">
          <Input
            id="newSectionTitle"
            placeholder="Enter section title"
            value={newSectionTitle}
            onChange={(e) => setNewSectionTitle(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                addSection();
              }
            }}
          />
          <Button type="button" onClick={addSection}>
            <Plus className="h-4 w-4 mr-2" />
            Add
          </Button>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="mt-8 border-t pt-6 flex justify-between">
        {onBack ? (
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        ) : (
          <div></div> /* Empty div to maintain flex layout */
        )}
        <Button 
          type="button" 
          onClick={handleSubmit}
          className="gap-2"
        >
          Continue
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Add lesson dialog */}
      <Dialog open={isAddLessonDialogOpen} onOpenChange={setIsAddLessonDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Lesson</DialogTitle>
            <DialogDescription>
              Create a new lesson for your course
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-2">
            <div>
              <Label htmlFor="lesson-title">Lesson Title</Label>
              <Input
                id="lesson-title"
                placeholder="Enter title"
                value={newLesson.title}
                onChange={(e) => setNewLesson(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="lesson-type">Lesson Type</Label>
              <Select 
                defaultValue={newLesson.type}
                onValueChange={(value) => setNewLesson(prev => ({ ...prev, type: value as 'video' | 'text' | 'quiz' }))}
              >
                <SelectTrigger id="lesson-type">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="video">
                    <div className="flex items-center">
                      <Film className="mr-2 h-4 w-4 text-purple-500" />
                      <span>Video</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="text">
                    <div className="flex items-center">
                      <FileText className="mr-2 h-4 w-4 text-blue-500" />
                      <span>Text</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="quiz">
                    <div className="flex items-center">
                      <BrainCircuit className="mr-2 h-4 w-4 text-orange-500" />
                      <span>Quiz</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddLessonDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={addLesson}>Add Lesson</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit lesson dialog */}
      <Dialog open={isEditLessonDialogOpen} onOpenChange={setIsEditLessonDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Lesson</DialogTitle>
            <DialogDescription>
              Update lesson details
            </DialogDescription>
          </DialogHeader>
          {lessonToEdit && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-title">Title</Label>
                <Input
                  id="edit-title"
                  value={lessonToEdit.title}
                  onChange={(e) => setLessonToEdit({...lessonToEdit, title: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">Type</Label>
                <Select
                  value={lessonToEdit.type}
                  onValueChange={(value) => setLessonToEdit({...lessonToEdit, type: value as 'video' | 'text' | 'quiz'})}
                >
                  <SelectTrigger id="edit-type">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="video">
                      <div className="flex items-center">
                        <Film className="mr-2 h-4 w-4 text-purple-500" />
                        <span>Video</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="text">
                      <div className="flex items-center">
                        <FileText className="mr-2 h-4 w-4 text-blue-500" />
                        <span>Text</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="quiz">
                      <div className="flex items-center">
                        <BrainCircuit className="mr-2 h-4 w-4 text-orange-500" />
                        <span>Quiz</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditLessonDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={updateLesson}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}