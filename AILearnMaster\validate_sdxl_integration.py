#!/usr/bin/env python3
"""
SDXL Integration Validation Script
Validates that all SDXL integration changes are properly implemented
"""

import os
import re
from typing import List, Dict, Any

def check_file_exists(filepath: str) -> bool:
    """Check if a file exists"""
    return os.path.exists(filepath)

def check_content_in_file(filepath: str, patterns: List[str]) -> Dict[str, bool]:
    """Check if specific patterns exist in a file"""
    results = {}
    if not os.path.exists(filepath):
        return {pattern: False for pattern in patterns}
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    for pattern in patterns:
        results[pattern] = pattern in content
    
    return results

def validate_modal_deployment():
    """Validate modal_a100_simple.py has SDXL integration"""
    print("🔍 Validating Modal Deployment File...")
    
    filepath = "modal_a100_simple.py"
    required_patterns = [
        "# SDXL Image Generation",
        "diffusers==0.24.0",
        "xformers==0.0.22.post7",
        "safetensors==0.4.1",
        "compel==2.0.2",
        "def sdxl_generate_image(",
        "def api_sdxl(",
        "/api_sdxl - SDXL image generation",
        "SDXL image generation",
        "gpu=\"A100-80GB\"",
        "memory=32768"
    ]
    
    if not check_file_exists(filepath):
        print(f"❌ {filepath} not found!")
        return False
    
    results = check_content_in_file(filepath, required_patterns)
    
    all_found = True
    for pattern, found in results.items():
        if found:
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            all_found = False
    
    return all_found

def validate_test_script():
    """Validate test_modal_comprehensive_final.py has SDXL testing"""
    print("\n🔍 Validating Test Script...")
    
    filepath = "test_modal_comprehensive_final.py"
    required_patterns = [
        '"sdxl": f"{BASE_URL}-api-sdxl.modal.run"',
        "def test_sdxl(",
        "Testing SDXL Image Generation",
        "tester.test_sdxl",
        "SDXL image generation working",
        "professional course thumbnail"
    ]
    
    if not check_file_exists(filepath):
        print(f"❌ {filepath} not found!")
        return False
    
    results = check_content_in_file(filepath, required_patterns)
    
    all_found = True
    for pattern, found in results.items():
        if found:
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            all_found = False
    
    return all_found

def validate_simple_test():
    """Validate test_sdxl_simple.py exists"""
    print("\n🔍 Validating Simple Test Script...")
    
    filepath = "test_sdxl_simple.py"
    required_patterns = [
        "def test_sdxl(",
        "api-sdxl.modal.run",
        "A simple test image, digital art"
    ]
    
    if not check_file_exists(filepath):
        print(f"❌ {filepath} not found!")
        return False
    
    results = check_content_in_file(filepath, required_patterns)
    
    all_found = True
    for pattern, found in results.items():
        if found:
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            all_found = False
    
    return all_found

def validate_documentation():
    """Validate SDXL documentation exists"""
    print("\n🔍 Validating Documentation...")
    
    filepath = "SDXL_INTEGRATION_SUMMARY.md"
    required_patterns = [
        "SDXL Integration Summary",
        "100% SUCCESS ACHIEVED",
        "api-sdxl.modal.run",
        "Success Rate: 100.0%",
        "NVIDIA A100 80GB PCIe"
    ]
    
    if not check_file_exists(filepath):
        print(f"❌ {filepath} not found!")
        return False
    
    results = check_content_in_file(filepath, required_patterns)
    
    all_found = True
    for pattern, found in results.items():
        if found:
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            all_found = False
    
    return all_found

def count_endpoints_in_modal():
    """Count the number of endpoints in modal file"""
    print("\n🔍 Counting Modal Endpoints...")
    
    filepath = "modal_a100_simple.py"
    if not check_file_exists(filepath):
        print(f"❌ {filepath} not found!")
        return False
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Count @modal.fastapi_endpoint occurrences
    endpoint_pattern = r'@modal\.fastapi_endpoint'
    endpoints = re.findall(endpoint_pattern, content)
    endpoint_count = len(endpoints)
    
    print(f"   📊 Found {endpoint_count} FastAPI endpoints")
    
    if endpoint_count <= 8:
        print(f"   ✅ Within Modal's 8 endpoint limit")
        return True
    else:
        print(f"   ❌ Exceeds Modal's 8 endpoint limit!")
        return False

def main():
    """Run all validation checks"""
    print("🚀 SDXL Integration Validation")
    print("=" * 50)
    
    checks = [
        ("Modal Deployment", validate_modal_deployment),
        ("Test Script", validate_test_script),
        ("Simple Test", validate_simple_test),
        ("Documentation", validate_documentation),
        ("Endpoint Count", count_endpoints_in_modal)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Error in {check_name}: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    success_rate = (passed / total * 100) if total > 0 else 0
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {check_name}")
    
    print(f"\n📈 Overall Success Rate: {success_rate:.1f}% ({passed}/{total})")
    
    if success_rate == 100:
        print("🎉 ALL VALIDATIONS PASSED - SDXL Integration Complete!")
    else:
        print("⚠️  Some validations failed - Review and fix issues")
    
    return success_rate == 100

if __name__ == "__main__":
    main()
