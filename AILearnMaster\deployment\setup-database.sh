#!/bin/bash

# Database Setup and Migration Script for Production
# Configures PostgreSQL with SSL and runs migrations

set -e

echo "🗄️ Setting up Production Database"
echo "================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check database connection
check_database_connection() {
    print_status "Checking database connection..."
    
    if npm run db:health >/dev/null 2>&1; then
        print_success "Database connection successful"
        return 0
    else
        print_error "Database connection failed"
        return 1
    fi
}

# Function to verify SSL connection
verify_ssl_connection() {
    print_status "Verifying SSL connection..."
    
    # Create a test script to check SSL
    cat > /tmp/check_ssl.js << 'EOF'
const { Pool } = require('pg');

async function checkSSL() {
    const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: { rejectUnauthorized: false }
    });
    
    try {
        const client = await pool.connect();
        const result = await client.query('SELECT ssl_is_used() as ssl_enabled');
        client.release();
        
        if (result.rows[0].ssl_enabled) {
            console.log('✅ SSL connection verified');
            process.exit(0);
        } else {
            console.log('❌ SSL not enabled');
            process.exit(1);
        }
    } catch (error) {
        console.log('❌ SSL verification failed:', error.message);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

checkSSL();
EOF
    
    if node /tmp/check_ssl.js; then
        print_success "SSL connection verified"
        rm -f /tmp/check_ssl.js
        return 0
    else
        print_error "SSL verification failed"
        rm -f /tmp/check_ssl.js
        return 1
    fi
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    if npm run db:migrate >/dev/null 2>&1; then
        print_success "Database migrations completed"
        return 0
    else
        print_error "Database migrations failed"
        return 1
    fi
}

# Function to seed initial data
seed_database() {
    print_status "Seeding initial database data..."
    
    if npm run db:seed >/dev/null 2>&1; then
        print_success "Database seeding completed"
        return 0
    else
        print_warning "Database seeding failed or not configured"
        return 0
    fi
}

# Function to create database backup
create_backup() {
    print_status "Creating database backup..."
    
    local backup_name="ailearn_backup_$(date +%Y%m%d_%H%M%S).sql"
    local backup_path="/tmp/$backup_name"
    
    # Extract database connection details
    local db_url=${DATABASE_URL:-$(node -e "console.log(process.env.DATABASE_URL)")}
    
    if [ -z "$db_url" ]; then
        print_error "DATABASE_URL not found"
        return 1
    fi
    
    # Use pg_dump if available
    if command -v pg_dump >/dev/null 2>&1; then
        if pg_dump "$db_url" > "$backup_path" 2>/dev/null; then
            print_success "Database backup created: $backup_path"
            
            # Upload to S3 if AWS CLI is available
            if command -v aws >/dev/null 2>&1; then
                local s3_bucket="ailearn-master-backups-prod"
                if aws s3 cp "$backup_path" "s3://$s3_bucket/database-backups/" >/dev/null 2>&1; then
                    print_success "Backup uploaded to S3: s3://$s3_bucket/database-backups/$backup_name"
                else
                    print_warning "Failed to upload backup to S3"
                fi
            fi
            
            # Clean up local backup
            rm -f "$backup_path"
            return 0
        else
            print_error "Database backup failed"
            return 1
        fi
    else
        print_warning "pg_dump not available, skipping backup"
        return 0
    fi
}

# Function to optimize database
optimize_database() {
    print_status "Optimizing database for production..."
    
    # Create optimization script
    cat > /tmp/optimize_db.js << 'EOF'
const { Pool } = require('pg');

async function optimizeDatabase() {
    const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: { rejectUnauthorized: false }
    });
    
    try {
        const client = await pool.connect();
        
        // Analyze tables for better query planning
        console.log('Running ANALYZE on all tables...');
        await client.query('ANALYZE');
        
        // Update table statistics
        console.log('Updating table statistics...');
        await client.query('VACUUM ANALYZE');
        
        // Check for unused indexes
        console.log('Checking database health...');
        const healthCheck = await client.query(`
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE schemaname = 'public'
            LIMIT 5
        `);
        
        console.log('Database optimization completed');
        console.log('Tables analyzed:', healthCheck.rows.length);
        
        client.release();
        process.exit(0);
    } catch (error) {
        console.log('Database optimization failed:', error.message);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

optimizeDatabase();
EOF
    
    if node /tmp/optimize_db.js; then
        print_success "Database optimization completed"
        rm -f /tmp/optimize_db.js
        return 0
    else
        print_error "Database optimization failed"
        rm -f /tmp/optimize_db.js
        return 1
    fi
}

# Function to setup monitoring
setup_monitoring() {
    print_status "Setting up database monitoring..."
    
    # Create monitoring script
    cat > /tmp/setup_monitoring.js << 'EOF'
const { Pool } = require('pg');

async function setupMonitoring() {
    const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: { rejectUnauthorized: false }
    });
    
    try {
        const client = await pool.connect();
        
        // Enable query logging for slow queries
        await client.query("ALTER SYSTEM SET log_min_duration_statement = '1000'");
        await client.query("ALTER SYSTEM SET log_statement = 'mod'");
        await client.query("SELECT pg_reload_conf()");
        
        console.log('Database monitoring configured');
        
        client.release();
        process.exit(0);
    } catch (error) {
        console.log('Monitoring setup failed:', error.message);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

setupMonitoring();
EOF
    
    if node /tmp/setup_monitoring.js; then
        print_success "Database monitoring configured"
        rm -f /tmp/setup_monitoring.js
        return 0
    else
        print_warning "Database monitoring setup failed"
        rm -f /tmp/setup_monitoring.js
        return 0
    fi
}

# Function to validate database schema
validate_schema() {
    print_status "Validating database schema..."
    
    # Create schema validation script
    cat > /tmp/validate_schema.js << 'EOF'
const { Pool } = require('pg');

async function validateSchema() {
    const pool = new Pool({
        connectionString: process.env.DATABASE_URL,
        ssl: { rejectUnauthorized: false }
    });
    
    try {
        const client = await pool.connect();
        
        // Check for required tables
        const tables = await client.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name
        `);
        
        console.log('Database tables found:', tables.rows.length);
        
        // Check for required indexes
        const indexes = await client.query(`
            SELECT indexname 
            FROM pg_indexes 
            WHERE schemaname = 'public'
        `);
        
        console.log('Database indexes found:', indexes.rows.length);
        
        // Validate essential tables exist
        const essentialTables = ['users', 'courses', 'lessons'];
        const existingTables = tables.rows.map(row => row.table_name);
        
        let allTablesExist = true;
        for (const table of essentialTables) {
            if (!existingTables.includes(table)) {
                console.log(`❌ Essential table missing: ${table}`);
                allTablesExist = false;
            } else {
                console.log(`✅ Essential table found: ${table}`);
            }
        }
        
        client.release();
        
        if (allTablesExist) {
            console.log('✅ Schema validation passed');
            process.exit(0);
        } else {
            console.log('❌ Schema validation failed');
            process.exit(1);
        }
    } catch (error) {
        console.log('Schema validation failed:', error.message);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

validateSchema();
EOF
    
    if node /tmp/validate_schema.js; then
        print_success "Database schema validation passed"
        rm -f /tmp/validate_schema.js
        return 0
    else
        print_error "Database schema validation failed"
        rm -f /tmp/validate_schema.js
        return 1
    fi
}

# Main execution function
main() {
    print_status "Starting database setup for production..."
    
    # Check if Node.js and npm are available
    if ! command -v node >/dev/null 2>&1; then
        print_error "Node.js is required but not installed"
        exit 1
    fi
    
    if ! command -v npm >/dev/null 2>&1; then
        print_error "npm is required but not installed"
        exit 1
    fi
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm ci >/dev/null 2>&1
    fi
    
    # Set production environment
    export NODE_ENV=production
    
    # Run database setup steps
    local steps_passed=0
    local total_steps=7
    
    if check_database_connection; then
        steps_passed=$((steps_passed + 1))
    fi
    
    if verify_ssl_connection; then
        steps_passed=$((steps_passed + 1))
    fi
    
    if run_migrations; then
        steps_passed=$((steps_passed + 1))
    fi
    
    if validate_schema; then
        steps_passed=$((steps_passed + 1))
    fi
    
    if seed_database; then
        steps_passed=$((steps_passed + 1))
    fi
    
    if optimize_database; then
        steps_passed=$((steps_passed + 1))
    fi
    
    if setup_monitoring; then
        steps_passed=$((steps_passed + 1))
    fi
    
    # Create backup (optional)
    create_backup || true
    
    # Display results
    echo ""
    print_success "🎉 Database setup completed!"
    echo "=============================="
    echo "Steps completed: $steps_passed/$total_steps"
    
    if [ $steps_passed -eq $total_steps ]; then
        print_success "All database setup steps completed successfully"
        echo ""
        echo "Database is ready for production use with:"
        echo "• SSL/TLS encryption enabled"
        echo "• Schema validated and optimized"
        echo "• Monitoring configured"
        echo "• Backup procedures in place"
    else
        print_warning "Some database setup steps failed - review logs above"
        echo ""
        echo "You may need to:"
        echo "• Check database credentials and connectivity"
        echo "• Verify SSL configuration"
        echo "• Review migration scripts"
        echo "• Ensure proper permissions"
    fi
    
    echo ""
    echo "Next steps:"
    echo "1. Verify Modal A100 GPU connectivity"
    echo "2. Deploy application to AWS Amplify"
    echo "3. Run production validation tests"
}

# Run main function
main "$@"
