import { useCallback, useEffect, useRef } from 'react';
import { useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

interface AutoSaveData {
  draftId: string;
  courseDetails: any;
  courseStructure?: any;
  courseScripts?: any;
  mediaAttachments?: any;
  quizData?: any;
  publishData?: any;
  stepProgress?: number;
  completedSteps?: number[];
  generatedAudioFiles?: any[];
}

interface UseAutoSaveOptions {
  enabled?: boolean;
  saveInterval?: number; // milliseconds
  onSaveSuccess?: () => void;
  onSaveError?: (error: Error) => void;
}

export function useAutoSave(options: UseAutoSaveOptions = {}) {
  const {
    enabled = true,
    saveInterval = 5000, // 5 seconds
    onSaveSuccess,
    onSaveError,
  } = options;

  const { toast } = useToast();
  const dataRef = useRef<AutoSaveData | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isDirtyRef = useRef(false);
  const lastSaveRef = useRef<string>('');

  // Auto-save mutation
  const autoSaveMutation = useMutation({
    mutationFn: async (data: AutoSaveData) => {
      try {
        // Ensure data is properly serializable
        const sanitizedData = JSON.parse(JSON.stringify(data, (key, value) => {
          if (value === undefined) return null;
          if (typeof value === 'function') return null;
          if (value instanceof Date) return value.toISOString();
          return value;
        }));

        const response = await apiRequest('POST', '/api/course/drafts/auto-save', sanitizedData);
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Auto-save failed: ${response.status} ${errorText}`);
        }
        return response.json();
      } catch (error) {
        console.error('Auto-save mutation error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      isDirtyRef.current = false;
      onSaveSuccess?.();
    },
    onError: (error: Error) => {
      console.error('Auto-save failed:', error);
      onSaveError?.(error);
    },
  });

  // Update data and mark as dirty
  const updateData = useCallback((newData: Partial<AutoSaveData>) => {
    if (!enabled) return;

    try {
      const currentData = dataRef.current || {} as AutoSaveData;
      const updatedData = { ...currentData, ...newData };
      
      // Sanitize data to prevent circular references and undefined values
      const sanitizedData = JSON.parse(JSON.stringify(updatedData, (key, value) => {
        if (value === undefined) return null;
        if (typeof value === 'function') return null;
        return value;
      }));
      
      // Check if data actually changed
      const dataString = JSON.stringify(sanitizedData);
      if (dataString === lastSaveRef.current) {
        return;
      }

      dataRef.current = sanitizedData;
      isDirtyRef.current = true;
      lastSaveRef.current = dataString;

      // Schedule auto-save
      scheduleAutoSave();
    } catch (error) {
      console.warn('Failed to update auto-save data:', error);
    }
  }, [enabled]);

  // Schedule auto-save with debouncing
  const scheduleAutoSave = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      if (isDirtyRef.current && dataRef.current) {
        performAutoSave();
      }
    }, saveInterval);
  }, [saveInterval]);

  // Perform auto-save
  const performAutoSave = useCallback(() => {
    if (!dataRef.current || !isDirtyRef.current) return;

    autoSaveMutation.mutate(dataRef.current);
  }, [autoSaveMutation]);

  // Manual save
  const saveNow = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    performAutoSave();
  }, [performAutoSave]);

  // Initialize data
  const initializeData = useCallback((initialData: AutoSaveData) => {
    dataRef.current = initialData;
    lastSaveRef.current = JSON.stringify(initialData);
    isDirtyRef.current = false;
  }, []);

  // Clear auto-save timer on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Auto-save on page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (isDirtyRef.current && dataRef.current) {
        try {
          // Use sendBeacon for reliable saving on page unload
          const blob = new Blob([JSON.stringify(dataRef.current)], {
            type: 'application/json'
          });
          navigator.sendBeacon('/api/course/drafts/auto-save', blob);
        } catch (error) {
          console.warn('Failed to save on page unload:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, []);

  return {
    updateData,
    saveNow,
    initializeData,
    isSaving: autoSaveMutation.isPending,
    lastSaveTime: autoSaveMutation.isSuccess ? new Date() : null,
    saveError: autoSaveMutation.error,
  };
}

// Specialized hook for course creation
export function useCourseAutoSave(draftId: string) {
  const autoSave = useAutoSave({
    enabled: true,
    saveInterval: 3000, // 3 seconds for course creation
    onSaveSuccess: () => {
      // Silent success - no notification needed for auto-save
    },
    onSaveError: (error) => {
      console.warn('Auto-save failed, data may be lost:', error);
    },
  });

  // Course-specific update methods
  const updateCourseDetails = useCallback((courseDetails: any) => {
    try {
      if (courseDetails && typeof courseDetails === 'object') {
        autoSave.updateData({ draftId, courseDetails });
      }
    } catch (error) {
      console.warn('Failed to update course details:', error);
    }
  }, [autoSave, draftId]);

  const updateCourseStructure = useCallback((courseStructure: any) => {
    try {
      if (courseStructure && typeof courseStructure === 'object') {
        autoSave.updateData({ draftId, courseStructure });
      }
    } catch (error) {
      console.warn('Failed to update course structure:', error);
    }
  }, [autoSave, draftId]);

  const updateCourseScripts = useCallback((courseScripts: any) => {
    try {
      if (courseScripts && typeof courseScripts === 'object') {
        autoSave.updateData({ draftId, courseScripts });
      }
    } catch (error) {
      console.warn('Failed to update course scripts:', error);
    }
  }, [autoSave, draftId]);

  const updateMediaAttachments = useCallback((mediaAttachments: any) => {
    try {
      if (mediaAttachments && (Array.isArray(mediaAttachments) || typeof mediaAttachments === 'object')) {
        autoSave.updateData({ draftId, mediaAttachments });
      }
    } catch (error) {
      console.warn('Failed to update media attachments:', error);
    }
  }, [autoSave, draftId]);

  const updateStepProgress = useCallback((stepProgress: number, completedSteps: number[] = []) => {
    try {
      if (typeof stepProgress === 'number' && Array.isArray(completedSteps)) {
        autoSave.updateData({ draftId, stepProgress, completedSteps });
      }
    } catch (error) {
      console.warn('Failed to update step progress:', error);
    }
  }, [autoSave, draftId]);

  const updateGeneratedAudioFiles = useCallback((generatedAudioFiles: any[]) => {
    try {
      if (Array.isArray(generatedAudioFiles)) {
        autoSave.updateData({ draftId, generatedAudioFiles });
      }
    } catch (error) {
      console.warn('Failed to update generated audio files:', error);
    }
  }, [autoSave, draftId]);

  // Initialize with course-specific data
  const initialize = useCallback((initialData: Partial<AutoSaveData>) => {
    autoSave.initializeData({
      draftId,
      courseDetails: {},
      courseStructure: null,
      courseScripts: {},
      mediaAttachments: {},
      quizData: {},
      publishData: {},
      stepProgress: 0,
      completedSteps: [],
      generatedAudioFiles: [],
      ...initialData,
    });
  }, [autoSave, draftId]);

  return {
    ...autoSave,
    initialize,
    updateCourseDetails,
    updateCourseStructure,
    updateCourseScripts,
    updateMediaAttachments,
    updateStepProgress,
    updateGeneratedAudioFiles,
  };
}