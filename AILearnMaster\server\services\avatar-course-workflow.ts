/**
 * Complete Avatar Course Creation Workflow
 * Orchestrates: Title → Content → Avatar → Voice → TTS → Avatar Video → Slides → Composition → Subtitles → Final Course
 */

import { dbManager, safeDbOperation } from '../db-enhanced';
import { courses, modules, lessons, mediaLibrary } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import path from 'path';
import axios from 'axios';

// Import AI services
import * as mistralService from './mistralPrimaryService';
import * as coquiTTS from './coqui-tts';
import { marpSlideService } from './marpSlideService';
import { awsS3Service } from './awsS3Service';
import { workflowOptimizer } from './workflow-performance-optimizer';

// Types
export interface AvatarCourseRequest {
  title: string;
  userId: number;
  category?: string;
  targetAudience?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  duration?: 'short' | 'medium' | 'long';
  avatarSettings: {
    type: 'image' | 'video';
    sourceUrl?: string;
    sourceBase64?: string;
    style?: 'professional' | 'casual' | 'animated';
    background?: 'office' | 'classroom' | 'studio' | 'custom';
  };
  voiceSettings?: {
    provider: 'coqui' | 'kokoro';
    voiceId: string;
    speed: number;
    pitch: number;
    volume: number;
    emotion?: 'neutral' | 'enthusiastic' | 'calm';
  };
  slideSettings?: {
    style: 'professional' | 'educational' | 'creative' | 'minimal';
    includeBackground: boolean;
    compositeWithAvatar: boolean;
  };
}

export interface AvatarWorkflowProgress {
  jobId: string;
  status: 'initializing' | 'generating_content' | 'processing_avatar' | 'generating_audio' | 
          'creating_avatar_video' | 'creating_slides' | 'compositing_final' | 'generating_subtitles' | 
          'finalizing' | 'completed' | 'error';
  progress: number; // 0-100
  currentStep: string;
  estimatedTimeRemaining?: number;
  courseId?: number;
  error?: string;
  startTime: Date;
  lastUpdate: Date;
  results?: {
    courseStructure?: any;
    avatarImagePath?: string;
    audioFiles?: string[];
    avatarVideoFiles?: string[];
    slideFiles?: string[];
    compositedVideos?: string[];
    subtitleFiles?: string[];
    finalCourseUrl?: string;
  };
}

export interface CourseStructure {
  title: string;
  description: string;
  targetAudience: string;
  learningObjectives: string[];
  modules: ModuleStructure[];
  estimatedDuration: number;
}

export interface ModuleStructure {
  title: string;
  description: string;
  learningObjectives: string[];
  lessons: LessonStructure[];
  order: number;
}

export interface LessonStructure {
  title: string;
  description: string;
  script: string;
  duration: number;
  order: number;
  keyPoints: string[];
}

export class AvatarCourseWorkflow {
  private progressMap = new Map<string, AvatarWorkflowProgress>();
  private readonly MODAL_A100_URL = process.env.MODAL_A100_URL || 'https://hajhasni1984--courseai-a100-production.modal.run';
  private readonly MAX_RETRIES = 3;
  private readonly LESSON_TARGET_DURATION = 300; // 5 minutes

  /**
   * Start the complete avatar course creation workflow
   */
  async startWorkflow(request: AvatarCourseRequest): Promise<{ jobId: string; estimatedTime: number }> {
    const jobId = uuidv4();
    const estimatedTime = this.calculateEstimatedTime(request);

    // Initialize progress tracking
    const progress: AvatarWorkflowProgress = {
      jobId,
      status: 'initializing',
      progress: 0,
      currentStep: 'Initializing avatar course workflow...',
      estimatedTimeRemaining: estimatedTime,
      startTime: new Date(),
      lastUpdate: new Date(),
      results: {}
    };

    this.progressMap.set(jobId, progress);

    // Start the workflow asynchronously
    this.executeWorkflow(jobId, request).catch(error => {
      console.error(`Avatar workflow ${jobId} failed:`, error);
      this.updateProgress(jobId, {
        status: 'error',
        error: error.message,
        progress: 0
      });
    });

    return { jobId, estimatedTime };
  }

  /**
   * Get workflow progress
   */
  getProgress(jobId: string): AvatarWorkflowProgress | null {
    return this.progressMap.get(jobId) || null;
  }

  /**
   * Execute the complete workflow
   */
  private async executeWorkflow(jobId: string, request: AvatarCourseRequest): Promise<void> {
    try {
      // Step 1: Generate course content using Mistral LLM
      await this.step1_GenerateContent(jobId, request);

      // Step 2: Process avatar image/video
      await this.step2_ProcessAvatar(jobId, request);

      // Step 3: Generate audio using Coqui TTS
      await this.step3_GenerateAudio(jobId, request);

      // Step 4: Create avatar videos using EchoMimic V2
      await this.step4_CreateAvatarVideos(jobId, request);

      // Step 5: Generate background slides using Marp
      await this.step5_GenerateSlides(jobId, request);

      // Step 6: Composite avatar with slides (if needed)
      await this.step6_CompositeVideos(jobId, request);

      // Step 7: Generate subtitles using Whisper
      await this.step7_GenerateSubtitles(jobId, request);

      // Step 8: Finalize course
      await this.step8_FinalizeCourse(jobId, request);

      this.updateProgress(jobId, {
        status: 'completed',
        progress: 100,
        currentStep: 'Avatar course creation completed successfully!'
      });

    } catch (error: any) {
      console.error(`Avatar workflow step failed for ${jobId}:`, error);

      // Enhanced error handling for Modal A100 issues
      let errorMessage = error.message;
      if (error.message?.includes('404') || error.message?.includes('modal-http')) {
        errorMessage = `❌ Modal A100 GPU service is not available. Please check deployment status.\n\nTo fix this issue:\n1. Run: modal token new\n2. Deploy: modal deploy modal_a100_comprehensive_fixed.py\n3. Update MODAL_A100_URL environment variable\n\nOriginal error: ${error.message}`;
      } else if (error.message?.includes('timeout') || error.message?.includes('ECONNREFUSED')) {
        errorMessage = `❌ Modal A100 GPU service is not responding. Please check service status.\n\nTroubleshooting:\n1. Check Modal dashboard for app status\n2. Verify GPU allocation\n3. Try redeploying the Modal app\n\nOriginal error: ${error.message}`;
      } else if (error.message?.includes('ENOTFOUND') || error.message?.includes('DNS')) {
        errorMessage = `❌ Cannot reach Modal A100 service. Please check the URL.\n\nCurrent URL: ${this.MODAL_A100_URL}\n\nPlease verify the correct Modal URL and update MODAL_A100_URL environment variable.\n\nOriginal error: ${error.message}`;
      }

      this.updateProgress(jobId, {
        status: 'error',
        error: errorMessage,
        progress: 0
      });
      throw error;
    }
  }

  /**
   * Update workflow progress
   */
  private updateProgress(jobId: string, updates: Partial<AvatarWorkflowProgress>): void {
    const current = this.progressMap.get(jobId);
    if (current) {
      const updated = {
        ...current,
        ...updates,
        lastUpdate: new Date()
      };
      this.progressMap.set(jobId, updated);
    }
  }

  /**
   * Calculate estimated completion time
   */
  private calculateEstimatedTime(request: AvatarCourseRequest): number {
    const baseDuration = request.duration || 'medium';
    const durationMultipliers = {
      'short': 1.5,    // ~3 minutes (avatar processing takes longer)
      'medium': 2.5,   // ~5 minutes  
      'long': 4        // ~8 minutes
    };
    
    return 120 * durationMultipliers[baseDuration]; // seconds
  }

  /**
   * Step 1: Generate course content using Mistral LLM
   */
  private async step1_GenerateContent(jobId: string, request: AvatarCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'generating_content',
      progress: 10,
      currentStep: 'Generating course structure with Mistral AI...'
    });

    try {
      // Call Modal A100 Mistral service for content generation with optimization
      const response = await workflowOptimizer.optimizedModalRequest('/mistral', {
        prompt: this.buildContentGenerationPrompt(request),
        max_tokens: 4000,
        temperature: 0.7
      });

      const courseStructure = this.parseCourseStructure(response.text);
      
      // Save to database
      const courseId = await this.saveCourseToDatabase(request, courseStructure);
      
      this.updateProgress(jobId, {
        progress: 15,
        currentStep: 'Course structure generated successfully',
        courseId,
        results: {
          ...this.progressMap.get(jobId)?.results,
          courseStructure
        }
      });

    } catch (error) {
      throw new Error(`Content generation failed: ${error.message}`);
    }
  }

  /**
   * Step 2: Process avatar image/video
   */
  private async step2_ProcessAvatar(jobId: string, request: AvatarCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'processing_avatar',
      progress: 20,
      currentStep: 'Processing avatar image/video...'
    });

    try {
      let avatarImagePath: string;

      if (request.avatarSettings.sourceBase64) {
        // Save base64 image
        avatarImagePath = await this.saveBase64Image(request.avatarSettings.sourceBase64, jobId);
      } else if (request.avatarSettings.sourceUrl) {
        // Download image from URL
        avatarImagePath = await this.downloadAvatarImage(request.avatarSettings.sourceUrl, jobId);
      } else {
        throw new Error('Avatar image source not provided');
      }

      this.updateProgress(jobId, {
        progress: 25,
        currentStep: 'Avatar image processed successfully',
        results: {
          ...this.progressMap.get(jobId)?.results,
          avatarImagePath
        }
      });

    } catch (error) {
      throw new Error(`Avatar processing failed: ${error.message}`);
    }
  }

  /**
   * Step 3: Generate audio using Coqui TTS
   */
  private async step3_GenerateAudio(jobId: string, request: AvatarCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'generating_audio',
      progress: 30,
      currentStep: 'Generating audio with Coqui TTS...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const courseStructure = progress?.results?.courseStructure;

      if (!courseStructure) {
        throw new Error('Course structure not found');
      }

      const audioFiles: string[] = [];
      let currentProgress = 30;
      const progressIncrement = 20 / courseStructure.modules.length;

      for (const module of courseStructure.modules) {
        for (const lesson of module.lessons) {
          // Generate TTS audio via Modal A100
          const audioResponse = await axios.post(`${this.MODAL_A100_URL}/tts`, {
            text: lesson.script,
            voice_preset: request.voiceSettings?.voiceId,
            speed: request.voiceSettings?.speed || 1.0
          });

          // Save audio file
          const audioPath = await this.saveAudioFile(audioResponse.data, lesson.title, jobId);
          audioFiles.push(audioPath);
        }

        currentProgress += progressIncrement;
        this.updateProgress(jobId, {
          progress: Math.round(currentProgress),
          currentStep: `Generated audio for module: ${module.title}`
        });
      }

      this.updateProgress(jobId, {
        progress: 50,
        currentStep: 'Audio generation completed',
        results: {
          ...progress?.results,
          audioFiles
        }
      });

    } catch (error) {
      throw new Error(`Audio generation failed: ${error.message}`);
    }
  }

  /**
   * Step 4: Create avatar videos using EchoMimic V2
   */
  private async step4_CreateAvatarVideos(jobId: string, request: AvatarCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'creating_avatar_video',
      progress: 55,
      currentStep: 'Creating avatar videos with EchoMimic V2...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const { courseStructure, avatarImagePath, audioFiles } = progress?.results || {};

      if (!courseStructure || !avatarImagePath || !audioFiles) {
        throw new Error('Required assets not found for avatar video creation');
      }

      const avatarVideoFiles: string[] = [];
      let currentProgress = 55;
      const progressIncrement = 25 / audioFiles.length;

      // Prepare avatar video operations for parallel processing
      const avatarImageBase64 = await this.fileToBase64(avatarImagePath);

      const avatarOperations = audioFiles.map((audioFile, index) => ({
        name: `Avatar-Video-${index + 1}`,
        operation: async () => {
          // Convert audio to base64
          const audioBase64 = await this.fileToBase64(audioFile);

          // Generate avatar video via Modal A100 EchoMimic V2 with optimization
          const avatarResponse = await workflowOptimizer.optimizedModalRequest('/api_avatar', {
            image_base64: avatarImageBase64,
            audio_base64: audioBase64,
            style: request.avatarSettings.style || 'professional',
            background: request.avatarSettings.background || 'studio'
          });

          // Save avatar video
          return await this.saveVideoFile(avatarResponse, `lesson_${index + 1}`, jobId);
        }
      }));

      // Execute avatar operations with limited concurrency (avatar generation is resource-intensive)
      const results = await workflowOptimizer.executeParallel(avatarOperations, 2);
      avatarVideoFiles.push(...results);

      // Update progress
      for (let i = 0; i < audioFiles.length; i++) {
        currentProgress += progressIncrement;
        this.updateProgress(jobId, {
          progress: Math.round(currentProgress),
          currentStep: `Created avatar video ${i + 1} of ${audioFiles.length}`
        });
      }

      this.updateProgress(jobId, {
        progress: 80,
        currentStep: 'Avatar video creation completed',
        results: {
          ...progress?.results,
          avatarVideoFiles
        }
      });

    } catch (error) {
      throw new Error(`Avatar video creation failed: ${error.message}`);
    }
  }

  /**
   * Step 5: Generate background slides using Marp
   */
  private async step5_GenerateSlides(jobId: string, request: AvatarCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'creating_slides',
      progress: 85,
      currentStep: 'Creating background slides with Marp...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const courseStructure = progress?.results?.courseStructure;

      if (!courseStructure) {
        throw new Error('Course structure not found');
      }

      const slideFiles: string[] = [];

      if (request.slideSettings?.includeBackground) {
        for (const module of courseStructure.modules) {
          for (const lesson of module.lessons) {
            // Generate Marp slides via Modal A100
            const slideMarkdown = this.generateSlideMarkdown(lesson, request.slideSettings?.style || 'professional');

            const slideResponse = await axios.post(`${this.MODAL_A100_URL}/slides`, {
              markdown_content: slideMarkdown,
              theme: request.slideSettings?.style || 'professional',
              output_format: 'pdf'
            });

            // Save slide file
            const slidePath = await this.saveSlideFile(slideResponse.data, lesson.title, jobId);
            slideFiles.push(slidePath);
          }
        }
      }

      this.updateProgress(jobId, {
        progress: 90,
        currentStep: 'Background slides created',
        results: {
          ...progress?.results,
          slideFiles
        }
      });

    } catch (error) {
      throw new Error(`Slide generation failed: ${error.message}`);
    }
  }

  /**
   * Step 6: Composite avatar with slides (if needed)
   */
  private async step6_CompositeVideos(jobId: string, request: AvatarCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'compositing_final',
      progress: 92,
      currentStep: 'Compositing final videos...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const { avatarVideoFiles, slideFiles } = progress?.results || {};

      if (!avatarVideoFiles) {
        throw new Error('Avatar video files not found');
      }

      let compositedVideos: string[] = [];

      if (request.slideSettings?.compositeWithAvatar && slideFiles && slideFiles.length > 0) {
        // Composite avatar videos with background slides
        for (let i = 0; i < avatarVideoFiles.length; i++) {
          const compositedPath = await this.compositeAvatarWithSlides(
            avatarVideoFiles[i],
            slideFiles[i] || slideFiles[0], // Use first slide if not enough slides
            jobId,
            i
          );
          compositedVideos.push(compositedPath);
        }
      } else {
        // Use avatar videos as-is
        compositedVideos = avatarVideoFiles;
      }

      this.updateProgress(jobId, {
        progress: 95,
        currentStep: 'Video compositing completed',
        results: {
          ...progress?.results,
          compositedVideos
        }
      });

    } catch (error) {
      throw new Error(`Video compositing failed: ${error.message}`);
    }
  }

  /**
   * Step 7: Generate subtitles using Whisper
   */
  private async step7_GenerateSubtitles(jobId: string, request: AvatarCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'generating_subtitles',
      progress: 97,
      currentStep: 'Generating subtitles with Whisper...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const { audioFiles } = progress?.results || {};

      if (!audioFiles) {
        throw new Error('Audio files not found for subtitle generation');
      }

      const subtitleFiles: string[] = [];

      for (const audioFile of audioFiles) {
        // Generate subtitles via Modal A100 Whisper service
        const subtitleResponse = await axios.post(`${this.MODAL_A100_URL}/whisper`, {
          audio_base64: await this.fileToBase64(audioFile),
          output_format: 'srt'
        });

        const subtitlePath = await this.saveSubtitleFile(subtitleResponse.data, audioFile, jobId);
        subtitleFiles.push(subtitlePath);
      }

      this.updateProgress(jobId, {
        progress: 98,
        currentStep: 'Subtitle generation completed',
        results: {
          ...progress?.results,
          subtitleFiles
        }
      });

    } catch (error) {
      throw new Error(`Subtitle generation failed: ${error.message}`);
    }
  }

  /**
   * Step 8: Finalize course
   */
  private async step8_FinalizeCourse(jobId: string, request: AvatarCourseRequest): Promise<void> {
    this.updateProgress(jobId, {
      status: 'finalizing',
      progress: 99,
      currentStep: 'Finalizing course and uploading to S3...'
    });

    try {
      const progress = this.progressMap.get(jobId);
      const { compositedVideos, subtitleFiles } = progress?.results || {};

      if (!compositedVideos) {
        throw new Error('Final videos not found for finalization');
      }

      // Upload all files to S3
      const uploadedUrls: string[] = [];

      for (let i = 0; i < compositedVideos.length; i++) {
        const videoUrl = await awsS3Service.uploadFile(compositedVideos[i], `courses/${jobId}/videos/`);
        uploadedUrls.push(videoUrl);

        if (subtitleFiles && subtitleFiles[i]) {
          const subtitleUrl = await awsS3Service.uploadFile(subtitleFiles[i], `courses/${jobId}/subtitles/`);
          uploadedUrls.push(subtitleUrl);
        }
      }

      // Update database with final course data
      await this.updateCourseInDatabase(progress.courseId!, uploadedUrls);

      this.updateProgress(jobId, {
        progress: 100,
        currentStep: 'Avatar course finalization completed',
        results: {
          ...progress?.results,
          finalCourseUrl: uploadedUrls[0] // First video URL
        }
      });

    } catch (error) {
      throw new Error(`Course finalization failed: ${error.message}`);
    }
  }

  // Helper Methods

  /**
   * Build content generation prompt for Mistral
   */
  private buildContentGenerationPrompt(request: AvatarCourseRequest): string {
    return `Create a comprehensive course structure for an avatar-based course: "${request.title}"

Target Audience: ${request.targetAudience || 'General learners'}
Category: ${request.category || 'General'}
Difficulty: ${request.difficulty || 'intermediate'}
Duration: ${request.duration || 'medium'}

This course will be delivered by an AI avatar instructor, so please:
1. Write scripts in a conversational, engaging tone suitable for video presentation
2. Include natural pauses and emphasis points
3. Structure content for visual presentation with an avatar
4. Keep lessons focused and digestible (3-5 minutes each)

Please provide a detailed course structure with:
1. Course description and learning objectives
2. 3-5 modules with clear titles and descriptions
3. 3-5 lessons per module with detailed scripts (200-400 words each)
4. Key learning points for each lesson

Format the response as JSON with the following structure:
{
  "title": "Course Title",
  "description": "Course description",
  "targetAudience": "Target audience",
  "learningObjectives": ["objective1", "objective2"],
  "estimatedDuration": 120,
  "modules": [
    {
      "title": "Module Title",
      "description": "Module description",
      "order": 1,
      "learningObjectives": ["objective1"],
      "lessons": [
        {
          "title": "Lesson Title",
          "description": "Lesson description",
          "script": "Conversational lesson script for avatar delivery...",
          "duration": 240,
          "order": 1,
          "keyPoints": ["point1", "point2"]
        }
      ]
    }
  ]
}`;
  }

  /**
   * Parse course structure from Mistral response
   */
  private parseCourseStructure(response: string): CourseStructure {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const parsed = JSON.parse(jsonMatch[0]);
      return parsed as CourseStructure;
    } catch (error) {
      throw new Error(`Failed to parse course structure: ${error.message}`);
    }
  }

  /**
   * Save course to database
   */
  private async saveCourseToDatabase(request: AvatarCourseRequest, structure: CourseStructure): Promise<number> {
    return await safeDbOperation(async (db) => {
      // Create course
      const [course] = await db.insert(courses).values({
        title: structure.title,
        description: structure.description,
        userId: request.userId,
        category: request.category || 'general',
        difficulty: request.difficulty || 'intermediate',
        estimatedDuration: structure.estimatedDuration,
        status: 'generating',
        format: 'avatar'
      }).returning();

      // Create modules and lessons
      for (const moduleData of structure.modules) {
        const [module] = await db.insert(modules).values({
          courseId: course.id,
          title: moduleData.title,
          description: moduleData.description,
          order: moduleData.order
        }).returning();

        for (const lessonData of moduleData.lessons) {
          await db.insert(lessons).values({
            moduleId: module.id,
            title: lessonData.title,
            content: lessonData.description,
            script: lessonData.script,
            duration: lessonData.duration,
            order: lessonData.order
          });
        }
      }

      return course.id;
    });
  }

  /**
   * Save base64 image
   */
  private async saveBase64Image(base64Data: string, jobId: string): Promise<string> {
    const fileName = `avatar_${Date.now()}.jpg`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'avatar', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });

    // Remove data URL prefix if present
    const base64Clean = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Clean, 'base64');
    await fs.writeFile(filePath, buffer);

    return filePath;
  }

  /**
   * Download avatar image from URL
   */
  private async downloadAvatarImage(url: string, jobId: string): Promise<string> {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    const fileName = `avatar_${Date.now()}.jpg`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'avatar', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, response.data);

    return filePath;
  }

  /**
   * Save audio file
   */
  private async saveAudioFile(audioData: any, lessonTitle: string, jobId: string): Promise<string> {
    const fileName = `${lessonTitle.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.mp3`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'audio', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });

    if (typeof audioData === 'string') {
      // Base64 encoded audio
      const buffer = Buffer.from(audioData, 'base64');
      await fs.writeFile(filePath, buffer);
    } else {
      // Binary audio data
      await fs.writeFile(filePath, audioData);
    }

    return filePath;
  }

  /**
   * Save video file
   */
  private async saveVideoFile(videoData: any, lessonTitle: string, jobId: string): Promise<string> {
    const fileName = `${lessonTitle.replace(/[^a-zA-Z0-9]/g, '_')}_avatar_${Date.now()}.mp4`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'videos', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });

    if (typeof videoData === 'string') {
      // Base64 encoded video
      const buffer = Buffer.from(videoData, 'base64');
      await fs.writeFile(filePath, buffer);
    } else {
      // Binary video data
      await fs.writeFile(filePath, videoData);
    }

    return filePath;
  }

  /**
   * Generate slide markdown for Marp
   */
  private generateSlideMarkdown(lesson: LessonStructure, style: string): string {
    const themeClass = style === 'professional' ? 'class: lead' : '';

    return `---
marp: true
theme: ${style}
${themeClass}
---

# ${lesson.title}

${lesson.description}

---

## Key Learning Points

${lesson.keyPoints.map(point => `- ${point}`).join('\n')}

---

## Summary

- Topic: ${lesson.title}
- Duration: ${Math.round(lesson.duration / 60)} minutes
- Key concepts covered

`;
  }

  /**
   * Save slide file
   */
  private async saveSlideFile(slideData: any, lessonTitle: string, jobId: string): Promise<string> {
    const fileName = `${lessonTitle.replace(/[^a-zA-Z0-9]/g, '_')}_slides_${Date.now()}.pdf`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'slides', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });

    if (typeof slideData === 'string') {
      const buffer = Buffer.from(slideData, 'base64');
      await fs.writeFile(filePath, buffer);
    } else {
      await fs.writeFile(filePath, slideData);
    }

    return filePath;
  }

  /**
   * Composite avatar video with background slides
   */
  private async compositeAvatarWithSlides(
    avatarVideoPath: string,
    slidePath: string,
    jobId: string,
    index: number
  ): Promise<string> {
    const { spawn } = require('child_process');
    const outputPath = path.join(process.cwd(), 'temp', jobId, 'composited',
      `composited_lesson_${index + 1}_${Date.now()}.mp4`);

    await fs.mkdir(path.dirname(outputPath), { recursive: true });

    return new Promise((resolve, reject) => {
      // Create FFmpeg command for compositing avatar with slides
      const ffmpegArgs = [
        '-y', // Overwrite output
        '-i', avatarVideoPath, // Avatar video input
        '-i', slidePath, // Slide background (convert PDF to image first)
        '-filter_complex', '[1:v]scale=1920:1080[bg];[bg][0:v]overlay=W-w-50:50', // Overlay avatar on slides
        '-c:v', 'libx264',
        '-c:a', 'copy',
        '-pix_fmt', 'yuv420p',
        outputPath
      ];

      const ffmpeg = spawn('ffmpeg', ffmpegArgs);

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          resolve(outputPath);
        } else {
          reject(new Error(`FFmpeg compositing failed with code ${code}`));
        }
      });

      ffmpeg.on('error', (error) => {
        reject(new Error(`FFmpeg compositing error: ${error.message}`));
      });
    });
  }

  /**
   * Save subtitle file
   */
  private async saveSubtitleFile(subtitleData: any, audioFile: string, jobId: string): Promise<string> {
    const baseName = path.basename(audioFile, path.extname(audioFile));
    const fileName = `${baseName}_subtitles.srt`;
    const filePath = path.join(process.cwd(), 'temp', jobId, 'subtitles', fileName);

    await fs.mkdir(path.dirname(filePath), { recursive: true });
    await fs.writeFile(filePath, subtitleData);

    return filePath;
  }

  /**
   * Convert file to base64
   */
  private async fileToBase64(filePath: string): Promise<string> {
    const buffer = await fs.readFile(filePath);
    return buffer.toString('base64');
  }

  /**
   * Update course in database with final URLs
   */
  private async updateCourseInDatabase(courseId: number, urls: string[]): Promise<void> {
    await safeDbOperation(async (db) => {
      await db.update(courses)
        .set({
          status: 'published',
          videoUrl: urls[0], // First video URL
          updatedAt: new Date()
        })
        .where(eq(courses.id, courseId));
    });
  }
}

// Export singleton instance
export const avatarCourseWorkflow = new AvatarCourseWorkflow();
