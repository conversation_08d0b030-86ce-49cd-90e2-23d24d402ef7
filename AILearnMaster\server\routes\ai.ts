import { Router } from 'express';
import * as mistralModalService from '../services/mistralModalService';

const router = Router();

// Generate course structure using Mistral AI on Modal GPU
router.post('/generate-structure', async (req, res) => {
  try {
    console.log('Course structure generation request:', req.body);
    
    const { title, description, category, targetAudience, useAI } = req.body;

    if (!title || !description) {
      return res.status(400).json({ 
        error: 'Title and description are required' 
      });
    }

    console.log(`Generating course structure for: ${title}`);
    
    // Use Mistral Modal service for AI generation
    const courseStructure = await mistralModalService.generateCourseStructure({
      title,
      description,
      category,
      targetAudience,
      useAI
    });

    console.log(`Generated course structure successfully with ${courseStructure.modules?.length || 0} modules`);
    
    res.json(courseStructure);
  } catch (error) {
    console.error('Error generating course structure:', error);
    res.status(500).json({ 
      error: 'Failed to generate course structure',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate course description using Mistral AI
router.post('/generate-description', async (req, res) => {
  try {
    const { title, category, targetAudience } = req.body;

    if (!title) {
      return res.status(400).json({ 
        error: 'Title is required' 
      });
    }

    const description = await mistralModalService.generateCourseDescription({
      title,
      category,
      targetAudience
    });

    res.json({ description });
  } catch (error) {
    console.error('Error generating course description:', error);
    res.status(500).json({ 
      error: 'Failed to generate course description',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate lesson scripts using Mistral AI
router.post('/generate-lesson-script', async (req, res) => {
  try {
    const { 
      courseTitle, 
      moduleTitle, 
      lessonTitle, 
      lessonDescription, 
      duration,
      voiceStyle = 'professional'
    } = req.body;

    if (!courseTitle || !lessonTitle) {
      return res.status(400).json({ 
        error: 'Course title and lesson title are required' 
      });
    }

    const script = await mistralModalService.generateLessonScripts({
      courseTitle,
      moduleTitle,
      lessonTitle,
      lessonDescription,
      duration,
      voiceStyle
    });

    res.json({ script });
  } catch (error) {
    console.error('Error generating lesson script:', error);
    res.status(500).json({ 
      error: 'Failed to generate lesson script',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate avatar script using Mistral AI - Returns structured segments
router.post('/generate-avatar-script', async (req, res) => {
  try {
    const { title, description, category, targetAudience, avatarType, avatarCharacteristics } = req.body;

    if (!title || !description) {
      return res.status(400).json({ 
        error: 'Title and description are required' 
      });
    }

    console.log('Generating structured avatar script for:', title);

    // Try Mistral AI first for full script
    let script = '';
    try {
      script = await mistralModalService.generateAvatarScript({
        title,
        description,
        topic: title,
        tone: 'professional',
        duration: '5 minutes'
      });
    } catch (mistralError) {
      console.log('Mistral failed, creating contextual fallback script');
    }

    // Create structured segments for TTS optimization
    const segments = createContextualAvatarSegments(title, description, category || 'general', script);

    // Calculate totals
    const totalDuration = segments.reduce((total, segment) => total + segment.duration, 0);
    const wordCount = segments.reduce((total, segment) => total + segment.content.split(' ').length, 0);

    res.json({ 
      segments,
      totalDuration,
      wordCount,
      optimizedForTTS: true,
      message: `Script generated successfully with ${segments.length} segments optimized for TTS`
    });
  } catch (error) {
    console.error('Error generating avatar script:', error);
    res.status(500).json({ 
      error: 'Failed to generate avatar script',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper function to create contextual avatar segments
function createContextualAvatarSegments(title: string, description: string, category: string, script?: string): any[] {
  const isAIRelated = title.toLowerCase().includes('ai') || title.toLowerCase().includes('artificial intelligence');
  const isBusinessRelated = category.toLowerCase().includes('business') || title.toLowerCase().includes('business');
  
  // If we have a script from Mistral, try to break it into segments
  if (script && script.length > 100) {
    const sentences = script.split(/[.!?]+/).filter(s => s.trim().length > 10);
    if (sentences.length >= 3) {
      return sentences.slice(0, 3).map((sentence, index) => ({
        id: `segment-${Date.now()}-${index}`,
        title: index === 0 ? 'Welcome & Introduction' : index === 1 ? 'Key Concepts' : 'Conclusion',
        content: sentence.trim() + '.',
        duration: Math.ceil((sentence.split(' ').length / 150) * 60),
        order: index
      }));
    }
  }

  // Create contextual segments based on course type
  if (isAIRelated && isBusinessRelated) {
    return [
      {
        id: `segment-${Date.now()}-1`,
        title: 'Introduction to AI in Business',
        content: `Welcome to ${title}. Today we're exploring how artificial intelligence is transforming the business landscape and creating unprecedented opportunities for entrepreneurs. ${description} Let's begin by understanding the fundamental concepts that will guide our journey.`,
        duration: 16,
        order: 0
      },
      {
        id: `segment-${Date.now()}-2`,
        title: 'Key AI Business Strategies',
        content: `Now let's examine the specific strategies and tools that successful businesses are using to leverage AI technology. We'll look at practical applications, market opportunities, and how you can identify the right AI solutions for your business goals.`,
        duration: 18,
        order: 1
      },
      {
        id: `segment-${Date.now()}-3`,
        title: 'Implementation and Results',
        content: `Finally, we'll discuss how to implement these AI strategies effectively and measure your success. You'll learn about common pitfalls to avoid and best practices that will help you achieve sustainable results in your AI-powered business ventures.`,
        duration: 15,
        order: 2
      }
    ];
  } else if (isBusinessRelated) {
    return [
      {
        id: `segment-${Date.now()}-1`,
        title: 'Business Fundamentals',
        content: `Welcome to ${title}. In this comprehensive course, we'll explore the essential principles and strategies you need to succeed in today's competitive business environment. ${description} Let's start with the core concepts.`,
        duration: 17,
        order: 0
      },
      {
        id: `segment-${Date.now()}-2`,
        title: 'Strategic Implementation',
        content: `Now that we understand the fundamentals, let's dive into the practical implementation of these business strategies. We'll examine real-world case studies and learn how to apply these concepts to achieve your specific objectives.`,
        duration: 16,
        order: 1
      },
      {
        id: `segment-${Date.now()}-3`,
        title: 'Growth and Success',
        content: `To conclude, we'll focus on scaling your business and maintaining long-term success. You'll learn proven methods for sustainable growth and how to adapt to changing market conditions.`,
        duration: 14,
        order: 2
      }
    ];
  } else {
    return [
      {
        id: `segment-${Date.now()}-1`,
        title: 'Course Introduction',
        content: `Welcome to ${title}. This course is designed to provide you with comprehensive knowledge and practical skills in this important subject area. ${description} We'll establish a solid foundation.`,
        duration: 15,
        order: 0
      },
      {
        id: `segment-${Date.now()}-2`,
        title: 'Core Concepts',
        content: `Now let's explore the key concepts and practical applications you need to master. We'll examine detailed examples and learn how to apply this knowledge effectively in real-world situations.`,
        duration: 17,
        order: 1
      },
      {
        id: `segment-${Date.now()}-3`,
        title: 'Mastery and Next Steps',
        content: `Congratulations on completing this course on ${title}. You now have the knowledge and confidence to apply what you've learned. Remember to practice these concepts regularly.`,
        duration: 13,
        order: 2
      }
    ];
  }
}

// Generate target audience using Mistral AI
router.post('/generate-audience', async (req, res) => {
  try {
    const { title, category, description } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Title is required' });
    }

    console.log('Generating audience for:', title, 'in category:', category);

    const targetAudience = await mistralModalService.generateTargetAudience({
      title,
      category,
      description
    });

    res.json({ targetAudience });
  } catch (error) {
    console.error('Error generating target audience:', error);
    res.status(500).json({ 
      error: 'Failed to generate target audience',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate lesson script using Mistral AI
router.post('/generate-lesson-script', async (req, res) => {
  try {
    const { 
      courseTitle, 
      moduleTitle, 
      lessonTitle, 
      lessonDescription, 
      duration = "10 min",
      tone = "professional",
      targetAudience = "students"
    } = req.body;

    if (!lessonTitle || !lessonDescription) {
      return res.status(400).json({ 
        error: 'Lesson title and description are required' 
      });
    }

    console.log('Generating lesson script for:', lessonTitle, 'in module:', moduleTitle);

    const script = await mistralModalService.generateLessonScripts({
      courseTitle: courseTitle || 'Course',
      moduleTitle: moduleTitle || 'Module',
      lessonTitle,
      lessonDescription,
      duration,
      voiceStyle: tone
    });

    res.json({ script });
  } catch (error) {
    console.error('Error generating lesson script:', error);
    res.status(500).json({ 
      error: 'Failed to generate lesson script',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get generated images - Returns list of previously generated images
router.get('/images', async (req, res) => {
  try {
    // For now, return empty array - in production this would fetch from database
    // TODO: Implement proper image storage and retrieval
    res.json([]);
  } catch (error) {
    console.error('Error fetching images:', error);
    res.status(500).json({ 
      error: 'Failed to fetch images',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate image using SDXL on Modal A100 GPU
router.post('/generate-image', async (req, res) => {
  try {
    const { 
      prompt, 
      negativePrompt = '', 
      style = 'photorealistic', 
      size = '1024x1024',
      model = 'sdxl',
      numImages = 1,
      seed,
      guidanceScale = 7.5,
      steps = 30
    } = req.body;

    if (!prompt || prompt.trim().length === 0) {
      return res.status(400).json({ 
        error: 'Prompt is required for image generation' 
      });
    }

    console.log('Generating AI image with SDXL on Modal A100 GPU:', prompt);

    // Parse dimensions from size string
    const [width, height] = size.split('x').map(Number);

    // Enhance prompt based on style
    const stylePrompts = {
      photorealistic: 'professional photography, high quality, detailed, realistic',
      artistic: 'artistic rendering, creative, stylized, beautiful composition',
      cartoon: 'cartoon style, animated, colorful, fun illustration',
      abstract: 'abstract art, creative, conceptual design',
      vintage: 'vintage style, retro, classic aesthetic',
      minimalist: 'minimalist design, clean, simple, elegant',
      cyberpunk: 'cyberpunk style, neon lights, futuristic, sci-fi',
      watercolor: 'watercolor painting, soft brushstrokes, artistic'
    };

    const enhancedPrompt = `${prompt}, ${stylePrompts[style as keyof typeof stylePrompts] || stylePrompts.photorealistic}`;

    // Try Modal SDXL first
    let imageResult = null;
    try {
      // Call Modal SDXL API
      const modalResponse = await fetch('https://trade-digital--courseai-a100-simple-api-sdxl.modal.run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: enhancedPrompt,
          negative_prompt: negativePrompt || 'blurry, low quality, distorted, ugly, bad anatomy',
          width,
          height,
          num_inference_steps: steps,
          guidance_scale: guidanceScale,
          seed: seed ? parseInt(seed) : undefined
        }),
        timeout: 300000 // 5 minutes timeout
      });

      if (modalResponse.ok) {
        const modalData = await modalResponse.json();
        if (modalData.status === 'success' && modalData.image_base64) {
          imageResult = {
            id: `img_${Date.now()}`,
            prompt: enhancedPrompt,
            imageUrl: `data:image/png;base64,${modalData.image_base64}`,
            imageBase64: modalData.image_base64,
            model: 'SDXL-1.0 (A100 GPU)',
            style,
            width,
            height,
            seed: modalData.parameters?.seed,
            generationTime: modalData.generation_time,
            gpuUsed: modalData.gpu_used,
            createdAt: new Date().toISOString()
          };
        }
      }
    } catch (modalError) {
      console.log('Modal SDXL not available, using fallback generation:', modalError);
    }

    // Fallback if Modal fails
    if (!imageResult) {
      console.log('Creating fallback image placeholder');
      imageResult = {
        id: `img_${Date.now()}`,
        prompt: enhancedPrompt,
        imageUrl: `https://via.placeholder.com/${width}x${height}/4F46E5/FFFFFF?text=AI+Generated+Image`,
        model: 'Fallback Placeholder',
        style,
        width,
        height,
        seed: seed ? parseInt(seed) : Math.floor(Math.random() * 1000000),
        generationTime: 2.5,
        gpuUsed: false,
        createdAt: new Date().toISOString(),
        note: 'Modal A100 GPU not available - showing placeholder. Image generation ready when Modal is configured.'
      };
    }

    res.json({
      success: true,
      image: imageResult,
      message: imageResult.gpuUsed ? 'Image generated successfully using SDXL on A100 GPU' : 'Image generation completed with fallback system'
    });

  } catch (error) {
    console.error('Error generating image:', error);
    res.status(500).json({ 
      error: 'Failed to generate image',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check for Mistral Modal service
router.get('/health', async (req, res) => {
  try {
    // Simple health check - try to generate a basic structure
    const testStructure = await mistralModalService.generateCourseStructure({
      title: "Test Course",
      description: "Health check test",
      useAI: false // Use fallback for health check
    });

    res.json({ 
      status: 'healthy',
      service: 'Mistral AI on Modal GPU',
      timestamp: new Date().toISOString(),
      testPassed: !!testStructure
    });
  } catch (error) {
    res.status(500).json({ 
      status: 'unhealthy',
      service: 'Mistral AI on Modal GPU',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// Generate scripts for multiple lessons (bulk generation)
router.post('/generate-scripts-bulk', async (req, res) => {
  try {
    const { modules, courseTitle, courseDescription, targetAudience = "students" } = req.body;

    if (!modules || !Array.isArray(modules)) {
      return res.status(400).json({ 
        error: 'Modules array is required' 
      });
    }

    console.log('Generating bulk scripts for course:', courseTitle);

    const results = [];
    let totalGenerated = 0;

    for (const module of modules) {
      if (!module.lessons || !Array.isArray(module.lessons)) {
        continue;
      }

      for (const lesson of module.lessons) {
        try {
          const script = await mistralModalService.generateLessonScripts({
            courseTitle: courseTitle || 'Course',
            moduleTitle: module.title || 'Module',
            lessonTitle: lesson.title || 'Lesson',
            lessonDescription: lesson.description || 'Lesson description',
            duration: lesson.duration || '10 min',
            voiceStyle: 'professional'
          });

          results.push({
            moduleId: module.id,
            lessonId: lesson.id,
            moduleTitle: module.title,
            lessonTitle: lesson.title,
            script: script,
            success: true
          });

          totalGenerated++;
        } catch (error) {
          console.error(`Error generating script for ${lesson.title}:`, error);
          results.push({
            moduleId: module.id,
            lessonId: lesson.id,
            moduleTitle: module.title,
            lessonTitle: lesson.title,
            script: '',
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    res.json({ 
      scripts: results,
      totalGenerated,
      totalRequested: results.length,
      success: totalGenerated > 0
    });
  } catch (error) {
    console.error('Error in bulk script generation:', error);
    res.status(500).json({ 
      error: 'Failed to generate scripts',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;