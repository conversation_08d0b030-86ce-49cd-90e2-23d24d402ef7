import { Router } from 'express';
import * as mistralModalService from '../services/mistralModalService';

const router = Router();

// Generate course structure using Mistral AI on Modal GPU
router.post('/generate-structure', async (req, res) => {
  try {
    console.log('Course structure generation request:', req.body);
    
    const { title, description, category, targetAudience, useAI } = req.body;

    if (!title || !description) {
      return res.status(400).json({ 
        error: 'Title and description are required' 
      });
    }

    console.log(`Generating course structure for: ${title}`);
    
    // Use Mistral Modal service for AI generation
    const courseStructure = await mistralModalService.generateCourseStructure({
      title,
      description,
      category,
      targetAudience,
      useAI
    });

    console.log(`Generated course structure successfully with ${courseStructure.modules?.length || 0} modules`);
    
    res.json(courseStructure);
  } catch (error) {
    console.error('Error generating course structure:', error);
    res.status(500).json({ 
      error: 'Failed to generate course structure',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate course description using Mistral AI
router.post('/generate-description', async (req, res) => {
  try {
    const { title, category, targetAudience } = req.body;

    if (!title) {
      return res.status(400).json({ 
        error: 'Title is required' 
      });
    }

    const description = await mistralModalService.generateCourseDescription({
      title,
      category,
      targetAudience
    });

    res.json({ description });
  } catch (error) {
    console.error('Error generating course description:', error);
    res.status(500).json({ 
      error: 'Failed to generate course description',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate lesson scripts using Mistral AI
router.post('/generate-lesson-script', async (req, res) => {
  try {
    const { 
      courseTitle, 
      moduleTitle, 
      lessonTitle, 
      lessonDescription, 
      duration,
      voiceStyle = 'professional'
    } = req.body;

    if (!courseTitle || !lessonTitle) {
      return res.status(400).json({ 
        error: 'Course title and lesson title are required' 
      });
    }

    const script = await mistralModalService.generateLessonScripts({
      courseTitle,
      moduleTitle,
      lessonTitle,
      lessonDescription,
      duration,
      voiceStyle
    });

    res.json({ script });
  } catch (error) {
    console.error('Error generating lesson script:', error);
    res.status(500).json({ 
      error: 'Failed to generate lesson script',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate avatar script using Mistral AI
router.post('/generate-avatar-script', async (req, res) => {
  try {
    const { title, description, topic, tone, duration } = req.body;

    if (!title || !description) {
      return res.status(400).json({ 
        error: 'Title and description are required' 
      });
    }

    const script = await mistralModalService.generateAvatarScript({
      title,
      description,
      topic,
      tone,
      duration
    });

    res.json({ script });
  } catch (error) {
    console.error('Error generating avatar script:', error);
    res.status(500).json({ 
      error: 'Failed to generate avatar script',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check for Mistral Modal service
router.get('/health', async (req, res) => {
  try {
    // Simple health check - try to generate a basic structure
    const testStructure = await mistralModalService.generateCourseStructure({
      title: "Test Course",
      description: "Health check test",
      useAI: false // Use fallback for health check
    });

    res.json({ 
      status: 'healthy',
      service: 'Mistral AI on Modal GPU',
      timestamp: new Date().toISOString(),
      testPassed: !!testStructure
    });
  } catch (error) {
    res.status(500).json({ 
      status: 'unhealthy',
      service: 'Mistral AI on Modal GPU',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;