# Modal A100 GPU Deployment Fix Guide

## 🚨 Current Issues Identified

**Primary Issue**: HTTP 404 errors on all endpoints
- Modal app is not properly deployed or endpoints are missing
- Current URL returns "modal-http: invalid function call"
- All 7 AI services are unreachable

## 🔧 Step-by-Step Fix Process

### Step 1: Modal Authentication
```bash
# Install Modal CLI if not already installed
pip install modal

# Authenticate with Modal (required)
modal token new
```

### Step 2: Deploy Fixed Modal Script
```bash
# Deploy the comprehensive fixed version
modal deploy modal_a100_comprehensive_fixed.py

# Verify deployment
modal app list
```

### Step 3: Get Correct Modal URL
After deployment, Modal will provide the correct URL. Update the following:

1. **Environment Variables** (`.env` file):
```
MODAL_A100_URL=https://your-username--courseai-a100-production.modal.run
```

2. **Workflow Services** (`server/services/traditional-course-workflow.ts` and `avatar-course-workflow.ts`):
```typescript
private MODAL_A100_URL = process.env.MODAL_A100_URL || 'https://your-username--courseai-a100-production.modal.run';
```

### Step 4: Test Deployment
```bash
# Run comprehensive diagnostic
python diagnose_modal_issues.py

# Run full test suite
python test_modal_a100_comprehensive.py
```

## 🛠️ Fixed Modal Script Features

The new `modal_a100_comprehensive_fixed.py` includes:

### ✅ All 7 AI Services
1. **Mistral LLM** (`/mistral`) - Content generation with `device_map="auto"`
2. **SDXL** (`/sdxl`) - Image generation with memory optimization
3. **Coqui TTS** (`/tts`) - Voice synthesis with speed/pitch control
4. **EchoMimic V2** (`/api_avatar`) - Avatar video generation
5. **Whisper** (`/whisper`) - Speech-to-text with SRT output
6. **Marp** (`/slides`) - Slide generation with themes
7. **FFmpeg** (`/ffmpeg`) - Video processing and merging

### ✅ Performance Optimizations
- `keep_warm=1` for faster response times
- Proper GPU memory management with `torch.cuda.empty_cache()`
- `device_map="auto"` for optimal GPU utilization
- Memory-efficient attention for SDXL
- Timeout and error handling for all services

### ✅ Proper Error Handling
- Comprehensive try-catch blocks
- Detailed error messages
- GPU cleanup on failures
- Timeout protection

## 🧪 Testing Checklist

After deployment, verify each service:

### Health Check
```bash
curl https://your-modal-url/health
```
Expected: GPU status, memory info, service availability

### Mistral LLM
```bash
curl -X POST https://your-modal-url/mistral \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Create a course outline for Python:", "max_tokens": 500}'
```

### SDXL Image Generation
```bash
curl -X POST https://your-modal-url/sdxl \
  -H "Content-Type: application/json" \
  -d '{"prompt": "A professional teacher in a classroom", "width": 512, "height": 512}'
```

### Coqui TTS
```bash
curl -X POST https://your-modal-url/tts \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello, this is a test of the text-to-speech system."}'
```

### EchoMimic V2 Avatar
```bash
curl -X POST https://your-modal-url/api_avatar \
  -H "Content-Type: application/json" \
  -d '{"image_base64": "base64_image_data", "audio_base64": "base64_audio_data"}'
```

### Whisper STT
```bash
curl -X POST https://your-modal-url/whisper \
  -H "Content-Type: application/json" \
  -d '{"audio_base64": "base64_audio_data", "output_format": "srt"}'
```

### Marp Slides
```bash
curl -X POST https://your-modal-url/slides \
  -H "Content-Type: application/json" \
  -d '{"markdown_content": "# Test Slide\n\nThis is a test.", "output_format": "pdf"}'
```

### FFmpeg Processing
```bash
curl -X POST https://your-modal-url/ffmpeg \
  -H "Content-Type: application/json" \
  -d '{"operation": "merge", "video_base64": "video_data", "audio_base64": "audio_data"}'
```

## 🔄 Workflow Integration

After Modal is working, update the workflow services:

### Traditional Course Workflow
- Content generation via `/mistral`
- Voice synthesis via `/tts`
- Media gathering (external APIs)
- Slide generation via `/slides`
- Video assembly via `/ffmpeg`
- Subtitle generation via `/whisper`

### Avatar Course Workflow
- Content generation via `/mistral`
- Avatar processing (image upload)
- Voice synthesis via `/tts`
- Avatar video creation via `/api_avatar`
- Slide generation via `/slides` (optional)
- Video composition via `/ffmpeg`
- Subtitle generation via `/whisper`

## 🚀 Expected Performance

With the fixed deployment:
- **Traditional Workflow**: 2-5 minutes
- **Avatar Workflow**: 3-8 minutes
- **GPU Utilization**: Optimal with A100 80GB
- **Concurrent Requests**: Supported with `keep_warm=1`

## 🔍 Monitoring

### Check Modal Logs
```bash
modal logs courseai-a100-production
```

### Monitor GPU Usage
```bash
# Health check shows GPU memory usage
curl https://your-modal-url/health
```

### Performance Analytics
The workflow performance optimizer tracks:
- Request latency
- Success rates
- GPU memory usage
- Concurrent request handling

## 🆘 Troubleshooting

### Common Issues

1. **Authentication Error**
   - Run `modal token new`
   - Verify account has GPU access

2. **Deployment Fails**
   - Check Modal CLI version: `modal --version`
   - Verify Python dependencies
   - Check Modal dashboard for errors

3. **GPU Not Available**
   - Verify A100 GPU allocation in Modal dashboard
   - Check GPU quotas and limits
   - Try redeploying the app

4. **Service Timeouts**
   - Increase timeout values in Modal functions
   - Check GPU memory usage
   - Verify model loading

5. **Memory Issues**
   - Monitor GPU memory in health check
   - Ensure proper cleanup with `torch.cuda.empty_cache()`
   - Reduce batch sizes if needed

## 📞 Support

If issues persist:
1. Check Modal documentation: https://modal.com/docs
2. Review Modal logs for detailed errors
3. Test individual services separately
4. Contact Modal support for GPU-specific issues

---

**Next Steps**: Run `modal token new` to authenticate, then deploy the fixed script with `modal deploy modal_a100_comprehensive_fixed.py`
