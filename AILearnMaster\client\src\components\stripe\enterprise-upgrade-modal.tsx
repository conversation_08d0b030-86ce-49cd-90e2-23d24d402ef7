import { useState } from 'react';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Crown, Check, Loader2, CreditCard } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

// Initialize Stripe outside component to avoid recreation
if (!import.meta.env.VITE_STRIPE_PUBLIC_KEY) {
  throw new Error('Missing required Stripe key: VITE_STRIPE_PUBLIC_KEY');
}
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

interface EnterpriseUpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const CheckoutForm = ({ onSuccess }: { onSuccess?: () => void }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/dashboard?upgrade=success`,
        },
      });

      if (error) {
        toast({
          title: "Payment Failed",
          description: error.message || "An error occurred during payment processing",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Upgrade Successful!",
          description: "Welcome to Enterprise! Your account has been upgraded.",
        });
        onSuccess?.();
      }
    } catch (error) {
      toast({
        title: "Payment Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border">
        <PaymentElement 
          options={{
            layout: 'tabs',
            defaultValues: {
              billingDetails: {
                name: '',
                email: '',
              }
            }
          }}
        />
      </div>
      
      <Button 
        type="submit" 
        disabled={!stripe || isProcessing}
        className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
        size="lg"
      >
        {isProcessing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing Payment...
          </>
        ) : (
          <>
            <CreditCard className="mr-2 h-4 w-4" />
            Upgrade to Enterprise - $299/month
          </>
        )}
      </Button>
      
      <p className="text-xs text-gray-500 text-center">
        Secure payment processing by Stripe. Cancel anytime.
      </p>
    </form>
  );
};

export default function EnterpriseUpgradeModal({ isOpen, onClose, onSuccess }: EnterpriseUpgradeModalProps) {
  const [clientSecret, setClientSecret] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'plan' | 'payment'>('plan');
  const { toast } = useToast();

  // Debug logging
  console.log('EnterpriseUpgradeModal rendered with isOpen:', isOpen);

  const enterpriseFeatures = [
    "Unlimited Courses & Content Creation",
    "200+ AI Credits Monthly",
    "Advanced Video Meetings & Recording",
    "Unlimited Teams & Collaboration",
    "Shared Courses & Role Management",
    "Advanced Course Analytics",
    "Priority AI Processing & Support",
    "White-label Branding & Custom Domain",
    "Custom Integrations & API Access",
    "Advanced Security & Compliance",
    "Priority Customer Support",
    "Live Training & Onboarding"
  ];

  const handleStartPayment = async () => {
    setIsLoading(true);
    
    try {
      const response = await apiRequest("POST", "/api/create-enterprise-subscription");
      const data = await response.json();
      
      if (data.clientSecret) {
        setClientSecret(data.clientSecret);
        setStep('payment');
      } else {
        throw new Error('Failed to initialize payment');
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
      toast({
        title: "Setup Error",
        description: "Failed to initialize payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccess = () => {
    onSuccess?.();
    onClose();
    // Refresh the page to update user plan
    window.location.reload();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-500" />
            Upgrade to Enterprise
          </DialogTitle>
          <DialogDescription>
            {step === 'plan' 
              ? "Unlock all premium features and advanced collaboration tools"
              : "Complete your Enterprise upgrade with secure payment"
            }
          </DialogDescription>
        </DialogHeader>

        {step === 'plan' ? (
          <div className="space-y-6">
            {/* Plan Details */}
            <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-2">
                  <Crown className="h-8 w-8 text-yellow-500" />
                </div>
                <CardTitle className="text-2xl">Enterprise Plan</CardTitle>
                <div className="flex items-center justify-center gap-2">
                  <span className="text-3xl font-bold">$299</span>
                  <span className="text-gray-600">/month</span>
                  <Badge variant="secondary">Most Popular</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {enterpriseFeatures.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Call to Action */}
            <div className="space-y-4">
              <Button 
                onClick={handleStartPayment}
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting up payment...
                  </>
                ) : (
                  <>
                    <Crown className="mr-2 h-4 w-4" />
                    Start Enterprise Upgrade
                  </>
                )}
              </Button>
              
              <Button 
                variant="outline"
                onClick={() => window.open('/pricing', '_blank')}
                className="w-full"
                size="lg"
              >
                View Full Pricing Page
              </Button>
              
              <div className="text-center space-y-2">
                <p className="text-xs text-gray-500">
                  Cancel anytime. No long-term contracts.
                </p>
              </div>
            </div>
          </div>
        ) : clientSecret ? (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Elements 
                  stripe={stripePromise} 
                  options={{ 
                    clientSecret,
                    appearance: {
                      theme: 'stripe',
                      variables: {
                        colorPrimary: '#3b82f6',
                      }
                    }
                  }}
                >
                  <CheckoutForm onSuccess={handleSuccess} />
                </Elements>
              </CardContent>
            </Card>
            
            <Button 
              variant="outline" 
              onClick={() => setStep('plan')}
              className="w-full"
            >
              Back to Plan Details
            </Button>
          </div>
        ) : (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}