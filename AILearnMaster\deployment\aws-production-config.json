{"deployment": {"name": "AILearnMaster Production", "environment": "production", "region": "us-east-1", "version": "1.0.0"}, "amplify": {"appName": "ailearn-master-prod", "framework": "React", "platform": "Web", "repository": {"provider": "GitHub", "name": "AILearnMaster", "branch": "main"}, "buildSettings": {"buildSpec": "amplify.yml"}, "environmentVariables": {"NODE_ENV": "production", "AMPLIFY_DIFF_DEPLOY": "false", "AMPLIFY_MONOREPO_APP_ROOT": "."}}, "domain": {"domainName": "ailearn.com", "subdomains": [{"prefix": "www", "branchName": "main"}, {"prefix": "api", "branchName": "main"}], "certificateSettings": {"type": "AMPLIFY_MANAGED", "certificateVerificationDNSRecord": "auto"}}, "s3": {"buckets": [{"name": "ailearn-master-storage-prod", "purpose": "Course files and user uploads", "versioning": true, "encryption": "AES256", "publicAccess": false, "cors": {"allowedOrigins": ["https://ailearn.com", "https://www.ailearn.com"], "allowedMethods": ["GET", "POST", "PUT", "DELETE"], "allowedHeaders": ["*"], "maxAge": 3000}}, {"name": "ailearn-master-backups-prod", "purpose": "Database and application backups", "versioning": true, "encryption": "AES256", "publicAccess": false, "lifecycle": {"transitionToIA": 30, "transitionToGlacier": 90, "expiration": 2555}}]}, "cloudfront": {"distributions": [{"name": "ailearn-master-cdn", "origins": [{"domainName": "ailearn-master-storage-prod.s3.amazonaws.com", "originPath": "/public", "s3OriginConfig": {"originAccessIdentity": "origin-access-identity/cloudfront/ABCDEFG1234567"}}], "defaultCacheBehavior": {"targetOriginId": "S3-ailearn-master-storage-prod", "viewerProtocolPolicy": "redirect-to-https", "compress": true, "cachePolicyId": "managed-caching-optimized"}, "priceClass": "PriceClass_100", "enabled": true, "comment": "AILearnMaster CDN for course content delivery"}]}, "iam": {"roles": [{"name": "AILearnMaster-AmplifyRole", "assumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "amplify.amazonaws.com"}, "Action": "sts:<PERSON><PERSON>Role"}]}, "policies": ["arn:aws:iam::aws:policy/AdministratorAccess-Amplify"]}, {"name": "AILearnMaster-S3AccessRole", "policies": [{"name": "S3AccessPolicy", "document": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:ListBucket"], "Resource": ["arn:aws:s3:::ailearn-master-storage-prod", "arn:aws:s3:::ailearn-master-storage-prod/*"]}]}}]}]}, "secretsManager": {"secrets": [{"name": "ailearn-master/production/database", "description": "Production database credentials", "secretString": {"DATABASE_URL": "postgresql://username:password@host:port/database?sslmode=require"}}, {"name": "a<PERSON><PERSON>n-master/production/session", "description": "Session and JWT secrets", "secretString": {"SESSION_SECRET": "your-super-secure-session-secret-32-chars-minimum", "JWT_SECRET": "your-jwt-secret-32-chars-minimum", "SECRETS_ENCRYPTION_KEY": "your-encryption-key-for-secrets-32-chars-minimum"}}, {"name": "ailearn-master/production/ai-services", "description": "AI service API keys", "secretString": {"OPENAI_API_KEY": "sk-your-openai-api-key", "MODAL_TOKEN_ID": "your-modal-token-id", "MODAL_TOKEN_SECRET": "your-modal-token-secret", "ELEVENLABS_API_KEY": "your-elevenlabs-api-key"}}, {"name": "ailearn-master/production/aws-services", "description": "AWS service credentials", "secretString": {"AWS_ACCESS_KEY_ID": "your-aws-access-key-id", "AWS_SECRET_ACCESS_KEY": "your-aws-secret-access-key", "AWS_S3_BUCKET": "ailearn-master-storage-prod", "AWS_CLOUDFRONT_DOMAIN": "your-cloudfront-domain.cloudfront.net"}}]}, "monitoring": {"cloudwatch": {"logGroups": [{"name": "/aws/amplify/ailearn-master-prod", "retentionInDays": 30}], "alarms": [{"name": "AILearnMaster-HighErrorRate", "metricName": "4XXError", "threshold": 10, "comparisonOperator": "GreaterThanThreshold", "evaluationPeriods": 2, "period": 300}, {"name": "AILearnMaster-HighLatency", "metricName": "OriginLatency", "threshold": 5000, "comparisonOperator": "GreaterThanThreshold", "evaluationPeriods": 2, "period": 300}]}}, "security": {"waf": {"webAcl": {"name": "AILearnMaster-WebACL", "scope": "CLOUDFRONT", "rules": [{"name": "AWSManagedRulesCommonRuleSet", "priority": 1, "managedRuleGroupStatement": {"vendorName": "AWS", "name": "AWSManagedRulesCommonRuleSet"}}, {"name": "AWSManagedRulesKnownBadInputsRuleSet", "priority": 2, "managedRuleGroupStatement": {"vendorName": "AWS", "name": "AWSManagedRulesKnownBadInputsRuleSet"}}]}}}}