#!/usr/bin/env python3
"""
Debug Voice Discovery Response
"""

import requests
import json

def test_voice_discovery():
    """Test voice discovery endpoint and show full response"""
    url = "https://trade-digital--courseai-a100-simple-api-voices.modal.run"
    
    print("🔍 Testing Enhanced Voice Discovery endpoint...")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=60)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Full Response:")
            print(json.dumps(data, indent=2))
            
            # Check different possible keys
            print(f"\nResponse Analysis:")
            print(f"  'success' key: {data.get('success', 'NOT FOUND')}")
            print(f"  'status' key: {data.get('status', 'NOT FOUND')}")
            print(f"  'voices' key: {len(data.get('voices', []))} voices found")
            print(f"  'total_count' key: {data.get('total_count', 'NOT FOUND')}")
            
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_voice_discovery()
