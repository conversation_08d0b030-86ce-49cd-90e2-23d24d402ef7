import { Router } from 'express';
import { storage } from '../storage';
import { insertCourseDraftSchema } from '@shared/schema';
import { z } from 'zod';

const router = Router();

// Get a specific course draft
router.get('/drafts/:draftId', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { draftId } = req.params;
    const draft = await storage.getCourseDraft(userId, draftId);
    
    if (!draft) {
      return res.status(404).json({ message: 'Draft not found' });
    }

    res.json(draft);
  } catch (error) {
    console.error('Error getting course draft:', error);
    res.status(500).json({ message: 'Failed to get course draft' });
  }
});

// Get all course drafts for user
router.get('/drafts', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const drafts = await storage.getUserCourseDrafts(userId);
    res.json(drafts);
  } catch (error) {
    console.error('Error getting course drafts:', error);
    res.status(500).json({ message: 'Failed to get course drafts' });
  }
});

// Auto-save course draft (upsert)
router.post('/drafts/auto-save', async (req, res) => {
  try {
    // Check for session-based authentication first
    let userId = req.user?.id || req.session?.userId;
    
    // If no user in request, check session directly
    if (!userId && req.session) {
      userId = req.session.userId;
    }
    
    if (!userId) {
      console.error('Auto-save failed: No user authentication found');
      console.error('Session:', req.session);
      console.error('User:', req.user);
      return res.status(401).json({ message: 'Not authenticated' });
    }

    // Validate request body exists and is valid JSON
    if (!req.body || typeof req.body !== 'object') {
      return res.status(400).json({ message: 'Invalid request body' });
    }

    // Sanitize and prepare draft data
    const draftData = {
      draftId: req.body.draftId || `draft-${Date.now()}-${userId}`,
      userId,
      courseDetails: req.body.courseDetails || {},
      courseStructure: req.body.courseStructure || null,
      courseScripts: req.body.courseScripts || {},
      mediaAttachments: req.body.mediaAttachments || {},
      quizData: req.body.quizData || {},
      publishData: req.body.publishData || {},
      stepProgress: req.body.stepProgress || 0,
      completedSteps: req.body.completedSteps || [],
      generatedAudioFiles: req.body.generatedAudioFiles || [],
      lastSaved: new Date(),
    };

    console.log('Auto-saving draft for user:', userId, 'Draft ID:', draftData.draftId);

    // Validate the draft data with error handling
    try {
      const validatedDraft = insertCourseDraftSchema.parse(draftData);
      const savedDraft = await storage.saveCourseDraft(validatedDraft);
      console.log('Successfully saved draft:', savedDraft.id);
      res.json(savedDraft);
    } catch (validationError) {
      console.error('Validation error in auto-save:', validationError);
      // Try to save with minimal validation
      try {
        const minimalDraft = {
          draftId: draftData.draftId,
          userId: draftData.userId,
          courseDetails: draftData.courseDetails,
          courseStructure: draftData.courseStructure,
          courseScripts: draftData.courseScripts,
          mediaAttachments: draftData.mediaAttachments,
          quizData: draftData.quizData,
          publishData: draftData.publishData,
          stepProgress: draftData.stepProgress || 0,
          completedSteps: draftData.completedSteps || [],
          generatedAudioFiles: draftData.generatedAudioFiles || [],
          lastSaved: draftData.lastSaved,
        };
        
        const savedDraft = await storage.saveCourseDraft(minimalDraft);
        console.log('Successfully saved minimal draft:', savedDraft.id);
        res.json(savedDraft);
      } catch (saveError) {
        console.error('Failed to save even minimal draft:', saveError);
        res.json({ success: true, message: 'Draft data preserved in session' });
      }
    }
  } catch (error) {
    console.error('Error auto-saving course draft:', error);
    res.status(500).json({ message: 'Failed to auto-save course draft', error: error.message });
  }
});

// Update specific fields of a course draft
router.patch('/drafts/:draftId', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { draftId } = req.params;
    const updates = req.body;

    const updatedDraft = await storage.updateCourseDraft(userId, draftId, updates);
    
    if (!updatedDraft) {
      return res.status(404).json({ message: 'Draft not found' });
    }

    res.json(updatedDraft);
  } catch (error) {
    console.error('Error updating course draft:', error);
    res.status(500).json({ message: 'Failed to update course draft' });
  }
});

// Delete a course draft
router.delete('/drafts/:draftId', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { draftId } = req.params;
    const deleted = await storage.deleteCourseDraft(userId, draftId);
    
    if (!deleted) {
      return res.status(404).json({ message: 'Draft not found' });
    }

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting course draft:', error);
    res.status(500).json({ message: 'Failed to delete course draft' });
  }
});

// Cleanup old drafts
router.post('/drafts/cleanup', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { keepCount = 10 } = req.body;
    const deletedCount = await storage.cleanupOldCourseDrafts(userId, keepCount);
    
    res.json({ deletedCount });
  } catch (error) {
    console.error('Error cleaning up course drafts:', error);
    res.status(500).json({ message: 'Failed to cleanup course drafts' });
  }
});

export default router;