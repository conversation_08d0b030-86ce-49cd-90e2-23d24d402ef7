import { Request, Response, NextFunction } from 'express';
import { storage } from '../storage';

/**
 * Session-based authentication middleware for course creation
 * Ensures user sessions persist throughout the course creation workflow
 */
export async function sessionAuth(req: Request, res: Response, next: NextFunction) {
  try {
    // Check for existing session
    const userId = (req.session as any)?.userId;
    
    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated - please log in' });
    }

    // Verify user still exists in database
    const user = await storage.getUser(userId);
    if (!user) {
      // Clear invalid session
      req.session?.destroy((err) => {
        if (err) console.error('Session destroy error:', err);
      });
      return res.status(401).json({ message: 'Invalid session - please log in again' });
    }

    // Attach user to request for downstream handlers
    (req as any).user = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role
    };

    next();
  } catch (error) {
    console.error('Session authentication error:', error);
    res.status(500).json({ message: 'Authentication service error' });
  }
}

/**
 * Optional authentication middleware - allows access without login but provides user context if available
 */
export async function optionalAuth(req: Request, res: Response, next: NextFunction) {
  try {
    const userId = (req.session as any)?.userId;
    
    if (userId) {
      const user = await storage.getUser(userId);
      if (user) {
        (req as any).user = {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role
        };
      }
    }

    next();
  } catch (error) {
    console.error('Optional auth error:', error);
    // Continue without authentication in case of error
    next();
  }
}