#!/bin/bash

# Production Deployment Validation Script
# Validates all aspects of the production deployment

set -e

echo "🔍 AILearnMaster Production Validation"
echo "====================================="

# Configuration
DOMAIN="ailearn.com"
API_DOMAIN="api.ailearn.com"
REGION="us-east-1"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

# Function to run test
run_test() {
    local test_name=$1
    local test_command=$2
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    print_status "Testing: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        print_success "✓ $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        print_error "✗ $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Function to test HTTP response
test_http() {
    local url=$1
    local expected_code=${2:-200}
    local description=$3
    
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
    
    if [ "$response_code" = "$expected_code" ]; then
        print_success "✓ $description (HTTP $response_code)"
        return 0
    else
        print_error "✗ $description (HTTP $response_code, expected $expected_code)"
        return 1
    fi
}

# Function to test security headers
test_security_headers() {
    local url=$1
    local description=$2
    
    print_status "Testing security headers for $description"
    
    local headers=$(curl -s -I "$url")
    local score=0
    local max_score=6
    
    # Check for security headers
    if echo "$headers" | grep -qi "strict-transport-security"; then
        print_success "  ✓ HSTS header present"
        score=$((score + 1))
    else
        print_error "  ✗ HSTS header missing"
    fi
    
    if echo "$headers" | grep -qi "content-security-policy"; then
        print_success "  ✓ CSP header present"
        score=$((score + 1))
    else
        print_error "  ✗ CSP header missing"
    fi
    
    if echo "$headers" | grep -qi "x-frame-options"; then
        print_success "  ✓ X-Frame-Options header present"
        score=$((score + 1))
    else
        print_error "  ✗ X-Frame-Options header missing"
    fi
    
    if echo "$headers" | grep -qi "x-content-type-options"; then
        print_success "  ✓ X-Content-Type-Options header present"
        score=$((score + 1))
    else
        print_error "  ✗ X-Content-Type-Options header missing"
    fi
    
    if echo "$headers" | grep -qi "referrer-policy"; then
        print_success "  ✓ Referrer-Policy header present"
        score=$((score + 1))
    else
        print_error "  ✗ Referrer-Policy header missing"
    fi
    
    if ! echo "$headers" | grep -qi "x-powered-by"; then
        print_success "  ✓ X-Powered-By header removed"
        score=$((score + 1))
    else
        print_error "  ✗ X-Powered-By header present (should be removed)"
    fi
    
    local percentage=$((score * 100 / max_score))
    if [ $percentage -ge 80 ]; then
        print_success "Security headers score: $percentage% ($score/$max_score)"
        return 0
    else
        print_error "Security headers score: $percentage% ($score/$max_score) - needs improvement"
        return 1
    fi
}

# Function to test SSL certificate
test_ssl_certificate() {
    local domain=$1
    local description=$2
    
    print_status "Testing SSL certificate for $description"
    
    local ssl_info=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        local not_after=$(echo "$ssl_info" | grep "notAfter" | cut -d= -f2)
        print_success "✓ SSL certificate valid until: $not_after"
        return 0
    else
        print_error "✗ SSL certificate validation failed"
        return 1
    fi
}

# Function to test database connectivity
test_database() {
    print_status "Testing database connectivity"
    
    if command -v npm >/dev/null 2>&1; then
        if npm run db:health >/dev/null 2>&1; then
            print_success "✓ Database connection successful"
            return 0
        else
            print_error "✗ Database connection failed"
            return 1
        fi
    else
        print_warning "⚠ npm not available, skipping database test"
        return 0
    fi
}

# Function to test Modal AI services
test_modal_services() {
    print_status "Testing Modal AI services"
    
    # Test Modal health endpoint (if available)
    if curl -s "https://$API_DOMAIN/api/modal/health" >/dev/null 2>&1; then
        print_success "✓ Modal AI services accessible"
        return 0
    else
        print_warning "⚠ Modal AI services test inconclusive"
        return 0
    fi
}

# Function to test S3 and CloudFront
test_s3_cloudfront() {
    print_status "Testing S3 and CloudFront"
    
    # Test S3 bucket access (indirect through API)
    if curl -s "https://$API_DOMAIN/api/storage/health" >/dev/null 2>&1; then
        print_success "✓ S3 storage accessible"
    else
        print_warning "⚠ S3 storage test inconclusive"
    fi
    
    # Test CloudFront (if configured)
    if [ -n "$AWS_CLOUDFRONT_DOMAIN" ]; then
        if curl -s "https://$AWS_CLOUDFRONT_DOMAIN" >/dev/null 2>&1; then
            print_success "✓ CloudFront CDN accessible"
        else
            print_warning "⚠ CloudFront CDN test inconclusive"
        fi
    fi
    
    return 0
}

# Function to test rate limiting
test_rate_limiting() {
    print_status "Testing rate limiting"
    
    local api_url="https://$API_DOMAIN/api/health"
    local requests=0
    local max_requests=10
    
    # Send multiple requests quickly
    for i in $(seq 1 $max_requests); do
        local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$api_url")
        if [ "$response_code" = "429" ]; then
            print_success "✓ Rate limiting active (got 429 after $i requests)"
            return 0
        fi
        requests=$((requests + 1))
        sleep 0.1
    done
    
    print_warning "⚠ Rate limiting test inconclusive (no 429 response in $max_requests requests)"
    return 0
}

# Main validation function
run_validation() {
    print_status "Starting production validation tests..."
    echo ""
    
    # Test 1: Frontend accessibility
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_http "https://$DOMAIN" 200 "Frontend accessibility"; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 2: API accessibility
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_http "https://$API_DOMAIN/api/health" 200 "API accessibility"; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 3: HTTPS redirect
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_http "http://$DOMAIN" 301 "HTTP to HTTPS redirect"; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 4: SSL certificates
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_ssl_certificate "$DOMAIN" "Frontend SSL"; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_ssl_certificate "$API_DOMAIN" "API SSL"; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 5: Security headers
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_security_headers "https://$DOMAIN" "Frontend"; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_security_headers "https://$API_DOMAIN" "API"; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 6: Database connectivity
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_database; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 7: Modal AI services
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_modal_services; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 8: S3 and CloudFront
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_s3_cloudfront; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test 9: Rate limiting
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if test_rate_limiting; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
}

# Function to display results
display_results() {
    echo ""
    echo "🏁 Validation Results"
    echo "===================="
    echo ""
    echo "Total Tests: $TOTAL_TESTS"
    echo "Passed: $TESTS_PASSED ✓"
    echo "Failed: $TESTS_FAILED ✗"
    
    local success_rate=$((TESTS_PASSED * 100 / TOTAL_TESTS))
    echo "Success Rate: $success_rate%"
    
    echo ""
    if [ $success_rate -ge 90 ]; then
        print_success "🎉 Excellent! Production deployment is highly successful"
    elif [ $success_rate -ge 80 ]; then
        print_success "✅ Good! Production deployment is successful with minor issues"
    elif [ $success_rate -ge 70 ]; then
        print_warning "⚠️ Fair! Production deployment has some issues that should be addressed"
    else
        print_error "❌ Poor! Production deployment has significant issues that need immediate attention"
    fi
    
    echo ""
    echo "Production URLs:"
    echo "• Frontend: https://$DOMAIN"
    echo "• API: https://$API_DOMAIN"
    echo "• Admin: https://$DOMAIN/admin"
    
    if [ $success_rate -lt 80 ]; then
        echo ""
        print_warning "Recommended actions:"
        echo "1. Review failed tests and address issues"
        echo "2. Check AWS Amplify build logs"
        echo "3. Verify environment variables are set correctly"
        echo "4. Ensure all secrets are properly configured"
        echo "5. Re-run validation after fixes"
    fi
}

# Main execution
main() {
    # Check dependencies
    if ! command -v curl >/dev/null 2>&1; then
        print_error "curl is required but not installed"
        exit 1
    fi
    
    if ! command -v openssl >/dev/null 2>&1; then
        print_error "openssl is required but not installed"
        exit 1
    fi
    
    # Run validation
    run_validation
    display_results
    
    # Exit with appropriate code
    if [ $TESTS_FAILED -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Run main function
main "$@"
