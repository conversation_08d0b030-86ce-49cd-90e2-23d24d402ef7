#!/usr/bin/env python3
"""
Modal CLI Installation and Setup for Replit Environment
"""

import subprocess
import sys
import os
import json
from pathlib import Path

def install_modal_direct():
    """Install Modal CLI directly using pip"""
    try:
        print("Installing Modal CLI...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "modal-client", "--user", "--quiet"
        ], check=True, capture_output=True, text=True)
        
        print("Modal CLI installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Installation failed: {e.stderr}")
        return False

def setup_modal_config():
    """Configure Modal with environment credentials"""
    try:
        modal_dir = Path.home() / ".modal"
        modal_dir.mkdir(exist_ok=True)
        
        token_id = os.getenv('MODAL_TOKEN_ID')
        token_secret = os.getenv('MODAL_TOKEN_SECRET')
        
        if not token_id or not token_secret:
            print("Modal credentials not found in environment")
            return False
        
        config = {
            "token_id": token_id,
            "token_secret": token_secret,
            "environment": "main"
        }
        
        config_file = modal_dir / "config.json"
        with open(config_file, "w") as f:
            json.dump(config, f, indent=2)
        
        print("Modal configuration created")
        return True
        
    except Exception as e:
        print(f"Configuration failed: {e}")
        return False

def test_modal_auth():
    """Test Modal authentication"""
    try:
        # Test authentication by importing and checking token
        import modal
        
        # Create a simple test
        app = modal.App("test-auth")
        
        @app.function()
        def hello():
            return "Hello from Modal!"
        
        print("Modal authentication verified")
        return True
        
    except Exception as e:
        print(f"Authentication test failed: {e}")
        return False

def main():
    """Main installation process"""
    print("Modal CLI Installation for A100 GPU Setup")
    print("=" * 45)
    
    # Install Modal CLI
    if not install_modal_direct():
        return False
    
    # Configure authentication
    if not setup_modal_config():
        return False
    
    # Test authentication
    if not test_modal_auth():
        print("Warning: Authentication test failed, but proceeding with deployment")
    
    print("\nModal CLI setup completed!")
    print("Ready for A100 GPU deployment")
    
    return True

if __name__ == "__main__":
    main()