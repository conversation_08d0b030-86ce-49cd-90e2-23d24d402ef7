import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Search, 
  Download, 
  Eye, 
  X,
  Plus,
  Image,
  Video,
  CheckCircle2,
  Filter,
  Grid3X3,
  List,
  ExternalLink,
  Play,
  RefreshCw,
  ChevronDown,
  SlidersHorizontal,
  Square,
  RectangleHorizontal,
  RectangleVertical,
  Heart,
  Check,
  Loader2,
  ZoomIn,
  ArrowUp,
  Palette
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface StockMedia {
  id: string;
  type: 'photo' | 'video';
  url: string;
  thumbnailUrl: string;
  title: string;
  tags: string[];
  photographer?: string;
  source: 'pexels' | 'pixabay';
  license: string;
  downloadUrl?: string;
  width?: number;
  height?: number;
  duration?: number;
  size?: string;
}

interface SearchFilters {
  orientation: 'all' | 'horizontal' | 'vertical' | 'square';
  category: string;
  minWidth: number;
  minHeight: number;
  color: string;
  safeSearch: boolean;
}

const orientationOptions = [
  { value: 'all', label: 'All Orientations', icon: Square },
  { value: 'horizontal', label: 'Landscape', icon: RectangleHorizontal },
  { value: 'vertical', label: 'Portrait', icon: RectangleVertical },
  { value: 'square', label: 'Square', icon: Square }
];

const categoryOptions = [
  'backgrounds', 'nature', 'people', 'animals', 'food', 'travel', 
  'technology', 'business', 'education', 'health', 'sports', 'music',
  'fashion', 'architecture', 'transportation', 'science'
];

const colorOptions = [
  { value: '', label: 'Any Color' },
  { value: 'grayscale', label: 'Grayscale' },
  { value: 'red', label: 'Red' },
  { value: 'orange', label: 'Orange' },
  { value: 'yellow', label: 'Yellow' },
  { value: 'green', label: 'Green' },
  { value: 'blue', label: 'Blue' },
  { value: 'purple', label: 'Purple' },
  { value: 'pink', label: 'Pink' },
  { value: 'white', label: 'White' },
  { value: 'gray', label: 'Gray' },
  { value: 'black', label: 'Black' },
  { value: 'brown', label: 'Brown' }
];

export default function EnhancedStockMedia() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const scrollRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'photos' | 'videos'>('photos');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    orientation: 'all',
    category: '',
    minWidth: 0,
    minHeight: 0,
    color: '',
    safeSearch: true
  });

  // Results state
  const [currentPage, setCurrentPage] = useState(1);
  const [allResults, setAllResults] = useState<StockMedia[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [previewItem, setPreviewItem] = useState<StockMedia | null>(null);
  const [isImporting, setIsImporting] = useState(false);

  // Search function with debouncing
  const [debouncedQuery, setDebouncedQuery] = useState(searchQuery);
  useEffect(() => {
    const timer = setTimeout(() => setDebouncedQuery(searchQuery), 500);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch stock media
  const { data: stockResults, isLoading, error } = useQuery({
    queryKey: ['/api/stock', activeTab, debouncedQuery, filters, currentPage],
    queryFn: async () => {
      if (!debouncedQuery.trim()) return { photos: [], videos: [], totalHits: 0 };
      
      const params = new URLSearchParams({
        q: debouncedQuery,
        page: currentPage.toString(),
        per_page: '40',
        orientation: filters.orientation,
        category: filters.category,
        min_width: filters.minWidth.toString(),
        min_height: filters.minHeight.toString(),
        color: filters.color,
        safesearch: filters.safeSearch.toString()
      });

      const endpoint = activeTab === 'photos' ? '/api/stock/photos' : '/api/stock/videos';
      const response = await fetch(`${endpoint}?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to search ${activeTab}`);
      }
      
      return response.json();
    },
    enabled: !!debouncedQuery.trim(),
    placeholderData: (previousData) => previousData
  });

  // Handle search results
  useEffect(() => {
    if (stockResults) {
      const newResults = activeTab === 'photos' ? stockResults.photos || [] : stockResults.videos || [];
      
      if (currentPage === 1) {
        setAllResults(newResults);
      } else {
        setAllResults(prev => [...prev, ...newResults]);
      }
    }
  }, [stockResults, currentPage, activeTab]);

  // Reset when changing tabs or search
  useEffect(() => {
    setCurrentPage(1);
    setAllResults([]);
    setSelectedItems(new Set());
  }, [activeTab, debouncedQuery, filters]);

  // Infinite scroll setup
  const lastItemRef = useCallback((node: HTMLDivElement | null) => {
    if (isLoading) return;
    if (observerRef.current) observerRef.current.disconnect();
    
    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && stockResults && allResults.length < stockResults.totalHits) {
        setCurrentPage(prev => prev + 1);
      }
    });
    
    if (node) observerRef.current.observe(node);
  }, [isLoading, stockResults, allResults.length]);

  // Import selected media
  const importMutation = useMutation({
    mutationFn: async (items: StockMedia[]) => {
      const response = await apiRequest('POST', '/api/stock/import', {
        items: items.map(item => ({
          id: item.id,
          type: item.type,
          url: item.url,
          thumbnailUrl: item.thumbnailUrl,
          title: item.title,
          tags: item.tags,
          photographer: item.photographer,
          source: item.source,
          license: item.license,
          width: item.width,
          height: item.height,
          duration: item.duration
        }))
      });
      return response;
    },
    onSuccess: () => {
      toast({
        title: "Media Imported Successfully",
        description: `${selectedItems.size} items added to your Media Library`,
      });
      setSelectedItems(new Set());
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
    },
    onError: (error: any) => {
      toast({
        title: "Import Failed",
        description: error.message || "Failed to import selected media",
        variant: "destructive",
      });
    }
  });

  const handleImportSelected = () => {
    const selectedMedia = allResults.filter(item => selectedItems.has(item.id));
    if (selectedMedia.length === 0) {
      toast({
        title: "No Items Selected",
        description: "Please select some media to import",
        variant: "destructive",
      });
      return;
    }
    setIsImporting(true);
    importMutation.mutate(selectedMedia);
    setTimeout(() => setIsImporting(false), 1000);
  };

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedItems.size === allResults.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(allResults.map(item => item.id)));
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for high-quality photos and videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <SlidersHorizontal className="h-4 w-4" />
            Filters
            <ChevronDown className={`h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </Button>
          <Button
            variant="outline"
            onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
          </Button>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Orientation</label>
                  <Select
                    value={filters.orientation}
                    onValueChange={(value: any) => setFilters(prev => ({ ...prev, orientation: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {orientationOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <option.icon className="h-4 w-4" />
                            {option.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Category</label>
                  <Select
                    value={filters.category}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Categories</SelectItem>
                      {categoryOptions.map(category => (
                        <SelectItem key={category} value={category}>
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Color</label>
                  <Select
                    value={filters.color}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, color: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorOptions.map(color => (
                        <SelectItem key={color.value} value={color.value}>
                          <div className="flex items-center gap-2">
                            {color.value && (
                              <div 
                                className="w-4 h-4 rounded border"
                                style={{ backgroundColor: color.value === 'grayscale' ? '#888' : color.value }}
                              />
                            )}
                            {color.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Minimum Size</label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Width"
                      value={filters.minWidth || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, minWidth: Number(e.target.value) || 0 }))}
                      className="text-xs"
                    />
                    <Input
                      type="number"
                      placeholder="Height"
                      value={filters.minHeight || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, minHeight: Number(e.target.value) || 0 }))}
                      className="text-xs"
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="safeSearch"
                    checked={filters.safeSearch}
                    onCheckedChange={(checked) => setFilters(prev => ({ ...prev, safeSearch: checked === true }))}
                  />
                  <label htmlFor="safeSearch" className="text-sm">Safe search</label>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setFilters({
                    orientation: 'all',
                    category: '',
                    minWidth: 0,
                    minHeight: 0,
                    color: '',
                    safeSearch: true
                  })}
                >
                  Reset Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Content Tabs */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)}>
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="photos" className="flex items-center gap-2">
              <Image className="h-4 w-4" />
              Photos
            </TabsTrigger>
            <TabsTrigger value="videos" className="flex items-center gap-2">
              <Video className="h-4 w-4" />
              Videos
            </TabsTrigger>
          </TabsList>

          {/* Selection Controls */}
          {allResults.length > 0 && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                className="text-xs"
              >
                {selectedItems.size === allResults.length ? 'Deselect All' : 'Select All'}
              </Button>
              {selectedItems.size > 0 && (
                <Button
                  onClick={handleImportSelected}
                  disabled={importMutation.isPending || isImporting}
                  className="text-xs"
                >
                  {importMutation.isPending || isImporting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Importing...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Import Selected ({selectedItems.size})
                    </>
                  )}
                </Button>
              )}
            </div>
          )}
        </div>

        <TabsContent value="photos" className="space-y-4">
          <MediaGrid
            items={allResults}
            selectedItems={selectedItems}
            onSelectItem={handleSelectItem}
            onPreview={setPreviewItem}
            isLoading={isLoading}
            viewMode={viewMode}
            lastItemRef={lastItemRef}
            type="photo"
          />
        </TabsContent>

        <TabsContent value="videos" className="space-y-4">
          <MediaGrid
            items={allResults}
            selectedItems={selectedItems}
            onSelectItem={handleSelectItem}
            onPreview={setPreviewItem}
            isLoading={isLoading}
            viewMode={viewMode}
            lastItemRef={lastItemRef}
            type="video"
          />
        </TabsContent>
      </Tabs>

      {/* Results Info */}
      {!isLoading && allResults.length === 0 && debouncedQuery && (
        <div className="text-center py-12">
          <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No results found</h3>
          <p className="text-muted-foreground">
            Try adjusting your search terms or filters
          </p>
        </div>
      )}

      {!debouncedQuery && (
        <div className="text-center py-12">
          <Image className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Search for Stock Media</h3>
          <p className="text-muted-foreground">
            Enter keywords to find professional photos and videos from Pexels and Pixabay
          </p>
        </div>
      )}

      {/* Preview Modal */}
      <MediaPreview
        item={previewItem}
        onClose={() => setPreviewItem(null)}
        onImport={(item) => {
          setSelectedItems(prev => new Set([...prev, item.id]));
          setPreviewItem(null);
        }}
      />
    </div>
  );
}

// Media Grid Component
interface MediaGridProps {
  items: StockMedia[];
  selectedItems: Set<string>;
  onSelectItem: (id: string) => void;
  onPreview: (item: StockMedia) => void;
  isLoading: boolean;
  viewMode: 'grid' | 'list';
  lastItemRef: (node: HTMLDivElement | null) => void;
  type: 'photo' | 'video';
}

function MediaGrid({ 
  items, 
  selectedItems, 
  onSelectItem, 
  onPreview, 
  isLoading, 
  viewMode,
  lastItemRef,
  type 
}: MediaGridProps) {
  if (viewMode === 'grid') {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {items.map((item, index) => (
          <div
            key={item.id}
            ref={index === items.length - 1 ? lastItemRef : null}
            className="group relative aspect-square rounded-lg overflow-hidden border hover:border-primary transition-colors cursor-pointer"
            onClick={() => onPreview(item)}
          >
            <img
              src={item.thumbnailUrl}
              alt={item.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
            
            {/* Selection Checkbox */}
            <div className="absolute top-2 left-2">
              <Checkbox
                checked={selectedItems.has(item.id)}
                onCheckedChange={() => onSelectItem(item.id)}
                onClick={(e) => e.stopPropagation()}
                className="bg-white border-white shadow-md"
              />
            </div>

            {/* Type Badge */}
            <div className="absolute top-2 right-2">
              <Badge variant="secondary" className="bg-black/70 text-white text-xs">
                {type === 'video' && item.duration && formatDuration(item.duration)}
                {type === 'photo' && <Image className="h-3 w-3" />}
                {type === 'video' && <Video className="h-3 w-3" />}
              </Badge>
            </div>

            {/* Hover Overlay */}
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <Button variant="secondary" size="sm">
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
            </div>

            {/* Source Badge */}
            <div className="absolute bottom-2 left-2">
              <Badge variant="outline" className="bg-white/90 text-xs">
                {item.source}
              </Badge>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="col-span-full flex justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin" />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {items.map((item, index) => (
        <Card
          key={item.id}
          ref={index === items.length - 1 ? lastItemRef : null}
          className="cursor-pointer hover:border-primary transition-colors"
          onClick={() => onPreview(item)}
        >
          <CardContent className="p-4">
            <div className="flex gap-4">
              <div className="relative w-24 h-24 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  src={item.thumbnailUrl}
                  alt={item.title}
                  className="w-full h-full object-cover"
                />
                {type === 'video' && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Play className="h-6 w-6 text-white drop-shadow-lg" />
                  </div>
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1 min-w-0">
                    <h4 className="font-medium truncate">{item.title}</h4>
                    {item.photographer && (
                      <p className="text-sm text-muted-foreground">by {item.photographer}</p>
                    )}
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <span>{item.width} × {item.height}</span>
                      {type === 'video' && item.duration && (
                        <>
                          <span>•</span>
                          <span>{formatDuration(item.duration)}</span>
                        </>
                      )}
                      <span>•</span>
                      <Badge variant="outline" className="text-xs">
                        {item.source}
                      </Badge>
                    </div>
                  </div>
                  
                  <Checkbox
                    checked={selectedItems.has(item.id)}
                    onCheckedChange={() => onSelectItem(item.id)}
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Preview Modal Component
interface MediaPreviewProps {
  item: StockMedia | null;
  onClose: () => void;
  onImport: (item: StockMedia) => void;
}

function MediaPreview({ item, onClose, onImport }: MediaPreviewProps) {
  if (!item) return null;

  return (
    <Dialog open={!!item} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">{item.title}</h3>
              {item.photographer && (
                <p className="text-sm text-muted-foreground">by {item.photographer}</p>
              )}
            </div>
            <Button onClick={onClose} variant="outline" size="sm">
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="relative rounded-lg overflow-hidden bg-gray-100">
            {item.type === 'photo' ? (
              <img
                src={item.url}
                alt={item.title}
                className="w-full max-h-96 object-contain"
              />
            ) : (
              <video
                src={item.url}
                controls
                className="w-full max-h-96"
                poster={item.thumbnailUrl}
              />
            )}
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Dimensions:</span> {item.width} × {item.height}
            </div>
            <div>
              <span className="font-medium">Source:</span> {item.source}
            </div>
            {item.duration && (
              <div>
                <span className="font-medium">Duration:</span> {formatDuration(item.duration)}
              </div>
            )}
            <div>
              <span className="font-medium">License:</span> {item.license}
            </div>
          </div>

          {item.tags && item.tags.length > 0 && (
            <div>
              <span className="font-medium text-sm">Tags:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {item.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={() => onImport(item)} className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              Import to Library
            </Button>
            <Button variant="outline" asChild>
              <a href={item.url} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                View Original
              </a>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function formatDuration(seconds: number) {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}