import { spawn } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

export interface CoquiVoiceModel {
  id: string;
  name: string;
  language: string;
  gender?: string;
  style?: string;
  quality: 'high' | 'medium' | 'basic';
  description: string;
  category: 'single-speaker' | 'multi-speaker' | 'multilingual';
  dataset?: string;
  architecture?: string;
}

interface VoiceDiscoveryCache {
  models: CoquiVoiceModel[];
  lastUpdated: number;
  ttl: number; // Time to live in milliseconds
}

class CoquiVoiceDiscoveryService {
  private cache: VoiceDiscoveryCache | null = null;
  private readonly CACHE_TTL = 24 * 60 * 60 * 1000; // 24 hours
  private readonly CACHE_FILE = path.join(process.cwd(), 'temp', 'coqui-voices-cache.json');
  private isDiscovering = false;

  constructor() {
    this.ensureCacheDir();
    this.loadCacheFromFile();
  }

  private ensureCacheDir() {
    const cacheDir = path.dirname(this.CACHE_FILE);
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true });
    }
  }

  private loadCacheFromFile() {
    try {
      if (fs.existsSync(this.CACHE_FILE)) {
        const cacheData = JSON.parse(fs.readFileSync(this.CACHE_FILE, 'utf8'));
        if (this.isCacheValid(cacheData)) {
          this.cache = cacheData;
          console.log(`Loaded ${this.cache?.models.length} Coqui voices from cache`);
        }
      }
    } catch (error) {
      console.warn('Failed to load voice cache:', error);
    }
  }

  private saveCacheToFile() {
    try {
      if (this.cache) {
        fs.writeFileSync(this.CACHE_FILE, JSON.stringify(this.cache, null, 2));
      }
    } catch (error) {
      console.warn('Failed to save voice cache:', error);
    }
  }

  private isCacheValid(cache: any): boolean {
    return cache && 
           cache.models && 
           Array.isArray(cache.models) && 
           cache.lastUpdated && 
           cache.ttl &&
           (Date.now() - cache.lastUpdated) < cache.ttl;
  }

  /**
   * Discover all available Coqui TTS models using the TTS CLI
   */
  private async discoverModelsViaCLI(): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const process = spawn('python', ['-c', `
from TTS.api import TTS
import json
try:
    tts = TTS()
    models = tts.list_models()
    print(json.dumps(models))
except Exception as e:
    print(json.dumps({"error": str(e)}))
`], {
        stdio: 'pipe',
        env: { ...process.env }
      });

      let output = '';
      let errorOutput = '';

      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output.trim());
            if (result.error) {
              reject(new Error(result.error));
            } else {
              resolve(result);
            }
          } catch (parseError) {
            reject(new Error(`Failed to parse TTS models output: ${parseError}`));
          }
        } else {
          reject(new Error(`TTS CLI failed with code ${code}: ${errorOutput}`));
        }
      });

      process.on('error', (error) => {
        reject(new Error(`Failed to spawn TTS process: ${error.message}`));
      });
    });
  }

  /**
   * Parse model ID to extract metadata
   */
  private parseModelMetadata(modelId: string): Partial<CoquiVoiceModel> {
    const parts = modelId.split('/');
    const metadata: Partial<CoquiVoiceModel> = {
      id: modelId,
      category: 'single-speaker',
      quality: 'medium'
    };

    // Extract language
    if (parts.length >= 3) {
      metadata.language = parts[1];
    }

    // Extract dataset and architecture
    if (parts.length >= 4) {
      metadata.dataset = parts[2];
      if (parts.length >= 5) {
        metadata.architecture = parts[3];
      }
    }

    // Determine category and quality based on patterns
    if (modelId.includes('multi') || modelId.includes('vctk') || modelId.includes('your_tts')) {
      metadata.category = 'multi-speaker';
    }
    
    if (modelId.includes('multilingual')) {
      metadata.category = 'multilingual';
    }

    if (modelId.includes('glow') || modelId.includes('tacotron2')) {
      metadata.quality = 'high';
    }

    if (modelId.includes('speedy') || modelId.includes('fast')) {
      metadata.quality = 'basic';
    }

    return metadata;
  }

  /**
   * Generate human-readable name and description for a model
   */
  private generateModelInfo(modelId: string, metadata: Partial<CoquiVoiceModel>): { name: string; description: string } {
    const parts = modelId.split('/');
    let name = modelId;
    let description = `Coqui TTS model: ${modelId}`;

    // Generate better names based on common patterns
    if (metadata.dataset) {
      switch (metadata.dataset.toLowerCase()) {
        case 'ljspeech':
          name = `LJSpeech ${metadata.architecture ? metadata.architecture.toUpperCase() : 'Model'}`;
          description = 'Clear, professional female voice with neutral American accent';
          metadata.gender = 'female';
          break;
        case 'vctk':
          name = `VCTK Multi-Speaker ${metadata.architecture ? metadata.architecture.toUpperCase() : 'Model'}`;
          description = 'Multi-speaker model with various British accents and styles';
          metadata.gender = 'mixed';
          break;
        case 'sam':
          name = `SAM ${metadata.architecture ? metadata.architecture.toUpperCase() : 'Model'}`;
          description = 'Multi-speaker American English model';
          metadata.gender = 'mixed';
          break;
        case 'multi-dataset':
          name = `Multi-Dataset ${metadata.architecture ? metadata.architecture.toUpperCase() : 'Model'}`;
          description = 'Multilingual model trained on multiple datasets';
          metadata.gender = 'mixed';
          break;
        default:
          name = `${metadata.dataset.charAt(0).toUpperCase() + metadata.dataset.slice(1)} Model`;
      }
    }

    // Add language info to name if not English
    if (metadata.language && metadata.language !== 'en') {
      const languageNames: { [key: string]: string } = {
        'fr': 'French',
        'de': 'German',
        'es': 'Spanish',
        'it': 'Italian',
        'pt': 'Portuguese',
        'ja': 'Japanese',
        'zh': 'Chinese',
        'ru': 'Russian',
        'nl': 'Dutch',
        'tr': 'Turkish'
      };
      const langName = languageNames[metadata.language] || metadata.language.toUpperCase();
      name = `${langName} - ${name}`;
    }

    return { name, description };
  }

  /**
   * Get all available Coqui TTS voices with caching
   */
  async getAllVoices(): Promise<CoquiVoiceModel[]> {
    // Return cached results if valid
    if (this.cache && this.isCacheValid(this.cache)) {
      return this.cache.models;
    }

    // Prevent multiple simultaneous discovery attempts
    if (this.isDiscovering) {
      // Wait for ongoing discovery to complete
      while (this.isDiscovering) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.cache?.models || [];
    }

    this.isDiscovering = true;

    try {
      console.log('Discovering Coqui TTS models...');
      const modelIds = await this.discoverModelsViaCLI();
      
      const models: CoquiVoiceModel[] = modelIds.map(modelId => {
        const metadata = this.parseModelMetadata(modelId);
        const { name, description } = this.generateModelInfo(modelId, metadata);
        
        return {
          id: modelId,
          name,
          language: metadata.language || 'en',
          gender: metadata.gender || 'unknown',
          style: metadata.style || 'neutral',
          quality: metadata.quality || 'medium',
          description,
          category: metadata.category || 'single-speaker',
          dataset: metadata.dataset,
          architecture: metadata.architecture
        };
      });

      // Update cache
      this.cache = {
        models,
        lastUpdated: Date.now(),
        ttl: this.CACHE_TTL
      };

      this.saveCacheToFile();
      console.log(`Discovered ${models.length} Coqui TTS models`);
      
      return models;
    } catch (error) {
      console.error('Failed to discover Coqui TTS models:', error);
      
      // Return fallback models if discovery fails
      return this.getFallbackModels();
    } finally {
      this.isDiscovering = false;
    }
  }

  /**
   * Get fallback models when discovery fails
   */
  private getFallbackModels(): CoquiVoiceModel[] {
    return [
      {
        id: 'tts_models/en/ljspeech/tacotron2-DDC',
        name: 'LJSpeech Tacotron2',
        language: 'en',
        gender: 'female',
        style: 'neutral',
        quality: 'high',
        description: 'Clear, professional female voice with neutral American accent',
        category: 'single-speaker',
        dataset: 'ljspeech',
        architecture: 'tacotron2-DDC'
      },
      {
        id: 'tts_models/en/ljspeech/glow-tts',
        name: 'LJSpeech Glow-TTS',
        language: 'en',
        gender: 'female',
        style: 'expressive',
        quality: 'high',
        description: 'Expressive female voice with natural intonation',
        category: 'single-speaker',
        dataset: 'ljspeech',
        architecture: 'glow-tts'
      },
      {
        id: 'tts_models/en/vctk/vits',
        name: 'VCTK Multi-Speaker VITS',
        language: 'en',
        gender: 'mixed',
        style: 'varied',
        quality: 'high',
        description: 'Multi-speaker model with various British accents and styles',
        category: 'multi-speaker',
        dataset: 'vctk',
        architecture: 'vits'
      }
    ];
  }

  /**
   * Force refresh the voice cache
   */
  async refreshVoices(): Promise<CoquiVoiceModel[]> {
    this.cache = null;
    if (fs.existsSync(this.CACHE_FILE)) {
      fs.unlinkSync(this.CACHE_FILE);
    }
    return this.getAllVoices();
  }

  /**
   * Get voices filtered by language
   */
  async getVoicesByLanguage(language: string): Promise<CoquiVoiceModel[]> {
    const allVoices = await this.getAllVoices();
    return allVoices.filter(voice => voice.language === language);
  }

  /**
   * Get voices filtered by category
   */
  async getVoicesByCategory(category: string): Promise<CoquiVoiceModel[]> {
    const allVoices = await this.getAllVoices();
    return allVoices.filter(voice => voice.category === category);
  }

  /**
   * Test if a specific voice model is available and working
   */
  async testVoice(modelId: string): Promise<{ available: boolean; error?: string }> {
    return new Promise((resolve) => {
      const process = spawn('python', ['-c', `
from TTS.api import TTS
import tempfile
import os
try:
    tts = TTS("${modelId}")
    temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    tts.tts_to_file(text="Test", file_path=temp_file.name)
    os.unlink(temp_file.name)
    print("SUCCESS")
except Exception as e:
    print(f"ERROR: {str(e)}")
`], {
        stdio: 'pipe',
        env: { ...process.env }
      });

      let output = '';
      process.stdout.on('data', (data) => {
        output += data.toString();
      });

      process.on('close', () => {
        const result = output.trim();
        if (result === 'SUCCESS') {
          resolve({ available: true });
        } else {
          resolve({
            available: false,
            error: result.startsWith('ERROR:') ? result.substring(7) : result
          });
        }
      });

      process.on('error', (error) => {
        resolve({ available: false, error: error.message });
      });
    });
  }

  /**
   * Get voice statistics
   */
  async getVoiceStats(): Promise<{
    total: number;
    byLanguage: { [key: string]: number };
    byCategory: { [key: string]: number };
    byQuality: { [key: string]: number };
  }> {
    const voices = await this.getAllVoices();

    const stats = {
      total: voices.length,
      byLanguage: {} as { [key: string]: number },
      byCategory: {} as { [key: string]: number },
      byQuality: {} as { [key: string]: number }
    };

    voices.forEach(voice => {
      // Count by language
      stats.byLanguage[voice.language] = (stats.byLanguage[voice.language] || 0) + 1;

      // Count by category
      stats.byCategory[voice.category] = (stats.byCategory[voice.category] || 0) + 1;

      // Count by quality
      stats.byQuality[voice.quality] = (stats.byQuality[voice.quality] || 0) + 1;
    });

    return stats;
  }
}

// Export singleton instance
export const coquiVoiceDiscovery = new CoquiVoiceDiscoveryService();
