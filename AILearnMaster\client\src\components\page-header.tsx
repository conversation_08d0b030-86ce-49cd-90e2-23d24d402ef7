import React from "react";
import { cn } from "@/lib/utils";

interface PageHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  separator?: boolean;
}

function PageHeader({
  className,
  children,
  separator = true,
  ...props
}: PageHeaderProps) {
  return (
    <div
      className={cn(
        "flex flex-col items-start gap-1 pb-5",
        separator && "border-b",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

interface PageHeaderHeadingProps
  extends React.HTMLAttributes<HTMLHeadingElement> {}

function PageHeaderHeading({
  className,
  children,
  ...props
}: PageHeaderHeadingProps) {
  return (
    <h1
      className={cn(
        "text-2xl font-semibold tracking-tight sm:text-3xl",
        className
      )}
      {...props}
    >
      {children}
    </h1>
  );
}

interface PageHeaderDescriptionProps
  extends React.HTMLAttributes<HTMLParagraphElement> {}

function PageHeaderDescription({
  className,
  children,
  ...props
}: PageHeaderDescriptionProps) {
  return (
    <p
      className={cn("text-sm text-muted-foreground sm:text-base", className)}
      {...props}
    >
      {children}
    </p>
  );
}

export { PageHeader, PageHeaderHeading, PageHeaderDescription };