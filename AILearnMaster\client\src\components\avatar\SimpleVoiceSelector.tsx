import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, ArrowRight, Volume2 } from 'lucide-react';
import { VoiceServiceSelector } from '@/components/voice/VoiceServiceSelector';

interface SimpleVoiceSelectorProps {
  onSubmit: (data: any) => void;
  onBack: () => void;
  defaultValues?: any;
  scriptData: any;
}

export function SimpleVoiceSelector({ 
  onSubmit, 
  onBack, 
  defaultValues,
  scriptData 
}: SimpleVoiceSelectorProps) {
  const [voiceSettings, setVoiceSettings] = useState<any>(null);

  const handleVoiceSelect = (settings: any) => {
    setVoiceSettings(settings);
  };

  const handleSubmit = () => {
    if (voiceSettings) {
      onSubmit(voiceSettings);
    }
  };

  const sampleText = scriptData?.segments?.[0]?.content || 
    scriptData?.courseScript?.substring(0, 200) || 
    "Welcome to this avatar course. This is a preview of how your AI presenter will sound.";

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold">Voice Settings</h2>
          <p className="text-muted-foreground">Choose your preferred voice service and settings for avatar course generation</p>
        </div>
        <Badge variant="outline" className="flex items-center gap-1">
          <Volume2 className="h-3 w-3" />
          Avatar Voice Generation
        </Badge>
      </div>

      <VoiceServiceSelector
        onVoiceSelect={handleVoiceSelect}
        sampleText={sampleText}
        defaultService="chatterbox"
        showPreview={true}
      />

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button onClick={onBack} variant="outline">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <Button 
          onClick={handleSubmit} 
          disabled={!voiceSettings}
          className="flex items-center gap-2"
        >
          Continue to Preview
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}