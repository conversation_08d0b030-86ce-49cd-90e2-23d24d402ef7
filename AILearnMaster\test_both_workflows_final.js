#!/usr/bin/env node

/**
 * Final Comprehensive Test: Both Course Creation Workflows
 * Tests Traditional and Avatar course creation workflows end-to-end
 * Powered by Mistral AI on Modal GPU infrastructure with intelligent fallbacks
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

// Test data for workflows
const traditionalCourseData = {
  title: "JavaScript Fundamentals",
  description: "Learn the basics of JavaScript programming",
  category: "technology",
  difficulty: "beginner",
  voiceProvider: "coqui",
  voiceId: "default"
};

const avatarCourseData = {
  title: "Python for Data Science",
  description: "Master Python for data analysis and machine learning",
  category: "technology", 
  difficulty: "intermediate",
  voiceProvider: "openai",
  voiceId: "alloy"
};

async function makeRequest(endpoint, method = 'GET', data = null) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  if (data && method !== 'GET') {
    options.body = JSON.stringify(data);
  }
  
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    return { success: response.ok, data: result, status: response.status };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function testTraditionalWorkflow() {
  console.log('\n🔥 TESTING TRADITIONAL COURSE WORKFLOW');
  console.log('=====================================');
  
  const result = await makeRequest('/api/workflows/traditional-course', 'POST', traditionalCourseData);
  
  if (result.success) {
    console.log('✅ Traditional workflow started successfully');
    console.log(`📋 Workflow ID: ${result.data.workflowId}`);
    console.log(`🔗 Status URL: ${result.data.statusUrl}`);
    
    // Monitor progress for a few seconds
    await monitorWorkflow(result.data.workflowId, 'Traditional');
    return result.data.workflowId;
  } else {
    console.log('❌ Traditional workflow failed to start:', result.error);
    return null;
  }
}

async function testAvatarWorkflow() {
  console.log('\n🎭 TESTING AVATAR COURSE WORKFLOW');
  console.log('=================================');
  
  const result = await makeRequest('/api/workflows/avatar-course-json', 'POST', avatarCourseData);
  
  if (result.success) {
    console.log('✅ Avatar workflow started successfully');
    console.log(`📋 Workflow ID: ${result.data.workflowId}`);
    console.log(`🔗 Status URL: ${result.data.statusUrl}`);
    
    // Monitor progress for a few seconds
    await monitorWorkflow(result.data.workflowId, 'Avatar');
    return result.data.workflowId;
  } else {
    console.log('❌ Avatar workflow failed to start:', result.error);
    return null;
  }
}

async function monitorWorkflow(workflowId, type) {
  console.log(`\n📊 Monitoring ${type} Workflow Progress:`);
  console.log('─'.repeat(40));
  
  for (let i = 0; i < 5; i++) {
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    
    const statusResult = await makeRequest(`/api/workflows/status/${workflowId}`);
    
    if (statusResult.success) {
      const status = statusResult.data;
      console.log(`⏱️  Step ${status.progress?.completed || 0}/${status.progress?.total || 0}: ${status.currentStep}`);
      console.log(`📈 Progress: ${status.progress?.percentage || 0}%`);
      
      if (status.status === 'completed') {
        console.log('🎉 Workflow completed successfully!');
        break;
      } else if (status.status === 'failed') {
        console.log('❌ Workflow failed:', status.error);
        break;
      }
    } else {
      console.log('⚠️  Could not get status:', statusResult.error);
      break;
    }
  }
}

async function getDetailedStatus(workflowId, type) {
  console.log(`\n📋 Final ${type} Workflow Status:`);
  console.log('─'.repeat(35));
  
  const statusResult = await makeRequest(`/api/workflows/status/${workflowId}`);
  
  if (statusResult.success) {
    const status = statusResult.data;
    console.log(`🏷️  Workflow ID: ${status.workflowId}`);
    console.log(`📊 Status: ${status.status}`);
    console.log(`⏰ Duration: ${status.duration}s`);
    
    if (status.results?.courseStructure) {
      const structure = status.results.courseStructure;
      console.log(`📚 Course: "${structure.title}"`);
      console.log(`📖 Modules: ${structure.modules?.length || 0}`);
      
      const totalLessons = structure.modules?.reduce((total, module) => 
        total + (module.lessons?.length || 0), 0) || 0;
      console.log(`🎯 Total Lessons: ${totalLessons}`);
      console.log(`⏱️  Estimated Duration: ${structure.totalDuration}`);
    }
    
    if (status.error) {
      console.log(`❌ Error: ${status.error}`);
    }
  } else {
    console.log('❌ Could not get final status:', statusResult.error);
  }
}

async function runComprehensiveTest() {
  console.log('🚀 KOURSIA PLATFORM - WORKFLOW TESTING SUITE');
  console.log('='.repeat(50));
  console.log('Testing both Traditional and Avatar course creation workflows');
  console.log('Powered by Mistral AI with intelligent fallback systems\n');
  
  // Test Traditional Workflow
  const traditionalWorkflowId = await testTraditionalWorkflow();
  
  // Test Avatar Workflow  
  const avatarWorkflowId = await testAvatarWorkflow();
  
  // Wait a bit for processing
  console.log('\n⏳ Waiting for workflows to process...');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // Get detailed final status
  if (traditionalWorkflowId) {
    await getDetailedStatus(traditionalWorkflowId, 'Traditional');
  }
  
  if (avatarWorkflowId) {
    await getDetailedStatus(avatarWorkflowId, 'Avatar');
  }
  
  // Summary
  console.log('\n📋 WORKFLOW TESTING SUMMARY');
  console.log('='.repeat(30));
  console.log(`✅ Traditional Workflow: ${traditionalWorkflowId ? 'FUNCTIONAL' : 'FAILED'}`);
  console.log(`✅ Avatar Workflow: ${avatarWorkflowId ? 'FUNCTIONAL' : 'FAILED'}`);
  
  if (traditionalWorkflowId && avatarWorkflowId) {
    console.log('\n🎉 SUCCESS: Both course creation workflows are fully operational!');
    console.log('📱 Ready for production use with comprehensive progress tracking');
    console.log('🤖 Mistral AI integration with intelligent fallback systems active');
  } else {
    console.log('\n⚠️  One or more workflows need attention');
  }
  
  console.log('\n' + '='.repeat(50));
}

// Run the comprehensive test
runComprehensiveTest().catch(error => {
  console.error('❌ Test suite failed:', error);
  process.exit(1);
});