/* Enhanced Visual Styles with clean, professional colors */

/* Gradients for headings and cards - updated with cleaner color scheme */
.gradient-heading {
  background: linear-gradient(135deg, var(--color-primary) 0%, #4dabf7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.gradient-card {
  background: linear-gradient(135deg, white 0%, #f5f9fc 100%);
  box-shadow: 0 8px 16px -5px rgba(25, 118, 210, 0.1), 
              0 4px 8px -5px rgba(25, 118, 210, 0.05);
  border: 1px solid rgba(25, 118, 210, 0.05);
  transition: all 0.3s ease;
}

.gradient-card:hover {
  box-shadow: 0 12px 24px -8px rgba(25, 118, 210, 0.15),
              0 8px 12px -5px rgba(25, 118, 210, 0.08);
  transform: translateY(-3px);
  border-color: rgba(25, 118, 210, 0.1);
}

/* Enhanced buttons with cleaner gradients and hover effects */
.button-gradient {
  background: linear-gradient(135deg, var(--color-primary) 0%, #4dabf7 100%);
  color: white;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.button-gradient:hover {
  background: linear-gradient(135deg, var(--color-primary) 10%, #339af0 100%);
  box-shadow: 0 4px 12px -2px rgba(25, 118, 210, 0.3);
  transform: translateY(-2px);
}

/* Colorful icons styling - more vibrant and engaging */
.icon-colorful {
  transition: all 0.2s ease;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));
}

/* Primary icon colors for different categories */
.icon-colorful.icon-dashboard { color: #4dabf7; }
.icon-colorful.icon-content { color: #38d9a9; }
.icon-colorful.icon-analytics { color: #f76707; }
.icon-colorful.icon-settings { color: #7950f2; }
.icon-colorful.icon-media { color: #fd7e14; }
.icon-colorful.icon-users { color: #40c057; }
.icon-colorful.icon-courses { color: #3b82f6; }
.icon-colorful.icon-ai { color: #e64980; }
.icon-colorful.icon-calendar { color: #4c6ef5; }
.icon-colorful.icon-chat { color: #20c997; }
.icon-colorful.icon-warning { color: #fa5252; }
.icon-colorful.icon-success { color: #40c057; }
.icon-colorful.icon-info { color: #15aabf; }

.icon-colorful:hover {
  transform: scale(1.1);
  filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.2));
}

/* Animated hover effects for cards */
.card-animated {
  transition: all 0.3s cubic-bezier(0.2, 0.8, 0.2, 1);
  backface-visibility: hidden;
}

.card-animated:hover {
  transform: translateY(-5px) translateZ(0);
  box-shadow: 0 20px 40px rgba(var(--color-primary-rgb), 0.2);
}

/* Course card enhancements */
.course-card {
  border-radius: var(--radius);
  overflow: hidden;
  transition: all 0.3s ease;
}

.course-card:hover {
  transform: translateY(-5px);
}

.course-card img {
  transition: all 0.5s ease;
}

.course-card:hover img {
  transform: scale(1.05);
}

/* Visual indicator for active elements */
.active-indicator {
  position: relative;
}

.active-indicator::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary) 0%, #4dabf7 100%);
  border-radius: 3px;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.active-indicator:hover::after,
.active-indicator.active::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Dashboard stats card */
.stats-card {
  background: white;
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(25, 118, 210, 0.05);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, #4dabf7 100%);
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(25, 118, 210, 0.15);
}

/* Colorful progress bars */
.progress-colorful {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-colorful .progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary) 0%, #4dabf7 100%);
  border-radius: 4px;
  transition: width 0.5s ease;
}

/* Enhanced form inputs */
.input-enhanced {
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.input-enhanced:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.15);
}

/* Navigation highlight effect */
.nav-highlight {
  position: relative;
  transition: all 0.3s ease;
}

.nav-highlight::before {
  content: '';
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 0;
  background: linear-gradient(to bottom, var(--color-primary), #4dabf7);
  border-radius: 2px;
  transition: height 0.3s ease;
}

.nav-highlight:hover::before,
.nav-highlight.active::before {
  height: 70%;
}

/* Timeline styling for course progress */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 2px;
  background: linear-gradient(to bottom, var(--color-primary), #4dabf7);
}

.timeline-item {
  position: relative;
  padding-bottom: 1.5rem;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -2.25rem;
  top: 0.25rem;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background: white;
  border: 2px solid var(--color-primary);
  z-index: 1;
}

.timeline-item.completed::before {
  background: var(--color-primary);
}