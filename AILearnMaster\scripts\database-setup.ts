#!/usr/bin/env tsx
/**
 * Database Setup and Migration Script
 * Comprehensive database initialization, migration, and validation for AILearnMaster
 */

import dotenv from 'dotenv';
import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool } from '@neondatabase/serverless';
import { migrate } from 'drizzle-orm/neon-serverless/migrator';
import { sql } from 'drizzle-orm';
import * as schema from '../shared/schema';
import { users, courses, modules, lessons } from '../shared/schema';
import { eq } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

interface SetupOptions {
  force?: boolean;
  skipMigrations?: boolean;
  skipSeeding?: boolean;
  validateOnly?: boolean;
  verbose?: boolean;
}

class DatabaseSetup {
  private pool: Pool;
  private db: ReturnType<typeof drizzle>;
  private options: SetupOptions;

  constructor(options: SetupOptions = {}) {
    this.options = options;
    
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    this.pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      max: 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000
    });

    this.db = drizzle({ client: this.pool, schema });
  }

  /**
   * Main setup process
   */
  async setup(): Promise<void> {
    try {
      console.log('🚀 Starting AILearnMaster Database Setup...\n');

      // Step 1: Validate connection
      await this.validateConnection();

      if (this.options.validateOnly) {
        console.log('✅ Validation complete. Exiting...');
        return;
      }

      // Step 2: Run migrations
      if (!this.options.skipMigrations) {
        await this.runMigrations();
      }

      // Step 3: Validate schema
      await this.validateSchema();

      // Step 4: Seed initial data
      if (!this.options.skipSeeding) {
        await this.seedInitialData();
      }

      // Step 5: Final validation
      await this.finalValidation();

      console.log('\n🎉 Database setup completed successfully!');
      
    } catch (error) {
      console.error('\n❌ Database setup failed:', error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Validate database connection
   */
  private async validateConnection(): Promise<void> {
    console.log('🔍 Validating database connection...');
    
    try {
      const result = await this.db.execute(sql`SELECT 1 as test, NOW() as timestamp`);
      
      if (result.length === 0) {
        throw new Error('Database connection test failed');
      }

      const dbInfo = await this.db.execute(sql`
        SELECT 
          current_database() as database_name,
          current_user as user_name,
          version() as version
      `);

      console.log('✅ Database connection successful');
      console.log(`   Database: ${dbInfo[0].database_name}`);
      console.log(`   User: ${dbInfo[0].user_name}`);
      console.log(`   Version: ${dbInfo[0].version}\n`);
      
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  /**
   * Run database migrations
   */
  private async runMigrations(): Promise<void> {
    console.log('📦 Running database migrations...');
    
    try {
      const migrationsPath = path.join(process.cwd(), 'migrations');
      
      // Check if migrations directory exists
      if (!fs.existsSync(migrationsPath)) {
        console.log('⚠️  No migrations directory found, skipping migrations');
        return;
      }

      // Run migrations
      await migrate(this.db, { migrationsFolder: migrationsPath });
      console.log('✅ Migrations completed successfully\n');
      
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Validate database schema
   */
  private async validateSchema(): Promise<void> {
    console.log('🔍 Validating database schema...');
    
    try {
      // Check if core tables exist
      const tables = await this.db.execute(sql`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
      `);

      const tableNames = tables.map(t => t.table_name);
      const requiredTables = ['users', 'courses', 'modules', 'lessons'];
      
      console.log(`   Found ${tableNames.length} tables:`);
      if (this.options.verbose) {
        tableNames.forEach(name => console.log(`     - ${name}`));
      }

      // Check required tables
      const missingTables = requiredTables.filter(table => !tableNames.includes(table));
      
      if (missingTables.length > 0) {
        throw new Error(`Missing required tables: ${missingTables.join(', ')}`);
      }

      console.log('✅ Schema validation successful\n');
      
    } catch (error) {
      console.error('❌ Schema validation failed:', error);
      throw error;
    }
  }

  /**
   * Seed initial data
   */
  private async seedInitialData(): Promise<void> {
    console.log('🌱 Seeding initial data...');
    
    try {
      // Check if admin user exists
      const existingAdmin = await this.db
        .select()
        .from(users)
        .where(eq(users.email, '<EMAIL>'))
        .limit(1);

      if (existingAdmin.length === 0) {
        // Create admin user
        const adminUser = await this.db.insert(users).values({
          username: 'admin',
          email: '<EMAIL>',
          password: '$2b$10$example.hash.for.admin.password', // Should be properly hashed
          name: 'System Administrator',
          role: 'admin',
          plan: 'enterprise',
          emailVerified: true
        }).returning();

        console.log('✅ Created admin user');
      } else {
        console.log('ℹ️  Admin user already exists');
      }

      // Seed notification types (if the seeder exists)
      try {
        const { seedNotificationTypes } = await import('../server/seeders/notificationTypes');
        await seedNotificationTypes();
        console.log('✅ Notification types seeded');
      } catch (error) {
        console.log('ℹ️  Notification types seeder not available');
      }

      console.log('✅ Initial data seeding completed\n');
      
    } catch (error) {
      console.error('❌ Data seeding failed:', error);
      throw error;
    }
  }

  /**
   * Final validation
   */
  private async finalValidation(): Promise<void> {
    console.log('🔍 Running final validation...');
    
    try {
      // Count records in core tables
      const [userCount] = await this.db.execute(sql`SELECT COUNT(*) as count FROM users`);
      const [courseCount] = await this.db.execute(sql`SELECT COUNT(*) as count FROM courses`);
      const [moduleCount] = await this.db.execute(sql`SELECT COUNT(*) as count FROM modules`);
      const [lessonCount] = await this.db.execute(sql`SELECT COUNT(*) as count FROM lessons`);

      console.log('📊 Database Statistics:');
      console.log(`   Users: ${userCount.count}`);
      console.log(`   Courses: ${courseCount.count}`);
      console.log(`   Modules: ${moduleCount.count}`);
      console.log(`   Lessons: ${lessonCount.count}`);

      // Test basic operations
      await this.db.execute(sql`SELECT 1`);
      console.log('✅ Basic operations test passed');

      // Test transaction
      await this.db.transaction(async (tx) => {
        await tx.execute(sql`SELECT 1`);
      });
      console.log('✅ Transaction test passed');

      console.log('✅ Final validation completed\n');
      
    } catch (error) {
      console.error('❌ Final validation failed:', error);
      throw error;
    }
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    try {
      await this.pool.end();
      console.log('🧹 Cleanup completed');
    } catch (error) {
      console.error('⚠️  Cleanup warning:', error);
    }
  }

  /**
   * Generate database report
   */
  async generateReport(): Promise<void> {
    console.log('📋 Generating Database Report...\n');
    
    try {
      // Database info
      const dbInfo = await this.db.execute(sql`
        SELECT 
          current_database() as database_name,
          current_user as user_name,
          version() as version,
          pg_size_pretty(pg_database_size(current_database())) as database_size
      `);

      // Table information
      const tableInfo = await this.db.execute(sql`
        SELECT 
          schemaname,
          tablename,
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
          pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
      `);

      // Connection info
      const connectionInfo = await this.db.execute(sql`
        SELECT 
          count(*) as active_connections,
          max_conn.setting as max_connections
        FROM pg_stat_activity, 
             (SELECT setting FROM pg_settings WHERE name = 'max_connections') max_conn
        WHERE state = 'active'
        GROUP BY max_conn.setting
      `);

      console.log('='.repeat(60));
      console.log('DATABASE REPORT');
      console.log('='.repeat(60));
      console.log(`Database: ${dbInfo[0].database_name}`);
      console.log(`User: ${dbInfo[0].user_name}`);
      console.log(`Size: ${dbInfo[0].database_size}`);
      console.log(`Version: ${dbInfo[0].version}`);
      console.log('');
      
      console.log('TABLES:');
      tableInfo.forEach(table => {
        console.log(`  ${table.tablename}: ${table.size}`);
      });
      console.log('');
      
      console.log('CONNECTIONS:');
      console.log(`  Active: ${connectionInfo[0]?.active_connections || 0}`);
      console.log(`  Max: ${connectionInfo[0]?.max_connections || 0}`);
      console.log('='.repeat(60));
      
    } catch (error) {
      console.error('❌ Report generation failed:', error);
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const options: SetupOptions = {
    force: args.includes('--force'),
    skipMigrations: args.includes('--skip-migrations'),
    skipSeeding: args.includes('--skip-seeding'),
    validateOnly: args.includes('--validate-only'),
    verbose: args.includes('--verbose')
  };

  const setup = new DatabaseSetup(options);

  if (args.includes('--report')) {
    await setup.generateReport();
    return;
  }

  await setup.setup();
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('Setup failed:', error);
    process.exit(1);
  });
}

export { DatabaseSetup };
