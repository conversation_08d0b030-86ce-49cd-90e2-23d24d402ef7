import React, { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Sparkles, Video, FileText, Mic, Download, Play, Pause, Volume2, Eye, CheckCircle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

// Custom fetch function that handles the API request properly
const makeApiRequest = async (url: string, options: any) => {
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });
  return await response.json();
};

interface Scene {
  id: string;
  text: string;
  searchTerms: string[];
  videoUrl?: string;
  audioUrl?: string;
  captionsUrl?: string;
}

interface Lesson {
  id: string;
  title: string;
  description: string;
  scenes: Scene[];
  status: 'pending' | 'generating' | 'completed' | 'error';
  videoUrl?: string;
}

interface Module {
  id: string;
  title: string;
  description: string;
  lessons: Lesson[];
  status: 'pending' | 'generating' | 'completed' | 'error';
}

interface CourseStructure {
  title: string;
  description: string;
  modules: Module[];
  status: 'draft' | 'generating' | 'completed' | 'error';
}

export default function SmartCourse() {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  
  // Core state
  const [courseTitle, setCourseTitle] = useState("");
  const [courseStructure, setCourseStructure] = useState<CourseStructure | null>(null);
  const [currentStep, setCurrentStep] = useState<'input' | 'generating' | 'review' | 'processing' | 'completed'>('input');
  const [progress, setProgress] = useState(0);
  const [generationStatus, setGenerationStatus] = useState("");
  
  // Generation state
  const [isGeneratingStructure, setIsGeneratingStructure] = useState(false);
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);
  const [currentProcessingModule, setCurrentProcessingModule] = useState<string | null>(null);
  const [currentProcessingLesson, setCurrentProcessingLesson] = useState<string | null>(null);

  // Generate course structure using AI
  const generateCourseStructure = async () => {
    if (!courseTitle.trim()) {
      toast({
        title: "Course Title Required",
        description: "Please enter a course title to generate the structure.",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingStructure(true);
    setCurrentStep('generating');
    setProgress(10);
    setGenerationStatus("Analyzing course topic and generating structure...");

    try {
      const response = await makeApiRequest('/api/ai/generate-smart-course-structure', {
        method: 'POST',
        body: JSON.stringify({ title: courseTitle }),
      });

      if (response.success) {
        setCourseStructure(response.courseStructure);
        setProgress(30);
        setGenerationStatus("Course structure generated successfully!");
        setCurrentStep('review');
      } else {
        throw new Error(response.error || 'Failed to generate course structure');
      }
    } catch (error: any) {
      console.error('Course structure generation error:', error);
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate course structure. Please try again.",
        variant: "destructive",
      });
      setCurrentStep('input');
    } finally {
      setIsGeneratingStructure(false);
    }
  };

  // Generate video content for the entire course
  const generateVideoContent = async () => {
    if (!courseStructure) return;

    setIsGeneratingContent(true);
    setCurrentStep('processing');
    setProgress(40);
    setGenerationStatus("Starting video content generation...");

    try {
      const totalLessons = courseStructure.modules.reduce((acc, module) => acc + module.lessons.length, 0);
      let processedLessons = 0;

      for (const moduleIndex in courseStructure.modules) {
        const module = courseStructure.modules[moduleIndex];
        setCurrentProcessingModule(module.title);
        setGenerationStatus(`Processing module: ${module.title}`);

        for (const lessonIndex in module.lessons) {
          const lesson = module.lessons[lessonIndex];
          setCurrentProcessingLesson(lesson.title);
          setGenerationStatus(`Generating video for: ${lesson.title}`);

          // Generate scenes for this lesson
          const scenesResponse = await makeApiRequest('/api/ai/generate-lesson-scenes', {
            method: 'POST',
            body: JSON.stringify({
              courseTitle: courseStructure.title,
              moduleTitle: module.title,
              lessonTitle: lesson.title,
              lessonDescription: lesson.description,
              isLastLesson: parseInt(lessonIndex) === module.lessons.length - 1,
              isLastModule: parseInt(moduleIndex) === courseStructure.modules.length - 1
            }),
          });

          if (scenesResponse.success) {
            // Update lesson with generated scenes
            setCourseStructure(prev => {
              if (!prev) return prev;
              const updated = { ...prev };
              updated.modules[parseInt(moduleIndex)].lessons[parseInt(lessonIndex)].scenes = scenesResponse.scenes;
              updated.modules[parseInt(moduleIndex)].lessons[parseInt(lessonIndex)].status = 'generating';
              return updated;
            });

            // Generate media content for each scene
            for (let sceneIndex = 0; sceneIndex < scenesResponse.scenes.length; sceneIndex++) {
              const scene = scenesResponse.scenes[sceneIndex];
              
              // Generate TTS audio using Kokoro
              const audioResponse = await makeApiRequest('/api/ai/kokoro-tts', {
                method: 'POST',
                body: JSON.stringify({
                  text: scene.text,
                  voice: 'alloy',
                  speed: 1.0
                }),
              });

              // Generate captions using Whisper
              if (audioResponse.success) {
                const captionsResponse = await makeApiRequest('/api/ai/whisper-captions', {
                  method: 'POST',
                  body: JSON.stringify({
                    audioUrl: audioResponse.audioUrl
                  }),
                });

                // Get background video from Pexels
                const mediaResponse = await makeApiRequest('/api/ai/pexels-media', {
                  method: 'POST',
                  body: JSON.stringify({
                    searchTerms: scene.searchTerms,
                    mediaType: 'video',
                    duration: audioResponse.duration || 10
                  }),
                });

                // Update scene with generated content
                setCourseStructure(prev => {
                  if (!prev) return prev;
                  const updated = { ...prev };
                  updated.modules[parseInt(moduleIndex)].lessons[parseInt(lessonIndex)].scenes[sceneIndex] = {
                    ...scene,
                    audioUrl: audioResponse.audioUrl,
                    captionsUrl: captionsResponse.success ? captionsResponse.captionsUrl : undefined,
                    videoUrl: mediaResponse.success ? mediaResponse.videoUrl : undefined
                  };
                  return updated;
                });
              }
            }

            // Assemble final lesson video
            const assemblyResponse = await makeApiRequest('/api/ai/assemble-lesson-video', {
              method: 'POST',
              body: JSON.stringify({
                lessonId: lesson.id,
                scenes: scenesResponse.scenes
              }),
            });

            if (assemblyResponse.success) {
              setCourseStructure(prev => {
                if (!prev) return prev;
                const updated = { ...prev };
                updated.modules[parseInt(moduleIndex)].lessons[parseInt(lessonIndex)].videoUrl = assemblyResponse.videoUrl;
                updated.modules[parseInt(moduleIndex)].lessons[parseInt(lessonIndex)].status = 'completed';
                return updated;
              });
            }
          }

          processedLessons++;
          const progressValue = 40 + (processedLessons / totalLessons) * 50;
          setProgress(progressValue);
        }

        // Mark module as completed
        setCourseStructure(prev => {
          if (!prev) return prev;
          const updated = { ...prev };
          updated.modules[parseInt(moduleIndex)].status = 'completed';
          return updated;
        });
      }

      setProgress(100);
      setGenerationStatus("Course generation completed successfully!");
      setCurrentStep('completed');
      
      toast({
        title: "Course Generated",
        description: "Your smart course has been generated with AI-powered videos and content.",
      });

    } catch (error: any) {
      console.error('Video content generation error:', error);
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate video content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingContent(false);
      setCurrentProcessingModule(null);
      setCurrentProcessingLesson(null);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              onClick={() => setLocation('/course-creation')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Course Creation
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
                <Sparkles className="h-8 w-8 text-blue-600" />
                Smart Course Builder
              </h1>
              <p className="text-gray-600 mt-1">
                AI-powered course generation with just a title
              </p>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        {currentStep !== 'input' && (
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium">Generation Progress</Label>
                <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full mb-2" />
              <p className="text-sm text-gray-600">{generationStatus}</p>
              {currentProcessingModule && (
                <p className="text-xs text-blue-600 mt-1">
                  Module: {currentProcessingModule} {currentProcessingLesson && `• Lesson: ${currentProcessingLesson}`}
                </p>
              )}
            </CardContent>
          </Card>
        )}

        <AnimatePresence mode="wait">
          {/* Step 1: Course Title Input */}
          {currentStep === 'input' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              key="input"
            >
              <Card className="max-w-2xl mx-auto">
                <CardHeader className="text-center">
                  <CardTitle className="text-2xl">What would you like to teach?</CardTitle>
                  <CardDescription>
                    Enter your course title and our AI will generate a complete course structure with engaging video content
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="courseTitle">Course Title</Label>
                    <Input
                      id="courseTitle"
                      placeholder="e.g., Complete Guide to Digital Marketing in 2025"
                      value={courseTitle}
                      onChange={(e) => setCourseTitle(e.target.value)}
                      className="text-lg"
                      onKeyPress={(e) => e.key === 'Enter' && generateCourseStructure()}
                    />
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">What our AI will generate:</h4>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• Complete course structure with modules and lessons</li>
                      <li>• 4-scene videos for each lesson with educational content</li>
                      <li>• AI-generated scripts optimized for engagement</li>
                      <li>• Professional voiceovers using Kokoro TTS</li>
                      <li>• Auto-generated captions and subtitles</li>
                      <li>• Stock videos and images from Pexels</li>
                    </ul>
                  </div>

                  <Button 
                    onClick={generateCourseStructure}
                    disabled={!courseTitle.trim() || isGeneratingStructure}
                    className="w-full h-12 text-lg"
                  >
                    {isGeneratingStructure ? (
                      <>
                        <Sparkles className="mr-2 h-5 w-5 animate-spin" />
                        Generating Course Structure...
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-5 w-5" />
                        Generate Smart Course
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Course Structure Review */}
          {currentStep === 'review' && courseStructure && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              key="review"
            >
              <Card>
                <CardHeader>
                  <CardTitle>Review Your Course Structure</CardTitle>
                  <CardDescription>
                    AI has generated a comprehensive course structure. Review and start video generation.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold">{courseStructure.title}</h3>
                      <p className="text-gray-600 mt-1">{courseStructure.description}</p>
                    </div>

                    <div className="space-y-4">
                      {courseStructure.modules.map((module, moduleIndex) => (
                        <Card key={module.id} className="border-l-4 border-l-blue-500">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">
                              Module {moduleIndex + 1}: {module.title}
                            </CardTitle>
                            <CardDescription>{module.description}</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              {module.lessons.map((lesson, lessonIndex) => (
                                <div key={lesson.id} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                                  <Video className="h-4 w-4 text-blue-600" />
                                  <div className="flex-1">
                                    <div className="font-medium text-sm">
                                      Lesson {lessonIndex + 1}: {lesson.title}
                                    </div>
                                    <div className="text-xs text-gray-500">{lesson.description}</div>
                                  </div>
                                  <Badge variant="outline" className="text-xs">
                                    4 scenes
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    <div className="flex gap-3">
                      <Button 
                        variant="outline" 
                        onClick={() => setCurrentStep('input')}
                        className="flex-1"
                      >
                        Edit Title
                      </Button>
                      <Button 
                        onClick={generateVideoContent}
                        disabled={isGeneratingContent}
                        className="flex-1"
                      >
                        {isGeneratingContent ? (
                          <>
                            <Video className="mr-2 h-4 w-4 animate-pulse" />
                            Generating Videos...
                          </>
                        ) : (
                          <>
                            <Video className="mr-2 h-4 w-4" />
                            Generate Video Content
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Step 3: Content Generation in Progress */}
          {currentStep === 'processing' && courseStructure && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              key="processing"
            >
              <Card>
                <CardHeader>
                  <CardTitle>Generating Course Content</CardTitle>
                  <CardDescription>
                    AI is creating engaging video content for your course. This may take several minutes.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {courseStructure.modules.map((module, moduleIndex) => (
                      <Card key={module.id} className="border-l-4 border-l-blue-500">
                        <CardContent className="pt-4">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-medium">Module {moduleIndex + 1}: {module.title}</h4>
                            <Badge 
                              variant={module.status === 'completed' ? 'default' : 
                                      module.status === 'generating' ? 'secondary' : 'outline'}
                              className="text-xs"
                            >
                              {module.status === 'completed' && <CheckCircle className="h-3 w-3 mr-1" />}
                              {module.status}
                            </Badge>
                          </div>
                          
                          <div className="space-y-2">
                            {module.lessons.map((lesson, lessonIndex) => (
                              <div key={lesson.id} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                                <Video className="h-4 w-4 text-blue-600" />
                                <div className="flex-1">
                                  <div className="font-medium text-sm">{lesson.title}</div>
                                  <div className="text-xs text-gray-500">
                                    {lesson.status === 'generating' && currentProcessingLesson === lesson.title && (
                                      <span className="text-blue-600">Currently processing...</span>
                                    )}
                                    {lesson.status === 'completed' && (
                                      <span className="text-green-600">Video generated</span>
                                    )}
                                    {lesson.status === 'pending' && (
                                      <span className="text-gray-500">Waiting...</span>
                                    )}
                                  </div>
                                </div>
                                <Badge 
                                  variant={lesson.status === 'completed' ? 'default' : 
                                          lesson.status === 'generating' ? 'secondary' : 'outline'}
                                  className="text-xs"
                                >
                                  {lesson.status === 'completed' && <CheckCircle className="h-3 w-3 mr-1" />}
                                  {lesson.status}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Step 4: Completed Course */}
          {currentStep === 'completed' && courseStructure && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              key="completed"
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                    Course Generated Successfully!
                  </CardTitle>
                  <CardDescription>
                    Your AI-powered course is ready with professional videos and content.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="overview" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="overview">Overview</TabsTrigger>
                      <TabsTrigger value="content">Content</TabsTrigger>
                      <TabsTrigger value="export">Export</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="overview" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card>
                          <CardContent className="pt-6 text-center">
                            <div className="text-2xl font-bold text-blue-600">
                              {courseStructure.modules.length}
                            </div>
                            <div className="text-sm text-gray-600">Modules</div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="pt-6 text-center">
                            <div className="text-2xl font-bold text-green-600">
                              {courseStructure.modules.reduce((acc, module) => acc + module.lessons.length, 0)}
                            </div>
                            <div className="text-sm text-gray-600">Video Lessons</div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="pt-6 text-center">
                            <div className="text-2xl font-bold text-purple-600">
                              {courseStructure.modules.reduce((acc, module) => 
                                acc + module.lessons.reduce((lessonAcc, lesson) => lessonAcc + lesson.scenes.length, 0), 0
                              )}
                            </div>
                            <div className="text-sm text-gray-600">Video Scenes</div>
                          </CardContent>
                        </Card>
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="content" className="space-y-4">
                      {courseStructure.modules.map((module, moduleIndex) => (
                        <Card key={module.id}>
                          <CardHeader>
                            <CardTitle className="text-lg">
                              Module {moduleIndex + 1}: {module.title}
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-3">
                              {module.lessons.map((lesson, lessonIndex) => (
                                <div key={lesson.id} className="border rounded-lg p-4">
                                  <div className="flex items-center justify-between mb-3">
                                    <h5 className="font-medium">
                                      Lesson {lessonIndex + 1}: {lesson.title}
                                    </h5>
                                    {lesson.videoUrl && (
                                      <Button size="sm" variant="outline">
                                        <Play className="h-4 w-4 mr-1" />
                                        Preview
                                      </Button>
                                    )}
                                  </div>
                                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                                    {lesson.scenes.map((scene, sceneIndex) => (
                                      <div key={scene.id} className="bg-gray-50 p-2 rounded text-center">
                                        <div className="text-xs font-medium">Scene {sceneIndex + 1}</div>
                                        <div className="text-xs text-gray-500 mt-1">
                                          {scene.searchTerms.slice(0, 3).join(', ')}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </TabsContent>
                    
                    <TabsContent value="export" className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-lg">Download Course</CardTitle>
                            <CardDescription>Export your course in various formats</CardDescription>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <Button className="w-full" variant="outline">
                              <Download className="h-4 w-4 mr-2" />
                              Download All Videos (ZIP)
                            </Button>
                            <Button className="w-full" variant="outline">
                              <FileText className="h-4 w-4 mr-2" />
                              Export Course PDF
                            </Button>
                            <Button className="w-full" variant="outline">
                              <Mic className="h-4 w-4 mr-2" />
                              Export Audio Files
                            </Button>
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader>
                            <CardTitle className="text-lg">Publish Course</CardTitle>
                            <CardDescription>Share your course with the world</CardDescription>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <Button className="w-full">
                              <Video className="h-4 w-4 mr-2" />
                              Publish to Platform
                            </Button>
                            <Button className="w-full" variant="outline">
                              <Eye className="h-4 w-4 mr-2" />
                              Preview Course
                            </Button>
                          </CardContent>
                        </Card>
                      </div>
                    </TabsContent>
                  </Tabs>
                  
                  <div className="flex gap-3 mt-6">
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        setCourseStructure(null);
                        setCurrentStep('input');
                        setProgress(0);
                        setCourseTitle("");
                      }}
                      className="flex-1"
                    >
                      Create Another Course
                    </Button>
                    <Button 
                      onClick={() => setLocation('/dashboard')}
                      className="flex-1"
                    >
                      Go to Dashboard
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}