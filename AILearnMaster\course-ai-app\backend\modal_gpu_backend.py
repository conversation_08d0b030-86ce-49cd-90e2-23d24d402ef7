"""
Modal A100 80GB GPU Backend for AI Course Builder
Provides Stable Diffusion XL, SadTalker, Chatterbox TTS, Coqui TTS, and Marp endpoints
"""

import modal
import os
import base64
import tempfile
import subprocess
import json
import torch
import io
from typing import Dict, List, Any, Optional
from pathlib import Path
import hashlib
import time

# Modal App Configuration
app = modal.App("course-ai-builder")

# A100 80GB GPU Image with all AI dependencies
gpu_image = (
    modal.Image.debian_slim(python_version="3.12")
    .apt_install([
        "git", "wget", "curl", "ffmpeg", "libsm6", "libxext6", 
        "libxrender-dev", "libglib2.0-0", "libgl1-mesa-glx",
        "libsndfile1", "espeak", "espeak-data", "libespeak1",
        "libespeak-dev", "nodejs", "npm", "build-essential",
        "cmake", "libboost-system-dev", "libboost-filesystem-dev",
        "libboost-thread-dev", "libopencv-dev", "python3-dev"
    ])
    .pip_install([
        "torch==2.1.0",
        "torchvision==0.16.0",
        "torchaudio==2.1.0",
        "diffusers==0.24.0",
        "transformers==4.36.0",
        "accelerate==0.25.0",
        "xformers==0.0.23",
        "opencv-python==********",
        "pillow==10.1.0",
        "numpy==1.24.3",
        "scipy==1.11.4",
        "scikit-image==0.22.0",
        "imageio==2.33.0",
        "imageio-ffmpeg==0.4.9",
        "librosa==0.10.1",
        "soundfile==0.12.1",
        "pydub==0.25.1",
        "TTS==0.22.0",
        "face-alignment==1.4.1",
        "gfpgan==1.3.8",
        "basicsr==1.4.2",
        "facexlib==0.3.0",
        "requests==2.31.0",
        "fastapi==0.104.1",
        "uvicorn==0.24.0",
        "redis==5.0.1"
    ])
    .run_commands([
        "npm install -g @marp-team/marp-cli",
        "mkdir -p /app/models /app/cache /app/temp",
        "cd /app && git clone https://github.com/OpenTalker/SadTalker.git",
        "cd /app/SadTalker && pip install -r requirements.txt",
        "mkdir -p /app/SadTalker/checkpoints",
        "wget -O /app/SadTalker/checkpoints/mapping_00109-model.pth.tar https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00109-model.pth.tar",
        "wget -O /app/SadTalker/checkpoints/mapping_00229-model.pth.tar https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/mapping_00229-model.pth.tar",
        "wget -O /app/SadTalker/checkpoints/SadTalker_V002.safetensors https://github.com/OpenTalker/SadTalker/releases/download/v0.0.2-rc/SadTalker_V002.safetensors"
    ])
)

# CPU-only image for Marp slides
cpu_image = (
    modal.Image.debian_slim(python_version="3.12")
    .apt_install(["nodejs", "npm", "curl", "wget"])
    .run_commands([
        "npm install -g @marp-team/marp-cli"
    ])
)

# Shared storage for models and cache
shared_volume = modal.Volume.from_name("course-ai-cache", create_if_missing=True)

# Cache utilities
class CacheManager:
    @staticmethod
    def generate_cache_key(data: str, prefix: str = "") -> str:
        return f"{prefix}_{hashlib.md5(data.encode()).hexdigest()}"
    
    @staticmethod
    def save_to_cache(key: str, data: bytes, cache_dir: str = "/app/cache"):
        os.makedirs(cache_dir, exist_ok=True)
        with open(f"{cache_dir}/{key}", "wb") as f:
            f.write(data)
    
    @staticmethod
    def load_from_cache(key: str, cache_dir: str = "/app/cache") -> Optional[bytes]:
        try:
            with open(f"{cache_dir}/{key}", "rb") as f:
                return f.read()
        except FileNotFoundError:
            return None

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/cache": shared_volume},
    timeout=1800,
    memory=32768,
    min_containers=1
)
def gen_image(
    prompt: str,
    negative_prompt: str = "",
    width: int = 1024,
    height: int = 1024,
    num_inference_steps: int = 30,
    guidance_scale: float = 7.5,
    batch_size: int = 1
) -> Dict[str, Any]:
    """Generate images using Stable Diffusion XL with caching"""
    try:
        from diffusers import StableDiffusionXLPipeline
        import torch
        
        # Cache key for this request
        cache_key = CacheManager.generate_cache_key(
            f"{prompt}_{negative_prompt}_{width}_{height}_{num_inference_steps}_{guidance_scale}",
            "sdxl"
        )
        
        # Check cache first
        cached_result = CacheManager.load_from_cache(cache_key)
        if cached_result:
            return {
                "status": "success",
                "images_base64": [base64.b64encode(cached_result).decode()],
                "prompt": prompt,
                "cached": True,
                "gpu_used": True,
                "timestamp": int(time.time() * 1000)
            }
        
        # Load SDXL pipeline
        pipe = StableDiffusionXLPipeline.from_pretrained(
            "stabilityai/stable-diffusion-xl-base-1.0",
            torch_dtype=torch.float16,
            use_safetensors=True,
            variant="fp16"
        ).to("cuda")
        
        # Enable memory optimization
        pipe.enable_model_cpu_offload()
        pipe.enable_attention_slicing()
        
        # Generate images
        images = pipe(
            prompt=prompt,
            negative_prompt=negative_prompt,
            width=width,
            height=height,
            num_inference_steps=num_inference_steps,
            guidance_scale=guidance_scale,
            num_images_per_prompt=batch_size
        ).images
        
        # Convert to base64 and cache
        images_base64 = []
        for i, image in enumerate(images):
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='PNG')
            img_data = img_buffer.getvalue()
            
            # Cache first image
            if i == 0:
                CacheManager.save_to_cache(cache_key, img_data)
            
            images_base64.append(base64.b64encode(img_data).decode())
        
        return {
            "status": "success",
            "images_base64": images_base64,
            "prompt": prompt,
            "negative_prompt": negative_prompt,
            "dimensions": f"{width}x{height}",
            "inference_steps": num_inference_steps,
            "guidance_scale": guidance_scale,
            "cached": False,
            "gpu_used": True,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "prompt": prompt,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/cache": shared_volume},
    timeout=1800,
    memory=24576
)
def gen_video(
    image_base64: str,
    audio_base64: str,
    enhancer: str = "gfpgan",
    size: int = 512,
    expression_scale: float = 1.0,
    still_mode: bool = True
) -> Dict[str, Any]:
    """Generate talking head videos using SadTalker with caching"""
    try:
        import sys
        import cv2
        from PIL import Image
        
        # Cache key
        cache_key = CacheManager.generate_cache_key(
            f"{image_base64[:100]}_{audio_base64[:100]}_{enhancer}_{size}",
            "sadtalker"
        )
        
        # Check cache
        cached_result = CacheManager.load_from_cache(cache_key)
        if cached_result:
            return {
                "status": "success",
                "video_base64": base64.b64encode(cached_result).decode(),
                "cached": True,
                "gpu_used": True,
                "timestamp": int(time.time() * 1000)
            }
        
        # Add SadTalker to path
        sys.path.append("/app/SadTalker")
        from src.utils.preprocess import CropAndExtract
        from src.test_audio2coeff import Audio2Coeff
        from src.facerender.animate import AnimateFromCoeff
        from src.generate_batch import get_data
        from src.generate_facerender_batch import get_facerender_data
        
        # Create temp directories
        temp_dir = tempfile.mkdtemp()
        image_path = os.path.join(temp_dir, "input_image.jpg")
        audio_path = os.path.join(temp_dir, "input_audio.wav")
        output_path = os.path.join(temp_dir, "output_video.mp4")
        
        # Decode inputs
        image_data = base64.b64decode(image_base64)
        audio_data = base64.b64decode(audio_base64)
        
        # Save input files
        with open(image_path, "wb") as f:
            f.write(image_data)
        with open(audio_path, "wb") as f:
            f.write(audio_data)
        
        # Initialize SadTalker components
        preprocess_model = CropAndExtract("/app/SadTalker/checkpoints", "cuda")
        audio2coeff = Audio2Coeff("/app/SadTalker/checkpoints", "cuda")
        animate = AnimateFromCoeff("/app/SadTalker/checkpoints", "cuda")
        
        # Process image and audio
        first_frame_dir = os.path.join(temp_dir, "first_frame_dir")
        os.makedirs(first_frame_dir, exist_ok=True)
        
        # Extract first frame and coefficients
        first_coeff_path, crop_pic_path, crop_info = preprocess_model.generate(
            image_path, first_frame_dir, "full", source_image_flag=True, pic_size=size
        )
        
        # Generate audio coefficients
        batch = get_data(first_coeff_path, audio_path, "cuda", ref_eyeblink=None, still=still_mode)
        coeff_path = audio2coeff.generate(batch, "/app/SadTalker/checkpoints", pose_style=0, ref_pose=None)
        
        # Generate final video
        data = get_facerender_data(
            coeff_path, crop_pic_path, first_coeff_path, audio_path, 
            batch_size=1, input_yaw_list=None, input_pitch_list=None, 
            input_roll_list=None, expression_scale=expression_scale, 
            still_mode=still_mode, preprocess="crop", size=size
        )
        
        animate.generate(
            data, "/app/SadTalker/checkpoints", pic_path=crop_pic_path, 
            crop_info=crop_info, enhancer=enhancer, background_enhancer=None, 
            face_enhancer=None, verbose=False, save_path=output_path
        )
        
        # Read output video
        with open(output_path, "rb") as video_file:
            video_data = video_file.read()
            
        # Cache result
        CacheManager.save_to_cache(cache_key, video_data)
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "video_base64": base64.b64encode(video_data).decode(),
            "video_format": "mp4",
            "enhancer": enhancer,
            "size": size,
            "expression_scale": expression_scale,
            "cached": False,
            "gpu_used": True,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "enhancer": enhancer,
            "size": size,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/cache": shared_volume},
    timeout=600,
    memory=16384
)
def tts_chatterbox(
    text: str,
    voice: str = "v2/en_speaker_6",
    temperature: float = 0.7,
    speed: float = 1.0,
    batch_texts: List[str] = None
) -> Dict[str, Any]:
    """Generate speech using Chatterbox TTS with batching support"""
    try:
        from TTS.api import TTS
        import soundfile as sf
        
        texts_to_process = batch_texts if batch_texts else [text]
        results = []
        
        # Initialize TTS model
        tts = TTS("tts_models/en/ljspeech/tacotron2-DDC").to("cuda")
        
        for i, txt in enumerate(texts_to_process):
            # Cache key
            cache_key = CacheManager.generate_cache_key(
                f"{txt}_{voice}_{temperature}_{speed}",
                "chatterbox"
            )
            
            # Check cache
            cached_result = CacheManager.load_from_cache(cache_key)
            if cached_result:
                results.append({
                    "index": i,
                    "text": txt,
                    "audio_base64": base64.b64encode(cached_result).decode(),
                    "cached": True
                })
                continue
            
            # Generate audio
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                tts.tts_to_file(text=txt, file_path=temp_file.name)
                
                # Read and process audio
                with open(temp_file.name, "rb") as audio_file:
                    audio_data = audio_file.read()
                    
                # Cache result
                CacheManager.save_to_cache(cache_key, audio_data)
                
                results.append({
                    "index": i,
                    "text": txt,
                    "audio_base64": base64.b64encode(audio_data).decode(),
                    "cached": False
                })
                
                os.unlink(temp_file.name)
        
        return {
            "status": "success",
            "results": results,
            "voice": voice,
            "temperature": temperature,
            "speed": speed,
            "batch_size": len(texts_to_process),
            "gpu_used": True,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "text": text,
            "voice": voice,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/cache": shared_volume},
    timeout=600,
    memory=16384
)
def tts_coqui(
    text: str,
    voice_model: str = "tts_models/en/vctk/vits",
    speaker: str = "p225",
    emotion: str = "neutral",
    speed: float = 1.0,
    batch_texts: List[str] = None
) -> Dict[str, Any]:
    """Generate speech using Coqui TTS with advanced voice cloning"""
    try:
        from TTS.api import TTS
        import torchaudio
        
        texts_to_process = batch_texts if batch_texts else [text]
        results = []
        
        # Initialize Coqui TTS
        tts = TTS(voice_model).to("cuda")
        
        for i, txt in enumerate(texts_to_process):
            # Cache key
            cache_key = CacheManager.generate_cache_key(
                f"{txt}_{voice_model}_{speaker}_{emotion}_{speed}",
                "coqui"
            )
            
            # Check cache
            cached_result = CacheManager.load_from_cache(cache_key)
            if cached_result:
                results.append({
                    "index": i,
                    "text": txt,
                    "audio_base64": base64.b64encode(cached_result).decode(),
                    "cached": True
                })
                continue
            
            # Generate audio
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                if "vctk" in voice_model:
                    tts.tts_to_file(text=txt, speaker=speaker, file_path=temp_file.name)
                else:
                    tts.tts_to_file(text=txt, file_path=temp_file.name)
                
                # Read audio data
                with open(temp_file.name, "rb") as audio_file:
                    audio_data = audio_file.read()
                    
                # Cache result
                CacheManager.save_to_cache(cache_key, audio_data)
                
                results.append({
                    "index": i,
                    "text": txt,
                    "audio_base64": base64.b64encode(audio_data).decode(),
                    "cached": False
                })
                
                os.unlink(temp_file.name)
        
        return {
            "status": "success",
            "results": results,
            "voice_model": voice_model,
            "speaker": speaker,
            "emotion": emotion,
            "speed": speed,
            "batch_size": len(texts_to_process),
            "gpu_used": True,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "text": text,
            "voice_model": voice_model,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=cpu_image,
    timeout=300,
    memory=4096
)
def slides(
    markdown_content: str,
    theme: str = "default",
    output_format: str = "pdf",
    batch_slides: List[Dict[str, str]] = None
) -> Dict[str, Any]:
    """Generate presentation slides using Marp CLI with batch support"""
    try:
        slides_to_process = batch_slides if batch_slides else [{"content": markdown_content, "theme": theme}]
        results = []
        
        for i, slide_data in enumerate(slides_to_process):
            content = slide_data.get("content", markdown_content)
            slide_theme = slide_data.get("theme", theme)
            
            temp_dir = tempfile.mkdtemp()
            markdown_path = os.path.join(temp_dir, f"slides_{i}.md")
            output_path = os.path.join(temp_dir, f"slides_{i}.{output_format}")
            
            # Write markdown content
            with open(markdown_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            # Generate slides
            cmd = [
                "marp",
                markdown_path,
                "--theme", slide_theme,
                "--output", output_path,
                "--allow-local-files"
            ]
            
            if output_format == "pdf":
                cmd.extend(["--pdf"])
            elif output_format == "html":
                cmd.extend(["--html"])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
            
            if result.returncode != 0:
                results.append({
                    "index": i,
                    "status": "error",
                    "error": f"Marp generation failed: {result.stderr}"
                })
                continue
            
            # Read output
            with open(output_path, "rb") as output_file:
                output_data = output_file.read()
                slides_base64 = base64.b64encode(output_data).decode()
            
            results.append({
                "index": i,
                "status": "success",
                "slides_base64": slides_base64,
                "theme": slide_theme,
                "format": output_format
            })
            
            # Cleanup
            import shutil
            shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "results": results,
            "batch_size": len(slides_to_process),
            "format": output_format,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "format": output_format,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    timeout=300,
    memory=16384
)
def health() -> Dict[str, Any]:
    """Health check for A100 GPU services"""
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        gpu_name = torch.cuda.get_device_name(0) if gpu_available else "No GPU"
        
        if gpu_available:
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            torch.cuda.empty_cache()
            gpu_memory_free = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / (1024**3)
        else:
            gpu_memory_total = gpu_memory_free = 0
        
        return {
            "status": "online",
            "gpu_available": gpu_available,
            "gpu_count": gpu_count,
            "gpu_name": gpu_name,
            "gpu_memory_total_gb": round(gpu_memory_total, 2),
            "gpu_memory_free_gb": round(gpu_memory_free, 2),
            "services": {
                "stable_diffusion_xl": True,
                "sadtalker": True,
                "chatterbox_tts": True,
                "coqui_tts": True,
                "marp_slides": True
            },
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "gpu_available": False,
            "services": {
                "stable_diffusion_xl": False,
                "sadtalker": False,
                "chatterbox_tts": False,
                "coqui_tts": False,
                "marp_slides": False
            },
            "timestamp": int(time.time() * 1000)
        }

# FastAPI endpoints
@app.function(image=gpu_image, gpu="A100-80GB", timeout=1800)
@modal.asgi_app()
def fastapi_app():
    from fastapi import FastAPI, HTTPException
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel
    
    web_app = FastAPI(title="AI Course Builder API")
    
    # CORS middleware with restricted origins
    import os
    allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:5000").split(",")
    web_app.add_middleware(
        CORSMiddleware,
        allow_origins=allowed_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["Origin", "X-Requested-With", "Content-Type", "Accept", "Authorization", "X-CSRF-Token"],
    )
    
    # Request models
    class ImageGenRequest(BaseModel):
        prompt: str
        negative_prompt: str = ""
        width: int = 1024
        height: int = 1024
        num_inference_steps: int = 30
        guidance_scale: float = 7.5
        batch_size: int = 1
    
    class VideoGenRequest(BaseModel):
        image_base64: str
        audio_base64: str
        enhancer: str = "gfpgan"
        size: int = 512
        expression_scale: float = 1.0
        still_mode: bool = True
    
    class TTSRequest(BaseModel):
        text: str
        voice: str = "default"
        temperature: float = 0.7
        speed: float = 1.0
        batch_texts: List[str] = None
    
    class CoquiTTSRequest(BaseModel):
        text: str
        voice_model: str = "tts_models/en/vctk/vits"
        speaker: str = "p225"
        emotion: str = "neutral"
        speed: float = 1.0
        batch_texts: List[str] = None
    
    class SlidesRequest(BaseModel):
        markdown_content: str
        theme: str = "default"
        output_format: str = "pdf"
        batch_slides: List[Dict[str, str]] = None
    
    @web_app.get("/health")
    async def health_check():
        return health.remote()
    
    @web_app.post("/gen_image")
    async def generate_image(request: ImageGenRequest):
        return gen_image.remote(
            request.prompt,
            request.negative_prompt,
            request.width,
            request.height,
            request.num_inference_steps,
            request.guidance_scale,
            request.batch_size
        )
    
    @web_app.post("/gen_video")
    async def generate_video(request: VideoGenRequest):
        return gen_video.remote(
            request.image_base64,
            request.audio_base64,
            request.enhancer,
            request.size,
            request.expression_scale,
            request.still_mode
        )
    
    @web_app.post("/tts_chatter")
    async def chatterbox_tts(request: TTSRequest):
        return tts_chatterbox.remote(
            request.text,
            request.voice,
            request.temperature,
            request.speed,
            request.batch_texts
        )
    
    @web_app.post("/tts_coqui")
    async def coqui_tts(request: CoquiTTSRequest):
        return tts_coqui.remote(
            request.text,
            request.voice_model,
            request.speaker,
            request.emotion,
            request.speed,
            request.batch_texts
        )
    
    @web_app.post("/slides")
    async def generate_slides(request: SlidesRequest):
        return slides.remote(
            request.markdown_content,
            request.theme,
            request.output_format,
            request.batch_slides
        )
    
    return web_app

if __name__ == "__main__":
    print("AI Course Builder Modal Backend - A100 80GB GPU Ready")