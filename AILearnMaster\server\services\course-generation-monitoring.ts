/**
 * Course Generation Monitoring and Progress Tracking Service
 * Real-time monitoring, logging, and quality validation for course generation pipeline
 */

import { dbManager, safeDbOperation } from '../db-enhanced';
import { courses, lessons, mediaLibrary } from '@shared/schema';
import { eq, and, gte, lte } from 'drizzle-orm';
import { EventEmitter } from 'events';

export interface GenerationMetrics {
  jobId: string;
  courseId?: number;
  courseType: 'traditional' | 'avatar';
  startTime: Date;
  endTime?: Date;
  duration?: number; // milliseconds
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  
  // Performance metrics
  performance: {
    cpuUsage: number[];
    memoryUsage: number[];
    diskUsage: number;
    networkUsage: number;
    peakMemory: number;
    averageCpu: number;
  };
  
  // Quality metrics
  quality: {
    contentScore: number; // 0-100
    audioScore: number; // 0-100
    videoScore: number; // 0-100
    overallScore: number; // 0-100
    issues: string[];
    warnings: string[];
  };
  
  // Resource usage
  resources: {
    filesCreated: number;
    storageUsed: number; // bytes
    apiCalls: {
      mistral: number;
      coquiTTS: number;
      kokoroTTS: number;
      pexels: number;
      pixabay: number;
      modal: number;
    };
    costs: {
      estimated: number;
      breakdown: { [service: string]: number };
    };
  };
  
  // Progress tracking
  progress: {
    currentStage: string;
    stagesCompleted: string[];
    totalStages: number;
    percentage: number;
    estimatedTimeRemaining: number;
  };
  
  // Error tracking
  errors: {
    count: number;
    details: Array<{
      timestamp: Date;
      stage: string;
      error: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      resolved: boolean;
    }>;
  };
}

export interface QualityValidationResult {
  isValid: boolean;
  score: number;
  issues: Array<{
    type: 'content' | 'audio' | 'video' | 'structure';
    severity: 'low' | 'medium' | 'high';
    description: string;
    suggestion: string;
  }>;
  recommendations: string[];
}

export interface SystemHealthMetrics {
  timestamp: Date;
  activeJobs: number;
  queueLength: number;
  averageProcessingTime: number;
  successRate: number;
  errorRate: number;
  resourceUtilization: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  serviceStatus: {
    database: 'healthy' | 'degraded' | 'down';
    mistral: 'healthy' | 'degraded' | 'down';
    coquiTTS: 'healthy' | 'degraded' | 'down';
    kokoroTTS: 'healthy' | 'degraded' | 'down';
    modal: 'healthy' | 'degraded' | 'down';
    s3: 'healthy' | 'degraded' | 'down';
  };
}

export class CourseGenerationMonitoringService extends EventEmitter {
  private metricsMap = new Map<string, GenerationMetrics>();
  private systemMetrics: SystemHealthMetrics[] = [];
  private readonly MAX_METRICS_HISTORY = 1000;
  private readonly MONITORING_INTERVAL = 30000; // 30 seconds
  private monitoringTimer?: NodeJS.Timeout;

  constructor() {
    super();
    this.startSystemMonitoring();
  }

  /**
   * Start monitoring a course generation job
   */
  startMonitoring(jobId: string, courseType: 'traditional' | 'avatar'): void {
    const metrics: GenerationMetrics = {
      jobId,
      courseType,
      startTime: new Date(),
      status: 'running',
      performance: {
        cpuUsage: [],
        memoryUsage: [],
        diskUsage: 0,
        networkUsage: 0,
        peakMemory: 0,
        averageCpu: 0
      },
      quality: {
        contentScore: 0,
        audioScore: 0,
        videoScore: 0,
        overallScore: 0,
        issues: [],
        warnings: []
      },
      resources: {
        filesCreated: 0,
        storageUsed: 0,
        apiCalls: {
          mistral: 0,
          coquiTTS: 0,
          kokoroTTS: 0,
          pexels: 0,
          pixabay: 0,
          modal: 0
        },
        costs: {
          estimated: 0,
          breakdown: {}
        }
      },
      progress: {
        currentStage: 'initializing',
        stagesCompleted: [],
        totalStages: courseType === 'avatar' ? 6 : 5,
        percentage: 0,
        estimatedTimeRemaining: 0
      },
      errors: {
        count: 0,
        details: []
      }
    };

    this.metricsMap.set(jobId, metrics);
    this.emit('monitoring_started', { jobId, courseType });
  }

  /**
   * Update job progress
   */
  updateProgress(
    jobId: string,
    stage: string,
    percentage: number,
    estimatedTimeRemaining: number
  ): void {
    const metrics = this.metricsMap.get(jobId);
    if (!metrics) return;

    metrics.progress.currentStage = stage;
    metrics.progress.percentage = percentage;
    metrics.progress.estimatedTimeRemaining = estimatedTimeRemaining;

    // Add to completed stages if not already there
    if (!metrics.progress.stagesCompleted.includes(stage) && percentage === 100) {
      metrics.progress.stagesCompleted.push(stage);
    }

    this.metricsMap.set(jobId, metrics);
    this.emit('progress_updated', { jobId, stage, percentage });
  }

  /**
   * Record API call
   */
  recordApiCall(jobId: string, service: keyof GenerationMetrics['resources']['apiCalls'], cost?: number): void {
    const metrics = this.metricsMap.get(jobId);
    if (!metrics) return;

    metrics.resources.apiCalls[service]++;
    
    if (cost) {
      metrics.resources.costs.estimated += cost;
      metrics.resources.costs.breakdown[service] = (metrics.resources.costs.breakdown[service] || 0) + cost;
    }

    this.metricsMap.set(jobId, metrics);
  }

  /**
   * Record error
   */
  recordError(
    jobId: string,
    stage: string,
    error: string,
    severity: 'low' | 'medium' | 'high' | 'critical'
  ): void {
    const metrics = this.metricsMap.get(jobId);
    if (!metrics) return;

    metrics.errors.count++;
    metrics.errors.details.push({
      timestamp: new Date(),
      stage,
      error,
      severity,
      resolved: false
    });

    this.metricsMap.set(jobId, metrics);
    this.emit('error_recorded', { jobId, stage, error, severity });
  }

  /**
   * Update performance metrics
   */
  updatePerformanceMetrics(jobId: string, cpu: number, memory: number, disk: number, network: number): void {
    const metrics = this.metricsMap.get(jobId);
    if (!metrics) return;

    metrics.performance.cpuUsage.push(cpu);
    metrics.performance.memoryUsage.push(memory);
    metrics.performance.diskUsage = disk;
    metrics.performance.networkUsage = network;
    metrics.performance.peakMemory = Math.max(metrics.performance.peakMemory, memory);
    metrics.performance.averageCpu = metrics.performance.cpuUsage.reduce((a, b) => a + b, 0) / metrics.performance.cpuUsage.length;

    // Keep only last 100 measurements
    if (metrics.performance.cpuUsage.length > 100) {
      metrics.performance.cpuUsage = metrics.performance.cpuUsage.slice(-100);
      metrics.performance.memoryUsage = metrics.performance.memoryUsage.slice(-100);
    }

    this.metricsMap.set(jobId, metrics);
  }

  /**
   * Complete job monitoring
   */
  completeMonitoring(jobId: string, status: 'completed' | 'failed' | 'cancelled', courseId?: number): void {
    const metrics = this.metricsMap.get(jobId);
    if (!metrics) return;

    metrics.endTime = new Date();
    metrics.duration = metrics.endTime.getTime() - metrics.startTime.getTime();
    metrics.status = status;
    metrics.courseId = courseId;

    this.metricsMap.set(jobId, metrics);
    this.emit('monitoring_completed', { jobId, status, duration: metrics.duration });

    // Store metrics in database for historical analysis
    this.storeMetricsInDatabase(metrics);
  }

  /**
   * Validate course quality
   */
  async validateCourseQuality(courseId: number): Promise<QualityValidationResult> {
    try {
      const course = await this.getCourseDetails(courseId);
      if (!course) {
        return {
          isValid: false,
          score: 0,
          issues: [{ type: 'structure', severity: 'high', description: 'Course not found', suggestion: 'Verify course ID' }],
          recommendations: ['Course does not exist']
        };
      }

      const issues: QualityValidationResult['issues'] = [];
      let score = 100;

      // Content validation
      const contentValidation = await this.validateContent(course);
      issues.push(...contentValidation.issues);
      score -= contentValidation.penalty;

      // Audio validation
      const audioValidation = await this.validateAudio(courseId);
      issues.push(...audioValidation.issues);
      score -= audioValidation.penalty;

      // Video validation
      const videoValidation = await this.validateVideo(courseId);
      issues.push(...videoValidation.issues);
      score -= videoValidation.penalty;

      // Structure validation
      const structureValidation = await this.validateStructure(courseId);
      issues.push(...structureValidation.issues);
      score -= structureValidation.penalty;

      const finalScore = Math.max(0, score);
      const isValid = finalScore >= 75; // 75% threshold

      return {
        isValid,
        score: finalScore,
        issues,
        recommendations: this.generateRecommendations(issues)
      };

    } catch (error) {
      console.error('Quality validation failed:', error);
      return {
        isValid: false,
        score: 0,
        issues: [{ type: 'structure', severity: 'critical', description: 'Validation error', suggestion: 'Contact support' }],
        recommendations: ['Quality validation failed']
      };
    }
  }

  /**
   * Get job metrics
   */
  getJobMetrics(jobId: string): GenerationMetrics | null {
    return this.metricsMap.get(jobId) || null;
  }

  /**
   * Get system health metrics
   */
  getSystemHealth(): SystemHealthMetrics | null {
    return this.systemMetrics[this.systemMetrics.length - 1] || null;
  }

  /**
   * Get historical metrics
   */
  getHistoricalMetrics(hours: number = 24): SystemHealthMetrics[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.systemMetrics.filter(m => m.timestamp >= cutoff);
  }

  /**
   * Get generation statistics
   */
  getGenerationStatistics(): {
    totalJobs: number;
    completedJobs: number;
    failedJobs: number;
    averageProcessingTime: number;
    successRate: number;
  } {
    const allMetrics = Array.from(this.metricsMap.values());
    const completedMetrics = allMetrics.filter(m => m.status === 'completed');
    const failedMetrics = allMetrics.filter(m => m.status === 'failed');

    const averageProcessingTime = completedMetrics.length > 0
      ? completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / completedMetrics.length
      : 0;

    const successRate = allMetrics.length > 0
      ? (completedMetrics.length / allMetrics.length) * 100
      : 0;

    return {
      totalJobs: allMetrics.length,
      completedJobs: completedMetrics.length,
      failedJobs: failedMetrics.length,
      averageProcessingTime,
      successRate
    };
  }

  /**
   * Start system monitoring
   */
  private startSystemMonitoring(): void {
    this.monitoringTimer = setInterval(async () => {
      await this.collectSystemMetrics();
    }, this.MONITORING_INTERVAL);
  }

  /**
   * Collect system metrics
   */
  private async collectSystemMetrics(): Promise<void> {
    try {
      const activeJobs = Array.from(this.metricsMap.values()).filter(m => m.status === 'running').length;
      const stats = this.getGenerationStatistics();

      const systemMetrics: SystemHealthMetrics = {
        timestamp: new Date(),
        activeJobs,
        queueLength: 0, // Would be implemented with actual queue
        averageProcessingTime: stats.averageProcessingTime,
        successRate: stats.successRate,
        errorRate: 100 - stats.successRate,
        resourceUtilization: await this.getResourceUtilization(),
        serviceStatus: await this.checkServiceStatus()
      };

      this.systemMetrics.push(systemMetrics);

      // Keep only recent metrics
      if (this.systemMetrics.length > this.MAX_METRICS_HISTORY) {
        this.systemMetrics = this.systemMetrics.slice(-this.MAX_METRICS_HISTORY);
      }

      this.emit('system_metrics_updated', systemMetrics);

    } catch (error) {
      console.error('Failed to collect system metrics:', error);
    }
  }

  /**
   * Get resource utilization
   */
  private async getResourceUtilization(): Promise<SystemHealthMetrics['resourceUtilization']> {
    // Simplified implementation - would use actual system monitoring
    return {
      cpu: Math.random() * 100,
      memory: Math.random() * 100,
      disk: Math.random() * 100,
      network: Math.random() * 100
    };
  }

  /**
   * Check service status
   */
  private async checkServiceStatus(): Promise<SystemHealthMetrics['serviceStatus']> {
    // Simplified implementation - would check actual service health
    return {
      database: 'healthy',
      mistral: 'healthy',
      coquiTTS: 'healthy',
      kokoroTTS: 'healthy',
      modal: 'healthy',
      s3: 'healthy'
    };
  }

  /**
   * Validate content quality
   */
  private async validateContent(course: any): Promise<{ issues: QualityValidationResult['issues']; penalty: number }> {
    const issues: QualityValidationResult['issues'] = [];
    let penalty = 0;

    if (!course.title || course.title.length < 5) {
      issues.push({
        type: 'content',
        severity: 'medium',
        description: 'Course title is too short',
        suggestion: 'Use a more descriptive title (at least 5 characters)'
      });
      penalty += 10;
    }

    if (!course.description || course.description.length < 50) {
      issues.push({
        type: 'content',
        severity: 'medium',
        description: 'Course description is insufficient',
        suggestion: 'Add a comprehensive description (at least 50 characters)'
      });
      penalty += 15;
    }

    return { issues, penalty };
  }

  /**
   * Validate audio quality
   */
  private async validateAudio(courseId: number): Promise<{ issues: QualityValidationResult['issues']; penalty: number }> {
    const issues: QualityValidationResult['issues'] = [];
    let penalty = 0;

    // Check if audio files exist for all lessons
    const audioCount = await this.getAudioAssetCount(courseId);
    const lessonCount = await this.getLessonCount(courseId);

    if (audioCount < lessonCount) {
      issues.push({
        type: 'audio',
        severity: 'high',
        description: 'Missing audio for some lessons',
        suggestion: 'Ensure all lessons have voice narration'
      });
      penalty += 25;
    }

    return { issues, penalty };
  }

  /**
   * Validate video quality
   */
  private async validateVideo(courseId: number): Promise<{ issues: QualityValidationResult['issues']; penalty: number }> {
    const issues: QualityValidationResult['issues'] = [];
    let penalty = 0;

    // Check if videos exist for all lessons
    const completedLessons = await this.getCompletedLessonsCount(courseId);
    const totalLessons = await this.getLessonCount(courseId);

    if (completedLessons < totalLessons) {
      issues.push({
        type: 'video',
        severity: 'high',
        description: 'Incomplete video generation',
        suggestion: 'Complete video generation for all lessons'
      });
      penalty += 30;
    }

    return { issues, penalty };
  }

  /**
   * Validate course structure
   */
  private async validateStructure(courseId: number): Promise<{ issues: QualityValidationResult['issues']; penalty: number }> {
    const issues: QualityValidationResult['issues'] = [];
    let penalty = 0;

    const lessonCount = await this.getLessonCount(courseId);
    
    if (lessonCount < 3) {
      issues.push({
        type: 'structure',
        severity: 'high',
        description: 'Insufficient number of lessons',
        suggestion: 'Add more lessons for comprehensive coverage'
      });
      penalty += 20;
    }

    return { issues, penalty };
  }

  /**
   * Generate quality recommendations
   */
  private generateRecommendations(issues: QualityValidationResult['issues']): string[] {
    const recommendations = issues.map(issue => issue.suggestion);
    
    if (recommendations.length === 0) {
      recommendations.push('Course quality is excellent! Consider adding interactive elements.');
    }
    
    return [...new Set(recommendations)]; // Remove duplicates
  }

  /**
   * Store metrics in database
   */
  private async storeMetricsInDatabase(metrics: GenerationMetrics): Promise<void> {
    try {
      await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        
        // Store in a metrics table (would need to create this table)
        // For now, just log the metrics
        console.log('Storing metrics for job:', metrics.jobId, {
          duration: metrics.duration,
          status: metrics.status,
          qualityScore: metrics.quality.overallScore,
          resourceUsage: metrics.resources
        });
      });
    } catch (error) {
      console.error('Failed to store metrics in database:', error);
    }
  }

  /**
   * Helper methods for database queries
   */
  private async getCourseDetails(courseId: number): Promise<any> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const [course] = await db.select().from(courses).where(eq(courses.id, courseId));
      return course;
    });
  }

  private async getAudioAssetCount(courseId: number): Promise<number> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const result = await db.execute(`SELECT COUNT(*) as count FROM media_library WHERE course_id = ${courseId} AND type = 'audio'`);
      return parseInt(result[0]?.count as string || '0');
    }) || 0;
  }

  private async getLessonCount(courseId: number): Promise<number> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const result = await db.execute(`SELECT COUNT(*) as count FROM lessons WHERE course_id = ${courseId}`);
      return parseInt(result[0]?.count as string || '0');
    }) || 0;
  }

  private async getCompletedLessonsCount(courseId: number): Promise<number> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const result = await db.execute(`SELECT COUNT(*) as count FROM lessons WHERE course_id = ${courseId} AND status = 'completed'`);
      return parseInt(result[0]?.count as string || '0');
    }) || 0;
  }

  /**
   * Cleanup on shutdown
   */
  shutdown(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
  }
}

export const courseGenerationMonitoringService = new CourseGenerationMonitoringService();
