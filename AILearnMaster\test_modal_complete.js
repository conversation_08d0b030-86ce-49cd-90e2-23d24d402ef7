#!/usr/bin/env node
/**
 * Complete Modal A100 GPU Integration Test
 * Tests all Modal endpoints and fallback systems
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:5000/api';

async function testModalCredentials() {
  console.log('=== Testing Modal Credentials ===\n');
  
  try {
    const response = await axios.get(`${BASE_URL}/modal/validate-credentials`);
    
    console.log('✅ Credentials validation response:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data.credentials_present;
  } catch (error) {
    console.log('❌ Failed to validate credentials:', error.message);
    return false;
  }
}

async function testModalHealth() {
  console.log('\n=== Testing Modal Health Check ===\n');
  
  try {
    const response = await axios.get(`${BASE_URL}/modal/health`);
    
    console.log('Health check response:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data.status === 'healthy';
  } catch (error) {
    console.log('Health check failed (expected if not deployed):', error.response?.data || error.message);
    return false;
  }
}

async function testModalEcho() {
  console.log('\n=== Testing Modal Echo Function ===\n');
  
  try {
    const testMessage = 'Hello from Koursia Platform Integration Test';
    const response = await axios.post(`${BASE_URL}/modal/echo`, {
      message: testMessage
    });
    
    console.log('Echo test response:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data.echo === testMessage;
  } catch (error) {
    console.log('Echo test failed (expected if not deployed):', error.response?.data || error.message);
    return false;
  }
}

async function testImageGeneration() {
  console.log('\n=== Testing Image Generation ===\n');
  
  try {
    const response = await axios.post(`${BASE_URL}/modal/generate-image`, {
      prompt: 'A professional course instructor teaching AI concepts',
      width: 512,
      height: 512
    });
    
    console.log('Image generation response:');
    console.log('Status:', response.data.status);
    console.log('Prompt:', response.data.prompt);
    console.log('Image data length:', response.data.image_base64?.length || 0);
    
    return response.data.status === 'success';
  } catch (error) {
    console.log('Image generation failed (expected if not deployed):', error.response?.data || error.message);
    return false;
  }
}

async function testTextAnalysis() {
  console.log('\n=== Testing Text Analysis ===\n');
  
  try {
    const response = await axios.post(`${BASE_URL}/modal/analyze-text`, {
      text: 'This is an excellent course platform with amazing AI capabilities!'
    });
    
    console.log('Text analysis response:');
    console.log(JSON.stringify(response.data, null, 2));
    
    return response.data.status === 'success';
  } catch (error) {
    console.log('Text analysis failed (expected if not deployed):', error.response?.data || error.message);
    return false;
  }
}

async function runCompleteTest() {
  console.log('Modal A100 GPU Integration - Complete Test Suite\n');
  console.log('='.repeat(60));
  
  const results = {
    credentials: await testModalCredentials(),
    health: await testModalHealth(),
    echo: await testModalEcho(),
    imageGeneration: await testImageGeneration(),
    textAnalysis: await testTextAnalysis()
  };
  
  console.log('\n' + '='.repeat(60));
  console.log('TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  
  Object.keys(results).forEach(test => {
    const status = results[test] ? '✅ PASS' : '❌ FAIL';
    console.log(`${test.padEnd(20)}: ${status}`);
  });
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  
  console.log('\n' + '='.repeat(60));
  console.log(`OVERALL: ${passedTests}/${totalTests} tests passed`);
  
  if (results.credentials) {
    console.log('\n✅ Modal credentials are properly configured');
    if (!results.health) {
      console.log('📋 Modal A100 GPU app needs to be deployed to activate GPU functions');
      console.log('   This is normal - the infrastructure is ready for deployment');
    }
  } else {
    console.log('\n❌ Modal credentials need to be refreshed');
  }
  
  console.log('='.repeat(60));
  
  return passedTests >= 1; // At least credentials should work
}

// Run the test
runCompleteTest()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });