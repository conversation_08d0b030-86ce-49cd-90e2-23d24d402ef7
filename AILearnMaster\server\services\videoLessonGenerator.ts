/**
 * Video Lesson Generator Service
 * Generates synchronized video lessons by combining text-to-speech audio with images
 */

import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import { storage } from '../storage';
import { InsertMedia } from '@shared/schema';
// Use Coqui TTS as primary with fallback to ElevenLabs
import { generateImage } from './openai';
// We'll use OpenAI's image generation instead of StabilityAI
import { spawn } from 'child_process';
import { Media } from '@shared/schema';

// In-memory job storage (could be moved to database in production)
const activeJobs: Record<string, VideoLessonJobStatus> = {};

// Directory for temporary files
const TEMP_DIR = path.join(process.cwd(), 'temp');
const UPLOADS_DIR = path.join(process.cwd(), 'uploads');

// Ensure directories exist
if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}
if (!fs.existsSync(UPLOADS_DIR)) {
  fs.mkdirSync(UPLOADS_DIR, { recursive: true });
}

export interface VideoLessonGenerationOptions {
  userId: number;
  lessonTitle: string;
  script: string;
  mediaIds?: number[];  // IDs of images/videos to include
  voiceId?: string;     // ElevenLabs voice ID
  style?: string;       // Visual style preference
  subtitles?: boolean;  // Whether to generate subtitles
  courseId?: number;
  lessonId?: number;
}

export interface VideoLessonSegment {
  type: 'text' | 'image' | 'video';
  content: string;
  mediaId?: number;
  startTime: number;
  endTime: number;
}

export interface VideoLessonJobStatus {
  id: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  userId: number;
  lessonTitle: string;
  mediaId?: number;
  videoUrl?: string;
  error?: string;
  createdAt: number;
  updatedAt: number;
  estimatedCompletionTime: number;
}

/**
 * Start a video lesson generation job
 */
export async function startVideoLessonGeneration(
  options: VideoLessonGenerationOptions
): Promise<VideoLessonJobStatus> {
  const jobId = uuidv4();
  const now = Date.now();
  
  // Estimate completion time based on script length and complexity
  // Rough estimate: 10s base + 1s per word + 5s per image
  const wordCount = options.script.split(/\s+/).length;
  const mediaCount = options.mediaIds?.length || 0;
  const estimatedSeconds = 10 + (wordCount * 1) + (mediaCount * 5);
  
  const job: VideoLessonJobStatus = {
    id: jobId,
    status: 'queued',
    progress: 0,
    userId: options.userId,
    lessonTitle: options.lessonTitle,
    createdAt: now,
    updatedAt: now,
    estimatedCompletionTime: now + (estimatedSeconds * 1000)
  };
  
  activeJobs[jobId] = job;
  
  // Process the job asynchronously
  processVideoLessonJob(jobId, options).catch(error => {
    console.error(`Error processing video lesson job ${jobId}:`, error);
    updateJobStatus(jobId, {
      status: 'failed',
      progress: 0,
      error: error.message
    });
  });
  
  return job;
}

/**
 * Get the status of a video lesson generation job
 */
export function getVideoLessonStatus(jobId: string): VideoLessonJobStatus | null {
  return activeJobs[jobId] || null;
}

/**
 * Update the status of a video lesson job
 */
function updateJobStatus(
  jobId: string, 
  update: Partial<VideoLessonJobStatus>
) {
  if (activeJobs[jobId]) {
    activeJobs[jobId] = {
      ...activeJobs[jobId],
      ...update,
      updatedAt: Date.now()
    };
  }
}

/**
 * Process a video lesson generation job
 */
async function processVideoLessonJob(
  jobId: string,
  options: VideoLessonGenerationOptions
): Promise<void> {
  try {
    updateJobStatus(jobId, { status: 'processing', progress: 5 });
    
    // Step 1: Generate audio from script
    console.log(`Generating audio for lesson "${options.lessonTitle}"`);
    updateJobStatus(jobId, { progress: 10 });
    
    // Use Coqui TTS as the primary service
    const coquiTTS = await import('./coquiTTS');
    const { VOICE_IDS } = coquiTTS;
    
    let speechResult;
    try {
      // First try with Coqui TTS
      speechResult = await coquiTTS.generateSpeech({
        text: options.script,
        voiceId: options.voiceId || Object.keys(VOICE_IDS)[0], // Use first available Coqui voice
      }, options.userId);
      
      // If Coqui fails, fall back to ElevenLabs
      if (speechResult.error) {
        console.log("Coqui TTS failed, trying ElevenLabs as fallback...");
        const elevenlabs = await import('./elevenlabs');
        speechResult = await elevenlabs.generateSpeech(options.userId, {
          text: options.script,
          voiceId: options.voiceId || 'Rachel', // Default to Rachel voice
          modelId: 'eleven_multilingual_v2',
          stability: 0.5,
          similarityBoost: 0.75
        });
      }
    } catch (error) {
      // If Coqui TTS throws an exception, try ElevenLabs
      console.log("Coqui TTS error, trying ElevenLabs as fallback...", error);
      const elevenlabs = await import('./elevenlabs');
      speechResult = await elevenlabs.generateSpeech(options.userId, {
        text: options.script,
        voiceId: options.voiceId || 'Rachel', // Default to Rachel voice
        modelId: 'eleven_multilingual_v2',
        stability: 0.5,
        similarityBoost: 0.75
      });
    }
    
    // Check for errors
    if (!speechResult.audioData || speechResult.error) {
      throw new Error(speechResult.error || 'Failed to generate audio for the lesson');
    }
    
    // Use the audio buffer from the speech result
    const audioBuffer = speechResult.audioData;
    
    // Save audio to temp file
    const audioFilePath = path.join(TEMP_DIR, `${jobId}_audio.mp3`);
    fs.writeFileSync(audioFilePath, audioBuffer);
    updateJobStatus(jobId, { progress: 30 });
    
    // Step 2: Fetch or generate images for the lesson
    let mediaItems: Media[] = [];
    
    if (options.mediaIds && options.mediaIds.length > 0) {
      // Fetch existing media items
      const fetchedMedia = await Promise.all(
        options.mediaIds.map(id => storage.getMediaById(id))
      );
      mediaItems = fetchedMedia.filter((item): item is Media => !!item); // Remove any null/undefined items
    }
    
    // If not enough media items, generate some based on the script
    if (mediaItems.length < 2) {
      const segments = splitScriptIntoSegments(options.script);
      
      for (let i = 0; i < Math.min(5, segments.length); i++) {
        updateJobStatus(jobId, { 
          progress: 30 + Math.floor((i / segments.length) * 30) 
        });
        
        // Generate an image based on the segment
        try {
          const response = await generateImage(segments[i], '1024x1024');
          if (response && response.url) {
            // Download the image
            const imageResponse = await fetch(response.url);
            const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
            
            // Save to uploads folder
            const imageName = `lesson_image_${Date.now()}_${i}.png`;
            const imagePath = path.join(UPLOADS_DIR, imageName);
            fs.writeFileSync(imagePath, imageBuffer);
            
            // Add to media library
            const imageMedia = await storage.createMedia({
              userId: options.userId,
              name: `Image for ${options.lessonTitle}`,
              type: 'image',
              mimeType: 'image/png',
              fileSize: imageBuffer.length,
              url: `/uploads/${imageName}`,
              courseId: options.courseId,
              lessonId: options.lessonId,
              source: 'generated'
            });
            
            mediaItems.push(imageMedia);
          }
        } catch (error) {
          console.error(`Failed to generate image for segment ${i}:`, error);
          // Continue with other segments if one fails
        }
      }
    }
    
    updateJobStatus(jobId, { progress: 60 });
    
    // Step 3: Generate video by combining audio with images
    const videoFilePath = await generateSynchronizedVideo(
      audioFilePath,
      mediaItems,
      jobId,
      options
    );
    
    if (!videoFilePath) {
      throw new Error('Failed to generate synchronized video');
    }
    
    updateJobStatus(jobId, { progress: 90 });
    
    // Step 4: Add to media library
    const videoStats = fs.statSync(videoFilePath);
    const videoFileName = path.basename(videoFilePath);
    
    // Move from temp to uploads
    const finalVideoPath = path.join(UPLOADS_DIR, videoFileName);
    fs.copyFileSync(videoFilePath, finalVideoPath);
    
    // Create media record
    const videoMediaItem: InsertMedia = {
      userId: options.userId,
      name: options.lessonTitle,
      type: 'video',
      mimeType: 'video/mp4',
      fileSize: videoStats.size,
      url: `/uploads/${videoFileName}`,
      courseId: options.courseId,
      lessonId: options.lessonId,
      duration: 0, // Will be determined when video is processed
      source: 'generated'
    };
    
    const media = await storage.createMedia(videoMediaItem);
    
    // Clean up temp files
    try {
      fs.unlinkSync(audioFilePath);
      fs.unlinkSync(videoFilePath);
    } catch (error) {
      console.error('Error cleaning up temp files:', error);
    }
    
    // Update job status to completed
    updateJobStatus(jobId, {
      status: 'completed',
      progress: 100,
      mediaId: media.id,
      videoUrl: media.url
    });
    
    console.log(`Video lesson generation completed for "${options.lessonTitle}"`);
  } catch (error: unknown) {
    console.error(`Error in video lesson generation for "${options.lessonTitle}":`, error);
    const errorMessage = error instanceof Error 
      ? error.message 
      : String(error);
    
    updateJobStatus(jobId, {
      status: 'failed',
      error: errorMessage
    });
    
    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error(errorMessage);
    }
  }
}

/**
 * Split a script into segments for synchronization with media
 */
function splitScriptIntoSegments(script: string): string[] {
  // Simple splitting by sentences, limiting to 3 sentences per segment
  const sentences = script
    .replace(/([.!?])\s*(?=[A-Z])/g, "$1|")
    .split("|")
    .map(s => s.trim())
    .filter(s => s.length > 0);
  
  const segments = [];
  let currentSegment = '';
  
  sentences.forEach(sentence => {
    if (currentSegment && 
        (currentSegment.split('.').length > 3 || 
         currentSegment.length + sentence.length > 300)) {
      segments.push(currentSegment);
      currentSegment = sentence;
    } else {
      currentSegment = currentSegment
        ? `${currentSegment} ${sentence}`
        : sentence;
    }
  });
  
  if (currentSegment) {
    segments.push(currentSegment);
  }
  
  return segments;
}

/**
 * Calculate the total duration of the video
 */
function calculateVideoDuration(audioDuration: number, mediaItems: Media[]): number {
  // In a real implementation, this would calculate timing based on the script
  // For now, just use audio duration
  return audioDuration;
}

/**
 * Generate a synchronized video with audio and media
 */
function generateSynchronizedVideo(
  audioFilePath: string,
  mediaItems: Media[],
  jobId: string,
  options: VideoLessonGenerationOptions
): Promise<string | null> {
  return new Promise((resolve, reject) => {
    try {
      // Output file path
      const outputPath = path.join(TEMP_DIR, `${jobId}_lesson.mp4`);
      
      // Get the best image to use
      // If multiple images, we'll just use the first one to keep it simple and reliable
      let imagePath = '';
      
      // Find the first valid image from media items
      for (const media of mediaItems) {
        if (media.type === 'image') {
          if (media.url.startsWith('http')) {
            imagePath = media.url;
          } else {
            imagePath = path.join(process.cwd(), media.url.replace(/^\//, ''));
          }
          if (fs.existsSync(imagePath)) {
            break;
          }
        }
      }
      
      // If no valid image found, use a blank image
      if (!imagePath || !fs.existsSync(imagePath)) {
        const blankImagePath = path.join(process.cwd(), 'client', 'public', 'blank.png');
        
        // Create blank image if it doesn't exist
        if (!fs.existsSync(blankImagePath)) {
          console.log("Creating blank image for video...");
          
          // Ensure directories exist
          const publicDir = path.join(process.cwd(), 'client', 'public');
          if (!fs.existsSync(publicDir)) {
            fs.mkdirSync(publicDir, { recursive: true });
          }
          
          try {
            // Try to use ImageMagick to create a blank image
            try {
              require('child_process').execSync(
                `convert -size 1920x1080 xc:white "${blankImagePath}"`
              );
            } catch (e) {
              // If ImageMagick fails, create a simple text file as placeholder
              fs.writeFileSync(blankImagePath, 'Blank Image');
            }
          } catch (e) {
            console.error('Failed to create blank image:', e);
            // Last resort - create a simple text file
            const textImagePath = path.join(TEMP_DIR, `${jobId}_blank.txt`);
            fs.writeFileSync(textImagePath, 'Blank Image');
            imagePath = textImagePath;
          }
        }
        
        if (fs.existsSync(blankImagePath)) {
          imagePath = blankImagePath;
        } else {
          // Emergency fallback - create a text file in temp directory
          const emergencyImage = path.join(TEMP_DIR, `${jobId}_emergency.txt`);
          fs.writeFileSync(emergencyImage, 'Emergency Blank Image');
          imagePath = emergencyImage;
        }
      }
      
      console.log(`Using image for video: ${imagePath}`);
      
      // Use the simplest, most reliable FFmpeg command possible
      // Single static image + audio = video
      const ffmpegArgs = [
        '-loop', '1',           // Loop the image
        '-i', imagePath,        // Input image
        '-i', audioFilePath,    // Input audio
        '-c:v', 'libx264',      // Video codec
        '-tune', 'stillimage',  // Optimize for still image
        '-c:a', 'aac',          // Audio codec
        '-b:a', '192k',         // Audio bitrate
        '-pix_fmt', 'yuv420p',  // Pixel format
        '-shortest',            // End when shortest input ends (audio)
        '-y',                   // Overwrite output
        outputPath              // Output file
      ];
      
      // Log the command for debugging
      console.log('Running FFmpeg command:', ffmpegArgs.join(' '));
      
      // Execute FFmpeg
      const ffmpeg = spawn('ffmpeg', ffmpegArgs);
      
      // Collect output for detailed logging
      let stdoutChunks = [];
      let stderrChunks = [];
      
      ffmpeg.stdout.on('data', (data) => {
        stdoutChunks.push(data);
        console.log(`ffmpeg stdout: ${data}`);
      });
      
      ffmpeg.stderr.on('data', (data) => {
        stderrChunks.push(data);
        console.log(`ffmpeg stderr: ${data}`);
      });
      
      ffmpeg.on('close', (code) => {
        if (code === 0) {
          console.log(`Video created successfully: ${outputPath}`);
          resolve(outputPath);
        } else {
          // Log the full output for debugging
          console.error(`FFmpeg process failed with code ${code}`);
          console.error('FFmpeg stdout:', Buffer.concat(stdoutChunks).toString());
          console.error('FFmpeg stderr:', Buffer.concat(stderrChunks).toString());
          
          // Check if output file exists despite error
          if (fs.existsSync(outputPath) && fs.statSync(outputPath).size > 0) {
            console.log(`Output file exists despite error, trying to use it anyway`);
            resolve(outputPath);
          } else {
            reject(new Error(`FFmpeg process exited with code ${code}`));
          }
        }
      });
      
      ffmpeg.on('error', (err) => {
        console.error('Failed to start FFmpeg process:', err);
        reject(err);
      });
    } catch (error) {
      console.error('Error in generateSynchronizedVideo:', error);
      reject(error);
    }
  });
}

/**
 * Generate a subtitle file (SRT format) from script segments
 */
async function generateSubtitleFile(
  script: string,
  audioDuration: number,
  outputPath: string
): Promise<string> {
  const segments = splitScriptIntoSegments(script);
  
  // Simple approach: divide audio duration by number of segments
  const segmentDuration = audioDuration / segments.length;
  
  let srtContent = '';
  
  segments.forEach((segment, index) => {
    const startTime = index * segmentDuration;
    const endTime = (index + 1) * segmentDuration;
    
    // Format: HH:MM:SS,MS
    const formatTime = (timeInSeconds: number) => {
      const hours = Math.floor(timeInSeconds / 3600);
      const minutes = Math.floor((timeInSeconds % 3600) / 60);
      const seconds = Math.floor(timeInSeconds % 60);
      const ms = Math.floor((timeInSeconds % 1) * 1000);
      
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')},${String(ms).padStart(3, '0')}`;
    };
    
    srtContent += `${index + 1}\n`;
    srtContent += `${formatTime(startTime)} --> ${formatTime(endTime)}\n`;
    srtContent += `${segment}\n\n`;
  });
  
  fs.writeFileSync(outputPath, srtContent);
  return outputPath;
}