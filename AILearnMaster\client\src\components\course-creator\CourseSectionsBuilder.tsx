import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { 
  GripVertical, 
  Plus, 
  Trash2, 
  Edit, 
  RotateCw, 
  Sparkles, 
  Book, 
  FileText, 
  Film, 
  BrainCircuit, 
  ArrowRight,
  ArrowLeft
} from "lucide-react";
import { v4 as uuidv4 } from 'uuid';
import { cn } from "@/lib/utils";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription, 
  DialogFooter 
} from "@/components/ui/dialog";

export function CourseSectionsBuilder({ 
  onSubmit,
  onBack, 
  defaultValues = { sections: [] },
  useAI = true
}) {
  const [courseStructure, setCourseStructure] = useState(defaultValues);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isBalancingComplexity, setIsBalancingComplexity] = useState(false);
  const [editingSectionId, setEditingSectionId] = useState(null);
  const [newSectionTitle, setNewSectionTitle] = useState("");
  const [moduleCount, setModuleCount] = useState(defaultValues.sections.length || 3);
  const [aiModuleCount, setAiModuleCount] = useState(4); // Default module count for AI generation
  const [isModuleCountDialogOpen, setIsModuleCountDialogOpen] = useState(false);
  const [isAiModuleCountDialogOpen, setIsAiModuleCountDialogOpen] = useState(false);
  const { toast } = useToast();

  // Add Lesson Dialog
  const [isAddLessonDialogOpen, setIsAddLessonDialogOpen] = useState(false);
  const [activeSectionForLesson, setActiveSectionForLesson] = useState(null);
  const [newLesson, setNewLesson] = useState({
    title: '',
    type: 'video'
  });

  // Edit Lesson Dialog
  const [isEditLessonDialogOpen, setIsEditLessonDialogOpen] = useState(false);
  const [lessonToEdit, setLessonToEdit] = useState(null);
  const [activeSectionForEditing, setActiveSectionForEditing] = useState(null);

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const { source, destination, type } = result;

    // If dropped in a different position
    if (source.droppableId !== destination.droppableId || source.index !== destination.index) {
      const newStructure = { ...courseStructure };

      // Handle section drag
      if (type === 'SECTION') {
        const sections = Array.from(newStructure.sections);
        const [movedSection] = sections.splice(source.index, 1);
        sections.splice(destination.index, 0, movedSection);
        newStructure.sections = sections;
      } 
      // Handle lesson drag
      else if (type === 'LESSON') {
        const sourceSectionIndex = parseInt(source.droppableId.split('-')[1]);
        const destSectionIndex = parseInt(destination.droppableId.split('-')[1]);
        
        // If moving within the same section
        if (sourceSectionIndex === destSectionIndex) {
          const lessons = Array.from(newStructure.sections[sourceSectionIndex].lessons);
          const [movedLesson] = lessons.splice(source.index, 1);
          lessons.splice(destination.index, 0, movedLesson);
          newStructure.sections[sourceSectionIndex].lessons = lessons;
        } 
        // If moving between different sections
        else {
          const sourceSection = newStructure.sections[sourceSectionIndex];
          const destSection = newStructure.sections[destSectionIndex];
          const [movedLesson] = sourceSection.lessons.splice(source.index, 1);
          destSection.lessons.splice(destination.index, 0, movedLesson);
        }
      }

      setCourseStructure(newStructure);
    }
  };

  // Add a new section
  const addSection = () => {
    if (!newSectionTitle.trim()) {
      toast({
        title: "Section title required",
        description: "Please enter a title for the new section",
        variant: "destructive",
      });
      return;
    }

    const newSection = {
      id: uuidv4(),
      title: newSectionTitle,
      lessons: []
    };

    setCourseStructure(prev => ({
      sections: [...prev.sections, newSection]
    }));

    setNewSectionTitle("");
  };

  // Delete a section
  const deleteSection = (sectionId) => {
    setCourseStructure(prev => ({
      sections: prev.sections.filter(section => section.id !== sectionId)
    }));
  };

  // Edit a section title
  const editSection = (sectionId) => {
    setEditingSectionId(sectionId);
  };

  // Update a section title
  const updateSectionTitle = (sectionId, newTitle) => {
    if (!newTitle.trim()) {
      toast({
        title: "Section title required",
        description: "Section title cannot be empty",
        variant: "destructive",
      });
      return;
    }

    setCourseStructure(prev => ({
      sections: prev.sections.map(section => 
        section.id === sectionId ? { ...section, title: newTitle } : section
      )
    }));

    setEditingSectionId(null);
  };

  const openAddLessonDialog = (sectionId) => {
    setActiveSectionForLesson(sectionId);
    setNewLesson({
      title: '',
      type: 'video'
    });
    setIsAddLessonDialogOpen(true);
  };

  const addLesson = () => {
    if (!activeSectionForLesson) return;
    
    if (!newLesson.title.trim()) {
      toast({
        title: "Lesson title required",
        description: "Please enter a title for the new lesson",
        variant: "destructive",
      });
      return;
    }

    const lessonToAdd = {
      id: uuidv4(),
      ...newLesson
    };

    setCourseStructure(prev => ({
      sections: prev.sections.map(section => 
        section.id === activeSectionForLesson 
          ? { ...section, lessons: [...section.lessons, lessonToAdd] } 
          : section
      )
    }));

    setIsAddLessonDialogOpen(false);
    setActiveSectionForLesson(null);
  };

  // Delete a lesson
  const deleteLesson = (sectionId, lessonId) => {
    setCourseStructure(prev => ({
      sections: prev.sections.map(section => 
        section.id === sectionId 
          ? { ...section, lessons: section.lessons.filter(lesson => lesson.id !== lessonId) } 
          : section
      )
    }));
  };

  const openEditLessonDialog = (sectionId, lesson) => {
    setActiveSectionForEditing(sectionId);
    setLessonToEdit(lesson);
    setIsEditLessonDialogOpen(true);
  };

  const updateLesson = () => {
    if (!activeSectionForEditing || !lessonToEdit) return;
    
    if (!lessonToEdit.title.trim()) {
      toast({
        title: "Lesson title required",
        description: "Lesson title cannot be empty",
        variant: "destructive",
      });
      return;
    }

    setCourseStructure(prev => ({
      sections: prev.sections.map(section => 
        section.id === activeSectionForEditing 
          ? { 
              ...section, 
              lessons: section.lessons.map(lesson => 
                lesson.id === lessonToEdit.id 
                  ? lessonToEdit 
                  : lesson
              ) 
            } 
          : section
      )
    }));

    setIsEditLessonDialogOpen(false);
    setLessonToEdit(null);
    setActiveSectionForEditing(null);
  };

  // Module complexity balancing functionality
  const balanceModuleComplexityMutation = useMutation({
    mutationFn: async () => {
      setIsBalancingComplexity(true);
      
      try {
        // Extract current course structure data
        const currentStructure = courseStructure.sections.map(section => ({
          id: section.id,
          title: section.title,
          lessonCount: section.lessons?.length || 0,
          lessons: section.lessons?.map(lesson => ({
            id: lesson.id,
            title: lesson.title,
            type: lesson.type
          })) || []
        }));
        
        // Call the AI service to balance module complexity
        const response = await apiRequest('POST', '/api/ai/balance-module-complexity', {
          modules: currentStructure
        });
        
        if (!response.ok) {
          console.error("Server returned error status:", response.status);
          throw new Error('Failed to balance module complexity');
        }
        
        const data = await response.json();
        return data;
      } catch (error) {
        console.error("Error balancing module complexity:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      try {
        console.log("Processing balanced module structure");
        
        // Update course structure with the balanced modules
        if (Array.isArray(data.modules)) {
          const balancedSections = data.modules.map(module => {
            // Find the original section to preserve IDs
            const originalSection = courseStructure.sections.find(s => s.title === module.title) || {
              id: uuidv4(),
              lessons: []
            };
            
            return {
              id: originalSection.id,
              title: module.title,
              complexity: module.complexity || 'balanced',
              lessons: module.lessons.map((lesson, idx) => {
                // Try to find original lesson to preserve IDs
                const originalLesson = originalSection.lessons && 
                  originalSection.lessons[idx] || { id: uuidv4() };
                
                return {
                  id: originalLesson.id,
                  title: lesson.title,
                  type: lesson.type || 'video',
                  complexity: lesson.complexity || 'medium'
                };
              })
            };
          });
          
          setCourseStructure({ sections: balancedSections });
          
          toast({
            title: "Modules balanced",
            description: "Your course modules have been rebalanced for optimal learning progression.",
          });
        } else {
          throw new Error("Invalid response format from the complexity balancer");
        }
      } catch (err) {
        console.error("Error processing balanced modules:", err);
        toast({
          title: "Error balancing modules",
          description: "Unable to balance modules. Your course structure remains unchanged.",
          variant: "destructive",
        });
      }
      
      setIsBalancingComplexity(false);
    },
    onError: (error) => {
      console.error("Module complexity balancing error:", error);
      setIsBalancingComplexity(false);
      toast({
        title: "Balancing failed",
        description: "Failed to balance module complexity. Please try again later.",
        variant: "destructive",
      });
    }
  });

  // Function to open the AI module count dialog before generating
  const openAiGenerationDialog = () => {
    setIsAiModuleCountDialogOpen(true);
  };
  
  // Function to handle AI generation with specified module count
  const handleAiGeneration = (moduleCount) => {
    setAiModuleCount(moduleCount);
    setIsAiModuleCountDialogOpen(false);
    generateStructureMutation.mutate(moduleCount);
  };

  // AI structure generation
  const generateStructureMutation = useMutation({
    mutationFn: async (requestedModuleCount) => {
      setIsGenerating(true);
      const courseTitle = 'New Course';
      console.log(`Requesting AI-generated course structure with ${requestedModuleCount} modules`);
      
      try {
        const response = await apiRequest('POST', '/api/ai/generate-course-structure', {
          title: courseTitle,
          description: 'A comprehensive course on this subject',
          category: 'Education',
          level: 'beginner',
          moduleCount: requestedModuleCount || aiModuleCount // Pass the requested module count
        });
        
        if (!response.ok) {
          console.error("Server returned error status:", response.status);
          throw new Error('Failed to generate course structure');
        }
        
        const data = await response.json();
        console.log("Successfully received AI-generated structure response");
        return data;
      } catch (error) {
        console.error("Network or parsing error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      try {
        console.log("Processing AI response for course structure");
        
        // Handle different response formats with more robust error handling
        const modules = data.modules || data.sections || [];
        
        if (!Array.isArray(modules)) {
          console.error("Modules is not an array:", modules);
          throw new Error("Invalid response format: modules is not an array");
        }
        
        // Convert AI generated structure to our format with better error checking
        const generatedSections = modules.map(module => {
          // Ensure module has expected properties or assign defaults
          const moduleTitle = module?.title || 'Untitled Section';
          const moduleLessons = module?.lessons || [];
          
          // Ensure lessons is an array
          const lessonArray = Array.isArray(moduleLessons) ? moduleLessons : [];
          
          return {
            id: uuidv4(),
            title: moduleTitle,
            lessons: lessonArray.map(lesson => ({
              id: uuidv4(),
              title: lesson?.title || 'Untitled Lesson',
              type: lesson?.type && ['video', 'text', 'quiz'].includes(lesson.type) ? lesson.type : 'video',
              content: lesson?.content || '',
              duration: lesson?.duration || 0
            }))
          };
        });
        
        setCourseStructure({ sections: generatedSections });
        
        toast({
          title: "Structure generated",
          description: "AI-generated structure has been created. Feel free to modify it as needed.",
        });
        
        console.log("Course structure generation completed successfully");
      } catch (err) {
        console.error("Error processing AI response:", err);
        toast({
          title: "Error processing response",
          description: "There was an issue with the AI response. A basic structure has been created that you can modify.",
          variant: "destructive",
        });
        
        // Create a minimal fallback structure on the client side if processing fails
        const fallbackSections = [
          {
            id: uuidv4(),
            title: "Introduction",
            lessons: [
              {
                id: uuidv4(),
                title: "Getting Started",
                type: "video",
                content: "",
                duration: 0
              }
            ]
          },
          {
            id: uuidv4(),
            title: "Main Content",
            lessons: [
              {
                id: uuidv4(),
                title: "Key Concepts",
                type: "video",
                content: "",
                duration: 0
              }
            ]
          }
        ];
        
        setCourseStructure({ sections: fallbackSections });
      }
      setIsGenerating(false);
    },
    onError: (error) => {
      console.error("AI generation error:", error);
      setIsGenerating(false);
      toast({
        title: "Generation failed",
        description: "Failed to generate course structure. A basic template has been created for you.",
        variant: "destructive",
      });
      
      // Create a fallback structure even on error
      const fallbackSections = [
        {
          id: uuidv4(),
          title: "Module 1",
          lessons: [
            {
              id: uuidv4(),
              title: "Lesson 1",
              type: "video",
              content: "",
              duration: 0
            }
          ]
        }
      ];
      
      setCourseStructure({ sections: fallbackSections });
    }
  });

  const handleSubmit = () => {
    // Validate before submission
    if (courseStructure.sections.length === 0) {
      toast({
        title: "No sections added",
        description: "Please add at least one section to your course",
        variant: "destructive",
      });
      return;
    }

    for (const section of courseStructure.sections) {
      if (section.lessons.length === 0) {
        toast({
          title: "Empty section",
          description: `Section "${section.title}" has no lessons. Please add at least one lesson to each section.`,
          variant: "destructive",
        });
        return;
      }
    }

    onSubmit(courseStructure);
  };

  // Function to create the specified number of modules
  const createModules = (count) => {
    // Create new sections array with the specified count
    const newSections = [];
    
    // Preserve existing modules where possible
    for (let i = 0; i < count; i++) {
      if (i < courseStructure.sections.length) {
        // Keep existing module
        newSections.push(courseStructure.sections[i]);
      } else {
        // Create new module with a default lesson
        newSections.push({
          id: uuidv4(),
          title: `Module ${i + 1}`,
          lessons: [
            {
              id: uuidv4(),
              title: `Lesson 1`,
              type: 'video'
            }
          ]
        });
      }
    }
    
    setCourseStructure({
      sections: newSections
    });
    
    toast({
      title: "Module count updated",
      description: `Your course now has ${count} modules.`
    });
  };
  
  // Handle module count change
  const handleModuleCountChange = (newCount) => {
    setModuleCount(newCount);
    createModules(newCount);
    setIsModuleCountDialogOpen(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Course Structure</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Your course has {courseStructure.sections.length} module{courseStructure.sections.length !== 1 ? 's' : ''}
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => setIsModuleCountDialogOpen(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Set Module Count
          </Button>
          
          {/* Module Complexity Balancer Button */}
          {useAI && courseStructure.sections.length >= 2 && (
            <Button 
              variant="outline" 
              onClick={() => balanceModuleComplexityMutation.mutate()} 
              disabled={isBalancingComplexity}
              className="gap-2"
              title="Optimize learning progression by balancing the complexity across modules"
            >
              {isBalancingComplexity ? (
                <>
                  <RotateCw className="h-4 w-4 animate-spin" />
                  Balancing...
                </>
              ) : (
                <>
                  <BrainCircuit className="h-4 w-4 text-primary" />
                  Balance Complexity
                </>
              )}
            </Button>
          )}
          
          {useAI && (
            <Button 
              variant="outline" 
              onClick={openAiGenerationDialog} 
              disabled={isGenerating}
              className="gap-2"
            >
              {isGenerating ? (
                <>
                  <RotateCw className="h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 text-primary" />
                  Generate with AI
                </>
              )}
            </Button>
          )}
        </div>
      </div>
      
      {/* Module Count Dialog */}
      <Dialog open={isModuleCountDialogOpen} onOpenChange={setIsModuleCountDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Set Module Count</DialogTitle>
            <DialogDescription>
              Choose how many modules your course will have. Each module can contain multiple lessons.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <Label htmlFor="moduleCount">Number of Modules</Label>
            <div className="flex items-center space-x-4 mt-2">
              <Input
                id="moduleCount"
                type="number"
                min={1}
                max={15}
                value={moduleCount}
                onChange={(e) => setModuleCount(Math.max(1, Math.min(15, parseInt(e.target.value) || 1)))}
                className="w-24"
              />
              <span className="text-sm text-muted-foreground">
                (Min: 1, Max: 15)
              </span>
            </div>
            
            <div className="mt-4">
              <div className="text-sm text-muted-foreground mb-2">Preview:</div>
              <div className="bg-slate-50 p-3 rounded-md border max-h-72 overflow-y-auto">
                {Array.from({ length: moduleCount }, (_, i) => (
                  <div key={i} className="py-1.5 pl-2 text-sm">
                    Module {i + 1}{i < courseStructure.sections.length ? `: ${courseStructure.sections[i]?.title || ''}` : ''}
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsModuleCountDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => handleModuleCountChange(moduleCount)}>
              Apply Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="sections" type="SECTION">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-4"
            >
              {courseStructure.sections.length === 0 ? (
                <Card className="border-dashed">
                  <CardContent className="flex flex-col items-center justify-center py-10 text-center text-muted-foreground">
                    <Book className="h-10 w-10 mb-3 text-primary/70" />
                    <p className="text-lg font-medium">No sections yet</p>
                    <p className="text-sm max-w-md">
                      Add your first section or generate a structure with AI to get started
                    </p>
                  </CardContent>
                </Card>
              ) : (
                courseStructure.sections.map((section, index) => (
                  <Draggable
                    key={section.id}
                    draggableId={section.id}
                    index={index}
                  >
                    {(provided) => (
                      <Card
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className="border"
                      >
                        <CardHeader className="p-4 pb-0">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div
                                {...provided.dragHandleProps}
                                className="cursor-move p-1 rounded-sm hover:bg-muted"
                              >
                                <GripVertical className="h-5 w-5 text-muted-foreground" />
                              </div>

                              {editingSectionId === section.id ? (
                                <div className="flex gap-2 flex-grow">
                                  <Input
                                    defaultValue={section.title}
                                    className="h-9 max-w-sm"
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        updateSectionTitle(section.id, e.currentTarget.value);
                                      }
                                    }}
                                    onBlur={(e) => updateSectionTitle(section.id, e.target.value)}
                                    autoFocus
                                  />
                                </div>
                              ) : (
                                <CardTitle className="text-lg">{section.title}</CardTitle>
                              )}
                            </div>

                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => editSection(section.id)}
                                className="h-8 w-8"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => deleteSection(section.id)}
                                className="h-8 w-8 text-destructive"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </CardHeader>

                        <CardContent className="p-4">
                          <Droppable
                            droppableId={`section-${index}`}
                            type="LESSON"
                          >
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                                className="space-y-2 min-h-[50px]"
                              >
                                {section.lessons.length === 0 ? (
                                  <div className="text-center text-muted-foreground py-4 border border-dashed rounded-md">
                                    <p className="text-sm">No lessons yet</p>
                                  </div>
                                ) : (
                                  section.lessons.map((lesson, lessonIndex) => (
                                    <Draggable
                                      key={lesson.id}
                                      draggableId={lesson.id}
                                      index={lessonIndex}
                                    >
                                      {(provided) => (
                                        <div
                                          ref={provided.innerRef}
                                          {...provided.draggableProps}
                                          {...provided.dragHandleProps}
                                          className={cn(
                                            "flex items-center justify-between p-2 rounded-md border bg-background",
                                            lesson.type === 'video' && "border-l-4 border-l-purple-500",
                                            lesson.type === 'text' && "border-l-4 border-l-blue-500",
                                            lesson.type === 'quiz' && "border-l-4 border-l-orange-500"
                                          )}
                                        >
                                          <div className="flex items-center gap-3">
                                            <div className="flex-shrink-0">
                                              {lesson.type === 'video' && <Film className="h-4 w-4 text-purple-500" />}
                                              {lesson.type === 'text' && <FileText className="h-4 w-4 text-blue-500" />}
                                              {lesson.type === 'quiz' && <BrainCircuit className="h-4 w-4 text-orange-500" />}
                                            </div>
                                            <span className="font-medium">{lesson.title}</span>
                                          </div>
                                          <div className="flex gap-1">
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              onClick={() => openEditLessonDialog(section.id, lesson)}
                                              className="h-7 w-7"
                                            >
                                              <Edit className="h-3.5 w-3.5" />
                                            </Button>
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              onClick={() => deleteLesson(section.id, lesson.id)}
                                              className="h-7 w-7 text-destructive"
                                            >
                                              <Trash2 className="h-3.5 w-3.5" />
                                            </Button>
                                          </div>
                                        </div>
                                      )}
                                    </Draggable>
                                  ))
                                )}
                                {provided.placeholder}

                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => openAddLessonDialog(section.id)}
                                  className="w-full mt-2 border border-dashed text-muted-foreground hover:text-foreground"
                                >
                                  <Plus className="h-4 w-4 mr-2" />
                                  Add Lesson
                                </Button>
                              </div>
                            )}
                          </Droppable>
                        </CardContent>
                      </Card>
                    )}
                  </Draggable>
                ))
              )}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Add section input */}
      <div className="mt-6 border-t pt-6">
        <Label htmlFor="newSectionTitle">Add New Section</Label>
        <div className="flex gap-2 mt-2">
          <Input
            id="newSectionTitle"
            placeholder="Enter section title"
            value={newSectionTitle}
            onChange={(e) => setNewSectionTitle(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                addSection();
              }
            }}
          />
          <Button type="button" onClick={addSection}>
            <Plus className="h-4 w-4 mr-2" />
            Add
          </Button>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="mt-8 border-t pt-6 flex justify-between">
        {onBack ? (
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        ) : (
          <div>{/* Empty div to maintain flex layout */}</div>
        )}
        <Button 
          type="button" 
          onClick={handleSubmit}
          className="gap-2"
        >
          Continue
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Add lesson dialog */}
      <Dialog open={isAddLessonDialogOpen} onOpenChange={setIsAddLessonDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Lesson</DialogTitle>
            <DialogDescription>
              Create a new lesson for your course
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-2">
            <div>
              <Label htmlFor="lesson-title">Lesson Title</Label>
              <Input
                id="lesson-title"
                placeholder="Enter title"
                value={newLesson.title}
                onChange={(e) => setNewLesson({...newLesson, title: e.target.value})}
              />
            </div>
            
            <div>
              <Label htmlFor="lesson-type">Lesson Type</Label>
              <Select 
                defaultValue={newLesson.type}
                onValueChange={(value) => setNewLesson({...newLesson, type: value})}
              >
                <SelectTrigger id="lesson-type">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="video">
                    <div className="flex items-center">
                      <Film className="mr-2 h-4 w-4 text-purple-500" />
                      <span>Video</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="text">
                    <div className="flex items-center">
                      <FileText className="mr-2 h-4 w-4 text-blue-500" />
                      <span>Text</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="quiz">
                    <div className="flex items-center">
                      <BrainCircuit className="mr-2 h-4 w-4 text-orange-500" />
                      <span>Quiz</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddLessonDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={addLesson}>Add Lesson</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit lesson dialog */}
      <Dialog open={isEditLessonDialogOpen} onOpenChange={setIsEditLessonDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Lesson</DialogTitle>
            <DialogDescription>
              Update lesson details
            </DialogDescription>
          </DialogHeader>
          {lessonToEdit && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-title">Title</Label>
                <Input
                  id="edit-title"
                  value={lessonToEdit.title}
                  onChange={(e) => setLessonToEdit({...lessonToEdit, title: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-type">Type</Label>
                <Select
                  value={lessonToEdit.type}
                  onValueChange={(value) => setLessonToEdit({...lessonToEdit, type: value})}
                >
                  <SelectTrigger id="edit-type">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="video">
                      <div className="flex items-center">
                        <Film className="mr-2 h-4 w-4 text-purple-500" />
                        <span>Video</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="text">
                      <div className="flex items-center">
                        <FileText className="mr-2 h-4 w-4 text-blue-500" />
                        <span>Text</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="quiz">
                      <div className="flex items-center">
                        <BrainCircuit className="mr-2 h-4 w-4 text-orange-500" />
                        <span>Quiz</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditLessonDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={updateLesson}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* AI Module Count Dialog */}
      <Dialog open={isAiModuleCountDialogOpen} onOpenChange={setIsAiModuleCountDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Generate Course Modules with AI</DialogTitle>
            <DialogDescription>
              Choose how many modules to generate for your course. Each module will include relevant lessons.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <Label htmlFor="aiModuleCount">Number of Modules to Generate</Label>
            <div className="flex items-center space-x-4 mt-2">
              <Input
                id="aiModuleCount"
                type="number"
                min={1}
                max={10}
                value={aiModuleCount}
                onChange={(e) => setAiModuleCount(Math.max(1, Math.min(10, parseInt(e.target.value) || 1)))}
                className="w-24"
              />
              <span className="text-sm text-muted-foreground">
                (Min: 1, Max: 10)
              </span>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAiModuleCountDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={() => handleAiGeneration(aiModuleCount)}
              className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white hover:from-indigo-600 hover:to-purple-700"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Generate {aiModuleCount} {aiModuleCount === 1 ? 'Module' : 'Modules'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}