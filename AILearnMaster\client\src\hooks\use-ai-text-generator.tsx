import { useState } from 'react';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

type FieldType = 
  | 'headline'
  | 'subheadline'
  | 'description'
  | 'emailSubject'
  | 'emailContent'
  | 'benefitPoint'
  | 'testimonial'
  | 'callToAction'
  | 'seoDescription'
  | 'paragraph';

type Tone = 'professional' | 'friendly' | 'casual' | 'urgent' | 'persuasive';
type Length = 'short' | 'medium' | 'long';

interface GenerateTextParams {
  fieldType: FieldType;
  courseId?: number;
  context?: string;
  tone?: Tone;
  length?: Length;
}

export function useAiTextGenerator() {
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const generateText = async (params: GenerateTextParams): Promise<string> => {
    setIsGenerating(true);
    
    try {
      const response = await apiRequest('POST', '/api/marketing/generate-text', params);
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to generate text');
      }
      
      const data = await response.json();
      return data.text;
    } catch (error: any) {
      toast({
        title: 'Text Generation Failed',
        description: error.message || 'Failed to generate text. Please try again.',
        variant: 'destructive',
      });
      return '';
    } finally {
      setIsGenerating(false);
    }
  };

  return {
    generateText,
    isGenerating
  };
}
