import { useState } from "react";
import { <PERSON> } from "wouter";
import { BrainCircuit, Zap, LayoutDashboard, Sparkles, Menu, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import NotificationBell from "@/components/notifications/NotificationBell";
import { Button } from "@/components/ui/button";
import { SubscriptionBadge } from "@/components/header/SubscriptionBadge";
import { ProfileDropdown } from "@/components/header/ProfileDropdown";
import { useUser } from "@/hooks/use-user";
import { HintToggle } from "@/components/hints/HintToggle";
import { SimpleAccessibilityMenu } from "@/components/accessibility/SimpleAccessibilityMenu";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface HeaderProps {
  onToggleSmartSidebar?: () => void;
  showSmartSidebar?: boolean;
}

export function Header({ onToggleSmartSidebar, showSmartSidebar = true }: HeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user } = useUser();

  return (
    <>
      <motion.header 
        className="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 px-4 sm:px-6 py-4 sticky top-0 z-30 shadow-sm"
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="flex items-center justify-between">
          {/* Left Section */}
          <div className="flex items-center space-x-6">
            

            {/* Mobile menu toggle */}
            <div className="block md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="rounded-full hover:bg-blue-50 transition-all duration-200"
              >
                {mobileMenuOpen ? (
                  <X className="h-5 w-5 text-gray-700" />
                ) : (
                  <Menu className="h-5 w-5 text-gray-700" />
                )}
              </Button>
            </div>

            
            
            {/* Navigation Pills for desktop */}
            <div className="hidden md:flex space-x-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link href="/dashboard">
                      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="rounded-full bg-white/60 hover:bg-white hover:shadow-md transition-all duration-300 border border-gray-200/50"
                        >
                          <LayoutDashboard className="h-4 w-4 mr-2 text-gray-600" />
                          <span className="text-sm font-medium text-gray-700">Dashboard</span>
                        </Button>
                      </motion.div>
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent>Go to Dashboard</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              

            </div>
          </div>

          {/* Right side content */}
          <div className="flex items-center space-x-3">
            {/* Subscription Status Badge */}
            {user && (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                <SubscriptionBadge user={user} />
              </motion.div>
            )}
            
            {/* Utility Actions */}
            <div className="hidden md:flex items-center space-x-2">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <HintToggle showLabel={false} />
              </motion.div>
              
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <SimpleAccessibilityMenu />
              </motion.div>
            </div>
            
            {/* Notifications */}
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <NotificationBell className="relative text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-all duration-300 p-2" />
            </motion.div>
            
            {/* Profile Dropdown Menu */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <ProfileDropdown />
            </motion.div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm md:hidden"
            onClick={() => setMobileMenuOpen(false)}
          >
            <motion.div
              initial={{ x: -300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="absolute left-0 top-0 h-full w-80 bg-white/95 backdrop-blur-xl shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 space-y-6">
                {/* Mobile Navigation */}
                <div className="space-y-3">
                  <Link href="/dashboard">
                    <Button 
                      variant="ghost" 
                      className="w-full justify-start rounded-xl p-4 h-auto hover:bg-blue-50"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <LayoutDashboard className="h-5 w-5 mr-3 text-gray-600" />
                      <span className="text-base font-medium text-gray-700">Dashboard</span>
                    </Button>
                  </Link>
                  
                  <Link href="/course-creation/avatar-course">
                    <Button 
                      variant="ghost" 
                      className="w-full justify-start rounded-xl p-4 h-auto bg-gradient-to-r from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <Zap className="h-5 w-5 mr-3 text-blue-600" />
                      <span className="text-base font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        Create Course
                      </span>
                    </Button>
                  </Link>
                </div>

                {/* Mobile Utilities */}
                <div className="pt-4 border-t border-gray-200/50 space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-600">Settings</span>
                    <div className="flex items-center space-x-2">
                      <HintToggle showLabel={false} />
                      <SimpleAccessibilityMenu />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}