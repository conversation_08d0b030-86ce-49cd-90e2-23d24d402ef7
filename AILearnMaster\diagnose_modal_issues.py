#!/usr/bin/env python3
"""
Modal A100 Issues Diagnostic Script
Diagnoses issues with the current Modal deployment without requiring authentication
"""

import requests
import json
import time
import base64
import io
from PIL import Image, ImageDraw

# Current Modal URL (update if different)
MODAL_URL = "https://trade-digital--courseai-a100-simple"

class ModalDiagnostic:
    def __init__(self, base_url=MODAL_URL):
        self.base_url = base_url
        self.issues = []
        self.working_services = []
        
    def log_issue(self, service, issue, severity="ERROR"):
        """Log an issue found during diagnosis"""
        self.issues.append({
            "service": service,
            "issue": issue,
            "severity": severity,
            "timestamp": time.time()
        })
        
    def log_success(self, service, message):
        """Log a successful test"""
        self.working_services.append({
            "service": service,
            "message": message,
            "timestamp": time.time()
        })
    
    def test_connectivity(self):
        """Test basic connectivity to Modal app"""
        print("🔍 Testing Modal App Connectivity...")
        
        try:
            response = requests.get(f"{self.base_url}-health.modal.run", timeout=10)
            if response.status_code == 200:
                print("✅ Modal app is reachable")
                self.log_success("connectivity", "App is reachable")
                return True
            else:
                print(f"⚠️  Modal app returned HTTP {response.status_code}")
                self.log_issue("connectivity", f"HTTP {response.status_code}", "WARNING")
                return True  # Still reachable, just different response
        except requests.exceptions.Timeout:
            print("❌ Modal app connection timed out")
            self.log_issue("connectivity", "Connection timeout")
            return False
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to Modal app")
            self.log_issue("connectivity", "Connection failed")
            return False
        except Exception as e:
            print(f"❌ Connectivity error: {e}")
            self.log_issue("connectivity", str(e))
            return False
    
    def test_health_endpoint(self):
        """Test the health check endpoint"""
        print("\n🔍 Testing Health Check Endpoint...")
        
        try:
            response = requests.get(f"{self.base_url}-health.modal.run", timeout=30)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print("✅ Health endpoint is working")
                    
                    # Analyze health data
                    gpu_available = data.get('gpu_available', False)
                    gpu_name = data.get('gpu_name', 'Unknown')
                    status = data.get('status', 'unknown')
                    
                    print(f"   Status: {status}")
                    print(f"   GPU Available: {gpu_available}")
                    print(f"   GPU Name: {gpu_name}")
                    
                    if gpu_available:
                        memory_total = data.get('gpu_memory_total_gb', 0)
                        memory_free = data.get('gpu_memory_free_gb', 0)
                        print(f"   GPU Memory: {memory_free:.1f}GB free / {memory_total:.1f}GB total")
                        
                        if memory_free < 10:
                            self.log_issue("gpu", f"Low GPU memory: {memory_free:.1f}GB free", "WARNING")
                    else:
                        self.log_issue("gpu", "GPU not available")
                    
                    # Check services
                    services = data.get('services', {})
                    print(f"   Services Status:")
                    
                    for service, service_status in services.items():
                        if service_status is True:
                            print(f"     ✅ {service}")
                            self.log_success(service, "Service available")
                        else:
                            print(f"     ❌ {service}: {service_status}")
                            self.log_issue(service, f"Service unavailable: {service_status}")
                    
                    self.log_success("health", "Health endpoint working")
                    return True
                    
                except json.JSONDecodeError:
                    print("❌ Health endpoint returned invalid JSON")
                    self.log_issue("health", "Invalid JSON response")
                    return False
            else:
                print(f"❌ Health endpoint failed: HTTP {response.status_code}")
                print(f"   Response: {response.text[:200]}")
                self.log_issue("health", f"HTTP {response.status_code}: {response.text[:100]}")
                return False
                
        except Exception as e:
            print(f"❌ Health endpoint error: {e}")
            self.log_issue("health", str(e))
            return False
    
    def test_mistral_endpoint(self):
        """Test Mistral LLM endpoint"""
        print("\n🔍 Testing Mistral LLM Endpoint...")
        
        try:
            payload = {
                "prompt": "Hello, this is a test.",
                "max_tokens": 50,
                "temperature": 0.7
            }
            
            response = requests.post(f"{self.base_url}-api-mistral.modal.run", json=payload, timeout=60)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('status') == 'success':
                        print("✅ Mistral endpoint is working")
                        self.log_success("mistral", "Endpoint working")
                        return True
                    else:
                        error = data.get('error', 'Unknown error')
                        print(f"❌ Mistral endpoint error: {error}")
                        self.log_issue("mistral", error)
                        return False
                except json.JSONDecodeError:
                    print("❌ Mistral endpoint returned invalid JSON")
                    self.log_issue("mistral", "Invalid JSON response")
                    return False
            else:
                print(f"❌ Mistral endpoint failed: HTTP {response.status_code}")
                self.log_issue("mistral", f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Mistral endpoint error: {e}")
            self.log_issue("mistral", str(e))
            return False
    
    def test_tts_endpoint(self):
        """Test TTS endpoint"""
        print("\n🔍 Testing TTS Endpoint...")
        
        try:
            payload = {
                "text": "Hello, this is a test.",
                "voice_preset": "tts_models/en/ljspeech/tacotron2-DDC"
            }
            
            response = requests.post(f"{self.base_url}-api-tts.modal.run", json=payload, timeout=60)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('status') == 'success':
                        print("✅ TTS endpoint is working")
                        self.log_success("tts", "Endpoint working")
                        return True
                    else:
                        error = data.get('error', 'Unknown error')
                        print(f"❌ TTS endpoint error: {error}")
                        self.log_issue("tts", error)
                        return False
                except json.JSONDecodeError:
                    print("❌ TTS endpoint returned invalid JSON")
                    self.log_issue("tts", "Invalid JSON response")
                    return False
            else:
                print(f"❌ TTS endpoint failed: HTTP {response.status_code}")
                self.log_issue("tts", f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ TTS endpoint error: {e}")
            self.log_issue("tts", str(e))
            return False
    
    def test_all_endpoints(self):
        """Test all available endpoints"""
        endpoints = [
            ("api-mistral", "Mistral LLM", {"prompt": "Test", "max_tokens": 10}),
            ("api-tts", "Coqui TTS", {"text": "Test"}),
            ("api-voices", "Voice Discovery", {}),
            ("api-slides", "Marp Slides", {"markdown": "# Test"}),
            ("api-avatar", "EchoMimic Avatar", {"ref_image_base64": "dGVzdA==", "audio_base64": "dGVzdA=="}),
            ("api-course-avatar", "Course Avatar", {"prompt": "Test", "ref_image_base64": "dGVzdA=="})
        ]
        
        print("\n🔍 Testing All Service Endpoints...")
        
        working_count = 0
        total_count = len(endpoints)
        
        for endpoint, name, payload in endpoints:
            try:
                # Use GET for voices endpoint, POST for others
                if endpoint == "api-voices":
                    response = requests.get(f"{self.base_url}-{endpoint}.modal.run", timeout=30)
                else:
                    response = requests.post(f"{self.base_url}-{endpoint}.modal.run", json=payload, timeout=30)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if data.get('status') == 'success':
                            print(f"✅ {name}: Working")
                            self.log_success(endpoint, "Endpoint working")
                            working_count += 1
                        else:
                            error = data.get('error', 'Unknown error')
                            print(f"❌ {name}: {error}")
                            self.log_issue(endpoint, error)
                    except json.JSONDecodeError:
                        print(f"❌ {name}: Invalid JSON response")
                        self.log_issue(endpoint, "Invalid JSON response")
                else:
                    print(f"❌ {name}: HTTP {response.status_code}")
                    self.log_issue(endpoint, f"HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {name}: {e}")
                self.log_issue(endpoint, str(e))
        
        print(f"\nEndpoint Summary: {working_count}/{total_count} working")
        return working_count, total_count
    
    def analyze_issues(self):
        """Analyze found issues and provide recommendations"""
        print("\n🔍 Analyzing Issues...")
        
        if not self.issues:
            print("✅ No issues found!")
            return
        
        # Group issues by service
        service_issues = {}
        for issue in self.issues:
            service = issue['service']
            if service not in service_issues:
                service_issues[service] = []
            service_issues[service].append(issue)
        
        print(f"\nFound {len(self.issues)} issues across {len(service_issues)} services:")
        
        for service, issues in service_issues.items():
            print(f"\n❌ {service.upper()}:")
            for issue in issues:
                severity = issue['severity']
                print(f"   {severity}: {issue['issue']}")
    
    def generate_recommendations(self):
        """Generate specific recommendations based on found issues"""
        print("\n💡 RECOMMENDATIONS:")
        print("=" * 50)
        
        # Check for common issues
        connectivity_issues = [i for i in self.issues if i['service'] == 'connectivity']
        gpu_issues = [i for i in self.issues if i['service'] == 'gpu']
        service_issues = [i for i in self.issues if i['service'] not in ['connectivity', 'gpu', 'health']]
        
        if connectivity_issues:
            print("🔧 CONNECTIVITY ISSUES:")
            print("   1. Check if Modal app is deployed and running")
            print("   2. Verify the Modal URL is correct")
            print("   3. Check network connectivity")
            print("   4. Redeploy the Modal app if necessary")
        
        if gpu_issues:
            print("\n🔧 GPU ISSUES:")
            print("   1. Check GPU availability in Modal dashboard")
            print("   2. Verify A100 GPU allocation")
            print("   3. Check GPU memory usage")
            print("   4. Restart the Modal app if GPU is stuck")
        
        if service_issues:
            print("\n🔧 SERVICE ISSUES:")
            print("   1. Check model loading and initialization")
            print("   2. Verify all dependencies are installed")
            print("   3. Check for memory or timeout issues")
            print("   4. Review Modal logs for detailed errors")
        
        if not self.issues:
            print("✅ No issues found - all services appear to be working!")
        
        print("\n🚀 NEXT STEPS:")
        print("   1. Deploy the fixed Modal script: python deploy_modal_a100_fixed.py")
        print("   2. Run comprehensive tests: python test_modal_a100_comprehensive.py")
        print("   3. Test course creation workflows")
        print("   4. Monitor performance and logs")
    
    def run_full_diagnosis(self):
        """Run complete diagnostic process"""
        print("🔍 Modal A100 GPU Comprehensive Diagnosis")
        print("=" * 60)
        
        start_time = time.time()
        
        # Test connectivity
        if not self.test_connectivity():
            print("\n❌ Cannot proceed - Modal app is not reachable")
            self.generate_recommendations()
            return False
        
        # Test health endpoint
        self.test_health_endpoint()
        
        # Test all endpoints
        working, total = self.test_all_endpoints()
        
        end_time = time.time()
        
        # Generate report
        print("\n" + "=" * 60)
        print("📊 DIAGNOSTIC REPORT")
        print("=" * 60)
        
        print(f"Diagnosis Time: {end_time - start_time:.1f} seconds")
        print(f"Working Services: {len(self.working_services)}")
        print(f"Issues Found: {len(self.issues)}")
        print(f"Endpoint Success Rate: {working}/{total} ({(working/total)*100:.1f}%)")
        
        # Analyze issues
        self.analyze_issues()
        
        # Generate recommendations
        self.generate_recommendations()
        
        # Save detailed report
        report = {
            'timestamp': time.time(),
            'diagnosis_time': end_time - start_time,
            'working_services': self.working_services,
            'issues': self.issues,
            'success_rate': working / total if total > 0 else 0
        }
        
        with open('modal_diagnosis_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\nDetailed report saved to: modal_diagnosis_report.json")
        
        return len(self.issues) == 0

if __name__ == "__main__":
    diagnostic = ModalDiagnostic()
    success = diagnostic.run_full_diagnosis()
    
    if success:
        print("\n🎉 All Modal A100 services are working correctly!")
    else:
        print("\n⚠️  Issues found with Modal A100 deployment.")
        print("   Please follow the recommendations above to fix the issues.")
    
    exit(0 if success else 1)
