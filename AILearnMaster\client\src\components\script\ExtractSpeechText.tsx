import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";
import { Mic, X, AlertCircle, HelpCircle, Wand2 } from "lucide-react";

interface ExtractSpeechTextProps {
  rawText: string;
  onExtractedTextChange: (text: string) => void;
}

export function ExtractSpeechText({ rawText, onExtractedTextChange }: ExtractSpeechTextProps) {
  const [extractedText, setExtractedText] = useState<string>(rawText);
  const [activeTab, setActiveTab] = useState<string>("auto");
  const [processingText, setProcessingText] = useState<boolean>(false);
  const [autoSettings, setAutoSettings] = useState({
    removeMarkdown: true,
    removeCodeBlocks: true,
    removeNotes: true,
    removeParentheses: true,
    removeBrackets: true,
    removeUrls: true,
    trimLines: true,
    collapseSpaces: true,
  });

  // Process the text whenever rawText or settings change
  useEffect(() => {
    if (activeTab === "auto" && rawText) {
      processText();
    }
  }, [rawText, autoSettings, activeTab]);

  // Process the text based on active settings
  const processText = () => {
    setProcessingText(true);
    
    let processed = rawText;

    // Process the text based on the selected settings
    if (autoSettings.removeMarkdown) {
      // Remove markdown headings
      processed = processed.replace(/^#{1,6}\s+(.+)$/gm, '$1');
      // Remove markdown bold and italic
      processed = processed.replace(/(\*\*|__)(.*?)\1/g, '$2');
      processed = processed.replace(/(\*|_)(.*?)\1/g, '$2');
      // Remove markdown lists
      processed = processed.replace(/^[\s]*[-*+][\s]+(.+)$/gm, '$1');
      processed = processed.replace(/^[\s]*\d+\.[\s]+(.+)$/gm, '$1');
    }

    if (autoSettings.removeCodeBlocks) {
      // Remove code blocks
      processed = processed.replace(/```[\s\S]*?```/g, '');
      processed = processed.replace(/`([^`]+)`/g, '$1');
    }

    if (autoSettings.removeNotes) {
      // Remove content in notes, comments or metadata sections
      processed = processed.replace(/^Note:.*$/gm, '');
      processed = processed.replace(/^Comment:.*$/gm, '');
      processed = processed.replace(/<!--[\s\S]*?-->/g, '');
      processed = processed.replace(/\[\^.*?\]/g, '');
    }

    if (autoSettings.removeParentheses) {
      // Remove content in parentheses
      processed = processed.replace(/\([^)]*\)/g, ' ');
    }

    if (autoSettings.removeBrackets) {
      // Remove content in brackets
      processed = processed.replace(/\[[^\]]*\]/g, ' ');
    }

    if (autoSettings.removeUrls) {
      // Remove URLs
      processed = processed.replace(/https?:\/\/[^\s]+/g, '');
    }

    if (autoSettings.trimLines) {
      // Trim each line
      processed = processed.split('\n').map(line => line.trim()).join('\n');
    }

    if (autoSettings.collapseSpaces) {
      // Collapse multiple spaces into one
      processed = processed.replace(/\s+/g, ' ');
    }

    // Clean up empty lines
    processed = processed.replace(/\n\s*\n/g, '\n\n');
    
    setExtractedText(processed);
    onExtractedTextChange(processed);
    setProcessingText(false);
  };

  // Handle manual edit of the extracted text
  const handleManualEdit = (value: string) => {
    setExtractedText(value);
    onExtractedTextChange(value);
  };

  // Toggle a specific auto setting
  const toggleSetting = (setting: keyof typeof autoSettings) => {
    setAutoSettings({
      ...autoSettings,
      [setting]: !autoSettings[setting]
    });
  };

  const characterCount = extractedText.length;
  const wordCount = extractedText.trim().split(/\s+/).filter(Boolean).length;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center text-lg font-medium">
          <Mic className="mr-2 h-5 w-5 text-primary" />
          Voice-Ready Text Extraction
        </CardTitle>
        <CardDescription>
          Extract only the spoken content your AI voice needs to narrate.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="auto">Auto-Extract</TabsTrigger>
            <TabsTrigger value="manual">Manual Edit</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>
          
          <TabsContent value="auto" className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="removeMarkdown" 
                  checked={autoSettings.removeMarkdown}
                  onCheckedChange={() => toggleSetting('removeMarkdown')}
                />
                <Label htmlFor="removeMarkdown" className="cursor-pointer">Remove markdown</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="removeCodeBlocks" 
                  checked={autoSettings.removeCodeBlocks}
                  onCheckedChange={() => toggleSetting('removeCodeBlocks')}
                />
                <Label htmlFor="removeCodeBlocks" className="cursor-pointer">Remove code blocks</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="removeNotes" 
                  checked={autoSettings.removeNotes}
                  onCheckedChange={() => toggleSetting('removeNotes')}
                />
                <Label htmlFor="removeNotes" className="cursor-pointer">Remove notes</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="removeParentheses" 
                  checked={autoSettings.removeParentheses}
                  onCheckedChange={() => toggleSetting('removeParentheses')}
                />
                <Label htmlFor="removeParentheses" className="cursor-pointer">Remove parentheses</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="removeBrackets" 
                  checked={autoSettings.removeBrackets}
                  onCheckedChange={() => toggleSetting('removeBrackets')}
                />
                <Label htmlFor="removeBrackets" className="cursor-pointer">Remove brackets</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="removeUrls" 
                  checked={autoSettings.removeUrls}
                  onCheckedChange={() => toggleSetting('removeUrls')}
                />
                <Label htmlFor="removeUrls" className="cursor-pointer">Remove URLs</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="trimLines" 
                  checked={autoSettings.trimLines}
                  onCheckedChange={() => toggleSetting('trimLines')}
                />
                <Label htmlFor="trimLines" className="cursor-pointer">Trim lines</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="collapseSpaces" 
                  checked={autoSettings.collapseSpaces}
                  onCheckedChange={() => toggleSetting('collapseSpaces')}
                />
                <Label htmlFor="collapseSpaces" className="cursor-pointer">Collapse spaces</Label>
              </div>
            </div>
            
            <div className="relative">
              <Textarea
                value={extractedText}
                onChange={(e) => handleManualEdit(e.target.value)}
                className="min-h-[180px] font-mono text-sm"
                placeholder="Processed text will appear here..."
                disabled={processingText}
              />
              {processingText && (
                <div className="absolute inset-0 bg-slate-100/80 flex items-center justify-center">
                  <span className="text-sm text-slate-600">Processing text...</span>
                </div>
              )}
            </div>
            
            <Alert className="bg-slate-50">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-slate-500" />
                <AlertDescription className="text-sm text-slate-600">
                  Auto-extraction keeps only the spoken content by removing formatting, code blocks, and non-speech elements.
                </AlertDescription>
              </div>
            </Alert>
          </TabsContent>
          
          <TabsContent value="manual" className="space-y-4">
            <Label htmlFor="manual-edit">Edit Text Manually</Label>
            <Textarea
              id="manual-edit"
              value={extractedText}
              onChange={(e) => handleManualEdit(e.target.value)}
              className="min-h-[300px] font-mono text-sm"
              placeholder="Edit the extracted text manually..."
            />
            <Alert className="bg-slate-50">
              <div className="flex items-center gap-2">
                <HelpCircle className="h-4 w-4 text-slate-500" />
                <AlertDescription className="text-sm text-slate-600">
                  Remove any text that shouldn't be spoken, like formatting instructions, code examples, or notes to yourself.
                </AlertDescription>
              </div>
            </Alert>
          </TabsContent>
          
          <TabsContent value="preview" className="space-y-4">
            <div className="bg-slate-50 p-4 rounded-md border border-slate-200 min-h-[300px] whitespace-pre-wrap">
              {extractedText || <span className="text-slate-400">No text to preview</span>}
            </div>
            <div className="flex justify-between text-sm text-slate-500">
              <div>Characters: {characterCount}</div>
              <div>Words: {wordCount}</div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className="text-slate-500">
                {wordCount} words / {characterCount} chars
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">AI voice synthesis typically costs 1 credit per 100 words</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => {
              setExtractedText(rawText);
              onExtractedTextChange(rawText);
            }}
          >
            <X className="mr-2 h-4 w-4" />
            Reset
          </Button>
          <Button 
            size="sm"
            onClick={processText}
          >
            <Wand2 className="mr-2 h-4 w-4" />
            Extract Speech
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}