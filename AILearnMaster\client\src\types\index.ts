export interface User {
  id: number;
  username: string;
  email: string;
  name: string;
  plan?: string | null;
  role?: string | null;
  avatarUrl?: string | null;
  emailVerified?: boolean | null;
  createdAt?: Date | null;
  stripeCustomerId?: string | null;
  stripeSubscriptionId?: string | null;
}

export interface Course {
  id: number;
  userId: number;
  title: string;
  description: string;
  targetAudience?: string;
  category: string;
  status: string;
  thumbnailUrl?: string;
  completion: number;
  lessonsCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Lesson {
  id: number;
  courseId: number;
  title: string;
  description?: string;
  script?: string;
  videoUrl?: string;
  voiceoverId?: string;
  duration: number;
  order: number;
  status: string;
}

export interface Media {
  id: number;
  userId: number;
  name: string;
  type: string;
  url: string;
  createdAt: Date;
}

export interface Template {
  id: number;
  name: string;
  description: string;
  icon: string;
  type: string;
}

export interface Integration {
  id: number;
  userId: number;
  platform: string;
  isConnected: boolean;
  credentials: Record<string, any>;
}

export interface UserStats {
  id: number;
  userId: number;
  activeCourses: number;
  publishedCourses: number;
  aiCredits: number;
  storageUsed: number;
  storageLimit: number;
}

export interface BillingHistory {
  id: number;
  userId: number;
  amount: number;
  currency: string;
  description: string;
  status: string;
  transactionDate: Date;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    marketing: boolean;
  }
}

export interface LoginActivity {
  id: number;
  userId: number;
  ipAddress: string;
  location: string;
  device: string;
  timestamp: Date;
}

export interface PaymentMethod {
  id: number;
  userId: number;
  type: string;
  lastFour: string;
  expiryDate: string;
  isDefault: boolean;
}
