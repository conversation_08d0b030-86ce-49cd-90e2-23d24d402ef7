#!/usr/bin/env tsx
/**
 * Security Configuration Validation Script
 * Validates environment variables and security settings for production deployment
 */

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

interface ValidationResult {
  category: string;
  check: string;
  status: 'pass' | 'fail' | 'warn';
  message: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
}

class SecurityConfigValidator {
  private results: ValidationResult[] = [];

  /**
   * Run all security configuration validations
   */
  async validateSecurityConfig(): Promise<void> {
    console.log('🔒 Starting Security Configuration Validation\n');
    console.log('=' .repeat(70));

    // Validate environment variables
    this.validateEnvironmentVariables();

    // Validate database configuration
    this.validateDatabaseConfig();

    // Validate secrets management
    this.validateSecretsManagement();

    // Validate security middleware
    this.validateSecurityMiddleware();

    // Validate production settings
    this.validateProductionSettings();

    // Validate file permissions
    this.validateFilePermissions();

    // Generate validation report
    this.generateValidationReport();
  }

  /**
   * Validate required environment variables
   */
  private validateEnvironmentVariables(): void {
    const requiredVars = [
      { name: 'NODE_ENV', severity: 'critical' as const },
      { name: 'DATABASE_URL', severity: 'critical' as const },
      { name: 'SESSION_SECRET', severity: 'critical' as const },
      { name: 'MODAL_TOKEN_ID', severity: 'high' as const },
      { name: 'MODAL_TOKEN_SECRET', severity: 'high' as const },
      { name: 'OPENAI_API_KEY', severity: 'medium' as const },
      { name: 'AWS_ACCESS_KEY_ID', severity: 'medium' as const },
      { name: 'AWS_SECRET_ACCESS_KEY', severity: 'medium' as const }
    ];

    requiredVars.forEach(({ name, severity }) => {
      const value = process.env[name];
      
      if (!value) {
        this.addResult('Environment Variables', `${name} configured`, 'fail', 
          `${name} is not set`, severity);
      } else {
        this.addResult('Environment Variables', `${name} configured`, 'pass', 
          `${name} is properly configured`, severity);
      }
    });

    // Validate SESSION_SECRET strength
    const sessionSecret = process.env.SESSION_SECRET;
    if (sessionSecret) {
      if (sessionSecret.length < 32) {
        this.addResult('Environment Variables', 'SESSION_SECRET strength', 'fail',
          'SESSION_SECRET must be at least 32 characters long', 'critical');
      } else {
        this.addResult('Environment Variables', 'SESSION_SECRET strength', 'pass',
          'SESSION_SECRET meets minimum length requirement', 'critical');
      }
    }

    // Validate ALLOWED_ORIGINS for production
    if (process.env.NODE_ENV === 'production') {
      const allowedOrigins = process.env.ALLOWED_ORIGINS;
      if (!allowedOrigins || allowedOrigins.includes('*')) {
        this.addResult('Environment Variables', 'CORS origins', 'fail',
          'ALLOWED_ORIGINS must be set and not contain wildcards in production', 'high');
      } else {
        this.addResult('Environment Variables', 'CORS origins', 'pass',
          'ALLOWED_ORIGINS properly configured for production', 'high');
      }
    }
  }

  /**
   * Validate database configuration
   */
  private validateDatabaseConfig(): void {
    const databaseUrl = process.env.DATABASE_URL;
    
    if (databaseUrl) {
      // Check SSL enforcement
      if (process.env.NODE_ENV === 'production' && !databaseUrl.includes('sslmode=require')) {
        this.addResult('Database', 'SSL enforcement', 'fail',
          'Database SSL is not enforced in production', 'high');
      } else {
        this.addResult('Database', 'SSL enforcement', 'pass',
          'Database SSL properly configured', 'high');
      }

      // Check for credentials in URL
      if (databaseUrl.includes('password=') || databaseUrl.includes('user=')) {
        this.addResult('Database', 'Credential exposure', 'warn',
          'Database credentials may be exposed in connection string', 'medium');
      } else {
        this.addResult('Database', 'Credential exposure', 'pass',
          'Database credentials properly secured', 'medium');
      }
    }
  }

  /**
   * Validate secrets management configuration
   */
  private validateSecretsManagement(): void {
    const encryptionKey = process.env.SECRETS_ENCRYPTION_KEY;
    
    if (process.env.NODE_ENV === 'production') {
      if (!encryptionKey) {
        this.addResult('Secrets Management', 'Encryption key', 'fail',
          'SECRETS_ENCRYPTION_KEY not configured for production', 'critical');
      } else if (encryptionKey.length < 32) {
        this.addResult('Secrets Management', 'Encryption key strength', 'fail',
          'SECRETS_ENCRYPTION_KEY must be at least 32 characters', 'critical');
      } else {
        this.addResult('Secrets Management', 'Encryption key', 'pass',
          'Secrets encryption properly configured', 'critical');
      }
    }

    // Check for .secrets directory
    const secretsPath = path.join(process.cwd(), '.secrets');
    if (fs.existsSync(secretsPath)) {
      this.addResult('Secrets Management', 'Secrets directory', 'pass',
        'Secrets directory exists', 'medium');
      
      // Check directory permissions
      try {
        const stats = fs.statSync(secretsPath);
        const mode = stats.mode & parseInt('777', 8);
        if (mode > parseInt('700', 8)) {
          this.addResult('Secrets Management', 'Directory permissions', 'warn',
            'Secrets directory permissions may be too permissive', 'medium');
        } else {
          this.addResult('Secrets Management', 'Directory permissions', 'pass',
            'Secrets directory permissions are secure', 'medium');
        }
      } catch (error) {
        this.addResult('Secrets Management', 'Directory permissions', 'warn',
          'Could not check secrets directory permissions', 'low');
      }
    }
  }

  /**
   * Validate security middleware configuration
   */
  private validateSecurityMiddleware(): void {
    // Check if security middleware files exist
    const securityFiles = [
      'server/security-fixes/enhanced-security-middleware.ts',
      'server/security-fixes/secure-secrets-manager.ts',
      'server/security-fixes/secure-database-config.ts'
    ];

    securityFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.addResult('Security Middleware', `${path.basename(file)} exists`, 'pass',
          `Security file ${file} is present`, 'high');
      } else {
        this.addResult('Security Middleware', `${path.basename(file)} exists`, 'fail',
          `Security file ${file} is missing`, 'high');
      }
    });

    // Check for security configuration in package.json
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        const hasSecurityScripts = packageJson.scripts && (
          packageJson.scripts['security:audit'] ||
          packageJson.scripts['security:validate']
        );
        
        if (hasSecurityScripts) {
          this.addResult('Security Middleware', 'Security scripts', 'pass',
            'Security validation scripts are configured', 'medium');
        } else {
          this.addResult('Security Middleware', 'Security scripts', 'warn',
            'Security validation scripts not found in package.json', 'medium');
        }
      } catch (error) {
        this.addResult('Security Middleware', 'Package.json validation', 'warn',
          'Could not validate package.json configuration', 'low');
      }
    }
  }

  /**
   * Validate production-specific settings
   */
  private validateProductionSettings(): void {
    const isProduction = process.env.NODE_ENV === 'production';
    
    if (isProduction) {
      // Check debug settings
      if (process.env.DEBUG === 'true') {
        this.addResult('Production Settings', 'Debug mode', 'fail',
          'Debug mode is enabled in production', 'high');
      } else {
        this.addResult('Production Settings', 'Debug mode', 'pass',
          'Debug mode is disabled in production', 'high');
      }

      // Check logging level
      const logLevel = process.env.LOG_LEVEL;
      if (logLevel === 'debug' || logLevel === 'trace') {
        this.addResult('Production Settings', 'Log level', 'warn',
          'Verbose logging enabled in production', 'medium');
      } else {
        this.addResult('Production Settings', 'Log level', 'pass',
          'Appropriate log level for production', 'medium');
      }

      // Check for development dependencies
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      if (fs.existsSync(packageJsonPath)) {
        try {
          const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
          if (packageJson.devDependencies && Object.keys(packageJson.devDependencies).length > 0) {
            this.addResult('Production Settings', 'Dev dependencies', 'warn',
              'Development dependencies present (ensure they are not installed in production)', 'low');
          }
        } catch (error) {
          // Ignore package.json parsing errors
        }
      }
    }
  }

  /**
   * Validate file permissions and security
   */
  private validateFilePermissions(): void {
    const sensitiveFiles = [
      '.env',
      '.env.local',
      '.env.production',
      'server/security-fixes/secure-secrets-manager.ts'
    ];

    sensitiveFiles.forEach(file => {
      if (fs.existsSync(file)) {
        try {
          const stats = fs.statSync(file);
          const mode = stats.mode & parseInt('777', 8);
          
          if (mode > parseInt('644', 8)) {
            this.addResult('File Permissions', `${file} permissions`, 'warn',
              `File ${file} may have overly permissive permissions`, 'medium');
          } else {
            this.addResult('File Permissions', `${file} permissions`, 'pass',
              `File ${file} has appropriate permissions`, 'medium');
          }
        } catch (error) {
          this.addResult('File Permissions', `${file} permissions`, 'warn',
            `Could not check permissions for ${file}`, 'low');
        }
      }
    });

    // Check for .git directory permissions
    const gitDir = path.join(process.cwd(), '.git');
    if (fs.existsSync(gitDir)) {
      try {
        const stats = fs.statSync(gitDir);
        const mode = stats.mode & parseInt('777', 8);
        
        if (mode > parseInt('755', 8)) {
          this.addResult('File Permissions', '.git directory permissions', 'warn',
            '.git directory may have overly permissive permissions', 'low');
        } else {
          this.addResult('File Permissions', '.git directory permissions', 'pass',
            '.git directory has appropriate permissions', 'low');
        }
      } catch (error) {
        // Ignore git directory permission errors
      }
    }
  }

  /**
   * Add validation result
   */
  private addResult(category: string, check: string, status: ValidationResult['status'], 
                   message: string, severity: ValidationResult['severity']): void {
    this.results.push({ category, check, status, message, severity });
  }

  /**
   * Generate comprehensive validation report
   */
  private generateValidationReport(): void {
    console.log('\n' + '='.repeat(70));
    console.log('🔒 SECURITY CONFIGURATION VALIDATION REPORT');
    console.log('='.repeat(70));

    const totalChecks = this.results.length;
    const passed = this.results.filter(r => r.status === 'pass').length;
    const failed = this.results.filter(r => r.status === 'fail').length;
    const warnings = this.results.filter(r => r.status === 'warn').length;

    // Calculate security score
    const criticalFailed = this.results.filter(r => r.status === 'fail' && r.severity === 'critical').length;
    const highFailed = this.results.filter(r => r.status === 'fail' && r.severity === 'high').length;
    const mediumFailed = this.results.filter(r => r.status === 'fail' && r.severity === 'medium').length;
    const lowFailed = this.results.filter(r => r.status === 'fail' && r.severity === 'low').length;

    const securityScore = Math.max(0, 100 - (criticalFailed * 25) - (highFailed * 15) - (mediumFailed * 8) - (lowFailed * 3));

    console.log(`\n📊 VALIDATION SUMMARY:`);
    console.log(`   Total Checks: ${totalChecks}`);
    console.log(`   Passed: ${passed} ✅`);
    console.log(`   Failed: ${failed} ❌`);
    console.log(`   Warnings: ${warnings} ⚠️`);
    console.log(`   Security Score: ${securityScore}/100 ${securityScore >= 85 ? '✅' : securityScore >= 70 ? '⚠️' : '❌'}`);

    // Group results by category
    const categories = [...new Set(this.results.map(r => r.category))];
    
    categories.forEach(category => {
      const categoryResults = this.results.filter(r => r.category === category);
      const categoryPassed = categoryResults.filter(r => r.status === 'pass').length;
      const categoryFailed = categoryResults.filter(r => r.status === 'fail').length;
      const categoryWarnings = categoryResults.filter(r => r.status === 'warn').length;

      console.log(`\n📋 ${category.toUpperCase()}:`);
      console.log(`   Passed: ${categoryPassed} | Failed: ${categoryFailed} | Warnings: ${categoryWarnings}`);

      categoryResults.forEach(result => {
        const icon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⚠️';
        const severity = result.severity.toUpperCase();
        console.log(`   ${icon} [${severity}] ${result.check}: ${result.message}`);
      });
    });

    // Deployment readiness assessment
    console.log(`\n🚀 DEPLOYMENT READINESS:`);
    if (criticalFailed > 0) {
      console.log('   🛑 NOT READY - Critical security issues must be resolved');
    } else if (highFailed > 0) {
      console.log('   ⚠️ CAUTION - High priority issues should be resolved before deployment');
    } else if (securityScore >= 85) {
      console.log('   ✅ READY - Security configuration meets deployment standards');
    } else {
      console.log('   ⚠️ REVIEW - Security configuration needs improvement');
    }

    console.log('\n' + '='.repeat(70));
    
    if (securityScore >= 85 && criticalFailed === 0) {
      console.log('🎉 SECURITY VALIDATION PASSED! Configuration ready for deployment.');
    } else {
      console.log('⚠️ SECURITY VALIDATION INCOMPLETE! Address issues before deployment.');
    }

    // Exit with appropriate code
    if (criticalFailed > 0) {
      process.exit(1);
    } else if (highFailed > 0) {
      process.exit(2);
    } else {
      process.exit(0);
    }
  }
}

// CLI interface
async function main() {
  const validator = new SecurityConfigValidator();
  await validator.validateSecurityConfig();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Security validation failed:', error);
    process.exit(1);
  });
}

export { SecurityConfigValidator };
