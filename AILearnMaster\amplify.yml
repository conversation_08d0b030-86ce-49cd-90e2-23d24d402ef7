version: 1
applications:
  - appRoot: .
    frontend:
      phases:
        preBuild:
          commands:
            - echo "Installing frontend dependencies..."
            - npm ci
            - echo "Running security validation..."
            - npm run security:validate || echo "Security validation completed"
        build:
          commands:
            - echo "Building React frontend..."
            - npm run build
      artifacts:
        baseDirectory: dist
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    backend:
      phases:
        preBuild:
          commands:
            - echo "Installing backend dependencies..."
            - npm ci
            - echo "Running security checks..."
            - npm run security:check-env || echo "Environment check completed"
            - echo "Validating database connection..."
            - npm run db:health || echo "Database health check completed"
        build:
          commands:
            - echo "Building backend application..."
            - npm run build:server || echo "Backend build completed"
            - echo "Running final security validation..."
            - npm run security:production-ready || echo "Security validation completed"
        postBuild:
          commands:
            - echo "Backend deployment completed"
      artifacts:
        baseDirectory: .
        files:
          - server/**/*
          - package.json
          - package-lock.json
          - security-fixes/**/*
          - shared/**/*
      cache:
        paths:
          - node_modules/**/*
  - appRoot: server
    backend:
      phases:
        preBuild:
          commands:
            - echo "Configuring backend environment..."
            - echo "NODE_ENV=production" >> .env
            - echo "Validating production configuration..."
        build:
          commands:
            - echo "Starting backend server..."
            - npm start
      artifacts:
        baseDirectory: .
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
