import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { exec } from 'child_process';
import fetch from 'cross-fetch';

const execPromise = promisify(exec);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// Define directories
const uploadsDir = path.join(process.cwd(), 'uploads');
const audioDir = path.join(uploadsDir, 'audio');
const videoDir = path.join(uploadsDir, 'videos');
const tempDir = path.join(process.cwd(), 'temp');

// Job storage
const videoJobs = new Map<string, any>();

// Ensure directories exist
async function ensureDirectoriesExist() {
  for (const dir of [uploadsDir, audioDir, videoDir, tempDir]) {
    try {
      await mkdir(dir, { recursive: true });
    } catch (err) {
      if ((err as NodeJS.ErrnoException).code !== 'EEXIST') {
        throw err;
      }
    }
  }
}

interface VideoGenerationParams {
  jobId: string;
  script: string;
  voiceSettings: {
    voice: string;
    speed: number;
    service: string;
  };
  mediaUrls: string[];
  includeSubtitles?: boolean;
}

export class VideoPipeline {
  async generateVideo(params: VideoGenerationParams): Promise<void> {
    const { jobId, script, voiceSettings, mediaUrls, includeSubtitles = true } = params;
    
    try {
      await ensureDirectoriesExist();
      
      // Update job status
      this.updateJobStatus(jobId, 'processing', 10);
      
      // Step 1: Generate voice
      const audioUrl = await this.generateVoice(script, voiceSettings);
      this.updateJobStatus(jobId, 'processing', 30);
      
      // Step 2: Download media files
      const mediaPaths = await this.downloadMedia(mediaUrls, jobId);
      this.updateJobStatus(jobId, 'processing', 50);
      
      // Step 3: Get audio duration
      const audioPath = path.join(process.cwd(), audioUrl.replace(/^\//, ''));
      const audioDuration = await this.getAudioDuration(audioPath);
      this.updateJobStatus(jobId, 'processing', 70);
      
      // Step 4: Create video
      const outputPath = path.join(videoDir, `${jobId}.mp4`);
      await this.createVideoWithFFmpeg(audioPath, mediaPaths, outputPath, audioDuration, script, includeSubtitles);
      this.updateJobStatus(jobId, 'processing', 90);
      
      // Step 5: Complete
      const videoUrl = `/uploads/videos/${path.basename(outputPath)}`;
      this.updateJobStatus(jobId, 'completed', 100, videoUrl, audioUrl);
      
      // Cleanup
      this.cleanupFiles(mediaPaths);
      
    } catch (error) {
      console.error(`Video generation failed for job ${jobId}:`, error);
      this.updateJobStatus(jobId, 'error', 0, undefined, undefined, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  private async generateVoice(text: string, voiceSettings: any): Promise<string> {
    const response = await fetch('http://localhost:5000/api/ai/text-to-speech', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        text,
        voice: voiceSettings.voice,
        speed: voiceSettings.speed || 1.0,
        service: voiceSettings.service || 'openai'
      })
    });

    if (!response.ok) {
      throw new Error(`Voice generation failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result.audioUrl;
  }

  private async downloadMedia(mediaUrls: string[], jobId: string): Promise<string[]> {
    const downloadedPaths: string[] = [];
    
    for (let i = 0; i < mediaUrls.length; i++) {
      const url = mediaUrls[i];
      const fileName = `${jobId}_media_${i}${path.extname(url) || '.jpg'}`;
      const filePath = path.join(tempDir, fileName);
      
      try {
        const response = await fetch(url);
        if (!response.ok) {
          console.warn(`Failed to download media ${url}: ${response.statusText}`);
          continue;
        }
        
        const buffer = await response.arrayBuffer();
        await writeFile(filePath, Buffer.from(buffer));
        downloadedPaths.push(filePath);
      } catch (error) {
        console.warn(`Error downloading media ${url}:`, error);
      }
    }
    
    // If no media downloaded, create a default background
    if (downloadedPaths.length === 0) {
      const defaultPath = await this.createDefaultBackground(jobId);
      downloadedPaths.push(defaultPath);
    }
    
    return downloadedPaths;
  }

  private async createDefaultBackground(jobId: string): Promise<string> {
    const defaultPath = path.join(tempDir, `${jobId}_default.png`);
    
    // Create a simple colored background using FFmpeg
    const ffmpegCmd = `ffmpeg -y -f lavfi -i color=c=blue:size=1920x1080:duration=1 -frames:v 1 "${defaultPath}"`;
    await execPromise(ffmpegCmd);
    
    return defaultPath;
  }

  private async getAudioDuration(audioPath: string): Promise<number> {
    const ffprobeCmd = `ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "${audioPath}"`;
    const { stdout } = await execPromise(ffprobeCmd);
    const duration = parseFloat(stdout.trim());
    
    if (isNaN(duration)) {
      throw new Error('Failed to determine audio duration');
    }
    
    return duration;
  }

  private async createVideoWithFFmpeg(
    audioPath: string,
    mediaPaths: string[],
    outputPath: string,
    audioDuration: number,
    script: string,
    includeSubtitles: boolean
  ): Promise<void> {
    // Create image sequence file
    const imageListPath = path.join(tempDir, `${Date.now()}_images.txt`);
    const durationPerImage = audioDuration / mediaPaths.length;
    
    let imageListContent = '';
    for (const mediaPath of mediaPaths) {
      imageListContent += `file '${mediaPath}'\nduration ${durationPerImage}\n`;
    }
    
    // Add last image again (required by ffmpeg)
    if (mediaPaths.length > 0) {
      imageListContent += `file '${mediaPaths[mediaPaths.length - 1]}'\n`;
    }
    
    await writeFile(imageListPath, imageListContent);
    
    // Create video with audio
    let ffmpegCmd = `ffmpeg -y -f concat -safe 0 -i "${imageListPath}" -i "${audioPath}" -vsync vfr -pix_fmt yuv420p -r 30 -c:a aac -b:a 192k -shortest`;
    
    // Add subtitles if requested
    if (includeSubtitles) {
      const subtitlesPath = path.join(tempDir, `${Date.now()}_subtitles.srt`);
      const subtitleContent = this.generateSubtitles(script, audioDuration);
      await writeFile(subtitlesPath, subtitleContent);
      
      ffmpegCmd += ` -vf "subtitles='${subtitlesPath.replace(/\\/g, '\\\\')}'"`; 
    }
    
    ffmpegCmd += ` "${outputPath}"`;
    
    console.log(`Executing FFmpeg command: ${ffmpegCmd}`);
    const { stderr } = await execPromise(ffmpegCmd);
    
    if (stderr && !stderr.includes('frame=')) {
      console.warn('FFmpeg stderr:', stderr);
    }
    
    // Clean up temporary files
    fs.unlink(imageListPath, (err) => {
      if (err) console.error('Error deleting image list:', err);
    });
  }

  private generateSubtitles(script: string, duration: number): string {
    const words = script.split(/\s+/);
    const chunks: string[] = [];
    let currentChunk = '';
    
    for (const word of words) {
      if (currentChunk.length + word.length > 50) {
        chunks.push(currentChunk.trim());
        currentChunk = word;
      } else {
        currentChunk += ' ' + word;
      }
    }
    
    if (currentChunk.trim().length > 0) {
      chunks.push(currentChunk.trim());
    }
    
    const timePerChunk = duration / chunks.length;
    let srt = '';
    
    chunks.forEach((chunk, index) => {
      const startTime = index * timePerChunk;
      const endTime = (index + 1) * timePerChunk;
      
      srt += `${index + 1}\n`;
      srt += `${this.formatSRTTime(startTime)} --> ${this.formatSRTTime(endTime)}\n`;
      srt += `${chunk}\n\n`;
    });
    
    return srt;
  }

  private formatSRTTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const milliseconds = Math.floor((seconds - Math.floor(seconds)) * 1000);
    
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')},${String(milliseconds).padStart(3, '0')}`;
  }

  private cleanupFiles(filePaths: string[]): void {
    filePaths.forEach(filePath => {
      fs.unlink(filePath, (err) => {
        if (err) console.error('Error deleting file:', err);
      });
    });
  }

  private updateJobStatus(
    jobId: string, 
    status: string, 
    progress: number, 
    videoUrl?: string, 
    audioUrl?: string, 
    error?: string
  ): void {
    const job = videoJobs.get(jobId) || {};
    job.status = status;
    job.progress = progress;
    if (videoUrl) job.videoUrl = videoUrl;
    if (audioUrl) job.audioUrl = audioUrl;
    if (error) job.error = error;
    if (status === 'completed' || status === 'error') {
      job.completedAt = Date.now();
    }
    videoJobs.set(jobId, job);
  }

  getJobStatus(jobId: string): any {
    return videoJobs.get(jobId);
  }

  createJob(jobId: string, lessonId: string, moduleId: string): void {
    videoJobs.set(jobId, {
      status: 'queued',
      progress: 0,
      lessonId,
      moduleId,
      startTime: Date.now()
    });
  }
}

export const videoPipeline = new VideoPipeline();