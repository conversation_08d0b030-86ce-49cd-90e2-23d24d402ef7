import { useState, useRef, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Send, 
  PaperclipIcon, 
  Image, 
  FileIcon, 
  Smile, 
  Users,
  UserIcon,
  Hash,
  Video,
  MessageSquare
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useAuth } from '@/hooks/use-auth';
import { toast } from '@/hooks/use-toast';

// Types
type User = {
  id: number;
  name: string;
  username: string;
  avatarUrl: string | null;
  isOnline?: boolean;
};

type Message = {
  id: string;
  senderId: number;
  sender: User;
  content: string;
  timestamp: Date;
  attachments?: {
    id: string;
    type: 'image' | 'file';
    url: string;
    name: string;
  }[];
  reactions?: {
    emoji: string;
    count: number;
    userIds: number[];
  }[];
  isRead?: boolean;
};

type Channel = {
  id: string;
  name: string;
  type: 'text' | 'voice';
  members: User[];
  unreadCount?: number;
};

interface CollaborationChatProps {
  courseId?: number;
  teamId?: number;
  initialChannels?: Channel[];
  initialMembers?: User[];
}

// Sample data
const sampleUsers: User[] = [
  { id: 1, name: 'Current User', username: 'currentuser', avatarUrl: null, isOnline: true },
  { id: 2, name: 'Alex Johnson', username: 'alexj', avatarUrl: null, isOnline: true },
  { id: 3, name: 'Maria Garcia', username: 'mariag', avatarUrl: null, isOnline: false },
  { id: 4, name: 'James Wilson', username: 'jamesw', avatarUrl: null, isOnline: true },
];

const sampleChannels: Channel[] = [
  { 
    id: 'general', 
    name: 'general', 
    type: 'text', 
    members: sampleUsers,
    unreadCount: 0
  },
  { 
    id: 'course-content', 
    name: 'course-content', 
    type: 'text', 
    members: [sampleUsers[0], sampleUsers[1], sampleUsers[2]],
    unreadCount: 3
  },
  { 
    id: 'voice-channel', 
    name: 'team-voice', 
    type: 'voice', 
    members: [sampleUsers[1], sampleUsers[3]]
  },
];

const sampleMessages: Record<string, Message[]> = {
  'general': [
    {
      id: '1',
      senderId: 2,
      sender: sampleUsers[1],
      content: "Hi everyone! Has anyone started working on the introduction module yet?",
      timestamp: new Date(2025, 3, 28, 10, 15),
      isRead: true,
    },
    {
      id: '2',
      senderId: 3,
      sender: sampleUsers[2],
      content: "I've been working on the outline. Should have it ready by tomorrow.",
      timestamp: new Date(2025, 3, 28, 10, 17),
      isRead: true,
    },
    {
      id: '3',
      senderId: 1,
      sender: sampleUsers[0],
      content: "Great! I've also prepared some reference materials we can use. I'll upload them here.",
      timestamp: new Date(2025, 3, 28, 10, 20),
      isRead: true,
      attachments: [
        {
          id: 'file1',
          type: 'file',
          url: '#',
          name: 'reference_materials.pdf'
        }
      ]
    },
    {
      id: '4',
      senderId: 4,
      sender: sampleUsers[3],
      content: "Thanks! I'll review these and start working on the visuals for the introduction.",
      timestamp: new Date(2025, 3, 28, 10, 25),
      isRead: true,
    },
  ],
  'course-content': [
    {
      id: '5',
      senderId: 2,
      sender: sampleUsers[1],
      content: "I've finished the draft for Module 2. Could someone review it?",
      timestamp: new Date(2025, 3, 28, 14, 30),
      isRead: false,
    },
    {
      id: '6',
      senderId: 3,
      sender: sampleUsers[2],
      content: "I can take a look at it tomorrow morning.",
      timestamp: new Date(2025, 3, 28, 14, 35),
      isRead: false,
    },
    {
      id: '7',
      senderId: 2,
      sender: sampleUsers[1],
      content: "Here's the link to the document:",
      timestamp: new Date(2025, 3, 28, 14, 36),
      isRead: false,
      attachments: [
        {
          id: 'file2',
          type: 'file',
          url: '#',
          name: 'module_2_draft.docx'
        }
      ]
    },
  ]
};

export function CollaborationChat({ 
  courseId, 
  teamId, 
  initialChannels = sampleChannels,
  initialMembers = sampleUsers
}: CollaborationChatProps) {
  const { user } = useAuth();
  const [channels, setChannels] = useState<Channel[]>(initialChannels);
  const [activeChannel, setActiveChannel] = useState<string>(initialChannels[0]?.id || '');
  const [messages, setMessages] = useState<Record<string, Message[]>>(sampleMessages);
  const [newMessage, setNewMessage] = useState('');
  const [members, setMembers] = useState<User[]>(initialMembers);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Scroll to the bottom of messages when they change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, activeChannel]);
  
  // Send a new message
  const handleSendMessage = () => {
    if (!newMessage.trim()) return;
    
    const newMsg: Message = {
      id: Date.now().toString(),
      senderId: user?.id || 1,
      sender: {
        id: user?.id || 1,
        name: user?.name || user?.username || 'Current User',
        username: user?.username || 'currentuser',
        avatarUrl: user?.avatarUrl || null,
        isOnline: true
      },
      content: newMessage,
      timestamp: new Date(),
      isRead: true
    };
    
    setMessages(prev => ({
      ...prev,
      [activeChannel]: [...(prev[activeChannel] || []), newMsg]
    }));
    
    setNewMessage('');
  };
  
  // Handle keypress (Enter to send)
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  // Format timestamp
  const formatTimestamp = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date);
  };
  
  return (
    <Card className="flex flex-col w-full h-[600px]">
      <CardHeader className="p-4 border-b">
        <CardTitle className="text-lg font-medium">Collaboration Chat</CardTitle>
        <CardDescription>
          Communicate with your team in real-time
        </CardDescription>
      </CardHeader>
      
      <div className="flex flex-grow overflow-hidden">
        {/* Sidebar */}
        <div className="w-64 border-r flex flex-col">
          <Tabs defaultValue="channels" className="flex-grow">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="channels">Channels</TabsTrigger>
              <TabsTrigger value="members">Members</TabsTrigger>
            </TabsList>
            
            <TabsContent value="channels" className="flex flex-col overflow-hidden h-[calc(600px-110px)]">
              <ScrollArea className="flex-grow">
                <div className="px-4 py-2 space-y-1">
                  {channels.map(channel => (
                    <button
                      key={channel.id}
                      className={`w-full flex items-center px-2 py-1.5 rounded-md text-left 
                                ${activeChannel === channel.id 
                                  ? 'bg-primary/10 text-primary' 
                                  : 'hover:bg-slate-100'}`}
                      onClick={() => setActiveChannel(channel.id)}
                    >
                      {channel.type === 'text' ? (
                        <Hash className="h-4 w-4 mr-2 shrink-0" />
                      ) : (
                        <MessageSquare className="h-4 w-4 mr-2 shrink-0" />
                      )}
                      <span className="truncate">{channel.name}</span>
                      {(channel.unreadCount && channel.unreadCount > 0) && (
                        <Badge variant="default" className="ml-auto">
                          {channel.unreadCount}
                        </Badge>
                      )}
                    </button>
                  ))}
                </div>
              </ScrollArea>
              
              <div className="p-4 border-t">
                <Button variant="outline" className="w-full" onClick={() => {
                  toast({
                    title: "Coming Soon",
                    description: "Channel creation will be available in a future update",
                  });
                }}>
                  Create Channel
                </Button>
              </div>
            </TabsContent>
            
            <TabsContent value="members" className="flex flex-col overflow-hidden h-[calc(600px-110px)]">
              <ScrollArea className="flex-grow">
                <div className="px-4 py-2 space-y-1">
                  {members.map(member => (
                    <div key={member.id} className="flex items-center px-2 py-1.5 rounded-md hover:bg-slate-100">
                      <div className="relative">
                        <Avatar className="h-6 w-6">
                          {member.avatarUrl ? (
                            <AvatarImage src={member.avatarUrl} alt={member.name} />
                          ) : (
                            <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                          )}
                        </Avatar>
                        {member.isOnline && (
                          <span className="absolute bottom-0 right-0 block h-2 w-2 rounded-full bg-emerald-500 ring-1 ring-white" />
                        )}
                      </div>
                      <span className="ml-2 truncate">{member.name}</span>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              
              <div className="p-4 border-t">
                <Button variant="outline" className="w-full" onClick={() => {
                  toast({
                    title: "Coming Soon",
                    description: "Invite functionality will be available in a future update",
                  });
                }}>
                  Invite Members
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        {/* Main Chat Area */}
        <div className="flex-grow flex flex-col overflow-hidden">
          {/* Current Channel Header */}
          <div className="p-3 border-b flex items-center justify-between">
            <div className="flex items-center">
              <Hash className="h-5 w-5 mr-2 text-slate-500" />
              <span className="font-medium">{channels.find(c => c.id === activeChannel)?.name}</span>
            </div>
            
            <div className="flex gap-2">
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8" 
                onClick={() => {
                  toast({
                    title: "Video Call",
                    description: "Redirecting to video call interface",
                  });
                  // In a real implementation we would use navigation here
                }}
              >
                <Video className="h-4 w-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8"
                onClick={() => {
                  toast({
                    title: "Members",
                    description: `${channels.find(c => c.id === activeChannel)?.members.length} members in this channel`,
                  });
                }}
              >
                <Users className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Messages */}
          <ScrollArea className="flex-grow px-4 py-2">
            <div className="space-y-4">
              {messages[activeChannel]?.map((message, index) => {
                // Check if we should display the date
                const showDate = index === 0 || (
                  new Date(message.timestamp).toDateString() !== 
                  new Date(messages[activeChannel][index - 1].timestamp).toDateString()
                );
                
                // Check if we should group messages (same sender, close in time)
                const isGrouped = index > 0 && 
                  message.senderId === messages[activeChannel][index - 1].senderId &&
                  new Date(message.timestamp).getTime() - 
                  new Date(messages[activeChannel][index - 1].timestamp).getTime() < 300000; // 5 minutes
                
                return (
                  <div key={message.id}>
                    {showDate && (
                      <div className="flex justify-center my-4">
                        <div className="bg-slate-100 px-3 py-1 rounded-full text-xs text-slate-500">
                          {new Date(message.timestamp).toLocaleDateString('en-US', { 
                            weekday: 'long', 
                            month: 'short', 
                            day: 'numeric' 
                          })}
                        </div>
                      </div>
                    )}
                    
                    <div className={`flex ${isGrouped ? 'mt-1' : 'mt-4'}`}>
                      {!isGrouped ? (
                        <Avatar className="h-8 w-8 mt-0.5 mr-3">
                          {message.sender.avatarUrl ? (
                            <AvatarImage src={message.sender.avatarUrl} alt={message.sender.name} />
                          ) : (
                            <AvatarFallback>{message.sender.name.charAt(0)}</AvatarFallback>
                          )}
                        </Avatar>
                      ) : (
                        <div className="w-8 mr-3" />
                      )}
                      
                      <div className="flex-grow">
                        {!isGrouped && (
                          <div className="flex items-baseline">
                            <span className="font-medium mr-2">{message.sender.name}</span>
                            <span className="text-xs text-slate-500">
                              {formatTimestamp(new Date(message.timestamp))}
                            </span>
                          </div>
                        )}
                        
                        <div>
                          <p className="text-slate-800 break-words">{message.content}</p>
                          
                          {message.attachments && message.attachments.length > 0 && (
                            <div className="mt-2 space-y-2">
                              {message.attachments.map(attachment => (
                                <div 
                                  key={attachment.id} 
                                  className="flex items-center p-2 rounded-md bg-slate-50 border"
                                >
                                  {attachment.type === 'image' ? (
                                    <Image className="h-4 w-4 mr-2 text-slate-500" />
                                  ) : (
                                    <FileIcon className="h-4 w-4 mr-2 text-slate-500" />
                                  )}
                                  <span className="text-sm">{attachment.name}</span>
                                  <Button 
                                    variant="ghost" 
                                    size="sm" 
                                    className="ml-auto h-7 px-2"
                                    onClick={() => {
                                      toast({
                                        title: "Download",
                                        description: `Downloading ${attachment.name}`,
                                      });
                                    }}
                                  >
                                    Download
                                  </Button>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>
          
          {/* Message Input */}
          <div className="p-3 border-t">
            <div className="relative">
              <Textarea
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                placeholder={`Message #${channels.find(c => c.id === activeChannel)?.name}`}
                className="w-full pr-24 min-h-12 max-h-24 resize-none"
                rows={1}
              />
              <div className="absolute right-2 bottom-2 flex items-center gap-1">
                <Button 
                  variant="ghost" 
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={() => {
                    toast({
                      title: "Attachments",
                      description: "File upload will be available in a future update",
                    });
                  }}
                >
                  <PaperclipIcon className="h-4 w-4" />
                </Button>
                <Button 
                  variant="ghost" 
                  size="icon"
                  className="h-8 w-8 rounded-full"
                  onClick={() => {
                    toast({
                      title: "Emoji",
                      description: "Emoji selector will be available in a future update",
                    });
                  }}
                >
                  <Smile className="h-4 w-4" />
                </Button>
                <Button 
                  className="h-8 w-8 rounded-full px-0"
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}