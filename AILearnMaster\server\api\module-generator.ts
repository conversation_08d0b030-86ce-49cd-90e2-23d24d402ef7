import { Router, Request, Response } from "express";
import * as openAIFallbackService from "../services/openAIFallbackService";
import { z } from "zod";

export const moduleGeneratorRouter = Router();

// Module structure generation endpoint
moduleGeneratorRouter.post("/generate-module-structure", async (req: Request, res: Response) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }
    
    // Schema for request validation
    const moduleStructureSchema = z.object({
      courseTitle: z.string(),
      courseDescription: z.string().optional(),
      existingModules: z.array(z.string()).optional(),
      moduleTitle: z.string().optional(),
      moduleIndex: z.number().optional(),
      totalModules: z.number().optional()
    });
    
    // Validate request
    const validationResult = moduleStructureSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: "Invalid request data", 
        errors: validationResult.error.format() 
      });
    }
    
    const { 
      courseTitle, 
      courseDescription = "", 
      existingModules = [], 
      moduleTitle, 
      moduleIndex, 
      totalModules 
    } = validationResult.data;
    
    try {
      // Generate module structure using OpenAI
      const moduleStructure = await openAIFallbackService.generateModuleStructureWithOpenAI(
        courseTitle,
        courseDescription,
        existingModules,
        moduleTitle,
        moduleIndex,
        totalModules
      );
      
      return res.status(200).json(moduleStructure);
    } catch (aiError: any) {
      console.error("Error generating module structure with AI:", aiError);
      
      // Return a simple fallback structure if AI generation fails
      return res.status(200).json({
        title: moduleTitle || `Module ${moduleIndex || ''} for ${courseTitle}`,
        description: `A comprehensive module covering key topics related to ${courseTitle}.`
      });
    }
  } catch (error: any) {
    console.error("Server error generating module structure:", error);
    return res.status(500).json({ 
      message: "Failed to generate module structure", 
      error: error.message 
    });
  }
});