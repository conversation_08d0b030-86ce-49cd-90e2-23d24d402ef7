#!/usr/bin/env python3
"""
Debug TTS endpoint specifically
"""

import requests
import json

def test_tts_debug():
    """Debug TTS endpoint"""
    print("🔊 Debugging TTS Endpoint...")
    
    url = "https://trade-digital--courseai-a100-simple-api-tts.modal.run"
    
    # Test with minimal payload
    payload = {
        "text": "Hello world",
        "voice_id": "tts_models/en/ljspeech/tacotron2-DDC"
    }
    
    try:
        print(f"Testing URL: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, json=payload, timeout=120)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response JSON: {json.dumps(data, indent=2)}")
            except json.JSONDecodeError:
                print(f"Response Text: {response.text}")
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"Exception: {e}")

if __name__ == "__main__":
    test_tts_debug()
