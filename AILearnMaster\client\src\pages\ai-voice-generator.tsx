import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON>Footer, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import { apiRequest } from "@/lib/queryClient";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Play, Volume2, Save, Upload, MicIcon, RefreshCcw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useAuth } from "@/hooks/use-auth";
import { ExtractSpeechText } from "@/components/script/ExtractSpeechText";

type Voice = {
  voice_id: string;
  name: string;
  preview_url?: string;
  category?: string;
  description?: string;
  accent?: string;
  gender?: string;
  source?: string;  // 'coqui' or 'elevenlabs'
};

type Model = {
  model_id: string;
  name: string;
  description?: string;
};

type CourseScript = {
  id: number;
  courseId: number;
  lessonId: number;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
};

const AIVoiceGenerator = () => {
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [activeTab, setActiveTab] = useState("customize");
  const [voices, setVoices] = useState<Voice[]>([]);
  const [filteredVoices, setFilteredVoices] = useState<Voice[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [courseScripts, setCourseScripts] = useState<CourseScript[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>("");
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [selectedScript, setSelectedScript] = useState<string>("");
  const [text, setText] = useState<string>("Hello, this is a test of the AI voice generation feature. I hope you like how it sounds!");
  const [stability, setStability] = useState<number>(0.5);
  const [similarityBoost, setSimilarityBoost] = useState<number>(0.75);
  const [style, setStyle] = useState<number>(0);
  const [speakerBoost, setSpeakerBoost] = useState<boolean>(true);
  const [speed, setSpeed] = useState<number>(1.0);
  const [emotion, setEmotion] = useState<string>("neutral");
  const [tone, setTone] = useState<string>("professional");
  const [loading, setLoading] = useState<boolean>(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [fetchingData, setFetchingData] = useState<boolean>(true);
  const [genderFilter, setGenderFilter] = useState<string>("all");
  const [accentFilter, setAccentFilter] = useState<string>("all");
  const [cloneFileName, setCloneFileName] = useState<string>("");
  const [cloneName, setCloneName] = useState<string>("");
  const [cloneDescription, setCloneDescription] = useState<string>("");
  const [cloneLoading, setCloneLoading] = useState<boolean>(false);
  const [useOpenAI, setUseOpenAI] = useState<boolean>(false);
  const [selectedProvider, setSelectedProvider] = useState<string>("coqui"); // Default to Coqui TTS
  const { toast } = useToast();

  // Handle voice filter changes including provider selection
  useEffect(() => {
    if (voices.length > 0) {
      let filtered = [...voices];
      
      // First filter by provider
      if (!useOpenAI && selectedProvider) {
        filtered = filtered.filter(voice => voice.source === selectedProvider);
      }
      
      // Apply gender filter
      if (genderFilter !== "all") {
        filtered = filtered.filter(voice => voice.gender === genderFilter);
      }
      
      // Apply accent filter
      if (accentFilter !== "all") {
        filtered = filtered.filter(voice => voice.accent === accentFilter);
      }
      
      setFilteredVoices(filtered);
      
      // Reset selected voice if it's not in filtered list
      if (filtered.length > 0 && !filtered.some(v => v.voice_id === selectedVoice)) {
        setSelectedVoice(filtered[0].voice_id);
      }
    }
  }, [genderFilter, accentFilter, voices, selectedVoice, selectedProvider, useOpenAI]);

  useEffect(() => {
    // Fetch voices from all providers
    const fetchVoices = async () => {
      try {
        // Use the all-voices endpoint to get voices from both Coqui and ElevenLabs
        const response = await apiRequest("GET", "/api/ai/all-voices");
        const data = await response.json();
        
        // Add gender and accent attributes if not present
        const enhancedVoices = data.map((voice: Voice) => ({
          ...voice,
          gender: voice.gender || (voice.name.includes("Female") ? "female" : 
                    voice.name.includes("Male") ? "male" : "other"),
          accent: voice.accent || (
            voice.name.includes("British") ? "british" : 
            voice.name.includes("American") ? "american" :
            voice.name.includes("Indian") ? "indian" :
            voice.name.includes("Australian") ? "australian" :
            voice.name.includes("Spanish") ? "spanish" : "other"
          )
        }));
        
        // Set initial provider based on what's available
        if (enhancedVoices.some((voice: Voice) => voice.source === 'coqui')) {
          setSelectedProvider('coqui');
        } else if (enhancedVoices.some((voice: Voice) => voice.source === 'elevenlabs')) {
          setSelectedProvider('elevenlabs');
        }
        
        setVoices(enhancedVoices);
        setFilteredVoices(enhancedVoices);
        
        // Select first voice of selected provider
        const providerVoices = enhancedVoices.filter((v: Voice) => v.source === selectedProvider);
        if (providerVoices.length > 0) {
          setSelectedVoice(providerVoices[0].voice_id);
        } else if (enhancedVoices.length > 0) {
          setSelectedVoice(enhancedVoices[0].voice_id);
        }
      } catch (error) {
        console.error("Failed to fetch voices:", error);
        toast({
          title: "Error fetching voices",
          description: "Could not load voice options. Falling back to OpenAI voices.",
          variant: "destructive",
        });
        setUseOpenAI(true);
      }
    };

    // Fetch models
    const fetchModels = async () => {
      try {
        const response = await apiRequest("GET", "/api/ai/tts-models");
        const data = await response.json();
        setModels(data);
        if (data.length > 0) {
          setSelectedModel(data[0].model_id);
        }
      } catch (error) {
        console.error("Failed to fetch models:", error);
      }
    };

    // Fetch course scripts
    const fetchCourseScripts = async () => {
      try {
        const response = await apiRequest("GET", "/api/course-scripts");
        const data = await response.json();
        setCourseScripts(data);
      } catch (error) {
        console.error("Failed to fetch course scripts:", error);
      } finally {
        setFetchingData(false);
      }
    };

    fetchVoices();
    fetchModels();
    fetchCourseScripts();
  }, [toast]);

  const handleGenerate = async () => {
    if ((!selectedVoice && !useOpenAI) || !text) {
      toast({
        title: "Missing information",
        description: "Please select a voice and enter text to convert to speech.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    setAudioUrl(null);

    try {
      const endpoint = useOpenAI ? "/api/ai/openai-text-to-speech" : "/api/ai/text-to-speech";
      
      // Include the provider parameter
      const response = await apiRequest("POST", endpoint, {
        text,
        voiceId: selectedVoice,
        provider: selectedProvider, // Add provider parameter (Coqui or ElevenLabs)
        modelId: selectedModel,
        stability,
        similarityBoost,
        style,
        speakerBoost,
        speed,
        emotion,
        tone,
        // Add speaker parameter for Coqui voices
        speaker: selectedProvider === 'coqui' ? 'en_US_001' : undefined
      });

      const data = await response.json();

      if (response.ok) {
        // Handle different response formats from different providers
        // The API returns audioData as base64 string when using Coqui
        if (data.audioData) {
          // Convert the Buffer data to audio URL
          const audioBuffer = new Uint8Array(data.audioData.data);
          const blob = new Blob([audioBuffer], { type: 'audio/wav' });
          const url = URL.createObjectURL(blob);
          setAudioUrl(url);
        } else if (data.url) {
          // Direct URL from ElevenLabs or uploaded audio
          setAudioUrl(data.url);
        } else {
          throw new Error("No audio data received from the server");
        }
        
        toast({
          title: "Success!",
          description: `Audio generated successfully using ${selectedProvider === 'coqui' ? 'Coqui TTS' : 'ElevenLabs'}`,
        });
      } else {
        throw new Error(data.message || "Unknown error");
      }
    } catch (error) {
      console.error("Error generating audio:", error);
      
      // If primary service failed and we weren't already using OpenAI, try with the other provider
      if (!useOpenAI) {
        // Try the other provider first before falling back to OpenAI
        const otherProvider = selectedProvider === 'coqui' ? 'elevenlabs' : 'coqui';
        
        toast({
          title: `Trying with ${otherProvider === 'coqui' ? 'Coqui TTS' : 'ElevenLabs'}`,
          description: `Falling back to ${otherProvider} voice service`,
        });
        
        try {
          // Get a voice from the other provider
          const otherProviderVoice = voices.find(v => v.source === otherProvider)?.voice_id;
          
          if (otherProviderVoice) {
            const response = await apiRequest("POST", "/api/ai/text-to-speech", {
              text,
              voiceId: otherProviderVoice,
              provider: otherProvider,
              // Include only parameters relevant to the other provider
              ...(otherProvider === 'elevenlabs' ? {
                modelId: selectedModel,
                stability,
                similarityBoost,
                style,
                speakerBoost
              } : {
                speaker: 'en_US_001'
              })
            });
            
            const data = await response.json();
            
            if (response.ok) {
              // Handle different response formats from different providers
              if (data.audioData) {
                // Convert the Buffer data to audio URL
                const audioBuffer = new Uint8Array(data.audioData.data);
                const blob = new Blob([audioBuffer], { type: 'audio/wav' });
                const url = URL.createObjectURL(blob);
                setAudioUrl(url);
              } else if (data.url) {
                // Direct URL from ElevenLabs or uploaded audio
                setAudioUrl(data.url);
              } else {
                throw new Error("No audio data received from the server");
              }
              
              toast({
                title: `Success with ${otherProvider === 'coqui' ? 'Coqui TTS' : 'ElevenLabs'}!`,
                description: "Audio generated successfully using fallback service",
              });
              return;
            }
          }
          
          // If the other provider failed too, try OpenAI as last resort
          setUseOpenAI(true);
          toast({
            title: "Trying with OpenAI",
            description: "Falling back to OpenAI voice service as a last resort",
          });
          
          const openaiResponse = await apiRequest("POST", "/api/ai/openai-text-to-speech", {
            text,
            voice: "onyx", // Default OpenAI voice
            speed,
          });
          
          const openaiData = await openaiResponse.json();
          
          if (openaiResponse.ok) {
            setAudioUrl(openaiData.url);
            toast({
              title: "Success with OpenAI!",
              description: "Audio generated successfully using OpenAI as fallback",
            });
          } else {
            throw new Error(openaiData.message || "Unknown error with OpenAI fallback");
          }
        } catch (fallbackError) {
          console.error("Error with fallback services:", fallbackError);
          toast({
            title: "Error generating audio",
            description: "All TTS services failed. Please try again later.",
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Error generating audio",
          description: error instanceof Error ? error.message : "An unknown error occurred",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCloneVoice = async () => {
    // Only available for paid users
    if (user?.plan === "Free") {
      toast({
        title: "Upgrade Required",
        description: "Voice cloning is only available for paid subscribers. Please upgrade your plan to use this feature.",
        variant: "destructive",
      });
      return;
    }

    if (!fileInputRef.current?.files?.length) {
      toast({
        title: "Missing file",
        description: "Please upload an audio sample for voice cloning.",
        variant: "destructive",
      });
      return;
    }

    if (!cloneName) {
      toast({
        title: "Missing information",
        description: "Please provide a name for your cloned voice.",
        variant: "destructive",
      });
      return;
    }

    setCloneLoading(true);

    try {
      const formData = new FormData();
      formData.append('sample', fileInputRef.current.files[0]);
      formData.append('name', cloneName);
      formData.append('description', cloneDescription);

      const response = await fetch('/api/ai/clone-voice', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Voice Cloned Successfully",
          description: "Your voice has been cloned and is now available in the voice selection dropdown.",
        });
        
        // Add the new voice to the list
        setVoices(prev => [...prev, data]);
        setSelectedVoice(data.voice_id);
        
        // Reset form
        setCloneName("");
        setCloneDescription("");
        setCloneFileName("");
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
        
        // Switch to customize tab
        setActiveTab("customize");
      } else {
        throw new Error(data.message || "Unknown error");
      }
    } catch (error) {
      console.error("Error cloning voice:", error);
      toast({
        title: "Error Cloning Voice",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setCloneLoading(false);
    }
  };

  const handleScriptSelect = (scriptId: string) => {
    if (scriptId) {
      const script = courseScripts.find(script => script.id.toString() === scriptId);
      if (script) {
        setText(script.content);
      }
    }
  };

  const handleSaveToLibrary = async () => {
    if (!audioUrl) {
      toast({
        title: "No audio to save",
        description: "Please generate audio first before saving to library.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Determine the correct source for the saved audio
      let audioSource = "coqui"; // Default to Coqui
      
      if (useOpenAI) {
        audioSource = "openai";
      } else {
        audioSource = selectedProvider; // Will be 'coqui' or 'elevenlabs'
      }
      
      const response = await apiRequest("POST", "/api/media", {
        type: "audio",
        name: "Generated Voice - " + new Date().toISOString().split('T')[0],
        url: audioUrl,
        source: audioSource,
        mimeType: "audio/mpeg",
        duration: 0, // This would be calculated from the audio file
      });

      if (response.ok) {
        toast({
          title: "Saved to Library",
          description: `Audio has been saved to your media library (Source: ${audioSource})`,
        });
      } else {
        const data = await response.json();
        throw new Error(data.message || "Unknown error");
      }
    } catch (error) {
      console.error("Error saving to library:", error);
      toast({
        title: "Error saving to library",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const playPreviewAudio = (previewUrl?: string) => {
    if (previewUrl) {
      const audio = new Audio(previewUrl);
      audio.play();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setCloneFileName(event.target.files[0].name);
    } else {
      setCloneFileName("");
    }
  };

  // Function to get unique accent values from voices
  const getUniqueAccents = () => {
    const accents = new Set(voices.map(voice => voice.accent));
    return Array.from(accents).filter(Boolean);
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-2">AI Voice Generator</h1>
      <p className="text-slate-600 mb-6">
        Create natural-sounding voiceovers for your courses with AI voice technology.
        {useOpenAI && " Currently using OpenAI voice services."}
      </p>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="customize">Customize Voice</TabsTrigger>
          <TabsTrigger value="clone" disabled={user?.plan === "Free"}>Clone Your Voice</TabsTrigger>
          <TabsTrigger value="scripts">Course Scripts</TabsTrigger>
        </TabsList>

        <TabsContent value="customize" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle>Voice Settings</CardTitle>
                <CardDescription>
                  Customize the voice and settings for your generated audio
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="text">Text to convert</Label>
                  <Textarea
                    id="text"
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    placeholder="Enter text to convert to speech..."
                    className="min-h-[150px]"
                  />
                </div>

                {!useOpenAI && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Voice Provider Selection */}
                    <div className="space-y-2">
                      <Label htmlFor="provider">Voice Provider</Label>
                      <Select 
                        value={selectedProvider} 
                        onValueChange={(value) => {
                          setSelectedProvider(value);
                          // Update selected voice when provider changes
                          const providerVoices = voices.filter(v => v.source === value);
                          if (providerVoices.length > 0) {
                            setSelectedVoice(providerVoices[0].voice_id);
                          }
                        }}
                      >
                        <SelectTrigger id="provider">
                          <SelectValue placeholder="Select voice provider" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="coqui">Coqui TTS (Default)</SelectItem>
                          <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  
                    <div className="space-y-2">
                      <Label htmlFor="gender-filter">Gender</Label>
                      <Select value={genderFilter} onValueChange={setGenderFilter}>
                        <SelectTrigger id="gender-filter">
                          <SelectValue placeholder="Filter by gender" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Genders</SelectItem>
                          <SelectItem value="male">Male</SelectItem>
                          <SelectItem value="female">Female</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="accent-filter">Accent</Label>
                      <Select value={accentFilter} onValueChange={setAccentFilter}>
                        <SelectTrigger id="accent-filter">
                          <SelectValue placeholder="Filter by accent" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Accents</SelectItem>
                          {getUniqueAccents().map((accent) => (
                            <SelectItem key={accent} value={accent || ""}>
                              {accent?.charAt(0).toUpperCase() + accent?.slice(1) || "Unknown"}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Display TTS model selector only for ElevenLabs, not needed for Coqui */}
                    {selectedProvider === 'elevenlabs' && (
                      <div className="space-y-2 md:col-span-3">
                        <Label htmlFor="model">TTS Model (ElevenLabs)</Label>
                        <Select
                          value={selectedModel}
                          onValueChange={setSelectedModel}
                          disabled={fetchingData}
                        >
                          <SelectTrigger id="model">
                            <SelectValue placeholder="Select a model" />
                          </SelectTrigger>
                          <SelectContent>
                            {models.map((model) => (
                              <SelectItem key={model.model_id} value={model.model_id}>
                                {model.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="voice">Voice Selection</Label>
                  
                  {useOpenAI ? (
                    <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                      <SelectTrigger id="voice">
                        <SelectValue placeholder="Select OpenAI voice" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="alloy">Alloy (Neutral)</SelectItem>
                        <SelectItem value="echo">Echo (Male)</SelectItem>
                        <SelectItem value="fable">Fable (Male)</SelectItem>
                        <SelectItem value="onyx">Onyx (Male)</SelectItem>
                        <SelectItem value="nova">Nova (Female)</SelectItem>
                        <SelectItem value="shimmer">Shimmer (Female)</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Select
                      value={selectedVoice}
                      onValueChange={setSelectedVoice}
                      disabled={fetchingData}
                    >
                      <SelectTrigger id="voice">
                        <SelectValue placeholder="Select a voice" />
                      </SelectTrigger>
                      <SelectContent className="max-h-80">
                        {filteredVoices
                          .filter((voice) => voice.source === selectedProvider)
                          .map((voice) => (
                          <SelectItem key={voice.voice_id} value={voice.voice_id}>
                            <div className="flex items-center justify-between w-full">
                              <span>
                                {voice.name}
                                <span className="text-xs text-muted-foreground ml-2">
                                  {voice.gender === "male" ? "♂" : voice.gender === "female" ? "♀" : ""}
                                  {voice.accent && ` - ${voice.accent.charAt(0).toUpperCase() + voice.accent.slice(1)}`}
                                </span>
                              </span>
                              {voice.preview_url && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 ml-2"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    playPreviewAudio(voice.preview_url);
                                  }}
                                >
                                  <Play className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}

                  {useOpenAI && (
                    <div className="mt-2">
                      <Button 
                        variant="secondary" 
                        size="sm" 
                        onClick={() => {
                          setUseOpenAI(false);
                          setSelectedProvider('coqui'); // Default to Coqui when coming back from OpenAI
                        }}
                        disabled={loading}
                        className="flex items-center"
                      >
                        <RefreshCcw className="mr-2 h-4 w-4" /> Try Coqui TTS instead
                      </Button>
                    </div>
                  )}
                </div>

                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="advanced-settings">
                    <AccordionTrigger>Advanced Voice Settings</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-6 pt-2">
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <Label htmlFor="speed">Speed ({speed.toFixed(1)}x)</Label>
                          </div>
                          <Slider
                            id="speed"
                            min={0.5}
                            max={2.0}
                            step={0.1}
                            value={[speed]}
                            onValueChange={(value) => setSpeed(value[0])}
                          />
                          <p className="text-sm text-muted-foreground">
                            Adjust the speed of the audio playback.
                          </p>
                        </div>

                        {!useOpenAI && (
                          <>
                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <Label htmlFor="stability">Stability ({stability.toFixed(2)})</Label>
                              </div>
                              <Slider
                                id="stability"
                                min={0}
                                max={1}
                                step={0.01}
                                value={[stability]}
                                onValueChange={(value) => setStability(value[0])}
                              />
                              <p className="text-sm text-muted-foreground">
                                Higher values give more consistent results between generation but may sound monotonous.
                              </p>
                            </div>

                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <Label htmlFor="similarity-boost">Similarity Boost ({similarityBoost.toFixed(2)})</Label>
                              </div>
                              <Slider
                                id="similarity-boost"
                                min={0}
                                max={1}
                                step={0.01}
                                value={[similarityBoost]}
                                onValueChange={(value) => setSimilarityBoost(value[0])}
                              />
                              <p className="text-sm text-muted-foreground">
                                Higher values make the voice more closely resemble the reference sample.
                              </p>
                            </div>

                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <Label htmlFor="style">Style ({style.toFixed(2)})</Label>
                              </div>
                              <Slider
                                id="style"
                                min={0}
                                max={1}
                                step={0.01}
                                value={[style]}
                                onValueChange={(value) => setStyle(value[0])}
                              />
                              <p className="text-sm text-muted-foreground">
                                Higher values enhance the style of speech.
                              </p>
                            </div>

                            <div className="flex items-center space-x-2 pt-2">
                              <Switch
                                id="speaker-boost"
                                checked={speakerBoost}
                                onCheckedChange={setSpeakerBoost}
                              />
                              <Label htmlFor="speaker-boost">Speaker Boost</Label>
                            </div>
                          </>
                        )}

                        <div className="space-y-4 pt-2">
                          <div className="space-y-2">
                            <Label>Emotion</Label>
                            <RadioGroup value={emotion} onValueChange={setEmotion} className="flex flex-wrap gap-4">
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="neutral" id="emotion-neutral" />
                                <Label htmlFor="emotion-neutral">Neutral</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="happy" id="emotion-happy" />
                                <Label htmlFor="emotion-happy">Happy</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="sad" id="emotion-sad" />
                                <Label htmlFor="emotion-sad">Sad</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="excited" id="emotion-excited" />
                                <Label htmlFor="emotion-excited">Excited</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="serious" id="emotion-serious" />
                                <Label htmlFor="emotion-serious">Serious</Label>
                              </div>
                            </RadioGroup>
                          </div>

                          <div className="space-y-2">
                            <Label>Tone</Label>
                            <RadioGroup value={tone} onValueChange={setTone} className="flex flex-wrap gap-4">
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="professional" id="tone-professional" />
                                <Label htmlFor="tone-professional">Professional</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="conversational" id="tone-conversational" />
                                <Label htmlFor="tone-conversational">Conversational</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="friendly" id="tone-friendly" />
                                <Label htmlFor="tone-friendly">Friendly</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="authoritative" id="tone-authoritative" />
                                <Label htmlFor="tone-authoritative">Authoritative</Label>
                              </div>
                            </RadioGroup>
                          </div>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button onClick={handleGenerate} disabled={loading || !text}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
                    </>
                  ) : (
                    "Generate Speech"
                  )}
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={handleSaveToLibrary} 
                  disabled={!audioUrl}
                  className="ml-2"
                >
                  <Save className="mr-2 h-4 w-4" /> Save to Library
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Audio Preview</CardTitle>
                <CardDescription>Listen to the generated audio</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center justify-center min-h-[200px]">
                {loading ? (
                  <div className="flex flex-col items-center space-y-4">
                    <Loader2 className="h-16 w-16 animate-spin text-primary" />
                    <p>Generating audio, please wait...</p>
                  </div>
                ) : audioUrl ? (
                  <div className="w-full space-y-4">
                    <div className="flex justify-center">
                      <Volume2 className="h-16 w-16 text-primary" />
                    </div>
                    <audio controls className="w-full mt-4" src={audioUrl}>
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground">
                    <p>Generated audio will appear here</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="clone">
          <Card>
            <CardHeader>
              <CardTitle>Clone Your Voice</CardTitle>
              <CardDescription>
                Create a custom voice by uploading a sample of your voice (Paid users only)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="voice-name">Voice Name</Label>
                <Input
                  id="voice-name"
                  value={cloneName}
                  onChange={(e) => setCloneName(e.target.value)}
                  placeholder="E.g., My Custom Voice"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="voice-description">Voice Description (Optional)</Label>
                <Input
                  id="voice-description"
                  value={cloneDescription}
                  onChange={(e) => setCloneDescription(e.target.value)}
                  placeholder="E.g., My professional teaching voice"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="voice-sample">Voice Sample</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Sample
                  </Button>
                  <Input
                    ref={fileInputRef}
                    id="voice-sample"
                    type="file"
                    accept="audio/mp3,audio/wav,audio/m4a"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                  <span className="text-sm text-muted-foreground">
                    {cloneFileName || "No file selected"}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Upload a clear audio sample of your voice (MP3, WAV, or M4A format).
                  For best results, use a 1-2 minute recording of you speaking naturally.
                </p>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleCloneVoice}
                disabled={cloneLoading || !cloneFileName || !cloneName}
              >
                {cloneLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Cloning Voice...
                  </>
                ) : (
                  <>
                    <MicIcon className="mr-2 h-4 w-4" /> Clone Voice
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="scripts">
          <Card>
            <CardHeader>
              <CardTitle>Use Course Scripts</CardTitle>
              <CardDescription>
                Select a script from your courses to generate audio
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="script">Select Script</Label>
                <Select
                  value={selectedScript}
                  onValueChange={(value) => {
                    setSelectedScript(value);
                    handleScriptSelect(value);
                  }}
                  disabled={courseScripts.length === 0 || fetchingData}
                >
                  <SelectTrigger id="script">
                    <SelectValue placeholder="Select a script" />
                  </SelectTrigger>
                  <SelectContent>
                    {courseScripts.length === 0 ? (
                      <SelectItem value="no-scripts" disabled>
                        No scripts available
                      </SelectItem>
                    ) : (
                      courseScripts.map((script) => (
                        <SelectItem key={script.id} value={script.id.toString()}>
                          {script.title}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 pt-4">
                <Label htmlFor="voice">Voice</Label>
                {useOpenAI ? (
                  <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                    <SelectTrigger id="voice">
                      <SelectValue placeholder="Select OpenAI voice" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="alloy">Alloy (Neutral)</SelectItem>
                      <SelectItem value="echo">Echo (Male)</SelectItem>
                      <SelectItem value="fable">Fable (Male)</SelectItem>
                      <SelectItem value="onyx">Onyx (Male)</SelectItem>
                      <SelectItem value="nova">Nova (Female)</SelectItem>
                      <SelectItem value="shimmer">Shimmer (Female)</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div className="space-y-2">
                        <Label htmlFor="script-provider">Voice Provider</Label>
                        <Select 
                          value={selectedProvider} 
                          onValueChange={(value) => {
                            setSelectedProvider(value);
                            // Update selected voice when provider changes
                            const providerVoices = voices.filter(v => v.source === value);
                            if (providerVoices.length > 0) {
                              setSelectedVoice(providerVoices[0].voice_id);
                            }
                          }}
                        >
                          <SelectTrigger id="script-provider">
                            <SelectValue placeholder="Select voice provider" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="coqui">Coqui TTS (Default)</SelectItem>
                            <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <Select
                      value={selectedVoice}
                      onValueChange={setSelectedVoice}
                      disabled={fetchingData}
                    >
                      <SelectTrigger id="voice">
                        <SelectValue placeholder="Select a voice" />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredVoices
                          .filter((voice) => voice.source === selectedProvider)
                          .map((voice) => (
                          <SelectItem key={voice.voice_id} value={voice.voice_id}>
                            <div className="flex items-center justify-between w-full">
                              <span>
                                {voice.name}
                                <span className="text-xs text-muted-foreground ml-2">
                                  {voice.gender === "male" ? "♂" : voice.gender === "female" ? "♀" : ""}
                                  {voice.accent && ` - ${voice.accent.charAt(0).toUpperCase() + voice.accent.slice(1)}`}
                                </span>
                              </span>
                              {voice.preview_url && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 ml-2"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    playPreviewAudio(voice.preview_url);
                                  }}
                                >
                                  <Play className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </>
                )}
              </div>

              <div className="space-y-2 pt-4">
                <Label htmlFor="script-text">Script Content</Label>
                <div className="mb-2">
                  <Textarea
                    id="script-text"
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    className="min-h-[120px]"
                    disabled={courseScripts.length === 0}
                  />
                </div>
                
                {text.trim().length > 0 && (
                  <div className="border-t pt-4 mt-4">
                    <div className="mb-2">
                      <h4 className="text-sm font-medium">Optimize for Voice Generation</h4>
                      <p className="text-xs text-slate-500 mb-2">
                        Extract only the text that needs to be spoken by the AI voice.
                      </p>
                    </div>
                    
                    {/* Extract Speech Text Component */}
                    <ExtractSpeechText 
                      rawText={text} 
                      onExtractedTextChange={(extractedText: string) => setText(extractedText)} 
                    />
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                onClick={handleGenerate}
                disabled={loading || !text || courseScripts.length === 0}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
                  </>
                ) : (
                  "Generate Speech"
                )}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={handleSaveToLibrary} 
                disabled={!audioUrl}
                className="ml-2"
              >
                <Save className="mr-2 h-4 w-4" /> Save to Library
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIVoiceGenerator;