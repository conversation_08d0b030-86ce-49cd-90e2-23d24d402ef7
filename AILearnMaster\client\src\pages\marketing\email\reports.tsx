import React, { useState } from 'react';
import EmailLayout from './layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import {
  CalendarIcon,
  Download,
  ExternalLink,
  Filter,
  LineChart,
  Mail,
  MailCheck,
  Users
} from 'lucide-react';
import { useMediaQuery } from '@/hooks/use-media-query';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  Line,
  LineChart as RechartsLineChart,
  Area,
  AreaChart,
} from 'recharts';
import { DateRange } from "react-day-picker";
import { addDays, format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const EmailReportsPage = () => {
  const [date, setDate] = React.useState<DateRange | undefined>({
    from: addDays(new Date(), -30),
    to: new Date(),
  });
  const [campaignFilter, setCampaignFilter] = useState<string | null>(null);
  const [reportTab, setReportTab] = useState('overview');
  const { isMobile } = useMediaQuery();

  // Fetch campaign statistics data
  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ['/api/email-reports/stats', date, campaignFilter],
    queryFn: async () => {
      // This would be an actual API call in production
      // const res = await apiRequest('GET', '/api/email-reports/stats');
      // return res.json();
      
      // Sample data for demonstration
      return {
        totalSent: 12580,
        totalOpened: 4832,
        totalClicked: 1510,
        openRate: 38.4,
        clickRate: 12.0,
        clickToOpenRate: 31.2,
        bounceRate: 1.2,
        unsubscribeRate: 0.3,
        topCampaigns: [
          { name: 'May 2025 Newsletter', openRate: 42.1, clickRate: 15.3 },
          { name: 'Summer Sale Promotion', openRate: 38.7, clickRate: 16.2 },
          { name: 'Product Launch Announcement', openRate: 45.2, clickRate: 22.1 },
        ]
      };
    },
  });

  // Fetch campaigns for dropdown
  const { data: campaignsData } = useQuery({
    queryKey: ['/api/email-campaigns', 'all'],
    queryFn: async () => {
      // This would be an actual API call in production
      // const res = await apiRequest('GET', '/api/email-campaigns?limit=100');
      // return res.json();
      
      // Sample data for demonstration
      return {
        data: [
          { id: '1', name: 'May 2025 Newsletter', status: 'sent' },
          { id: '2', name: 'Summer Sale Promotion', status: 'sent' },
          { id: '3', name: 'Product Launch Announcement', status: 'sent' },
          { id: '4', name: 'Customer Feedback Survey', status: 'sent' },
        ]
      };
    },
  });

  // Sample data for charts
  const openRateData = [
    { date: 'May 1', openRate: 35.2 },
    { date: 'May 2', openRate: 38.1 },
    { date: 'May 3', openRate: 41.5 },
    { date: 'May 4', openRate: 37.8 },
    { date: 'May 5', openRate: 42.3 },
    { date: 'May 6', openRate: 39.9 },
    { date: 'May 7', openRate: 43.2 },
  ];

  const clickRateData = [
    { date: 'May 1', clickRate: 10.5 },
    { date: 'May 2', clickRate: 11.2 },
    { date: 'May 3', clickRate: 14.3 },
    { date: 'May 4', clickRate: 12.1 },
    { date: 'May 5', clickRate: 13.8 },
    { date: 'May 6', clickRate: 12.5 },
    { date: 'May 7', clickRate: 15.1 },
  ];

  const engagementData = [
    { name: 'Opened', value: 38.4 },
    { name: 'Clicked', value: 12.0 },
    { name: 'No Interaction', value: 49.6 },
  ];

  const deviceData = [
    { name: 'Mobile', value: 62 },
    { name: 'Desktop', value: 32 },
    { name: 'Tablet', value: 6 },
  ];

  const campaignPerformanceData = [
    { name: 'May Newsletter', opened: 42.1, clicked: 15.3 },
    { name: 'Summer Sale', opened: 38.7, clicked: 16.2 },
    { name: 'Product Launch', opened: 45.2, clicked: 22.1 },
    { name: 'Survey', opened: 35.6, clicked: 18.9 },
  ];

  const timeOfDayData = [
    { time: '12am-4am', opens: 120 },
    { time: '4am-8am', opens: 450 },
    { time: '8am-12pm', opens: 1250 },
    { time: '12pm-4pm', opens: 950 },
    { time: '4pm-8pm', opens: 1580 },
    { time: '8pm-12am', opens: 750 },
  ];

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A569BD'];

  // Render key statistics cards
  const renderStatCards = () => {
    if (statsLoading) {
      return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="pb-2">
                <Skeleton className="h-5 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    if (!statsData) return null;

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Open Rate</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{statsData.openRate}%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Click Rate</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{statsData.clickRate}%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Click-to-Open Rate</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{statsData.clickToOpenRate}%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Bounce Rate</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{statsData.bounceRate}%</p>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <EmailLayout>
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Email Reports</h2>
          <p className="text-muted-foreground">Analyze and track your email marketing performance</p>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          {/* Date Range Picker */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="justify-start text-left font-normal">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {date?.from ? (
                  date.to ? (
                    <>
                      {format(date.from, "LLL dd, y")} - {format(date.to, "LLL dd, y")}
                    </>
                  ) : (
                    format(date.from, "LLL dd, y")
                  )
                ) : (
                  <span>Pick a date range</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onSelect={setDate}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>

          {/* Campaign Filter */}
          <Select 
            value={campaignFilter || "all"} 
            onValueChange={(value) => setCampaignFilter(value === "all" ? null : value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Campaigns" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Campaigns</SelectItem>
              {campaignsData?.data.map((campaign: any) => (
                <SelectItem key={campaign.id} value={campaign.id}>
                  {campaign.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Export Report Button */}
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Statistics */}
      {renderStatCards()}

      {/* Report Tabs */}
      <Tabs 
        defaultValue="overview" 
        value={reportTab} 
        onValueChange={setReportTab}
        className="mt-6"
      >
        <TabsList className="grid grid-cols-2 md:grid-cols-4 mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="subscribers">Subscribers</TabsTrigger>
          <TabsTrigger value="links">Link Activity</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Open Rate & Click Rate Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Open Rate Trend</CardTitle>
                <CardDescription>Email open rates over the selected period</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={openRateData}>
                      <defs>
                        <linearGradient id="openRateGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#0088FE" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#0088FE" stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={[0, 50]} tickFormatter={(value) => `${value}%`} />
                      <Tooltip formatter={(value) => [`${value}%`, 'Open Rate']} />
                      <Area 
                        type="monotone" 
                        dataKey="openRate" 
                        stroke="#0088FE" 
                        fillOpacity={1} 
                        fill="url(#openRateGradient)" 
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Click Rate Trend</CardTitle>
                <CardDescription>Email click rates over the selected period</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={clickRateData}>
                      <defs>
                        <linearGradient id="clickRateGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#00C49F" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#00C49F" stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={[0, 25]} tickFormatter={(value) => `${value}%`} />
                      <Tooltip formatter={(value) => [`${value}%`, 'Click Rate']} />
                      <Area 
                        type="monotone" 
                        dataKey="clickRate" 
                        stroke="#00C49F" 
                        fillOpacity={1} 
                        fill="url(#clickRateGradient)" 
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Engagement & Device Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Engagement Breakdown</CardTitle>
                <CardDescription>Distribution of subscriber engagement</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={engagementData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {engagementData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}%`, '']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Device Distribution</CardTitle>
                <CardDescription>Email opens by device type</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={deviceData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {deviceData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}%`, '']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Time of Day & Top Campaigns */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Time of Day Analysis</CardTitle>
                <CardDescription>Email opens by time of day</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={timeOfDayData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="opens" fill="#8884d8" name="Opens" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Top Performing Campaigns</CardTitle>
                <CardDescription>Campaigns with highest engagement</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={campaignPerformanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis tickFormatter={(value) => `${value}%`} />
                      <Tooltip formatter={(value) => [`${value}%`, '']} />
                      <Legend />
                      <Bar dataKey="opened" fill="#0088FE" name="Open Rate" />
                      <Bar dataKey="clicked" fill="#00C49F" name="Click Rate" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Campaigns Tab */}
        <TabsContent value="campaigns">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Campaign Performance</CardTitle>
              <CardDescription>Compare performance across all campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Campaign Name</TableHead>
                    <TableHead>Sent</TableHead>
                    <TableHead>Open Rate</TableHead>
                    <TableHead>Click Rate</TableHead>
                    <TableHead>Bounce Rate</TableHead>
                    <TableHead>Sent Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">May 2025 Newsletter</TableCell>
                    <TableCell>3,450</TableCell>
                    <TableCell>42.1%</TableCell>
                    <TableCell>15.3%</TableCell>
                    <TableCell>0.8%</TableCell>
                    <TableCell>May. 1, 2025</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Summer Sale Promotion</TableCell>
                    <TableCell>5,320</TableCell>
                    <TableCell>38.7%</TableCell>
                    <TableCell>16.2%</TableCell>
                    <TableCell>1.1%</TableCell>
                    <TableCell>May. 5, 2025</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Product Launch Announcement</TableCell>
                    <TableCell>2,780</TableCell>
                    <TableCell>45.2%</TableCell>
                    <TableCell>22.1%</TableCell>
                    <TableCell>0.9%</TableCell>
                    <TableCell>Apr. 28, 2025</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">Customer Feedback Survey</TableCell>
                    <TableCell>1,030</TableCell>
                    <TableCell>35.6%</TableCell>
                    <TableCell>18.9%</TableCell>
                    <TableCell>1.2%</TableCell>
                    <TableCell>Apr. 20, 2025</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Subscribers Tab */}
        <TabsContent value="subscribers">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Subscriber Growth</CardTitle>
              <CardDescription>Subscriber list growth over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsLineChart
                    data={[
                      { date: 'Jan', subscribers: 8520 },
                      { date: 'Feb', subscribers: 9350 },
                      { date: 'Mar', subscribers: 10120 },
                      { date: 'Apr', subscribers: 11450 },
                      { date: 'May', subscribers: 12580 },
                    ]}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="subscribers" 
                      stroke="#8884d8" 
                      activeDot={{ r: 8 }} 
                      name="Subscribers" 
                    />
                  </RechartsLineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="text-lg">Subscriber Activity</CardTitle>
              <CardDescription>Engagement levels of your subscriber base</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <h3 className="text-lg font-medium">Active</h3>
                  <p className="text-3xl font-bold mt-2">68%</p>
                  <p className="text-muted-foreground mt-1">Opened or clicked in last 30 days</p>
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-medium">At Risk</h3>
                  <p className="text-3xl font-bold mt-2">24%</p>
                  <p className="text-muted-foreground mt-1">No activity in 30-90 days</p>
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-medium">Inactive</h3>
                  <p className="text-3xl font-bold mt-2">8%</p>
                  <p className="text-muted-foreground mt-1">No activity in 90+ days</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Link Activity Tab */}
        <TabsContent value="links">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Link Click Analysis</CardTitle>
              <CardDescription>Performance of links across all campaigns</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Link URL</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Campaign</TableHead>
                    <TableHead>Clicks</TableHead>
                    <TableHead>Click Rate</TableHead>
                    <TableHead>Unique Clicks</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        <span className="text-blue-500">product-page.example.com</span>
                      </div>
                    </TableCell>
                    <TableCell>Product Details</TableCell>
                    <TableCell>Summer Sale Promotion</TableCell>
                    <TableCell>685</TableCell>
                    <TableCell>12.9%</TableCell>
                    <TableCell>542</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        <span className="text-blue-500">special-offer.example.com</span>
                      </div>
                    </TableCell>
                    <TableCell>Discount Code</TableCell>
                    <TableCell>Summer Sale Promotion</TableCell>
                    <TableCell>412</TableCell>
                    <TableCell>7.7%</TableCell>
                    <TableCell>398</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        <span className="text-blue-500">blog.example.com/new-feature</span>
                      </div>
                    </TableCell>
                    <TableCell>Feature Announcement</TableCell>
                    <TableCell>Product Launch Announcement</TableCell>
                    <TableCell>365</TableCell>
                    <TableCell>13.1%</TableCell>
                    <TableCell>328</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">
                      <div className="flex items-center">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        <span className="text-blue-500">survey.example.com/feedback</span>
                      </div>
                    </TableCell>
                    <TableCell>Feedback Form</TableCell>
                    <TableCell>Customer Feedback Survey</TableCell>
                    <TableCell>195</TableCell>
                    <TableCell>18.9%</TableCell>
                    <TableCell>195</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </EmailLayout>
  );
};

export default EmailReportsPage;