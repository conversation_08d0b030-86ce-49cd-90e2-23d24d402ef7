import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  FileText, 
  Wand2, 
  Play, 
  Pause, 
  ArrowRight, 
  ArrowLeft, 
  Loader2, 
  Volume2,
  Clock,
  Edit,
  Plus,
  Trash2,
  CheckCircle
} from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

interface AvatarScriptCreatorProps {
  onSubmit: (scriptData: any) => void;
  onBack: () => void;
  courseData: any;
  avatarData: any;
  defaultValues?: any;
}

interface ScriptSegment {
  id: string;
  title: string;
  content: string;
  duration: number;
  order: number;
}

export function AvatarScriptCreator({ 
  onSubmit, 
  onBack, 
  courseData, 
  avatarData,
  defaultValues 
}: AvatarScriptCreatorProps) {
  const [scriptSegments, setScriptSegments] = useState<ScriptSegment[]>(
    defaultValues?.segments || []
  );
  const [currentSegment, setCurrentSegment] = useState<number>(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [editingSegment, setEditingSegment] = useState<string | null>(null);
  const { toast } = useToast();

  // AI Script Generation Mutation
  const generateScriptMutation = useMutation({
    mutationFn: async ({ courseData, avatarData }: { courseData: any, avatarData: any }) => {
      const response = await fetch('/api/ai/generate-avatar-script', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: courseData.title,
          description: courseData.description,
          category: courseData.category,
          targetAudience: courseData.targetAudience,
          avatarType: avatarData.avatarType,
          avatarCharacteristics: avatarData.characteristics
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate script');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      setScriptSegments(data.segments || []);
      setGenerationProgress(100);
      toast({
        title: "Script Generated Successfully",
        description: `Generated ${data.segments?.length || 0} script segments optimized for TTS`
      });
    },
    onError: (error: any) => {
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate script",
        variant: "destructive"
      });
      setGenerationProgress(0);
    }
  });

  const handleGenerateScript = () => {
    if (!courseData.title || !courseData.description) {
      toast({
        title: "Missing Information",
        description: "Course title and description are required for script generation",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(20);
    generateScriptMutation.mutate({ courseData, avatarData });
  };

  const addNewSegment = () => {
    const newSegment: ScriptSegment = {
      id: `segment-${Date.now()}`,
      title: `Segment ${scriptSegments.length + 1}`,
      content: '',
      duration: 0,
      order: scriptSegments.length
    };
    setScriptSegments([...scriptSegments, newSegment]);
    setEditingSegment(newSegment.id);
  };

  const updateSegment = (id: string, updates: Partial<ScriptSegment>) => {
    setScriptSegments(prev => 
      prev.map(segment => 
        segment.id === id ? { ...segment, ...updates } : segment
      )
    );
  };

  const deleteSegment = (id: string) => {
    setScriptSegments(prev => prev.filter(segment => segment.id !== id));
  };

  const estimateReadingTime = (text: string): number => {
    // Average reading speed: 150-160 words per minute for TTS
    const words = text.split(' ').length;
    return Math.ceil((words / 150) * 60); // Return in seconds
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getTotalDuration = (): number => {
    return scriptSegments.reduce((total, segment) => {
      const estimatedDuration = estimateReadingTime(segment.content);
      return total + estimatedDuration;
    }, 0);
  };

  const handleSubmit = () => {
    if (scriptSegments.length === 0) {
      toast({
        title: "No Script Content",
        description: "Please generate or add script segments before continuing",
        variant: "destructive"
      });
      return;
    }

    const scriptData = {
      segments: scriptSegments,
      totalDuration: getTotalDuration(),
      wordCount: scriptSegments.reduce((total, segment) => 
        total + segment.content.split(' ').length, 0
      ),
      optimizedForTTS: true,
      avatarOptimized: true
    };

    onSubmit(scriptData);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Create Your Course Script</h2>
        <p className="text-muted-foreground">
          Generate AI-powered scripts optimized for your avatar and text-to-speech
        </p>
      </div>

      {/* AI Generation Section */}
      <Card className="border-2 border-dashed border-primary/20 bg-gradient-to-r from-primary/5 to-purple/5">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-primary" />
            AI Script Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline">Course</Badge>
                <span className="font-medium">{courseData.title || 'Untitled Course'}</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Category</Badge>
                <span>{courseData.category || 'General'}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline">Avatar</Badge>
                <span>{avatarData.avatarType === 'generate' ? 'AI Generated' : 'From Photo'}</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline">Format</Badge>
                <span>TTS Optimized</span>
              </div>
            </div>
          </div>

          {generationProgress > 0 && generationProgress < 100 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Generating Script...</Label>
                <span className="text-sm text-muted-foreground">{generationProgress}%</span>
              </div>
              <Progress value={generationProgress} className="w-full" />
            </div>
          )}

          <Button 
            onClick={handleGenerateScript}
            disabled={generateScriptMutation.isPending || !courseData.title}
            className="w-full gap-2"
          >
            {generateScriptMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Wand2 className="h-4 w-4" />
            )}
            Generate AI Script
          </Button>
        </CardContent>
      </Card>

      {/* Script Overview */}
      {scriptSegments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Script Overview
              </span>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {formatDuration(getTotalDuration())}
                </div>
                <div className="flex items-center gap-1">
                  <FileText className="h-4 w-4" />
                  {scriptSegments.reduce((total, segment) => 
                    total + segment.content.split(' ').length, 0
                  )} words
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4">
              {scriptSegments.map((segment, index) => (
                <Card key={segment.id} className="border-l-4 border-l-primary">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{index + 1}</Badge>
                        {editingSegment === segment.id ? (
                          <Input
                            value={segment.title}
                            onChange={(e) => updateSegment(segment.id, { title: e.target.value })}
                            className="font-medium"
                            onBlur={() => setEditingSegment(null)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') setEditingSegment(null);
                            }}
                          />
                        ) : (
                          <h4 
                            className="font-medium cursor-pointer hover:text-primary"
                            onClick={() => setEditingSegment(segment.id)}
                          >
                            {segment.title}
                          </h4>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {formatDuration(estimateReadingTime(segment.content))}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingSegment(
                            editingSegment === segment.id ? null : segment.id
                          )}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteSegment(segment.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <Textarea
                      value={segment.content}
                      onChange={(e) => updateSegment(segment.id, { content: e.target.value })}
                      placeholder="Enter script content optimized for text-to-speech..."
                      className="min-h-[120px] resize-none"
                      disabled={editingSegment !== segment.id && editingSegment !== null}
                    />
                    <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                      <span>{segment.content.split(' ').length} words</span>
                      <span>Est. reading time: {formatDuration(estimateReadingTime(segment.content))}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Button
              variant="outline"
              onClick={addNewSegment}
              className="w-full gap-2 border-dashed"
            >
              <Plus className="h-4 w-4" />
              Add New Segment
            </Button>
          </CardContent>
        </Card>
      )}

      {/* TTS Optimization Tips */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Volume2 className="h-4 w-4 text-blue-600" />
            TTS Optimization Tips
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="text-sm text-blue-800 space-y-2">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
              <span>Use clear, conversational language that sounds natural when spoken</span>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
              <span>Avoid complex punctuation and write numbers as words</span>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
              <span>Keep sentences moderate length for better speech flow</span>
            </div>
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 mt-0.5 text-blue-600" />
              <span>Add pauses using periods or commas for natural pacing</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Avatar
        </Button>
        <Button 
          onClick={handleSubmit}
          disabled={scriptSegments.length === 0}
          className="gap-2"
        >
          Configure Voice Settings
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}