import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
import { CheckCircle2, Clock, Target, Zap } from 'lucide-react';

interface ProgressStep {
  id: string;
  title: string;
  status: 'pending' | 'active' | 'completed' | 'skipped';
  duration?: number;
}

interface AnimatedProgressTrackerProps {
  steps: ProgressStep[];
  currentStepIndex: number;
  overallProgress: number;
  timeElapsed?: number;
  estimatedTimeRemaining?: number;
  showCelebration?: boolean;
  compactMode?: boolean;
}

export default function AnimatedProgressTracker({
  steps,
  currentStepIndex,
  overallProgress,
  timeElapsed,
  estimatedTimeRemaining,
  showCelebration = true,
  compactMode = false
}: AnimatedProgressTrackerProps) {
  const [animatedProgress, setAnimatedProgress] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(overallProgress);
    }, 100);
    return () => clearTimeout(timer);
  }, [overallProgress]);

  useEffect(() => {
    const newCompletedSteps = steps
      .filter(step => step.status === 'completed')
      .map(step => step.id);
    
    setCompletedSteps(newCompletedSteps);
  }, [steps]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (compactMode) {
    return (
      <motion.div
        className="flex items-center gap-4 p-3 bg-white/80 backdrop-blur-sm rounded-lg shadow-sm border"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Step {currentStepIndex + 1} of {steps.length}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(overallProgress)}%
            </span>
          </div>
          <Progress 
            value={animatedProgress} 
            className="h-2"
          />
        </div>
        
        {timeElapsed && (
          <div className="flex items-center gap-1 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            {formatTime(timeElapsed)}
          </div>
        )}
      </motion.div>
    );
  }

  return (
    <Card className="p-6 space-y-6">
      {/* Header with overall progress */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Target className="w-5 h-5 text-blue-500" />
            Learning Progress
          </h3>
          <motion.div
            className="text-2xl font-bold text-blue-600"
            key={Math.round(overallProgress)}
            initial={{ scale: 1.2, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {Math.round(overallProgress)}%
          </motion.div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm text-gray-600">
            <span>Step {currentStepIndex + 1} of {steps.length}</span>
            <span>{completedSteps.length} completed</span>
          </div>
          
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{ duration: 0.5 }}
          >
            <Progress 
              value={animatedProgress} 
              className="h-3 transition-all duration-1000 ease-out"
            />
          </motion.div>
        </div>
      </div>

      {/* Time tracking */}
      {(timeElapsed || estimatedTimeRemaining) && (
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          {timeElapsed && (
            <div className="flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4 text-gray-500" />
              <span className="text-gray-600">Time elapsed:</span>
              <motion.span 
                className="font-mono font-medium"
                key={timeElapsed}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                {formatTime(timeElapsed)}
              </motion.span>
            </div>
          )}
          
          {estimatedTimeRemaining && (
            <div className="flex items-center gap-2 text-sm">
              <Zap className="w-4 h-4 text-green-500" />
              <span className="text-gray-600">Est. remaining:</span>
              <span className="font-mono font-medium text-green-600">
                {formatTime(estimatedTimeRemaining)}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Step indicators */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700">Steps</h4>
        <div className="space-y-2">
          {steps.map((step, index) => (
            <motion.div
              key={step.id}
              className={`
                flex items-center gap-3 p-3 rounded-lg transition-all duration-300
                ${step.status === 'active' ? 'bg-blue-50 border border-blue-200' : ''}
                ${step.status === 'completed' ? 'bg-green-50 border border-green-200' : ''}
                ${step.status === 'pending' ? 'bg-gray-50' : ''}
              `}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {/* Step icon */}
              <motion.div
                className={`
                  w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium
                  ${step.status === 'completed' ? 'bg-green-500 text-white' : ''}
                  ${step.status === 'active' ? 'bg-blue-500 text-white' : ''}
                  ${step.status === 'pending' ? 'bg-gray-300 text-gray-600' : ''}
                `}
                animate={step.status === 'active' ? { scale: [1, 1.1, 1] } : { scale: 1 }}
                transition={{ duration: 0.5, repeat: step.status === 'active' ? Infinity : 0 }}
              >
                {step.status === 'completed' ? (
                  <CheckCircle2 className="w-4 h-4" />
                ) : (
                  index + 1
                )}
              </motion.div>

              {/* Step content */}
              <div className="flex-1">
                <span className={`
                  text-sm font-medium
                  ${step.status === 'completed' ? 'text-green-700' : ''}
                  ${step.status === 'active' ? 'text-blue-700' : ''}
                  ${step.status === 'pending' ? 'text-gray-600' : ''}
                `}>
                  {step.title}
                </span>
                
                {step.status === 'active' && (
                  <motion.div
                    className="w-2 h-2 bg-blue-500 rounded-full mt-1"
                    animate={{ opacity: [1, 0.3, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                )}
              </div>

              {/* Step status indicator */}
              <AnimatePresence>
                {step.status === 'completed' && showCelebration && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    className="text-green-500"
                  >
                    <CheckCircle2 className="w-5 h-5" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Completion celebration */}
      <AnimatePresence>
        {overallProgress >= 100 && showCelebration && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className="p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200 text-center"
          >
            <motion.div
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 0.5, repeat: 2 }}
              className="text-2xl mb-2"
            >
              🎉
            </motion.div>
            <p className="text-green-700 font-medium">
              Congratulations! You've completed all steps!
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}