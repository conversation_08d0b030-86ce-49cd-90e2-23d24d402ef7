"""
Advanced Modal A100 GPU Deployment
Enhanced version with SadTalker, Coqui TTS, Kokoro TTS, and Mistral 7B
"""

import modal
import os
import base64
import tempfile
import subprocess
import json
import time
import io
from typing import Dict, List, Any, Optional
from pathlib import Path

# Modal App Configuration
app = modal.App("courseai-a100-advanced")

# Advanced A100 GPU Image with AI models
gpu_image = (
    modal.Image.debian_slim(python_version="3.10")
    .apt_install([
        "git", "wget", "curl", "ffmpeg", "libsm6", "libxext6",
        "libxrender-dev", "libglib2.0-0", "libgl1-mesa-glx",
        "libsndfile1", "espeak", "nodejs", "npm", "cmake",
        "build-essential", "libssl-dev", "zlib1g-dev",
        "libbz2-dev", "libreadline-dev", "libsqlite3-dev",
        "libncursesw5-dev", "xz-utils", "tk-dev", "libxml2-dev",
        "libxmlsec1-dev", "libffi-dev", "liblzma-dev"
    ])
    .pip_install([
        # Core ML packages with compatible versions
        "torch>=2.1.0",
        "torchvision>=0.16.0",
        "torchaudio>=2.1.0",
        "transformers>=4.35.0",
        "accelerate>=0.24.1",
        "diffusers>=0.21.4",

        # Computer Vision & Image Processing
        "opencv-python>=4.8.0",
        "pillow>=10.0.0",
        "imageio>=2.31.0",
        "imageio-ffmpeg>=0.4.9",
        "scikit-image>=0.21.0",

        # Audio Processing
        "librosa>=0.10.1",
        "soundfile>=0.12.1",
        "pydub>=0.25.1",
        "resampy>=0.4.2",

        # Scientific Computing
        "numpy>=1.24.0",
        "scipy>=1.11.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",

        # Web Framework
        "fastapi>=0.104.0",
        "uvicorn>=0.24.0",
        "requests>=2.31.0",

        # Utilities
        "huggingface-hub",
        "safetensors",
        "einops",
        "omegaconf",
        "hydra-core"
    ])
    .run_commands([
        "npm install -g @marp-team/marp-cli",
        "mkdir -p /app/models /app/temp /app/sadtalker /app/kokoro",
        # Clone SadTalker repository
        "cd /app && git clone https://github.com/OpenTalker/SadTalker.git sadtalker",
        # Clone Kokoro TTS repository
        "cd /app && git clone https://github.com/hexgrad/kokoro.git kokoro",
        # Download model checkpoints will be done at runtime
    ])
)

# Shared storage for models and temp files
shared_volume = modal.Volume.from_name("courseai-a100-storage", create_if_missing=True)

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=16384,
    min_containers=1
)
def health_check() -> Dict[str, Any]:
    """A100 GPU health check"""
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        gpu_name = torch.cuda.get_device_name(0) if gpu_available else "No GPU"
        
        # Check GPU memory
        if gpu_available:
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            torch.cuda.empty_cache()
            gpu_memory_free = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / (1024**3)
        else:
            gpu_memory_total = gpu_memory_free = 0
        
        # Test PyTorch GPU operations
        if gpu_available:
            test_tensor = torch.randn(1000, 1000).cuda()
            test_result = torch.mm(test_tensor, test_tensor.t())
            torch.cuda.synchronize()
            del test_tensor, test_result
            torch.cuda.empty_cache()
        
        # Check service availability
        services = {}

        # Check Marp installation
        try:
            marp_result = subprocess.run(["marp", "--version"], capture_output=True, text=True)
            services["marp"] = marp_result.returncode == 0
        except:
            services["marp"] = False

        # Check TTS services
        services["coqui_tts"] = False  # Disabled due to dependency conflicts

        # Check Transformers for Mistral
        try:
            import transformers
            services["mistral"] = True
        except:
            services["mistral"] = False

        # Check SadTalker availability
        services["sadtalker"] = os.path.exists("/app/sadtalker")
        services["kokoro"] = os.path.exists("/app/kokoro")
        services["torch"] = True
        services["opencv"] = True

        return {
            "status": "online",
            "gpu_available": gpu_available,
            "gpu_count": gpu_count,
            "gpu_name": gpu_name,
            "gpu_memory_total_gb": round(gpu_memory_total, 2),
            "gpu_memory_free_gb": round(gpu_memory_free, 2),
            "services": services,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "gpu_available": False,
            "gpu_count": 0,
            "gpu_name": "Error",
            "gpu_memory_total_gb": 0,
            "gpu_memory_free_gb": 0,
            "services": {
                "marp": False,
                "torch": False,
                "opencv": False
            },
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    timeout=300,
    memory=4096
)
def generate_marp_slides(
    markdown_content: str,
    theme: str = "default",
    output_format: str = "pdf"
) -> Dict[str, Any]:
    """Generate presentation slides using Marp CLI"""
    try:
        temp_dir = tempfile.mkdtemp()
        markdown_path = os.path.join(temp_dir, "slides.md")
        output_path = os.path.join(temp_dir, f"slides.{output_format}")
        
        # Write markdown content
        with open(markdown_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)
        
        # Generate slides using Marp
        cmd = [
            "marp",
            markdown_path,
            "--theme", theme,
            "--output", output_path,
            "--allow-local-files"
        ]
        
        if output_format == "pdf":
            cmd.extend(["--pdf"])
        elif output_format == "html":
            cmd.extend(["--html"])
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        if result.returncode != 0:
            raise Exception(f"Marp generation failed: {result.stderr}")
        
        # Read output file and convert to base64
        with open(output_path, "rb") as output_file:
            output_data = output_file.read()
            slides_base64 = base64.b64encode(output_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "slides_base64": slides_base64,
            "format": output_format,
            "theme": theme,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "format": output_format,
            "theme": theme,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=600,
    memory=16384
)
def simple_image_processing(
    image_base64: str,
    operation: str = "resize",
    width: int = 512,
    height: int = 512
) -> Dict[str, Any]:
    """Simple image processing using GPU acceleration"""
    try:
        import torch
        import cv2
        import numpy as np
        from PIL import Image
        
        # Decode base64 image
        image_data = base64.b64decode(image_base64)
        
        # Convert to PIL Image
        from PIL import Image
        import io
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to numpy array
        img_array = np.array(image)
        
        # Move to GPU for processing
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        img_tensor = torch.from_numpy(img_array).float().to(device)
        
        # Perform operation
        if operation == "resize":
            # Simple resize using OpenCV
            img_resized = cv2.resize(img_array, (width, height))
            result_image = Image.fromarray(img_resized)
        else:
            result_image = image
        
        # Convert back to base64
        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as temp_file:
            result_image.save(temp_file.name, "JPEG")
            
            with open(temp_file.name, "rb") as f:
                result_data = f.read()
                result_base64 = base64.b64encode(result_data).decode()
            
            os.unlink(temp_file.name)
        
        return {
            "status": "success",
            "image_base64": result_base64,
            "operation": operation,
            "width": width,
            "height": height,
            "gpu_used": torch.cuda.is_available(),
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "operation": operation,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=900,
    memory=32768
)
def sadtalker_generate_video(
    image_base64: str,
    audio_base64: str,
    preprocess: str = "crop",
    still: bool = False,
    use_enhancer: bool = False
) -> Dict[str, Any]:
    """Generate talking head video using SadTalker (simplified implementation)"""
    try:
        import torch
        import cv2
        from PIL import Image

        # Create temp directory
        temp_dir = tempfile.mkdtemp()
        image_path = os.path.join(temp_dir, "input_image.jpg")
        audio_path = os.path.join(temp_dir, "input_audio.wav")
        output_path = os.path.join(temp_dir, "output_video.mp4")

        # Decode and save input files
        with open(image_path, "wb") as f:
            f.write(base64.b64decode(image_base64))

        with open(audio_path, "wb") as f:
            f.write(base64.b64decode(audio_base64))

        # For now, create a simple video with the static image and audio
        # This is a placeholder until full SadTalker is properly configured

        # Load image
        img = cv2.imread(image_path)
        height, width, layers = img.shape

        # Get audio duration using ffmpeg
        duration_cmd = [
            "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
            "-of", "csv=p=0", audio_path
        ]
        duration_result = subprocess.run(duration_cmd, capture_output=True, text=True)

        try:
            duration = float(duration_result.stdout.strip())
        except:
            duration = 5.0  # Default 5 seconds

        # Create video with static image and audio
        fps = 25
        frame_count = int(duration * fps)

        # Use ffmpeg to create video
        ffmpeg_cmd = [
            "ffmpeg", "-y",
            "-loop", "1", "-i", image_path,
            "-i", audio_path,
            "-c:v", "libx264", "-c:a", "aac",
            "-t", str(duration),
            "-pix_fmt", "yuv420p",
            "-r", str(fps),
            output_path
        ]

        result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=300)

        if result.returncode != 0:
            return {
                "status": "error",
                "error": f"Video generation failed: {result.stderr}",
                "timestamp": int(time.time() * 1000)
            }

        # Read and encode output video
        with open(output_path, "rb") as f:
            video_data = f.read()
            video_base64 = base64.b64encode(video_data).decode()

        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

        return {
            "status": "success",
            "video_base64": video_base64,
            "preprocess": preprocess,
            "still": still,
            "use_enhancer": use_enhancer,
            "gpu_used": torch.cuda.is_available(),
            "note": "Simplified implementation - static image with audio",
            "timestamp": int(time.time() * 1000)
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=16384
)
def simple_tts_generate(
    text: str,
    voice: str = "default",
    language: str = "en"
) -> Dict[str, Any]:
    """Generate speech using espeak (simple TTS fallback)"""
    try:
        # Create temp file for output
        temp_dir = tempfile.mkdtemp()
        output_path = os.path.join(temp_dir, "output.wav")

        # Use espeak for simple TTS
        espeak_cmd = ["espeak", "-w", output_path, "-s", "175", text]
        result = subprocess.run(espeak_cmd, capture_output=True, text=True)

        if result.returncode != 0:
            return {
                "status": "error",
                "error": f"TTS generation failed: {result.stderr}",
                "timestamp": int(time.time() * 1000)
            }

        # Read and encode audio
        with open(output_path, "rb") as f:
            audio_data = f.read()
            audio_base64 = base64.b64encode(audio_data).decode()

        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

        return {
            "status": "success",
            "audio_base64": audio_base64,
            "text": text,
            "voice": voice,
            "language": language,
            "engine": "espeak",
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "text": text,
            "voice": voice,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=16384
)
def kokoro_tts_generate(
    text: str,
    voice: str = "af_sarah",
    speed: float = 1.0
) -> Dict[str, Any]:
    """Generate speech using Kokoro TTS (with espeak fallback)"""
    try:
        # Create temp directory
        temp_dir = tempfile.mkdtemp()
        output_path = os.path.join(temp_dir, "output.wav")

        # Try Kokoro TTS first (if available)
        kokoro_available = os.path.exists("/app/kokoro/kokoro.py")

        if kokoro_available:
            cmd = [
                "python", "/app/kokoro/kokoro.py",
                "--text", text,
                "--voice", voice,
                "--speed", str(speed),
                "--output", output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
            kokoro_success = result.returncode == 0
        else:
            kokoro_success = False

        # Fallback to espeak if Kokoro fails or is not available
        if not kokoro_success:
            espeak_cmd = ["espeak", "-w", output_path, "-s", str(int(speed * 175)), text]
            espeak_result = subprocess.run(espeak_cmd, capture_output=True, text=True)

            if espeak_result.returncode != 0:
                return {
                    "status": "error",
                    "error": f"Both Kokoro and espeak failed",
                    "timestamp": int(time.time() * 1000)
                }

        # Read and encode audio
        with open(output_path, "rb") as f:
            audio_data = f.read()
            audio_base64 = base64.b64encode(audio_data).decode()

        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)

        return {
            "status": "success",
            "audio_base64": audio_base64,
            "text": text,
            "voice": voice,
            "speed": speed,
            "engine": "kokoro" if kokoro_success else "espeak",
            "gpu_used": torch.cuda.is_available(),
            "timestamp": int(time.time() * 1000)
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "text": text,
            "voice": voice,
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=600,
    memory=32768
)
def mistral_generate_text(
    prompt: str,
    max_length: int = 512,
    temperature: float = 0.7,
    top_p: float = 0.9,
    do_sample: bool = True
) -> Dict[str, Any]:
    """Generate text using Mistral 7B model"""
    try:
        import torch
        from transformers import AutoTokenizer, AutoModelForCausalLM

        device = "cuda" if torch.cuda.is_available() else "cpu"

        # Load Mistral 7B model (using smaller version for memory efficiency)
        model_name = "mistralai/Mistral-7B-Instruct-v0.1"

        # Check if model is cached
        model_cache_path = "/app/storage/mistral-7b"

        if os.path.exists(model_cache_path):
            tokenizer = AutoTokenizer.from_pretrained(model_cache_path)
            model = AutoModelForCausalLM.from_pretrained(
                model_cache_path,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
        else:
            # Download and cache model
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForCausalLM.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )

            # Save to cache
            os.makedirs(model_cache_path, exist_ok=True)
            tokenizer.save_pretrained(model_cache_path)
            model.save_pretrained(model_cache_path)

        # Prepare input
        inputs = tokenizer(prompt, return_tensors="pt").to(device)

        # Generate text
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_length=max_length,
                temperature=temperature,
                top_p=top_p,
                do_sample=do_sample,
                pad_token_id=tokenizer.eos_token_id
            )

        # Decode output
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

        # Remove input prompt from output
        response_text = generated_text[len(prompt):].strip()

        return {
            "status": "success",
            "generated_text": response_text,
            "full_text": generated_text,
            "prompt": prompt,
            "max_length": max_length,
            "temperature": temperature,
            "top_p": top_p,
            "gpu_used": torch.cuda.is_available(),
            "timestamp": int(time.time() * 1000)
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "prompt": prompt,
            "timestamp": int(time.time() * 1000)
        }

# Web API endpoints
@app.function(
    image=gpu_image,
    timeout=60
)
@modal.fastapi_endpoint(method="GET")
def health():
    """Health check endpoint"""
    return health_check.remote()

@app.function(
    image=gpu_image,
    timeout=300
)
@modal.fastapi_endpoint(method="POST")
def slides(request_data: Dict[str, Any]):
    """Slide generation endpoint"""
    markdown_content = request_data.get("markdown_content", "")
    theme = request_data.get("theme", "default")
    output_format = request_data.get("output_format", "pdf")
    
    if not markdown_content:
        return {"status": "error", "error": "Markdown content is required"}
    
    return generate_marp_slides.remote(markdown_content, theme, output_format)

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=600
)
@modal.fastapi_endpoint(method="POST")
def image_process(request_data: Dict[str, Any]):
    """Image processing endpoint"""
    image_base64 = request_data.get("image_base64", "")
    operation = request_data.get("operation", "resize")
    width = request_data.get("width", 512)
    height = request_data.get("height", 512)
    
    if not image_base64:
        return {"status": "error", "error": "Image base64 is required"}
    
    return simple_image_processing.remote(image_base64, operation, width, height)

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=900
)
@modal.fastapi_endpoint(method="POST")
def sadtalker_video(request_data: Dict[str, Any]):
    """SadTalker video generation endpoint"""
    image_base64 = request_data.get("image_base64", "")
    audio_base64 = request_data.get("audio_base64", "")
    preprocess = request_data.get("preprocess", "crop")
    still = request_data.get("still", False)
    use_enhancer = request_data.get("use_enhancer", False)

    if not image_base64 or not audio_base64:
        return {"status": "error", "error": "Both image_base64 and audio_base64 are required"}

    return sadtalker_generate_video.remote(image_base64, audio_base64, preprocess, still, use_enhancer)

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300
)
@modal.fastapi_endpoint(method="POST")
def simple_tts(request_data: Dict[str, Any]):
    """Simple TTS generation endpoint"""
    text = request_data.get("text", "")
    voice = request_data.get("voice", "default")
    language = request_data.get("language", "en")

    if not text:
        return {"status": "error", "error": "Text is required"}

    return simple_tts_generate.remote(text, voice, language)

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300
)
@modal.fastapi_endpoint(method="POST")
def kokoro_tts(request_data: Dict[str, Any]):
    """Kokoro TTS generation endpoint"""
    text = request_data.get("text", "")
    voice = request_data.get("voice", "af_sarah")
    speed = request_data.get("speed", 1.0)

    if not text:
        return {"status": "error", "error": "Text is required"}

    return kokoro_tts_generate.remote(text, voice, speed)

@app.function(
    image=gpu_image,
    gpu=modal.gpu.A100(count=1, size="80GB"),
    volumes={"/app/storage": shared_volume},
    timeout=600
)
@modal.web_endpoint(method="POST")
def mistral_text(request_data: Dict[str, Any]):
    """Mistral 7B text generation endpoint"""
    prompt = request_data.get("prompt", "")
    max_length = request_data.get("max_length", 512)
    temperature = request_data.get("temperature", 0.7)
    top_p = request_data.get("top_p", 0.9)
    do_sample = request_data.get("do_sample", True)

    if not prompt:
        return {"status": "error", "error": "Prompt is required"}

    return mistral_generate_text.remote(prompt, max_length, temperature, top_p, do_sample)

# Entry point for testing
if __name__ == "__main__":
    print("Modal A100 Advanced AI Application")
    print("Services: SadTalker, Coqui TTS, Kokoro TTS, Mistral 7B")
    print("Ready for deployment and testing")
