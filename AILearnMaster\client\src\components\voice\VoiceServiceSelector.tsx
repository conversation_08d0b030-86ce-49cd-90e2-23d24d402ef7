import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Play, Pause, Volume2, Zap, Crown, Cpu, Upload, Mic, Users, Settings, AlertCircle, Search, Filter, RefreshCw } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { voiceCacheService } from '@/services/voiceCacheService';

interface VoiceService {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  tier: 'standard' | 'premium' | 'enterprise';
  features: string[];
}

interface Voice {
  id: string;
  name: string;
  gender?: string;
  language?: string;
  accent?: string;
  description?: string;
  service?: string;
  quality?: 'high' | 'medium' | 'basic';
  style?: string;
  category?: string;
  dataset?: string;
  architecture?: string;
  tier?: 'free' | 'premium' | 'enterprise';
}

interface VoiceSettings {
  service: string;
  voiceId: string;
  speed: number;
  pitch: number;
  temperature?: number;
  stability?: number;
  isClonedVoice?: boolean;
  clonedVoiceName?: string;
}

interface VoiceServiceSelectorProps {
  onVoiceSelect: (settings: VoiceSettings) => void;
  sampleText?: string;
  defaultService?: string;
  defaultVoice?: string;
  showPreview?: boolean;
}

const voiceServices: VoiceService[] = [
  {
    id: 'coqui',
    name: 'Coqui TTS (Primary)',
    description: 'Open-source high-quality text-to-speech synthesis',
    icon: <Zap className="h-5 w-5" />,
    tier: 'standard',
    features: ['Open Source', 'GPU Acceleration', 'Multiple Languages', 'High Quality', 'Free']
  },
  {
    id: 'kokoro',
    name: 'Kokoro TTS (Fallback)',
    description: 'Lightweight open-source voice synthesis',
    icon: <Cpu className="h-5 w-5" />,
    tier: 'standard',
    features: ['Open Source', 'Fast Generation', 'Multiple Voices', 'Reliable', 'Free']
  },
  {
    id: 'elevenlabs',
    name: 'Premium Voice Synthesis',
    description: 'Ultra-realistic voice synthesis (Requires API Key)',
    icon: <Crown className="h-5 w-5" />,
    tier: 'premium',
    features: ['Emotional Range', 'Voice Cloning', 'Multilingual', 'Ultra Realistic']
  }
];

export function VoiceServiceSelector({
  onVoiceSelect,
  sampleText = "Welcome to this course. This is a preview of how your voice will sound.",
  defaultService = 'coqui',
  defaultVoice = '',
  showPreview = true
}: VoiceServiceSelectorProps) {
  const { toast } = useToast();
  const [selectedService, setSelectedService] = useState(defaultService);
  const [selectedVoice, setSelectedVoice] = useState(defaultVoice);
  const [speed, setSpeed] = useState(1.0);
  const [pitch, setPitch] = useState(1.0);
  const [temperature, setTemperature] = useState(0.7);
  const [stability, setStability] = useState(0.5);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playingVoice, setPlayingVoice] = useState<string | null>(null);
  
  // Voice cloning state
  const [showCloneDialog, setShowCloneDialog] = useState(false);
  const [cloneName, setCloneName] = useState('');
  const [cloneDescription, setCloneDescription] = useState('');
  const [cloneFile, setCloneFile] = useState<File | null>(null);
  const [isCloning, setIsCloning] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Voice filtering and search
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('all');
  const [selectedGender, setSelectedGender] = useState<string>('all');
  const [selectedQuality, setSelectedQuality] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Fetch voices for selected service with caching
  const { data: voices, isLoading: voicesLoading, refetch: refetchVoices } = useQuery({
    queryKey: ['/api/voices', selectedService],
    queryFn: async () => {
      // Try to get from cache first
      const cachedVoices = await voiceCacheService.getVoicesByService(
        selectedService === 'coqui' ? 'coqui_tts' :
        selectedService === 'kokoro' ? 'kokoro_tts' : selectedService
      );

      if (cachedVoices.length > 0) {
        return cachedVoices;
      }

      // Fetch from API if not in cache
      const response = await fetch('/api/tts/tts-models');
      if (!response.ok) throw new Error('Failed to fetch voices');
      const data = await response.json();

      // Cache the response
      if (data.voices && data.statistics) {
        await voiceCacheService.setVoices({
          voices: data.voices,
          statistics: data.statistics,
          lastUpdated: data.lastUpdated || new Date().toISOString(),
          version: '1.0'
        });
      }

      // Filter voices by selected service
      if (selectedService === 'coqui') {
        return data.voices?.filter((v: any) => v.service === 'coqui_tts') || [];
      } else if (selectedService === 'kokoro') {
        return data.voices?.filter((v: any) => v.service === 'kokoro_tts') || [];
      }

      return data.voices || [];
    },
    enabled: !!selectedService,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });

  // Filter voices based on search and filters
  const filteredVoices = useMemo(() => {
    if (!voices) return [];

    return voices.filter((voice: Voice) => {
      // Search term filter
      if (searchTerm && !voice.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !voice.description?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // Language filter
      if (selectedLanguage !== 'all' && voice.language !== selectedLanguage) {
        return false;
      }

      // Gender filter
      if (selectedGender !== 'all' && voice.gender !== selectedGender) {
        return false;
      }

      // Quality filter
      if (selectedQuality !== 'all' && voice.quality !== selectedQuality) {
        return false;
      }

      // Category filter
      if (selectedCategory !== 'all' && voice.category !== selectedCategory) {
        return false;
      }

      return true;
    });
  }, [voices, searchTerm, selectedLanguage, selectedGender, selectedQuality, selectedCategory]);

  // Get unique filter options
  const filterOptions = useMemo(() => {
    if (!voices) return { languages: [], genders: [], qualities: [], categories: [] };

    const languages = [...new Set(voices.map((v: Voice) => v.language).filter(Boolean))];
    const genders = [...new Set(voices.map((v: Voice) => v.gender).filter(Boolean))];
    const qualities = [...new Set(voices.map((v: Voice) => v.quality).filter(Boolean))];
    const categories = [...new Set(voices.map((v: Voice) => v.category).filter(Boolean))];

    return { languages, genders, qualities, categories };
  }, [voices]);

  // Set default voice when voices load
  useEffect(() => {
    if (filteredVoices && filteredVoices.length > 0 && !selectedVoice) {
      setSelectedVoice(filteredVoices[0].id);
    }
  }, [filteredVoices, selectedVoice]);

  // Update parent when settings change
  useEffect(() => {
    if (selectedService && selectedVoice) {
      onVoiceSelect({
        service: selectedService,
        voiceId: selectedVoice,
        speed,
        pitch,
        temperature,
        stability
      });
    }
  }, [selectedService, selectedVoice, speed, pitch, temperature, stability, onVoiceSelect]);

  const handlePreview = async (voiceId?: string) => {
    const targetVoice = voiceId || selectedVoice;
    if (!targetVoice) return;
    
    setPlayingVoice(targetVoice);
    
    try {
      // Use unified TTS generation endpoint for open-source services
      const endpoint = '/api/tts/generate';
      const requestBody = {
        text: sampleText,
        voiceId: targetVoice,
        language: 'en',
        speed,
        preferredEngine: selectedService === 'kokoro' ? 'kokoro' : 'coqui'
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate preview');
      }

      // Handle different response types
      if (selectedService === 'openai') {
        const data = await response.json();
        if (data.success && data.audioUrl) {
          const audio = new Audio(data.audioUrl);
          audio.play();
          audio.onended = () => setPlayingVoice(null);
        } else {
          throw new Error('Invalid response format');
        }
      } else {
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const audio = new Audio(url);
        audio.play();
        audio.onended = () => setPlayingVoice(null);
      }
      
    } catch (error) {
      console.error('Preview failed:', error);
      toast({
        title: "Preview Failed",
        description: "Unable to generate voice preview. Please try again.",
        variant: "destructive"
      });
      setPlayingVoice(null);
    }
  };

  const handleCloneVoice = async () => {
    if (!cloneFile || !cloneName) {
      toast({
        title: "Missing Information",
        description: "Please provide a voice sample file and name for cloning",
        variant: "destructive"
      });
      return;
    }

    setIsCloning(true);

    try {
      const formData = new FormData();
      formData.append('sampleAudio', cloneFile);
      formData.append('name', cloneName);
      formData.append('description', cloneDescription);
      formData.append('targetText', sampleText);

      const response = await fetch('/api/chatterbox-tts/clone-voice', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Voice cloning failed');
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const audio = new Audio(url);
      audio.play();

      toast({
        title: "Voice Cloned Successfully",
        description: `Created custom voice: ${cloneName}`,
      });

      // Reset form
      setCloneName('');
      setCloneDescription('');
      setCloneFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setShowCloneDialog(false);

    } catch (error) {
      console.error('Voice cloning failed:', error);
      toast({
        title: "Cloning Failed",
        description: "Unable to clone voice. Please try again with a different audio sample.",
        variant: "destructive"
      });
    } finally {
      setIsCloning(false);
    }
  };

  const getTierBadge = (tier: string) => {
    const colors = {
      standard: 'bg-gray-100 text-gray-800',
      premium: 'bg-blue-100 text-blue-800',
      enterprise: 'bg-purple-100 text-purple-800'
    };
    return <Badge className={colors[tier as keyof typeof colors]}>{tier}</Badge>;
  };

  const selectedServiceData = voiceServices.find(s => s.id === selectedService)!;

  return (
    <div className="space-y-6">
      {/* Service Selection */}
      <div>
        <h3 className="text-lg font-semibold">Choose Voice Service</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          {voiceServices.map((service) => (
            <Card 
              key={service.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedService === service.id 
                  ? 'ring-2 ring-blue-500 bg-blue-50' 
                  : ''
              }`}
              onClick={() => setSelectedService(service.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {service.icon}
                    <div>
                      <CardTitle className="text-base">{service.name}</CardTitle>
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    {service.id === 'chatterbox' && (
                      <Badge variant="secondary" className="text-xs">
                        Coming Soon
                      </Badge>
                    )}
                    {getTierBadge(service.tier)}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-sm">
                  {service.description}
                </CardDescription>
                <div className="mt-3">
                  <div className="flex flex-wrap gap-1">
                    {service.features.slice(0, 2).map((feature) => (
                      <Badge key={feature} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Voice Selection */}
      {selectedService && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">{selectedServiceData.name} Settings</h3>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium">Voice Selection</label>
                <div className="flex items-center gap-2 mt-2">
                  <div className="flex-1">
                    {selectedService === 'chatterbox' && (
                      <Badge variant="secondary" className="mb-2">
                        GPU-Accelerated
                      </Badge>
                    )}
                    <Button
                      variant="outline"
                      onClick={() => setShowCloneDialog(true)}
                      className="mb-2"
                      disabled={selectedService !== 'chatterbox'}
                    >
                      <Mic className="h-4 w-4" />
                      Clone Voice
                    </Button>
                  </div>
                </div>
                
                {selectedService === 'chatterbox' && (
                  <Badge variant="outline" className="mb-2">
                    Enterprise Feature
                  </Badge>
                )}
              </div>

              {selectedService === 'chatterbox' && (
                <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm font-medium text-yellow-800">
                      GPU Service Configuration Required
                    </span>
                  </div>
                  <p className="text-sm text-yellow-700">
                    This service requires Modal A100 GPU configuration. Please ensure your Modal credentials are properly configured to access enterprise voice synthesis features.
                  </p>
                </div>
              )}

              {/* Voice Cloning Dialog */}
              <Dialog open={showCloneDialog} onOpenChange={setShowCloneDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" disabled={selectedService !== 'chatterbox'}>
                    <Mic className="h-4 w-4" />
                    Clone Voice
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Clone Your Voice</DialogTitle>
                    <DialogDescription>
                      Upload an audio sample to create a custom voice clone.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="clone-name" className="text-right">
                        Voice Name
                      </Label>
                      <Input
                        id="clone-name"
                        value={cloneName}
                        onChange={(e) => setCloneName(e.target.value)}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="clone-description" className="text-right">
                        Description (optional)
                      </Label>
                      <Textarea
                        id="clone-description"
                        value={cloneDescription}
                        onChange={(e) => setCloneDescription(e.target.value)}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="clone-file" className="text-right">
                        Audio Sample
                      </Label>
                      <div className="col-span-3">
                        <Input
                          id="clone-file"
                          type="file"
                          ref={fileInputRef}
                          accept="audio/*"
                          onChange={(e) => setCloneFile(e.target.files?.[0] || null)}
                        />
                      </div>
                      <div className="col-span-4 text-center">
                        <p className="text-xs text-gray-500">
                          Upload a clear audio sample (10-30 seconds recommended)
                        </p>
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setShowCloneDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCloneVoice} disabled={isCloning || !cloneFile || !cloneName}>
                      {isCloning ? (
                        <>
                          <Settings className="h-4 w-4 mr-2 animate-spin" />
                          Cloning...
                        </>
                      ) : (
                        <>
                          <Mic className="h-4 w-4 mr-2" />
                          Clone Voice
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              {/* Voice Search and Filters */}
              {voices && voices.length > 0 && (
                <div className="space-y-4 mb-6">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search voices..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  {/* Filters */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <div>
                      <Label className="text-xs text-gray-500 mb-1 block">Language</Label>
                      <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Languages</SelectItem>
                          {filterOptions.languages.map(lang => (
                            <SelectItem key={lang} value={lang}>{lang.toUpperCase()}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-xs text-gray-500 mb-1 block">Gender</Label>
                      <Select value={selectedGender} onValueChange={setSelectedGender}>
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Genders</SelectItem>
                          {filterOptions.genders.map(gender => (
                            <SelectItem key={gender} value={gender}>{gender}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-xs text-gray-500 mb-1 block">Quality</Label>
                      <Select value={selectedQuality} onValueChange={setSelectedQuality}>
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Qualities</SelectItem>
                          {filterOptions.qualities.map(quality => (
                            <SelectItem key={quality} value={quality}>{quality}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-xs text-gray-500 mb-1 block">Category</Label>
                      <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          {filterOptions.categories.map(category => (
                            <SelectItem key={category} value={category}>
                              {category.replace('-', ' ')}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Results count and cache controls */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>
                      Showing {filteredVoices.length} of {voices.length} voices
                    </span>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => refetchVoices()}
                        className="h-6 px-2 text-xs"
                        title="Refresh voices from server"
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Refresh
                      </Button>
                      {(searchTerm || selectedLanguage !== 'all' || selectedGender !== 'all' ||
                        selectedQuality !== 'all' || selectedCategory !== 'all') && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSearchTerm('');
                            setSelectedLanguage('all');
                            setSelectedGender('all');
                            setSelectedQuality('all');
                            setSelectedCategory('all');
                          }}
                          className="h-6 px-2 text-xs"
                        >
                          Clear filters
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              )}

              <div>
                {voicesLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-sm text-gray-500 mt-2">Loading voices...</p>
                  </div>
                ) : filteredVoices.length === 0 && voices && voices.length > 0 ? (
                  <div className="text-center py-8">
                    <Filter className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500">No voices match your filters</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setSearchTerm('');
                        setSelectedLanguage('all');
                        setSelectedGender('all');
                        setSelectedQuality('all');
                        setSelectedCategory('all');
                      }}
                      className="mt-2"
                    >
                      Clear filters
                    </Button>
                  </div>
                ) : (
                  <div className="grid gap-3">
                    {filteredVoices?.map((voice: Voice) => (
                      <Card 
                        key={voice.id} 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedVoice === voice.id 
                            ? 'ring-2 ring-blue-500 bg-blue-50' 
                            : ''
                        }`}
                        onClick={() => setSelectedVoice(voice.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-gray-500" />
                                <div className="flex-1">
                                  <div className="flex items-center gap-2">
                                    <h4 className="font-medium">{voice.name}</h4>
                                    {voice.quality && (
                                      <Badge
                                        variant={voice.quality === 'high' ? 'default' : voice.quality === 'medium' ? 'secondary' : 'outline'}
                                        className="text-xs"
                                      >
                                        {voice.quality}
                                      </Badge>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-2 mt-1 flex-wrap">
                                    {voice.language && (
                                      <Badge variant="outline" className="text-xs">
                                        {voice.language.toUpperCase()}
                                      </Badge>
                                    )}
                                    {voice.gender && (
                                      <Badge variant="outline" className="text-xs">
                                        {voice.gender}
                                      </Badge>
                                    )}
                                    {voice.category && (
                                      <Badge variant="secondary" className="text-xs">
                                        {voice.category.replace('-', ' ')}
                                      </Badge>
                                    )}
                                    {voice.style && voice.style !== 'neutral' && (
                                      <Badge variant="secondary" className="text-xs">
                                        {voice.style}
                                      </Badge>
                                    )}
                                  </div>
                                  {voice.description && (
                                    <p className="text-xs text-gray-500 mt-1 line-clamp-2">{voice.description}</p>
                                  )}
                                  {(voice.dataset || voice.architecture) && (
                                    <div className="flex items-center gap-1 mt-1 text-xs text-gray-400">
                                      {voice.dataset && <span>{voice.dataset}</span>}
                                      {voice.dataset && voice.architecture && <span>•</span>}
                                      {voice.architecture && <span>{voice.architecture}</span>}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant={playingVoice === voice.id ? "default" : "outline"}
                              onClick={(e) => {
                                e.stopPropagation();
                                void handlePreview(voice.id);
                              }}
                              disabled={
                                (playingVoice !== null && playingVoice !== voice.id) ||
                                selectedService === 'chatterbox' ||
                                selectedService === 'elevenlabs'
                              }
                              className="ml-4"
                            >
                              {playingVoice === voice.id ? (
                                <>
                                  <Pause className="h-4 w-4 mr-1" />
                                  Playing
                                </>
                              ) : (
                                <>
                                  <Play className="h-4 w-4 mr-1" />
                                  Preview
                                </>
                              )}
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Speed: {speed.toFixed(1)}x
                </label>
                <Slider
                  value={[speed]}
                  onValueChange={(value) => setSpeed(value[0])}
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  className="w-full"
                />
              </div>

              {selectedService === 'chatterbox' && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Temperature: {temperature.toFixed(1)}
                  </label>
                  <Slider
                    value={[temperature]}
                    onValueChange={(value) => setTemperature(value[0])}
                    min={0.1}
                    max={1.0}
                    step={0.1}
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Higher values create more expressive speech
                  </p>
                </div>
              )}

              {selectedService === 'elevenlabs' && (
                <>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Stability: {stability.toFixed(1)}
                    </label>
                    <Slider
                      value={[stability]}
                      onValueChange={(value) => setStability(value[0])}
                      min={0.0}
                      max={1.0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Pitch: {pitch.toFixed(1)}
                    </label>
                    <Slider
                      value={[pitch]}
                      onValueChange={(value) => setPitch(value[0])}
                      min={0.5}
                      max={2.0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </>
              )}
            </div>

            {/* Enhanced Preview Section */}
            {showPreview && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Global Voice Preview</label>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 mb-3">
                      "{sampleText}"
                    </p>
                    <Button 
                      onClick={() => void handlePreview()}
                      disabled={playingVoice !== null || !selectedVoice}
                      className="w-full"
                    >
                      {playingVoice === selectedVoice ? (
                        <>
                          <Pause className="h-4 w-4 mr-2" />
                          Playing Selected Voice...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          Preview Selected Voice
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Volume2 className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">
                        {selectedServiceData.name} Features
                      </span>
                    </div>
                    <ul className="text-sm text-blue-700 space-y-1">
                      {selectedServiceData.features.map((feature) => (
                        <li key={feature}>• {feature}</li>
                      ))}
                    </ul>
                  </div>

                  {selectedService === 'openai' && (
                    <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Zap className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-medium text-green-800">
                          Professional Voice Synthesis
                        </span>
                      </div>
                      <p className="text-sm text-green-700">
                        Professional-grade text-to-speech with natural intonation and fast processing for high-quality course creation.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}