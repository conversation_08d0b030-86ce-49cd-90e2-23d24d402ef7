import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  Di<PERSON>Title,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Loader2,
  Search,
  Filter,
  Image,
  Video,
  Upload,
  Grid3X3,
  Maximize,
  Square,
  ArrowUpDown,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface PexelsPhoto {
  id: number;
  width: number;
  height: number;
  url: string;
  photographer: string;
  photographer_url: string;
  src: {
    original: string;
    large2x: string;
    large: string;
    medium: string;
    small: string;
    portrait: string;
    landscape: string;
    tiny: string;
  };
  alt: string;
}

interface PexelsVideo {
  id: number;
  width: number;
  height: number;
  url: string;
  image: string;
  duration: number;
  user: {
    id: number;
    name: string;
    url: string;
  };
  video_files: {
    id: number;
    quality: string;
    file_type: string;
    width: number | null;
    height: number | null;
    fps: number | null;
    link: string;
  }[];
  video_pictures: {
    id: number;
    picture: string;
    nr: number;
  }[];
}

interface PexelsMediaSearchProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (media: { id: number; type: string; url: string; name: string; size: number }) => void;
}

type Orientation = "all" | "landscape" | "portrait" | "square";
type Size = "all" | "large" | "medium" | "small";

export function PexelsMediaSearch({ isOpen, onClose, onSelect }: PexelsMediaSearchProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("photos");
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [orientation, setOrientation] = useState<Orientation>("all");
  const [size, setSize] = useState<Size>("all");
  const { toast } = useToast();

  // Construct the query parameters
  const getQueryParams = () => {
    const params = new URLSearchParams();
    params.append("query", searchQuery);
    
    if (orientation !== "all") {
      params.append("orientation", orientation);
    }
    
    if (size !== "all") {
      params.append("size", size);
    }
    
    return params.toString();
  };

  const {
    data: photosData,
    isLoading: isLoadingPhotos,
    error: photosError,
  } = useQuery({
    queryKey: ["/api/pexels/photos", searchQuery, orientation, size],
    queryFn: () => 
      fetch(`/api/pexels/photos?${getQueryParams()}`).then(res => res.json()),
    enabled: searchQuery !== "" && activeTab === "photos" && searchPerformed,
  });

  const {
    data: videosData,
    isLoading: isLoadingVideos,
    error: videosError,
  } = useQuery({
    queryKey: ["/api/pexels/videos", searchQuery, size],
    queryFn: () => 
      fetch(`/api/pexels/videos?${getQueryParams()}`).then(res => res.json()),
    enabled: searchQuery !== "" && activeTab === "videos" && searchPerformed,
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setSearchPerformed(true);
    }
  };

  const handleImportMedia = async (item: PexelsPhoto | PexelsVideo, type: "photo" | "video") => {
    try {
      // Determine file size (estimate since we don't have the actual size)
      // Using positive values to ensure validation passes
      const estimatedSize = type === "photo" 
        ? 500 * 1024 // Assume 500KB for photos
        : 5 * 1024 * 1024; // Assume 5MB for videos
      
      // Important: mediaType needs to be "image" or "video" for storage, but 
      // type needs to be "photo" or "video" for the Pexels API
      const mediaType = type === "photo" ? "image" : "video";
      const name = type === "photo" 
        ? ((item as PexelsPhoto).alt || `Pexels Photo ${item.id}`)
        : `Pexels Video ${item.id}`;
      
      console.log("Sending import request with:", {
        type,  // "photo" or "video" for the API type
        id: item.id,
        mediaType, // "image" or "video" for storage type
        name,
        fileSize: estimatedSize
      });
      
      const response = await apiRequest("POST", "/api/pexels/import", {
        type,  // Keeping as "photo" or "video" for API validation
        id: item.id,
        mediaType, // "image" or "video" for storage
        name,
        fileSize: estimatedSize
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Import response error:", errorData);
        throw new Error(errorData.message || "Failed to import media from Pexels");
      }
      
      const importedMedia = await response.json();
      
      // Update media library cache
      queryClient.invalidateQueries({ queryKey: ["/api/media"] });
      
      toast({
        title: "Media imported successfully",
        description: `The ${type} has been added to your media library.`
      });
      
      // Close the dialog and trigger the onSelect callback
      onClose();
      onSelect({
        id: importedMedia.id,
        type: mediaType,
        url: importedMedia.url,
        name: importedMedia.name,
        size: importedMedia.fileSize
      });
    } catch (error: any) {
      console.error("Import error:", error);
      toast({
        title: "Import failed",
        description: error.message || "There was an error importing the media.",
        variant: "destructive"
      });
    }
  };

  // Helper function to determine orientation based on dimensions
  const getPhotoOrientation = (width: number, height: number): Orientation => {
    if (width === height) return "square";
    return width > height ? "landscape" : "portrait";
  };

  // Filter photos based on selected orientation
  const filteredPhotos = photosData?.photos.filter((photo: PexelsPhoto) => {
    if (orientation === "all") return true;
    return getPhotoOrientation(photo.width, photo.height) === orientation;
  });

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="sm:max-w-[1000px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Search Pexels Media</DialogTitle>
          <DialogDescription>
            Find and import free photos and videos from Pexels
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSearch} className="flex gap-2 my-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for photos or videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button type="submit" disabled={!searchQuery.trim()}>
            <Search className="w-4 h-4 mr-2" />
            Search
          </Button>
        </form>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
            <TabsList className="grid w-full sm:w-auto grid-cols-2">
              <TabsTrigger value="photos" className="flex items-center gap-1">
                <Image className="w-4 h-4" />
                Photos
              </TabsTrigger>
              <TabsTrigger value="videos" className="flex items-center gap-1">
                <Video className="w-4 h-4" />
                Videos
              </TabsTrigger>
            </TabsList>

            <div className="flex flex-wrap gap-2 items-center">
              <div className="flex items-center">
                <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
                <span className="text-sm font-medium mr-2">Filters:</span>
              </div>

              {activeTab === "photos" && (
                <Select value={orientation} onValueChange={(value) => setOrientation(value as Orientation)}>
                  <SelectTrigger className="w-[130px] h-9">
                    <SelectValue placeholder="Orientation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all" className="flex items-center gap-2">
                      <Grid3X3 className="w-4 h-4" /> All
                    </SelectItem>
                    <SelectItem value="landscape" className="flex items-center gap-2">
                      <Maximize className="w-4 h-4" /> Landscape
                    </SelectItem>
                    <SelectItem value="portrait" className="flex items-center gap-2">
                      <ArrowUpDown className="w-4 h-4" /> Portrait
                    </SelectItem>
                    <SelectItem value="square" className="flex items-center gap-2">
                      <Square className="w-4 h-4" /> Square
                    </SelectItem>
                  </SelectContent>
                </Select>
              )}

              <Select value={size} onValueChange={(value) => setSize(value as Size)}>
                <SelectTrigger className="w-[100px] h-9">
                  <SelectValue placeholder="Size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sizes</SelectItem>
                  <SelectItem value="large">Large</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="small">Small</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active filters display */}
          {searchPerformed && (orientation !== "all" || size !== "all") && (
            <div className="flex flex-wrap gap-2 mb-4">
              <span className="text-sm text-muted-foreground">Active filters:</span>
              {orientation !== "all" && (
                <Badge variant="outline" className="flex items-center gap-1">
                  {orientation.charAt(0).toUpperCase() + orientation.slice(1)}
                  <button
                    onClick={() => setOrientation("all")}
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {size !== "all" && (
                <Badge variant="outline" className="flex items-center gap-1">
                  Size: {size.charAt(0).toUpperCase() + size.slice(1)}
                  <button
                    onClick={() => setSize("all")}
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
            </div>
          )}

          <TabsContent value="photos" className="space-y-4">
            {isLoadingPhotos ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : photosError ? (
              <div className="text-center text-red-500 py-8">
                Error loading photos: {(photosError as Error).message}
              </div>
            ) : filteredPhotos?.length > 0 ? (
              <>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    Found {filteredPhotos.length} photos
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {filteredPhotos.map((photo: PexelsPhoto) => (
                    <Card key={photo.id} className="overflow-hidden group">
                      <div className="aspect-video relative overflow-hidden">
                        <img
                          src={photo.src.medium}
                          alt={photo.alt || `Photo by ${photo.photographer}`}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <Button 
                            size="sm" 
                            onClick={() => handleImportMedia(photo, "photo")}
                            className="bg-white/90 text-black hover:bg-white"
                          >
                            <Upload className="h-4 w-4 mr-1" />
                            Import
                          </Button>
                        </div>
                      </div>
                      <CardContent className="p-3">
                        <div className="flex flex-col gap-1">
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-muted-foreground truncate flex-1">
                              By {photo.photographer}
                            </div>
                            <Button 
                              size="sm" 
                              variant="ghost"
                              onClick={() => handleImportMedia(photo, "photo")}
                            >
                              Import
                            </Button>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{photo.width}×{photo.height}</span>
                            <Badge variant="outline" className="text-xs">
                              {getPhotoOrientation(photo.width, photo.height)}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                {photosData && photosData.total_results > photosData.photos.length && (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground mb-2">
                      Showing {photosData.photos.length} of {photosData.total_results} results
                    </p>
                  </div>
                )}
              </>
            ) : searchPerformed ? (
              <div className="text-center py-8">
                No photos found. Try a different search term or adjust your filters.
              </div>
            ) : null}
          </TabsContent>

          <TabsContent value="videos" className="space-y-4">
            {isLoadingVideos ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : videosError ? (
              <div className="text-center text-red-500 py-8">
                Error loading videos: {(videosError as Error).message}
              </div>
            ) : videosData?.videos?.length > 0 ? (
              <>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    Found {videosData.videos.length} videos
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {videosData.videos.map((video: PexelsVideo) => (
                    <Card key={video.id} className="overflow-hidden group">
                      <div className="aspect-video relative overflow-hidden">
                        <img
                          src={video.image}
                          alt={`Video by ${video.user.name}`}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                          <div className="text-white font-medium bg-black/50 px-2 py-1 rounded">
                            {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}
                          </div>
                        </div>
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <Button 
                            size="sm" 
                            onClick={() => handleImportMedia(video, "video")}
                            className="bg-white/90 text-black hover:bg-white"
                          >
                            <Upload className="h-4 w-4 mr-1" />
                            Import
                          </Button>
                        </div>
                      </div>
                      <CardContent className="p-3">
                        <div className="flex flex-col gap-1">
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-muted-foreground truncate flex-1">
                              By {video.user.name}
                            </div>
                            <Button 
                              size="sm" 
                              variant="ghost"
                              onClick={() => handleImportMedia(video, "video")}
                            >
                              Import
                            </Button>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{video.width}×{video.height}</span>
                            <span>{Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                {videosData && videosData.total_results > videosData.videos.length && (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground mb-2">
                      Showing {videosData.videos.length} of {videosData.total_results} results
                    </p>
                  </div>
                )}
              </>
            ) : searchPerformed ? (
              <div className="text-center py-8">
                No videos found. Try a different search term or adjust your filters.
              </div>
            ) : null}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}