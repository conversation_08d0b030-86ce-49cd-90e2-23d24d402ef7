import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, Plus, Trash2, Loader2, Wand2, Network, Brain, Clock } from "lucide-react";
import { generateCourseStructure } from "@/lib/ai";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { MindMap } from "@/components/mind-map/MindMap";
import { useToast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { MicroLearningSettings } from "@/components/micro-learning/MicroLearningSettings";

export interface Module {
  title: string;
  description?: string;
  lessons: Lesson[];
  expanded?: boolean;
}

export interface Lesson {
  title: string;
  description: string;
  id?: number;
  microLearningEnabled?: boolean;
  microLearningSegmentCount?: number;
  microLearningBreakInterval?: number; // In seconds
  microLearningBreakDuration?: number; // In seconds
}

interface ContentStructureProps {
  onNext: () => void;
  onPrevious: () => void;
  courseDetails: {
    title: string;
    description: string;
    category: string;
    targetAudience?: string;
    useAI: boolean;
    moduleCount?: number;
  };
  // Add optional callback to update parent state with the modules
  onUpdateStructure?: React.Dispatch<React.SetStateAction<{ modules: Module[] }>>;
  // Initial structure for when editing existing courses
  initialStructure?: { modules: Module[] };
}

export function ContentStructure({ onNext, onPrevious, courseDetails, onUpdateStructure, initialStructure }: ContentStructureProps) {
  const [activeTab, setActiveTab] = useState<string>("ai-generated");
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [generationError, setGenerationError] = useState<string | null>(null);
  const [selectedModuleCount, setSelectedModuleCount] = useState<number>(courseDetails.moduleCount || 4);
  const { toast } = useToast();
  
  // Helper function to create default modules based on count
  const createDefaultModules = (count: number): Module[] => {
    // Always include an introduction module as the first module
    const defaultModules: Module[] = [
      {
        title: "Introduction",
        description: "An introduction to the course",
        lessons: [
          {
            title: "Getting Started",
            description: "An overview of what to expect in this course"
          }
        ],
        expanded: true
      }
    ];

    // Add remaining modules
    for (let i = 1; i < count; i++) {
      defaultModules.push({
        title: `Module ${i + 1}`,
        description: `Content for module ${i + 1}`,
        lessons: [
          {
            title: `Lesson 1`,
            description: `First lesson in module ${i + 1}`
          }
        ],
        expanded: false
      });
    }

    return defaultModules;
  };

  // Use initialStructure if provided (for edit mode), otherwise use default structure based on moduleCount
  const [modules, setModules] = useState<Module[]>(
    initialStructure?.modules ? 
    // Add expanded property to modules from initialStructure if not already present
    initialStructure.modules.map((module, index) => ({
      ...module,
      expanded: module.expanded !== undefined ? module.expanded : index === 0
    })) : 
    // Create default modules based on moduleCount or default to 1 module
    createDefaultModules(courseDetails.moduleCount || 1)
  );
  
  // User's notes and ideas for content
  const [contentNotes, setContentNotes] = useState<string>("");
  const [keyTopics, setKeyTopics] = useState<string>("");
  
  // Generate structure using AI when component mounts if useAI is true
  useEffect(() => {
    if (courseDetails.useAI && activeTab === "ai-generated") {
      handleGenerateStructure();
    }
  }, [courseDetails.useAI]);
  
  const handleGenerateStructure = async () => {
    setIsGenerating(true);
    setGenerationError(null);
    
    try {
      // Include user's content notes and key topics in the generation
      const enrichedDescription = [
        courseDetails.description,
        contentNotes ? `Additional notes: ${contentNotes}` : "",
        keyTopics ? `Key topics to cover: ${keyTopics}` : ""
      ].filter(Boolean).join("\n\n");
      
      const result = await generateCourseStructure({
        title: courseDetails.title,
        description: courseDetails.description,
        category: courseDetails.category,
        targetAudience: courseDetails.targetAudience,
        keyTopics: keyTopics || undefined,
        contentNotes: contentNotes || undefined,
        moduleCount: selectedModuleCount
      });
      
      if (result && result.modules && result.modules.length > 0) {
        // Add expanded property to each module
        const modulesWithExpanded = result.modules.map((module: Module, index: number) => ({
          ...module,
          expanded: index === 0 // Only expand the first module by default
        }));
        
        setModules(modulesWithExpanded);
      }
    } catch (error) {
      console.error("Error generating structure:", error);
      setGenerationError(error instanceof Error ? error.message : "Failed to generate course structure");
    } finally {
      setIsGenerating(false);
    }
  };
  
  const handleAddModule = () => {
    setModules([
      ...modules,
      {
        title: `Module ${modules.length + 1}`,
        description: "",
        lessons: [
          {
            title: "New Lesson",
            description: ""
          }
        ],
        expanded: true
      }
    ]);
  };
  
  const handleAddLesson = (moduleIndex: number) => {
    const updatedModules = [...modules];
    updatedModules[moduleIndex].lessons.push({
      title: `Lesson ${updatedModules[moduleIndex].lessons.length + 1}`,
      description: ""
    });
    setModules(updatedModules);
  };
  
  const handleRemoveModule = (moduleIndex: number) => {
    const updatedModules = [...modules];
    updatedModules.splice(moduleIndex, 1);
    setModules(updatedModules);
  };
  
  const handleRemoveLesson = (moduleIndex: number, lessonIndex: number) => {
    const updatedModules = [...modules];
    updatedModules[moduleIndex].lessons.splice(lessonIndex, 1);
    setModules(updatedModules);
  };
  
  const handleUpdateModule = (moduleIndex: number, field: string, value: string) => {
    const updatedModules = [...modules];
    (updatedModules[moduleIndex] as any)[field] = value;
    setModules(updatedModules);
  };
  
  const handleUpdateLesson = (moduleIndex: number, lessonIndex: number, field: string, value: string) => {
    const updatedModules = [...modules];
    (updatedModules[moduleIndex].lessons[lessonIndex] as any)[field] = value;
    setModules(updatedModules);
  };
  
  const handleToggleModuleExpansion = (moduleIndex: number) => {
    const updatedModules = [...modules];
    updatedModules[moduleIndex].expanded = !updatedModules[moduleIndex].expanded;
    setModules(updatedModules);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold">Content Structure</h3>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => {
            // Update parent component's state with current modules before navigating back
            if (onUpdateStructure) {
              onUpdateStructure({ modules });
            }
            onPrevious();
          }}>Back</Button>
          <Button onClick={() => {
            // Update parent component's state with current modules before navigating
            if (onUpdateStructure) {
              onUpdateStructure({ modules });
            }
            onNext();
          }}>Continue</Button>
        </div>
      </div>
      
      <Tabs defaultValue={activeTab} className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="your-ideas">Your Ideas</TabsTrigger>
          <TabsTrigger value="mind-map">Concept Mind Map</TabsTrigger>
          <TabsTrigger value="ai-generated">AI Generated Structure</TabsTrigger>
        </TabsList>
        
        <TabsContent value="your-ideas" className="space-y-4 mt-4">
          <div className="grid grid-cols-1 gap-6">
            <div>
              <Label htmlFor="keyTopics">Key Topics</Label>
              <Textarea 
                id="keyTopics"
                placeholder="List the key topics you want to cover in your course..."
                value={keyTopics}
                onChange={(e) => setKeyTopics(e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-sm text-muted-foreground mt-1">
                Separate different topics with new lines or commas
              </p>
            </div>
            
            <div>
              <Label htmlFor="contentNotes">Additional Notes & Ideas</Label>
              <Textarea 
                id="contentNotes"
                placeholder="Add any additional ideas, notes, or structure information..."
                value={contentNotes}
                onChange={(e) => setContentNotes(e.target.value)}
                className="min-h-[150px]"
              />
            </div>
            
            <div className="flex justify-end">
              <Button 
                onClick={() => {
                  setActiveTab("ai-generated");
                  handleGenerateStructure();
                }}
                variant="secondary"
              >
                <Wand2 className="mr-2 h-4 w-4" />
                Generate Structure from Ideas
              </Button>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="mind-map" className="mt-4">
          <MindMap 
            courseTitle={courseDetails.title}
            courseDescription={courseDetails.description}
            onConvertToCourseStructure={(generatedModules) => {
              setModules(generatedModules);
              setActiveTab("ai-generated");
              
              // Show success message
              toast({
                title: "Course structure created",
                description: "The mind map has been converted to a course structure",
              });
            }}
          />
        </TabsContent>
        
        <TabsContent value="ai-generated" className="space-y-4 mt-4">
          {generationError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                {generationError}
              </AlertDescription>
            </Alert>
          )}
          
          {isGenerating ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
              <p className="text-muted-foreground">Generating course structure...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex flex-col gap-4 p-4 border rounded-lg bg-slate-50">
                <div className="flex justify-between items-center">
                  <div className="flex flex-col gap-1">
                    <h4 className="font-medium">AI Generation Settings</h4>
                    <p className="text-sm text-muted-foreground">Customize your AI-generated course structure</p>
                  </div>
                  
                  <Button 
                    onClick={handleGenerateStructure}
                    className="gap-2"
                  >
                    <Wand2 className="h-4 w-4" />
                    Generate with AI
                  </Button>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="moduleCount">Number of Modules</Label>
                    <div className="text-sm font-medium">{selectedModuleCount}</div>
                  </div>
                  <Slider 
                    id="moduleCount"
                    min={1} 
                    max={10} 
                    step={1} 
                    value={[selectedModuleCount]} 
                    onValueChange={(value) => setSelectedModuleCount(value[0])}
                    className="py-2"
                  />
                  <p className="text-xs text-muted-foreground">Adjust the slider to set how many modules you want in your course.</p>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <h4 className="font-medium">Course Modules</h4>
                <Button variant="outline" size="sm" onClick={handleAddModule}>
                  <Plus className="h-4 w-4 mr-1" /> Add Module
                </Button>
              </div>
              
              <Accordion type="multiple" className="w-full">
                {modules.map((module, moduleIndex) => (
                  <AccordionItem 
                    key={moduleIndex} 
                    value={`module-${moduleIndex}`}
                    className="border rounded-md mb-4 border-border"
                  >
                    <div className="flex items-center justify-between px-4">
                      <AccordionTrigger className="flex-1 py-2">
                        <div className="flex items-center">
                          <span className="font-medium">{module.title}</span>
                        </div>
                      </AccordionTrigger>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveModule(moduleIndex);
                        }}
                      >
                        <Trash2 className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </div>
                    
                    <AccordionContent className="px-4 pb-4">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor={`module-${moduleIndex}-title`}>Module Title</Label>
                          <Input
                            id={`module-${moduleIndex}-title`}
                            value={module.title}
                            onChange={(e) => handleUpdateModule(moduleIndex, 'title', e.target.value)}
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor={`module-${moduleIndex}-description`}>Description</Label>
                          <Textarea
                            id={`module-${moduleIndex}-description`}
                            value={module.description || ""}
                            onChange={(e) => handleUpdateModule(moduleIndex, 'description', e.target.value)}
                            rows={2}
                          />
                        </div>
                        
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <Label>Lessons</Label>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleAddLesson(moduleIndex)}
                            >
                              <Plus className="h-4 w-4 mr-1" /> Add Lesson
                            </Button>
                          </div>
                          
                          {module.lessons.map((lesson, lessonIndex) => (
                            <Card key={lessonIndex} className="border border-border">
                              <CardHeader className="py-3 px-4">
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-2">
                                    <Label>Lesson {lessonIndex + 1}</Label>
                                    {lesson.microLearningEnabled && (
                                      <Badge variant="outline" className="text-xs gap-1 flex items-center">
                                        <Brain className="h-3 w-3" />
                                        <span>Micro-Learning</span>
                                      </Badge>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <MicroLearningSettings 
                                      lesson={lesson}
                                      onSettingsChanged={(updatedLesson) => {
                                        const updatedModules = [...modules];
                                        updatedModules[moduleIndex].lessons[lessonIndex] = {
                                          ...updatedModules[moduleIndex].lessons[lessonIndex],
                                          ...updatedLesson
                                        };
                                        setModules(updatedModules);
                                        
                                        toast({
                                          title: "Micro-Learning Settings Updated",
                                          description: updatedLesson.microLearningEnabled 
                                            ? "Micro-learning has been enabled for this lesson" 
                                            : "Micro-learning has been disabled for this lesson"
                                        });
                                      }}
                                    />
                                    <Button 
                                      variant="ghost" 
                                      size="icon"
                                      onClick={() => handleRemoveLesson(moduleIndex, lessonIndex)}
                                    >
                                      <Trash2 className="h-4 w-4 text-muted-foreground" />
                                    </Button>
                                  </div>
                                </div>
                              </CardHeader>
                              <CardContent className="py-2 px-4 space-y-4">
                                <div className="space-y-2">
                                  <Label htmlFor={`module-${moduleIndex}-lesson-${lessonIndex}-title`}>Title</Label>
                                  <Input
                                    id={`module-${moduleIndex}-lesson-${lessonIndex}-title`}
                                    value={lesson.title}
                                    onChange={(e) => handleUpdateLesson(moduleIndex, lessonIndex, 'title', e.target.value)}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor={`module-${moduleIndex}-lesson-${lessonIndex}-description`}>Description</Label>
                                  <Textarea
                                    id={`module-${moduleIndex}-lesson-${lessonIndex}-description`}
                                    value={lesson.description}
                                    onChange={(e) => handleUpdateLesson(moduleIndex, lessonIndex, 'description', e.target.value)}
                                    rows={2}
                                  />
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
              
              <div className="flex justify-end space-x-2">
                <Button 
                  variant="outline"
                  onClick={handleGenerateStructure}
                  disabled={isGenerating}
                >
                  <Wand2 className="mr-2 h-4 w-4" />
                  Regenerate Structure
                </Button>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}