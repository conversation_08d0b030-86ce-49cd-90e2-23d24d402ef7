import { Request, Response } from 'express';
import { z } from 'zod';
import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Ensure uploads directory exists
const uploadsDir = path.join(process.cwd(), 'uploads', 'audio');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Request validation schema
const openAITTSSchema = z.object({
  text: z.string().min(1, "Text is required").max(4096, "Text is too long for OpenAI TTS"),
  voice: z.enum(['alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer']).default('nova'),
  model: z.enum(['tts-1', 'tts-1-hd']).default('tts-1'),
  speed: z.number().min(0.25).max(4.0).default(1.0),
  moduleId: z.string().optional(),
  lessonId: z.string().optional(),
  moduleTitle: z.string().optional(),
  lessonTitle: z.string().optional(),
});

// OpenAI Text-to-Speech endpoint
export async function generateOpenAITTS(req: Request, res: Response) {
  try {
    // Validate request body
    const validationResult = openAITTSSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Invalid request data',
        details: validationResult.error.format()
      });
    }

    const { text, voice, model, speed, moduleId, lessonId, moduleTitle, lessonTitle } = validationResult.data;

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY) {
      return res.status(500).json({
        error: 'OpenAI API key not configured',
        message: 'Please configure your OpenAI API key to use this service'
      });
    }

    // Generate unique filename
    const fileName = `openai-tts-${uuidv4()}.mp3`;
    const filePath = path.join(uploadsDir, fileName);

    try {
      // Generate speech using OpenAI TTS
      const mp3 = await openai.audio.speech.create({
        model: model,
        voice: voice,
        input: text,
        speed: speed,
      });

      // Convert the response to a buffer and save to file
      const buffer = Buffer.from(await mp3.arrayBuffer());
      fs.writeFileSync(filePath, buffer);

      // Calculate duration estimate (rough approximation)
      const wordCount = text.split(/\s+/).length;
      const estimatedDuration = Math.ceil((wordCount / 150) * 60); // ~150 words per minute

      // Generate accessible URL
      const audioUrl = `/uploads/audio/${fileName}`;

      return res.status(200).json({
        success: true,
        audioUrl: audioUrl,
        fileName: fileName,
        duration: estimatedDuration,
        service: 'openai',
        model: model,
        voice: voice,
        speed: speed,
        wordCount: wordCount,
        moduleId: moduleId,
        lessonId: lessonId,
        moduleTitle: moduleTitle,
        lessonTitle: lessonTitle,
        message: 'Voice generated successfully with OpenAI TTS'
      });

    } catch (openaiError: any) {
      console.error('OpenAI TTS error:', openaiError);
      
      // Handle specific OpenAI errors
      if (openaiError.status === 401) {
        return res.status(401).json({
          error: 'Invalid OpenAI API key',
          message: 'Please check your OpenAI API key configuration'
        });
      }
      
      if (openaiError.status === 429) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: 'Please try again in a few moments'
        });
      }

      return res.status(500).json({
        error: 'OpenAI TTS generation failed',
        message: openaiError.message || 'Unknown error occurred'
      });
    }

  } catch (error: any) {
    console.error('Error in OpenAI TTS generation:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process TTS request'
    });
  }
}

// Get available OpenAI voices
export async function getOpenAIVoices(req: Request, res: Response) {
  try {
    const voices = [
      { id: 'alloy', name: 'Alloy', gender: 'female', description: 'Balanced, warm', quality: 'standard' },
      { id: 'echo', name: 'Echo', gender: 'male', description: 'Clear, professional', quality: 'standard' },
      { id: 'fable', name: 'Fable', gender: 'male', description: 'Storytelling, engaging', quality: 'standard' },
      { id: 'onyx', name: 'Onyx', gender: 'male', description: 'Deep, authoritative', quality: 'standard' },
      { id: 'nova', name: 'Nova', gender: 'female', description: 'Friendly, conversational', quality: 'standard' },
      { id: 'shimmer', name: 'Shimmer', gender: 'female', description: 'Energetic, bright', quality: 'standard' }
    ];

    return res.status(200).json({
      voices: voices,
      models: ['tts-1', 'tts-1-hd'],
      service: 'openai'
    });

  } catch (error: any) {
    console.error('Error fetching OpenAI voices:', error);
    return res.status(500).json({
      error: 'Failed to fetch voice options',
      message: 'Could not retrieve available voices'
    });
  }
}

// Get available TTS models
export async function getOpenAIModels(req: Request, res: Response) {
  try {
    const models = [
      {
        id: 'tts-1',
        name: 'Standard Quality',
        description: 'Fast generation with good quality',
        pricing: '$0.015 per 1K characters'
      },
      {
        id: 'tts-1-hd',
        name: 'HD Quality',
        description: 'Higher quality audio generation',
        pricing: '$0.030 per 1K characters'
      }
    ];

    return res.status(200).json({
      models: models,
      service: 'openai'
    });

  } catch (error: any) {
    console.error('Error fetching OpenAI models:', error);
    return res.status(500).json({
      error: 'Failed to fetch model options',
      message: 'Could not retrieve available models'
    });
  }
}