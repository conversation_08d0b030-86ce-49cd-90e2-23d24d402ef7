import express from 'express';
import { storage } from '../storage';
import { seedTemplates } from '../templates/seedTemplates';

const router = express.Router();

// Get all templates
router.get('/', async (req, res) => {
  try {
    const templates = await storage.getAllTemplates();
    
    // If no templates exist, seed them
    if (templates.length === 0) {
      await seedTemplates();
      const newTemplates = await storage.getAllTemplates();
      return res.json(newTemplates);
    }
    
    res.json(templates);
  } catch (error: any) {
    console.error('Error fetching templates:', error);
    res.status(500).json({ 
      message: 'Failed to fetch templates',
      error: error.message 
    });
  }
});

// Get template history for authenticated user
router.get('/history', async (req: any, res) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const history = await storage.getTemplateHistoryByUserId(req.session.userId);
    res.json(history);
  } catch (error: any) {
    console.error('Error fetching template history:', error);
    res.status(500).json({ 
      message: 'Failed to fetch template history',
      error: error.message 
    });
  }
});

// Execute a template
router.post('/execute', async (req: any, res) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { templateId, name, prompt, formData } = req.body;

    if (!templateId || !name || !prompt) {
      return res.status(400).json({ 
        message: 'Template ID, name, and prompt are required' 
      });
    }

    // Get the template to determine the API endpoint and structure
    const template = await storage.getTemplate(templateId);
    if (!template) {
      return res.status(404).json({ message: 'Template not found' });
    }

    // Execute the template based on its structure
    let result: any;
    let aiCreditsUsed = (template.structure as any)?.creditsRequired || 1;

    try {
      // Route to the appropriate API endpoint based on template type
      switch (template.type) {
        case 'course-generation':
          // Use the course generation API
          const courseResponse = await fetch(`${req.protocol}://${req.get('host')}/api/ai/generate-course-structure`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
          });
          result = await courseResponse.json();
          break;

        case 'voice-generation':
          // Use the voice generation API
          const voiceResponse = await fetch(`${req.protocol}://${req.get('host')}/api/voice-generation/generate`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
          });
          result = await voiceResponse.json();
          break;

        case 'image-generation':
          // Use the Modal image generation API
          const imageResponse = await fetch(`${req.protocol}://${req.get('host')}/api/modal/generate-image`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
          });
          result = await imageResponse.json();
          break;

        case 'assessment-generation':
          // Use the quiz generation API
          const quizResponse = await fetch(`${req.protocol}://${req.get('host')}/api/ai/generate-quiz-questions`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
          });
          result = await quizResponse.json();
          break;

        default:
          result = { 
            message: 'Template executed successfully',
            data: formData,
            type: template.type
          };
      }
    } catch (apiError: any) {
      console.error('API execution error:', apiError);
      result = { 
        error: 'Failed to execute template API',
        message: apiError.message 
      };
    }

    // Save to template history
    const historyEntry = await storage.createTemplateHistory({
      userId: req.session.userId,
      templateId,
      name,
      prompt,
      result: JSON.stringify(result),
      aiCreditsUsed
    });

    res.json({
      id: historyEntry.id,
      result: JSON.stringify(result),
      aiCreditsUsed
    });

  } catch (error: any) {
    console.error('Error executing template:', error);
    res.status(500).json({ 
      message: 'Failed to execute template',
      error: error.message 
    });
  }
});

// Update favorite status for template history
router.patch('/history/:id/favorite', async (req: any, res) => {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const { id } = req.params;
    const { favorited } = req.body;

    const updated = await storage.updateTemplateHistoryFavorite(
      parseInt(id), 
      favorited
    );

    if (!updated) {
      return res.status(404).json({ message: 'Template history not found' });
    }

    res.json(updated);
  } catch (error: any) {
    console.error('Error updating favorite status:', error);
    res.status(500).json({ 
      message: 'Failed to update favorite status',
      error: error.message 
    });
  }
});

export default router;