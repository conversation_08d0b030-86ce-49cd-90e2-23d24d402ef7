import React, { useState } from 'react';
import { Card, CardContent, TextField, Button, Grid, Typography, CircularProgress, FormControl, InputLabel, Select, MenuItem, Tabs, Tab, Box } from '@mui/material';
import { Slideshow, Download, Preview, Add, Clear } from '@mui/icons-material';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import apiService from '../services/apiService';

const SlideGenerator = () => {
  const [markdownContent, setMarkdownContent] = useState('');
  const [batchSlides, setBatchSlides] = useState([{ content: '', theme: 'default' }]);
  const [formData, setFormData] = useState({
    theme: 'default',
    outputFormat: 'pdf'
  });
  
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');
  const [mode, setMode] = useState('single');
  const [previewTab, setPreviewTab] = useState(0);

  const themes = [
    { value: 'default', label: 'Default' },
    { value: 'gaia', label: 'Gaia (Modern)' },
    { value: 'uncover', label: 'Uncover (Clean)' },
    { value: 'gradient', label: 'Gradient' },
    { value: 'academic', label: 'Academic' }
  ];

  const formats = [
    { value: 'pdf', label: 'PDF' },
    { value: 'html', label: 'HTML' },
    { value: 'pptx', label: 'PowerPoint' }
  ];

  const templateSlides = {
    course_intro: `---
marp: true
theme: default
---

# Course Introduction
## Welcome to Advanced Machine Learning

### What You'll Learn
- Deep Learning Fundamentals
- Neural Network Architectures
- Practical Implementation
- Real-world Applications

---

# Learning Objectives

By the end of this course, you will:

1. **Understand** core ML concepts
2. **Implement** neural networks from scratch
3. **Apply** models to real problems
4. **Evaluate** model performance

---

# Course Structure

## Module 1: Foundations
- Linear Algebra Review
- Statistics & Probability
- Python Programming

## Module 2: Core Algorithms
- Supervised Learning
- Unsupervised Learning
- Reinforcement Learning`,

    lesson_template: `---
marp: true
theme: default
---

# Lesson Title
## Subtitle Goes Here

### Key Topics
- Topic 1
- Topic 2
- Topic 3

---

# Concept Overview

![bg right:40% 80%](https://via.placeholder.com/400x300)

## Main Concept
Detailed explanation of the core concept goes here.

### Key Points:
- Point A
- Point B
- Point C

---

# Practical Example

\`\`\`python
# Code example
import numpy as np
import matplotlib.pyplot as plt

def example_function(x):
    return x ** 2 + 2 * x + 1

# Visualization
x = np.linspace(-5, 5, 100)
y = example_function(x)
plt.plot(x, y)
plt.show()
\`\`\`

---

# Summary & Next Steps

## What We Covered
1. Core concepts
2. Practical examples
3. Code implementation

## Next Lesson Preview
- Advanced topics
- More complex examples
- Hands-on projects`,

    assessment: `---
marp: true
theme: default
---

# Knowledge Check
## Lesson Assessment

### Question 1
What is the primary purpose of neural networks?

A) Data storage
B) Pattern recognition
C) File compression
D) Network security

---

# Practical Exercise

## Task Description
Implement a simple neural network using the concepts learned.

### Requirements:
1. Use provided dataset
2. Apply preprocessing steps
3. Train the model
4. Evaluate performance

### Deliverables:
- Code implementation
- Results analysis
- Performance metrics

---

# Project Rubric

| Criteria | Excellent (4) | Good (3) | Fair (2) | Poor (1) |
|----------|---------------|----------|----------|----------|
| Implementation | Complete & optimized | Complete | Partially complete | Incomplete |
| Analysis | Thorough insights | Good analysis | Basic analysis | Limited analysis |
| Presentation | Clear & professional | Well organized | Adequate | Poor quality |`
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addBatchSlide = () => {
    setBatchSlides(prev => [...prev, { content: '', theme: 'default' }]);
  };

  const removeBatchSlide = (index) => {
    setBatchSlides(prev => prev.filter((_, i) => i !== index));
  };

  const updateBatchSlide = (index, field, value) => {
    setBatchSlides(prev => prev.map((slide, i) => 
      i === index ? { ...slide, [field]: value } : slide
    ));
  };

  const useTemplate = (template) => {
    setMarkdownContent(templateSlides[template]);
    setPreviewTab(1);
  };

  const handleGenerate = async () => {
    const slidesToProcess = mode === 'single' 
      ? [{ content: markdownContent, theme: formData.theme }]
      : batchSlides.filter(slide => slide.content.trim());
    
    if (slidesToProcess.length === 0 || slidesToProcess.every(slide => !slide.content.trim())) {
      setError('Please enter markdown content for slides');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const requestData = {
        markdownContent: slidesToProcess[0].content,
        batchSlides: mode === 'batch' ? slidesToProcess : null,
        ...formData
      };

      const cacheKey = apiService.generateCacheKey(requestData);
      const cached = apiService.getCachedResult(cacheKey);
      
      if (cached) {
        setResults(cached.results.map((result, index) => ({ 
          ...result, 
          id: Date.now() + index,
          cached: true 
        })));
        setLoading(false);
        return;
      }

      const result = await apiService.generateSlides(requestData);
      
      if (result.status === 'success') {
        const newResults = result.results.map((res, index) => ({
          ...res,
          id: Date.now() + index,
          cached: res.cached || false
        }));
        setResults(newResults);
        
        if (!result.cached) {
          apiService.setCachedResult(cacheKey, result);
        }
      } else {
        setError(result.error || 'Slide generation failed');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = (slidesBase64, index, format) => {
    const filename = `generated-slides-${Date.now()}-${index}.${format}`;
    const mimeType = format === 'pdf' ? 'application/pdf' : 
                     format === 'html' ? 'text/html' : 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
    apiService.downloadBase64File(slidesBase64, filename, mimeType);
  };

  const clearAll = () => {
    setMarkdownContent('');
    setBatchSlides([{ content: '', theme: 'default' }]);
    setResults([]);
    setError('');
  };

  return (
    <div className="slide-generator">
      <Typography variant="h4" gutterBottom>
        <Slideshow /> Marp Slide Generator
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Content Input</Typography>
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Mode</InputLabel>
                <Select
                  value={mode}
                  onChange={(e) => setMode(e.target.value)}
                >
                  <MenuItem value="single">Single Presentation</MenuItem>
                  <MenuItem value="batch">Batch Generation</MenuItem>
                </Select>
              </FormControl>

              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Quick Templates:
              </Typography>
              <Grid container spacing={1} sx={{ mb: 2 }}>
                <Grid item xs={4}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => useTemplate('course_intro')}
                    fullWidth
                  >
                    Course Intro
                  </Button>
                </Grid>
                <Grid item xs={4}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => useTemplate('lesson_template')}
                    fullWidth
                  >
                    Lesson Template
                  </Button>
                </Grid>
                <Grid item xs={4}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => useTemplate('assessment')}
                    fullWidth
                  >
                    Assessment
                  </Button>
                </Grid>
              </Grid>

              {mode === 'single' ? (
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs value={previewTab} onChange={(e, newValue) => setPreviewTab(newValue)}>
                    <Tab label="Edit" />
                    <Tab label="Preview" />
                  </Tabs>
                  
                  {previewTab === 0 ? (
                    <TextField
                      fullWidth
                      multiline
                      rows={12}
                      label="Markdown Content"
                      value={markdownContent}
                      onChange={(e) => setMarkdownContent(e.target.value)}
                      margin="normal"
                      placeholder="Enter your slide markdown content..."
                      sx={{ fontFamily: 'monospace' }}
                    />
                  ) : (
                    <Box sx={{ p: 2, maxHeight: '400px', overflow: 'auto', border: '1px solid #ddd', borderRadius: 1 }}>
                      <ReactMarkdown
                        components={{
                          code({node, inline, className, children, ...props}) {
                            const match = /language-(\w+)/.exec(className || '')
                            return !inline && match ? (
                              <SyntaxHighlighter
                                style={dark}
                                language={match[1]}
                                PreTag="div"
                                {...props}
                              >
                                {String(children).replace(/\n$/, '')}
                              </SyntaxHighlighter>
                            ) : (
                              <code className={className} {...props}>
                                {children}
                              </code>
                            )
                          }
                        }}
                      >
                        {markdownContent}
                      </ReactMarkdown>
                    </Box>
                  )}
                </Box>
              ) : (
                <div>
                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                    Batch Slides:
                  </Typography>
                  {batchSlides.map((slide, index) => (
                    <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                      <CardContent>
                        <Grid container spacing={2} alignItems="center">
                          <Grid item xs={8}>
                            <TextField
                              fullWidth
                              multiline
                              rows={3}
                              label={`Slide Set ${index + 1}`}
                              value={slide.content}
                              onChange={(e) => updateBatchSlide(index, 'content', e.target.value)}
                              size="small"
                            />
                          </Grid>
                          <Grid item xs={3}>
                            <FormControl fullWidth size="small">
                              <InputLabel>Theme</InputLabel>
                              <Select
                                value={slide.theme}
                                onChange={(e) => updateBatchSlide(index, 'theme', e.target.value)}
                              >
                                {themes.map((theme) => (
                                  <MenuItem key={theme.value} value={theme.value}>
                                    {theme.label}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={1}>
                            {batchSlides.length > 1 && (
                              <Button
                                onClick={() => removeBatchSlide(index)}
                                size="small"
                              >
                                <Clear />
                              </Button>
                            )}
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  ))}
                  <Button
                    startIcon={<Add />}
                    onClick={addBatchSlide}
                    size="small"
                  >
                    Add Slide Set
                  </Button>
                </div>
              )}

              <Button
                variant="outlined"
                onClick={clearAll}
                fullWidth
                sx={{ mt: 2 }}
              >
                Clear All
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Generation Settings</Typography>
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Theme</InputLabel>
                <Select
                  value={formData.theme}
                  onChange={(e) => handleInputChange('theme', e.target.value)}
                >
                  {themes.map((theme) => (
                    <MenuItem key={theme.value} value={theme.value}>
                      {theme.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth margin="normal">
                <InputLabel>Output Format</InputLabel>
                <Select
                  value={formData.outputFormat}
                  onChange={(e) => handleInputChange('outputFormat', e.target.value)}
                >
                  {formats.map((format) => (
                    <MenuItem key={format.value} value={format.value}>
                      {format.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleGenerate}
                disabled={loading}
                sx={{ mt: 3 }}
              >
                {loading ? <CircularProgress size={24} /> : `Generate ${mode === 'batch' ? 'Batch ' : ''}Slides`}
              </Button>

              <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
                CPU-based generation using Marp CLI. Supports themes, custom styling, and multiple output formats.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {error && (
          <Grid item xs={12}>
            <Card sx={{ backgroundColor: '#ffebee' }}>
              <CardContent>
                <Typography color="error">{error}</Typography>
              </CardContent>
            </Card>
          </Grid>
        )}

        {results.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Generated Presentations {loading && '(Processing...)'}
                </Typography>
                <Grid container spacing={2}>
                  {results.map((result, index) => (
                    <Grid item xs={12} sm={6} md={4} key={result.id}>
                      <Card variant="outlined">
                        <CardContent>
                          <Typography variant="subtitle2" gutterBottom>
                            Presentation {result.index + 1}
                            {result.cached && <span style={{ marginLeft: '8px', fontSize: '0.75em', color: '#666' }}>(Cached)</span>}
                          </Typography>
                          
                          {result.status === 'success' ? (
                            <>
                              <div style={{ 
                                width: '100%', 
                                height: '120px', 
                                backgroundColor: '#f5f5f5', 
                                display: 'flex', 
                                alignItems: 'center', 
                                justifyContent: 'center',
                                marginBottom: '8px',
                                borderRadius: '4px'
                              }}>
                                <Slideshow sx={{ fontSize: 48, color: '#ccc' }} />
                              </div>
                              
                              <Button
                                fullWidth
                                size="small"
                                startIcon={<Download />}
                                onClick={() => handleDownload(result.slides_base64, index, result.format)}
                                variant="outlined"
                              >
                                Download {result.format.toUpperCase()}
                              </Button>
                              
                              <Typography variant="caption" display="block" sx={{ mt: 1, textAlign: 'center' }}>
                                Theme: {result.theme} | Format: {result.format}
                              </Typography>
                            </>
                          ) : (
                            <Typography color="error" variant="body2">
                              {result.error}
                            </Typography>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </div>
  );
};

export default SlideGenerator;