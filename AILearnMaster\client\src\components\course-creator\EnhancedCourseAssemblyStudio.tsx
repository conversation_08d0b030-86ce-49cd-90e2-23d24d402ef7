import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  Pause, 
  Image as ImageIcon, 
  Video, 
  FileText, 
  GripVertical, 
  Plus, 
  Eye, 
  Upload,
  Settings,
  Clock,
  CheckCircle,
  AlertCircle,
  Mic,
  Download,
  ArrowRight,
  PlayCircle
} from 'lucide-react';

interface LessonElement {
  id: string;
  type: 'module' | 'lesson' | 'image' | 'video' | 'text' | 'slide';
  title: string;
  content?: string;
  url?: string;
  duration?: number;
  thumbnail?: string;
  order: number;
  moduleId?: string;
  lessonId?: string;
  assignedMedia?: MediaItem[];
  hasVoice?: boolean;
  hasScript?: boolean;
  isComplete?: boolean;
}

interface MediaItem {
  id: number;
  name: string;
  type: string;
  url: string;
  thumbnail?: string;
  duration?: number;
}

interface CourseStructure {
  id: string;
  title: string;
  description: string;
  elements: LessonElement[];
  estimatedDuration: number;
  status: 'draft' | 'ready' | 'published';
}

interface Module {
  id: string;
  title: string;
  description?: string;
  lessons: Lesson[];
}

interface Lesson {
  id: string;
  title: string;
  type?: string;
  content?: string;
  duration?: number;
}

interface EnhancedCourseAssemblyStudioProps {
  courseScripts?: Record<string, Record<string, string>>;
  generatedStructure?: {
    modules: Module[];
  };
  onStructureChange?: (structure: CourseStructure) => void;
  initialStructure?: CourseStructure;
}

export default function EnhancedCourseAssemblyStudio({ 
  courseScripts = {},
  generatedStructure,
  onStructureChange,
  initialStructure
}: EnhancedCourseAssemblyStudioProps) {
  const [courseStructure, setCourseStructure] = useState<CourseStructure>(
    initialStructure || {
      id: 'course-1',
      title: 'My Course',
      description: 'Course description',
      elements: [],
      estimatedDuration: 0,
      status: 'draft'
    }
  );

  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [showMediaLibrary, setShowMediaLibrary] = useState(false);
  const [draggedMedia, setDraggedMedia] = useState<MediaItem | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const { toast } = useToast();

  // Fetch media library
  const { data: mediaLibrary = [] } = useQuery<MediaItem[]>({
    queryKey: ['/api/media']
  });

  // Initialize structure from generated course structure
  useEffect(() => {
    if (generatedStructure && generatedStructure.modules && courseStructure.elements.length === 0) {
      const elements: LessonElement[] = [];
      let order = 0;

      generatedStructure.modules.forEach((module) => {
        // Add module header
        elements.push({
          id: `module-${module.id}`,
          type: 'module',
          title: module.title,
          content: module.description,
          moduleId: module.id,
          order: order++,
          duration: 0,
          isComplete: false
        });

        // Add lessons with proper titles
        module.lessons.forEach((lesson) => {
          const script = courseScripts[module.id]?.[lesson.id] || '';
          const hasScript = script.length > 0;
          const estimatedDuration = lesson.duration || Math.ceil(script.length / 200);
          
          elements.push({
            id: `lesson-${module.id}-${lesson.id}`,
            type: 'lesson',
            title: lesson.title,
            content: script,
            moduleId: module.id,
            lessonId: lesson.id,
            order: order++,
            duration: estimatedDuration,
            hasScript,
            hasVoice: false, // Will be checked later
            assignedMedia: [],
            isComplete: false
          });
        });
      });

      setCourseStructure(prev => ({
        ...prev,
        elements,
        estimatedDuration: elements.reduce((total, el) => total + (el.duration || 0), 0)
      }));
    }
  }, [generatedStructure, courseScripts]);

  // Check voice availability for lessons
  useEffect(() => {
    const checkVoiceAvailability = async () => {
      const updatedElements = [...courseStructure.elements];
      let hasUpdates = false;

      for (const element of updatedElements) {
        if (element.type === 'lesson' && element.moduleId && element.lessonId) {
          try {
            const response = await fetch(`/api/ai/voice-status/${element.moduleId}/${element.lessonId}`);
            if (response.ok) {
              const data = await response.json();
              if (element.hasVoice !== data.exists) {
                element.hasVoice = data.exists;
                element.isComplete = element.hasScript && element.hasVoice && (element.assignedMedia?.length || 0) > 0;
                hasUpdates = true;
              }
            }
          } catch (error) {
            console.log('Voice check failed for', element.id);
          }
        }
      }

      if (hasUpdates) {
        setCourseStructure(prev => ({ ...prev, elements: updatedElements }));
      }
    };

    if (courseStructure.elements.length > 0) {
      checkVoiceAvailability();
    }
  }, [courseStructure.elements.length]);

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const { source, destination, type } = result;

    if (type === 'LESSON') {
      // Reorder lesson elements
      const items = Array.from(courseStructure.elements);
      const [reorderedItem] = items.splice(source.index, 1);
      items.splice(destination.index, 0, reorderedItem);

      const updatedItems = items.map((item, index) => ({
        ...item,
        order: index
      }));

      setCourseStructure(prev => ({ ...prev, elements: updatedItems }));
      onStructureChange?.({ ...courseStructure, elements: updatedItems });
    }
  };

  const calculateMediaDuration = (mediaItems: MediaItem[]) => {
    return mediaItems.reduce((total, media) => {
      // For videos, use actual duration; for images, assume 3-5 seconds display time
      if (media.type.startsWith('video') && media.duration) {
        return total + media.duration;
      } else if (media.type.startsWith('image')) {
        return total + 4; // 4 seconds per image
      } else {
        return total + 2; // 2 seconds for other media types
      }
    }, 0);
  };

  const handleMediaDrop = (lessonId: string, mediaItem: MediaItem) => {
    const updatedElements = courseStructure.elements.map(element => {
      if (element.id === lessonId) {
        const assignedMedia = [...(element.assignedMedia || [])];
        if (!assignedMedia.find(m => m.id === mediaItem.id)) {
          assignedMedia.push(mediaItem);
        }
        
        // Calculate new duration based on assigned media
        const mediaDuration = calculateMediaDuration(assignedMedia);
        const scriptDuration = element.content ? Math.ceil(element.content.length / 200) : 0;
        const totalDuration = Math.max(mediaDuration, scriptDuration);
        
        return {
          ...element,
          assignedMedia,
          duration: totalDuration,
          isComplete: element.hasScript && element.hasVoice && assignedMedia.length > 0
        };
      }
      return element;
    });

    // Update total course duration
    const totalDuration = updatedElements.reduce((total, el) => total + (el.duration || 0), 0);
    const updatedStructure = { ...courseStructure, elements: updatedElements, estimatedDuration: totalDuration };

    setCourseStructure(updatedStructure);
    onStructureChange?.(updatedStructure);

    toast({
      title: "Media assigned successfully",
      description: `${mediaItem.name} has been assigned to the lesson. Duration updated to ${Math.ceil(calculateMediaDuration([mediaItem]))} seconds.`
    });
  };

  const removeMedia = (lessonId: string, mediaId: number) => {
    const updatedElements = courseStructure.elements.map(element => {
      if (element.id === lessonId) {
        const assignedMedia = element.assignedMedia?.filter(m => m.id !== mediaId) || [];
        
        // Recalculate duration after removing media
        const mediaDuration = calculateMediaDuration(assignedMedia);
        const scriptDuration = element.content ? Math.ceil(element.content.length / 200) : 0;
        const totalDuration = Math.max(mediaDuration, scriptDuration);
        
        return {
          ...element,
          assignedMedia,
          duration: totalDuration,
          isComplete: element.hasScript && element.hasVoice && assignedMedia.length > 0
        };
      }
      return element;
    });

    // Update total course duration
    const totalDuration = updatedElements.reduce((total, el) => total + (el.duration || 0), 0);
    const updatedStructure = { ...courseStructure, elements: updatedElements, estimatedDuration: totalDuration };

    setCourseStructure(updatedStructure);
    onStructureChange?.(updatedStructure);

    toast({
      title: "Media removed",
      description: "Media file has been removed from the lesson and duration updated."
    });
  };

  const generateLessonVideo = async (element: LessonElement) => {
    if (!element.hasScript || !element.hasVoice || !element.assignedMedia?.length) {
      toast({
        title: "Prerequisites missing",
        description: "Lesson needs script, voice, and media to generate video.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const response = await apiRequest('POST', '/api/ai/generate-lesson-video', {
        moduleId: element.moduleId,
        lessonId: element.lessonId,
        title: element.title,
        script: element.content,
        media: element.assignedMedia,
        includeSubtitles: true
      });

      if (!response.ok) throw new Error('Failed to start video generation');

      const { jobId } = await response.json();

      // Poll for progress
      const pollProgress = setInterval(async () => {
        try {
          const statusResponse = await fetch(`/api/ai/video-status/${jobId}`);
          if (statusResponse.ok) {
            const status = await statusResponse.json();
            setGenerationProgress(status.progress || 0);

            if (status.status === 'completed') {
              clearInterval(pollProgress);
              setIsGenerating(false);
              setGenerationProgress(100);
              
              toast({
                title: "Video generated successfully",
                description: `Lesson video for "${element.title}" is ready.`
              });

              // Update element status
              const updatedElements = courseStructure.elements.map(el =>
                el.id === element.id ? { ...el, isComplete: true } : el
              );
              setCourseStructure(prev => ({ ...prev, elements: updatedElements }));
            } else if (status.status === 'failed') {
              clearInterval(pollProgress);
              setIsGenerating(false);
              throw new Error(status.error || 'Video generation failed');
            }
          }
        } catch (error) {
          console.error('Progress check failed:', error);
        }
      }, 2000);

    } catch (error) {
      setIsGenerating(false);
      console.error('Video generation error:', error);
      toast({
        title: "Video generation failed",
        description: "Please try again or check your content.",
        variant: "destructive"
      });
    }
  };

  const generateAllVideos = async () => {
    const lessonsToGenerate = courseStructure.elements.filter(
      el => el.type === 'lesson' && el.hasScript && el.hasVoice && el.assignedMedia?.length && !el.isComplete
    );

    if (lessonsToGenerate.length === 0) {
      toast({
        title: "No lessons ready",
        description: "All lessons need script, voice, and media to generate videos.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    
    for (const lesson of lessonsToGenerate) {
      await generateLessonVideo(lesson);
    }
    
    setIsGenerating(false);
  };

  const ElementCard = ({ element, index }: { element: LessonElement; index: number }) => (
    <Draggable draggableId={element.id} index={index}>
      {(provided, snapshot) => (
        <Card
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`mb-4 transition-all duration-200 ${
            snapshot.isDragging ? 'shadow-lg rotate-1' : 'hover:shadow-md'
          } ${element.isComplete ? 'border-green-200 bg-green-50' : ''}`}
        >
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div {...provided.dragHandleProps} className="cursor-grab active:cursor-grabbing">
                  <GripVertical className="h-4 w-4 text-gray-400" />
                </div>
                {element.type === 'module' ? (
                  <FileText className="h-5 w-5 text-blue-500" />
                ) : (
                  <Video className="h-5 w-5 text-purple-500" />
                )}
                <CardTitle className="text-sm font-medium">{element.title}</CardTitle>
              </div>
              
              <div className="flex items-center gap-2">
                {element.type === 'lesson' && (
                  <>
                    {element.hasScript && <Badge variant="secondary" className="text-xs">Script</Badge>}
                    {element.hasVoice && <Badge variant="secondary" className="text-xs">Voice</Badge>}
                    {element.assignedMedia?.length ? (
                      <Badge variant="secondary" className="text-xs">
                        {element.assignedMedia.length} Media
                      </Badge>
                    ) : null}
                    {element.isComplete ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-amber-500" />
                    )}
                  </>
                )}
                {element.duration && (
                  <Badge variant="outline" className="text-xs">
                    <Clock className="h-3 w-3 mr-1" />
                    {element.duration}min
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>

          {element.type === 'lesson' && (
            <CardContent>
              <div className="space-y-3">
                {/* Script Preview */}
                {element.content && (
                  <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                    {element.content.substring(0, 100)}...
                  </div>
                )}

                {/* Enhanced Media Assignment Area */}
                <div 
                  className="border-2 border-dashed border-gray-200 rounded-lg p-4 min-h-[120px] transition-all duration-200 hover:border-gray-300 group"
                  onDragOver={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    e.currentTarget.classList.add('border-blue-400', 'bg-blue-50', 'scale-105');
                  }}
                  onDragLeave={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // Only remove styles if leaving the drop zone entirely
                    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
                      e.currentTarget.classList.remove('border-blue-400', 'bg-blue-50', 'scale-105');
                    }
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    e.currentTarget.classList.remove('border-blue-400', 'bg-blue-50', 'scale-105');
                    
                    // Handle file drops
                    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                      handleFileUpload(e.dataTransfer.files);
                      return;
                    }
                    
                    // Handle media item drops
                    if (draggedMedia) {
                      handleMediaDrop(element.id, draggedMedia);
                      setDraggedMedia(null);
                    }
                  }}
                  onClick={() => {
                    setSelectedElement(element.id);
                    setShowMediaLibrary(true);
                  }}
                >
                  {element.assignedMedia?.length ? (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-medium text-gray-600">
                          {element.assignedMedia.length} media file{element.assignedMedia.length !== 1 ? 's' : ''} assigned
                        </span>
                        <Badge variant="outline" className="text-xs">
                          <Clock className="h-3 w-3 mr-1" />
                          ~{calculateMediaDuration(element.assignedMedia)}s
                        </Badge>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {element.assignedMedia.map((media) => (
                          <div
                            key={media.id}
                            className="relative group bg-white border rounded-lg p-2 flex items-center gap-2 shadow-sm hover:shadow-md transition-shadow"
                          >
                            <div className="flex items-center gap-2 min-w-0">
                              {media.type.startsWith('image') ? (
                                <div className="flex-shrink-0">
                                  <ImageIcon className="h-4 w-4 text-blue-500" />
                                </div>
                              ) : media.type.startsWith('video') ? (
                                <div className="flex-shrink-0">
                                  <Video className="h-4 w-4 text-purple-500" />
                                </div>
                              ) : (
                                <div className="flex-shrink-0">
                                  <FileText className="h-4 w-4 text-gray-500" />
                                </div>
                              )}
                              <div className="min-w-0">
                                <span className="text-xs font-medium truncate block" title={media.name}>
                                  {media.name}
                                </span>
                                {media.duration && (
                                  <span className="text-xs text-gray-500">
                                    {media.duration}s
                                  </span>
                                )}
                              </div>
                            </div>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                removeMedia(element.id, media.id);
                              }}
                              className="opacity-0 group-hover:opacity-100 text-red-400 hover:text-red-600 ml-1 flex-shrink-0 transition-opacity"
                              title="Remove media"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-gray-400 py-4">
                      <div className="space-y-2">
                        <Upload className="h-10 w-10 mx-auto text-gray-300 group-hover:text-blue-400 transition-colors" />
                        <div>
                          <p className="text-sm font-medium">Add Media Files</p>
                          <p className="text-xs text-gray-500">
                            Drag files here, click to browse, or select from library
                          </p>
                        </div>
                        <div className="flex flex-wrap justify-center gap-1 text-xs text-gray-400">
                          <span>Images</span>
                          <span>•</span>
                          <span>Videos</span>
                          <span>•</span>
                          <span>Documents</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowMediaLibrary(true)}
                    className="flex items-center gap-1"
                  >
                    <Plus className="h-3 w-3" />
                    Add Media
                  </Button>
                  
                  {element.hasScript && element.hasVoice && element.assignedMedia?.length ? (
                    <Button
                      size="sm"
                      onClick={() => generateLessonVideo(element)}
                      disabled={isGenerating}
                      className="flex items-center gap-1"
                    >
                      {isGenerating ? (
                        <div className="h-3 w-3 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      ) : (
                        <Play className="h-3 w-3" />
                      )}
                      Generate Video
                    </Button>
                  ) : (
                    <Button size="sm" variant="ghost" disabled className="text-gray-400">
                      {!element.hasScript ? 'Need Script' : 
                       !element.hasVoice ? 'Need Voice' : 'Need Media'}
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      )}
    </Draggable>
  );

  // Enhanced media upload with file selection
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const supportedMediaTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'video/mp4', 'video/webm', 'video/quicktime',
    'application/pdf', 'text/plain'
  ];

  const handleFileUpload = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const validFiles = Array.from(files).filter(file => 
      supportedMediaTypes.includes(file.type)
    );

    if (validFiles.length === 0) {
      toast({
        title: "Invalid file types",
        description: "Please upload images (JPG, PNG, GIF, WebP), videos (MP4, WebM, MOV), or documents (PDF, TXT).",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      for (let i = 0; i < validFiles.length; i++) {
        const file = validFiles[i];
        const formData = new FormData();
        formData.append('file', file);

        const response = await apiRequest('POST', '/api/media/upload', formData);
        
        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        setUploadProgress(((i + 1) / validFiles.length) * 100);
      }

      // Refresh media library
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });

      toast({
        title: "Upload successful",
        description: `Successfully uploaded ${validFiles.length} file(s).`
      });

    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload failed",
        description: "Some files failed to upload. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleMediaSelect = (media: MediaItem, lessonId?: string) => {
    if (lessonId) {
      handleMediaDrop(lessonId, media);
    } else if (selectedElement) {
      handleMediaDrop(selectedElement, media);
    }
    setShowMediaLibrary(false);
  };

  const MediaLibraryDialog = () => (
    <Dialog open={showMediaLibrary} onOpenChange={setShowMediaLibrary}>
      <DialogContent className="max-w-5xl max-h-[85vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle>Media Library</DialogTitle>
          <p className="text-sm text-gray-600">Upload new media or select existing files to assign to lessons</p>
        </DialogHeader>
        
        {/* Upload Section */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4">
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*,video/*,.pdf,.txt"
            onChange={(e) => handleFileUpload(e.target.files)}
            className="hidden"
          />
          
          {isUploading ? (
            <div className="space-y-2">
              <div className="h-8 w-8 animate-spin rounded-full border-2 border-blue-500 border-t-transparent mx-auto" />
              <p className="text-sm text-gray-600">Uploading files...</p>
              <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
            </div>
          ) : (
            <div 
              className="space-y-2 cursor-pointer"
              onClick={() => fileInputRef.current?.click()}
              onDragOver={(e) => e.preventDefault()}
              onDrop={(e) => {
                e.preventDefault();
                handleFileUpload(e.dataTransfer.files);
              }}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <p className="text-lg font-medium">Upload Media Files</p>
                <p className="text-sm text-gray-500">
                  Drag and drop files here or click to browse
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Supports: Images, Videos, PDFs, Text files
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Media Grid */}
        <div className="flex-1 overflow-y-auto">
          {mediaLibrary.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <ImageIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
              <p>No media files uploaded yet</p>
              <p className="text-sm">Upload some files to get started</p>
            </div>
          ) : (
            <div className="grid grid-cols-4 gap-4">
              {mediaLibrary.map((media) => (
                <div
                  key={media.id}
                  className="group border rounded-lg p-3 cursor-pointer hover:shadow-lg transition-all hover:border-blue-300"
                  draggable
                  onDragStart={() => setDraggedMedia(media)}
                  onDragEnd={() => setDraggedMedia(null)}
                  onClick={() => handleMediaSelect(media)}
                >
                  <div className="aspect-video bg-gray-100 rounded mb-2 flex items-center justify-center relative overflow-hidden">
                    {media.url && media.type.startsWith('image') ? (
                      <img 
                        src={media.url} 
                        alt={media.name}
                        className="w-full h-full object-cover"
                      />
                    ) : media.type.startsWith('video') ? (
                      <div className="relative w-full h-full bg-gray-200 flex items-center justify-center">
                        <Video className="h-8 w-8 text-gray-500" />
                        <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                          <PlayCircle className="h-6 w-6 text-white" />
                        </div>
                      </div>
                    ) : (
                      <FileText className="h-8 w-8 text-gray-400" />
                    )}
                    
                    {/* Selection indicator */}
                    <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="bg-blue-500 text-white rounded-full p-1">
                        <Plus className="h-3 w-3" />
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm font-medium truncate" title={media.name}>
                      {media.name}
                    </p>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="text-xs">
                        {media.type.split('/')[0]}
                      </Badge>
                      {media.duration && (
                        <span className="text-xs text-gray-500">
                          {media.duration}s
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Course Assembly Studio</h1>
          <p className="text-gray-600">Organize your course structure and assign media to create professional video lessons</p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowMediaLibrary(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Media Library
          </Button>
          <Button onClick={generateAllVideos} disabled={isGenerating}>
            {isGenerating ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent mr-2" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Generate All Videos
          </Button>
        </div>
      </div>

      {isGenerating && (
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent" />
            <span className="text-sm font-medium">Generating videos...</span>
          </div>
          <Progress value={generationProgress} className="w-full" />
        </div>
      )}

      <DragDropContext onDragEnd={handleDragEnd}>
        <Droppable droppableId="course-elements" type="LESSON">
          {(provided) => (
            <div
              {...provided.droppableProps}
              ref={provided.innerRef}
              className="space-y-4"
            >
              {courseStructure.elements.map((element, index) => (
                <ElementCard key={element.id} element={element} index={index} />
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      <MediaLibraryDialog />

      {courseStructure.elements.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">No course content found</h3>
          <p className="text-gray-500">Generate your course structure first, then return here to assemble your videos.</p>
        </div>
      )}
    </div>
  );
}