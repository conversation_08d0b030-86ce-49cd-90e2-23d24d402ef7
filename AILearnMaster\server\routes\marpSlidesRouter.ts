
import { Router } from 'express';
import { authenticateToken } from '../middleware/auth';
import { marpAIGenerator } from '../services/marpAIGenerator';
import { z } from 'zod';

const router = Router();

// Schema for Marp slide generation
const generateSlidesSchema = z.object({
  markdown: z.string(),
  format: z.enum(['html', 'pdf', 'pptx']).default('html'),
  courseId: z.number().optional(),
  lessonId: z.number().optional()
});

const generateFromLessonSchema = z.object({
  theme: z.enum(['default', 'gaia', 'uncover']).default('default'),
  includeImages: z.boolean().default(false),
  includeCode: z.boolean().default(false),
  maxSlidesPerLesson: z.number().min(1).max(10).default(5),
  slidesPerPoint: z.number().min(1).max(3).default(1)
});

const generateFromCourseSchema = z.object({
  theme: z.enum(['default', 'gaia', 'uncover']).default('default'),
  includeImages: z.boolean().default(false),
  includeCode: z.boolean().default(false),
  maxSlidesPerLesson: z.number().min(1).max(10).default(5),
  slidesPerPoint: z.number().min(1).max(3).default(1)
});

// Process Marp slides (existing endpoint from MarpSlideEditor)
router.post('/marp-slides', authenticateToken, async (req, res) => {
  try {
    const { markdown, format } = generateSlidesSchema.parse(req.body);
    
    if (!markdown?.trim()) {
      return res.status(400).json({ 
        success: false, 
        message: 'Markdown content is required' 
      });
    }

    // Process slides with Marp
    const result = await marpAIGenerator.processMarpSlides(markdown, format);
    
    res.json({
      success: true,
      url: result.url,
      filename: result.filename,
      format
    });
  } catch (error) {
    console.error('Error processing Marp slides:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to process slides',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate slides from lesson content
router.post('/courses/:courseId/lessons/:lessonId/generate-slides', authenticateToken, async (req, res) => {
  try {
    const courseId = parseInt(req.params.courseId);
    const lessonId = parseInt(req.params.lessonId);
    const userId = req.user.id;
    const config = generateFromLessonSchema.parse(req.body);

    if (isNaN(courseId) || isNaN(lessonId)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid course or lesson ID' 
      });
    }

    const result = await marpAIGenerator.generateSlidesFromLesson(
      courseId, 
      lessonId, 
      userId, 
      config
    );

    res.json({
      success: true,
      markdown: result.markdown,
      previewHtml: result.previewHtml
    });
  } catch (error) {
    console.error('Error generating slides from lesson:', error);
    res.status(500).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to generate slides from lesson'
    });
  }
});

// Generate slides from entire course
router.post('/courses/:courseId/generate-marp-slides', authenticateToken, async (req, res) => {
  try {
    const courseId = parseInt(req.params.courseId);
    const userId = req.user.id;
    const config = generateFromCourseSchema.parse(req.body);

    if (isNaN(courseId)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid course ID' 
      });
    }

    const markdown = await marpAIGenerator.generateSlidesFromCourse(
      courseId, 
      userId, 
      config
    );

    res.json({
      success: true,
      markdown,
      slideCount: (markdown.match(/^---$/gm) || []).length
    });
  } catch (error) {
    console.error('Error generating course slides:', error);
    res.status(500).json({ 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to generate course slides'
    });
  }
});

export default router;
