#!/usr/bin/env node
/**
 * Modal A100 GPU Client Test
 * Tests HTTP API integration between Replit and Modal A100 GPU backend
 */

import axios from 'axios';

// Configuration
const REPLIT_BASE_URL = 'http://localhost:5000'; // Your Replit server
const MODAL_BASE_URL = 'https://koursia-a100-gpu.modal.run'; // Modal A100 GPU endpoint

/**
 * Test Replit API endpoints that proxy to Modal
 */
async function testReplitModalIntegration() {
  console.log('=== Testing Replit → Modal A100 GPU Integration ===\n');

  try {
    // Test 1: Health check via Replit
    console.log('1. Testing Modal health check via Replit...');
    try {
      const healthResponse = await axios.get(`${REPLIT_BASE_URL}/api/modal/health`, {
        timeout: 30000
      });
      
      console.log('✓ Health check successful');
      console.log('Response:', JSON.stringify(healthResponse.data, null, 2));
    } catch (error) {
      console.log('✗ Health check failed:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Echo test via Replit
    console.log('2. Testing GPU echo via Replit...');
    try {
      const echoResponse = await axios.post(`${REPLIT_BASE_URL}/api/modal/echo`, {
        message: 'Hello from Koursia Platform via Replit!'
      }, {
        timeout: 30000,
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✓ Echo test successful');
      console.log('Response:', JSON.stringify(echoResponse.data, null, 2));
    } catch (error) {
      console.log('✗ Echo test failed:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Image generation via Replit
    console.log('3. Testing GPU image generation via Replit...');
    try {
      const imageResponse = await axios.post(`${REPLIT_BASE_URL}/api/modal/generate-image`, {
        prompt: 'A futuristic AI learning platform with holographic interfaces',
        width: 512,
        height: 512,
        num_inference_steps: 20
      }, {
        timeout: 120000, // 2 minutes for image generation
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✓ Image generation successful');
      console.log('Image details:', {
        status: imageResponse.data.result?.status,
        prompt: imageResponse.data.result?.prompt,
        dimensions: imageResponse.data.result?.dimensions,
        inference_steps: imageResponse.data.result?.inference_steps,
        image_size: imageResponse.data.result?.image_base64?.length || 0
      });
    } catch (error) {
      console.log('✗ Image generation failed:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: Text analysis via Replit
    console.log('4. Testing GPU text analysis via Replit...');
    try {
      const textResponse = await axios.post(`${REPLIT_BASE_URL}/api/modal/analyze-text`, {
        text: 'This Course AI Platform is absolutely amazing! I love how it uses A100 GPUs for fast processing.'
      }, {
        timeout: 30000,
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✓ Text analysis successful');
      console.log('Response:', JSON.stringify(textResponse.data, null, 2));
    } catch (error) {
      console.log('✗ Text analysis failed:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('Integration test failed:', error.message);
  }
}

/**
 * Test direct Modal endpoints (when available)
 */
async function testDirectModalEndpoints() {
  console.log('\n=== Testing Direct Modal A100 GPU Endpoints ===\n');

  try {
    // Test 1: Direct health check
    console.log('1. Testing direct Modal health endpoint...');
    try {
      const healthResponse = await axios.get(`${MODAL_BASE_URL}/health`, {
        timeout: 30000
      });
      
      console.log('✓ Direct health check successful');
      console.log('Response:', JSON.stringify(healthResponse.data, null, 2));
    } catch (error) {
      console.log('✗ Direct health check failed:', error.response?.data || error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Direct echo test
    console.log('2. Testing direct Modal echo endpoint...');
    try {
      const echoResponse = await axios.post(`${MODAL_BASE_URL}/api_echo`, {
        message: 'Direct test from Replit client!'
      }, {
        timeout: 30000,
        headers: { 'Content-Type': 'application/json' }
      });
      
      console.log('✓ Direct echo test successful');
      console.log('Response:', JSON.stringify(echoResponse.data, null, 2));
    } catch (error) {
      console.log('✗ Direct echo test failed:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('Direct Modal test failed:', error.message);
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('Course AI Platform - Modal A100 GPU Integration Test\n');
  
  // Test Replit → Modal integration
  await testReplitModalIntegration();
  
  // Test direct Modal endpoints
  await testDirectModalEndpoints();
  
  console.log('\n=== Test Summary ===');
  console.log('Integration tests completed.');
  console.log('Check the results above to verify Modal A100 GPU connectivity.');
  console.log('\nNext steps:');
  console.log('1. If Modal endpoints are unavailable, deploy Modal app using: python3 deploy_modal_a100.py');
  console.log('2. Update MODAL_GPU_BASE_URL environment variable with actual Modal endpoint');
  console.log('3. Integrate Modal GPU functions into your course generation workflows');
}

// Run tests
main().catch(console.error);