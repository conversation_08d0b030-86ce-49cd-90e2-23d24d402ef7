import { Request, Response } from 'express';
import OpenAI from 'openai';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Configure multer for avatar photo uploads
const upload = multer({
  dest: 'uploads/avatars/',
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only JPG and PNG images are allowed'));
    }
  }
});

export const avatarUpload = upload.single('photo');

interface AvatarCharacteristics {
  gender: string;
  ethnicity: string;
  ageRange: string;
  hairColor: string;
  hairStyle: string;
  eyeColor: string;
  facialFeatures: string[];
  clothingStyle: string;
}

export async function generateAvatar(req: Request, res: Response) {
  try {
    const characteristics: AvatarCharacteristics = req.body;

    // Validate required fields
    if (!characteristics.gender || !characteristics.ethnicity || !characteristics.ageRange) {
      return res.status(400).json({ 
        error: 'Gender, ethnicity, and age range are required' 
      });
    }

    console.log('Generating avatar variations with characteristics:', characteristics);

    // Build detailed prompt for realistic human avatar generation
    const basePrompt = buildRealisticAvatarPrompt(characteristics);
    
    const avatarVariations = [];

    try {
      // Generate OpenAI variation
      console.log('Generating OpenAI avatar variation...');
      const openaiPrompt = basePrompt + " Professional headshot photography style, studio lighting, natural expression.";
      
      const openaiResponse = await openai.images.generate({
        model: "dall-e-3",
        prompt: openaiPrompt,
        n: 1,
        size: "1024x1024",
        quality: "hd",
        style: "natural"
      });

      if (openaiResponse.data?.[0]?.url) {
        avatarVariations.push({
          id: 'openai-variation',
          imageUrl: openaiResponse.data[0].url,
          source: 'OpenAI DALL-E 3',
          style: 'Professional Photography',
          description: 'High-quality realistic human portrait with professional studio lighting'
        });
        console.log('OpenAI avatar variation generated successfully');
      }

    } catch (openaiError: any) {
      console.log('OpenAI generation failed:', openaiError?.message);
    }

    try {
      // Generate second OpenAI variation with different style
      console.log('Generating second OpenAI avatar variation...');
      const openaiPrompt2 = basePrompt + " Cinematic portrait style, soft natural lighting, warm tone, realistic human features.";
      
      const openaiResponse2 = await openai.images.generate({
        model: "dall-e-3",
        prompt: openaiPrompt2,
        n: 1,
        size: "1024x1024",
        quality: "hd",
        style: "natural"
      });

      if (openaiResponse2.data?.[0]?.url) {
        avatarVariations.push({
          id: 'openai-variation-2',
          imageUrl: openaiResponse2.data[0].url,
          source: 'OpenAI DALL-E 3',
          style: 'Cinematic Portrait',
          description: 'Realistic human portrait with cinematic lighting and warm tones'
        });
        console.log('Second OpenAI avatar variation generated successfully');
      }

    } catch (openaiError2: any) {
      console.log('Second OpenAI generation failed:', openaiError2?.message);
    }

    // If we have at least one variation, return them
    if (avatarVariations.length > 0) {
      const avatarData = {
        variations: avatarVariations,
        characteristics,
        createdAt: new Date().toISOString(),
        optimizedForAnimation: true,
        totalVariations: avatarVariations.length
      };

      return res.status(200).json({
        variations: avatarVariations,
        avatarData,
        message: `${avatarVariations.length} realistic avatar variations generated successfully`
      });
    } else {
      throw new Error('Failed to generate any avatar variations');
    }

  } catch (error: any) {
    console.error('Error generating avatar variations:', error);
    return res.status(500).json({ 
      error: 'Failed to generate avatar variations',
      details: error.message 
    });
  }
}

export async function processAvatarPhoto(req: Request, res: Response) {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No photo uploaded' });
    }

    console.log('Processing uploaded photo:', req.file.filename);

    const photoPath = req.file.path;
    
    try {
      // Read the uploaded image
      const imageBuffer = fs.readFileSync(photoPath);
      const base64Image = imageBuffer.toString('base64');

      // Use OpenAI Vision to analyze the photo
      const analysisResponse = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: "Analyze this photo and describe the person's facial features in detail for creating an avatar. Focus on: facial structure, skin tone, hair color and style, eye color, distinctive features, and overall appearance. Be specific and detailed for avatar generation."
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:image/jpeg;base64,${base64Image}`
                }
              }
            ],
          },
        ],
        max_tokens: 500,
      });

      const analysis = analysisResponse.choices[0].message.content;
      console.log('Photo analysis completed');

      // Generate avatar based on analysis
      const avatarPrompt = `Create a professional avatar portrait based on this description: ${analysis}. 
      Style: Professional headshot, clean background, optimized for animation, high quality, realistic human portrait, 
      suitable for educational content, front-facing, neutral expression, professional lighting.`;

      let avatarUrl;

      try {
        // Generate avatar using DALL-E
        const avatarResponse = await openai.images.generate({
          model: "dall-e-3",
          prompt: avatarPrompt,
          n: 1,
          size: "1024x1024",
          quality: "hd",
          style: "natural"
        });

        avatarUrl = avatarResponse.data?.[0]?.url;
        console.log('Avatar generated from photo successfully');

      } catch (avatarError: any) {
        console.log('Avatar generation failed:', avatarError?.message);
        throw new Error('Failed to generate avatar from photo');
      }

      // Clean up uploaded file
      fs.unlinkSync(photoPath);

      const avatarData = {
        avatarUrl,
        sourceAnalysis: analysis,
        createdAt: new Date().toISOString(),
        optimizedForAnimation: true,
        sourceType: 'photo'
      };

      return res.status(200).json({
        avatarUrl,
        avatarData,
        analysis,
        message: 'Avatar created from photo successfully'
      });

    } catch (processingError: any) {
      // Clean up uploaded file on error
      if (fs.existsSync(photoPath)) {
        fs.unlinkSync(photoPath);
      }
      throw processingError;
    }

  } catch (error: any) {
    console.error('Error processing avatar photo:', error);
    return res.status(500).json({ 
      error: 'Failed to process photo',
      details: error.message 
    });
  }
}

function buildRealisticAvatarPrompt(characteristics: AvatarCharacteristics): string {
  let prompt = "Ultra-realistic photographic portrait of a ";
  
  // Age and gender
  prompt += `${characteristics.ageRange.replace('-', ' to ')} year old `;
  if (characteristics.gender && characteristics.gender !== 'prefer-not-to-specify') {
    prompt += `${characteristics.gender} `;
  }
  
  // Ethnicity
  if (characteristics.ethnicity) {
    const ethnicityMap: Record<string, string> = {
      'east-asian': 'East Asian',
      'african': 'African',
      'middle-eastern': 'Middle Eastern',
      'european': 'European',
      'hispanic': 'Hispanic',
      'south-asian': 'South Asian',
      'mixed': 'mixed heritage'
    };
    prompt += `human of ${ethnicityMap[characteristics.ethnicity] || characteristics.ethnicity} descent `;
  }
  
  // Hair
  if (characteristics.hairColor && characteristics.hairStyle) {
    prompt += `with ${characteristics.hairStyle} ${characteristics.hairColor} hair `;
  } else if (characteristics.hairColor) {
    prompt += `with ${characteristics.hairColor} hair `;
  } else if (characteristics.hairStyle) {
    prompt += `with ${characteristics.hairStyle} hair `;
  }
  
  // Eyes
  if (characteristics.eyeColor) {
    prompt += `and ${characteristics.eyeColor} eyes `;
  }
  
  // Facial features
  if (characteristics.facialFeatures && characteristics.facialFeatures.length > 0) {
    prompt += `with ${characteristics.facialFeatures.join(', ')} `;
  }
  
  // Clothing
  if (characteristics.clothingStyle) {
    const clothingMap: Record<string, string> = {
      'business': 'business professional attire',
      'casual': 'casual clothing',
      'smart-casual': 'smart casual attire',
      'academic': 'academic professional attire',
      'creative': 'creative professional attire'
    };
    prompt += `wearing ${clothingMap[characteristics.clothingStyle] || characteristics.clothingStyle} `;
  }
  
  // Enhanced technical specifications for ultra-realistic human avatars
  prompt += ". Photorealistic human features, natural skin texture, authentic facial expressions, ";
  prompt += "professional headshot composition, clean neutral background, front-facing pose, ";
  prompt += "natural lighting, high-definition quality, human-like appearance, ";
  prompt += "suitable for professional video content and animation, realistic proportions, ";
  prompt += "detailed facial features, natural color grading, studio photography quality.";
  
  return prompt;
}

export async function getAvatarGallery(req: Request, res: Response) {
  try {
    // In a real implementation, this would fetch from a database
    // For now, return some sample avatar data
    const sampleAvatars = [
      {
        id: '1',
        imageUrl: 'https://example.com/avatar1.jpg',
        characteristics: {
          gender: 'female',
          ethnicity: 'european',
          ageRange: '30-40',
          clothingStyle: 'business'
        }
      },
      // Add more sample avatars as needed
    ];

    return res.status(200).json({
      avatars: sampleAvatars,
      total: sampleAvatars.length
    });

  } catch (error: any) {
    console.error('Error fetching avatar gallery:', error);
    return res.status(500).json({ 
      error: 'Failed to fetch avatar gallery' 
    });
  }
}