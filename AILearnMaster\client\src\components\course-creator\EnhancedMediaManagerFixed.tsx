import React, { useState, useRef, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Upload, 
  Search, 
  Image, 
  Video, 
  Download, 
  Plus, 
  Trash2, 
  Eye,
  ExternalLink,
  Grid3X3,
  List,
  CheckCircle2,
  AlertCircle,
  RefreshCw,
  X,
  Heart,
  HeartOff,
  Filter,
  SortAsc,
  Loader2,
  ImageIcon,
  PlayCircle,
  ZoomIn,
  Star,
  Archive
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import AutoImportStockMedia from './AutoImportStockMedia';

interface MediaItem {
  id: string;
  name: string;
  title?: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnailUrl?: string;
  size: number;
  uploadedAt: string;
  source: 'upload' | 'pexels' | 'pixabay';
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    tags?: string[];
    author?: string;
  };
}

interface StockMedia {
  id: string;
  name?: string;
  type: 'photo' | 'video';
  url: string;
  thumbnailUrl: string;
  title?: string;
  tags?: string;
  author?: string;
  source: 'pexels' | 'pixabay';
  width?: number;
  height?: number;
  duration?: number;
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    tags?: string[];
    author?: string;
  };
}

interface EnhancedMediaManagerProps {
  onBack: () => void;
  onNext: () => void;
}

export function EnhancedMediaManager({ onBack, onNext }: EnhancedMediaManagerProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State management
  const [activeTab, setActiveTab] = useState('library');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [lightboxMedia, setLightboxMedia] = useState<MediaItem | StockMedia | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Fetch user's media library
  const { data: mediaLibrary = [], isLoading: isLoadingLibrary, error: libraryError } = useQuery({
    queryKey: ['/api/media'],
    refetchOnWindowFocus: false,
  });

  // Upload file mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100;
            setUploadProgress(progress);
          }
        });
        
        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            resolve(JSON.parse(xhr.responseText));
          } else {
            reject(new Error('Upload failed'));
          }
        });
        
        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed'));
        });
        
        xhr.open('POST', '/api/media/upload');
        xhr.send(formData);
      });
    },
    onMutate: () => {
      setIsUploading(true);
      setUploadProgress(0);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "Upload Complete",
        description: "Your file has been uploaded successfully.",
      });
      setIsUploading(false);
      setUploadProgress(0);
    },
    onError: (error: any) => {
      toast({
        title: "Upload Failed",
        description: error.message || "Failed to upload file. Please try again.",
        variant: "destructive",
      });
      setIsUploading(false);
      setUploadProgress(0);
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (mediaId: string) => {
      return await apiRequest('DELETE', `/api/media/${mediaId}`, {});
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "File Deleted",
        description: "The file has been removed from your media library.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Delete Failed",
        description: error.message || "Failed to delete file.",
        variant: "destructive",
      });
    },
  });

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      uploadMutation.mutate(files[0]);
    }
  };

  // Handle drag and drop
  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      uploadMutation.mutate(files[0]);
    }
  }, [uploadMutation]);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
  }, []);

  // Open lightbox
  const openLightbox = (media: MediaItem | StockMedia) => {
    setLightboxMedia(media);
    setIsLightboxOpen(true);
  };

  // Filter media based on search and category
  const filteredMedia = (mediaLibrary as MediaItem[]).filter((item: MediaItem) => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.type === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-5 w-5" />;
      case 'video':
        return <Video className="h-5 w-5" />;
      default:
        return <ImageIcon className="h-5 w-5" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Media Library</h2>
          <p className="text-muted-foreground">
            Manage your course media, upload files, and discover stock content
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button onClick={onNext}>
            Continue
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="library" className="flex items-center gap-2">
            <Archive className="h-4 w-4" />
            My Library ({(mediaLibrary as MediaItem[]).length})
          </TabsTrigger>
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Upload Files
          </TabsTrigger>
          <TabsTrigger value="stock" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Stock Media
          </TabsTrigger>
        </TabsList>

        {/* My Library Tab */}
        <TabsContent value="library" className="space-y-4">
          {/* Search and Filter Controls */}
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search your media..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="rounded-md border border-input bg-background px-3 py-2 text-sm"
            >
              <option value="all">All Types</option>
              <option value="image">Images</option>
              <option value="video">Videos</option>
              <option value="document">Documents</option>
            </select>
            <Button
              variant="outline"
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
            </Button>
          </div>

          {/* Media Grid/List */}
          {isLoadingLibrary ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="h-6 w-6 animate-spin" />
            </div>
          ) : filteredMedia.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Archive className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Media Found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery ? 'No media matches your search criteria.' : 'Upload some files to get started.'}
                </p>
                <Button onClick={() => setActiveTab('upload')}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Files
                </Button>
              </CardContent>
            </Card>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {filteredMedia.map((item) => (
                <Card key={item.id} className="group cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-0">
                    <div className="aspect-square relative overflow-hidden rounded-t-lg">
                      {item.type === 'image' ? (
                        <img
                          src={item.thumbnailUrl || item.url}
                          alt={item.name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                          onClick={() => openLightbox(item)}
                        />
                      ) : item.type === 'video' ? (
                        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                          <PlayCircle className="h-12 w-12 text-muted-foreground" />
                        </div>
                      ) : (
                        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                          {getFileIcon(item.type)}
                        </div>
                      )}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <div className="flex gap-2">
                          <Button size="sm" variant="secondary" onClick={() => openLightbox(item)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button 
                            size="sm" 
                            variant="destructive"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteMutation.mutate(item.id);
                            }}
                            disabled={deleteMutation.isPending}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                    <div className="p-3">
                      <h4 className="font-medium truncate">{item.name}</h4>
                      <p className="text-sm text-muted-foreground">
                        {formatFileSize(item.size)}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredMedia.map((item) => (
                <Card key={item.id} className="cursor-pointer hover:shadow-sm transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                        {item.type === 'image' ? (
                          <img
                            src={item.thumbnailUrl || item.url}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            {getFileIcon(item.type)}
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium truncate">{item.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {item.type} • {formatFileSize(item.size)}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" onClick={() => openLightbox(item)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => deleteMutation.mutate(item.id)}
                          disabled={deleteMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        {/* Upload Tab */}
        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Files
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors cursor-pointer"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">
                  {isUploading ? 'Uploading...' : 'Drop files here or click to browse'}
                </h3>
                <p className="text-muted-foreground">
                  Supports images, videos, and documents up to 50MB
                </p>
                
                {isUploading && (
                  <div className="mt-4 space-y-2">
                    <Progress value={uploadProgress} className="w-full" />
                    <p className="text-sm text-muted-foreground">
                      {Math.round(uploadProgress)}% complete
                    </p>
                  </div>
                )}
              </div>
              
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileSelect}
                accept="image/*,video/*,.pdf,.doc,.docx,.txt"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Stock Media Tab */}
        <TabsContent value="stock" className="space-y-4">
          <AutoImportStockMedia 
            onMediaImported={(count) => {
              // Refresh media library when stock media is imported
              queryClient.invalidateQueries({ queryKey: ['/api/media'] });
              toast({
                title: "Media Added",
                description: `${count} item(s) automatically added to your library.`
              });
            }}
          />
        </TabsContent>
      </Tabs>

      {/* Lightbox Modal */}
      <Dialog open={isLightboxOpen} onOpenChange={setIsLightboxOpen}>
        <DialogContent className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{lightboxMedia?.name || lightboxMedia?.title || 'Media Preview'}</span>
              <Button variant="outline" size="sm" onClick={() => setIsLightboxOpen(false)}>
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {lightboxMedia && (
            <div className="space-y-4">
              <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                {lightboxMedia.type === 'image' ? (
                  <img
                    src={lightboxMedia.url}
                    alt={lightboxMedia.name || lightboxMedia.title || 'Preview'}
                    className="w-full h-full object-contain"
                  />
                ) : lightboxMedia.type === 'video' ? (
                  <video
                    src={lightboxMedia.url}
                    controls
                    className="w-full h-full"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-center">
                      {getFileIcon(lightboxMedia.type)}
                      <p className="mt-2 text-sm text-muted-foreground">
                        {lightboxMedia.name || lightboxMedia.title}
                      </p>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Type:</span> {lightboxMedia.type}
                </div>
                {('size' in lightboxMedia) && (
                  <div>
                    <span className="font-medium">Size:</span> {formatFileSize(lightboxMedia.size)}
                  </div>
                )}
                {lightboxMedia.metadata?.width && lightboxMedia.metadata?.height && (
                  <div>
                    <span className="font-medium">Dimensions:</span> {lightboxMedia.metadata.width} × {lightboxMedia.metadata.height}
                  </div>
                )}
                {lightboxMedia.source && (
                  <div>
                    <span className="font-medium">Source:</span> {lightboxMedia.source}
                  </div>
                )}
              </div>
              
              <div className="flex gap-2">
                <Button asChild>
                  <a href={lightboxMedia.url} download target="_blank">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </a>
                </Button>
                <Button variant="outline" asChild>
                  <a href={lightboxMedia.url} target="_blank">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Open Original
                  </a>
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}