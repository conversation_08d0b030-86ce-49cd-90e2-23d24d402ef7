import axios from 'axios';

// Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const MODAL_API_TIMEOUT = 300000; // 5 minutes for heavy GPU operations

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: MODAL_API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for debugging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    throw new Error(error.response?.data?.error || error.message);
  }
);

class ApiService {
  // Health check
  async checkHealth() {
    return await apiClient.get('/health');
  }

  // Image generation with Stable Diffusion XL
  async generateImage({
    prompt,
    negativePrompt = '',
    width = 1024,
    height = 1024,
    numInferenceSteps = 30,
    guidanceScale = 7.5,
    batchSize = 1
  }) {
    return await apiClient.post('/gen_image', {
      prompt,
      negative_prompt: negativePrompt,
      width,
      height,
      num_inference_steps: numInferenceSteps,
      guidance_scale: guidanceScale,
      batch_size: batchSize
    });
  }

  // Video generation with SadTalker
  async generateVideo({
    imageBase64,
    audioBase64,
    enhancer = 'gfpgan',
    size = 512,
    expressionScale = 1.0,
    stillMode = true
  }) {
    return await apiClient.post('/gen_video', {
      image_base64: imageBase64,
      audio_base64: audioBase64,
      enhancer,
      size,
      expression_scale: expressionScale,
      still_mode: stillMode
    });
  }

  // Chatterbox TTS
  async generateChatterboxTTS({
    text,
    voice = 'v2/en_speaker_6',
    temperature = 0.7,
    speed = 1.0,
    batchTexts = null
  }) {
    return await apiClient.post('/tts_chatter', {
      text,
      voice,
      temperature,
      speed,
      batch_texts: batchTexts
    });
  }

  // Coqui TTS
  async generateCoquiTTS({
    text,
    voiceModel = 'tts_models/en/vctk/vits',
    speaker = 'p225',
    emotion = 'neutral',
    speed = 1.0,
    batchTexts = null
  }) {
    return await apiClient.post('/tts_coqui', {
      text,
      voice_model: voiceModel,
      speaker,
      emotion,
      speed,
      batch_texts: batchTexts
    });
  }

  // Slide generation with Marp
  async generateSlides({
    markdownContent,
    theme = 'default',
    outputFormat = 'pdf',
    batchSlides = null
  }) {
    return await apiClient.post('/slides', {
      markdown_content: markdownContent,
      theme,
      output_format: outputFormat,
      batch_slides: batchSlides
    });
  }

  // Utility functions
  async fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  }

  downloadBase64File(base64Data, filename, mimeType) {
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: mimeType });
    
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  // Batch processing utilities
  async processBatch(items, processingFunction, batchSize = 5) {
    const results = [];
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await Promise.allSettled(
        batch.map(item => processingFunction(item))
      );
      results.push(...batchResults);
    }
    return results;
  }

  // Cache management
  getCachedResult(key) {
    try {
      const cached = localStorage.getItem(`ai_course_cache_${key}`);
      if (cached) {
        const { data, timestamp } = JSON.parse(cached);
        const now = Date.now();
        const cacheAge = now - timestamp;
        const maxAge = 60 * 60 * 1000; // 1 hour
        
        if (cacheAge < maxAge) {
          return data;
        } else {
          localStorage.removeItem(`ai_course_cache_${key}`);
        }
      }
    } catch (error) {
      console.warn('Cache read error:', error);
    }
    return null;
  }

  setCachedResult(key, data) {
    try {
      const cacheData = {
        data,
        timestamp: Date.now()
      };
      localStorage.setItem(`ai_course_cache_${key}`, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Cache write error:', error);
    }
  }

  generateCacheKey(params) {
    return btoa(JSON.stringify(params)).replace(/[^a-zA-Z0-9]/g, '');
  }
}

export default new ApiService();