import { pgTable, text, serial, integer, timestamp, uniqueIndex, index } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { users } from "./schema";

// AI Credits system
export const aiCredits = pgTable("ai_credits", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  totalCredits: integer("total_credits").notNull().default(100),
  usedCredits: integer("used_credits").notNull().default(0),
  plan: text("plan").notNull().default("free"), // free, starter, pro, business, enterprise
  nextRefill: timestamp("next_refill").notNull(),
  stripeCustomerId: text("stripe_customer_id"),
  stripeSubscriptionId: text("stripe_subscription_id"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => {
  return {
    userIdIdx: uniqueIndex("ai_credits_user_id_idx").on(table.userId),
  };
});

export const aiUsageHistory = pgTable("ai_usage_history", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  feature: text("feature").notNull(), // text, speech, image, video
  credits: integer("credits").notNull(),
  model: text("model").notNull(),
  courseId: integer("course_id"),
  courseName: text("course_name"),
  tokenCount: integer("token_count"),
  characterCount: integer("character_count"),
  imageCount: integer("image_count"),
  prompt: text("prompt"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
}, (table) => {
  return {
    userIdIdx: index("ai_usage_history_user_id_idx").on(table.userId),
    createdAtIdx: index("ai_usage_history_created_at_idx").on(table.createdAt),
  };
});

// Insert schemas
export const insertAiCreditsSchema = createInsertSchema(aiCredits).pick({
  userId: true,
  totalCredits: true,
  usedCredits: true,
  plan: true,
  nextRefill: true,
  stripeCustomerId: true,
  stripeSubscriptionId: true,
});

export const insertAiUsageHistorySchema = createInsertSchema(aiUsageHistory).pick({
  userId: true,
  feature: true,
  credits: true,
  model: true,
  courseId: true,
  courseName: true,
  tokenCount: true,
  characterCount: true,
  imageCount: true,
  prompt: true,
});

// Types
export type InsertAiCredits = z.infer<typeof insertAiCreditsSchema>;
export type InsertAiUsageHistory = z.infer<typeof insertAiUsageHistorySchema>;
export type AiCredits = typeof aiCredits.$inferSelect;
export type AiUsageHistory = typeof aiUsageHistory.$inferSelect;