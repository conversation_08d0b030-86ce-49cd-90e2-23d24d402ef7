import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { marked } from "marked";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  Pencil, 
  Eye, 
  FilePlus, 
  Clock, 
  ChevronRight,
  Wand2, 
  CopyCheck,
  Check,
  Save
} from "lucide-react";

export interface ScriptEditorProps {
  onSave: (scriptData: ScriptData) => void;
  defaultValue?: string | ScriptData;
  onBack: () => void;
}

export interface ScriptData {
  content: string;
  wordCount: number;
  estimatedDuration: number;
}

// Template options for quick starts
const scriptTemplates = [
  {
    name: "Welcome Introduction",
    content: "Welcome to this course! My name is [Your Name], and I'll be your guide through this learning journey. In this course, we'll explore [course subject] and learn valuable skills that you can apply in [relevant context]. By the end of this course, you'll be able to [learning objective]. Let's get started!"
  },
  {
    name: "Topic Overview",
    content: "In this module, we'll be covering [topic name]. This is an important concept because [reason for importance]. By understanding this topic, you'll be able to [benefit]. We'll start with the fundamentals and then progress to more advanced concepts. Let's dive in!"
  },
  {
    name: "Concept Explanation",
    content: "Let me explain what [concept] means. In simple terms, it's [basic definition]. To understand this better, think of it like [analogy or example]. The key components of this concept are: [component 1], [component 2], and [component 3]. This matters because [practical application]."
  },
  {
    name: "Step-by-Step Tutorial",
    content: "Today I'll show you how to [task]. Here's what we'll need: [requirements]. Let's break this down into simple steps:\n\n1. First, [step one]\n2. Next, [step two]\n3. Then, [step three]\n4. Finally, [final step]\n\nAnd that's it! With practice, you'll be able to [outcome] efficiently."
  },
  {
    name: "Summary & Conclusion",
    content: "To summarize what we've learned in this module: we covered [point 1], [point 2], and [point 3]. The key takeaways are: [takeaway 1] and [takeaway 2]. In the next module, we'll build on these concepts to explore [next topic]. Remember to practice what you've learned by [suggested activity]. If you have any questions, please use the course discussion forum. See you in the next module!"
  }
];

export function ScriptEditor({ 
  onSave, 
  defaultValue = "", 
  onBack 
}: ScriptEditorProps) {
  // Initialize script from defaultValue
  const initialScript = typeof defaultValue === 'string' 
    ? defaultValue 
    : defaultValue.content || "";

  const [script, setScript] = useState(initialScript);
  const [activeTab, setActiveTab] = useState<string>("write");
  const [wordCount, setWordCount] = useState(0);
  const [estimatedDuration, setEstimatedDuration] = useState(0);
  const [isCopied, setIsCopied] = useState(false);
  
  const { toast } = useToast();

  // Calculate word count and estimated duration
  useEffect(() => {
    // Count words
    const words = script.trim().split(/\s+/).filter(Boolean).length;
    setWordCount(words);
    
    // Estimate duration (average speaking rate is ~150 words per minute)
    const speakingRate = 150; // words per minute
    const durationInMinutes = words / speakingRate;
    setEstimatedDuration(durationInMinutes);
  }, [script]);

  const handleScriptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setScript(e.target.value);
  };

  const handleSave = () => {
    if (!script.trim()) {
      toast({
        title: "Empty Script",
        description: "Please write some content before saving.",
        variant: "destructive"
      });
      return;
    }
    
    const scriptData: ScriptData = {
      content: script,
      wordCount,
      estimatedDuration
    };
    
    onSave(scriptData);
  };

  const handleTemplateSelect = (templateName: string) => {
    const template = scriptTemplates.find(t => t.name === templateName);
    if (template) {
      if (script && script.trim().length > 0) {
        // Ask for confirmation if there's existing content
        if (window.confirm("This will replace your current script. Are you sure?")) {
          setScript(template.content);
          setActiveTab("write");
        }
      } else {
        setScript(template.content);
        setActiveTab("write");
      }
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(script);
    setIsCopied(true);
    
    toast({
      title: "Copied to clipboard",
      description: "The script has been copied to your clipboard.",
    });
    
    setTimeout(() => setIsCopied(false), 2000);
  };

  // Render markdown preview
  const renderPreview = () => {
    if (!script.trim()) {
      return <p className="text-muted-foreground text-center py-8">No content to preview.</p>;
    }
    
    const html = marked(script);
    return <div dangerouslySetInnerHTML={{ __html: html }} className="prose prose-sm max-w-none dark:prose-invert" />;
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Create Your Script</h2>
        <p className="text-muted-foreground mb-6">
          Write the script that will be narrated by your avatar. You can use the templates to get started or write your own.
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-[2fr,1fr] gap-6">
        <div className="space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-2 mb-4">
              <TabsTrigger value="write" className="flex items-center gap-2">
                <Pencil className="h-4 w-4" />
                <span>Write</span>
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                <span>Preview</span>
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="write" className="space-y-4">
              <Textarea
                value={script}
                onChange={handleScriptChange}
                placeholder="Type your script here... or select a template to get started."
                className="min-h-[400px] font-mono text-sm leading-relaxed"
              />
            </TabsContent>
            
            <TabsContent value="preview" className="p-4 border rounded-md min-h-[400px]">
              {renderPreview()}
            </TabsContent>
          </Tabs>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <span className="text-foreground font-medium">{wordCount}</span> words
              </div>
              <Separator orientation="vertical" className="h-4" />
              <div className="flex items-center gap-1">
                <Clock className="h-3.5 w-3.5" />
                <span className="text-foreground font-medium">
                  {estimatedDuration < 1 
                    ? `${Math.round(estimatedDuration * 60)} sec` 
                    : `${Math.floor(estimatedDuration)}:${Math.round((estimatedDuration % 1) * 60).toString().padStart(2, '0')} min`
                  }
                </span>
              </div>
            </div>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleCopyToClipboard}
              className="gap-1.5"
            >
              {isCopied ? <Check className="h-3.5 w-3.5" /> : <CopyCheck className="h-3.5 w-3.5" />}
              {isCopied ? "Copied" : "Copy"}
            </Button>
          </div>
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardContent className="p-6 space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-1">Script Templates</h3>
                <p className="text-sm text-muted-foreground">
                  Get started quickly with one of these templates
                </p>
              </div>
              
              <div className="space-y-3">
                {scriptTemplates.map((template, index) => (
                  <div 
                    key={index} 
                    className="p-3 border rounded-md cursor-pointer hover:border-primary/50 transition-colors"
                    onClick={() => handleTemplateSelect(template.name)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FilePlus className="h-4 w-4 text-primary" />
                        <h4 className="font-medium text-sm">{template.name}</h4>
                      </div>
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {template.content.substring(0, 100)}...
                    </p>
                  </div>
                ))}
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-lg font-medium mb-1">AI Assistance</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Let AI help you generate a script based on your topic
                </p>
                
                <div className="space-y-3">
                  <Input placeholder="Enter a topic or subject" />
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a tone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="professional">Professional</SelectItem>
                      <SelectItem value="conversational">Conversational</SelectItem>
                      <SelectItem value="friendly">Friendly</SelectItem>
                      <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
                      <SelectItem value="academic">Academic</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button variant="outline" className="w-full gap-2">
                    <Wand2 className="h-4 w-4" /> Generate Script
                  </Button>
                </div>
              </div>
              
              <div className="flex flex-col gap-2 pt-2">
                <Button 
                  variant="default"
                  onClick={handleSave}
                  disabled={!script.trim()}
                >
                  <Save className="h-4 w-4 mr-2" /> Save Script
                </Button>
                <Button 
                  variant="outline"
                  onClick={onBack}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" /> Back
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}