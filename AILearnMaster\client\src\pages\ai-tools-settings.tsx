import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { 
  Settings, 
  Zap, 
  Cloud, 
  Database, 
  AlertCircle, 
  CheckCircle, 
  Loader2,
  ExternalLink,
  RefreshCw,
  Shield,
  Cpu,
  Image,
  Mic,
  Video
} from "lucide-react";

const apiSettingsSchema = z.object({
  runpodApiKey: z.string().optional(),
  awsAccessKeyId: z.string().optional(),
  awsSecretAccessKey: z.string().optional(),
  awsRegion: z.string().default("us-east-1"),
  s3BucketName: z.string().optional(),
});

const endpointSettingsSchema = z.object({
  mistralEndpoint: z.string().url().optional().or(z.literal("")),
  coquiEndpoint: z.string().url().optional().or(z.literal("")),
  kandinskyEndpoint: z.string().url().optional().or(z.literal("")),
  animatediffEndpoint: z.string().url().optional().or(z.literal("")),
});

interface HealthStatus {
  status: string;
  s3: boolean;
  runpod: boolean;
  endpoints: {
    mistral: boolean;
    coqui: boolean;
    kandinsky: boolean;
    animatediff: boolean;
  };
}

export default function AIToolsSettings() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [testing, setTesting] = useState<string | null>(null);

  const apiForm = useForm<z.infer<typeof apiSettingsSchema>>({
    resolver: zodResolver(apiSettingsSchema),
    defaultValues: {
      runpodApiKey: "",
      awsAccessKeyId: "",
      awsSecretAccessKey: "",
      awsRegion: "us-east-1",
      s3BucketName: "",
    },
  });

  const endpointForm = useForm<z.infer<typeof endpointSettingsSchema>>({
    resolver: zodResolver(endpointSettingsSchema),
    defaultValues: {
      mistralEndpoint: "",
      coquiEndpoint: "",
      kandinskyEndpoint: "",
      animatediffEndpoint: "",
    },
  });

  // Fetch API settings
  const { data: apiSettings, isLoading: isLoadingApiSettings } = useQuery({
    queryKey: ["/api/ai-tools/settings/api"],
    queryFn: async () => {
      try {
        const response = await apiRequest("GET", "/api/ai-tools/settings/api");
        const data = await response.json();
        
        // Update form with fetched data
        apiForm.reset(data);
        
        return data;
      } catch (error) {
        console.error("Error fetching API settings:", error);
        return null;
      }
    }
  });

  // Fetch endpoint settings
  const { data: endpointSettings, isLoading: isLoadingEndpointSettings } = useQuery({
    queryKey: ["/api/ai-tools/settings/endpoints"],
    queryFn: async () => {
      try {
        const response = await apiRequest("GET", "/api/ai-tools/settings/endpoints");
        const data = await response.json();
        
        // Update form with fetched data
        endpointForm.reset(data);
        
        return data;
      } catch (error) {
        console.error("Error fetching endpoint settings:", error);
        return null;
      }
    }
  });

  // Fetch health status
  const { data: healthStatus, isLoading: isLoadingHealth } = useQuery<HealthStatus>({
    queryKey: ["/api/ai-tools/health"],
    queryFn: async () => {
      try {
        const response = await apiRequest("GET", "/api/ai-tools/health");
        return await response.json();
      } catch (error) {
        console.error("Error fetching health status:", error);
        return {
          status: "error",
          s3: false,
          runpod: false,
          endpoints: {
            mistral: false,
            coqui: false,
            kandinsky: false,
            animatediff: false
          }
        };
      }
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Save API settings mutation
  const saveApiSettingsMutation = useMutation({
    mutationFn: async (data: z.infer<typeof apiSettingsSchema>) => {
      const response = await apiRequest("POST", "/api/ai-tools/settings/api", data);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Settings Saved",
        description: "API settings have been saved successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/ai-tools/settings/api"] });
      queryClient.invalidateQueries({ queryKey: ["/api/ai-tools/health"] });
    },
    onError: (error) => {
      toast({
        title: "Error Saving Settings",
        description: "There was a problem saving your settings. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Save endpoint settings mutation
  const saveEndpointSettingsMutation = useMutation({
    mutationFn: async (data: z.infer<typeof endpointSettingsSchema>) => {
      const response = await apiRequest("POST", "/api/ai-tools/settings/endpoints", data);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: "Endpoints Saved",
        description: "Endpoint settings have been saved successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ["/api/ai-tools/settings/endpoints"] });
      queryClient.invalidateQueries({ queryKey: ["/api/ai-tools/health"] });
    },
    onError: (error) => {
      toast({
        title: "Error Saving Endpoints",
        description: "There was a problem saving your endpoint settings. Please try again.",
        variant: "destructive",
      });
    }
  });

  // Test endpoint connection
  const testConnection = async (endpoint: string) => {
    setTesting(endpoint);
    
    try {
      const response = await apiRequest("POST", "/api/ai-tools/test-connection", { endpoint });
      
      if (response.ok) {
        toast({
          title: "Connection Successful",
          description: `Successfully connected to ${endpoint}.`,
        });
      } else {
        toast({
          title: "Connection Failed",
          description: `Failed to connect to ${endpoint}. Please check your settings.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Connection Error",
        description: `An error occurred while testing the connection to ${endpoint}.`,
        variant: "destructive",
      });
    } finally {
      setTesting(null);
    }
  };

  const onSubmitApiSettings = (data: z.infer<typeof apiSettingsSchema>) => {
    saveApiSettingsMutation.mutate(data);
  };

  const onSubmitEndpointSettings = (data: z.infer<typeof endpointSettingsSchema>) => {
    saveEndpointSettingsMutation.mutate(data);
  };

  const getStatusIndicator = (isHealthy: boolean, isLoading: boolean = false) => {
    if (isLoading) {
      return <Loader2 className="h-4 w-4 animate-spin text-gray-400" />;
    }
    
    return isHealthy ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <AlertCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusBadge = (isHealthy: boolean) => {
    return (
      <Badge variant={isHealthy ? "default" : "destructive"}>
        {isHealthy ? "Connected" : "Disconnected"}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
          <Settings className="h-8 w-8" />
          AI Tools Configuration
        </h1>
        <p className="text-muted-foreground">
          Configure your AI services and endpoints for enhanced content creation
        </p>
      </div>

      {/* System Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            System Status
          </CardTitle>
          <CardDescription>
            Real-time status of your AI services and integrations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Cloud className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="font-medium">AWS S3</p>
                  <p className="text-sm text-muted-foreground">Media Storage</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIndicator(healthStatus?.s3 || false, isLoadingHealth)}
                {!isLoadingHealth && getStatusBadge(healthStatus?.s3 || false)}
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Cpu className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="font-medium">RunPod</p>
                  <p className="text-sm text-muted-foreground">GPU Computing</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIndicator(healthStatus?.runpod || false, isLoadingHealth)}
                {!isLoadingHealth && getStatusBadge(healthStatus?.runpod || false)}
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Mic className="h-5 w-5 text-green-500" />
                <div>
                  <p className="font-medium">Coqui TTS</p>
                  <p className="text-sm text-muted-foreground">Voice Synthesis</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIndicator(healthStatus?.endpoints?.coqui || false, isLoadingHealth)}
                {!isLoadingHealth && getStatusBadge(healthStatus?.endpoints?.coqui || false)}
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Image className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="font-medium">Kandinsky</p>
                  <p className="text-sm text-muted-foreground">Image Generation</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIndicator(healthStatus?.endpoints?.kandinsky || false, isLoadingHealth)}
                {!isLoadingHealth && getStatusBadge(healthStatus?.endpoints?.kandinsky || false)}
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Video className="h-5 w-5 text-red-500" />
                <div>
                  <p className="font-medium">AnimateDiff</p>
                  <p className="text-sm text-muted-foreground">Video Animation</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIndicator(healthStatus?.endpoints?.animatediff || false, isLoadingHealth)}
                {!isLoadingHealth && getStatusBadge(healthStatus?.endpoints?.animatediff || false)}
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-3">
                <Shield className="h-5 w-5 text-indigo-500" />
                <div>
                  <p className="font-medium">Mistral AI</p>
                  <p className="text-sm text-muted-foreground">Language Model</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIndicator(healthStatus?.endpoints?.mistral || false, isLoadingHealth)}
                {!isLoadingHealth && getStatusBadge(healthStatus?.endpoints?.mistral || false)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Tabs */}
      <Tabs defaultValue="api" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="api">API Configuration</TabsTrigger>
          <TabsTrigger value="endpoints">Service Endpoints</TabsTrigger>
        </TabsList>

        {/* API Configuration */}
        <TabsContent value="api">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                API Configuration
              </CardTitle>
              <CardDescription>
                Configure your cloud service API keys and credentials
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...apiForm}>
                <form onSubmit={apiForm.handleSubmit(onSubmitApiSettings)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={apiForm.control}
                      name="runpodApiKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>RunPod API Key</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter your RunPod API key"
                              type="password"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Your RunPod API key for GPU computing services
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={apiForm.control}
                      name="awsAccessKeyId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>AWS Access Key ID</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter your AWS Access Key ID"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Your AWS access key for S3 storage
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={apiForm.control}
                      name="awsSecretAccessKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>AWS Secret Access Key</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter your AWS Secret Access Key"
                              type="password"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Your AWS secret access key for S3 storage
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={apiForm.control}
                      name="awsRegion"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>AWS Region</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="us-east-1"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            AWS region for your S3 bucket
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={apiForm.control}
                      name="s3BucketName"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2">
                          <FormLabel>S3 Bucket Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="your-bucket-name"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Name of your S3 bucket for media storage
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={saveApiSettingsMutation.isPending}
                    className="w-full md:w-auto"
                  >
                    {saveApiSettingsMutation.isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save API Settings"
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Service Endpoints */}
        <TabsContent value="endpoints">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ExternalLink className="h-5 w-5" />
                Service Endpoints
              </CardTitle>
              <CardDescription>
                Configure endpoints for AI services and external APIs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...endpointForm}>
                <form onSubmit={endpointForm.handleSubmit(onSubmitEndpointSettings)} className="space-y-6">
                  <div className="space-y-6">
                    <FormField
                      control={endpointForm.control}
                      name="mistralEndpoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            Mistral AI Endpoint
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => testConnection("mistral")}
                              disabled={testing === "mistral"}
                            >
                              {testing === "mistral" ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <RefreshCw className="h-3 w-3" />
                              )}
                            </Button>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://api.mistral.ai/v1"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Endpoint URL for Mistral AI language model
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={endpointForm.control}
                      name="coquiEndpoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            Coqui TTS Endpoint
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => testConnection("coqui")}
                              disabled={testing === "coqui"}
                            >
                              {testing === "coqui" ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <RefreshCw className="h-3 w-3" />
                              )}
                            </Button>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://your-coqui-endpoint.com"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Endpoint URL for Coqui text-to-speech service
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={endpointForm.control}
                      name="kandinskyEndpoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            Kandinsky Endpoint
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => testConnection("kandinsky")}
                              disabled={testing === "kandinsky"}
                            >
                              {testing === "kandinsky" ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <RefreshCw className="h-3 w-3" />
                              )}
                            </Button>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://your-kandinsky-endpoint.com"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Endpoint URL for Kandinsky image generation
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={endpointForm.control}
                      name="animatediffEndpoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            AnimateDiff Endpoint
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => testConnection("animatediff")}
                              disabled={testing === "animatediff"}
                            >
                              {testing === "animatediff" ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <RefreshCw className="h-3 w-3" />
                              )}
                            </Button>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://your-animatediff-endpoint.com"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Endpoint URL for AnimateDiff video generation
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={saveEndpointSettingsMutation.isPending}
                    className="w-full md:w-auto"
                  >
                    {saveEndpointSettingsMutation.isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save Endpoint Settings"
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}