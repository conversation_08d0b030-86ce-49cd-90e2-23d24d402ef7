/**
 * Simple Audio Generator Service
 * A basic service to convert text to audio patterns as a fallback TTS option
 */

import { spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

/**
 * Extracts the main narration content from a lesson script
 * Filters out metadata, markdown formatting, and non-narrative sections
 */
function extractNarrationContent(text: string): string {
  if (!text) return '';
  
  // Step 1: Remove markdown formatting elements
  let content = text
    // Remove markdown headers (# Title)
    .replace(/^#+\s+.*$|^\s*#+\s+.*$/gm, '')
    // Remove markdown bold/italic markers but keep the text
    .replace(/\*\*(.*?)\*\*|\*(.*?)\*/g, '$1$2')
    // Remove markdown links but keep the text [text](url) -> text
    .replace(/\[(.*?)\]\(.*?\)/g, '$1')
    // Remove code blocks
    .replace(/```[\s\S]*?```/g, '')
    // Remove inline code
    .replace(/`(.*?)`/g, '$1');
  
  // Step 2: Remove section markers and non-narrative metadata
  content = content
    // Remove lines with metadata markers (---, Title:, Module:, etc.)
    .replace(/^---.*$|^\s*---.*$/gm, '')
    .replace(/^(Title:|Module:|Description:|Learning Objectives:|Keywords:|Duration:|Difficulty:|Prerequisites:|Materials:|Resources:|Notes:).*$/gm, '')
    // Remove scene descriptions and bracketed content [Scene: ...]
    .replace(/\[Scene:.*?\]|\[.*?\]/g, '')
    // Remove instructor directions/notes like (Pause for effect)
    .replace(/\(.*?\)/g, '');
  
  // Step 3: Handle special sections - we want to keep the content between certain sections
  // but remove the section headers themselves
  content = content
    // Remove section headers (Introduction:, Conclusion:, etc.) but keep their content
    .replace(/^\s*(Introduction:|Conclusion:|Summary:|Overview:|Key Points:|Takeaway:|Recap:)\s*$/gm, '')
    // Clean up "Instructor:" or "Speaker:" markers
    .replace(/^\s*(Instructor:|Speaker:|Presenter:|Narrator:)\s*/gm, '');
  
  // Step 4: Clean up any resulting artifacts
  content = content
    // Replace multiple consecutive line breaks with a single one
    .replace(/\n{3,}/g, '\n\n')
    // Replace multiple spaces with a single space
    .replace(/[ \t]+/g, ' ')
    // Trim leading/trailing whitespace
    .trim();
  
  // Step 5: Do a final check for any remaining special delimiters
  content = content
    // Remove any remaining special delimiters
    .replace(/^\s*\*{3,}\s*$|^\s*_{3,}\s*$|^\s*={3,}\s*$/gm, '');
  
  console.log('Original text length:', text.length);
  console.log('Filtered text length:', content.length);
  
  return content;
}

// Directory for temporary files
const TEMP_DIR = path.join(process.cwd(), 'temp');
if (!fs.existsSync(TEMP_DIR)) {
  fs.mkdirSync(TEMP_DIR, { recursive: true });
}

// Available language options for audio generation
export const AVAILABLE_MODELS = {
  'en': 'English',
  'fr': 'French',
  'de': 'German',
  'es': 'Spanish',
  'it': 'Italian',
  'pt': 'Portuguese',
  'ja': 'Japanese',
  'nl': 'Dutch',
  'tr': 'Turkish',
  'ru': 'Russian',
  'zh-CN': 'Chinese',
};

// Default language
const DEFAULT_MODEL = 'en';

/**
 * Map of voice names to language codes for easy selection
 */
export const VOICE_IDS: Record<string, string> = {
  'EnglishFemale': 'en',
  'EnglishMale': 'en',
  'FrenchFemale': 'fr',
  'FrenchMale': 'fr',
  'GermanMale': 'de',
  'GermanFemale': 'de',
  'SpanishFemale': 'es',
  'SpanishMale': 'es',
  'ItalianFemale': 'it',
  'ItalianMale': 'it',
  'PortugueseFemale': 'pt',
  'PortugueseMale': 'pt',
  'JapaneseFemale': 'ja',
  'JapaneseMale': 'ja',
  'DutchFemale': 'nl',
  'DutchMale': 'nl',
  'TurkishMale': 'tr',
  'TurkishFemale': 'tr',
  'RussianMale': 'ru',
  'RussianFemale': 'ru',
  'ChineseFemale': 'zh-CN',
  'ChineseMale': 'zh-CN',
};

export interface Voice {
  voice_id: string;
  name: string;
  language?: string;
  description?: string;
}

/**
 * Gets a list of available voices
 */
export async function getVoices(): Promise<Voice[]> {
  // Generate a list of voices with male and female variants for each language
  const voices: Voice[] = [];
  
  Object.entries(AVAILABLE_MODELS).forEach(([langCode, langName]) => {
    // Add female voice
    voices.push({
      voice_id: `coqui-${langCode}-female`,
      name: `${langName} Female`,
      language: langCode,
      description: `${langName} female synthetic voice`
    });
    
    // Add male voice
    voices.push({
      voice_id: `coqui-${langCode}-male`,
      name: `${langName} Male`,
      language: langCode,
      description: `${langName} male synthetic voice`
    });
  });
  
  return voices;
}

/**
 * Generate speech from text using our simple audio generator
 */
export async function generateSpeech(
  options: {
    text: string;
    voiceId?: string;
    speaker?: string;
    speed?: number;
  },
  userId?: number
): Promise<{
  audioData: Buffer | null;
  format: string;
  wordCount: number;
  duration: number;
  voiceId: string;
  error?: string;
}> {
  try {
    if (!options.text || options.text.trim() === '') {
      return {
        audioData: null,
        format: 'wav',
        wordCount: 0,
        duration: 0,
        voiceId: options.voiceId || DEFAULT_MODEL,
        error: 'No text provided for speech generation'
      };
    }
    
    // Filter the text to extract only the main content for narration
    const filteredText = extractNarrationContent(options.text);

    // Default voice/model if not provided
    let voiceId = options.voiceId || DEFAULT_MODEL;
    
    // Convert voice name to ID if applicable
    if (VOICE_IDS[voiceId]) {
      console.log(`Voice name "${voiceId}" resolved to language: ${VOICE_IDS[voiceId]}`);
      voiceId = VOICE_IDS[voiceId];
    }
    
    // If it's a full voice ID (like coqui-en-female), extract the language code
    if (voiceId.startsWith('coqui-')) {
      const parts = voiceId.split('-');
      // Handle format like "coqui-en-female" -> "en"
      if (parts.length >= 2) {
        if (parts[1] === 'zh' && parts.length > 2) {
          voiceId = 'zh-CN'; // Special case for Chinese
        } else {
          voiceId = parts[1]; // Use the language code part
        }
      }
    }

    // Generate a unique file name for the output
    const outputFileName = `${uuidv4()}.wav`;
    const outputPath = path.join(TEMP_DIR, outputFileName);
    
    // Prepare the text - use filtered text and remove special characters for TTS
    const cleanText = filteredText
      .replace(/[\\'"]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    console.log('Using filtered text for narration, removed metadata and formatting marks');
    
    // Prepare command line arguments for our audio generator script
    const args = [
      'scripts/tts_generate.py',
      '--text', cleanText,
      '--model', voiceId,
      '--output', outputPath
    ];
    
    // Add speaker ID if specified
    if (options.speaker) {
      args.push('--speaker', options.speaker);
    }
    
    // Add verbose flag for debugging
    args.push('--verbose');

    // Generate audio using the Python script
    console.log(`Generating audio with language ${voiceId}`);
    console.log(`Command: python ${args.join(' ')}`);
    
    return new Promise((resolve, reject) => {
      const pythonProcess = spawn('python', args);
      
      let stdoutData = '';
      let stderrData = '';
      
      pythonProcess.stdout.on('data', (data) => {
        stdoutData += data.toString();
        console.log(`Audio generator stdout: ${data}`);
      });
      
      pythonProcess.stderr.on('data', (data) => {
        stderrData += data.toString();
        console.error(`Audio generator stderr: ${data}`);
      });
      
      pythonProcess.on('close', (code) => {
        if (code !== 0) {
          console.error(`Audio generator process exited with code ${code}`);
          resolve({
            audioData: null,
            format: 'wav',
            wordCount: options.text.split(/\s+/).length,
            duration: 0,
            voiceId: voiceId,
            error: `Audio generator process exited with code ${code}: ${stderrData}`
          });
          return;
        }
        
        try {
          // Check if the file was created
          if (!fs.existsSync(outputPath)) {
            resolve({
              audioData: null,
              format: 'wav',
              wordCount: options.text.split(/\s+/).length,
              duration: 0,
              voiceId: voiceId,
              error: `Output file was not created`
            });
            return;
          }
          
          // Read the audio file
          const audioBuffer = fs.readFileSync(outputPath);
          
          // Calculate word count from the filtered text
          const wordCount = filteredText.split(/\s+/).length;
          
          // Rough estimate of duration based on filtered text (15 characters per second)
          const estimatedDuration = Math.max(1, Math.ceil(filteredText.length / 15));
          
          // Clean up the temporary file
          fs.unlinkSync(outputPath);
          
          resolve({
            audioData: audioBuffer,
            format: 'wav',
            wordCount,
            duration: estimatedDuration,
            voiceId: voiceId
          });
        } catch (err) {
          const error = err as Error;
          console.error('Error reading output file:', error);
          resolve({
            audioData: null,
            format: 'wav',
            wordCount: options.text.split(/\s+/).length,
            duration: 0,
            voiceId: voiceId,
            error: `Error reading output file: ${error.message}`
          });
        }
      });
      
      pythonProcess.on('error', (error) => {
        console.error('Failed to start audio generator process:', error);
        resolve({
          audioData: null,
          format: 'wav',
          wordCount: options.text.split(/\s+/).length,
          duration: 0,
          voiceId: voiceId,
          error: `Failed to start audio generator process: ${error.message}`
        });
      });
    });
  } catch (error) {
    console.error('Error in audio generation:', error);
    
    return {
      audioData: null,
      format: 'wav',
      wordCount: options.text ? options.text.split(/\s+/).length : 0,
      duration: 0,
      voiceId: options.voiceId || DEFAULT_MODEL,
      error: error instanceof Error ? error.message : 'Unknown error in audio generation'
    };
  }
}

/**
 * Get available language options
 */
export async function getModels(): Promise<any[]> {
  return Object.entries(AVAILABLE_MODELS).map(([id, name]) => ({
    model_id: id,
    name
  }));
}