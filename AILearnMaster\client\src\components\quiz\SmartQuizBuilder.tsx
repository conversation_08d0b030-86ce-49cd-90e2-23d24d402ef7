import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/hooks/use-toast';
import { 
  Brain, 
  Plus, 
  Trash2, 
  Edit3, 
  Save, 
  Sparkles,
  CheckCircle2,
  XCircle,
  Eye,
  Settings,
  BookOpen,
  Target,
  Clock,
  Users,
  Lightbulb,
  RefreshCw
} from 'lucide-react';
import { useMutation } from '@tanstack/react-query';

interface QuizQuestion {
  id: string;
  type: 'multiple_choice' | 'true_false' | 'fill_blank' | 'short_answer';
  question: string;
  options?: string[];
  correctAnswer: string | number;
  explanation?: string;
  points: number;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface Quiz {
  id?: string;
  title: string;
  description: string;
  questions: QuizQuestion[];
  settings: {
    timeLimit?: number;
    passingScore: number;
    randomizeQuestions: boolean;
    showCorrectAnswers: boolean;
    allowRetakes: boolean;
    showExplanations: boolean;
  };
}

interface SmartQuizBuilderProps {
  moduleContent?: string;
  lessonContent?: string;
  onSave?: (quiz: Quiz) => void;
  onCancel?: () => void;
}

export default function SmartQuizBuilder({ 
  moduleContent, 
  lessonContent, 
  onSave, 
  onCancel 
}: SmartQuizBuilderProps) {
  const { toast } = useToast();
  
  const [quiz, setQuiz] = useState<Quiz>({
    title: '',
    description: '',
    questions: [],
    settings: {
      passingScore: 70,
      randomizeQuestions: false,
      showCorrectAnswers: true,
      allowRetakes: true,
      showExplanations: true
    }
  });

  const [aiSettings, setAiSettings] = useState({
    questionCount: 5,
    difficulty: 'medium' as 'easy' | 'medium' | 'hard',
    questionTypes: ['multiple_choice'] as string[],
    focusAreas: [] as string[]
  });

  const [customContent, setCustomContent] = useState('');
  const [activeTab, setActiveTab] = useState('generate');

  // Smart AI Generation with OpenAI/Gemini
  const generateQuizMutation = useMutation({
    mutationFn: async (params: { 
      content: string; 
      questionCount: number; 
      difficulty: string;
      questionTypes: string[];
      focusAreas?: string[];
    }) => {
      const response = await fetch('/api/quiz-generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: params.content,
          questionCount: params.questionCount,
          difficulty: params.difficulty,
          questionTypes: params.questionTypes,
          focusAreas: params.focusAreas || []
        })
      });

      if (!response.ok) {
        const text = await response.text();
        let errorMessage = 'Failed to generate quiz questions';
        
        try {
          const errorData = JSON.parse(text);
          errorMessage = errorData.message || errorMessage;
        } catch {
          if (text.includes('API key')) {
            errorMessage = 'API service configuration needed. Please check your OpenAI or Gemini API keys.';
          } else if (text.includes('<!DOCTYPE html>')) {
            errorMessage = 'Service routing issue. Please try again or contact support.';
          } else {
            errorMessage = text || errorMessage;
          }
        }
        
        throw new Error(errorMessage);
      }

      return response.json();
    },
    onSuccess: (data) => {
      if (data.questions && Array.isArray(data.questions)) {
        const generatedQuestions: QuizQuestion[] = data.questions.map((q: any, index: number) => ({
          id: `q-${Date.now()}-${index}`,
          type: q.type || 'multiple_choice',
          question: q.question,
          options: q.options || [],
          correctAnswer: q.correctAnswer || 0,
          explanation: q.explanation || '',
          points: q.points || 1,
          difficulty: q.difficulty || aiSettings.difficulty
        }));

        setQuiz(prev => ({
          ...prev,
          questions: [...prev.questions, ...generatedQuestions]
        }));

        toast({
          title: "Questions Generated Successfully",
          description: `Added ${generatedQuestions.length} questions using AI`,
        });
        
        setActiveTab('review');
      }
    },
    onError: (error: any) => {
      toast({
        title: "Generation Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const handleGenerateQuiz = () => {
    let contentToUse = customContent.trim();
    
    if (!contentToUse) {
      if (lessonContent) {
        contentToUse = lessonContent;
      } else if (moduleContent) {
        contentToUse = moduleContent;
      }
    }

    if (!contentToUse || contentToUse.length < 20) {
      toast({
        title: "Content Required",
        description: "Please provide content for quiz generation (at least 20 characters)",
        variant: "destructive",
      });
      return;
    }

    generateQuizMutation.mutate({
      content: contentToUse,
      questionCount: aiSettings.questionCount,
      difficulty: aiSettings.difficulty,
      questionTypes: aiSettings.questionTypes,
      focusAreas: aiSettings.focusAreas
    });
  };

  const removeQuestion = (index: number) => {
    setQuiz(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }));
    
    toast({
      title: "Question Removed",
      description: "Question has been deleted from the quiz",
    });
  };

  const handleSave = () => {
    if (!quiz.title.trim()) {
      toast({
        title: "Title Required",
        description: "Please enter a title for your quiz",
        variant: "destructive",
      });
      return;
    }

    if (quiz.questions.length === 0) {
      toast({
        title: "Questions Required",
        description: "Please generate or add questions to your quiz",
        variant: "destructive",
      });
      return;
    }

    onSave?.(quiz);
    toast({
      title: "Quiz Saved",
      description: "Your quiz has been saved successfully",
    });
  };

  const getQuestionTypeIcon = (type: string) => {
    switch (type) {
      case 'multiple_choice': return <Target className="h-4 w-4" />;
      case 'true_false': return <CheckCircle2 className="h-4 w-4" />;
      case 'fill_blank': return <Edit3 className="h-4 w-4" />;
      case 'short_answer': return <BookOpen className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Brain className="h-8 w-8 text-blue-600" />
            Smart Quiz Builder
          </h1>
          <p className="text-muted-foreground mt-1">Create intelligent quizzes with AI-powered question generation</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={quiz.questions.length === 0}>
            <Save className="h-4 w-4 mr-2" />
            Save Quiz
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generate" className="flex items-center gap-2">
            <Sparkles className="h-4 w-4" />
            Generate
          </TabsTrigger>
          <TabsTrigger value="review" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Review ({quiz.questions.length})
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Preview
          </TabsTrigger>
        </TabsList>

        {/* AI Generation Tab */}
        <TabsContent value="generate" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Content Input */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Content Source
                </CardTitle>
                <CardDescription>
                  Provide content for AI to generate questions from
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Custom Content</label>
                  <Textarea
                    placeholder="Paste lesson content, study material, or key concepts here..."
                    value={customContent}
                    onChange={(e) => setCustomContent(e.target.value)}
                    className="min-h-[200px]"
                  />
                </div>
                
                {(moduleContent || lessonContent) && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Available Content</label>
                    <div className="space-y-2">
                      {lessonContent && (
                        <Badge variant="outline" className="mr-2">
                          Lesson content available ({lessonContent.length} chars)
                        </Badge>
                      )}
                      {moduleContent && (
                        <Badge variant="outline">
                          Module content available ({moduleContent.length} chars)
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Leave custom content empty to use available lesson/module content
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* AI Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  AI Generation Settings
                </CardTitle>
                <CardDescription>
                  Configure how AI generates your quiz questions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Number of Questions</label>
                  <div className="px-3">
                    <Slider
                      value={[aiSettings.questionCount]}
                      onValueChange={(value) => setAiSettings(prev => ({ ...prev, questionCount: value[0] }))}
                      max={20}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>1</span>
                      <span className="font-medium">{aiSettings.questionCount} questions</span>
                      <span>20</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Difficulty Level</label>
                  <Select 
                    value={aiSettings.difficulty} 
                    onValueChange={(value: any) => setAiSettings(prev => ({ ...prev, difficulty: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easy">Easy - Basic recall and understanding</SelectItem>
                      <SelectItem value="medium">Medium - Analysis and application</SelectItem>
                      <SelectItem value="hard">Hard - Synthesis and evaluation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Question Types</label>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      { id: 'multiple_choice', label: 'Multiple Choice' },
                      { id: 'true_false', label: 'True/False' },
                      { id: 'fill_blank', label: 'Fill in Blank' },
                      { id: 'short_answer', label: 'Short Answer' }
                    ].map((type) => (
                      <div key={type.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={type.id}
                          checked={aiSettings.questionTypes.includes(type.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setAiSettings(prev => ({
                                ...prev,
                                questionTypes: [...prev.questionTypes, type.id]
                              }));
                            } else {
                              setAiSettings(prev => ({
                                ...prev,
                                questionTypes: prev.questionTypes.filter(t => t !== type.id)
                              }));
                            }
                          }}
                        />
                        <label htmlFor={type.id} className="text-sm">{type.label}</label>
                      </div>
                    ))}
                  </div>
                </div>

                <Button 
                  onClick={handleGenerateQuiz} 
                  disabled={generateQuizMutation.isPending}
                  className="w-full"
                  size="lg"
                >
                  {generateQuizMutation.isPending ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Generating Questions...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Quiz with AI
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Review Questions Tab */}
        <TabsContent value="review" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Generated Questions</CardTitle>
              <CardDescription>
                Review and manage the questions in your quiz
              </CardDescription>
            </CardHeader>
            <CardContent>
              {quiz.questions.length === 0 ? (
                <div className="text-center py-8">
                  <Target className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No questions yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Generate questions using AI or add them manually
                  </p>
                  <Button onClick={() => setActiveTab('generate')}>
                    <Sparkles className="h-4 w-4 mr-2" />
                    Generate Questions
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {quiz.questions.map((question, index) => (
                    <Card key={question.id} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              {getQuestionTypeIcon(question.type)}
                              <Badge variant="outline">
                                {question.type.replace('_', ' ').toUpperCase()}
                              </Badge>
                              <Badge className={getDifficultyColor(question.difficulty)}>
                                {question.difficulty}
                              </Badge>
                              <Badge variant="secondary">
                                {question.points} {question.points === 1 ? 'point' : 'points'}
                              </Badge>
                            </div>
                            <h4 className="font-medium mb-2">
                              {index + 1}. {question.question}
                            </h4>
                            {question.options && question.options.length > 0 && (
                              <div className="space-y-1 mb-2">
                                {question.options.map((option, optIndex) => (
                                  <div key={optIndex} className="flex items-center gap-2">
                                    {question.correctAnswer === optIndex ? (
                                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                                    ) : (
                                      <XCircle className="h-4 w-4 text-gray-300" />
                                    )}
                                    <span className={question.correctAnswer === optIndex ? 'text-green-700 font-medium' : ''}>
                                      {option}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            )}
                            {question.explanation && (
                              <div className="bg-blue-50 p-3 rounded-md">
                                <div className="flex items-start gap-2">
                                  <Lightbulb className="h-4 w-4 text-blue-600 mt-0.5" />
                                  <div>
                                    <p className="text-sm font-medium text-blue-800">Explanation</p>
                                    <p className="text-sm text-blue-700">{question.explanation}</p>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeQuestion(index)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Quiz Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Quiz Information</CardTitle>
                <CardDescription>Basic details about your quiz</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Quiz Title</label>
                  <Input
                    placeholder="Enter quiz title"
                    value={quiz.title}
                    onChange={(e) => setQuiz(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    placeholder="Describe what this quiz covers"
                    value={quiz.description}
                    onChange={(e) => setQuiz(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quiz Configuration</CardTitle>
                <CardDescription>Set up rules and behavior for your quiz</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Passing Score (%)</label>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={quiz.settings.passingScore}
                    onChange={(e) => setQuiz(prev => ({
                      ...prev,
                      settings: { ...prev.settings, passingScore: parseInt(e.target.value) || 70 }
                    }))}
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">Time Limit (minutes)</label>
                  <Input
                    type="number"
                    min="0"
                    placeholder="No limit"
                    value={quiz.settings.timeLimit || ''}
                    onChange={(e) => setQuiz(prev => ({
                      ...prev,
                      settings: { ...prev.settings, timeLimit: e.target.value ? parseInt(e.target.value) : undefined }
                    }))}
                  />
                </div>

                <div className="space-y-3">
                  {[
                    { key: 'randomizeQuestions', label: 'Randomize Questions', description: 'Show questions in random order' },
                    { key: 'showCorrectAnswers', label: 'Show Correct Answers', description: 'Display correct answers after submission' },
                    { key: 'allowRetakes', label: 'Allow Retakes', description: 'Students can retake the quiz' },
                    { key: 'showExplanations', label: 'Show Explanations', description: 'Display explanations for answers' }
                  ].map((setting) => (
                    <div key={setting.key} className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">{setting.label}</p>
                        <p className="text-xs text-muted-foreground">{setting.description}</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={quiz.settings[setting.key as keyof typeof quiz.settings] as boolean}
                        onChange={(e) => setQuiz(prev => ({
                          ...prev,
                          settings: { ...prev.settings, [setting.key]: e.target.checked }
                        }))}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Preview Tab */}
        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quiz Preview</CardTitle>
              <CardDescription>
                See how your quiz will appear to students
              </CardDescription>
            </CardHeader>
            <CardContent>
              {quiz.questions.length === 0 ? (
                <div className="text-center py-8">
                  <Eye className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No questions to preview</h3>
                  <p className="text-muted-foreground">Generate some questions to see the preview</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="border-b pb-4">
                    <h2 className="text-xl font-bold">{quiz.title || 'Untitled Quiz'}</h2>
                    {quiz.description && (
                      <p className="text-muted-foreground mt-1">{quiz.description}</p>
                    )}
                    <div className="flex gap-4 mt-2 text-sm text-muted-foreground">
                      <span>{quiz.questions.length} questions</span>
                      <span>Passing score: {quiz.settings.passingScore}%</span>
                      {quiz.settings.timeLimit && (
                        <span>Time limit: {quiz.settings.timeLimit} minutes</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    {quiz.questions.map((question, index) => (
                      <div key={question.id} className="border rounded-lg p-4">
                        <h3 className="font-medium mb-3">
                          {index + 1}. {question.question}
                        </h3>
                        {question.options && question.options.length > 0 && (
                          <div className="space-y-2">
                            {question.options.map((option, optIndex) => (
                              <div key={optIndex} className="flex items-center gap-2">
                                <input type="radio" name={`question-${index}`} disabled />
                                <label className="text-sm">{option}</label>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}