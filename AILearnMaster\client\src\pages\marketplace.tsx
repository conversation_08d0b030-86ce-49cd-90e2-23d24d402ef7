
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useState } from "react";
import { Loader2 } from "lucide-react";

export default function MarketplacePage() {
  const [email, setEmail] = useState("");
  const [isSubscribing, setIsSubscribing] = useState(false);
  
  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubscribing(true);
    // TODO: Implement email subscription
    setTimeout(() => setIsSubscribing(false), 1000);
  };

  return (
    <div className="container mx-auto px-4 py-16">
      <Card className="max-w-2xl mx-auto p-8 text-center relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 animate-pulse" />
        
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Marketplace Coming Soon
        </h1>
        
        <p className="text-lg text-muted-foreground mb-8">
          We're building a powerful marketplace for course creators. 
          Get ready to discover and share amazing educational content!
        </p>

        <div className="w-16 h-16 mx-auto mb-8">
          <div className="w-full h-full border-4 border-primary border-t-transparent rounded-full animate-spin" />
        </div>

        <form onSubmit={handleSubscribe} className="max-w-md mx-auto">
          <div className="flex gap-2">
            <input
              type="email"
              placeholder="Enter your email for updates"
              className="flex-1 px-4 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-primary"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <Button type="submit" disabled={isSubscribing}>
              {isSubscribing ? (
                <Loader2 className="animate-spin" />
              ) : (
                "Notify Me"
              )}
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}
