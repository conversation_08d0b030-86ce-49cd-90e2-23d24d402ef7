#!/usr/bin/env python3
"""
AILearnMaster Open-Source AI Platform - Simplified Version
Modal A100 GPU deployment with core functionality: Mistral, Coqui TTS, and Marp
"""

import modal
import os
import time
import base64
import tempfile
import subprocess
import json
from typing import Dict, Any

# Create Modal app
app = modal.App("courseai-opensource")

# Create shared volume for model storage
shared_volume = modal.Volume.from_name("courseai-models", create_if_missing=True)

# Base image with essential dependencies
base_image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install([
        "git", "wget", "curl", "unzip", "ffmpeg", "espeak", "espeak-data",
        "build-essential", "nodejs", "npm"
    ])
    .run_commands([
        "npm install -g @marp-team/marp-cli",
        "mkdir -p /app/models /app/cache"
    ])
)

# GPU image with AI/ML packages
gpu_image = (
    base_image
    .pip_install([
        "torch>=2.1.0", "transformers>=4.35.0", "accelerate>=0.24.1",
        "bitsandbytes>=0.41.0", "TTS>=0.22.0", "librosa>=0.10.1",
        "soundfile>=0.12.1", "numpy>=1.24.0", "requests>=2.31.0",
        "fastapi[standard]>=0.104.0", "uvicorn>=0.24.0"
    ])
)

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=16384
)
def health_check() -> Dict[str, Any]:
    """Health check for open-source AI services"""
    import torch
    
    try:
        gpu_available = torch.cuda.is_available()
        gpu_info = {}
        if gpu_available:
            gpu_info = {
                "name": torch.cuda.get_device_name(0),
                "memory_total_gb": round(torch.cuda.get_device_properties(0).total_memory / 1024**3, 2),
                "memory_free_gb": round((torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / 1024**3, 2)
            }
        
        # Check services
        services = {
            "mistral_ready": True,  # Will be loaded on demand
            "coqui_tts": True,
            "marp_cli": os.system("marp --version") == 0
        }
        
        return {
            "status": "healthy",
            "timestamp": int(time.time() * 1000),
            "platform": "Open-Source AI Platform",
            "gpu_available": gpu_available,
            "gpu_info": gpu_info,
            "services": services
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=600,
    memory=32768
)
def mistral_generate_text(
    prompt: str,
    max_length: int = 512,
    temperature: float = 0.7,
    use_quantization: bool = True
) -> Dict[str, Any]:
    """Generate text using quantized Mistral 7B"""
    try:
        import torch
        from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
        
        model_name = "mistralai/Mistral-7B-Instruct-v0.1"
        
        # Configure quantization
        if use_quantization:
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
        else:
            quantization_config = None
        
        # Load model
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=quantization_config,
            device_map="auto",
            torch_dtype=torch.float16 if not use_quantization else None
        )
        
        # Format prompt
        formatted_prompt = f"<s>[INST] {prompt} [/INST]"
        inputs = tokenizer(formatted_prompt, return_tensors="pt").to(model.device)
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=max_length,
                temperature=temperature,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(formatted_prompt):].strip()
        
        return {
            "status": "success",
            "generated_text": response,
            "model": model_name,
            "quantized": use_quantization,
            "gpu_memory_used_gb": round(torch.cuda.memory_allocated(0) / 1024**3, 2),
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=16384
)
def coqui_tts_generate(
    text: str,
    voice_name: str = "tts_models/en/ljspeech/tacotron2-DDC",
    language: str = "en"
) -> Dict[str, Any]:
    """Generate speech using Coqui TTS"""
    try:
        import torch
        from TTS.api import TTS
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        
        # Initialize TTS
        tts = TTS(voice_name).to(device)
        
        # Create temp file
        temp_dir = tempfile.mkdtemp()
        output_path = os.path.join(temp_dir, "output.wav")
        
        # Generate speech
        tts.tts_to_file(text=text, file_path=output_path)
        
        # Read and encode
        with open(output_path, "rb") as f:
            audio_data = f.read()
            audio_base64 = base64.b64encode(audio_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "audio_base64": audio_base64,
            "text": text,
            "voice": voice_name,
            "engine": "coqui_tts",
            "gpu_used": torch.cuda.is_available(),
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    volumes={"/app/storage": shared_volume},
    timeout=300,
    memory=8192
)
def marp_generate_slides(
    markdown_content: str,
    theme: str = "default",
    output_format: str = "html"
) -> Dict[str, Any]:
    """Generate slides using Marp CLI"""
    try:
        # Create temp directory
        temp_dir = tempfile.mkdtemp()
        
        # Write markdown
        md_path = os.path.join(temp_dir, "slides.md")
        with open(md_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)
        
        # Output path
        output_ext = "html" if output_format == "html" else "pdf"
        output_path = os.path.join(temp_dir, f"slides.{output_ext}")
        
        # Marp command
        cmd = ["marp", md_path, "--theme", theme, "--output", output_path]
        if output_format == "pdf":
            cmd.extend(["--pdf"])
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        if result.returncode != 0:
            return {
                "status": "error",
                "error": f"Marp generation failed: {result.stderr}",
                "timestamp": int(time.time() * 1000)
            }
        
        # Read and encode output
        with open(output_path, "rb") as f:
            output_data = f.read()
            output_base64 = base64.b64encode(output_data).decode()
        
        # Cleanup
        import shutil
        shutil.rmtree(temp_dir)
        
        return {
            "status": "success",
            "slides_base64": output_base64,
            "format": output_format,
            "theme": theme,
            "engine": "marp_cli",
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }

# Web endpoints
@app.function()
@modal.fastapi_endpoint(method="GET")
def health():
    """Health check endpoint"""
    return health_check.remote()

@app.function()
@modal.fastapi_endpoint(method="POST")
def generate_text(request_data: Dict[str, Any]):
    """Mistral text generation endpoint"""
    prompt = request_data.get("prompt", "")
    if not prompt:
        return {"status": "error", "error": "Prompt is required"}

    return mistral_generate_text.remote(
        prompt,
        request_data.get("max_length", 512),
        request_data.get("temperature", 0.7),
        request_data.get("use_quantization", True)
    )

@app.function()
@modal.fastapi_endpoint(method="POST")
def generate_speech(request_data: Dict[str, Any]):
    """Coqui TTS speech generation endpoint"""
    text = request_data.get("text", "")
    if not text:
        return {"status": "error", "error": "Text is required"}

    return coqui_tts_generate.remote(
        text,
        request_data.get("voice_name", "tts_models/en/ljspeech/tacotron2-DDC"),
        request_data.get("language", "en")
    )

@app.function()
@modal.fastapi_endpoint(method="POST")
def generate_slides(request_data: Dict[str, Any]):
    """Marp slide generation endpoint"""
    markdown_content = request_data.get("markdown_content", "")
    if not markdown_content:
        return {"status": "error", "error": "Markdown content is required"}

    return marp_generate_slides.remote(
        markdown_content,
        request_data.get("theme", "default"),
        request_data.get("output_format", "html")
    )

if __name__ == "__main__":
    print("AILearnMaster Open-Source AI Platform - Simplified")
    print("Ready for deployment to Modal A100 GPU")
