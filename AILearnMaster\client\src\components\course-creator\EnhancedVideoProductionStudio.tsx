import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { VoiceServiceSelector } from '@/components/voice/VoiceServiceSelector';
import { 
  Play, 
  Pause, 
  Video, 
  Volume2,
  Download, 
  Settings,
  Eye,
  ArrowLeft,
  CheckCircle2,
  AlertCircle,
  Loader2,
  Wand2,
  Clock,
  BookOpen,
  Film,
  Music,
  Image as ImageIcon,
  RefreshCw,
  FileVideo,
  Mic,
  Camera,
  Layers,
  Sparkles,
  Monitor,
  Globe,
  PaintBucket,
  Zap
} from 'lucide-react';

interface Scene {
  id: string;
  title: string;
  content: string;
  duration: number;
  backgroundType: 'image' | 'video';
  backgroundUrl: string;
  backgroundQuery: string;
  voiceText: string;
  timestamp: number;
}

interface VideoProject {
  id: string;
  lessonId: string;
  moduleId: string;
  title: string;
  script: string;
  scenes: Scene[];
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  audioUrl?: string;
  videoUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface EnhancedVideoProductionStudioProps {
  modules: any[];
  scripts: any;
  voices: any[];
  onBack: () => void;
  onNext: () => void;
}

export default function EnhancedVideoProductionStudio({ 
  modules, 
  scripts, 
  voices,
  onBack, 
  onNext 
}: EnhancedVideoProductionStudioProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [videoProjects, setVideoProjects] = useState<VideoProject[]>([]);
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [isGeneratingAll, setIsGeneratingAll] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  const [selectedVoice, setSelectedVoice] = useState('kokoro-default');
  const [voiceSettings, setVoiceSettings] = useState({
    speed: 1.0,
    pitch: 1.0,
    volume: 1.0,
    stability: 0.5
  });
  const [completedVideos, setCompletedVideos] = useState<VideoProject[]>([]);
  const [showSceneEditor, setShowSceneEditor] = useState(false);
  const [editingScene, setEditingScene] = useState<Scene | null>(null);
  const [videoStyle, setVideoStyle] = useState('professional');
  
  const { toast } = useToast();

  // Initialize video projects from scripts
  useEffect(() => {
    const projects: VideoProject[] = [];
    
    modules.forEach((module) => {
      module.lessons?.forEach((lesson: any) => {
        const script = scripts[module.id]?.[lesson.id];
        if (script) {
          const existingProject = videoProjects.find(p => p.lessonId === lesson.id);
          if (!existingProject) {
            projects.push({
              id: `${module.id}-${lesson.id}`,
              lessonId: lesson.id,
              moduleId: module.id,
              title: lesson.title,
              script: script,
              scenes: [],
              status: 'pending',
              progress: 0,
              createdAt: new Date(),
              updatedAt: new Date()
            });
          }
        }
      });
    });
    
    if (!videoProjects.length && projects.length > 0) {
      setVideoProjects(projects);
    }
  }, [modules, scripts]);

  // Generate scenes from script
  const generateScenes = async (projectId: string): Promise<Scene[]> => {
    const project = videoProjects.find(p => p.id === projectId);
    if (!project) return [];

    try {
      const response = await fetch('/api/ai/generate-scenes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          script: project.script,
          title: project.title,
          targetScenes: 6,
          style: videoStyle
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate scenes');
      }

      const { scenes } = await response.json();
      return scenes;
    } catch (error) {
      console.error('Scene generation error:', error);
      toast({
        title: "Scene Generation Failed",
        description: "Failed to generate scenes. Using default structure.",
        variant: "destructive"
      });
      
      // Fallback to simple scene generation
      return generateFallbackScenes(project.script);
    }
  };

  // Fallback scene generation
  const generateFallbackScenes = (script: string): Scene[] => {
    const sentences = script.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const scenesPerGroup = Math.ceil(sentences.length / 6);
    const scenes: Scene[] = [];
    
    for (let i = 0; i < 6; i++) {
      const startIdx = i * scenesPerGroup;
      const endIdx = Math.min(startIdx + scenesPerGroup, sentences.length);
      const sceneText = sentences.slice(startIdx, endIdx).join('. ').trim();
      
      if (sceneText) {
        scenes.push({
          id: `scene-${i + 1}`,
          title: `Scene ${i + 1}`,
          content: sceneText,
          duration: Math.max(5, Math.min(15, sceneText.length / 10)),
          backgroundType: i % 2 === 0 ? 'image' : 'video',
          backgroundUrl: '',
          backgroundQuery: extractKeywords(sceneText),
          voiceText: sceneText,
          timestamp: i * 10
        });
      }
    }
    
    return scenes;
  };

  // Extract keywords for media search
  const extractKeywords = (text: string): string => {
    const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'];
    const words = text.toLowerCase().split(/\W+/).filter(word => 
      word.length > 3 && !commonWords.includes(word)
    );
    return words.slice(0, 3).join(' ');
  };

  // Generate video for a specific project
  const generateVideo = async (projectId: string) => {
    const project = videoProjects.find(p => p.id === projectId);
    if (!project) return;

    try {
      // Update project status
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, status: 'processing', progress: 5 } : p
      ));

      // Step 1: Generate scenes if not already done
      let scenes = project.scenes;
      if (scenes.length === 0) {
        setVideoProjects(prev => prev.map(p => 
          p.id === projectId ? { ...p, progress: 10 } : p
        ));
        
        scenes = await generateScenes(projectId);
        setVideoProjects(prev => prev.map(p => 
          p.id === projectId ? { ...p, scenes, progress: 20 } : p
        ));
      }

      // Step 2: Generate background media for scenes
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, progress: 30 } : p
      ));

      const scenesWithMedia = await Promise.all(scenes.map(async (scene) => {
        try {
          const mediaResponse = await fetch('/api/pexels/search', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              query: scene.backgroundQuery,
              type: scene.backgroundType,
              per_page: 1
            })
          });

          if (mediaResponse.ok) {
            const mediaData = await mediaResponse.json();
            if (mediaData.results && mediaData.results.length > 0) {
              const media = mediaData.results[0];
              return {
                ...scene,
                backgroundUrl: scene.backgroundType === 'video' 
                  ? media.video_files?.[0]?.link || media.src?.medium
                  : media.src?.medium || media.url
              };
            }
          }
        } catch (error) {
          console.error('Media fetch error for scene:', scene.id, error);
        }
        
        return scene;
      }));

      // Step 3: Generate audio using Kokoro TTS
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, progress: 50 } : p
      ));

      let audioData = null;
      
      // Try Kokoro TTS first
      try {
        const audioResponse = await fetch('/api/kokoro/generate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: project.script,
            voice: selectedVoice || 'kokoro-v1-en-speaker-1',
            settings: voiceSettings
          })
        });

        if (audioResponse.ok) {
          audioData = await audioResponse.json();
        }
      } catch (kokoroError) {
        console.warn('Kokoro TTS failed, trying OpenAI fallback');
      }
      
      // Fallback to OpenAI TTS if Kokoro fails
      if (!audioData) {
        try {
          const fallbackResponse = await fetch('/api/ai/openai-voices', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              text: project.script,
              voice: 'alloy',
              speed: voiceSettings.speed || 1.0
            })
          });
          
          if (fallbackResponse.ok) {
            audioData = await fallbackResponse.json();
          }
        } catch (openaiError) {
          console.warn('OpenAI TTS also failed, using placeholder audio');
          audioData = { audioUrl: '/api/placeholder/audio.mp3', success: true };
        }
      }

      // Step 4: Create video with FFmpeg
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, progress: 70 } : p
      ));

      const videoResponse = await fetch('/api/video/create-enhanced', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          scenes: scenesWithMedia.map(scene => ({
            id: scene.id,
            text: scene.content,
            duration: scene.duration,
            media: { url: scene.backgroundUrl, type: scene.backgroundType },
            visualDescription: scene.backgroundQuery
          })),
          audio: audioData,
          style: videoStyle || 'professional',
          title: project.title,
          settings: {
            resolution: '1920x1080',
            fps: 30,
            duration: scenesWithMedia.reduce((total, scene) => total + scene.duration, 0)
          }
        })
      });

      if (!videoResponse.ok) {
        const errorText = await videoResponse.text();
        throw new Error(`Video creation failed: ${errorText}`);
      }

      const videoResult = await videoResponse.json();

      // Video creation completed successfully
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { 
          ...p, 
          videoUrl: videoResult.videoUrl,
          audioUrl: audioData?.audioUrl,
          status: 'completed',
          progress: 100,
          updatedAt: new Date()
        } : p
      ));
      
      setCompletedVideos(prev => [...prev, {
        ...project,
        videoUrl: videoResult.videoUrl,
        status: 'completed'
      }]);
      
      toast({
        title: "Video Generated Successfully!",
        description: `"${project.title}" is ready for preview.`,
      });

    } catch (error: any) {
      console.error('Video generation error:', error);
      setVideoProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, status: 'error', progress: 0 } : p
      ));
      
      toast({
        title: "Video Generation Failed",
        description: error.message || "An error occurred during video generation.",
        variant: "destructive"
      });
    }
  };

  // Generate all videos
  const generateAllVideos = async () => {
    setIsGeneratingAll(true);
    setOverallProgress(0);
    
    const pendingProjects = videoProjects.filter(p => p.status === 'pending');
    
    for (let i = 0; i < pendingProjects.length; i++) {
      await generateVideo(pendingProjects[i].id);
      setOverallProgress(((i + 1) / pendingProjects.length) * 100);
    }
    
    setIsGeneratingAll(false);
    toast({
      title: "All Videos Generated",
      description: "All lesson videos have been generated successfully.",
    });
  };

  // Edit scene
  const editScene = (projectId: string, scene: Scene) => {
    setEditingScene(scene);
    setShowSceneEditor(true);
  };

  // Save scene changes
  const saveScene = (updatedScene: Scene) => {
    if (!selectedProject) return;
    
    setVideoProjects(prev => prev.map(p => 
      p.id === selectedProject ? {
        ...p,
        scenes: p.scenes.map(s => s.id === updatedScene.id ? updatedScene : s),
        updatedAt: new Date()
      } : p
    ));
    
    setShowSceneEditor(false);
    setEditingScene(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'processing': return 'bg-blue-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle2 className="h-4 w-4" />;
      case 'processing': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'error': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const currentProject = selectedProject ? videoProjects.find(p => p.id === selectedProject) : null;

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h2 className="text-xl font-semibold flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-purple-600" />
              Enhanced Video Production
            </h2>
            <p className="text-muted-foreground">
              Generate professional videos with scene-based workflow and high-quality TTS
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setActiveTab('settings')}>
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
          <Button onClick={onNext}>
            Review & Publish Course
          </Button>
        </div>
      </div>

      {/* Overall Progress */}
      {isGeneratingAll && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
              <div className="flex-1">
                <div className="flex justify-between text-sm mb-1">
                  <span className="font-medium">Generating All Videos...</span>
                  <span>{Math.round(overallProgress)}%</span>
                </div>
                <Progress value={overallProgress} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Production Overview</TabsTrigger>
          <TabsTrigger value="editor">Scene Editor</TabsTrigger>
          <TabsTrigger value="preview">Video Preview</TabsTrigger>
          <TabsTrigger value="settings">Voice & Settings</TabsTrigger>
        </TabsList>

        {/* Production Overview */}
        <TabsContent value="overview" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h3 className="text-lg font-semibold">Video Projects</h3>
              <Badge variant="outline">
                {videoProjects.length} lessons • {completedVideos.length} completed
              </Badge>
            </div>
            <Button 
              onClick={generateAllVideos}
              disabled={isGeneratingAll}
              className="gap-2"
            >
              <Wand2 className="w-4 h-4" />
              Generate All Videos
            </Button>
          </div>

          <div className="space-y-3">
            {videoProjects.map((project) => (
              <Card key={project.id} className="overflow-hidden">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(project.status)}`} />
                      <div>
                        <h4 className="font-medium">{project.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {project.scenes.length} scenes • {project.script.length} characters
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        {getStatusIcon(project.status)}
                        <span className="capitalize">{project.status}</span>
                      </div>
                      
                      {project.status === 'processing' && (
                        <div className="w-24">
                          <Progress value={project.progress} className="h-2" />
                        </div>
                      )}
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedProject(project.id)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      
                      {project.status === 'pending' && (
                        <Button
                          size="sm"
                          onClick={() => generateVideo(project.id)}
                        >
                          <Film className="h-4 w-4 mr-1" />
                          Generate
                        </Button>
                      )}
                      
                      {project.status === 'completed' && project.videoUrl && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(project.videoUrl, '_blank')}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Scene Editor */}
        <TabsContent value="editor" className="space-y-4">
          {currentProject ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Scene Editor: {currentProject.title}</h3>
                <Button
                  onClick={() => generateScenes(currentProject.id)}
                  disabled={currentProject.status === 'processing'}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Regenerate Scenes
                </Button>
              </div>

              {currentProject.scenes.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentProject.scenes.map((scene) => (
                    <Card key={scene.id} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">{scene.title}</CardTitle>
                          <Badge variant="outline">
                            {scene.duration}s
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                          {scene.backgroundUrl ? (
                            scene.backgroundType === 'image' ? (
                              <img 
                                src={scene.backgroundUrl} 
                                alt={scene.title}
                                className="w-full h-full object-cover rounded-lg"
                              />
                            ) : (
                              <video 
                                src={scene.backgroundUrl} 
                                className="w-full h-full object-cover rounded-lg"
                                muted
                              />
                            )
                          ) : (
                            <div className="text-center">
                              <Camera className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                              <p className="text-sm text-gray-500">No background set</p>
                            </div>
                          )}
                        </div>
                        
                        <p className="text-sm text-muted-foreground line-clamp-3">
                          {scene.content}
                        </p>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary">
                              {scene.backgroundType}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {scene.backgroundQuery}
                            </span>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => editScene(currentProject.id, scene)}
                          >
                            Edit
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Layers className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium mb-2">No Scenes Generated</h3>
                    <p className="text-muted-foreground mb-4">
                      Generate scenes to start creating your video lesson.
                    </p>
                    <Button onClick={() => generateScenes(currentProject.id)}>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Generate Scenes
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Monitor className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">Select a Project</h3>
                <p className="text-muted-foreground">
                  Choose a video project from the overview to edit its scenes.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Video Preview */}
        <TabsContent value="preview" className="space-y-4">
          {currentProject?.videoUrl ? (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Video Preview: {currentProject.title}</h3>
              <Card>
                <CardContent className="p-6">
                  <video 
                    src={currentProject.videoUrl} 
                    controls 
                    className="w-full max-w-4xl mx-auto rounded-lg"
                    poster={currentProject.scenes[0]?.backgroundUrl}
                  >
                    Your browser does not support the video tag.
                  </video>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <Video className="h-16 w-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">No Video Available</h3>
                <p className="text-muted-foreground">
                  Generate a video to see the preview here.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Voice & Settings */}
        <TabsContent value="settings" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Voice Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mic className="h-5 w-5" />
                  Voice Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <VoiceServiceSelector
                  onVoiceSelect={(voice: any) => setSelectedVoice(voice.voice)}
                />
                
                <div className="space-y-3">
                  <div>
                    <Label>Speed: {voiceSettings.speed}</Label>
                    <input
                      type="range"
                      min="0.5"
                      max="2.0"
                      step="0.1"
                      value={voiceSettings.speed}
                      onChange={(e) => setVoiceSettings(prev => ({ ...prev, speed: parseFloat(e.target.value) }))}
                      className="w-full"
                    />
                  </div>
                  
                  <div>
                    <Label>Pitch: {voiceSettings.pitch}</Label>
                    <input
                      type="range"
                      min="0.5"
                      max="2.0"
                      step="0.1"
                      value={voiceSettings.pitch}
                      onChange={(e) => setVoiceSettings(prev => ({ ...prev, pitch: parseFloat(e.target.value) }))}
                      className="w-full"
                    />
                  </div>
                  
                  <div>
                    <Label>Volume: {voiceSettings.volume}</Label>
                    <input
                      type="range"
                      min="0.1"
                      max="1.0"
                      step="0.1"
                      value={voiceSettings.volume}
                      onChange={(e) => setVoiceSettings(prev => ({ ...prev, volume: parseFloat(e.target.value) }))}
                      className="w-full"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Video Style Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PaintBucket className="h-5 w-5" />
                  Video Style
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Select value={videoStyle} onValueChange={setVideoStyle}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select video style" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="professional">Professional</SelectItem>
                    <SelectItem value="educational">Educational</SelectItem>
                    <SelectItem value="creative">Creative</SelectItem>
                    <SelectItem value="minimal">Minimal</SelectItem>
                  </SelectContent>
                </Select>
                
                <div className="text-sm text-muted-foreground">
                  {videoStyle === 'professional' && 'Clean, business-focused visuals with corporate aesthetics'}
                  {videoStyle === 'educational' && 'Clear, informative style optimized for learning'}
                  {videoStyle === 'creative' && 'Dynamic, engaging visuals with vibrant colors'}
                  {videoStyle === 'minimal' && 'Simple, distraction-free design with focus on content'}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Scene Editor Dialog */}
      <Dialog open={showSceneEditor} onOpenChange={setShowSceneEditor}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Scene: {editingScene?.title}</DialogTitle>
          </DialogHeader>
          
          {editingScene && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="scene-title">Scene Title</Label>
                <Input
                  id="scene-title"
                  value={editingScene.title}
                  onChange={(e) => setEditingScene(prev => prev ? { ...prev, title: e.target.value } : null)}
                />
              </div>
              
              <div>
                <Label htmlFor="scene-content">Scene Content</Label>
                <Textarea
                  id="scene-content"
                  value={editingScene.content}
                  onChange={(e) => setEditingScene(prev => prev ? { ...prev, content: e.target.value } : null)}
                  rows={4}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="scene-duration">Duration (seconds)</Label>
                  <Input
                    id="scene-duration"
                    type="number"
                    min="1"
                    max="30"
                    value={editingScene.duration}
                    onChange={(e) => setEditingScene(prev => prev ? { ...prev, duration: parseInt(e.target.value) } : null)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="background-type">Background Type</Label>
                  <Select 
                    value={editingScene.backgroundType} 
                    onValueChange={(value: 'image' | 'video') => setEditingScene(prev => prev ? { ...prev, backgroundType: value } : null)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="image">Image</SelectItem>
                      <SelectItem value="video">Video</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="background-query">Background Search Query</Label>
                <Input
                  id="background-query"
                  value={editingScene.backgroundQuery}
                  onChange={(e) => setEditingScene(prev => prev ? { ...prev, backgroundQuery: e.target.value } : null)}
                  placeholder="Enter keywords for background media search"
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowSceneEditor(false)}>
                  Cancel
                </Button>
                <Button onClick={() => editingScene && saveScene(editingScene)}>
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}