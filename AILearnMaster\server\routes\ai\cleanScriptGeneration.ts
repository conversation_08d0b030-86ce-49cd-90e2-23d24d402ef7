import { Request, Response } from 'express';
import { z } from 'zod';
import { 
  generateScript, 
  generateLessonScript as serviceLessonScript, 
  generateModuleScript as serviceModuleScript 
} from '../../services/enhancedScriptGeneration';

export async function generateModuleScript(req: any, res: Response) {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    const schema = z.object({
      moduleTitle: z.string(),
      moduleDescription: z.string().optional(),
      lessonTitles: z.array(z.string()).optional(),
      courseTitle: z.string().optional(),
      targetAudience: z.string().optional(),
      tone: z.string().default('conversational'),
      wordCount: z.number().default(500),
    });

    const result = schema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
    }

    const { moduleTitle, moduleDescription, lessonTitles, courseTitle, targetAudience, tone, wordCount } = result.data;

    try {
      const script = await serviceModuleScript({
        moduleTitle,
        moduleDescription,
        lessonTitles,
        courseTitle,
        targetAudience,
        tone,
        wordCount
      });

      return res.status(200).json({ 
        script,
        success: true
      });

    } catch (aiError: any) {
      console.error("AI script generation error:", aiError);
      return res.status(500).json({ 
        message: "Failed to generate script. Please try again.",
        error: "AI_GENERATION_FAILED"
      });
    }

  } catch (error: any) {
    console.error("Script generation error:", error);
    return res.status(500).json({ 
      message: "Server error during script generation",
      error: "SERVER_ERROR"
    });
  }
}

export async function generateLessonScript(req: any, res: Response) {
  try {
    if (!req.session?.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    const schema = z.object({
      lessonTitle: z.string(),
      lessonDescription: z.string().optional(),
      moduleTitle: z.string().optional(),
      moduleDescription: z.string().optional(),
      courseTitle: z.string().optional(),
      targetAudience: z.string().optional(),
      tone: z.string().default('conversational'),
      wordCount: z.number().default(500),
      keyPoints: z.array(z.string()).optional(),
    });

    const result = schema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ message: "Invalid data", errors: result.error.format() });
    }

    const { lessonTitle, lessonDescription, moduleTitle, moduleDescription, courseTitle, targetAudience, tone, wordCount, keyPoints } = result.data;

    try {
      const script = await serviceLessonScript({
        moduleTitle: moduleTitle || '',
        moduleDescription: moduleDescription || '',
        lessonTitle,
        lessonDescription,
        courseTitle,
        targetAudience,
        tone,
        wordCount,
        keyPoints
      });

      return res.status(200).json({ 
        script,
        success: true
      });

    } catch (aiError: any) {
      console.error("AI lesson script generation error:", aiError);
      return res.status(500).json({ 
        message: "Failed to generate lesson script. Please try again.",
        error: "AI_GENERATION_FAILED"
      });
    }

  } catch (error: any) {
    console.error("Lesson script generation error:", error);
    return res.status(500).json({ 
      message: "Server error during lesson script generation",
      error: "SERVER_ERROR"
    });
  }
}