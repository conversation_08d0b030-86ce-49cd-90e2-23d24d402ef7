import { useEffect, useState } from 'react';
import { useLocation, useRoute } from 'wouter';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { Loader2, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function PaymentSuccess() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentStatus, setPaymentStatus] = useState<'succeeded' | 'processing' | 'failed' | null>(null);
  const [subscriptionId, setSubscriptionId] = useState<string | null>(null);
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  
  // Get the session_id from the URL query parameters
  const searchParams = new URLSearchParams(window.location.search);
  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    if (!sessionId) {
      setError('No session ID provided. Please try again with a valid checkout session.');
      setIsLoading(false);
      return;
    }

    const checkSessionStatus = async () => {
      try {
        console.log('Checking session status for:', sessionId);
        
        const response = await apiRequest('GET', `/api/payments/session-status?sessionId=${sessionId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to verify payment status');
        }
        
        const data = await response.json();
        console.log('Session status data:', data);
        
        // For mock sessions in development, always set as succeeded
        if (sessionId?.startsWith('mock_session_')) {
          console.log('Mock session detected - marking as succeeded');
          setPaymentStatus('succeeded');
          setSubscriptionId(data.subscription || 'mock_subscription');
          
          // Update the user's plan based on URL parameters
          const searchParams = new URLSearchParams(window.location.search);
          const plan = searchParams.get('plan') || 'starter';
          
          // Update plan locally to refresh UI before API call completes
          try {
            // This will update the user's plan in the database
            await apiRequest('POST', '/api/billing/change-plan', { planId: plan });
            console.log('Updated user plan to:', plan);
            
            // Refresh user data to update UI throughout the application
            queryClient.invalidateQueries({ queryKey: ['/api/auth/me'] });
            
            toast({
              title: "Subscription Activated",
              description: `Your ${plan.charAt(0).toUpperCase() + plan.slice(1)} plan is now active.`,
              variant: "default",
            });
          } catch (updateError) {
            console.error('Error updating plan:', updateError);
            toast({
              title: "Update Error",
              description: "There was an issue updating your subscription. Please contact support if this persists.",
              variant: "destructive",
            });
          }
        }
        // Handle different payment statuses for real Stripe sessions
        else if (data.status === 'complete') {
          setPaymentStatus('succeeded');
          setSubscriptionId(data.subscription || null);
          
          // For real Stripe sessions, refresh the user data to update UI
          try {
            // This will invalidate the user's data to refresh the UI
            queryClient.invalidateQueries({ queryKey: ['/api/auth/me'] });
            
            const searchParams = new URLSearchParams(window.location.search);
            const plan = searchParams.get('plan') || 'starter';
            
            toast({
              title: "Payment Successful",
              description: `Your subscription is now active. Enjoy your ${plan.charAt(0).toUpperCase() + plan.slice(1)} plan!`,
              variant: "default",
            });
          } catch (error) {
            console.error('Error refreshing user data:', error);
          }
        } 
        else if (data.status === 'open') {
          setPaymentStatus('processing');
          
          // Set a timeout to check again in 5 seconds if we're still processing
          if (data.client_secret) {
            setTimeout(() => {
              checkSessionStatus();
            }, 5000);
          }
        } 
        else {
          setPaymentStatus('failed');
          throw new Error('Payment was not completed successfully. Please try again or contact support.');
        }
      } catch (err: any) {
        console.error('Error checking session status:', err);
        setError(err.message || 'An unexpected error occurred while verifying your payment');
        setPaymentStatus('failed');
      } finally {
        setIsLoading(false);
      }
    };

    checkSessionStatus();
  }, [sessionId, toast, queryClient]);

  const navigateToDashboard = () => {
    setLocation('/dashboard');
  };

  const navigateToProfile = () => {
    setLocation('/profile');
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-lg font-medium">Verifying your payment...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container max-w-md py-16">
        <Card>
          <CardHeader>
            <CardTitle className="text-destructive">Payment Verification Failed</CardTitle>
            <CardDescription>We couldn't verify your payment status</CardDescription>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
          </CardContent>
          <CardFooter className="flex justify-end space-x-2">
            <Button onClick={() => window.history.back()} variant="outline">
              Go Back
            </Button>
            <Button onClick={() => setLocation('/pricing')}>
              Try Again
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (paymentStatus === 'processing') {
    return (
      <div className="container max-w-md py-16">
        <Card className="border-amber-200 bg-amber-50 dark:bg-amber-950/20">
          <CardHeader className="pb-4">
            <div className="flex justify-center mb-4">
              <Loader2 className="h-16 w-16 text-amber-500 animate-spin" />
            </div>
            <CardTitle className="text-center text-2xl">Payment Processing</CardTitle>
            <CardDescription className="text-center">
              Your payment is being processed
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="mb-4">
              Thank you for your subscription. We're currently processing your payment. This usually takes just a few moments.
            </p>
            <p className="text-sm text-muted-foreground">
              This page will automatically update when your payment is complete.
            </p>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={navigateToDashboard} className="w-full">
              Continue to Dashboard
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container max-w-md py-16">
      <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
        <CardHeader className="pb-4">
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-center text-2xl">Payment Successful!</CardTitle>
          <CardDescription className="text-center">
            Your subscription is now active
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          {subscriptionId && (
            <p className="text-sm mb-4">
              Subscription ID: <span className="font-mono">{subscriptionId}</span>
            </p>
          )}
          <p className="mb-4">
            Thank you for subscribing to our service! You now have access to all the features included in your plan.
          </p>
          <p className="text-sm text-muted-foreground">
            Your first billing cycle has started. You can manage your subscription anytime from your profile.
          </p>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row justify-center gap-2">
          <Button onClick={navigateToProfile} variant="outline" className="w-full sm:w-auto">
            Manage Subscription
          </Button>
          <Button onClick={navigateToDashboard} className="w-full sm:w-auto">
            Go to Dashboard
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}