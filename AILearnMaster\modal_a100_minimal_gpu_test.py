"""
Minimal Modal A100 GPU Test - Focus on GPU availability
"""

import modal
import time

# Modal App Configuration
app = modal.App("courseai-a100-gpu-test")

# Minimal A100 GPU Image
gpu_image = (
    modal.Image.debian_slim(python_version="3.11")
    .pip_install([
        "torch==2.1.0",
        "transformers==4.36.0",
        "requests==2.31.0",
        "fastapi[standard]==0.104.1"
    ])
)

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    timeout=300,
    memory=16384
)
def test_gpu_availability() -> dict:
    """Test if A100 GPU is properly available"""
    try:
        import torch
        
        # Basic GPU checks
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        
        if gpu_available:
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            
            # Test GPU computation
            test_tensor = torch.randn(1000, 1000, device="cuda")
            result = torch.mm(test_tensor, test_tensor.t())
            torch.cuda.synchronize()
            
            # Cleanup
            del test_tensor, result
            torch.cuda.empty_cache()
            
            return {
                "status": "success",
                "gpu_available": True,
                "gpu_count": gpu_count,
                "gpu_name": gpu_name,
                "gpu_memory_total_gb": round(gpu_memory_total, 2),
                "gpu_test": "passed",
                "timestamp": int(time.time() * 1000)
            }
        else:
            return {
                "status": "error",
                "gpu_available": False,
                "gpu_count": 0,
                "gpu_name": "No GPU",
                "error": "CUDA not available",
                "timestamp": int(time.time() * 1000)
            }
            
    except Exception as e:
        return {
            "status": "error",
            "gpu_available": False,
            "error": str(e),
            "timestamp": int(time.time() * 1000)
        }

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    timeout=600,
    memory=24576
)
def test_mistral_simple(prompt: str = "Hello, world!") -> dict:
    """Simple Mistral test with A100 GPU"""
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        # Load small Mistral model
        model_name = "mistralai/Mistral-7B-Instruct-v0.1"
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        
        # Generate response
        inputs = tokenizer(prompt, return_tensors="pt").to("cuda")
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=50,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated_text = response[len(prompt):].strip()
        
        # Cleanup
        del model, tokenizer, inputs, outputs
        torch.cuda.empty_cache()
        
        return {
            "status": "success",
            "prompt": prompt,
            "generated_text": generated_text,
            "gpu_used": True,
            "timestamp": int(time.time() * 1000)
        }
        
    except Exception as e:
        import torch
        torch.cuda.empty_cache()
        return {
            "status": "error",
            "error": str(e),
            "prompt": prompt,
            "gpu_used": False,
            "timestamp": int(time.time() * 1000)
        }

# Web endpoints
@app.function(
    image=gpu_image,
    timeout=60
)
@modal.fastapi_endpoint(method="GET")
def health():
    """Health check with GPU test"""
    return test_gpu_availability.remote()

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    timeout=600
)
@modal.fastapi_endpoint(method="POST")
def mistral(request_data: dict):
    """Mistral endpoint"""
    prompt = request_data.get("prompt", "Hello, world!")
    return test_mistral_simple.remote(prompt)

if __name__ == "__main__":
    print("Modal A100 GPU Test App")
    print("Endpoints:")
    print("  /health - GPU availability test")
    print("  /mistral - Simple Mistral test")
