/* Import accessibility styles */
@import './styles/accessibility.css';
/* Import enhanced visual styles */
@import './styles/enhanced-visuals.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Form submission states */
  form.submitting {
    position: relative;
    pointer-events: none;
  }
  
  form.submitting::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 10;
    border-radius: 0.5rem;
  }
}

@layer base {
  :root {
    --color-primary-rgb: 91, 33, 245;
  }
  
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
  
  h1 {
    @apply text-4xl font-extrabold tracking-tight lg:text-5xl;
  }
  
  h2 {
    @apply text-3xl font-bold tracking-tight;
  }
  
  h3 {
    @apply text-2xl font-semibold;
  }
}

@layer components {
  .card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
  }
  
  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-[#9f6aff];
  }
  
  .gradient-text-reverse {
    @apply bg-clip-text text-transparent bg-gradient-to-l from-primary to-[#9f6aff];
  }
  
  .gradient-button {
    @apply bg-gradient-to-r from-primary to-[#9f6aff] text-white font-medium transition-all duration-300 hover:shadow-lg hover:translate-y-[-2px];
  }
  
  .input-focus {
    @apply focus:ring-2 focus:ring-primary/20 focus:border-primary;
  }
  
  .button-effect {
    @apply active:scale-95 transition-transform;
  }
}

@layer utilities {
  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm;
  }
  
  .page-container {
    @apply container mx-auto px-4 py-8 md:py-12;
  }
  
  .grid-responsive {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }
  
  /* Landing Page Preview Responsive Styling */
  .landing-page-preview-container {
    max-width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }
  
  .scale-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    transition: transform 0.3s ease;
  }
  
  .scale-container .preview-content {
    transform-origin: top center;
    width: 100%;
    min-height: 100px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  /* Responsive scaling for different screen widths */
  @media (max-width: 768px) {
    .scale-container {
      width: 100%;
    }
    
    .scale-container .preview-content > div {
      width: 100% !important;
      max-width: 100% !important;
    }
  }
  
  /* Make images in preview responsive */
  .preview-content img {
    max-width: 100%;
    height: auto;
  }
  
  /* Make landing page content responsive for mobile */
  @media (max-width: 640px) {
    .preview-content [style*="display: flex"],
    .preview-content [style*="display:flex"] {
      flex-direction: column !important;
    }
    
    .preview-content [style*="grid-template-columns"],
    .preview-content [style*="grid-template-columns:"] {
      grid-template-columns: 1fr !important;
    }
    
    .preview-content [style*="font-size: 2.5rem"],
    .preview-content [style*="font-size:2.5rem"] {
      font-size: 2rem !important;
    }
  }
}