import { useEffect, useState } from "react";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { Loader2, ArrowRight, Search, Filter, Star, Clock, PlusCircle, Zap, Sparkles, CheckCircle, FileText, PenTool, Mic, Volume2 } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

// Types
interface Template {
  id: number;
  name: string;
  description: string;
  icon: string;
  type: string;
  category: string | null;
  structure: any | null;
}

interface TemplateHistory {
  id: number;
  userId: number;
  templateId: number;
  name: string;
  prompt: string;
  result: string;
  createdAt: string;
  favorited: boolean;
  template?: Template;
}

interface TemplateExecutionResponse {
  id: number;
  result: string;
  aiCreditsUsed: number;
}

// Form schema for template execution
const templateFormSchema = z.object({
  prompt: z.string().min(5, "Prompt must be at least 5 characters"),
  templateId: z.number(),
  name: z.string().min(3, "Name must be at least 3 characters"),
});

// Convert the form schema to TypeScript type
type TemplateFormValues = z.infer<typeof templateFormSchema>;

export default function AITemplatesPage() {
  const { user } = useAuth();
  const [location, setLocation] = useLocation();
  const { toast } = useToast();
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filter, setFilter] = useState("all");
  const [activeTab, setActiveTab] = useState("templates"); // templates, history, favorites

  const form = useForm<TemplateFormValues>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: {
      prompt: "",
      templateId: 0,
      name: ""
    },
  });

  // Query for templates
  const { data: templates, isLoading: isLoadingTemplates } = useQuery({
    queryKey: ['/api/templates'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/templates');
      return res.json();
    },
  });

  // Query for template history
  const { data: templateHistory, isLoading: isLoadingHistory } = useQuery({
    queryKey: ['/api/template-history'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/template-history');
      return res.json();
    },
  });

  // Query for user stats (to display AI credits)
  const { data: userStats } = useQuery({
    queryKey: ['/api/user-stats'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/user-stats');
      return res.json();
    },
  });

  // Handler functions for template actions
  const handleQuickCourseGenerator = () => {
    setLocation('/course-creation/traditional-course');
  };

  const handleScriptWriter = () => {
    const template = templates?.find((t: any) => t.type === 'content-generation');
    if (template) {
      setSelectedTemplate(template);
      setIsDialogOpen(true);
    }
  };

  const handleAIVoiceover = () => {
    const template = templates?.find((t: any) => t.type === 'voice-generation');
    if (template) {
      setSelectedTemplate(template);
      setIsDialogOpen(true);
    }
  };

  // Mutation to execute template
  const executeTemplateMutation = useMutation({
    mutationFn: async (data: TemplateFormValues) => {
      const res = await apiRequest('POST', '/api/templates/execute', data);
      return res.json();
    },
    onSuccess: (data: TemplateExecutionResponse) => {
      toast({
        title: "Template executed successfully",
        description: `Used ${data.aiCreditsUsed} AI credits`,
      });
      queryClient.invalidateQueries({ queryKey: ['/api/template-history'] });
      queryClient.invalidateQueries({ queryKey: ['/api/user-stats'] });
      setIsDialogOpen(false);
      form.reset();
    },
    onError: (error: any) => {
      toast({
        title: "Failed to execute template",
        description: error.message || "An error occurred",
        variant: "destructive",
      });
    },
  });

  // Mutation to favorite/unfavorite a history item
  const toggleFavoriteMutation = useMutation({
    mutationFn: async ({ historyId, favorited }: { historyId: number, favorited: boolean }) => {
      const res = await apiRequest('PATCH', `/api/template-history/${historyId}/favorite`, { favorited });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/template-history'] });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to update favorite status",
        description: error.message || "An error occurred",
        variant: "destructive",
      });
    },
  });

  // Open template dialog
  const openTemplateDialog = (template: Template) => {
    setSelectedTemplate(template);
    form.reset({
      prompt: "",
      templateId: template.id,
      name: `${template.name} - ${new Date().toLocaleDateString()}`
    });
    setIsDialogOpen(true);
  };
  
  // Check for template in URL when component mounts
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const templateType = params.get('template');
    const templateId = params.get('id');
    
    if (templateType && templateId && templates) {
      const template = templates.find((t: Template) => 
        t.type === templateType && t.id === parseInt(templateId)
      );
      
      if (template) {
        openTemplateDialog(template);
        // Clear URL params after opening dialog
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    }
  }, [templates]);

  // Filter and search functionality
  const filteredHistory = templateHistory?.filter((item: TemplateHistory) => {
    const matchesSearch = searchQuery === "" || 
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
      item.prompt.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = 
      filter === "all" || 
      (filter === "favorites" && item.favorited) ||
      (filter === "course_generator" && item.template?.type === "course_generator") ||
      (filter === "script_generator" && item.template?.type === "script_generator") ||
      (filter === "voice_generator" && item.template?.type === "voice_generator");
    
    return matchesSearch && matchesFilter;
  });

  // Handle form submission
  const onSubmit = (values: TemplateFormValues) => {
    executeTemplateMutation.mutate(values);
  };

  return (
    <div className="container max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">AI Templates</h1>
          <p className="text-slate-600 mt-1">Create amazing course content using our pre-built AI templates</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="px-3 py-1">
            <Zap className="w-4 h-4 mr-1 text-amber-500" />
            <span className="font-medium">{userStats?.aiCredits || 0} AI Credits</span>
          </Badge>
          <Button asChild variant="outline">
            <Link href="/pricing">
              Get More Credits
            </Link>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="favorites">Favorites</TabsTrigger>
        </TabsList>

        {/* Templates Tab */}
        <TabsContent value="templates" className="space-y-8">
          {/* AI-Powered Templates Section */}
          <div>
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">AI-Powered Templates</h2>
                <p className="text-gray-600">Create professional content with AI assistance</p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Quick Course Generator */}
              <Card className="group shadow-sm hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-600 rounded-lg group-hover:bg-blue-700 transition-colors">
                      <Zap className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg group-hover:text-blue-700 transition-colors">Quick Course Generator</CardTitle>
                      <CardDescription className="text-sm">Create 15-minute courses instantly</CardDescription>
                    </div>
                  </div>
                  <Badge variant="secondary" className="w-fit">Max 15 minutes</Badge>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm text-gray-700 space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>AI course structure generation</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Automatic script writing</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Stock media integration</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Complete course assembly</span>
                    </div>
                  </div>
                  <div className="bg-white/50 p-3 rounded-lg text-xs text-gray-600">
                    Perfect for micro-learning, tutorials, and quick training sessions
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white group-hover:shadow-md transition-all"
                    onClick={() => handleQuickCourseGenerator()}
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Start Quick Course
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>

              {/* Script Writer */}
              <Card className="group shadow-sm hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-emerald-600 rounded-lg group-hover:bg-emerald-700 transition-colors">
                      <FileText className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg group-hover:text-emerald-700 transition-colors">Script Writer</CardTitle>
                      <CardDescription className="text-sm">Professional scripts with Mistral AI</CardDescription>
                    </div>
                  </div>
                  <Badge variant="secondary" className="w-fit">Mistral AI</Badge>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm text-gray-700 space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Engaging narrative structure</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Educational content optimization</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Multiple tone options</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Ready-to-record format</span>
                    </div>
                  </div>
                  <div className="bg-white/50 p-3 rounded-lg text-xs text-gray-600">
                    Generate professional scripts for lessons, presentations, and video content
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white group-hover:shadow-md transition-all"
                    onClick={() => handleScriptWriter()}
                  >
                    <PenTool className="h-4 w-4 mr-2" />
                    Write Script
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>

              {/* AI Voiceover */}
              <Card className="group shadow-sm hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-pink-50 hover:from-purple-100 hover:to-pink-100">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-600 rounded-lg group-hover:bg-purple-700 transition-colors">
                      <Mic className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-lg group-hover:text-purple-700 transition-colors">AI Voiceover</CardTitle>
                      <CardDescription className="text-sm">Natural speech with Coqui & Kokoro</CardDescription>
                    </div>
                  </div>
                  <Badge variant="secondary" className="w-fit">Coqui + Kokoro</Badge>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-sm text-gray-700 space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Natural voice synthesis</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Multiple voice options</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>Adjustable speed & pitch</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span>High-quality audio output</span>
                    </div>
                  </div>
                  <div className="bg-white/50 p-3 rounded-lg text-xs text-gray-600">
                    Convert any text to professional voiceover with advanced TTS models
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white group-hover:shadow-md transition-all"
                    onClick={() => handleAIVoiceover()}
                  >
                    <Volume2 className="h-4 w-4 mr-2" />
                    Generate Voice
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
          
          {/* Marketing Templates Section */}
          <div>
            <h2 className="text-xl font-bold mb-4">Marketing Course Templates</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {isLoadingTemplates ? (
                // Skeleton loaders for templates
                Array.from({ length: 2 }).map((_, i) => (
                  <Card key={i} className="shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader>
                      <Skeleton className="h-6 w-2/3 mb-2" />
                      <Skeleton className="h-4 w-full" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-24 w-full" />
                    </CardContent>
                    <CardFooter>
                      <Skeleton className="h-9 w-full" />
                    </CardFooter>
                  </Card>
                ))
              ) : (
                templates?.filter((t: Template) => t.type === 'course_template' && t.category === 'marketing').map((template: Template) => (
                  <Card key={template.id} className="shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <span className="text-xl text-primary">🚀</span>
                        <CardTitle>{template.name}</CardTitle>
                      </div>
                      <CardDescription>{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-slate-50 p-3 rounded text-sm space-y-3">
                        <p className="font-medium">Modules:</p>
                        <ul className="space-y-2 list-disc pl-5">
                          {template.structure?.modules.map((module: any, index: number) => (
                            <li key={index}>
                              <span className="font-medium">{module.title}</span>
                              <p className="text-xs text-slate-500">{module.description}</p>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        className="w-full" 
                        onClick={() => openTemplateDialog(template)}
                      >
                        Use Template
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                ))
              )}
            </div>
          </div>
          
          {/* Business Templates Section */}
          <div>
            <h2 className="text-xl font-bold mb-4">Business & Management Templates</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {isLoadingTemplates ? (
                // Skeleton loaders for templates
                Array.from({ length: 2 }).map((_, i) => (
                  <Card key={i} className="shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader>
                      <Skeleton className="h-6 w-2/3 mb-2" />
                      <Skeleton className="h-4 w-full" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-24 w-full" />
                    </CardContent>
                    <CardFooter>
                      <Skeleton className="h-9 w-full" />
                    </CardFooter>
                  </Card>
                ))
              ) : (
                templates?.filter((t: Template) => t.type === 'course_template' && t.category === 'business').map((template: Template) => (
                  <Card key={template.id} className="shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <span className="text-xl text-primary">🏢</span>
                        <CardTitle>{template.name}</CardTitle>
                      </div>
                      <CardDescription>{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-slate-50 p-3 rounded text-sm space-y-3">
                        <p className="font-medium">Modules:</p>
                        <ul className="space-y-2 list-disc pl-5">
                          {template.structure?.modules.map((module: any, index: number) => (
                            <li key={index}>
                              <span className="font-medium">{module.title}</span>
                              <p className="text-xs text-slate-500">{module.description}</p>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        className="w-full" 
                        onClick={() => openTemplateDialog(template)}
                      >
                        Use Template
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                ))
              )}
            </div>
          </div>
          
          {/* Personal Development Templates Section */}
          <div>
            <h2 className="text-xl font-bold mb-4">Personal Development Templates</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {isLoadingTemplates ? (
                // Skeleton loaders for templates
                Array.from({ length: 2 }).map((_, i) => (
                  <Card key={i} className="shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader>
                      <Skeleton className="h-6 w-2/3 mb-2" />
                      <Skeleton className="h-4 w-full" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-24 w-full" />
                    </CardContent>
                    <CardFooter>
                      <Skeleton className="h-9 w-full" />
                    </CardFooter>
                  </Card>
                ))
              ) : (
                templates?.filter((t: Template) => t.type === 'course_template' && t.category === 'personal_development').map((template: Template) => (
                  <Card key={template.id} className="shadow-sm hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <span className="text-xl text-primary">🌱</span>
                        <CardTitle>{template.name}</CardTitle>
                      </div>
                      <CardDescription>{template.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="bg-slate-50 p-3 rounded text-sm space-y-3">
                        <p className="font-medium">Modules:</p>
                        <ul className="space-y-2 list-disc pl-5">
                          {template.structure?.modules.map((module: any, index: number) => (
                            <li key={index}>
                              <span className="font-medium">{module.title}</span>
                              <p className="text-xs text-slate-500">{module.description}</p>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        className="w-full" 
                        onClick={() => openTemplateDialog(template)}
                      >
                        Use Template
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </CardFooter>
                  </Card>
                ))
              )}
            </div>
          </div>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-between mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <Input 
                placeholder="Search templates..." 
                className="pl-10 w-full sm:w-80"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={filter} onValueChange={setFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter templates" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Templates</SelectItem>
                <SelectItem value="favorites">Favorites</SelectItem>
                <SelectItem value="course_generator">Course Generator</SelectItem>
                <SelectItem value="script_generator">Script Generator</SelectItem>
                <SelectItem value="voice_generator">Voice Generator</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {isLoadingHistory ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="shadow-sm">
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-6 w-1/3" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-2/3 mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredHistory?.length > 0 ? (
            <div className="space-y-4">
              {filteredHistory.map((item: TemplateHistory) => (
                <Card key={item.id} className="shadow-sm">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2">
                        <span className="text-lg text-primary">
                          {item.template?.type === 'course_generator' ? '🤖' : 
                           item.template?.type === 'script_generator' ? '📝' : 
                           item.template?.type === 'voice_generator' ? '🎤' : '✨'}
                        </span>
                        <CardTitle className="text-lg">{item.name}</CardTitle>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => toggleFavoriteMutation.mutate({ historyId: item.id, favorited: !item.favorited })}
                        >
                          <Star className={`h-4 w-4 ${item.favorited ? 'fill-amber-400 text-amber-400' : 'text-slate-400'}`} />
                        </Button>
                        <Badge variant="outline">
                          {item.template?.type === 'course_generator' && 'Course Generator'}
                          {item.template?.type === 'script_generator' && 'Script Generator'}
                          {item.template?.type === 'voice_generator' && 'Voice Generator'}
                        </Badge>
                        <div className="text-sm text-slate-500 flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {new Date(item.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-2">
                      <h4 className="text-sm font-medium text-slate-700">Prompt:</h4>
                      <p className="text-sm text-slate-600">{item.prompt}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-slate-700">Result:</h4>
                      <div className="bg-slate-50 p-3 rounded text-sm text-slate-600 mt-1 max-h-40 overflow-y-auto">
                        {item.result.split('\n').map((line, i) => (
                          <p key={i} className={line.trim() === '' ? 'h-4' : ''}>{line}</p>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(item.result);
                        toast({
                          title: "Copied to clipboard",
                          description: "The result has been copied to your clipboard"
                        });
                      }}
                    >
                      Copy Result
                    </Button>
                    {item.template?.type === 'course_generator' && (
                      <Button size="sm">
                        Create Course
                      </Button>
                    )}
                    {item.template?.type === 'script_generator' && (
                      <Button size="sm">
                        Add to Lesson
                      </Button>
                    )}
                    {item.template?.type === 'voice_generator' && (
                      <Button size="sm">
                        Download Audio
                      </Button>
                    )}
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <div className="bg-slate-50 rounded-md p-8 text-center">
              <div className="mx-auto h-20 w-20 flex items-center justify-center rounded-full bg-slate-100">
                <Clock className="h-10 w-10 text-slate-400" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-slate-900">No template history found</h3>
              <p className="mt-2 text-sm text-slate-600">
                {searchQuery || filter !== 'all' 
                  ? 'No results found for your search. Try different filters or clear your search.'
                  : 'Start by using a template to generate content. Your history will appear here.'}
              </p>
              {(searchQuery || filter !== 'all') && (
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={() => {
                    setSearchQuery('');
                    setFilter('all');
                  }}
                >
                  Clear Filters
                </Button>
              )}
            </div>
          )}
        </TabsContent>

        {/* Favorites Tab */}
        <TabsContent value="favorites" className="space-y-4">
          {isLoadingHistory ? (
            <div className="space-y-4">
              {Array.from({ length: 2 }).map((_, i) => (
                <Card key={i} className="shadow-sm">
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-6 w-1/3" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-4 w-full mb-2" />
                    <Skeleton className="h-4 w-2/3 mb-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : templateHistory?.filter((item: TemplateHistory) => item.favorited).length > 0 ? (
            <div className="space-y-4">
              {templateHistory
                .filter((item: TemplateHistory) => item.favorited)
                .map((item: TemplateHistory) => (
                  <Card key={item.id} className="shadow-sm">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <span className="text-lg text-primary">
                            {item.template?.type === 'course_generator' ? '🤖' : 
                             item.template?.type === 'script_generator' ? '📝' : 
                             item.template?.type === 'voice_generator' ? '🎤' : '✨'}
                          </span>
                          <CardTitle className="text-lg">{item.name}</CardTitle>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => toggleFavoriteMutation.mutate({ historyId: item.id, favorited: false })}
                          >
                            <Star className="h-4 w-4 fill-amber-400 text-amber-400" />
                          </Button>
                          <Badge variant="outline">
                            {item.template?.type === 'course_generator' && 'Course Generator'}
                            {item.template?.type === 'script_generator' && 'Script Generator'}
                            {item.template?.type === 'voice_generator' && 'Voice Generator'}
                          </Badge>
                          <div className="text-sm text-slate-500 flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {new Date(item.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="mb-2">
                        <h4 className="text-sm font-medium text-slate-700">Prompt:</h4>
                        <p className="text-sm text-slate-600">{item.prompt}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-slate-700">Result:</h4>
                        <div className="bg-slate-50 p-3 rounded text-sm text-slate-600 mt-1 max-h-40 overflow-y-auto">
                          {item.result.split('\n').map((line, i) => (
                            <p key={i} className={line.trim() === '' ? 'h-4' : ''}>{line}</p>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => {
                          navigator.clipboard.writeText(item.result);
                          toast({
                            title: "Copied to clipboard",
                            description: "The result has been copied to your clipboard"
                          });
                        }}
                      >
                        Copy Result
                      </Button>
                      {item.template?.type === 'course_generator' && (
                        <Button size="sm">
                          Create Course
                        </Button>
                      )}
                      {item.template?.type === 'script_generator' && (
                        <Button size="sm">
                          Add to Lesson
                        </Button>
                      )}
                      {item.template?.type === 'voice_generator' && (
                        <Button size="sm">
                          Download Audio
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
            </div>
          ) : (
            <div className="bg-slate-50 rounded-md p-8 text-center">
              <div className="mx-auto h-20 w-20 flex items-center justify-center rounded-full bg-slate-100">
                <Star className="h-10 w-10 text-slate-400" />
              </div>
              <h3 className="mt-4 text-lg font-medium text-slate-900">No favorite templates</h3>
              <p className="mt-2 text-sm text-slate-600">
                Star your favorite template results to find them quickly later.
              </p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => setActiveTab('history')}
              >
                Go to History
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Template Execution Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedTemplate && (
                <>
                  <span className="text-xl text-primary">
                    {selectedTemplate.icon === 'ri-robot-line' ? '🤖' : 
                     selectedTemplate.icon === 'ri-file-text-line' ? '📝' : 
                     selectedTemplate.icon === 'ri-voice-recognition-line' ? '🎤' : 
                     selectedTemplate.type === 'course_template' && selectedTemplate.category === 'marketing' ? '🚀' :
                     selectedTemplate.type === 'course_template' && selectedTemplate.category === 'business' ? '🏢' :
                     selectedTemplate.type === 'course_template' && selectedTemplate.category === 'personal_development' ? '🌱' : '✨'}
                  </span>
                  <span>{selectedTemplate.name}</span>
                </>
              )}
            </DialogTitle>
            <DialogDescription>
              {selectedTemplate?.description}
            </DialogDescription>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Give this template execution a name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="prompt"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prompt</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder={selectedTemplate?.type === 'course_generator' 
                          ? "Describe the course you want to create..." 
                          : selectedTemplate?.type === 'script_generator'
                          ? "Describe the script you want to generate..."
                          : selectedTemplate?.type === 'voice_generator'
                          ? "Enter the text you want to convert to speech..."
                          : selectedTemplate?.type === 'course_template'
                          ? "Describe any customizations you'd like to make to this template..."
                          : "Enter your prompt..."}
                        className="min-h-[150px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      {selectedTemplate?.type === 'course_generator' && "Be specific about the target audience, learning objectives, and desired outcomes."}
                      {selectedTemplate?.type === 'script_generator' && "Include key points, tone, and target audience for better results."}
                      {selectedTemplate?.type === 'voice_generator' && "Keep the text clear and conversational for the best voice output."}
                      {selectedTemplate?.type === 'course_template' && "Describe any specific details you want to include in your course based on this template."}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter className="gap-2 sm:gap-0">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit"
                  disabled={executeTemplateMutation.isPending}
                >
                  {executeTemplateMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {selectedTemplate?.type === 'course_generator' 
                    ? "Generate Course Structure" 
                    : selectedTemplate?.type === 'script_generator'
                    ? "Generate Script"
                    : selectedTemplate?.type === 'voice_generator'
                    ? "Generate Voice"
                    : selectedTemplate?.type === 'course_template'
                    ? "Use Template"
                    : "Execute Template"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}