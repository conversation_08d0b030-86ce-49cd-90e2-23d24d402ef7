import { storage } from '../storage';
import { Request, Response } from 'express';

// Get all available platforms with their connection status for the current user
export const getAllPlatforms = async (req: Request, res: Response) => {
  try {
    const platforms = await storage.getAllPlatforms(req.user?.id);
    res.json(platforms);
  } catch (error) {
    console.error('Error getting platforms:', error);
    res.status(500).json({ error: 'Failed to retrieve platforms' });
  }
};

// Get a specific platform by slug
export const getPlatformBySlug = async (req: Request, res: Response) => {
  try {
    const { slug } = req.params;
    const platform = await storage.getPlatformBySlug(slug);
    
    if (!platform) {
      return res.status(404).json({ error: 'Platform not found' });
    }
    
    res.json(platform);
  } catch (error) {
    console.error('Error getting platform:', error);
    res.status(500).json({ error: 'Failed to retrieve platform' });
  }
};

// Connect to a platform
export const connectPlatform = async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'You must be logged in to connect a platform' });
  }
  
  try {
    const { slug } = req.params;
    const result = await storage.connectPlatform(req.user!.id, slug);
    
    if (!result) {
      return res.status(404).json({ error: 'Platform not found' });
    }
    
    res.json({ success: true, message: `Connected to ${slug} successfully` });
  } catch (error) {
    console.error('Error connecting platform:', error);
    res.status(500).json({ error: 'Failed to connect platform' });
  }
};

// Disconnect from a platform
export const disconnectPlatform = async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'You must be logged in to disconnect a platform' });
  }
  
  try {
    const { id } = req.params;
    const result = await storage.disconnectPlatform(req.user!.id, parseInt(id));
    
    if (!result) {
      return res.status(404).json({ error: 'Platform connection not found' });
    }
    
    res.json({ success: true, message: 'Disconnected from platform successfully' });
  } catch (error) {
    console.error('Error disconnecting platform:', error);
    res.status(500).json({ error: 'Failed to disconnect platform' });
  }
};

// OAuth callback handler
export const oauthCallback = async (req: Request, res: Response) => {
  try {
    const { platform, code, state } = req.query;
    
    if (!platform || !code) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }
    
    // In a real implementation, we would:
    // 1. Validate the state parameter to prevent CSRF
    // 2. Exchange the code for an access token
    // 3. Store the token in the database
    // 4. Connect the platform to the user
    
    // For the demo, we'll just connect the platform
    if (req.isAuthenticated()) {
      await storage.connectPlatform(req.user!.id, platform as string);
    }
    
    // Return a script that sends a message to the opener and closes the window
    const html = `
      <html>
        <body>
          <script>
            if (window.opener) {
              window.opener.postMessage({
                type: 'oauth_callback',
                platform: '${platform}',
                success: true
              }, window.location.origin);
              window.close();
            } else {
              window.location.href = '/platform-integrations';
            }
          </script>
          <p>Authentication successful. You can close this window.</p>
        </body>
      </html>
    `;
    
    res.send(html);
  } catch (error) {
    console.error('Error in OAuth callback:', error);
    res.status(500).json({ error: 'Failed to process OAuth callback' });
  }
};

// Publish a course to a platform
export const publishCourseToPlatform = async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'You must be logged in to publish a course' });
  }
  
  try {
    const { courseId, platformSlug } = req.params;
    const options = req.body.options || {};
    
    const result = await storage.publishCourseToPlatform(
      req.user!.id, 
      parseInt(courseId), 
      platformSlug, 
      options
    );
    
    res.json(result);
  } catch (error) {
    console.error('Error publishing course:', error);
    res.status(500).json({ error: 'Failed to publish course' });
  }
};

// Get a course's publications
export const getCoursePublications = async (req: Request, res: Response) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ error: 'You must be logged in to view publications' });
  }
  
  try {
    const { courseId } = req.params;
    const publications = await storage.getCoursePublications(req.user!.id, parseInt(courseId));
    
    res.json(publications);
  } catch (error) {
    console.error('Error getting publications:', error);
    res.status(500).json({ error: 'Failed to retrieve publications' });
  }
};