import { Router } from 'express';
import { generateCoursePDF } from '../services/pdfGenerator';

const router = Router();

router.post('/course/pdf', async (req, res) => {
  try {
    const { courseData } = req.body;
    
    if (!courseData || !courseData.title || !courseData.modules) {
      return res.status(400).json({ 
        error: 'Invalid course data. Title and modules are required.' 
      });
    }

    // Generate PDF buffer
    const pdfBuffer = await generateCoursePDF(courseData);
    
    // Set response headers for PDF download
    const fileName = `${courseData.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_course.pdf`;
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Length', pdfBuffer.length);
    
    // Send the PDF buffer
    res.send(pdfBuffer);
    
  } catch (error) {
    console.error('PDF generation error:', error);
    res.status(500).json({ 
      error: 'Failed to generate PDF. Please try again.' 
    });
  }
});

export default router;