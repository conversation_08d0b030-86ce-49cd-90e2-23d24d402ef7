import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { Search, ExternalLink, Info, AlertCircle, Loader2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface StockVideoSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelect: (videoUrl: string, source: string, videoId: string) => void;
}

interface StockVideo {
  id: string;
  width: number;
  height: number;
  url: string;
  thumbnail: string;
  preview: string;
  duration: number;
  user: {
    name: string;
    url: string;
  };
  source: string;
}

export function StockVideoSelector({ open, onOpenChange, onSelect }: StockVideoSelectorProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeProvider, setActiveProvider] = useState<"pexels" | "pixabay">("pexels");
  const { toast } = useToast();
  
  // Function to handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchTerm.trim()) {
      toast({
        title: "Search term required",
        description: "Please enter a search term to find stock videos",
        variant: "destructive",
      });
      return;
    }
    
    // Refetch the data when search term changes
    refetchPexels();
    refetchPixabay();
  };

  // Query for Pexels videos
  const { 
    data: pexelsData, 
    isLoading: pexelsLoading, 
    error: pexelsError,
    refetch: refetchPexels
  } = useQuery({
    queryKey: ['pexels', searchTerm],
    queryFn: async () => {
      if (!searchTerm) return { videos: [] };
      const response = await fetch(`/api/pexels/videos?query=${encodeURIComponent(searchTerm)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch Pexels videos');
      }
      return response.json();
    },
    enabled: !!searchTerm, // Only run if search term is provided
  });

  // Query for Pixabay videos
  const { 
    data: pixabayData, 
    isLoading: pixabayLoading, 
    error: pixabayError,
    refetch: refetchPixabay
  } = useQuery({
    queryKey: ['pixabay', searchTerm],
    queryFn: async () => {
      if (!searchTerm) return { hits: [] };
      const response = await fetch(`/api/pixabay/videos?query=${encodeURIComponent(searchTerm)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch Pixabay videos');
      }
      return response.json();
    },
    enabled: !!searchTerm, // Only run if search term is provided
  });

  // Handle video selection
  const handleVideoSelect = async (video: StockVideo) => {
    try {
      // Determine the import endpoint based on the source
      const endpoint = video.source === 'pexels' ? '/api/pexels/import' : '/api/pixabay/import';
      
      // Make the API call to import the video
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: video.id,
          source: video.source
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to import video from ${video.source}`);
      }
      
      const data = await response.json();
      
      // Call the onSelect callback with the imported video URL and source
      onSelect(data.url, video.source, video.id);
      
      // Close the dialog
      onOpenChange(false);
      
      // Show success toast
      toast({
        title: "Video added successfully",
        description: `The video from ${video.source} has been added to your course.`,
      });
    } catch (error) {
      console.error("Error importing video:", error);
      
      toast({
        title: "Failed to add video",
        description: "There was an error adding the video. Please try again.",
        variant: "destructive",
      });
    }
  };
  
  // Format video duration from seconds to MM:SS
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Format Pexels data to our StockVideo interface
  const formatPexelsVideos = (): StockVideo[] => {
    if (!pexelsData?.videos) return [];
    
    return pexelsData.videos.map((video: any) => ({
      id: video.id.toString(),
      width: video.width,
      height: video.height,
      url: video.video_files[0]?.link || '',
      thumbnail: video.image,
      preview: video.video_files[0]?.link || '',
      duration: video.duration,
      user: {
        name: video.user?.name || 'Unknown',
        url: video.user?.url || '',
      },
      source: 'pexels',
    }));
  };
  
  // Format Pixabay data to our StockVideo interface
  const formatPixabayVideos = (): StockVideo[] => {
    if (!pixabayData?.hits) return [];
    
    return pixabayData.hits.map((video: any) => ({
      id: video.id.toString(),
      width: video.width,
      height: video.height,
      url: video.videos.large.url,
      thumbnail: video.userImageURL || video.pageURL,
      preview: video.videos.small.url,
      duration: 0, // Pixabay doesn't provide duration
      user: {
        name: video.user || 'Unknown',
        url: video.pageURL || '',
      },
      source: 'pixabay',
    }));
  };
  
  // Get videos based on the active provider
  const getVideos = (): StockVideo[] => {
    if (activeProvider === 'pexels' && !pexelsError) {
      return formatPexelsVideos();
    } else if (activeProvider === 'pixabay' && !pixabayError) {
      return formatPixabayVideos();
    } else if (pexelsError && !pixabayError) {
      // If Pexels fails, try Pixabay
      setActiveProvider('pixabay');
      return formatPixabayVideos();
    } else if (pixabayError && !pexelsError) {
      // If Pixabay fails, try Pexels
      setActiveProvider('pexels');
      return formatPexelsVideos();
    } else {
      // If both fail or no data yet
      return [];
    }
  };

  const isLoading = pexelsLoading || pixabayLoading;
  const hasError = pexelsError && pixabayError; // Only true if both sources failed

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            Stock Media Library
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-4 w-4 ml-2 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">
                    Media is sourced from free public libraries. All content is free to use for commercial and non-commercial purposes.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </DialogTitle>
          <DialogDescription>
            Search and select free stock videos and images for your course
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 flex-1 overflow-hidden flex flex-col">
          <form onSubmit={handleSearch} className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search for stock videos and images..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button type="submit">Search</Button>
          </form>
          
          <div className="overflow-y-auto flex-1" style={{ maxHeight: 'calc(90vh - 240px)' }}>
            {hasError && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Unable to connect to media services. Please try again later.
                </AlertDescription>
              </Alert>
            )}
            
            {pexelsError && !pixabayError && (
              <Alert variant="default" className="mb-4">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Using alternative source for media.
                </AlertDescription>
              </Alert>
            )}
            
            {pixabayError && !pexelsError && (
              <Alert variant="default" className="mb-4">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Using primary source for media.
                </AlertDescription>
              </Alert>
            )}
            
            {isLoading ? (
              <div className="flex justify-center items-center h-40">
                <Loader2 className="h-10 w-10 animate-spin text-muted-foreground" />
              </div>
            ) : getVideos().length === 0 && searchTerm ? (
              <div className="text-center p-4">
                <p>No videos found. Try a different search term.</p>
              </div>
            ) : null}
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {getVideos().map((video) => (
                <Card key={`${video.source}-${video.id}`} className="overflow-hidden">
                  <CardContent className="p-0">
                    <AspectRatio ratio={16 / 9}>
                      <div className="relative w-full h-full">
                        <img 
                          src={video.thumbnail} 
                          alt={`Video preview by ${video.user.name}`}
                          className="object-cover w-full h-full"
                        />
                        
                        <div 
                          className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/30 transition-opacity cursor-pointer"
                          onClick={() => handleVideoSelect(video)}
                        >
                          <Button size="sm" className="bg-primary/90 hover:bg-primary">Select</Button>
                        </div>
                        
                        {/* Duration badge */}
                        <Badge 
                          className="absolute bottom-2 right-2 bg-black/75" 
                          variant="secondary"
                        >
                          {formatDuration(video.duration)}
                        </Badge>
                      </div>
                    </AspectRatio>
                    
                    <div className="flex justify-between items-center p-2 text-xs text-muted-foreground">
                      <span>By: {video.user.name}</span>
                      <a 
                        href={video.user.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center hover:text-foreground"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </a>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}