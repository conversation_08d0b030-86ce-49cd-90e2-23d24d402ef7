import { jsPDF } from 'jspdf';

interface CourseData {
  title: string;
  description: string;
  modules: Array<{
    title: string;
    description: string;
    lessons: Array<{
      title: string;
      description: string;
      script?: string;
    }>;
  }>;
}

export class CoursePDFGenerator {
  private doc: jsPDF;
  private pageHeight: number;
  private pageWidth: number;
  private currentY: number;
  private margin: number;
  private lineHeight: number;

  constructor() {
    this.doc = new jsPDF();
    this.pageHeight = this.doc.internal.pageSize.height;
    this.pageWidth = this.doc.internal.pageSize.width;
    this.currentY = 20;
    this.margin = 20;
    this.lineHeight = 7;
  }

  generateCoursePDF(courseData: CourseData): Buffer {
    this.addCoverPage(courseData);
    this.addTableOfContents(courseData);
    this.addCourseContent(courseData);
    
    return Buffer.from(this.doc.output('arraybuffer'));
  }

  private addCoverPage(courseData: CourseData): void {
    this.doc.setFontSize(28);
    this.doc.setFont('helvetica', 'bold');
    
    // Center the title
    const titleLines = this.doc.splitTextToSize(courseData.title, this.pageWidth - 2 * this.margin);
    const titleHeight = titleLines.length * 10;
    const titleY = (this.pageHeight - titleHeight) / 2 - 20;
    
    this.doc.text(titleLines, this.pageWidth / 2, titleY, { align: 'center' });
    
    // Add description
    this.doc.setFontSize(14);
    this.doc.setFont('helvetica', 'normal');
    const descLines = this.doc.splitTextToSize(courseData.description, this.pageWidth - 2 * this.margin);
    this.doc.text(descLines, this.pageWidth / 2, titleY + titleHeight + 20, { align: 'center' });
    
    // Add generation date
    this.doc.setFontSize(12);
    this.doc.text(`Generated on: ${new Date().toLocaleDateString()}`, this.pageWidth / 2, this.pageHeight - 30, { align: 'center' });
    
    this.doc.addPage();
    this.currentY = 20;
  }

  private addTableOfContents(courseData: CourseData): void {
    this.doc.setFontSize(20);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Table of Contents', this.margin, this.currentY);
    this.currentY += 15;
    
    let pageNumber = 3; // Starting page after cover and TOC
    
    courseData.modules.forEach((module, moduleIndex) => {
      this.checkPageBreak(15);
      
      this.doc.setFontSize(14);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text(`${moduleIndex + 1}. ${module.title}`, this.margin, this.currentY);
      this.doc.text(pageNumber.toString(), this.pageWidth - this.margin, this.currentY, { align: 'right' });
      this.currentY += 10;
      pageNumber++;
      
      module.lessons.forEach((lesson, lessonIndex) => {
        this.checkPageBreak(10);
        
        this.doc.setFontSize(12);
        this.doc.setFont('helvetica', 'normal');
        this.doc.text(`   ${moduleIndex + 1}.${lessonIndex + 1} ${lesson.title}`, this.margin + 10, this.currentY);
        this.doc.text(pageNumber.toString(), this.pageWidth - this.margin, this.currentY, { align: 'right' });
        this.currentY += 7;
        pageNumber++;
      });
      
      this.currentY += 5;
    });
    
    this.doc.addPage();
    this.currentY = 20;
  }

  private addCourseContent(courseData: CourseData): void {
    courseData.modules.forEach((module, moduleIndex) => {
      this.addModuleHeader(module, moduleIndex + 1);
      
      module.lessons.forEach((lesson, lessonIndex) => {
        this.addLessonContent(lesson, moduleIndex + 1, lessonIndex + 1);
      });
    });
  }

  private addModuleHeader(module: any, moduleNumber: number): void {
    this.checkPageBreak(30);
    
    // Module title
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(`Module ${moduleNumber}: ${module.title}`, this.margin, this.currentY);
    this.currentY += 12;
    
    // Module description
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    const descLines = this.doc.splitTextToSize(module.description, this.pageWidth - 2 * this.margin);
    this.doc.text(descLines, this.margin, this.currentY);
    this.currentY += descLines.length * this.lineHeight + 10;
    
    // Add a line separator
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    this.currentY += 10;
  }

  private addLessonContent(lesson: any, moduleNumber: number, lessonNumber: number): void {
    this.checkPageBreak(40);
    
    // Lesson title
    this.doc.setFontSize(16);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text(`${moduleNumber}.${lessonNumber} ${lesson.title}`, this.margin, this.currentY);
    this.currentY += 10;
    
    // Lesson description
    this.doc.setFontSize(11);
    this.doc.setFont('helvetica', 'normal');
    const descLines = this.doc.splitTextToSize(lesson.description, this.pageWidth - 2 * this.margin);
    this.doc.text(descLines, this.margin, this.currentY);
    this.currentY += descLines.length * this.lineHeight + 8;
    
    // Lesson script if available
    if (lesson.script && lesson.script.trim()) {
      this.doc.setFontSize(12);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text('Script:', this.margin, this.currentY);
      this.currentY += 8;
      
      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'normal');
      const scriptLines = this.doc.splitTextToSize(lesson.script, this.pageWidth - 2 * this.margin);
      
      scriptLines.forEach((line: string) => {
        this.checkPageBreak(6);
        this.doc.text(line, this.margin, this.currentY);
        this.currentY += 5;
      });
      
      this.currentY += 10;
    }
    
    // Add spacing between lessons
    this.currentY += 15;
  }

  private checkPageBreak(requiredSpace: number): void {
    if (this.currentY + requiredSpace > this.pageHeight - this.margin) {
      this.doc.addPage();
      this.currentY = 20;
    }
  }

  private addPageNumbers(): void {
    const pageCount = this.doc.getNumberOfPages();
    
    for (let i = 1; i <= pageCount; i++) {
      this.doc.setPage(i);
      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'normal');
      this.doc.text(`Page ${i} of ${pageCount}`, this.pageWidth - this.margin, this.pageHeight - 10, { align: 'right' });
    }
  }
}

export async function generateCoursePDF(courseData: CourseData): Promise<Buffer> {
  const generator = new CoursePDFGenerator();
  const pdfBuffer = generator.generateCoursePDF(courseData);
  return pdfBuffer;
}