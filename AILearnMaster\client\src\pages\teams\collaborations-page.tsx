import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  <PERSON>, 
  CardContent, 
  CardDescription, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON><PERSON><PERSON>, 
  Card<PERSON>itle 
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { Loader2, MoreHorizontal, SearchIcon, Users, BookOpen } from "lucide-react";
import { useState } from "react";
import { useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { Separator } from "@/components/ui/separator";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

// Define types based on our schema
type Course = {
  id: number;
  title: string;
  description: string;
  status: string;
  category: string;
  thumbnailUrl: string | null;
  userId: number;
  completion: number;
  createdAt: Date;
  updatedAt: Date;
  // Add collaboration-specific properties
  collaborationRole?: string;
  collaborationCanEdit?: boolean;
  collaborationAddedAt?: Date;
  owner?: {
    id: number;
    name: string;
    username: string;
    avatarUrl: string | null;
  };
};

export default function CollaborationsPage() {
  const [_, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  // Fetch collaborations where the user is a collaborator
  const { 
    data: collaborations,
    isLoading, 
    error 
  } = useQuery<Course[]>({
    queryKey: ["/api/my-collaborations"],
  });

  // Filter collaborations based on search query and active tab
  const filteredCollaborations = collaborations?.filter((course) => {
    const matchesSearch = searchQuery === "" || 
      course.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      course.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (activeTab === "all") return matchesSearch;
    if (activeTab === "edit") return matchesSearch && course.collaborationCanEdit;
    if (activeTab === "view") return matchesSearch && !course.collaborationCanEdit;
    return matchesSearch;
  });

  // Handle viewing a course
  const viewCourse = (courseId: number) => {
    setLocation(`/courses/${courseId}`);
  };

  return (
    <div className="container max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">My Collaborations</h1>
          <p className="text-slate-600 mt-1">Courses shared with you by other users</p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Tabs and Filters */}
        <div className="flex flex-col space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">All Collaborations</TabsTrigger>
              <TabsTrigger value="edit">Can Edit</TabsTrigger>
              <TabsTrigger value="view">View Only</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <div className="relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
            <Input
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search collaborations..."
              className="pl-10"
            />
          </div>
        </div>

        {/* Collaborations Grid */}
        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="text-center py-10">
            <p className="text-red-500">
              Error loading collaborations. Please try again later.
            </p>
          </div>
        ) : !collaborations || collaborations.length === 0 ? (
          <div className="text-center py-10">
            <Users className="h-12 w-12 mx-auto text-slate-300" />
            <h3 className="mt-4 text-lg font-medium text-slate-900">
              No collaborations found
            </h3>
            <p className="mt-1 text-sm text-slate-500">
              You are not collaborating on any courses yet.
            </p>
          </div>
        ) : !filteredCollaborations || filteredCollaborations.length === 0 ? (
          <div className="text-center py-10">
            <SearchIcon className="h-12 w-12 mx-auto text-slate-300" />
            <h3 className="mt-4 text-lg font-medium text-slate-900">
              No matching collaborations
            </h3>
            <p className="mt-1 text-sm text-slate-500">
              No collaborations match your search criteria.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCollaborations.map((course) => (
              <Card key={course.id} className="overflow-hidden">
                <div className="aspect-video relative bg-slate-100">
                  {course.thumbnailUrl ? (
                    <img
                      src={course.thumbnailUrl}
                      alt={course.title}
                      className="object-cover w-full h-full"
                    />
                  ) : (
                    <div className="flex items-center justify-center w-full h-full text-slate-400">
                      <BookOpen className="h-12 w-12" />
                    </div>
                  )}
                  <div className="absolute top-2 right-2">
                    <Badge variant={course.status === "published" ? "default" : "outline"}>
                      {course.status.charAt(0).toUpperCase() + course.status.slice(1)}
                    </Badge>
                  </div>
                </div>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{course.title}</CardTitle>
                      <CardDescription className="line-clamp-2 mt-1">
                        {course.description}
                      </CardDescription>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => viewCourse(course.id)}>
                          {course.collaborationCanEdit ? "Edit Course" : "View Course"}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="flex items-center text-sm text-slate-500 space-x-2">
                    <span>Owned by: {course.owner?.name || "Unknown"}</span>
                  </div>
                  <div className="mt-2 flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {course.category}
                    </Badge>
                    <Badge 
                      variant={course.collaborationCanEdit ? "secondary" : "outline"} 
                      className="text-xs"
                    >
                      {course.collaborationCanEdit ? "Can Edit" : "View Only"}
                    </Badge>
                  </div>
                </CardContent>
                <Separator />
                <CardFooter className="pt-3 pb-3">
                  <div className="flex justify-between items-center w-full text-sm">
                    <span className="text-slate-500">
                      Added: {new Date(course.collaborationAddedAt || course.createdAt).toLocaleDateString()}
                    </span>
                    <div>
                      <span className="inline-flex items-center text-emerald-600">
                        <span>{course.completion || 0}% Complete</span>
                      </span>
                    </div>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}