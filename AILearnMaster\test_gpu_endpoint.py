#!/usr/bin/env python3
"""
Test the GPU endpoint to verify A100 availability
"""

import requests
import json
import time

def test_gpu_endpoint():
    """Test the GPU health endpoint"""
    url = "https://trade-digital--courseai-a100-gpu-test-health.modal.run"
    
    print("🔍 Testing A100 GPU Availability...")
    print(f"URL: {url}")
    
    try:
        start_time = time.time()
        response = requests.get(url, timeout=120)  # 2 minute timeout for GPU allocation
        end_time = time.time()
        
        print(f"⏱️  Response time: {end_time - start_time:.1f} seconds")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ GPU Test Response:")
            print(json.dumps(data, indent=2))
            
            # Analyze results
            if data.get('gpu_available'):
                print("\n🎉 SUCCESS: A100 GPU is available and working!")
                print(f"   GPU Name: {data.get('gpu_name')}")
                print(f"   GPU Memory: {data.get('gpu_memory_total_gb')} GB")
                print(f"   GPU Test: {data.get('gpu_test')}")
                return True
            else:
                print("\n❌ FAILURE: GPU not available")
                print(f"   Error: {data.get('error', 'Unknown')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out (GPU allocation may be slow)")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_mistral_endpoint():
    """Test the Mistral endpoint with A100 GPU"""
    url = "https://trade-digital--courseai-a100-gpu-test-mistral.modal.run"
    
    print("\n🔍 Testing Mistral LLM with A100 GPU...")
    print(f"URL: {url}")
    
    try:
        payload = {
            "prompt": "Hello! Please respond with 'GPU test successful' if you can see this message."
        }
        
        start_time = time.time()
        response = requests.post(url, json=payload, timeout=180)  # 3 minute timeout for model loading
        end_time = time.time()
        
        print(f"⏱️  Response time: {end_time - start_time:.1f} seconds")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Mistral Test Response:")
            print(json.dumps(data, indent=2))
            
            if data.get('status') == 'success':
                print("\n🎉 SUCCESS: Mistral LLM is working with A100 GPU!")
                print(f"   Generated: {data.get('generated_text', '')}")
                return True
            else:
                print("\n❌ FAILURE: Mistral LLM failed")
                print(f"   Error: {data.get('error', 'Unknown')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out (Model loading may be slow)")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run GPU tests"""
    print("🚀 Modal A100 GPU Testing")
    print("=" * 50)
    
    # Test GPU availability first
    gpu_available = test_gpu_endpoint()
    
    if gpu_available:
        # Test Mistral LLM if GPU is available
        mistral_working = test_mistral_endpoint()
        
        print("\n" + "=" * 50)
        print("📊 FINAL RESULTS")
        print("=" * 50)
        
        if mistral_working:
            print("🎉 ALL TESTS PASSED!")
            print("   ✅ A100 GPU is available")
            print("   ✅ Mistral LLM is working")
            print("\n🚀 Modal A100 deployment is ready for production!")
        else:
            print("⚠️  PARTIAL SUCCESS")
            print("   ✅ A100 GPU is available")
            print("   ❌ Mistral LLM failed")
    else:
        print("\n" + "=" * 50)
        print("📊 FINAL RESULTS")
        print("=" * 50)
        print("❌ TESTS FAILED")
        print("   ❌ A100 GPU is not available")
        print("\n🔧 Please check Modal GPU allocation and deployment.")

if __name__ == "__main__":
    main()
