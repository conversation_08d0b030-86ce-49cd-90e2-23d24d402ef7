import { OpenAI } from 'openai';

let openai: OpenAI | null = null;
if (process.env.OPENAI_API_KEY) {
  openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
}

export async function generateSpeech(
  text: string,
  voice: string = 'alloy',
  settings: any = {}
): Promise<Buffer> {
  try {
    if (!openai) {
      throw new Error('OpenAI client not initialized - API key missing');
    }

    const response = await openai.audio.speech.create({
      model: 'tts-1',
      voice: voice as any,
      input: text,
      speed: settings.speed || 1.0,
    });

    const buffer = Buffer.from(await response.arrayBuffer());
    return buffer;
  } catch (error) {
    console.error('OpenAI TTS error:', error);
    throw error;
  }
}