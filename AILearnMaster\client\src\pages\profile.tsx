import { useState, useRef, useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { UserStats, BillingHistory } from "@/types";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Loader2 } from "lucide-react";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import SubscriptionSettings from "@/components/settings/SubscriptionSettings";

export default function ProfilePage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [selectedTab, setSelectedTab] = useState("account");
  const [isEditing, setIsEditing] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isAvatarUploading, setIsAvatarUploading] = useState(false);
  
  const [profileData, setProfileData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    password: "",
    confirmPassword: "",
  });
  
  // Update profile data when user data changes
  useEffect(() => {
    if (user) {
      setProfileData(prev => ({
        ...prev,
        name: user.name || "",
        email: user.email || "",
      }));
    }
  }, [user]);
  
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [show2FASetup, setShow2FASetup] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [preferences, setPreferences] = useState({
    theme: "light" as 'light' | 'dark' | 'system',
    language: "english",
    notifications: {
      email: true,
      push: true,
      marketing: false,
    },
  });

  // Fetch user statistics
  const { 
    data: userStats, 
    isLoading: isLoadingStats 
  } = useQuery<UserStats>({
    queryKey: ['/api/user-stats'],
    enabled: !!user,
  });

  // Billing history query
  const { data: billingHistory = [] } = useQuery<BillingHistory[]>({
    queryKey: ['/api/billing/history'],
    enabled: !!user,
    placeholderData: [],
  });

  // Profile update mutation
  const updateProfileMutation = useMutation({
    mutationFn: async (data: { name?: string; email?: string; avatarUrl?: string | null }) => {
      const res = await apiRequest('PATCH', '/api/users/profile', data);
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || "Failed to update profile");
      }
      return await res.json();
    },
    onSuccess: (updatedUser) => {
      queryClient.setQueryData(['/api/auth/me'], updatedUser);
      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully.",
      });
      setIsEditing(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Update failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Password change mutation
  const changePasswordMutation = useMutation({
    mutationFn: async (data: { password: string }) => {
      const res = await apiRequest('POST', '/api/users/change-password', data);
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || "Failed to change password");
      }
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Password updated",
        description: "Your password has been changed successfully.",
      });
      setProfileData((prev) => ({ ...prev, password: "", confirmPassword: "" }));
    },
    onError: (error: Error) => {
      toast({
        title: "Password change failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Avatar upload mutation
  const uploadAvatarMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      // Use fetch directly for FormData
      const res = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
      });
      if (!res.ok) {
        const error = await res.json();
        throw new Error(error.message || "Failed to upload avatar");
      }
      return await res.json();
    },
    onSuccess: (media) => {
      // Update the user profile with the new avatar URL
      updateProfileMutation.mutate({
        avatarUrl: media.url,
      });
      setIsAvatarUploading(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive",
      });
      setIsAvatarUploading(false);
    },
  });

  // Handle form changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle notification preference changes
  const handleNotificationChange = (key: keyof typeof preferences.notifications) => {
    setPreferences((prev) => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [key]: !prev.notifications[key],
      },
    }));
  };

  // Handle theme change
  const handleThemeChange = (theme: "light" | "dark" | "system") => {
    setPreferences((prev) => ({ ...prev, theme }));
  };

  // Handle language change
  const handleLanguageChange = (language: string) => {
    setPreferences((prev) => ({ ...prev, language }));
  };

  // Handle profile save
  const handleSaveProfile = () => {
    updateProfileMutation.mutate({
      name: profileData.name,
      email: profileData.email,
    });
  };

  // Handle avatar upload
  const handleAvatarUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle file selection for avatar
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type and size
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file.",
        variant: "destructive",
      });
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast({
        title: "File too large",
        description: "Please upload an image smaller than 5MB.",
        variant: "destructive",
      });
      return;
    }

    // Create form data for upload
    const formData = new FormData();
    formData.append('file', file);

    setIsAvatarUploading(true);
    uploadAvatarMutation.mutate(formData);
  };

  // Handle password change
  const handlePasswordChange = () => {
    if (profileData.password !== profileData.confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match.",
        variant: "destructive",
      });
      return;
    }
    
    if (profileData.password.length < 6) {
      toast({
        title: "Error",
        description: "Password must be at least 6 characters long.",
        variant: "destructive",
      });
      return;
    }
    
    changePasswordMutation.mutate({ password: profileData.password });
  };

  // Handle subscribe/upgrade
  const handleSubscribe = () => {
    window.location.href = "/pricing";
  };

  return (
    <div className="container py-6 md:py-10">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">My Profile</h1>
        <p className="text-muted-foreground">Manage your account settings and preferences</p>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="security">Security & Privacy</TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Profile Information */}
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Profile Information</CardTitle>
                    <CardDescription>Update your personal information</CardDescription>
                  </div>
                  <Button 
                    variant={isEditing ? "default" : "outline"} 
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    {isEditing ? "Cancel" : "Edit"}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {isEditing ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input 
                        id="name" 
                        name="name" 
                        value={profileData.name} 
                        onChange={handleChange} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input 
                        id="email" 
                        name="email" 
                        type="email" 
                        value={profileData.email} 
                        onChange={handleChange} 
                      />
                    </div>
                    <Button onClick={handleSaveProfile}>Save Changes</Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 gap-x-6 gap-y-4 sm:grid-cols-2">
                      <div>
                        <Label className="text-sm text-muted-foreground">Full Name</Label>
                        <div className="font-medium mt-1">{user?.name || 'Not set'}</div>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Email Address</Label>
                        <div className="font-medium mt-1">{user?.email}</div>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Username</Label>
                        <div className="font-medium mt-1">{user?.username}</div>
                      </div>
                      <div>
                        <Label className="text-sm text-muted-foreground">Member Since</Label>
                        <div className="font-medium mt-1">
                          {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Profile Photo */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Photo</CardTitle>
                <CardDescription>Update your profile picture</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center space-y-4">
                <Avatar className="h-24 w-24">
                  <AvatarImage src={user?.avatarUrl || ''} />
                  <AvatarFallback className="text-lg">
                    {user?.name 
                      ? user.name.split(" ").map((n: string) => n[0]).join("") 
                      : user?.username.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {isAvatarUploading ? (
                  <div className="flex items-center justify-center">
                    <Loader2 className="h-5 w-5 animate-spin mr-2" />
                    <span>Uploading...</span>
                  </div>
                ) : (
                  <>
                    <Button onClick={handleAvatarUpload} variant="outline">Upload New Photo</Button>
                    <input 
                      type="file" 
                      ref={fileInputRef} 
                      className="hidden" 
                      accept="image/*" 
                      onChange={handleFileChange}
                    />
                  </>
                )}
              </CardContent>
            </Card>

            {/* Password Change */}
            <Card className="md:col-span-3">
              <CardHeader>
                <CardTitle>Password</CardTitle>
                <CardDescription>Change your password</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-w-md">
                  <div className="space-y-2">
                    <Label htmlFor="new-password">New Password</Label>
                    <Input 
                      id="new-password" 
                      name="password" 
                      type="password" 
                      value={profileData.password} 
                      onChange={handleChange} 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                    <Input 
                      id="confirm-password" 
                      name="confirmPassword" 
                      type="password" 
                      value={profileData.confirmPassword} 
                      onChange={handleChange} 
                    />
                  </div>
                  <Button onClick={handlePasswordChange}>Change Password</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="subscription">
          {/* Import SubscriptionSettings from @/components/settings/SubscriptionSettings */}
          {user && (
            <SubscriptionSettings user={user} />
          )}
        </TabsContent>

        <TabsContent value="preferences">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Theme Preferences */}
            <Card>
              <CardHeader>
                <CardTitle>Appearance</CardTitle>
                <CardDescription>Customize your theme preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium mb-4">Theme</h3>
                  <div className="grid grid-cols-3 gap-4">
                    <Button 
                      variant={preferences.theme === 'light' ? "default" : "outline"}
                      className="justify-start"
                      onClick={() => handleThemeChange('light')}
                    >
                      <i className="ri-sun-line mr-2" />
                      Light
                    </Button>
                    <Button 
                      variant={preferences.theme === 'dark' ? "default" : "outline"}
                      className="justify-start"
                      onClick={() => handleThemeChange('dark')}
                    >
                      <i className="ri-moon-line mr-2" />
                      Dark
                    </Button>
                    <Button 
                      variant={preferences.theme === 'system' ? "default" : "outline"}
                      className="justify-start"
                      onClick={() => handleThemeChange('system')}
                    >
                      <i className="ri-computer-line mr-2" />
                      System
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Language Preferences */}
            <Card>
              <CardHeader>
                <CardTitle>Language</CardTitle>
                <CardDescription>Choose your preferred language</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="language">Select Language</Label>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between">
                        <span>{preferences.language === "english" ? "English" : preferences.language}</span>
                        <i className="ri-arrow-down-s-line" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-44">
                      <DropdownMenuItem onClick={() => handleLanguageChange("english")}>
                        English
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleLanguageChange("spanish")}>
                        Spanish
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleLanguageChange("french")}>
                        French
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleLanguageChange("german")}>
                        German
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleLanguageChange("japanese")}>
                        Japanese
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardContent>
            </Card>

            {/* Notification Preferences */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
                <CardDescription>Manage your notification preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <h4 className="font-medium">Email Notifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Receive notifications about your courses and account updates via email
                      </p>
                    </div>
                    <Switch 
                      checked={preferences.notifications.email}
                      onCheckedChange={() => handleNotificationChange("email")}
                    />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <h4 className="font-medium">Push Notifications</h4>
                      <p className="text-sm text-muted-foreground">
                        Receive real-time notifications in your browser
                      </p>
                    </div>
                    <Switch 
                      checked={preferences.notifications.push}
                      onCheckedChange={() => handleNotificationChange("push")}
                    />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <h4 className="font-medium">Marketing Communications</h4>
                      <p className="text-sm text-muted-foreground">
                        Receive updates about new features, promotions, and educational content
                      </p>
                    </div>
                    <Switch 
                      checked={preferences.notifications.marketing}
                      onCheckedChange={() => handleNotificationChange("marketing")}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="security">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Two-Factor Authentication */}
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Two-Factor Authentication</CardTitle>
                    <CardDescription>Add an extra layer of security to your account</CardDescription>
                  </div>
                  <Switch 
                    checked={twoFactorEnabled}
                    onCheckedChange={(checked) => {
                      setTwoFactorEnabled(checked);
                      setShow2FASetup(checked && !show2FASetup);
                      if (!checked) {
                        toast({
                          title: "2FA Disabled",
                          description: "Two-factor authentication has been disabled for your account.",
                        });
                      }
                    }}
                  />
                </div>
              </CardHeader>
              <CardContent>
                {show2FASetup ? (
                  <div className="space-y-4">
                    <div className="p-4 bg-amber-50 border border-amber-200 rounded-md text-amber-800">
                      <h3 className="text-sm font-medium mb-2">Set up two-factor authentication</h3>
                      <p className="text-sm">Scan the QR code with your authenticator app, then enter the verification code below.</p>
                    </div>
                    
                    <div className="flex justify-center p-4 bg-slate-50 border border-slate-200 rounded-md">
                      <div className="bg-white p-2 rounded">
                        {/* Placeholder for QR code */}
                        <div className="w-48 h-48 bg-slate-100 flex items-center justify-center">
                          <p className="text-sm text-center text-slate-500">QR Code Placeholder</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="verification-code">Verification Code</Label>
                      <Input id="verification-code" placeholder="Enter 6-digit code" />
                    </div>
                    
                    <Button onClick={() => {
                      toast({
                        title: "2FA Enabled",
                        description: "Two-factor authentication has been enabled for your account.",
                      });
                      setShow2FASetup(false);
                    }}>
                      Verify and Enable
                    </Button>
                  </div>
                ) : (
                  <div>
                    <p className="text-sm text-muted-foreground mb-4">
                      {twoFactorEnabled 
                        ? "Two-factor authentication is enabled. You'll need to enter a verification code from your authenticator app when signing in."
                        : "Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy."}
                    </p>
                    
                    {twoFactorEnabled && (
                      <div className="mt-4">
                        <Button 
                          variant="outline" 
                          onClick={() => setShow2FASetup(true)}
                        >
                          Reconfigure 2FA
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Login Activity */}
            <Card className="md:col-span-3">
              <CardHeader>
                <CardTitle>Recent Login Activity</CardTitle>
                <CardDescription>Monitor where your account is being accessed from</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-md">
                  <div className="grid grid-cols-4 gap-4 p-4 font-medium text-sm border-b">
                    <div>Date & Time</div>
                    <div>IP Address</div>
                    <div>Location</div>
                    <div>Device</div>
                  </div>
                  
                  {/* Mock login activity data */}
                  <div className="grid grid-cols-4 gap-4 p-4 text-sm border-b">
                    <div>{new Date().toLocaleString()}</div>
                    <div>***********</div>
                    <div>New York, USA</div>
                    <div>Chrome on Windows</div>
                  </div>
                  <div className="grid grid-cols-4 gap-4 p-4 text-sm border-b">
                    <div>{new Date(Date.now() - ********).toLocaleString()}</div>
                    <div>***********</div>
                    <div>New York, USA</div>
                    <div>Chrome on Windows</div>
                  </div>
                </div>
                
                <div className="mt-6 space-y-4">
                  <Button variant="outline" onClick={() => {
                    toast({
                      title: "Logged out from all devices",
                      description: "You have been logged out from all devices except this one.",
                    });
                  }}>
                    <i className="ri-logout-box-line mr-2"></i>
                    Logout from All Devices
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            {/* Account Actions */}
            <Card className="md:col-span-3 border-red-200">
              <CardHeader className="border-b border-red-100">
                <CardTitle className="text-red-600">Danger Zone</CardTitle>
                <CardDescription>Irreversible actions for your account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Delete Account</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Permanently delete your account and all your data. This action cannot be undone.
                    </p>
                  </div>
                  <Button 
                    variant="destructive"
                    onClick={() => setShowDeleteConfirm(true)}
                  >
                    Delete Account
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Delete Account Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background p-6 rounded-lg shadow-lg max-w-md w-full">
            <h2 className="text-xl font-bold text-destructive mb-2">Delete Account</h2>
            <p className="text-muted-foreground mb-6">
              Are you sure you want to delete your account? All of your data will be permanently removed. This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-4">
              <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={() => {
                toast({
                  title: "Account deleted",
                  description: "Your account has been permanently deleted.",
                });
                setShowDeleteConfirm(false);
              }}>
                Yes, Delete My Account
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}