import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Upload, FileText, AlertCircle, CheckCircle, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface FileUploaderProps {
  onFileSelected: (file: File) => void;
  acceptedFileTypes?: string;
  maxSizeMB?: number;
}

export function FileUploader({
  onFileSelected,
  acceptedFileTypes = "*",
  maxSizeMB = 100
}: FileUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const maxSizeBytes = maxSizeMB * 1024 * 1024;

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length) {
      handleFile(files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFile(files[0]);
    }
  };

  const handleFile = (file: File) => {
    // Reset state
    setError(null);
    
    // Check file type
    const acceptedTypes = acceptedFileTypes.split(',');
    if (acceptedFileTypes !== "*" && !acceptedTypes.some(type => {
      if (type.includes('*')) {
        const mainType = type.split('/')[0];
        return file.type.startsWith(mainType);
      }
      return file.type === type || type.includes(file.name.split('.').pop() || '');
    })) {
      setError(`Invalid file type. Accepted types: ${acceptedFileTypes}`);
      return;
    }
    
    // Check file size
    if (file.size > maxSizeBytes) {
      setError(`File size exceeds the maximum limit of ${maxSizeMB}MB`);
      return;
    }
    
    // Simulate upload
    setIsUploading(true);
    let progress = 0;
    const interval = setInterval(() => {
      progress += 5;
      setUploadProgress(progress);
      
      if (progress >= 100) {
        clearInterval(interval);
        setIsUploading(false);
        onFileSelected(file);
        
        toast({
          title: "Upload successful",
          description: `${file.name} has been uploaded`,
        });
      }
    }, 100);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const cancelUpload = () => {
    setIsUploading(false);
    setUploadProgress(0);
  };

  return (
    <div className="w-full">
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileInput}
        accept={acceptedFileTypes}
      />
      
      {!isUploading ? (
        <div
          className={`border-2 border-dashed rounded-lg p-8 transition-colors ${
            isDragging ? "border-primary bg-primary/5" : "border-muted-foreground/25"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center justify-center text-center">
            <Upload className="h-12 w-12 text-muted-foreground mb-4" />
            
            <div className="mb-4">
              <h3 className="text-lg font-medium">Drag and drop your file here</h3>
              <p className="text-sm text-muted-foreground mb-2">
                or click to browse
              </p>
              <p className="text-xs text-muted-foreground">
                {acceptedFileTypes !== "*" 
                  ? `Accepted file types: ${acceptedFileTypes}`
                  : "All file types accepted"} 
                (Max: {maxSizeMB}MB)
              </p>
            </div>
            
            <Button onClick={openFileDialog} className="gap-2">
              <FileText className="h-4 w-4" />
              Browse Files
            </Button>
            
            {error && (
              <div className="mt-4 flex items-center text-destructive text-sm">
                <AlertCircle className="h-4 w-4 mr-2" />
                {error}
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="border rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <FileText className="h-5 w-5 text-muted-foreground mr-2" />
              <span className="text-sm font-medium">Uploading...</span>
            </div>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={cancelUpload}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <Progress value={uploadProgress} className="h-2 mb-2" />
          
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{uploadProgress}%</span>
            {uploadProgress === 100 && (
              <span className="flex items-center text-green-500">
                <CheckCircle className="h-3 w-3 mr-1" />
                Complete
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}