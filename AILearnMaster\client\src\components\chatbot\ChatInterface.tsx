import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, <PERSON>Footer, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Loader2, Send, Brain, RotateCw } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import { ChatSession, ChatMessage } from "@/types/chat";

interface ChatInterfaceProps {
  sessionId: number;
  courseId?: number;
  lessonId?: number;
}

const ChatInterface = ({ sessionId, courseId, lessonId }: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [sending, setSending] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();
  const { user } = useAuth();
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  // Fetch messages for the session
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const response = await apiRequest('GET', `/api/chatbot/sessions/${sessionId}/messages`);
        const data = await response.json();
        setMessages(data);
        setInitialLoading(false);
        
        // Scroll to bottom after messages load
        setTimeout(scrollToBottom, 100);
      } catch (error) {
        console.error('Error fetching messages:', error);
        toast({
          title: 'Error',
          description: 'Could not load chat messages',
          variant: 'destructive',
        });
        setInitialLoading(false);
      }
    };
    
    fetchMessages();
  }, [sessionId, toast]);
  
  // Auto-scroll when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim() || sending) return;
    
    try {
      setSending(true);
      
      const response = await apiRequest('POST', `/api/chatbot/sessions/${sessionId}/messages`, {
        content: input.trim()
      });
      
      if (!response.ok) {
        // Check if it's an AI credits issue
        if (response.status === 402) {
          const error = await response.json();
          toast({
            title: 'Not enough AI credits',
            description: `You need ${error.creditsRequired} credits but only have ${error.creditsAvailable}. Please upgrade your plan or purchase more credits.`,
            variant: 'destructive',
          });
          setSending(false);
          return;
        }
        
        throw new Error('Failed to send message');
      }
      
      const data = await response.json();
      setMessages(prev => [...prev, data.userMessage, data.assistantMessage]);
      setInput('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Could not send message. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSending(false);
    }
  };
  
  const formatMessage = (content: string) => {
    // Simple formatting for code blocks
    return content.split('```').map((segment, index) => {
      if (index % 2 === 0) {
        // Text content - replace newlines with <br>
        return <span key={index} style={{ whiteSpace: 'pre-wrap' }}>{segment}</span>;
      } else {
        // Code block
        return (
          <pre key={index} className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md my-2 overflow-x-auto">
            <code>{segment}</code>
          </pre>
        );
      }
    });
  };
  
  if (initialLoading) {
    return (
      <Card className="h-[80vh] flex flex-col justify-center items-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-2 text-sm text-muted-foreground">Loading conversation...</p>
      </Card>
    );
  }
  
  return (
    <Card className="flex flex-col h-[80vh]">
      <CardHeader className="px-4 py-3 border-b">
        <CardTitle className="text-lg flex items-center">
          <Brain className="w-5 h-5 mr-2 text-primary" />
          <span>AI Learning Assistant</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="h-full flex flex-col items-center justify-center text-center">
            <Brain className="h-12 w-12 text-muted-foreground mb-3" />
            <h3 className="text-lg font-medium mb-1">Your AI Learning Assistant</h3>
            <p className="text-muted-foreground max-w-md">
              Ask questions about course content, request summaries, or get help understanding difficult concepts.
            </p>
          </div>
        ) : (
          messages.map((message) => (
            <div 
              key={message.id} 
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`flex gap-2 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                <Avatar className="h-8 w-8">
                  {message.role === 'user' ? (
                    <AvatarImage src={user?.avatarUrl || ''} alt="User" />
                  ) : (
                    <AvatarImage src="/ai-assistant.png" alt="AI Assistant" />
                  )}
                  <AvatarFallback>
                    {message.role === 'user' ? user?.name?.charAt(0) || 'U' : 'AI'}
                  </AvatarFallback>
                </Avatar>
                <div className={`rounded-lg px-4 py-3 ${
                  message.role === 'user' 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted'
                }`}>
                  {formatMessage(message.content)}
                </div>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </CardContent>
      <CardFooter className="p-3 border-t">
        <form onSubmit={sendMessage} className="flex w-full gap-2">
          <Input
            placeholder="Type your message..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            disabled={sending}
            className="flex-1"
          />
          <Button type="submit" disabled={sending || !input.trim()}>
            {sending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
          </Button>
        </form>
      </CardFooter>
    </Card>
  );
};

export default ChatInterface;