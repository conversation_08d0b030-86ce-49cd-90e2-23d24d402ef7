import React, { createContext, useState, useContext, useCallback, ReactNode } from 'react';
import { useLocalStorage } from '@/hooks/use-local-storage';
import { ContextualHint } from './ContextualHint';
import { hintMap } from './contextual-hints';

interface HintContextType {
  currentHint: React.ReactNode | string | null;
  showHint: (hint: React.ReactNode | string) => void;
  hideHint: () => void;
  hintsEnabled: boolean;
  setHintsEnabled: (enabled: boolean) => void;
}

const HintContext = createContext<HintContextType | undefined>(undefined);

interface HintProviderProps {
  children: ReactNode;
}

export const HintProvider: React.FC<HintProviderProps> = ({ children }) => {
  const [currentHint, setCurrentHint] = useState<React.ReactNode | string | null>(null);
  const [hintsEnabled, setHintsEnabled] = useLocalStorage('hintsEnabled', true);

  const showHint = useCallback((hint: React.ReactNode | string) => {
    if (!hintsEnabled) return;
    
    if (typeof hint === 'string' && hint in hintMap) {
      setCurrentHint(hintMap[hint]);
    } else {
      setCurrentHint(hint);
    }
  }, [hintsEnabled]);

  const hideHint = useCallback(() => {
    setCurrentHint(null);
  }, []);

  return (
    <HintContext.Provider 
      value={{ 
        currentHint, 
        showHint, 
        hideHint, 
        hintsEnabled, 
        setHintsEnabled 
      }}
    >
      {children}
      {currentHint && <ContextualHint content={currentHint} onClose={hideHint} />}
    </HintContext.Provider>
  );
};

export const useHints = (): HintContextType => {
  const context = useContext(HintContext);
  if (context === undefined) {
    throw new Error('useHints must be used within a HintProvider');
  }
  return context;
};