import { 
  S3<PERSON>lient, 
  PutO<PERSON>Command, 
  GetO<PERSON>Command, 
  DeleteObjectCommand,
  ListObjectsV2Command
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Readable } from 'stream';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

// Interface for upload options
interface UploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  acl?: 'private' | 'public-read';
  folder?: string;
}

// Interface for uploaded file result
interface UploadedFile {
  key: string;
  url: string;
  size: number;
  contentType: string;
}

// Class to handle AWS S3 operations
class S3Service {
  private s3Client: S3Client | null = null;
  private bucket: string | null = null;
  private region: string | null = null;
  private cloudfrontDomain: string | null = null;

  constructor() {
    this.initialize();
  }

  // Initialize the S3 client
  private initialize() {
    try {
      // Using environment variables directly
      const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
      const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;
      this.region = process.env.AWS_REGION || 'us-east-1';
      this.bucket = process.env.AWS_S3_BUCKET;

      // Log credential status (not the actual values for security)
      console.log('AWS S3 Credentials Status:');
      console.log(`Access Key ID: ${accessKeyId ? 'Configured' : 'Missing'}`);
      console.log(`Secret Access Key: ${secretAccessKey ? 'Configured' : 'Missing'}`);
      console.log(`S3 Bucket: ${this.bucket || 'Not configured'}`);
      console.log(`Region: ${this.region}`);
      this.cloudfrontDomain = process.env.AWS_CLOUDFRONT_DOMAIN || null;

      // Check if required credentials are available
      if (!accessKeyId || !secretAccessKey) {
        console.warn('AWS S3 credentials not configured. S3 service will operate in fallback mode.');
        return;
      }
      
      // Create the S3 client with the credentials
      this.s3Client = new S3Client({
        region: this.region,
        credentials: {
          accessKeyId,
          secretAccessKey
        }
      });
      
      // Verify the credentials work by making a simple API call
      if (this.bucket) {
        console.log(`AWS S3 client initialized with bucket: ${this.bucket}`);
        console.log('AWS S3 service initialized successfully');
      } else {
        console.warn('AWS S3 bucket not specified. S3 service initialized but may not function properly.');
      }
    } catch (error) {
      console.error('Error initializing AWS S3 service:', error);
      this.s3Client = null;
    }
  }

  // Check if the service is properly configured
  public isConfigured(): boolean {
    return this.s3Client !== null && this.bucket !== null;
  }

  // Upload buffer to S3
  public async uploadBuffer(
    buffer: Buffer, 
    filename: string, 
    options: UploadOptions = {}
  ): Promise<UploadedFile> {
    if (!this.isConfigured()) {
      console.warn('AWS S3 service is not configured, using fallback URL');
      // Return a fallback result that indicates S3 upload didn't happen
      return {
        key: `local-${uuidv4()}`,
        url: `/assets/placeholder-image.png`, // Fallback local URL
        size: buffer.length,
        contentType: options.contentType || 'application/octet-stream'
      };
    }

    try {
      // Generate a unique key if in a folder
      const folder = options.folder ? `${options.folder}/` : '';
      const uniqueId = uuidv4().substring(0, 8);
      const fileExtension = path.extname(filename);
      const fileNameWithoutExt = path.basename(filename, fileExtension);
      const sanitizedFilename = fileNameWithoutExt
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-');
      
      const key = `${folder}${sanitizedFilename}-${uniqueId}${fileExtension}`;
      
      // Set up S3 parameters
      const uploadParams = {
        Bucket: this.bucket!,
        Key: key,
        Body: buffer,
        ContentType: options.contentType || 'application/octet-stream',
        Metadata: options.metadata || {},
        ACL: options.acl || 'private'
      };

      // Upload the file
      await this.s3Client!.send(new PutObjectCommand(uploadParams));
      
      // Generate the URL
      const url = this.getFileUrl(key);
      
      return {
        key,
        url,
        size: buffer.length,
        contentType: options.contentType || 'application/octet-stream'
      };
    } catch (error) {
      console.error('Error uploading file to S3:', error);
      throw error;
    }
  }

  // Get a file from S3
  public async getFile(key: string): Promise<Buffer> {
    if (!this.isConfigured()) {
      console.warn('AWS S3 service is not configured, returning empty buffer');
      return Buffer.from('');
    }

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket!,
        Key: key
      });

      const response = await this.s3Client!.send(command);
      
      // Convert the stream to a buffer
      if (response.Body instanceof Readable) {
        return new Promise<Buffer>((resolve, reject) => {
          const chunks: Buffer[] = [];
          response.Body!.on('data', (chunk) => chunks.push(chunk));
          response.Body!.on('end', () => resolve(Buffer.concat(chunks)));
          response.Body!.on('error', reject);
        });
      } else {
        throw new Error('Response body is not a readable stream');
      }
    } catch (error) {
      console.error('Error getting file from S3:', error);
      throw error;
    }
  }

  // Delete a file from S3
  public async deleteFile(key: string): Promise<boolean> {
    if (!this.isConfigured()) {
      console.warn('AWS S3 service is not configured, cannot delete file');
      return true; // Return success anyway to not block the application
    }

    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucket!,
        Key: key
      });

      await this.s3Client!.send(command);
      return true;
    } catch (error) {
      console.error('Error deleting file from S3:', error);
      throw error;
    }
  }

  // List files in a folder
  public async listFiles(prefix: string): Promise<string[]> {
    if (!this.isConfigured()) {
      console.warn('AWS S3 service is not configured, returning empty list');
      return [];
    }

    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucket!,
        Prefix: prefix
      });

      const response = await this.s3Client!.send(command);
      
      if (!response.Contents) {
        return [];
      }
      
      return response.Contents.map(item => item.Key!);
    } catch (error) {
      console.error('Error listing files from S3:', error);
      throw error;
    }
  }

  // Get a signed URL for temporary access to a file
  public async getSignedUrl(key: string, expiresIn = 3600): Promise<string> {
    if (!this.isConfigured()) {
      console.warn('AWS S3 service is not configured, returning fallback URL');
      return `/assets/placeholder-image.png`;
    }

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket!,
        Key: key
      });

      return await getSignedUrl(this.s3Client!, command, { expiresIn });
    } catch (error) {
      console.error('Error generating signed URL:', error);
      throw error;
    }
  }

  // Get a public URL for a file
  private getFileUrl(key: string): string {
    // If CloudFront domain is set, use it
    if (this.cloudfrontDomain) {
      return `https://${this.cloudfrontDomain}/${key}`;
    }
    
    // Otherwise, use the S3 URL
    return `https://${this.bucket}.s3.${this.region}.amazonaws.com/${key}`;
  }
}

// Create and export a singleton instance
const s3Service = new S3Service();
export default s3Service;
export { s3Service as awsS3Service };