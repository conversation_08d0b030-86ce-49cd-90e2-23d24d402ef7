"""
Updated Modal A100 GPU Production Deployment
Compatible with Modal 1.0+ API and latest dependencies
"""

import modal
import os
import base64
import io
import tempfile
import subprocess
import json
import time
from typing import Dict, List, Any, Optional
from pathlib import Path

# Modal App Configuration
app = modal.App("courseai-a100-updated")

# Updated A100 GPU Image with latest compatible dependencies
gpu_image = (
    modal.Image.debian_slim(python_version="3.10")
    .apt_install([
        "git", "wget", "curl", "ffmpeg", "libsm6", "libxext6", 
        "libxrender-dev", "libglib2.0-0", "libgl1-mesa-glx",
        "libsndfile1", "espeak", "espeak-data", "libespeak1",
        "libespeak-dev", "festival", "festvox-kallpc16k",
        "nodejs", "npm", "cmake", "build-essential"
    ])
    .pip_install([
        "torch==2.1.0",
        "torchvision==0.16.0", 
        "torchaudio==2.1.0",
        "transformers==4.36.0",
        "accelerate==0.25.0",
        "diffusers==0.24.0",
        "opencv-python==********",
        "pillow==10.1.0",
        "numpy==1.24.4",
        "scipy==1.11.4",
        "scikit-image==0.22.0",
        "imageio==2.33.0",
        "imageio-ffmpeg==0.4.9",
        "librosa==0.10.1",
        "soundfile==0.12.1",
        "pydub==0.25.1",
        "TTS==0.22.0",  # Latest Coqui TTS
        "fastapi==0.104.1",
        "uvicorn==0.24.0",
        "requests==2.31.0",
        "matplotlib==3.8.0",
        "seaborn==0.13.0"
    ])
    .run_commands([
        "pip install --upgrade pip",
        "npm install -g @marp-team/marp-cli"
    ])
)

# Health check endpoint
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=32768,
    timeout=300,
    min_containers=0,
    max_containers=10
)
@modal.fastapi_endpoint(method="GET")
def health():
    """GPU health check and system information"""
    try:
        import torch
        import subprocess
        
        gpu_info = {
            "gpu_available": torch.cuda.is_available(),
            "gpu_count": torch.cuda.device_count(),
            "gpu_name": torch.cuda.get_device_name(0) if torch.cuda.is_available() else "No GPU",
            "cuda_version": torch.version.cuda if torch.cuda.is_available() else "No CUDA",
            "pytorch_version": torch.__version__,
            "memory_allocated": torch.cuda.memory_allocated(0) if torch.cuda.is_available() else 0,
            "memory_reserved": torch.cuda.memory_reserved(0) if torch.cuda.is_available() else 0
        }
        
        # System info
        try:
            nvidia_smi = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total,memory.used,temperature.gpu', '--format=csv,noheader,nounits'], 
                                      capture_output=True, text=True, timeout=10)
            gpu_info["nvidia_smi"] = nvidia_smi.stdout.strip() if nvidia_smi.returncode == 0 else "nvidia-smi failed"
        except:
            gpu_info["nvidia_smi"] = "nvidia-smi not available"
            
        return {
            "status": "healthy",
            "timestamp": time.time(),
            "gpu_info": gpu_info,
            "services": ["tts", "mistral", "slides", "sadtalker"]
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

# Mistral LLM Service
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=32768,
    timeout=600,
    min_containers=0,
    max_containers=5
)
def mistral_generate(prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> Dict[str, Any]:
    """Generate text using Mistral 7B model"""
    try:
        from transformers import AutoTokenizer, AutoModelForCausalLM
        import torch
        
        model_name = "mistralai/Mistral-7B-Instruct-v0.1"
        
        # Load model and tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        # Prepare input
        inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
        
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids,
                max_new_tokens=max_tokens,
                temperature=temperature,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        # Decode response
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated_text = response[len(prompt):].strip()
        
        return {
            "success": True,
            "generated_text": generated_text,
            "model": model_name,
            "tokens_generated": len(tokenizer.encode(generated_text))
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "model": "mistral-7b"
        }

# Enhanced Coqui TTS Service
@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    memory=16384,
    timeout=300,
    min_containers=0,
    max_containers=10
)
def coqui_tts_generate(text: str, voice_id: str = "tts_models/en/ljspeech/tacotron2-DDC") -> Dict[str, Any]:
    """Generate speech using Coqui TTS with dynamic voice discovery"""
    try:
        from TTS.api import TTS
        import tempfile
        import base64
        
        # Initialize TTS with specified model
        tts = TTS(voice_id)
        
        # Generate audio to temporary file
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
            tts.tts_to_file(text=text, file_path=tmp_file.name)
            
            # Read and encode audio
            with open(tmp_file.name, "rb") as audio_file:
                audio_data = audio_file.read()
                audio_base64 = base64.b64encode(audio_data).decode()
            
            # Cleanup
            os.unlink(tmp_file.name)
        
        return {
            "success": True,
            "audio_base64": audio_base64,
            "voice_id": voice_id,
            "text_length": len(text),
            "format": "wav"
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "voice_id": voice_id
        }

# Coqui Voice Discovery Service
@app.function(
    image=gpu_image,
    memory=8192,
    timeout=120,
    min_containers=0,
    max_containers=3
)
def discover_coqui_voices() -> Dict[str, Any]:
    """Discover all available Coqui TTS models"""
    try:
        from TTS.api import TTS
        
        # Get list of available models
        tts = TTS()
        models = tts.list_models()
        
        # Parse and categorize models
        voice_models = []
        for model_id in models:
            if isinstance(model_id, str) and "tts_models" in model_id:
                parts = model_id.split('/')
                
                voice_info = {
                    "id": model_id,
                    "name": model_id.split('/')[-1] if '/' in model_id else model_id,
                    "language": parts[1] if len(parts) > 1 else "unknown",
                    "dataset": parts[2] if len(parts) > 2 else "unknown",
                    "architecture": parts[3] if len(parts) > 3 else "unknown",
                    "category": "multi-speaker" if "vctk" in model_id or "multi" in model_id else "single-speaker",
                    "quality": "high" if any(x in model_id for x in ["tacotron2", "glow"]) else "medium"
                }
                voice_models.append(voice_info)
        
        return {
            "success": True,
            "voices": voice_models,
            "total_count": len(voice_models),
            "discovery_time": time.time()
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "voices": []
        }

# Marp Slide Generation Service
@app.function(
    image=gpu_image,
    memory=4096,
    timeout=180,
    min_containers=0,
    max_containers=5
)
def generate_slides(markdown_content: str, theme: str = "default") -> Dict[str, Any]:
    """Generate presentation slides from markdown using Marp"""
    try:
        import subprocess
        import tempfile
        import base64
        
        # Create temporary files
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as md_file:
            md_file.write(markdown_content)
            md_path = md_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.html', delete=False) as html_file:
            html_path = html_file.name
        
        # Generate slides using Marp CLI
        cmd = [
            "marp", 
            "--html", 
            "--theme", theme,
            "--output", html_path,
            md_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            # Read generated HTML
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # Cleanup
            os.unlink(md_path)
            os.unlink(html_path)
            
            return {
                "success": True,
                "html_content": html_content,
                "theme": theme,
                "slide_count": markdown_content.count('---') + 1
            }
        else:
            return {
                "success": False,
                "error": result.stderr,
                "stdout": result.stdout
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# Web endpoints for API access
@app.function(
    image=gpu_image,
    memory=2048,
    timeout=60
)
@modal.fastapi_endpoint(method="POST")
def api_mistral(request: Dict[str, Any]):
    """API endpoint for Mistral text generation"""
    prompt = request.get("prompt", "")
    max_tokens = request.get("max_tokens", 512)
    temperature = request.get("temperature", 0.7)
    
    if not prompt:
        return {"error": "Prompt is required"}
    
    return mistral_generate.remote(prompt, max_tokens, temperature)

@app.function(
    image=gpu_image,
    memory=2048,
    timeout=60
)
@modal.fastapi_endpoint(method="POST")
def api_tts(request: Dict[str, Any]):
    """API endpoint for Coqui TTS generation"""
    text = request.get("text", "")
    voice_id = request.get("voice_id", "tts_models/en/ljspeech/tacotron2-DDC")
    
    if not text:
        return {"error": "Text is required"}
    
    return coqui_tts_generate.remote(text, voice_id)

@app.function(
    image=gpu_image,
    memory=2048,
    timeout=60
)
@modal.fastapi_endpoint(method="GET")
def api_voices():
    """API endpoint for voice discovery"""
    return discover_coqui_voices.remote()

@app.function(
    image=gpu_image,
    memory=2048,
    timeout=60
)
@modal.fastapi_endpoint(method="POST")
def api_slides(request: Dict[str, Any]):
    """API endpoint for slide generation"""
    markdown = request.get("markdown", "")
    theme = request.get("theme", "default")
    
    if not markdown:
        return {"error": "Markdown content is required"}
    
    return generate_slides.remote(markdown, theme)

if __name__ == "__main__":
    print("Updated Modal A100 GPU Production App")
    print("Compatible with Modal 1.0+ API")
    print("Available endpoints:")
    print("  /health - GPU health check")
    print("  /api_mistral - Mistral text generation")
    print("  /api_tts - Coqui TTS generation")
    print("  /api_voices - Voice discovery")
    print("  /api_slides - Slide generation")
