import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { OnboardingStep } from './OnboardingStep';
import { OnboardingProgress } from './OnboardingProgress';
import { Button } from '@/components/ui/button';
import { X, ChevronRight, ChevronLeft } from 'lucide-react';

interface OnboardingAnimationProps {
  onComplete: () => void;
  onSkip: () => void;
}

const steps = [
  {
    title: "Welcome to Course Creator Pro",
    description: "The ultimate platform for creating AI-powered online courses with ease.",
    icon: "🚀",
    animationKey: "welcome",
    primaryColor: "bg-gradient-to-r from-blue-600 to-blue-400",
    image: "/onboarding/welcome.svg"
  },
  {
    title: "Create Courses with AI",
    description: "Generate course structure, content, and quizzes with just a few clicks.",
    icon: "🧠",
    animationKey: "ai",
    primaryColor: "bg-gradient-to-r from-purple-600 to-purple-400",
    image: "/onboarding/ai.svg"
  },
  {
    title: "Interactive Avatars",
    description: "Bring your courses to life with animated avatars and voice generation.",
    icon: "🎭",
    animationKey: "avatars",
    primaryColor: "bg-gradient-to-r from-green-600 to-green-400",
    image: "/onboarding/avatars.svg"
  },
  {
    title: "Collaborate & Share",
    description: "Work with team members and publish your courses to various platforms.",
    icon: "👥",
    animationKey: "collaborate",
    primaryColor: "bg-gradient-to-r from-amber-600 to-amber-400",
    image: "/onboarding/collaborate.svg"
  },
  {
    title: "Track & Analyze",
    description: "Monitor course performance and engagement with detailed analytics.",
    icon: "📊",
    animationKey: "analytics",
    primaryColor: "bg-gradient-to-r from-pink-600 to-pink-400",
    image: "/onboarding/analytics.svg"
  }
];

export function OnboardingAnimation({ onComplete, onSkip }: OnboardingAnimationProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [direction, setDirection] = useState(0);

  const goToNextStep = () => {
    if (currentStep < steps.length - 1) {
      setDirection(1);
      setCurrentStep(prev => prev + 1);
    } else {
      onComplete();
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setDirection(-1);
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSkip = () => {
    onSkip();
  };

  // Auto-advance to next step every 8 seconds if user doesn't interact
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentStep < steps.length - 1) {
        goToNextStep();
      }
    }, 8000);

    return () => clearTimeout(timer);
  }, [currentStep]);

  const isLastStep = currentStep === steps.length - 1;

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <motion.div 
        className="relative w-full max-w-4xl bg-white shadow-2xl rounded-xl overflow-hidden"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ type: "spring", damping: 20 }}
      >
        <button 
          onClick={handleSkip}
          className="absolute top-4 right-4 z-50 p-2 rounded-full bg-white/10 hover:bg-white/20 text-gray-800"
          aria-label="Skip onboarding"
        >
          <X className="w-5 h-5" />
        </button>

        <div className="h-[600px] relative overflow-hidden">
          <AnimatePresence initial={false} custom={direction} mode="wait">
            <OnboardingStep 
              key={currentStep}
              {...steps[currentStep]} 
              direction={direction}
            />
          </AnimatePresence>
        </div>

        <div className="p-4 bg-gray-50 border-t flex items-center justify-between">
          <OnboardingProgress 
            totalSteps={steps.length} 
            currentStep={currentStep} 
            onStepClick={setCurrentStep} 
          />
          
          <div className="flex items-center gap-3">
            {currentStep > 0 && (
              <Button
                variant="outline" 
                onClick={goToPreviousStep}
                className="gap-1"
              >
                <ChevronLeft className="w-4 h-4" />
                Back
              </Button>
            )}
            
            <Button 
              onClick={goToNextStep}
              className="gap-1"
            >
              {isLastStep ? "Get Started" : "Next"}
              {!isLastStep && <ChevronRight className="w-4 h-4" />}
            </Button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}