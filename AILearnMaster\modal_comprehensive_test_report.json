{"timestamp": **********, "duration": 80.47434210777283, "success_rate": 20.0, "gpu_available": false, "gpu_name": "Unknown", "services": {"health": {"status": "success", "data": {"status": "healthy", "timestamp": **********.6989279, "gpu_info": {"gpu_available": true, "gpu_count": 1, "gpu_name": "NVIDIA A100 80GB PCIe", "cuda_version": "12.1", "pytorch_version": "2.1.0+cu121", "memory_allocated": 0, "memory_reserved": 0}, "services": ["tts", "mistral", "slides"]}, "gpu_available": false, "gpu_name": "Unknown", "response_time": 13.946177}, "mistral": {"status": "unknown", "data": {"success": true, "generated_text": "The following is a list of the most common questions that people ask when they are asked about", "model": "gpt2", "tokens_generated": 18, "prompt_length": 61}, "response_time": 17.385998}, "tts": {"status": "unknown", "data": {"success": true, "audio_base64": "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********************************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", "voice_id": "tts_models/en/ljspeech/tacotron2-DDC", "text_length": 51, "format": "wav", "method": "coqui_tts"}, "response_time": 33.507174}, "voices": {"status": "unknown", "data": {"success": true, "voices": [], "total_count": 0, "discovery_time": 1751448547.7370212}, "response_time": 7.704377}, "slides": {"status": "unknown", "data": {"success": true, "html_content": "<!DOCTYPE html><html lang=\"C\"><head><title>Test Slide</title><meta property=\"og:title\" content=\"Test Slide\"><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width,height=device-height,initial-scale=1.0\"><meta name=\"apple-mobile-web-app-capable\" content=\"yes\"><meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\"><meta property=\"og:type\" content=\"website\"><meta name=\"twitter:card\" content=\"summary\"><style>@media screen{body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button{appearance:none;background-color:initial;border:0;color:inherit;cursor:pointer;font-size:inherit;opacity:.8;outline:none;padding:0;transition:opacity .2s linear;-webkit-tap-highlight-color:transparent}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button:disabled,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button:disabled,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button:disabled,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button:disabled{cursor:not-allowed;opacity:.15!important}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button:hover,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button:hover,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button:hover,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button:hover{opacity:1}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button:hover:active,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button:hover:active,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button:hover:active,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button:hover:active{opacity:.6}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button:hover:not(:disabled),body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button:hover:not(:disabled),body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button:hover:not(:disabled),body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button:hover:not(:disabled){transition:none}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=prev],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=prev],body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button.bespoke-marp-presenter-info-page-prev{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJNNjggOTAgMjggNTBsNDAtNDAiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=next],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=next],body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button.bespoke-marp-presenter-info-page-next{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJtMzIgOTAgNDAtNDAtNDAtNDAiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=fullscreen],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=fullscreen]{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48c3R5bGU+LmF7ZmlsbDpub25lO3N0cm9rZTojZmZmO3N0cm9rZS1saW5lY2FwOnJvdW5kO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2Utd2lkdGg6NXB4fTwvc3R5bGU+PC9kZWZzPjxyZWN0IHdpZHRoPSI4MCIgaGVpZ2h0PSI2MCIgeD0iMTAiIHk9IjIwIiBjbGFzcz0iYSIgcng9IjUuNjciLz48cGF0aCBkPSJNNDAgNzBIMjBWNTBtMjAgMEwyMCA3MG00MC00MGgyMHYyMG0tMjAgMCAyMC0yMCIgY2xhc3M9ImEiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button.exit[data-bespoke-marp-osc=fullscreen],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button.exit[data-bespoke-marp-osc=fullscreen]{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48c3R5bGU+LmF7ZmlsbDpub25lO3N0cm9rZTojZmZmO3N0cm9rZS1saW5lY2FwOnJvdW5kO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2Utd2lkdGg6NXB4fTwvc3R5bGU+PC9kZWZzPjxyZWN0IHdpZHRoPSI4MCIgaGVpZ2h0PSI2MCIgeD0iMTAiIHk9IjIwIiBjbGFzcz0iYSIgcng9IjUuNjciLz48cGF0aCBkPSJNMjAgNTBoMjB2MjBtLTIwIDAgMjAtMjBtNDAgMEg2MFYzMG0yMCAwTDYwIDUwIiBjbGFzcz0iYSIvPjwvc3ZnPg==\")}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=presenter],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=presenter]{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJNODcuOCA0Ny41Qzg5IDUwIDg3LjcgNTIgODUgNTJIMzVhOC43IDguNyAwIDAgMS03LjItNC41bC0xNS42LTMxQzExIDE0IDEyLjIgMTIgMTUgMTJoNTBhOC44IDguOCAwIDAgMSA3LjIgNC41ek02MCA1MnYzNm0tMTAgMGgyME00NSA0MmgyMCIvPjwvc3ZnPg==\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button.bespoke-marp-presenter-note-bigger{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJNMTIgNTBoODBNNTIgOTBWMTAiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button.bespoke-marp-presenter-note-smaller{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJNMTIgNTBoODAiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}}@keyframes __bespoke_marp_transition_reduced_outgoing__{0%{opacity:1}to{opacity:0}}@keyframes __bespoke_marp_transition_reduced_incoming__{0%{mix-blend-mode:plus-lighter;opacity:0}to{mix-blend-mode:plus-lighter;opacity:1}}.bespoke-marp-note,.bespoke-marp-osc,.bespoke-progress-parent{display:none;transition:none}@media screen{::view-transition-group(*){animation-duration:var(--marp-bespoke-transition-animation-duration,.5s);animation-timing-function:ease}::view-transition-new(*),::view-transition-old(*){animation-delay:0s;animation-direction:var(--marp-bespoke-transition-animation-direction,normal);animation-duration:var(--marp-bespoke-transition-animation-duration,.5s);animation-fill-mode:both;animation-name:var(--marp-bespoke-transition-animation-name,var(--marp-bespoke-transition-animation-name-fallback,__bespoke_marp_transition_no_animation__));mix-blend-mode:normal}::view-transition-old(*){--marp-bespoke-transition-animation-name-fallback:__bespoke_marp_transition_reduced_outgoing__;animation-timing-function:ease}::view-transition-new(*){--marp-bespoke-transition-animation-name-fallback:__bespoke_marp_transition_reduced_incoming__;animation-timing-function:ease}::view-transition-new(root),::view-transition-old(root){animation-timing-function:linear}::view-transition-new(__bespoke_marp_transition_osc__),::view-transition-old(__bespoke_marp_transition_osc__){animation-duration:0s!important;animation-name:__bespoke_marp_transition_osc__!important}::view-transition-new(__bespoke_marp_transition_osc__){opacity:0!important}.bespoke-marp-transition-warming-up::view-transition-group(*),.bespoke-marp-transition-warming-up::view-transition-new(*),.bespoke-marp-transition-warming-up::view-transition-old(*){animation-play-state:paused!important}body,html{height:100%;margin:0}body{background:#000;overflow:hidden}svg.bespoke-marp-slide{content-visibility:hidden;opacity:0;pointer-events:none;z-index:-1}svg.bespoke-marp-slide:not(.bespoke-marp-active) *{view-transition-name:none!important}svg.bespoke-marp-slide.bespoke-marp-active{content-visibility:visible;opacity:1;pointer-events:auto;z-index:0}svg.bespoke-marp-slide.bespoke-marp-active.bespoke-marp-active-ready *{animation-name:__bespoke_marp__!important}@supports not (content-visibility:hidden){svg.bespoke-marp-slide[data-bespoke-marp-load=hideable]{display:none}svg.bespoke-marp-slide[data-bespoke-marp-load=hideable].bespoke-marp-active{display:block}}}@media screen and (prefers-reduced-motion:reduce){svg.bespoke-marp-slide *{view-transition-name:none!important}}@media screen{[data-bespoke-marp-fragment=inactive]{visibility:hidden}body[data-bespoke-view=\"\"] .bespoke-marp-parent,body[data-bespoke-view=next] .bespoke-marp-parent{inset:0;position:absolute}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc{background:#000000a6;border-radius:7px;bottom:50px;color:#fff;contain:paint;display:block;font-family:Helvetica,Arial,sans-serif;font-size:16px;left:50%;line-height:0;opacity:1;padding:12px;position:absolute;touch-action:manipulation;transform:translateX(-50%);transition:opacity .2s linear;-webkit-user-select:none;user-select:none;white-space:nowrap;will-change:transform;z-index:1;view-transition-name:__bespoke_marp_transition_osc__}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>*,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>*{margin-left:6px}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>:first-child,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>:first-child{margin-left:0}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>span,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>span{opacity:.8}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>span[data-bespoke-marp-osc=page],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>span[data-bespoke-marp-osc=page]{display:inline-block;min-width:140px;text-align:center}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=fullscreen],body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=next],body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=presenter],body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=prev],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=fullscreen],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=next],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=presenter],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=prev]{height:32px;line-height:32px;width:32px}body[data-bespoke-view=\"\"] .bespoke-marp-parent.bespoke-marp-inactive,body[data-bespoke-view=next] .bespoke-marp-parent.bespoke-marp-inactive{cursor:none}body[data-bespoke-view=\"\"] .bespoke-marp-parent.bespoke-marp-inactive>.bespoke-marp-osc,body[data-bespoke-view=next] .bespoke-marp-parent.bespoke-marp-inactive>.bespoke-marp-osc{opacity:0;pointer-events:none}body[data-bespoke-view=\"\"] svg.bespoke-marp-slide,body[data-bespoke-view=next] svg.bespoke-marp-slide{height:100%;left:0;position:absolute;top:0;width:100%}body[data-bespoke-view=\"\"] .bespoke-progress-parent{background:#222;display:flex;height:5px;width:100%}body[data-bespoke-view=\"\"] .bespoke-progress-parent+.bespoke-marp-parent{top:5px}body[data-bespoke-view=\"\"] .bespoke-progress-parent .bespoke-progress-bar{background:#0288d1;flex:0 0 0;transition:flex-basis .2s cubic-bezier(0,1,1,1)}body[data-bespoke-view=next]{background:#0000}body[data-bespoke-view=presenter]{background:#161616}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container{display:grid;font-family:Helvetica,Arial,sans-serif;grid-template:\"current dragbar next\" minmax(140px,1fr) \"current dragbar note\" 2fr \"info    dragbar note\" 3em;grid-template-columns:minmax(3px,var(--bespoke-marp-presenter-split-ratio,66%)) 0 minmax(3px,1fr);height:100%;left:0;position:absolute;top:0;width:100%}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-parent{grid-area:current;overflow:hidden;position:relative}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-parent svg.bespoke-marp-slide{height:calc(100% - 40px);left:20px;pointer-events:none;position:absolute;top:20px;-webkit-user-select:none;user-select:none;width:calc(100% - 40px)}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-parent svg.bespoke-marp-slide.bespoke-marp-active{filter:drop-shadow(0 3px 10px rgba(0,0,0,.5))}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-dragbar-container{background:#0288d1;cursor:col-resize;grid-area:dragbar;margin-left:-3px;opacity:0;position:relative;transition:opacity .4s linear .1s;width:6px;z-index:10}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-dragbar-container:hover{opacity:1}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-dragbar-container.active{opacity:1;transition-delay:0s}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-next-container{background:#222;cursor:pointer;display:none;grid-area:next;overflow:hidden;position:relative}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-next-container.active{display:block}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-next-container iframe.bespoke-marp-presenter-next{background:#0000;border:0;display:block;filter:drop-shadow(0 3px 10px rgba(0,0,0,.5));height:calc(100% - 40px);left:20px;pointer-events:none;position:absolute;top:20px;-webkit-user-select:none;user-select:none;width:calc(100% - 40px)}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container{background:#222;color:#eee;grid-area:note;position:relative;z-index:1}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button{height:1.5em;line-height:1.5em;width:1.5em}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-presenter-note-wrapper{display:block;inset:0;position:absolute}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-presenter-note-buttons{background:#000000a6;border-radius:4px;bottom:0;display:flex;gap:4px;margin:12px;opacity:0;padding:6px;pointer-events:none;position:absolute;right:0;transition:opacity .2s linear}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-presenter-note-buttons:focus-within,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-presenter-note-wrapper:focus-within+.bespoke-marp-presenter-note-buttons,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container:hover .bespoke-marp-presenter-note-buttons{opacity:1;pointer-events:auto}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note{box-sizing:border-box;font-size:calc(1.1em*var(--bespoke-marp-note-font-scale, 1));height:calc(100% - 40px);margin:20px;overflow:auto;padding-right:3px;white-space:pre-wrap;width:calc(100% - 40px);word-wrap:break-word;scrollbar-color:#eeeeee80 #0000;scrollbar-width:thin}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note::-webkit-scrollbar{width:6px}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note::-webkit-scrollbar-track{background:#0000}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note::-webkit-scrollbar-thumb{background:#eeeeee80;border-radius:6px}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note:empty{pointer-events:none}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note.active{display:block}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note p:first-child{margin-top:0}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note p:last-child{margin-bottom:0}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container{align-items:center;box-sizing:border-box;color:#eee;display:flex;flex-wrap:nowrap;grid-area:info;justify-content:center;overflow:hidden;padding:0 10px}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-page,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-time,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-timer{box-sizing:border-box;display:block;padding:0 10px;white-space:nowrap;width:100%}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button{height:1.5em;line-height:1.5em;width:1.5em}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-page{order:2;text-align:center}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-page .bespoke-marp-presenter-info-page-text{display:inline-block;min-width:120px;text-align:center}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-time{color:#999;order:1;text-align:left}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-timer{color:#999;order:3;text-align:right}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-timer:hover{cursor:pointer}}@media print{.bespoke-marp-presenter-info-container,.bespoke-marp-presenter-next-container,.bespoke-marp-presenter-note-container{display:none}}</style><style>div#\\:\\$p > svg > foreignObject > section{width:1280px;height:720px;box-sizing:border-box;overflow:hidden;position:relative;scroll-snap-align:center center;-webkit-text-size-adjust:100%;text-size-adjust:100%}div#\\:\\$p > svg > foreignObject > section::after{bottom:0;content:attr(data-marpit-pagination);padding:inherit;pointer-events:none;position:absolute;right:0}div#\\:\\$p > svg > foreignObject > section:not([data-marpit-pagination])::after{display:none}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1){font-size:2em;margin-block:0.67em}div#\\:\\$p > svg > foreignObject > section video::-webkit-media-controls{will-change:transform}@page {size:1280px 720px;margin:0}@media print{html, body{background-color:#fff;margin:0;page-break-inside:avoid;break-inside:avoid-page}div#\\:\\$p > svg > foreignObject > section{page-break-before:always;break-before:page}div#\\:\\$p > svg > foreignObject > section, div#\\:\\$p > svg > foreignObject > section *{-webkit-print-color-adjust:exact!important;animation-delay:0s!important;animation-duration:0s!important;color-adjust:exact!important;print-color-adjust:exact!important;transition:none!important}div#\\:\\$p > svg[data-marpit-svg]{display:block;height:100vh;width:100vw}}div#\\:\\$p > svg > foreignObject > :where(section){container-type:size}div#\\:\\$p > svg > foreignObject > section img[data-marp-twemoji]{background:transparent;height:1em;margin:0 .05em 0 .1em;vertical-align:-.1em;width:1em}/*!\n * Marp default theme.\n *\n * @theme default\n * <AUTHOR> Hattori\n *\n * @auto-scaling true\n * @size 16:9 1280px 720px\n * @size 4:3 960px 720px\n */div#\\:\\$p > svg > foreignObject > section{--base-size-4:calc(var(--marpit-root-font-size, 1rem) * 0.25);--base-size-8:calc(var(--marpit-root-font-size, 1rem) * 0.5);--base-size-16:calc(var(--marpit-root-font-size, 1rem) * 1);--base-size-24:calc(var(--marpit-root-font-size, 1rem) * 1.5);--base-size-40:calc(var(--marpit-root-font-size, 1rem) * 2.5);--base-text-weight-normal:400;--base-text-weight-medium:500;--base-text-weight-semibold:600;--fontStack-monospace:ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;--fgColor-accent:Highlight;}div#\\:\\$p > svg > foreignObject > section [data-theme=light],div#\\:\\$p > svg > foreignObject > section{color-scheme:light;--focus-outlineColor:#0969da;--fgColor-default:#1f2328;--fgColor-muted:#59636e;--fgColor-accent:#0969da;--fgColor-success:#1a7f37;--fgColor-attention:#9a6700;--fgColor-danger:#d1242f;--fgColor-done:#8250df;--bgColor-default:#fff;--bgColor-muted:#f6f8fa;--bgColor-neutral-muted:#818b981f;--bgColor-attention-muted:#fff8c5;--borderColor-default:#d1d9e0;--borderColor-muted:#d1d9e0b3;--borderColor-neutral-muted:#d1d9e0b3;--borderColor-accent-emphasis:#0969da;--borderColor-success-emphasis:#1a7f37;--borderColor-attention-emphasis:#9a6700;--borderColor-danger-emphasis:#cf222e;--borderColor-done-emphasis:#8250df;--color-prettylights-syntax-comment:#59636e;--color-prettylights-syntax-constant:#0550ae;--color-prettylights-syntax-constant-other-reference-link:#0a3069;--color-prettylights-syntax-entity:#6639ba;--color-prettylights-syntax-storage-modifier-import:#1f2328;--color-prettylights-syntax-entity-tag:#0550ae;--color-prettylights-syntax-keyword:#cf222e;--color-prettylights-syntax-string:#0a3069;--color-prettylights-syntax-variable:#953800;--color-prettylights-syntax-brackethighlighter-unmatched:#82071e;--color-prettylights-syntax-brackethighlighter-angle:#59636e;--color-prettylights-syntax-invalid-illegal-text:#f6f8fa;--color-prettylights-syntax-invalid-illegal-bg:#82071e;--color-prettylights-syntax-carriage-return-text:#f6f8fa;--color-prettylights-syntax-carriage-return-bg:#cf222e;--color-prettylights-syntax-string-regexp:#116329;--color-prettylights-syntax-markup-list:#3b2300;--color-prettylights-syntax-markup-heading:#0550ae;--color-prettylights-syntax-markup-italic:#1f2328;--color-prettylights-syntax-markup-bold:#1f2328;--color-prettylights-syntax-markup-deleted-text:#82071e;--color-prettylights-syntax-markup-deleted-bg:#ffebe9;--color-prettylights-syntax-markup-inserted-text:#116329;--color-prettylights-syntax-markup-inserted-bg:#dafbe1;--color-prettylights-syntax-markup-changed-text:#953800;--color-prettylights-syntax-markup-changed-bg:#ffd8b5;--color-prettylights-syntax-markup-ignored-text:#d1d9e0;--color-prettylights-syntax-markup-ignored-bg:#0550ae;--color-prettylights-syntax-meta-diff-range:#8250df;--color-prettylights-syntax-sublimelinter-gutter-mark:#818b98;}div#\\:\\$p > svg > foreignObject > section [data-theme=dark],div#\\:\\$p > svg > foreignObject > section:where(.invert){color-scheme:dark;--focus-outlineColor:#1f6feb;--fgColor-default:#f0f6fc;--fgColor-muted:#9198a1;--fgColor-accent:#4493f8;--fgColor-success:#3fb950;--fgColor-attention:#d29922;--fgColor-danger:#f85149;--fgColor-done:#ab7df8;--bgColor-default:#0d1117;--bgColor-muted:#151b23;--bgColor-neutral-muted:#656c7633;--bgColor-attention-muted:#bb800926;--borderColor-default:#3d444d;--borderColor-muted:#3d444db3;--borderColor-neutral-muted:#3d444db3;--borderColor-accent-emphasis:#1f6feb;--borderColor-success-emphasis:#238636;--borderColor-attention-emphasis:#9e6a03;--borderColor-danger-emphasis:#da3633;--borderColor-done-emphasis:#8957e5;--color-prettylights-syntax-comment:#9198a1;--color-prettylights-syntax-constant:#79c0ff;--color-prettylights-syntax-constant-other-reference-link:#a5d6ff;--color-prettylights-syntax-entity:#d2a8ff;--color-prettylights-syntax-storage-modifier-import:#f0f6fc;--color-prettylights-syntax-entity-tag:#7ee787;--color-prettylights-syntax-keyword:#ff7b72;--color-prettylights-syntax-string:#a5d6ff;--color-prettylights-syntax-variable:#ffa657;--color-prettylights-syntax-brackethighlighter-unmatched:#f85149;--color-prettylights-syntax-brackethighlighter-angle:#9198a1;--color-prettylights-syntax-invalid-illegal-text:#f0f6fc;--color-prettylights-syntax-invalid-illegal-bg:#8e1519;--color-prettylights-syntax-carriage-return-text:#f0f6fc;--color-prettylights-syntax-carriage-return-bg:#b62324;--color-prettylights-syntax-string-regexp:#7ee787;--color-prettylights-syntax-markup-list:#f2cc60;--color-prettylights-syntax-markup-heading:#1f6feb;--color-prettylights-syntax-markup-italic:#f0f6fc;--color-prettylights-syntax-markup-bold:#f0f6fc;--color-prettylights-syntax-markup-deleted-text:#ffdcd7;--color-prettylights-syntax-markup-deleted-bg:#67060c;--color-prettylights-syntax-markup-inserted-text:#aff5b4;--color-prettylights-syntax-markup-inserted-bg:#033a16;--color-prettylights-syntax-markup-changed-text:#ffdfb6;--color-prettylights-syntax-markup-changed-bg:#5a1e02;--color-prettylights-syntax-markup-ignored-text:#f0f6fc;--color-prettylights-syntax-markup-ignored-bg:#1158c7;--color-prettylights-syntax-meta-diff-range:#d2a8ff;--color-prettylights-syntax-sublimelinter-gutter-mark:#3d444d;}div#\\:\\$p > svg > foreignObject > section{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;background-color:var(--bgColor-default);color:var(--fgColor-default);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Noto Sans,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;font-size:16px;line-height:1.5;margin:0;word-wrap:break-word}div#\\:\\$p > svg > foreignObject > section{--marpit-root-font-size:16px;}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6):hover .anchor .octicon-link:before{background-color:currentColor;content:\" \";display:inline-block;height:16px;-webkit-mask-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\" viewBox=\"0 0 16 16\"><path fill-rule=\"evenodd\" d=\"M7.775 3.275a.75.75 0 0 0 1.06 1.06l1.25-1.25a2 2 0 1 1 2.83 2.83l-2.5 2.5a2 2 0 0 1-2.83 0 .75.75 0 0 0-1.06 1.06 3.5 3.5 0 0 0 4.95 0l2.5-2.5a3.5 3.5 0 0 0-4.95-4.95zm-4.69 9.64a2 2 0 0 1 0-2.83l2.5-2.5a2 2 0 0 1 2.83 0 .75.75 0 0 0 1.06-1.06 3.5 3.5 0 0 0-4.95 0l-2.5 2.5a3.5 3.5 0 0 0 4.95 4.95l1.25-1.25a.75.75 0 0 0-1.06-1.06l-1.25 1.25a2 2 0 0 1-2.83 0\"/></svg>');mask-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\" viewBox=\"0 0 16 16\"><path fill-rule=\"evenodd\" d=\"M7.775 3.275a.75.75 0 0 0 1.06 1.06l1.25-1.25a2 2 0 1 1 2.83 2.83l-2.5 2.5a2 2 0 0 1-2.83 0 .75.75 0 0 0-1.06 1.06 3.5 3.5 0 0 0 4.95 0l2.5-2.5a3.5 3.5 0 0 0-4.95-4.95zm-4.69 9.64a2 2 0 0 1 0-2.83l2.5-2.5a2 2 0 0 1 2.83 0 .75.75 0 0 0 1.06-1.06 3.5 3.5 0 0 0-4.95 0l-2.5 2.5a3.5 3.5 0 0 0 4.95 4.95l1.25-1.25a.75.75 0 0 0-1.06-1.06l-1.25 1.25a2 2 0 0 1-2.83 0\"/></svg>');width:16px}div#\\:\\$p > svg > foreignObject > section details,div#\\:\\$p > svg > foreignObject > section figcaption,div#\\:\\$p > svg > foreignObject > section figure{display:block}div#\\:\\$p > svg > foreignObject > section summary{display:list-item}div#\\:\\$p > svg > foreignObject > section [hidden]{display:none!important}div#\\:\\$p > svg > foreignObject > section a{background-color:transparent;color:var(--fgColor-accent);text-decoration:none}div#\\:\\$p > svg > foreignObject > section abbr[title]{border-bottom:none;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}div#\\:\\$p > svg > foreignObject > section b,div#\\:\\$p > svg > foreignObject > section strong{font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section dfn{font-style:italic}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1){border-bottom:1px solid var(--borderColor-muted);font-size:2em;font-weight:var(--base-text-weight-semibold, 600);margin:.67em 0;padding-bottom:.3em}div#\\:\\$p > svg > foreignObject > section mark{background-color:var(--bgColor-attention-muted);color:var(--fgColor-default)}div#\\:\\$p > svg > foreignObject > section small{font-size:90%}div#\\:\\$p > svg > foreignObject > section sub,div#\\:\\$p > svg > foreignObject > section sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}div#\\:\\$p > svg > foreignObject > section sub{bottom:-.25em}div#\\:\\$p > svg > foreignObject > section sup{top:-.5em}div#\\:\\$p > svg > foreignObject > section img{border-style:none;box-sizing:content-box;max-width:100%}div#\\:\\$p > svg > foreignObject > section code,div#\\:\\$p > svg > foreignObject > section kbd,div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre),div#\\:\\$p > svg > foreignObject > section samp{font-family:monospace;font-size:1em}div#\\:\\$p > svg > foreignObject > section figure{margin:1em var(--base-size-40)}div#\\:\\$p > svg > foreignObject > section hr{background:transparent;background-color:var(--borderColor-default);border:0;box-sizing:content-box;height:.25em;margin:var(--base-size-24) 0;overflow:hidden;padding:0}div#\\:\\$p > svg > foreignObject > section input{font:inherit;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible}div#\\:\\$p > svg > foreignObject > section [type=button],div#\\:\\$p > svg > foreignObject > section [type=reset],div#\\:\\$p > svg > foreignObject > section [type=submit]{-webkit-appearance:button;-moz-appearance:button;appearance:button}div#\\:\\$p > svg > foreignObject > section [type=checkbox],div#\\:\\$p > svg > foreignObject > section [type=radio]{box-sizing:border-box;padding:0}div#\\:\\$p > svg > foreignObject > section [type=number]::-webkit-inner-spin-button,div#\\:\\$p > svg > foreignObject > section [type=number]::-webkit-outer-spin-button{height:auto}div#\\:\\$p > svg > foreignObject > section [type=search]::-webkit-search-cancel-button,div#\\:\\$p > svg > foreignObject > section [type=search]::-webkit-search-decoration{-webkit-appearance:none;appearance:none}div#\\:\\$p > svg > foreignObject > section ::-webkit-input-placeholder{color:inherit;opacity:.54}div#\\:\\$p > svg > foreignObject > section ::-webkit-file-upload-button{-webkit-appearance:button;appearance:button;font:inherit}div#\\:\\$p > svg > foreignObject > section a:hover{text-decoration:underline}div#\\:\\$p > svg > foreignObject > section ::-moz-placeholder{color:var(--fgColor-muted);opacity:1}div#\\:\\$p > svg > foreignObject > section ::placeholder{color:var(--fgColor-muted);opacity:1}div#\\:\\$p > svg > foreignObject > section hr:after,div#\\:\\$p > svg > foreignObject > section hr:before{content:\"\";display:table}div#\\:\\$p > svg > foreignObject > section hr:after{clear:both}div#\\:\\$p > svg > foreignObject > section table{border-collapse:collapse;border-spacing:0;display:block;font-variant:tabular-nums;max-width:100%;overflow:auto;width:-moz-max-content;width:max-content}div#\\:\\$p > svg > foreignObject > section td,div#\\:\\$p > svg > foreignObject > section th{padding:0}div#\\:\\$p > svg > foreignObject > section details summary{cursor:pointer}div#\\:\\$p > svg > foreignObject > section [role=button]:focus,div#\\:\\$p > svg > foreignObject > section a:focus,div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus,div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus{box-shadow:none;outline:2px solid var(--focus-outlineColor);outline-offset:-2px}div#\\:\\$p > svg > foreignObject > section [role=button]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section a:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus:not(:focus-visible){outline:1px solid transparent}div#\\:\\$p > svg > foreignObject > section [role=button]:focus-visible,div#\\:\\$p > svg > foreignObject > section a:focus-visible,div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus-visible,div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus-visible{box-shadow:none;outline:2px solid var(--focus-outlineColor);outline-offset:-2px}div#\\:\\$p > svg > foreignObject > section a:not([class]):focus,div#\\:\\$p > svg > foreignObject > section a:not([class]):focus-visible,div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus,div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus-visible,div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus,div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus-visible{outline-offset:0}div#\\:\\$p > svg > foreignObject > section kbd{background-color:var(--bgColor-muted);border-bottom-color:var(--borderColor-neutral-muted);border:1px solid var(--borderColor-neutral-muted);border-radius:6px;box-shadow:inset 0 -1px 0 var(--borderColor-neutral-muted);color:var(--fgColor-default);display:inline-block;font:11px var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);line-height:10px;padding:var(--base-size-4);vertical-align:middle}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2),div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3),div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4),div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5),div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6){font-weight:var(--base-text-weight-semibold, 600);line-height:1.25;margin-bottom:var(--base-size-16);margin-top:var(--base-size-24)}div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2){border-bottom:1px solid var(--borderColor-muted);font-size:1.5em;padding-bottom:.3em}div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2),div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3){font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3){font-size:1.25em}div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4){font-size:1em}div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4),div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5){font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5){font-size:.875em}div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6){color:var(--fgColor-muted);font-size:.85em;font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section p{margin-bottom:10px;margin-top:0}div#\\:\\$p > svg > foreignObject > section blockquote{border-left:.25em solid var(--borderColor-default);color:var(--fgColor-muted);margin:0;padding:0 1em}div#\\:\\$p > svg > foreignObject > section ol,div#\\:\\$p > svg > foreignObject > section ul{margin-bottom:0;margin-top:0;padding-left:2em}div#\\:\\$p > svg > foreignObject > section ol ol,div#\\:\\$p > svg > foreignObject > section ul ol{list-style-type:lower-roman}div#\\:\\$p > svg > foreignObject > section ol ol ol,div#\\:\\$p > svg > foreignObject > section ol ul ol,div#\\:\\$p > svg > foreignObject > section ul ol ol,div#\\:\\$p > svg > foreignObject > section ul ul ol{list-style-type:lower-alpha}div#\\:\\$p > svg > foreignObject > section dd{margin-left:0}div#\\:\\$p > svg > foreignObject > section code,div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre),div#\\:\\$p > svg > foreignObject > section samp,div#\\:\\$p > svg > foreignObject > section tt{font-family:var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);font-size:12px}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre){margin-bottom:0;margin-top:0;word-wrap:normal}div#\\:\\$p > svg > foreignObject > section .octicon{display:inline-block;overflow:visible!important;vertical-align:text-bottom;fill:currentColor}div#\\:\\$p > svg > foreignObject > section input::-webkit-inner-spin-button,div#\\:\\$p > svg > foreignObject > section input::-webkit-outer-spin-button{-webkit-appearance:none;appearance:none;margin:0}div#\\:\\$p > svg > foreignObject > section .mr-2{margin-right:var(--base-size-8, 8px)!important}div#\\:\\$p > svg > foreignObject > section:after,div#\\:\\$p > svg > foreignObject > section:before{display:table}div#\\:\\$p > svg > foreignObject > section:after{clear:both}div#\\:\\$p > svg > foreignObject > section>:first-child{margin-top:0!important}div#\\:\\$p > svg > foreignObject > section>:last-child{margin-bottom:0!important}div#\\:\\$p > svg > foreignObject > section a:not([href]){color:inherit;text-decoration:none}div#\\:\\$p > svg > foreignObject > section .absent{color:var(--fgColor-danger)}div#\\:\\$p > svg > foreignObject > section .anchor{float:left;line-height:1;margin-left:-20px;padding-right:var(--base-size-4)}div#\\:\\$p > svg > foreignObject > section .anchor:focus{outline:none}div#\\:\\$p > svg > foreignObject > section blockquote,div#\\:\\$p > svg > foreignObject > section details,div#\\:\\$p > svg > foreignObject > section dl,div#\\:\\$p > svg > foreignObject > section ol,div#\\:\\$p > svg > foreignObject > section p,div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre),div#\\:\\$p > svg > foreignObject > section table,div#\\:\\$p > svg > foreignObject > section ul{margin-bottom:var(--base-size-16);margin-top:0}div#\\:\\$p > svg > foreignObject > section blockquote>:first-child{margin-top:0}div#\\:\\$p > svg > foreignObject > section blockquote>:last-child{margin-bottom:0}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6) .octicon-link{color:var(--fgColor-default);vertical-align:middle;visibility:hidden}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6):hover .anchor{text-decoration:none}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6):hover .anchor .octicon-link{visibility:visible}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1) code,div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1) tt,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2) code,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2) tt,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3) code,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3) tt,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4) code,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4) tt,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5) code,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5) tt,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6) code,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6) tt{font-size:inherit;padding:0 .2em}div#\\:\\$p > svg > foreignObject > section summary :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section summary :is(h2, marp-h2),div#\\:\\$p > svg > foreignObject > section summary :is(h3, marp-h3),div#\\:\\$p > svg > foreignObject > section summary :is(h4, marp-h4),div#\\:\\$p > svg > foreignObject > section summary :is(h5, marp-h5),div#\\:\\$p > svg > foreignObject > section summary :is(h6, marp-h6){display:inline-block}div#\\:\\$p > svg > foreignObject > section summary :is(h1, marp-h1) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h2, marp-h2) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h3, marp-h3) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h4, marp-h4) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h5, marp-h5) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h6, marp-h6) .anchor{margin-left:-40px}div#\\:\\$p > svg > foreignObject > section summary :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section summary :is(h2, marp-h2){border-bottom:0;padding-bottom:0}div#\\:\\$p > svg > foreignObject > section ol.no-list,div#\\:\\$p > svg > foreignObject > section ul.no-list{list-style-type:none;padding:0}div#\\:\\$p > svg > foreignObject > section ol[type=\"a s\"]{list-style-type:lower-alpha}div#\\:\\$p > svg > foreignObject > section ol[type=\"A s\"]{list-style-type:upper-alpha}div#\\:\\$p > svg > foreignObject > section ol[type=\"i s\"]{list-style-type:lower-roman}div#\\:\\$p > svg > foreignObject > section ol[type=\"I s\"]{list-style-type:upper-roman}div#\\:\\$p > svg > foreignObject > section div>ol:not([type]),div#\\:\\$p > svg > foreignObject > section ol[type=\"1\"]{list-style-type:decimal}div#\\:\\$p > svg > foreignObject > section ol ol,div#\\:\\$p > svg > foreignObject > section ol ul,div#\\:\\$p > svg > foreignObject > section ul ol,div#\\:\\$p > svg > foreignObject > section ul ul{margin-bottom:0;margin-top:0}div#\\:\\$p > svg > foreignObject > section li>p{margin-top:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section li+li{margin-top:.25em}div#\\:\\$p > svg > foreignObject > section dl{padding:0}div#\\:\\$p > svg > foreignObject > section dl dt{font-size:1em;font-style:italic;font-weight:var(--base-text-weight-semibold, 600);margin-top:var(--base-size-16);padding:0}div#\\:\\$p > svg > foreignObject > section dl dd{margin-bottom:var(--base-size-16);padding:0 var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section table th{font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section table td,div#\\:\\$p > svg > foreignObject > section table th{border:1px solid var(--borderColor-default);padding:6px 13px}div#\\:\\$p > svg > foreignObject > section table td>:last-child{margin-bottom:0}div#\\:\\$p > svg > foreignObject > section table tr{background-color:var(--bgColor-default);border-top:1px solid var(--borderColor-muted)}div#\\:\\$p > svg > foreignObject > section table tr:nth-child(2n){background-color:var(--bgColor-muted)}div#\\:\\$p > svg > foreignObject > section table img{background-color:transparent}div#\\:\\$p > svg > foreignObject > section img[align=right]{padding-left:20px}div#\\:\\$p > svg > foreignObject > section img[align=left]{padding-right:20px}div#\\:\\$p > svg > foreignObject > section .emoji{background-color:transparent;max-width:none;vertical-align:text-top}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame,div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame>:is(span, marp-span){display:block;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame>:is(span, marp-span){border:1px solid var(--borderColor-default);float:left;margin:13px 0 0;padding:7px;width:auto}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame :is(span, marp-span) img{display:block;float:left}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame :is(span, marp-span) :is(span, marp-span){clear:both;color:var(--fgColor-default);display:block;padding:5px 0 0}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-center{clear:both;display:block;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-center>:is(span, marp-span){display:block;margin:13px auto 0;overflow:hidden;text-align:center}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-center :is(span, marp-span) img{margin:0 auto;text-align:center}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-right{clear:both;display:block;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-right>:is(span, marp-span){display:block;margin:13px 0 0;overflow:hidden;text-align:right}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-right :is(span, marp-span) img{margin:0;text-align:right}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).float-left{display:block;float:left;margin-right:13px;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).float-left :is(span, marp-span){margin:13px 0 0}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).float-right{display:block;float:right;margin-left:13px;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).float-right>:is(span, marp-span){display:block;margin:13px auto 0;overflow:hidden;text-align:right}div#\\:\\$p > svg > foreignObject > section code,div#\\:\\$p > svg > foreignObject > section tt{background-color:var(--bgColor-neutral-muted);border-radius:6px;font-size:85%;margin:0;padding:.2em .4em;white-space:break-spaces}div#\\:\\$p > svg > foreignObject > section code br,div#\\:\\$p > svg > foreignObject > section tt br{display:none}div#\\:\\$p > svg > foreignObject > section del code{text-decoration:inherit}div#\\:\\$p > svg > foreignObject > section samp{font-size:85%}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) code{font-size:100%}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre)>code{background:transparent;border:0;margin:0;padding:0;white-space:pre;word-break:normal}div#\\:\\$p > svg > foreignObject > section .highlight{margin-bottom:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section .highlight :is(pre, marp-pre){margin-bottom:0;word-break:normal}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre){background-color:var(--bgColor-muted);border-radius:6px;color:var(--fgColor-default);font-size:85%;line-height:1.45;overflow:auto;padding:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) code,div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) tt{display:inline;line-height:inherit;margin:0;max-width:auto;overflow:visible;padding:0;word-wrap:normal;background-color:transparent;border:0}div#\\:\\$p > svg > foreignObject > section .csv-data td,div#\\:\\$p > svg > foreignObject > section .csv-data th{font-size:12px;line-height:1;overflow:hidden;padding:5px;text-align:left;white-space:nowrap}div#\\:\\$p > svg > foreignObject > section .csv-data .blob-num{background:var(--bgColor-default);border:0;padding:10px var(--base-size-8) 9px;text-align:right}div#\\:\\$p > svg > foreignObject > section .csv-data tr{border-top:0}div#\\:\\$p > svg > foreignObject > section .csv-data th{background:var(--bgColor-muted);border-top:0;font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section [data-footnote-ref]:before{content:\"[\"}div#\\:\\$p > svg > foreignObject > section [data-footnote-ref]:after{content:\"]\"}div#\\:\\$p > svg > foreignObject > section .footnotes{border-top:1px solid var(--borderColor-default);color:var(--fgColor-muted);font-size:12px}div#\\:\\$p > svg > foreignObject > section div#\\:\\$p > svg > foreignObject > section section.footnotes{--marpit-root-font-size:12px;}div#\\:\\$p > svg > foreignObject > section .footnotes ol,div#\\:\\$p > svg > foreignObject > section .footnotes ol ul{padding-left:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section .footnotes ol ul{display:inline-block;margin-top:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section .footnotes li{position:relative}div#\\:\\$p > svg > foreignObject > section .footnotes li:target:before{border:2px solid var(--borderColor-accent-emphasis);border-radius:6px;bottom:calc(var(--base-size-8)*-1);content:\"\";left:calc(var(--base-size-24)*-1);pointer-events:none;position:absolute;right:calc(var(--base-size-8)*-1);top:calc(var(--base-size-8)*-1)}div#\\:\\$p > svg > foreignObject > section .footnotes li:target{color:var(--fgColor-default)}div#\\:\\$p > svg > foreignObject > section .footnotes .data-footnote-backref g-emoji{font-family:monospace}div#\\:\\$p > svg > foreignObject > section body:has(:modal){padding-right:var(--dialog-scrollgutter)!important}div#\\:\\$p > svg > foreignObject > section .pl-c{color:var(--color-prettylights-syntax-comment)}div#\\:\\$p > svg > foreignObject > section .pl-c1,div#\\:\\$p > svg > foreignObject > section .pl-s .pl-v{color:var(--color-prettylights-syntax-constant)}div#\\:\\$p > svg > foreignObject > section .pl-e,div#\\:\\$p > svg > foreignObject > section .pl-en{color:var(--color-prettylights-syntax-entity)}div#\\:\\$p > svg > foreignObject > section .pl-s .pl-s1,div#\\:\\$p > svg > foreignObject > section .pl-smi{color:var(--color-prettylights-syntax-storage-modifier-import)}div#\\:\\$p > svg > foreignObject > section .pl-ent{color:var(--color-prettylights-syntax-entity-tag)}div#\\:\\$p > svg > foreignObject > section .pl-k{color:var(--color-prettylights-syntax-keyword)}div#\\:\\$p > svg > foreignObject > section .pl-pds,div#\\:\\$p > svg > foreignObject > section .pl-s,div#\\:\\$p > svg > foreignObject > section .pl-s .pl-pse .pl-s1,div#\\:\\$p > svg > foreignObject > section .pl-sr,div#\\:\\$p > svg > foreignObject > section .pl-sr .pl-cce,div#\\:\\$p > svg > foreignObject > section .pl-sr .pl-sra,div#\\:\\$p > svg > foreignObject > section .pl-sr .pl-sre{color:var(--color-prettylights-syntax-string)}div#\\:\\$p > svg > foreignObject > section .pl-smw,div#\\:\\$p > svg > foreignObject > section .pl-v{color:var(--color-prettylights-syntax-variable)}div#\\:\\$p > svg > foreignObject > section .pl-bu{color:var(--color-prettylights-syntax-brackethighlighter-unmatched)}div#\\:\\$p > svg > foreignObject > section .pl-ii{background-color:var(--color-prettylights-syntax-invalid-illegal-bg);color:var(--color-prettylights-syntax-invalid-illegal-text)}div#\\:\\$p > svg > foreignObject > section .pl-c2{background-color:var(--color-prettylights-syntax-carriage-return-bg);color:var(--color-prettylights-syntax-carriage-return-text)}div#\\:\\$p > svg > foreignObject > section .pl-sr .pl-cce{color:var(--color-prettylights-syntax-string-regexp);font-weight:700}div#\\:\\$p > svg > foreignObject > section .pl-ml{color:var(--color-prettylights-syntax-markup-list)}div#\\:\\$p > svg > foreignObject > section .pl-mh,div#\\:\\$p > svg > foreignObject > section .pl-mh .pl-en,div#\\:\\$p > svg > foreignObject > section .pl-ms{color:var(--color-prettylights-syntax-markup-heading);font-weight:700}div#\\:\\$p > svg > foreignObject > section .pl-mi{color:var(--color-prettylights-syntax-markup-italic);font-style:italic}div#\\:\\$p > svg > foreignObject > section .pl-mb{color:var(--color-prettylights-syntax-markup-bold);font-weight:700}div#\\:\\$p > svg > foreignObject > section .pl-md{background-color:var(--color-prettylights-syntax-markup-deleted-bg);color:var(--color-prettylights-syntax-markup-deleted-text)}div#\\:\\$p > svg > foreignObject > section .pl-mi1{background-color:var(--color-prettylights-syntax-markup-inserted-bg);color:var(--color-prettylights-syntax-markup-inserted-text)}div#\\:\\$p > svg > foreignObject > section .pl-mc{background-color:var(--color-prettylights-syntax-markup-changed-bg);color:var(--color-prettylights-syntax-markup-changed-text)}div#\\:\\$p > svg > foreignObject > section .pl-mi2{background-color:var(--color-prettylights-syntax-markup-ignored-bg);color:var(--color-prettylights-syntax-markup-ignored-text)}div#\\:\\$p > svg > foreignObject > section .pl-mdr{color:var(--color-prettylights-syntax-meta-diff-range);font-weight:700}div#\\:\\$p > svg > foreignObject > section .pl-ba{color:var(--color-prettylights-syntax-brackethighlighter-angle)}div#\\:\\$p > svg > foreignObject > section .pl-sg{color:var(--color-prettylights-syntax-sublimelinter-gutter-mark)}div#\\:\\$p > svg > foreignObject > section .pl-corl{color:var(--color-prettylights-syntax-constant-other-reference-link);text-decoration:underline}div#\\:\\$p > svg > foreignObject > section [role=button]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section [role=tabpanel][tabindex=\"0\"]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section a:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section button:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section summary:focus:not(:focus-visible){box-shadow:none;outline:none}div#\\:\\$p > svg > foreignObject > section [tabindex=\"0\"]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section details-dialog:focus:not(:focus-visible){outline:none}div#\\:\\$p > svg > foreignObject > section g-emoji{display:inline-block;font-family:Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;font-size:1em;font-style:normal!important;font-weight:var(--base-text-weight-normal, 400);line-height:1;min-width:1ch;vertical-align:-.075em}div#\\:\\$p > svg > foreignObject > section g-emoji img{height:1em;width:1em}div#\\:\\$p > svg > foreignObject > section .task-list-item{list-style-type:none}div#\\:\\$p > svg > foreignObject > section .task-list-item label{font-weight:var(--base-text-weight-normal, 400)}div#\\:\\$p > svg > foreignObject > section .task-list-item.enabled label{cursor:pointer}div#\\:\\$p > svg > foreignObject > section .task-list-item+.task-list-item{margin-top:var(--base-size-4)}div#\\:\\$p > svg > foreignObject > section .task-list-item .handle{display:none}div#\\:\\$p > svg > foreignObject > section .task-list-item-checkbox{margin:0 .2em .25em -1.4em;vertical-align:middle}div#\\:\\$p > svg > foreignObject > section ul:dir(rtl) .task-list-item-checkbox{margin:0 -1.6em .25em .2em}div#\\:\\$p > svg > foreignObject > section ol:dir(rtl) .task-list-item-checkbox{margin:0 -1.6em .25em .2em}div#\\:\\$p > svg > foreignObject > section .contains-task-list:focus-within .task-list-item-convert-container,div#\\:\\$p > svg > foreignObject > section .contains-task-list:hover .task-list-item-convert-container{display:block;height:24px;overflow:visible;width:auto;clip:auto}div#\\:\\$p > svg > foreignObject > section ::-webkit-calendar-picker-indicator{filter:invert(50%)}div#\\:\\$p > svg > foreignObject > section .markdown-alert{border-left:.25em solid var(--borderColor-default);color:inherit;margin-bottom:var(--base-size-16);padding:var(--base-size-8) var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section .markdown-alert>:first-child{margin-top:0}div#\\:\\$p > svg > foreignObject > section .markdown-alert>:last-child{margin-bottom:0}div#\\:\\$p > svg > foreignObject > section .markdown-alert .markdown-alert-title{align-items:center;display:flex;font-weight:var(--base-text-weight-medium, 500);line-height:1}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-note{border-left-color:var(--borderColor-accent-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-note .markdown-alert-title{color:var(--fgColor-accent)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-important{border-left-color:var(--borderColor-done-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-important .markdown-alert-title{color:var(--fgColor-done)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-warning{border-left-color:var(--borderColor-attention-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-warning .markdown-alert-title{color:var(--fgColor-attention)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-tip{border-left-color:var(--borderColor-success-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-tip .markdown-alert-title{color:var(--fgColor-success)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-caution{border-left-color:var(--borderColor-danger-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-caution .markdown-alert-title{color:var(--fgColor-danger)}div#\\:\\$p > svg > foreignObject > section>:first-child>.heading-element:first-child{margin-top:0!important}div#\\:\\$p > svg > foreignObject > section .highlight :is(pre, marp-pre):has(+.zeroclipboard-container){min-height:52px}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1){color:var(--h1-color);font-size:1.6em}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2){border-bottom:none}div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2){font-size:1.3em}div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3){font-size:1.1em}div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4){font-size:1.05em}div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5){font-size:1em}div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6){font-size:.9em}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1) strong,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2) strong,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3) strong,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4) strong,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5) strong,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6) strong{color:var(--heading-strong-color);font-weight:inherit}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6)::part(auto-scaling){max-height:563px}div#\\:\\$p > svg > foreignObject > section hr{height:0;padding-top:.25em}div#\\:\\$p > svg > foreignObject > section img{background-color:transparent}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre){border:1px solid var(--borderColor-default);line-height:1.15;overflow:visible}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre)::part(auto-scaling){max-height:529px}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs){color:var(--color-prettylights-syntax-storage-modifier-import)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-doctag),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-keyword),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-meta .hljs-keyword),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-template-tag),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-template-variable),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-type),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-variable.language_){color:var(--color-prettylights-syntax-keyword)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-title),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-title.class_),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-title.class_.inherited__),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-title.function_){color:var(--color-prettylights-syntax-entity)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-attr),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-attribute),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-literal),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-meta),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-number),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-operator),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-attr),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-class),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-id),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-variable){color:var(--color-prettylights-syntax-constant)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-meta .hljs-string),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-regexp),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-string){color:var(--color-prettylights-syntax-string)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-built_in),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-symbol){color:var(--color-prettylights-syntax-variable)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-code),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-comment),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-formula){color:var(--color-prettylights-syntax-comment)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-name),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-quote),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-pseudo),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-tag){color:var(--color-prettylights-syntax-entity-tag)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-subst){color:var(--color-prettylights-syntax-storage-modifier-import)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-section){color:var(--color-prettylights-syntax-markup-heading);font-weight:700}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-bullet){color:var(--color-prettylights-syntax-markup-list)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-emphasis){color:var(--color-prettylights-syntax-markup-italic);font-style:italic}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-strong){color:var(--color-prettylights-syntax-markup-bold);font-weight:700}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-addition){background-color:var(--color-prettylights-syntax-markup-inserted-bg);color:var(--color-prettylights-syntax-markup-inserted-text)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-deletion){background-color:var(--color-prettylights-syntax-markup-deleted-bg);color:var(--color-prettylights-syntax-markup-deleted-text)}div#\\:\\$p > svg > foreignObject > section footer,div#\\:\\$p > svg > foreignObject > section header{color:var(--header-footer-color);font-size:18px;left:30px;margin:0;position:absolute}div#\\:\\$p > svg > foreignObject > section header{top:21px}div#\\:\\$p > svg > foreignObject > section footer{bottom:21px}div#\\:\\$p > svg > foreignObject > section{--h1-color:#246;--header-footer-color:hsla(0,0%,40%,.75);--heading-strong-color:#48c;--paginate-color:#777;--base-size-4:4px;--base-size-8:8px;--base-size-16:16px;--base-size-24:24px;--base-size-40:40px;align-items:stretch;display:block;flex-flow:column nowrap;font-size:29px;height:720px;padding:78.5px;place-content:safe center center;width:1280px}div#\\:\\$p > svg > foreignObject > section{--marpit-root-font-size:29px;}div#\\:\\$p > svg > foreignObject > section:where(.invert){--h1-color:#cee7ff;--header-footer-color:hsla(0,0%,60%,.75);--heading-strong-color:#7bf;--paginate-color:#999;}div#\\:\\$p > svg > foreignObject > section>:last-child,div#\\:\\$p > svg > foreignObject > section[data-footer]>:nth-last-child(2){margin-bottom:0}div#\\:\\$p > svg > foreignObject > section>:first-child,div#\\:\\$p > svg > foreignObject > section>header:first-child+*{margin-top:0}div#\\:\\$p > svg > foreignObject > section:after{bottom:21px;color:var(--paginate-color);font-size:24px;padding:0;position:absolute;right:30px}div#\\:\\$p > svg > foreignObject > section:after{--marpit-root-font-size:24px;}div#\\:\\$p > svg > foreignObject > section[data-color] :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h2, marp-h2),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h3, marp-h3),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h4, marp-h4),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h5, marp-h5),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h6, marp-h6){color:currentcolor}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"]{columns:initial!important;display:block!important;padding:0!important}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"]::before, div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"]::after, div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"content\"]::before, div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"content\"]::after{display:none!important}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"] > div[data-marpit-advanced-background-container]{all:initial;display:flex;flex-direction:row;height:100%;overflow:hidden;width:100%}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"] > div[data-marpit-advanced-background-container][data-marpit-advanced-background-direction=\"vertical\"]{flex-direction:column}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"][data-marpit-advanced-background-split] > div[data-marpit-advanced-background-container]{width:var(--marpit-advanced-background-split, 50%)}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"][data-marpit-advanced-background-split=\"right\"] > div[data-marpit-advanced-background-container]{margin-left:calc(100% - var(--marpit-advanced-background-split, 50%))}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"] > div[data-marpit-advanced-background-container] > figure{all:initial;background-position:center;background-repeat:no-repeat;background-size:cover;flex:auto;margin:0}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"] > div[data-marpit-advanced-background-container] > figure > figcaption{position:absolute;border:0;clip:rect(0, 0, 0, 0);height:1px;margin:-1px;overflow:hidden;padding:0;white-space:nowrap;width:1px}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"content\"], div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"pseudo\"]{background:transparent!important}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"pseudo\"], div#\\:\\$p > svg[data-marpit-svg] > foreignObject[data-marpit-advanced-background=\"pseudo\"]{pointer-events:none!important}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background-split]{width:100%;height:100%}\n</style></head><body><div class=\"bespoke-marp-osc\"><button data-bespoke-marp-osc=\"prev\" tabindex=\"-1\" title=\"Previous slide\">Previous slide</button><span data-bespoke-marp-osc=\"page\"></span><button data-bespoke-marp-osc=\"next\" tabindex=\"-1\" title=\"Next slide\">Next slide</button><button data-bespoke-marp-osc=\"fullscreen\" tabindex=\"-1\" title=\"Toggle fullscreen (f)\">Toggle fullscreen</button><button data-bespoke-marp-osc=\"presenter\" tabindex=\"-1\" title=\"Open presenter view (p)\">Open presenter view</button></div><div id=\":$p\"><svg data-marpit-svg=\"\" viewBox=\"0 0 1280 720\"><foreignObject width=\"1280\" height=\"720\"><section id=\"1\" data-theme=\"default\" lang=\"C\" style=\"--theme:default;\">\n<h1 id=\"test-slide\">Test Slide</h1>\n<h2 id=\"introduction\">Introduction</h2>\n<p>This is a test slide for Modal deployment.</p>\n<ul>\n<li>Point 1</li>\n<li>Point 2</li>\n<li>Point 3</li>\n</ul>\n</section>\n<script>!function(){\"use strict\";const t={h1:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"1\"},style:\"display: block; font-size: 2em; margin-block-start: 0.67em; margin-block-end: 0.67em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h2:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"2\"},style:\"display: block; font-size: 1.5em; margin-block-start: 0.83em; margin-block-end: 0.83em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h3:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"3\"},style:\"display: block; font-size: 1.17em; margin-block-start: 1em; margin-block-end: 1em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h4:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"4\"},style:\"display: block; margin-block-start: 1.33em; margin-block-end: 1.33em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h5:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"5\"},style:\"display: block; font-size: 0.83em; margin-block-start: 1.67em; margin-block-end: 1.67em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h6:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"6\"},style:\"display: block; font-size: 0.67em; margin-block-start: 2.33em; margin-block-end: 2.33em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},span:{proto:()=>HTMLSpanElement},pre:{proto:()=>HTMLElement,style:\"display: block; font-family: monospace; white-space: pre; margin: 1em 0; --marp-auto-scaling-white-space: pre;\"}},e=\"data-marp-auto-scaling-wrapper\",i=\"data-marp-auto-scaling-svg\",n=\"data-marp-auto-scaling-container\";class s extends HTMLElement{container;containerSize;containerObserver;svg;svgComputedStyle;svgPreserveAspectRatio=\"xMinYMid meet\";wrapper;wrapperSize;wrapperObserver;constructor(){super();const t=t=>([e])=>{const{width:i,height:n}=e.contentRect;this[t]={width:i,height:n},this.updateSVGRect()};this.attachShadow({mode:\"open\"}),this.containerObserver=new ResizeObserver(t(\"containerSize\")),this.wrapperObserver=new ResizeObserver(((...e)=>{t(\"wrapperSize\")(...e),this.flushSvgDisplay()}))}static get observedAttributes(){return[\"data-downscale-only\"]}connectedCallback(){this.shadowRoot.innerHTML=`\\n<style>\\n  svg[${i}] { display: block; width: 100%; height: auto; vertical-align: top; }\\n  span[${n}] { display: table; white-space: var(--marp-auto-scaling-white-space, nowrap); width: max-content; }\\n</style>\\n<div ${e}>\\n  <svg part=\"svg\" ${i}>\\n    <foreignObject><span ${n}><slot></slot></span></foreignObject>\\n  </svg>\\n</div>\\n    `.split(/\\n\\s*/).join(\"\"),this.wrapper=this.shadowRoot.querySelector(`div[${e}]`)??void 0;const t=this.svg;this.svg=this.wrapper?.querySelector(`svg[${i}]`)??void 0,this.svg!==t&&(this.svgComputedStyle=this.svg?window.getComputedStyle(this.svg):void 0),this.container=this.svg?.querySelector(`span[${n}]`)??void 0,this.observe()}disconnectedCallback(){this.svg=void 0,this.svgComputedStyle=void 0,this.wrapper=void 0,this.container=void 0,this.observe()}attributeChangedCallback(){this.observe()}flushSvgDisplay(){const{svg:t}=this;t&&(t.style.display=\"inline\",requestAnimationFrame((()=>{t.style.display=\"\"})))}observe(){this.containerObserver.disconnect(),this.wrapperObserver.disconnect(),this.wrapper&&this.wrapperObserver.observe(this.wrapper),this.container&&this.containerObserver.observe(this.container),this.svgComputedStyle&&this.observeSVGStyle(this.svgComputedStyle)}observeSVGStyle(t){const e=()=>{const i=(()=>{const e=t.getPropertyValue(\"--preserve-aspect-ratio\");if(e)return e.trim();return`x${(({textAlign:t,direction:e})=>{if(t.endsWith(\"left\"))return\"Min\";if(t.endsWith(\"right\"))return\"Max\";if(\"start\"===t||\"end\"===t){let i=\"rtl\"===e;return\"end\"===t&&(i=!i),i?\"Max\":\"Min\"}return\"Mid\"})(t)}YMid meet`})();i!==this.svgPreserveAspectRatio&&(this.svgPreserveAspectRatio=i,this.updateSVGRect()),t===this.svgComputedStyle&&requestAnimationFrame(e)};e()}updateSVGRect(){let t=Math.ceil(this.containerSize?.width??0);const e=Math.ceil(this.containerSize?.height??0);void 0!==this.dataset.downscaleOnly&&(t=Math.max(t,this.wrapperSize?.width??0));const i=this.svg?.querySelector(\":scope > foreignObject\");if(i?.setAttribute(\"width\",`${t}`),i?.setAttribute(\"height\",`${e}`),this.svg&&(this.svg.setAttribute(\"viewBox\",`0 0 ${t} ${e}`),this.svg.setAttribute(\"preserveAspectRatio\",this.svgPreserveAspectRatio),this.svg.style.height=t<=0||e<=0?\"0\":\"\"),this.container){const t=this.svgPreserveAspectRatio.toLowerCase();this.container.style.marginLeft=t.startsWith(\"xmid\")||t.startsWith(\"xmax\")?\"auto\":\"0\",this.container.style.marginRight=t.startsWith(\"xmi\")?\"auto\":\"0\"}}}const r=(t,{attrs:e={},style:i})=>class extends t{constructor(...t){super(...t);for(const[t,i]of Object.entries(e))this.hasAttribute(t)||this.setAttribute(t,i);this._shadow()}static get observedAttributes(){return[\"data-auto-scaling\"]}connectedCallback(){this._update()}attributeChangedCallback(){this._update()}_shadow(){if(!this.shadowRoot)try{this.attachShadow({mode:\"open\"})}catch(t){if(!(t instanceof Error&&\"NotSupportedError\"===t.name))throw t}return this.shadowRoot}_update(){const t=this._shadow();if(t){const e=i?`<style>:host { ${i} }</style>`:\"\";let n=\"<slot></slot>\";const{autoScaling:s}=this.dataset;if(void 0!==s){n=`<marp-auto-scaling exportparts=\"svg:auto-scaling\" ${\"downscale-only\"===s?\"data-downscale-only\":\"\"}>${n}</marp-auto-scaling>`}t.innerHTML=e+n}}};let o;const a=Symbol();let l;const c=\"marpitSVGPolyfill:setZoomFactor,\",d=Symbol(),h=Symbol();const g=()=>{const t=\"Apple Computer, Inc.\"===navigator.vendor,e=t?[u]:[],i={then:e=>(t?(async()=>{if(void 0===l){const t=document.createElement(\"canvas\");t.width=10,t.height=10;const e=t.getContext(\"2d\"),i=new Image(10,10),n=new Promise((t=>{i.addEventListener(\"load\",(()=>t()))}));i.crossOrigin=\"anonymous\",i.src=\"data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2210%22%20height%3D%2210%22%20viewBox%3D%220%200%201%201%22%3E%3CforeignObject%20width%3D%221%22%20height%3D%221%22%20requiredExtensions%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxhtml%22%3E%3Cdiv%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxhtml%22%20style%3D%22width%3A%201px%3B%20height%3A%201px%3B%20background%3A%20red%3B%20position%3A%20relative%22%3E%3C%2Fdiv%3E%3C%2FforeignObject%3E%3C%2Fsvg%3E\",await n,e.drawImage(i,0,0),l=e.getImageData(5,5,1,1).data[3]<128}return l})().then((t=>{null==e||e(t?[u]:[])})):null==e||e([]),i)};return Object.assign(e,i)};let p,m;function u(t){const e=\"object\"==typeof t&&t.target||document,i=\"object\"==typeof t?t.zoom:t;window[h]||(Object.defineProperty(window,h,{configurable:!0,value:!0}),document.body.style.zoom=1.0001,document.body.offsetHeight,document.body.style.zoom=1,window.addEventListener(\"message\",(({data:t,origin:e})=>{if(e===window.origin)try{if(t&&\"string\"==typeof t&&t.startsWith(c)){const[,e]=t.split(\",\"),i=Number.parseFloat(e);Number.isNaN(i)||(m=i)}}catch(t){console.error(t)}})));let n=!1;Array.from(e.querySelectorAll(\"svg[data-marpit-svg]\"),(t=>{var e,s,r,o;t.style.transform||(t.style.transform=\"translateZ(0)\");const a=i||m||t.currentScale||1;p!==a&&(p=a,n=a);const l=t.getBoundingClientRect(),{length:c}=t.children;for(let i=0;i<c;i+=1){const n=t.children[i];if(n.getScreenCTM){const t=n.getScreenCTM();if(t){const i=null!==(s=null===(e=n.x)||void 0===e?void 0:e.baseVal.value)&&void 0!==s?s:0,c=null!==(o=null===(r=n.y)||void 0===r?void 0:r.baseVal.value)&&void 0!==o?o:0,d=n.children.length;for(let e=0;e<d;e+=1){const s=n.children[e];if(\"SECTION\"===s.tagName){const{style:e}=s;e.transformOrigin||(e.transformOrigin=`${-i}px ${-c}px`),e.transform=`scale(${a}) matrix(${t.a}, ${t.b}, ${t.c}, ${t.d}, ${t.e-l.left}, ${t.f-l.top}) translateZ(0.0001px)`;break}}}}}})),!1!==n&&Array.from(e.querySelectorAll(\"iframe\"),(({contentWindow:t})=>{null==t||t.postMessage(`${c}${n}`,\"null\"===window.origin?\"*\":window.origin)}))}function v({once:t=!1,target:e=document}={}){const i=function(t=document){if(t[d])return t[d];let e=!0;const i=()=>{e=!1,delete t[d]};Object.defineProperty(t,d,{configurable:!0,value:i});let n=[],s=!1;(async()=>{try{n=await g()}finally{s=!0}})();const r=()=>{for(const e of n)e({target:t});s&&0===n.length||e&&window.requestAnimationFrame(r)};return r(),i}(e);return t?(i(),()=>{}):i}p=1,m=void 0;const w=Symbol(),b=(e=document)=>{if(\"undefined\"==typeof window)throw new Error(\"Marp Core's browser script is valid only in browser context.\");if(((e=document)=>{const i=window[a];i||customElements.define(\"marp-auto-scaling\",s);for(const n of Object.keys(t)){const s=`marp-${n}`,a=t[n].proto();(o??(o=!!document.createElement(\"div\",{is:\"marp-auto-scaling\"}).outerHTML.startsWith(\"<div is\"),o))&&a!==HTMLElement?i||customElements.define(s,r(a,{style:t[n].style}),{extends:n}):(i||customElements.define(s,r(HTMLElement,t[n])),e.querySelectorAll(`${n}[is=\"${s}\"]`).forEach((t=>{t.outerHTML=t.outerHTML.replace(new RegExp(`^<${n}`,\"i\"),`<${s}`).replace(new RegExp(`</${n}>$`,\"i\"),`</${s}>`)})))}window[a]=!0})(e),e[w])return e[w];const i=v({target:e}),n=()=>{i(),delete e[w]},l=Object.assign(n,{cleanup:n,update:()=>b(e)});return Object.defineProperty(e,w,{configurable:!0,value:l}),l},y=document.currentScript;b(y?y.getRootNode():document)}();\n</script></foreignObject></svg></div><script>/*!! License: https://unpkg.com/@marp-team/marp-cli@4.2.0/lib/bespoke.js.LICENSE.txt */\n!function(){\"use strict\";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,\"default\")?e.default:e}var t,n,r=(n||(n=1,t={from:function(e,t){var n,r=1===(e.parent||e).nodeType?e.parent||e:document.querySelector(e.parent||e),o=[].filter.call(\"string\"==typeof e.slides?r.querySelectorAll(e.slides):e.slides||r.children,(function(e){return\"SCRIPT\"!==e.nodeName})),a={},i=function(e,t){return(t=t||{}).index=o.indexOf(e),t.slide=e,t},s=function(e,t){a[e]=(a[e]||[]).filter((function(e){return e!==t}))},c=function(e,t){return(a[e]||[]).reduce((function(e,n){return e&&!1!==n(t)}),!0)},l=function(e,t){o[e]&&(n&&c(\"deactivate\",i(n,t)),n=o[e],c(\"activate\",i(n,t)))},d=function(e,t){var r=o.indexOf(n)+e;c(e>0?\"next\":\"prev\",i(n,t))&&l(r,t)},u={off:s,on:function(e,t){return(a[e]||(a[e]=[])).push(t),s.bind(null,e,t)},fire:c,slide:function(e,t){if(!arguments.length)return o.indexOf(n);c(\"slide\",i(o[e],t))&&l(e,t)},next:d.bind(null,1),prev:d.bind(null,-1),parent:r,slides:o,destroy:function(e){c(\"destroy\",i(n,e)),a={}}};return(t||[]).forEach((function(e){e(u)})),n||l(0),u}}),t),o=e(r);const a=document.body,i=(...e)=>history.replaceState(...e),s=\"\",c=\"presenter\",l=\"next\",d=[\"\",c,l],u=\"bespoke-marp-\",f=`data-${u}`,m=(e,{protocol:t,host:n,pathname:r,hash:o}=location)=>{const a=e.toString();return`${t}//${n}${r}${a?\"?\":\"\"}${a}${o}`},g=()=>a.dataset.bespokeView,p=e=>new URLSearchParams(location.search).get(e),v=(e,t={})=>{const n={location,setter:i,...t},r=new URLSearchParams(n.location.search);for(const t of Object.keys(e)){const n=e[t];\"string\"==typeof n?r.set(t,n):r.delete(t)}try{n.setter({...window.history.state??{}},\"\",m(r,n.location))}catch(e){console.error(e)}},h=(()=>{const e=\"bespoke-marp\";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch{return!1}})(),y=e=>{try{return localStorage.getItem(e)}catch{return null}},b=(e,t)=>{try{return localStorage.setItem(e,t),!0}catch{return!1}},w=e=>{try{return localStorage.removeItem(e),!0}catch{return!1}},x=(e,t)=>{const n=\"aria-hidden\";t?e.setAttribute(n,\"true\"):e.removeAttribute(n)},k=e=>{e.parent.classList.add(`${u}parent`),e.slides.forEach((e=>e.classList.add(`${u}slide`))),e.on(\"activate\",(t=>{const n=`${u}active`,r=t.slide,o=r.classList,a=!o.contains(n);if(e.slides.forEach((e=>{e.classList.remove(n),x(e,!0)})),o.add(n),x(r,!1),a){const e=`${n}-ready`;o.add(e),document.body.clientHeight,o.remove(e)}}))},$=e=>{let t=0,n=0;Object.defineProperty(e,\"fragments\",{enumerable:!0,value:e.slides.map((e=>[null,...e.querySelectorAll(\"[data-marpit-fragment]\")]))});const r=r=>void 0!==e.fragments[t][n+r],o=(r,o)=>{t=r,n=o,e.fragments.forEach(((e,t)=>{e.forEach(((e,n)=>{if(null==e)return;const a=t<r||t===r&&n<=o;e.setAttribute(`${f}fragment`,(a?\"\":\"in\")+\"active\");const i=`${f}current-fragment`;t===r&&n===o?e.setAttribute(i,\"current\"):e.removeAttribute(i)}))})),e.fragmentIndex=o;const a={slide:e.slides[r],index:r,fragments:e.fragments[r],fragmentIndex:o};e.fire(\"fragment\",a)};e.on(\"next\",(({fragment:a=!0})=>{if(a){if(r(1))return o(t,n+1),!1;const a=t+1;e.fragments[a]&&o(a,0)}else{const r=e.fragments[t].length;if(n+1<r)return o(t,r-1),!1;const a=e.fragments[t+1];a&&o(t+1,a.length-1)}})),e.on(\"prev\",(({fragment:a=!0})=>{if(r(-1)&&a)return o(t,n-1),!1;const i=t-1;e.fragments[i]&&o(i,e.fragments[i].length-1)})),e.on(\"slide\",(({index:t,fragment:n})=>{let r=0;if(void 0!==n){const o=e.fragments[t];if(o){const{length:e}=o;r=-1===n?e-1:Math.min(Math.max(n,0),e-1)}}o(t,r)})),o(0,0)},E=document,L=()=>!(!E.fullscreenEnabled&&!E.webkitFullscreenEnabled),S=()=>!(!E.fullscreenElement&&!E.webkitFullscreenElement),P=e=>{e.fullscreen=()=>{L()&&(async()=>{S()?(E.exitFullscreen||E.webkitExitFullscreen)?.call(E):((e=E.body)=>{(e.requestFullscreen||e.webkitRequestFullscreen)?.call(e)})()})()},document.addEventListener(\"keydown\",(t=>{\"f\"!==t.key&&\"F11\"!==t.key||t.altKey||t.ctrlKey||t.metaKey||!L()||(e.fullscreen(),t.preventDefault())}))},_=`${u}inactive`,T=(e=2e3)=>({parent:t,fire:n})=>{const r=t.classList,o=e=>n(`marp-${e?\"\":\"in\"}active`);let a;const i=()=>{a&&clearTimeout(a),a=setTimeout((()=>{r.add(_),o()}),e),r.contains(_)&&(r.remove(_),o(!0))};for(const e of[\"mousedown\",\"mousemove\",\"touchend\"])document.addEventListener(e,i);setTimeout(i,0)},I=[\"AUDIO\",\"BUTTON\",\"INPUT\",\"SELECT\",\"TEXTAREA\",\"VIDEO\"],M=e=>{e.parent.addEventListener(\"keydown\",(e=>{if(!e.target)return;const t=e.target;(I.includes(t.nodeName)||\"true\"===t.contentEditable)&&e.stopPropagation()}))},O=e=>{window.addEventListener(\"load\",(()=>{for(const t of e.slides){const e=t.querySelector(\"marp-auto-scaling, [data-auto-scaling], [data-marp-fitting]\");t.setAttribute(`${f}load`,e?\"\":\"hideable\")}}))},A=({interval:e=250}={})=>t=>{document.addEventListener(\"keydown\",(e=>{if(\" \"===e.key&&e.shiftKey)t.prev();else if(\"ArrowLeft\"===e.key||\"ArrowUp\"===e.key||\"PageUp\"===e.key)t.prev({fragment:!e.shiftKey});else if(\" \"!==e.key||e.shiftKey)if(\"ArrowRight\"===e.key||\"ArrowDown\"===e.key||\"PageDown\"===e.key)t.next({fragment:!e.shiftKey});else if(\"End\"===e.key)t.slide(t.slides.length-1,{fragment:-1});else{if(\"Home\"!==e.key)return;t.slide(0)}else t.next();e.preventDefault()}));let n,r,o=0;t.parent.addEventListener(\"wheel\",(a=>{let i=!1;const s=(e,t)=>{e&&(i=i||((e,t)=>((e,t)=>{const n=\"X\"===t?\"Width\":\"Height\";return e[`client${n}`]<e[`scroll${n}`]})(e,t)&&((e,t)=>{const{overflow:n}=e,r=e[`overflow${t}`];return\"auto\"===n||\"scroll\"===n||\"auto\"===r||\"scroll\"===r})(getComputedStyle(e),t))(e,t)),e?.parentElement&&s(e.parentElement,t)};if(0!==a.deltaX&&s(a.target,\"X\"),0!==a.deltaY&&s(a.target,\"Y\"),i)return;a.preventDefault();const c=Math.sqrt(a.deltaX**2+a.deltaY**2);if(void 0!==a.wheelDelta){if(void 0===a.webkitForce&&Math.abs(a.wheelDelta)<40)return;if(a.deltaMode===a.DOM_DELTA_PIXEL&&c<4)return}else if(a.deltaMode===a.DOM_DELTA_PIXEL&&c<12)return;r&&clearTimeout(r),r=setTimeout((()=>{n=0}),e);const l=Date.now()-o<e,d=c<=n;if(n=c,l||d)return;let u;(a.deltaX>0||a.deltaY>0)&&(u=\"next\"),(a.deltaX<0||a.deltaY<0)&&(u=\"prev\"),u&&(t[u](),o=Date.now())}))},C=(e=`.${u}osc`)=>{const t=document.querySelector(e);if(!t)return()=>{};const n=(e,n)=>{t.querySelectorAll(`[${f}osc=${JSON.stringify(e)}]`).forEach(n)};return L()||n(\"fullscreen\",(e=>e.style.display=\"none\")),h||n(\"presenter\",(e=>{e.disabled=!0,e.title=\"Presenter view is disabled due to restricted localStorage.\"})),e=>{t.addEventListener(\"click\",(t=>{if(t.target instanceof HTMLElement){const{bespokeMarpOsc:n}=t.target.dataset;n&&t.target.blur();const r={fragment:!t.shiftKey};\"next\"===n?e.next(r):\"prev\"===n?e.prev(r):\"fullscreen\"===n?e?.fullscreen():\"presenter\"===n&&e.openPresenterView()}})),e.parent.appendChild(t),e.on(\"activate\",(({index:t})=>{n(\"page\",(n=>n.textContent=`Page ${t+1} of ${e.slides.length}`))})),e.on(\"fragment\",(({index:t,fragments:r,fragmentIndex:o})=>{n(\"prev\",(e=>e.disabled=0===t&&0===o)),n(\"next\",(n=>n.disabled=t===e.slides.length-1&&o===r.length-1))})),e.on(\"marp-active\",(()=>x(t,!1))),e.on(\"marp-inactive\",(()=>x(t,!0))),L()&&(e=>{for(const t of[\"\",\"webkit\"])E.addEventListener(t+\"fullscreenchange\",e)})((()=>n(\"fullscreen\",(e=>e.classList.toggle(\"exit\",L()&&S())))))}},D=e=>{window.addEventListener(\"message\",(t=>{if(t.origin!==window.origin)return;const[n,r]=t.data.split(\":\");if(\"navigate\"===n){const[t,n]=r.split(\",\");let o=Number.parseInt(t,10),a=Number.parseInt(n,10)+1;a>=e.fragments[o].length&&(o+=1,a=0),e.slide(o,{fragment:a})}}))};var N,B,q,K,F,j,V,U={exports:{}},X=(N||(N=1,U.exports=(B=[\"area\",\"base\",\"br\",\"col\",\"command\",\"embed\",\"hr\",\"img\",\"input\",\"keygen\",\"link\",\"meta\",\"param\",\"source\",\"track\",\"wbr\"],q=function(e){return String(e).replace(/[&<>\"']/g,(function(e){return\"&\"+K[e]+\";\"}))},K={\"&\":\"amp\",\"<\":\"lt\",\">\":\"gt\",'\"':\"quot\",\"'\":\"apos\"},F=\"dangerouslySetInnerHTML\",j={className:\"class\",htmlFor:\"for\"},V={},function(e,t){var n=[],r=\"\";t=t||{};for(var o=arguments.length;o-- >2;)n.push(arguments[o]);if(\"function\"==typeof e)return t.children=n.reverse(),e(t);if(e){if(r+=\"<\"+e,t)for(var a in t)!1!==t[a]&&null!=t[a]&&a!==F&&(r+=\" \"+(j[a]?j[a]:q(a))+'=\"'+q(t[a])+'\"');r+=\">\"}if(-1===B.indexOf(e)){if(t[F])r+=t[F].__html;else for(;n.length;){var i=n.pop();if(i)if(i.pop)for(var s=i.length;s--;)n.push(i[s]);else r+=!0===V[i]?i:q(i)}r+=e?\"</\"+e+\">\":\"\"}return V[r]=!0,r})),U.exports),H=e(X);const R=({children:e})=>H(null,null,...e),W=`${u}presenter-`,J={container:`${W}container`,dragbar:`${W}dragbar-container`,next:`${W}next`,nextContainer:`${W}next-container`,noteContainer:`${W}note-container`,noteWrapper:`${W}note-wrapper`,noteButtons:`${W}note-buttons`,infoContainer:`${W}info-container`,infoPage:`${W}info-page`,infoPageText:`${W}info-page-text`,infoPagePrev:`${W}info-page-prev`,infoPageNext:`${W}info-page-next`,noteButtonsBigger:`${W}note-bigger`,noteButtonsSmaller:`${W}note-smaller`,infoTime:`${W}info-time`,infoTimer:`${W}info-timer`},Y=e=>{const{title:t}=document;document.title=\"[Presenter view]\"+(t?` - ${t}`:\"\");const n={},r=e=>(n[e]=n[e]||document.querySelector(`.${e}`),n[e]);document.body.appendChild((e=>{const t=document.createElement(\"div\");return t.className=J.container,t.appendChild(e),t.insertAdjacentHTML(\"beforeend\",H(R,null,H(\"div\",{class:J.nextContainer},H(\"iframe\",{class:J.next,src:\"?view=next\"})),H(\"div\",{class:J.dragbar}),H(\"div\",{class:J.noteContainer},H(\"div\",{class:J.noteWrapper}),H(\"div\",{class:J.noteButtons},H(\"button\",{class:J.noteButtonsSmaller,tabindex:\"-1\",title:\"Smaller notes font size\"},\"Smaller notes font size\"),H(\"button\",{class:J.noteButtonsBigger,tabindex:\"-1\",title:\"Bigger notes font size\"},\"Bigger notes font size\"))),H(\"div\",{class:J.infoContainer},H(\"div\",{class:J.infoPage},H(\"button\",{class:J.infoPagePrev,tabindex:\"-1\",title:\"Previous\"},\"Previous\"),H(\"span\",{class:J.infoPageText}),H(\"button\",{class:J.infoPageNext,tabindex:\"-1\",title:\"Next\"},\"Next\")),H(\"time\",{class:J.infoTime,title:\"Current time\"}),H(\"time\",{class:J.infoTimer,title:\"Timer\"})))),t})(e.parent)),(e=>{let t=!1;r(J.dragbar).addEventListener(\"mousedown\",(()=>{t=!0,r(J.dragbar).classList.add(\"active\")})),window.addEventListener(\"mouseup\",(()=>{t=!1,r(J.dragbar).classList.remove(\"active\")})),window.addEventListener(\"mousemove\",(e=>{if(!t)return;const n=e.clientX/document.documentElement.clientWidth*100;r(J.container).style.setProperty(\"--bespoke-marp-presenter-split-ratio\",`${Math.max(0,Math.min(100,n))}%`)})),r(J.nextContainer).addEventListener(\"click\",(()=>e.next()));const n=r(J.next),o=(a=n,(e,t)=>a.contentWindow?.postMessage(`navigate:${e},${t}`,\"null\"===window.origin?\"*\":window.origin));var a;n.addEventListener(\"load\",(()=>{r(J.nextContainer).classList.add(\"active\"),o(e.slide(),e.fragmentIndex),e.on(\"fragment\",(({index:e,fragmentIndex:t})=>o(e,t)))}));const i=document.querySelectorAll(\".bespoke-marp-note\");i.forEach((e=>{e.addEventListener(\"keydown\",(e=>e.stopPropagation())),r(J.noteWrapper).appendChild(e)})),e.on(\"activate\",(()=>i.forEach((t=>t.classList.toggle(\"active\",t.dataset.index==e.slide())))));let s=0;const c=e=>{s=Math.max(-5,s+e),r(J.noteContainer).style.setProperty(\"--bespoke-marp-note-font-scale\",(1.2**s).toFixed(4))},l=()=>c(1),d=()=>c(-1),u=r(J.noteButtonsBigger),f=r(J.noteButtonsSmaller);u.addEventListener(\"click\",(()=>{u.blur(),l()})),f.addEventListener(\"click\",(()=>{f.blur(),d()})),document.addEventListener(\"keydown\",(e=>{\"+\"===e.key&&l(),\"-\"===e.key&&d()}),!0),e.on(\"activate\",(({index:t})=>{r(J.infoPageText).textContent=`${t+1} / ${e.slides.length}`}));const m=r(J.infoPagePrev),g=r(J.infoPageNext);m.addEventListener(\"click\",(t=>{m.blur(),e.prev({fragment:!t.shiftKey})})),g.addEventListener(\"click\",(t=>{g.blur(),e.next({fragment:!t.shiftKey})})),e.on(\"fragment\",(({index:t,fragments:n,fragmentIndex:r})=>{m.disabled=0===t&&0===r,g.disabled=t===e.slides.length-1&&r===n.length-1}));let p=new Date;const v=()=>{const e=new Date,t=e=>`${Math.floor(e)}`.padStart(2,\"0\"),n=e.getTime()-p.getTime(),o=t(n/1e3%60),a=t(n/1e3/60%60),i=t(n/36e5%24);r(J.infoTime).textContent=e.toLocaleTimeString(),r(J.infoTimer).textContent=`${i}:${a}:${o}`};v(),setInterval(v,250),r(J.infoTimer).addEventListener(\"click\",(()=>{p=new Date}))})(e)},z=e=>{if(!(e=>e.syncKey&&\"string\"==typeof e.syncKey)(e))throw new Error(\"The current instance of Bespoke.js is invalid for Marp bespoke presenter plugin.\");Object.defineProperties(e,{openPresenterView:{enumerable:!0,value:G},presenterUrl:{enumerable:!0,get:Q}}),h&&document.addEventListener(\"keydown\",(t=>{\"p\"!==t.key||t.altKey||t.ctrlKey||t.metaKey||(t.preventDefault(),e.openPresenterView())}))};function G(){const{max:e,floor:t}=Math,n=e(t(.85*window.innerWidth),640),r=e(t(.85*window.innerHeight),360);return window.open(this.presenterUrl,W+this.syncKey,`width=${n},height=${r},menubar=no,toolbar=no`)}function Q(){const e=new URLSearchParams(location.search);return e.set(\"view\",\"presenter\"),e.set(\"sync\",this.syncKey),m(e)}const Z=e=>{const t=g();return t===l&&e.appendChild(document.createElement(\"span\")),{[s]:z,[c]:Y,[l]:D}[t]},ee=e=>{e.on(\"activate\",(t=>{document.querySelectorAll(\".bespoke-progress-parent > .bespoke-progress-bar\").forEach((n=>{n.style.flexBasis=100*t.index/(e.slides.length-1)+\"%\"}))}))},te=e=>{const t=Number.parseInt(e,10);return Number.isNaN(t)?null:t},ne=(e={})=>{const t={history:!0,...e};return e=>{let n=!0;const r=e=>{const t=n;try{return n=!0,e()}finally{n=t}},o=(t={fragment:!0})=>{let n=t.fragment?te(p(\"f\")||\"\"):null;((t,n)=>{const{min:r,max:o}=Math,{fragments:a,slides:i}=e,s=o(0,r(t,i.length-1)),c=o(0,r(n||0,a[s].length-1));s===e.slide()&&c===e.fragmentIndex||e.slide(s,{fragment:c})})((()=>{if(location.hash){const[t]=location.hash.slice(1).split(\":~:\");if(/^\\d+$/.test(t))return(te(t)??1)-1;const r=document.getElementById(t)||document.querySelector(`a[name=\"${CSS.escape(t)}\"]`);if(r){const{length:t}=e.slides;for(let o=0;o<t;o+=1)if(e.slides[o].contains(r)){const t=e.fragments?.[o],a=r.closest(\"[data-marpit-fragment]\");if(t&&a){const e=t.indexOf(a);e>=0&&(n=e)}return o}}}return 0})(),n)};e.on(\"fragment\",(({index:e,fragmentIndex:r})=>{n||v({f:0===r||r.toString()},{location:{...location,hash:`#${e+1}`},setter:(...e)=>t.history?history.pushState(...e):history.replaceState(...e)})})),setTimeout((()=>{o(),window.addEventListener(\"hashchange\",(()=>r((()=>{o({fragment:!1}),v({f:void 0})})))),window.addEventListener(\"popstate\",(()=>{n||r((()=>o()))})),n=!1}),0)}},re=(e={})=>{const t=e.key||window.history.state?.marpBespokeSyncKey||Math.random().toString(36).slice(2),n=`bespoke-marp-sync-${t}`;var r;r={marpBespokeSyncKey:t},v({},{setter:(e,...t)=>i({...e,...r},...t)});const o=()=>{const e=y(n);return e?JSON.parse(e):Object.create(null)},a=e=>{const t=o(),r={...t,...e(t)};return b(n,JSON.stringify(r)),r},s=()=>{window.removeEventListener(\"pageshow\",s),a((e=>({reference:(e.reference||0)+1})))};return e=>{s(),Object.defineProperty(e,\"syncKey\",{value:t,enumerable:!0});let r=!0;setTimeout((()=>{e.on(\"fragment\",(e=>{r&&a((()=>({index:e.index,fragmentIndex:e.fragmentIndex})))}))}),0),window.addEventListener(\"storage\",(t=>{if(t.key===n&&t.oldValue&&t.newValue){const n=JSON.parse(t.oldValue),o=JSON.parse(t.newValue);if(n.index!==o.index||n.fragmentIndex!==o.fragmentIndex)try{r=!1,e.slide(o.index,{fragment:o.fragmentIndex,forSync:!0})}finally{r=!0}}}));const i=()=>{const{reference:e}=o();void 0===e||e<=1?w(n):a((()=>({reference:e-1})))};window.addEventListener(\"pagehide\",(e=>{e.persisted&&window.addEventListener(\"pageshow\",s),i()})),e.on(\"destroy\",i)}},{PI:oe,abs:ae,sqrt:ie,atan2:se}=Math,ce={passive:!0},le=({slope:e=-.7,swipeThreshold:t=30}={})=>n=>{let r;const o=n.parent,a=e=>{const t=o.getBoundingClientRect();return{x:e.pageX-(t.left+t.right)/2,y:e.pageY-(t.top+t.bottom)/2}};o.addEventListener(\"touchstart\",(({touches:e})=>{r=1===e.length?a(e[0]):void 0}),ce),o.addEventListener(\"touchmove\",(e=>{if(r)if(1===e.touches.length){e.preventDefault();const t=a(e.touches[0]),n=t.x-r.x,o=t.y-r.y;r.delta=ie(ae(n)**2+ae(o)**2),r.radian=se(n,o)}else r=void 0})),o.addEventListener(\"touchend\",(o=>{if(r){if(r.delta&&r.delta>=t&&r.radian){const t=(r.radian-e+oe)%(2*oe)-oe;n[t<0?\"next\":\"prev\"](),o.stopPropagation()}r=void 0}}),ce)},de=new Map;de.clear(),de.set(\"none\",{backward:{both:void 0,incoming:void 0,outgoing:void 0},forward:{both:void 0,incoming:void 0,outgoing:void 0}});const ue={both:\"\",outgoing:\"outgoing-\",incoming:\"incoming-\"},fe={forward:\"\",backward:\"-backward\"},me=e=>`--marp-bespoke-transition-animation-${e}`,ge=e=>`--marp-transition-${e}`,pe=me(\"name\"),ve=me(\"duration\"),he=e=>new Promise((t=>{const n={},r=document.createElement(\"div\"),o=e=>{r.remove(),t(e)};r.addEventListener(\"animationstart\",(()=>o(n))),Object.assign(r.style,{animationName:e,animationDuration:\"1s\",animationFillMode:\"both\",animationPlayState:\"paused\",position:\"absolute\",pointerEvents:\"none\"}),document.body.appendChild(r);const a=getComputedStyle(r).getPropertyValue(ge(\"duration\"));a&&Number.parseFloat(a)>=0&&(n.defaultDuration=a),((e,t)=>{requestAnimationFrame((()=>{e.style.animationPlayState=\"running\",requestAnimationFrame((()=>t(void 0)))}))})(r,o)})),ye=async e=>de.has(e)?de.get(e):(e=>{const t={},n=[];for(const[r,o]of Object.entries(ue))for(const[a,i]of Object.entries(fe)){const s=`marp-${o}transition${i}-${e}`;n.push(he(s).then((e=>{t[a]=t[a]||{},t[a][r]=e?{...e,name:s}:void 0})))}return Promise.all(n).then((()=>t))})(e).then((t=>(de.set(e,t),t))),be=e=>Object.values(e).flatMap(Object.values).every((e=>!e)),we=(e,{type:t,backward:n})=>{const r=e[n?\"backward\":\"forward\"],o=(()=>{const e=r[t],n=e=>({[pe]:e.name});if(e)return n(e);if(r.both){const e=n(r.both);return\"incoming\"===t&&(e[me(\"direction\")]=\"reverse\"),e}})();return!o&&n?we(e,{type:t,backward:!1}):o||{[pe]:\"__bespoke_marp_transition_no_animation__\"}},xe=e=>{if(e)try{const t=JSON.parse(e);if((e=>{if(\"object\"!=typeof e)return!1;const t=e;return\"string\"==typeof t.name&&(void 0===t.duration||\"string\"==typeof t.duration)})(t))return t}catch{}},ke=\"_tSId\",$e=\"_tA\",Ee=\"bespoke-marp-transition-warming-up\",Le=window.matchMedia(\"(prefers-reduced-motion: reduce)\"),Se=\"__bespoke_marp_transition_reduced_outgoing__\",Pe=\"__bespoke_marp_transition_reduced_incoming__\",_e={forward:{both:void 0,incoming:{name:Pe},outgoing:{name:Se}},backward:{both:void 0,incoming:{name:Pe},outgoing:{name:Se}}},Te=e=>{if(!document.startViewTransition)return;const t=t=>(void 0!==t&&(e._tD=t),e._tD);let n;t(!1),((...e)=>{CSS.registerProperty({name:ge(\"duration\"),syntax:\"<time>\",inherits:!0,initialValue:\"-1s\"});const t=[...new Set(e).values()];return Promise.all(t.map((e=>ye(e)))).then()})(...Array.from(document.querySelectorAll(\"section[data-transition], section[data-transition-back]\")).flatMap((e=>[e.dataset.transition,e.dataset.transitionBack].flatMap((e=>{const t=xe(e);return[t?.name,t?.builtinFallback?`__builtin__${t.name}`:void 0]})).filter((e=>!!e))))).then((()=>{document.querySelectorAll(\"style\").forEach((e=>{e.innerHTML=e.innerHTML.replace(/--marp-transition-duration:[^;}]*[;}]/g,(e=>e.slice(0,-1)+\"!important\"+e.slice(-1)))}))}));const r=(n,{back:r,cond:o})=>a=>{const i=t();if(i)return!!a[$e]||!(\"object\"!=typeof i||(i.skipTransition(),!a.forSync));if(!o(a))return!0;const s=e.slides[e.slide()],c=()=>a.back??r,l=\"data-transition\"+(c()?\"-back\":\"\"),d=s.querySelector(`section[${l}]`);if(!d)return!0;const u=xe(d.getAttribute(l)??void 0);return!u||((async(e,{builtinFallback:t=!0}={})=>{let n=await ye(e);if(be(n)){if(!t)return;return n=await ye(`__builtin__${e}`),be(n)?void 0:n}return n})(u.name,{builtinFallback:u.builtinFallback}).then((e=>{if(!e){t(!0);try{n(a)}finally{t(!1)}return}let r=e;Le.matches&&(console.warn(\"Use a constant animation to transition because preferring reduced motion by viewer has detected.\"),r=_e);const o=document.getElementById(ke);o&&o.remove();const i=document.createElement(\"style\");i.id=ke,document.head.appendChild(i),((e,t)=>{const n=[`:root{${ge(\"direction\")}:${t.backward?-1:1};}`,\":root:has(.bespoke-marp-inactive){cursor:none;}\"],r=t=>{const n=e[t].both?.defaultDuration||e[t].outgoing?.defaultDuration||e[t].incoming?.defaultDuration;return\"forward\"===t?n:n||r(\"forward\")},o=t.duration||r(t.backward?\"backward\":\"forward\");void 0!==o&&n.push(`::view-transition-group(*){${ve}:${o};}`);const a=e=>Object.entries(e).map((([e,t])=>`${e}:${t};`)).join(\"\");return n.push(`::view-transition-old(root){${a(we(e,{...t,type:\"outgoing\"}))}}`,`::view-transition-new(root){${a(we(e,{...t,type:\"incoming\"}))}}`),n})(r,{backward:c(),duration:u.duration}).forEach((e=>i.sheet?.insertRule(e)));const s=document.documentElement.classList;s.add(Ee);let l=!1;const d=()=>{l||(n(a),l=!0,s.remove(Ee))},f=()=>{t(!1),i.remove(),s.remove(Ee)};try{t(!0);const e=document.startViewTransition(d);t(e),e.finished.finally(f)}catch(e){console.error(e),d(),f()}})),!1)};e.on(\"prev\",r((t=>e.prev({...t,[$e]:!0})),{back:!0,cond:e=>e.index>0&&!((e.fragment??1)&&n.fragmentIndex>0)})),e.on(\"next\",r((t=>e.next({...t,[$e]:!0})),{cond:t=>t.index+1<e.slides.length&&!(n.fragmentIndex+1<n.fragments.length)})),setTimeout((()=>{e.on(\"slide\",r((t=>e.slide(t.index,{...t,[$e]:!0})),{cond:t=>{const n=e.slide();return t.index!==n&&(t.back=t.index<n,!0)}}))}),0),e.on(\"fragment\",(e=>{n=e}))};let Ie;const Me=()=>(void 0===Ie&&(Ie=\"wakeLock\"in navigator&&navigator.wakeLock),Ie),Oe=async()=>{const e=Me();if(e)try{return await e.request(\"screen\")}catch(e){console.warn(e)}return null},Ae=async()=>{if(!Me())return;let e;const t=()=>{e&&\"visible\"===document.visibilityState&&Oe()};for(const e of[\"visibilitychange\",\"fullscreenchange\"])document.addEventListener(e,t);return e=await Oe(),e};((e=document.getElementById(\":$p\"))=>{(()=>{const e=p(\"view\");a.dataset.bespokeView=e===l||e===c?e:\"\"})();const t=(e=>{const t=p(e);return v({[e]:void 0}),t})(\"sync\")||void 0;o.from(e,((...e)=>{const t=d.findIndex((e=>g()===e));return e.map((([e,n])=>e[t]&&n)).filter((e=>e))})([[1,1,0],re({key:t})],[[1,1,1],Z(e)],[[1,1,0],M],[[1,1,1],k],[[1,0,0],T()],[[1,1,1],O],[[1,1,1],ne({history:!1})],[[1,1,0],A()],[[1,1,0],P],[[1,0,0],ee],[[1,1,0],le()],[[1,0,0],C()],[[1,0,0],Te],[[1,1,1],$],[[1,1,0],Ae]))})()}();</script></body></html>", "theme": "default", "slide_count": 1}, "response_time": 6.126237}}}