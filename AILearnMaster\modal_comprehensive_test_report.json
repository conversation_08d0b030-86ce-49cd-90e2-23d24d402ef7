{"timestamp": **********, "duration": 91.93859696388245, "success_rate": 100.0, "gpu_available": true, "gpu_name": "NVIDIA A100 80GB PCIe", "services": {"health": {"status": "success", "data": {"status": "healthy", "timestamp": 1751452688694, "gpu_available": true, "gpu_name": "NVIDIA A100 80GB PCIe", "gpu_info": {"gpu_available": true, "gpu_count": 1, "gpu_name": "NVIDIA A100 80GB PCIe", "gpu_memory_total_gb": 79.25, "gpu_memory_allocated_gb": 0.0, "gpu_memory_reserved_gb": 0.0, "gpu_memory_free_gb": 79.25, "gpu_test_passed": true, "gpu_test_error": null, "cuda_version": "12.1", "pytorch_version": "2.1.0+cu121"}, "services": {"mistral": "ready", "tts": "ready", "voices": "ready", "slides": "ready", "avatar": "ready", "course_avatar": "ready"}}, "gpu_available": true, "gpu_name": "NVIDIA A100 80GB PCIe", "response_time": 11.128085}, "mistral": {"status": "success", "data": {"status": "success", "generated_text": "and 'I'm sorry for the inconvenience'.\n\nI've been working on this for a while", "model": "gpt2", "tokens_generated": 14, "prompt_length": 61, "gpu_used": true, "gpu_name": "NVIDIA A100 80GB PCIe"}, "response_time": 22.629706}, "tts": {"status": "success", "data": {"status": "success", "audio_base64": "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", "voice_id": "tts_models/en/ljspeech/tacotron2-DDC", "text_length": 51, "format": "wav", "method": "coqui_tts", "gpu_used": true, "gpu_name": "NVIDIA A100 80GB PCIe"}, "response_time": 30.815607}, "voices": {"status": "success", "data": {"status": "success", "voices": [], "total_count": 0, "gpu_available": true, "gpu_name": "NVIDIA A100 80GB PCIe", "discovery_time": 1751452757224}, "response_time": 14.692633}, "slides": {"status": "success", "data": {"status": "success", "html_content": "<!DOCTYPE html><html lang=\"C\"><head><title>Test Slide</title><meta property=\"og:title\" content=\"Test Slide\"><meta charset=\"UTF-8\"><meta name=\"viewport\" content=\"width=device-width,height=device-height,initial-scale=1.0\"><meta name=\"apple-mobile-web-app-capable\" content=\"yes\"><meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\"><meta property=\"og:type\" content=\"website\"><meta name=\"twitter:card\" content=\"summary\"><style>@media screen{body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button{appearance:none;background-color:initial;border:0;color:inherit;cursor:pointer;font-size:inherit;opacity:.8;outline:none;padding:0;transition:opacity .2s linear;-webkit-tap-highlight-color:transparent}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button:disabled,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button:disabled,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button:disabled,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button:disabled{cursor:not-allowed;opacity:.15!important}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button:hover,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button:hover,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button:hover,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button:hover{opacity:1}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button:hover:active,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button:hover:active,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button:hover:active,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button:hover:active{opacity:.6}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button:hover:not(:disabled),body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button:hover:not(:disabled),body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button:hover:not(:disabled),body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button:hover:not(:disabled){transition:none}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=prev],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=prev],body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button.bespoke-marp-presenter-info-page-prev{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJNNjggOTAgMjggNTBsNDAtNDAiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=next],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=next],body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button.bespoke-marp-presenter-info-page-next{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJtMzIgOTAgNDAtNDAtNDAtNDAiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=fullscreen],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=fullscreen]{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48c3R5bGU+LmF7ZmlsbDpub25lO3N0cm9rZTojZmZmO3N0cm9rZS1saW5lY2FwOnJvdW5kO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2Utd2lkdGg6NXB4fTwvc3R5bGU+PC9kZWZzPjxyZWN0IHdpZHRoPSI4MCIgaGVpZ2h0PSI2MCIgeD0iMTAiIHk9IjIwIiBjbGFzcz0iYSIgcng9IjUuNjciLz48cGF0aCBkPSJNNDAgNzBIMjBWNTBtMjAgMEwyMCA3MG00MC00MGgyMHYyMG0tMjAgMCAyMC0yMCIgY2xhc3M9ImEiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button.exit[data-bespoke-marp-osc=fullscreen],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button.exit[data-bespoke-marp-osc=fullscreen]{background-image:url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48c3R5bGU+LmF7ZmlsbDpub25lO3N0cm9rZTojZmZmO3N0cm9rZS1saW5lY2FwOnJvdW5kO3N0cm9rZS1saW5lam9pbjpyb3VuZDtzdHJva2Utd2lkdGg6NXB4fTwvc3R5bGU+PC9kZWZzPjxyZWN0IHdpZHRoPSI4MCIgaGVpZ2h0PSI2MCIgeD0iMTAiIHk9IjIwIiBjbGFzcz0iYSIgcng9IjUuNjciLz48cGF0aCBkPSJNMjAgNTBoMjB2MjBtLTIwIDAgMjAtMjBtNDAgMEg2MFYzMG0yMCAwTDYwIDUwIiBjbGFzcz0iYSIvPjwvc3ZnPg==\")}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=presenter],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=presenter]{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJNODcuOCA0Ny41Qzg5IDUwIDg3LjcgNTIgODUgNTJIMzVhOC43IDguNyAwIDAgMS03LjItNC41bC0xNS42LTMxQzExIDE0IDEyLjIgMTIgMTUgMTJoNTBhOC44IDguOCAwIDAgMSA3LjIgNC41ek02MCA1MnYzNm0tMTAgMGgyME00NSA0MmgyMCIvPjwvc3ZnPg==\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button.bespoke-marp-presenter-note-bigger{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJNMTIgNTBoODBNNTIgOTBWMTAiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button.bespoke-marp-presenter-note-smaller{background:#0000 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgc3Ryb2tlLXdpZHRoPSI1IiBkPSJNMTIgNTBoODAiLz48L3N2Zz4=\") no-repeat 50%;background-size:contain;overflow:hidden;text-indent:100%;white-space:nowrap}}@keyframes __bespoke_marp_transition_reduced_outgoing__{0%{opacity:1}to{opacity:0}}@keyframes __bespoke_marp_transition_reduced_incoming__{0%{mix-blend-mode:plus-lighter;opacity:0}to{mix-blend-mode:plus-lighter;opacity:1}}.bespoke-marp-note,.bespoke-marp-osc,.bespoke-progress-parent{display:none;transition:none}@media screen{::view-transition-group(*){animation-duration:var(--marp-bespoke-transition-animation-duration,.5s);animation-timing-function:ease}::view-transition-new(*),::view-transition-old(*){animation-delay:0s;animation-direction:var(--marp-bespoke-transition-animation-direction,normal);animation-duration:var(--marp-bespoke-transition-animation-duration,.5s);animation-fill-mode:both;animation-name:var(--marp-bespoke-transition-animation-name,var(--marp-bespoke-transition-animation-name-fallback,__bespoke_marp_transition_no_animation__));mix-blend-mode:normal}::view-transition-old(*){--marp-bespoke-transition-animation-name-fallback:__bespoke_marp_transition_reduced_outgoing__;animation-timing-function:ease}::view-transition-new(*){--marp-bespoke-transition-animation-name-fallback:__bespoke_marp_transition_reduced_incoming__;animation-timing-function:ease}::view-transition-new(root),::view-transition-old(root){animation-timing-function:linear}::view-transition-new(__bespoke_marp_transition_osc__),::view-transition-old(__bespoke_marp_transition_osc__){animation-duration:0s!important;animation-name:__bespoke_marp_transition_osc__!important}::view-transition-new(__bespoke_marp_transition_osc__){opacity:0!important}.bespoke-marp-transition-warming-up::view-transition-group(*),.bespoke-marp-transition-warming-up::view-transition-new(*),.bespoke-marp-transition-warming-up::view-transition-old(*){animation-play-state:paused!important}body,html{height:100%;margin:0}body{background:#000;overflow:hidden}svg.bespoke-marp-slide{content-visibility:hidden;opacity:0;pointer-events:none;z-index:-1}svg.bespoke-marp-slide:not(.bespoke-marp-active) *{view-transition-name:none!important}svg.bespoke-marp-slide.bespoke-marp-active{content-visibility:visible;opacity:1;pointer-events:auto;z-index:0}svg.bespoke-marp-slide.bespoke-marp-active.bespoke-marp-active-ready *{animation-name:__bespoke_marp__!important}@supports not (content-visibility:hidden){svg.bespoke-marp-slide[data-bespoke-marp-load=hideable]{display:none}svg.bespoke-marp-slide[data-bespoke-marp-load=hideable].bespoke-marp-active{display:block}}}@media screen and (prefers-reduced-motion:reduce){svg.bespoke-marp-slide *{view-transition-name:none!important}}@media screen{[data-bespoke-marp-fragment=inactive]{visibility:hidden}body[data-bespoke-view=\"\"] .bespoke-marp-parent,body[data-bespoke-view=next] .bespoke-marp-parent{inset:0;position:absolute}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc{background:#000000a6;border-radius:7px;bottom:50px;color:#fff;contain:paint;display:block;font-family:Helvetica,Arial,sans-serif;font-size:16px;left:50%;line-height:0;opacity:1;padding:12px;position:absolute;touch-action:manipulation;transform:translateX(-50%);transition:opacity .2s linear;-webkit-user-select:none;user-select:none;white-space:nowrap;will-change:transform;z-index:1;view-transition-name:__bespoke_marp_transition_osc__}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>*,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>*{margin-left:6px}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>:first-child,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>:first-child{margin-left:0}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>span,body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>span{opacity:.8}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>span[data-bespoke-marp-osc=page],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>span[data-bespoke-marp-osc=page]{display:inline-block;min-width:140px;text-align:center}body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=fullscreen],body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=next],body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=presenter],body[data-bespoke-view=\"\"] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=prev],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=fullscreen],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=next],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=presenter],body[data-bespoke-view=next] .bespoke-marp-parent>.bespoke-marp-osc>button[data-bespoke-marp-osc=prev]{height:32px;line-height:32px;width:32px}body[data-bespoke-view=\"\"] .bespoke-marp-parent.bespoke-marp-inactive,body[data-bespoke-view=next] .bespoke-marp-parent.bespoke-marp-inactive{cursor:none}body[data-bespoke-view=\"\"] .bespoke-marp-parent.bespoke-marp-inactive>.bespoke-marp-osc,body[data-bespoke-view=next] .bespoke-marp-parent.bespoke-marp-inactive>.bespoke-marp-osc{opacity:0;pointer-events:none}body[data-bespoke-view=\"\"] svg.bespoke-marp-slide,body[data-bespoke-view=next] svg.bespoke-marp-slide{height:100%;left:0;position:absolute;top:0;width:100%}body[data-bespoke-view=\"\"] .bespoke-progress-parent{background:#222;display:flex;height:5px;width:100%}body[data-bespoke-view=\"\"] .bespoke-progress-parent+.bespoke-marp-parent{top:5px}body[data-bespoke-view=\"\"] .bespoke-progress-parent .bespoke-progress-bar{background:#0288d1;flex:0 0 0;transition:flex-basis .2s cubic-bezier(0,1,1,1)}body[data-bespoke-view=next]{background:#0000}body[data-bespoke-view=presenter]{background:#161616}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container{display:grid;font-family:Helvetica,Arial,sans-serif;grid-template:\"current dragbar next\" minmax(140px,1fr) \"current dragbar note\" 2fr \"info    dragbar note\" 3em;grid-template-columns:minmax(3px,var(--bespoke-marp-presenter-split-ratio,66%)) 0 minmax(3px,1fr);height:100%;left:0;position:absolute;top:0;width:100%}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-parent{grid-area:current;overflow:hidden;position:relative}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-parent svg.bespoke-marp-slide{height:calc(100% - 40px);left:20px;pointer-events:none;position:absolute;top:20px;-webkit-user-select:none;user-select:none;width:calc(100% - 40px)}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-parent svg.bespoke-marp-slide.bespoke-marp-active{filter:drop-shadow(0 3px 10px rgba(0,0,0,.5))}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-dragbar-container{background:#0288d1;cursor:col-resize;grid-area:dragbar;margin-left:-3px;opacity:0;position:relative;transition:opacity .4s linear .1s;width:6px;z-index:10}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-dragbar-container:hover{opacity:1}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-dragbar-container.active{opacity:1;transition-delay:0s}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-next-container{background:#222;cursor:pointer;display:none;grid-area:next;overflow:hidden;position:relative}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-next-container.active{display:block}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-next-container iframe.bespoke-marp-presenter-next{background:#0000;border:0;display:block;filter:drop-shadow(0 3px 10px rgba(0,0,0,.5));height:calc(100% - 40px);left:20px;pointer-events:none;position:absolute;top:20px;-webkit-user-select:none;user-select:none;width:calc(100% - 40px)}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container{background:#222;color:#eee;grid-area:note;position:relative;z-index:1}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container button{height:1.5em;line-height:1.5em;width:1.5em}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-presenter-note-wrapper{display:block;inset:0;position:absolute}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-presenter-note-buttons{background:#000000a6;border-radius:4px;bottom:0;display:flex;gap:4px;margin:12px;opacity:0;padding:6px;pointer-events:none;position:absolute;right:0;transition:opacity .2s linear}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-presenter-note-buttons:focus-within,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-presenter-note-wrapper:focus-within+.bespoke-marp-presenter-note-buttons,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container:hover .bespoke-marp-presenter-note-buttons{opacity:1;pointer-events:auto}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note{box-sizing:border-box;font-size:calc(1.1em*var(--bespoke-marp-note-font-scale, 1));height:calc(100% - 40px);margin:20px;overflow:auto;padding-right:3px;white-space:pre-wrap;width:calc(100% - 40px);word-wrap:break-word;scrollbar-color:#eeeeee80 #0000;scrollbar-width:thin}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note::-webkit-scrollbar{width:6px}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note::-webkit-scrollbar-track{background:#0000}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note::-webkit-scrollbar-thumb{background:#eeeeee80;border-radius:6px}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note:empty{pointer-events:none}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note.active{display:block}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note p:first-child{margin-top:0}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-note-container .bespoke-marp-note p:last-child{margin-bottom:0}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container{align-items:center;box-sizing:border-box;color:#eee;display:flex;flex-wrap:nowrap;grid-area:info;justify-content:center;overflow:hidden;padding:0 10px}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-page,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-time,body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-timer{box-sizing:border-box;display:block;padding:0 10px;white-space:nowrap;width:100%}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container button{height:1.5em;line-height:1.5em;width:1.5em}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-page{order:2;text-align:center}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-page .bespoke-marp-presenter-info-page-text{display:inline-block;min-width:120px;text-align:center}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-time{color:#999;order:1;text-align:left}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-timer{color:#999;order:3;text-align:right}body[data-bespoke-view=presenter] .bespoke-marp-presenter-container .bespoke-marp-presenter-info-container .bespoke-marp-presenter-info-timer:hover{cursor:pointer}}@media print{.bespoke-marp-presenter-info-container,.bespoke-marp-presenter-next-container,.bespoke-marp-presenter-note-container{display:none}}</style><style>div#\\:\\$p > svg > foreignObject > section{width:1280px;height:720px;box-sizing:border-box;overflow:hidden;position:relative;scroll-snap-align:center center;-webkit-text-size-adjust:100%;text-size-adjust:100%}div#\\:\\$p > svg > foreignObject > section::after{bottom:0;content:attr(data-marpit-pagination);padding:inherit;pointer-events:none;position:absolute;right:0}div#\\:\\$p > svg > foreignObject > section:not([data-marpit-pagination])::after{display:none}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1){font-size:2em;margin-block:0.67em}div#\\:\\$p > svg > foreignObject > section video::-webkit-media-controls{will-change:transform}@page {size:1280px 720px;margin:0}@media print{html, body{background-color:#fff;margin:0;page-break-inside:avoid;break-inside:avoid-page}div#\\:\\$p > svg > foreignObject > section{page-break-before:always;break-before:page}div#\\:\\$p > svg > foreignObject > section, div#\\:\\$p > svg > foreignObject > section *{-webkit-print-color-adjust:exact!important;animation-delay:0s!important;animation-duration:0s!important;color-adjust:exact!important;print-color-adjust:exact!important;transition:none!important}div#\\:\\$p > svg[data-marpit-svg]{display:block;height:100vh;width:100vw}}div#\\:\\$p > svg > foreignObject > :where(section){container-type:size}div#\\:\\$p > svg > foreignObject > section img[data-marp-twemoji]{background:transparent;height:1em;margin:0 .05em 0 .1em;vertical-align:-.1em;width:1em}/*!\n * Marp default theme.\n *\n * @theme default\n * <AUTHOR> Hattori\n *\n * @auto-scaling true\n * @size 16:9 1280px 720px\n * @size 4:3 960px 720px\n */div#\\:\\$p > svg > foreignObject > section{--base-size-4:calc(var(--marpit-root-font-size, 1rem) * 0.25);--base-size-8:calc(var(--marpit-root-font-size, 1rem) * 0.5);--base-size-16:calc(var(--marpit-root-font-size, 1rem) * 1);--base-size-24:calc(var(--marpit-root-font-size, 1rem) * 1.5);--base-size-40:calc(var(--marpit-root-font-size, 1rem) * 2.5);--base-text-weight-normal:400;--base-text-weight-medium:500;--base-text-weight-semibold:600;--fontStack-monospace:ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;--fgColor-accent:Highlight;}div#\\:\\$p > svg > foreignObject > section [data-theme=light],div#\\:\\$p > svg > foreignObject > section{color-scheme:light;--focus-outlineColor:#0969da;--fgColor-default:#1f2328;--fgColor-muted:#59636e;--fgColor-accent:#0969da;--fgColor-success:#1a7f37;--fgColor-attention:#9a6700;--fgColor-danger:#d1242f;--fgColor-done:#8250df;--bgColor-default:#fff;--bgColor-muted:#f6f8fa;--bgColor-neutral-muted:#818b981f;--bgColor-attention-muted:#fff8c5;--borderColor-default:#d1d9e0;--borderColor-muted:#d1d9e0b3;--borderColor-neutral-muted:#d1d9e0b3;--borderColor-accent-emphasis:#0969da;--borderColor-success-emphasis:#1a7f37;--borderColor-attention-emphasis:#9a6700;--borderColor-danger-emphasis:#cf222e;--borderColor-done-emphasis:#8250df;--color-prettylights-syntax-comment:#59636e;--color-prettylights-syntax-constant:#0550ae;--color-prettylights-syntax-constant-other-reference-link:#0a3069;--color-prettylights-syntax-entity:#6639ba;--color-prettylights-syntax-storage-modifier-import:#1f2328;--color-prettylights-syntax-entity-tag:#0550ae;--color-prettylights-syntax-keyword:#cf222e;--color-prettylights-syntax-string:#0a3069;--color-prettylights-syntax-variable:#953800;--color-prettylights-syntax-brackethighlighter-unmatched:#82071e;--color-prettylights-syntax-brackethighlighter-angle:#59636e;--color-prettylights-syntax-invalid-illegal-text:#f6f8fa;--color-prettylights-syntax-invalid-illegal-bg:#82071e;--color-prettylights-syntax-carriage-return-text:#f6f8fa;--color-prettylights-syntax-carriage-return-bg:#cf222e;--color-prettylights-syntax-string-regexp:#116329;--color-prettylights-syntax-markup-list:#3b2300;--color-prettylights-syntax-markup-heading:#0550ae;--color-prettylights-syntax-markup-italic:#1f2328;--color-prettylights-syntax-markup-bold:#1f2328;--color-prettylights-syntax-markup-deleted-text:#82071e;--color-prettylights-syntax-markup-deleted-bg:#ffebe9;--color-prettylights-syntax-markup-inserted-text:#116329;--color-prettylights-syntax-markup-inserted-bg:#dafbe1;--color-prettylights-syntax-markup-changed-text:#953800;--color-prettylights-syntax-markup-changed-bg:#ffd8b5;--color-prettylights-syntax-markup-ignored-text:#d1d9e0;--color-prettylights-syntax-markup-ignored-bg:#0550ae;--color-prettylights-syntax-meta-diff-range:#8250df;--color-prettylights-syntax-sublimelinter-gutter-mark:#818b98;}div#\\:\\$p > svg > foreignObject > section [data-theme=dark],div#\\:\\$p > svg > foreignObject > section:where(.invert){color-scheme:dark;--focus-outlineColor:#1f6feb;--fgColor-default:#f0f6fc;--fgColor-muted:#9198a1;--fgColor-accent:#4493f8;--fgColor-success:#3fb950;--fgColor-attention:#d29922;--fgColor-danger:#f85149;--fgColor-done:#ab7df8;--bgColor-default:#0d1117;--bgColor-muted:#151b23;--bgColor-neutral-muted:#656c7633;--bgColor-attention-muted:#bb800926;--borderColor-default:#3d444d;--borderColor-muted:#3d444db3;--borderColor-neutral-muted:#3d444db3;--borderColor-accent-emphasis:#1f6feb;--borderColor-success-emphasis:#238636;--borderColor-attention-emphasis:#9e6a03;--borderColor-danger-emphasis:#da3633;--borderColor-done-emphasis:#8957e5;--color-prettylights-syntax-comment:#9198a1;--color-prettylights-syntax-constant:#79c0ff;--color-prettylights-syntax-constant-other-reference-link:#a5d6ff;--color-prettylights-syntax-entity:#d2a8ff;--color-prettylights-syntax-storage-modifier-import:#f0f6fc;--color-prettylights-syntax-entity-tag:#7ee787;--color-prettylights-syntax-keyword:#ff7b72;--color-prettylights-syntax-string:#a5d6ff;--color-prettylights-syntax-variable:#ffa657;--color-prettylights-syntax-brackethighlighter-unmatched:#f85149;--color-prettylights-syntax-brackethighlighter-angle:#9198a1;--color-prettylights-syntax-invalid-illegal-text:#f0f6fc;--color-prettylights-syntax-invalid-illegal-bg:#8e1519;--color-prettylights-syntax-carriage-return-text:#f0f6fc;--color-prettylights-syntax-carriage-return-bg:#b62324;--color-prettylights-syntax-string-regexp:#7ee787;--color-prettylights-syntax-markup-list:#f2cc60;--color-prettylights-syntax-markup-heading:#1f6feb;--color-prettylights-syntax-markup-italic:#f0f6fc;--color-prettylights-syntax-markup-bold:#f0f6fc;--color-prettylights-syntax-markup-deleted-text:#ffdcd7;--color-prettylights-syntax-markup-deleted-bg:#67060c;--color-prettylights-syntax-markup-inserted-text:#aff5b4;--color-prettylights-syntax-markup-inserted-bg:#033a16;--color-prettylights-syntax-markup-changed-text:#ffdfb6;--color-prettylights-syntax-markup-changed-bg:#5a1e02;--color-prettylights-syntax-markup-ignored-text:#f0f6fc;--color-prettylights-syntax-markup-ignored-bg:#1158c7;--color-prettylights-syntax-meta-diff-range:#d2a8ff;--color-prettylights-syntax-sublimelinter-gutter-mark:#3d444d;}div#\\:\\$p > svg > foreignObject > section{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;background-color:var(--bgColor-default);color:var(--fgColor-default);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Noto Sans,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji;font-size:16px;line-height:1.5;margin:0;word-wrap:break-word}div#\\:\\$p > svg > foreignObject > section{--marpit-root-font-size:16px;}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5):hover .anchor .octicon-link:before,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6):hover .anchor .octicon-link:before{background-color:currentColor;content:\" \";display:inline-block;height:16px;-webkit-mask-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\" viewBox=\"0 0 16 16\"><path fill-rule=\"evenodd\" d=\"M7.775 3.275a.75.75 0 0 0 1.06 1.06l1.25-1.25a2 2 0 1 1 2.83 2.83l-2.5 2.5a2 2 0 0 1-2.83 0 .75.75 0 0 0-1.06 1.06 3.5 3.5 0 0 0 4.95 0l2.5-2.5a3.5 3.5 0 0 0-4.95-4.95zm-4.69 9.64a2 2 0 0 1 0-2.83l2.5-2.5a2 2 0 0 1 2.83 0 .75.75 0 0 0 1.06-1.06 3.5 3.5 0 0 0-4.95 0l-2.5 2.5a3.5 3.5 0 0 0 4.95 4.95l1.25-1.25a.75.75 0 0 0-1.06-1.06l-1.25 1.25a2 2 0 0 1-2.83 0\"/></svg>');mask-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" aria-hidden=\"true\" viewBox=\"0 0 16 16\"><path fill-rule=\"evenodd\" d=\"M7.775 3.275a.75.75 0 0 0 1.06 1.06l1.25-1.25a2 2 0 1 1 2.83 2.83l-2.5 2.5a2 2 0 0 1-2.83 0 .75.75 0 0 0-1.06 1.06 3.5 3.5 0 0 0 4.95 0l2.5-2.5a3.5 3.5 0 0 0-4.95-4.95zm-4.69 9.64a2 2 0 0 1 0-2.83l2.5-2.5a2 2 0 0 1 2.83 0 .75.75 0 0 0 1.06-1.06 3.5 3.5 0 0 0-4.95 0l-2.5 2.5a3.5 3.5 0 0 0 4.95 4.95l1.25-1.25a.75.75 0 0 0-1.06-1.06l-1.25 1.25a2 2 0 0 1-2.83 0\"/></svg>');width:16px}div#\\:\\$p > svg > foreignObject > section details,div#\\:\\$p > svg > foreignObject > section figcaption,div#\\:\\$p > svg > foreignObject > section figure{display:block}div#\\:\\$p > svg > foreignObject > section summary{display:list-item}div#\\:\\$p > svg > foreignObject > section [hidden]{display:none!important}div#\\:\\$p > svg > foreignObject > section a{background-color:transparent;color:var(--fgColor-accent);text-decoration:none}div#\\:\\$p > svg > foreignObject > section abbr[title]{border-bottom:none;-webkit-text-decoration:underline dotted;text-decoration:underline dotted}div#\\:\\$p > svg > foreignObject > section b,div#\\:\\$p > svg > foreignObject > section strong{font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section dfn{font-style:italic}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1){border-bottom:1px solid var(--borderColor-muted);font-size:2em;font-weight:var(--base-text-weight-semibold, 600);margin:.67em 0;padding-bottom:.3em}div#\\:\\$p > svg > foreignObject > section mark{background-color:var(--bgColor-attention-muted);color:var(--fgColor-default)}div#\\:\\$p > svg > foreignObject > section small{font-size:90%}div#\\:\\$p > svg > foreignObject > section sub,div#\\:\\$p > svg > foreignObject > section sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}div#\\:\\$p > svg > foreignObject > section sub{bottom:-.25em}div#\\:\\$p > svg > foreignObject > section sup{top:-.5em}div#\\:\\$p > svg > foreignObject > section img{border-style:none;box-sizing:content-box;max-width:100%}div#\\:\\$p > svg > foreignObject > section code,div#\\:\\$p > svg > foreignObject > section kbd,div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre),div#\\:\\$p > svg > foreignObject > section samp{font-family:monospace;font-size:1em}div#\\:\\$p > svg > foreignObject > section figure{margin:1em var(--base-size-40)}div#\\:\\$p > svg > foreignObject > section hr{background:transparent;background-color:var(--borderColor-default);border:0;box-sizing:content-box;height:.25em;margin:var(--base-size-24) 0;overflow:hidden;padding:0}div#\\:\\$p > svg > foreignObject > section input{font:inherit;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible}div#\\:\\$p > svg > foreignObject > section [type=button],div#\\:\\$p > svg > foreignObject > section [type=reset],div#\\:\\$p > svg > foreignObject > section [type=submit]{-webkit-appearance:button;-moz-appearance:button;appearance:button}div#\\:\\$p > svg > foreignObject > section [type=checkbox],div#\\:\\$p > svg > foreignObject > section [type=radio]{box-sizing:border-box;padding:0}div#\\:\\$p > svg > foreignObject > section [type=number]::-webkit-inner-spin-button,div#\\:\\$p > svg > foreignObject > section [type=number]::-webkit-outer-spin-button{height:auto}div#\\:\\$p > svg > foreignObject > section [type=search]::-webkit-search-cancel-button,div#\\:\\$p > svg > foreignObject > section [type=search]::-webkit-search-decoration{-webkit-appearance:none;appearance:none}div#\\:\\$p > svg > foreignObject > section ::-webkit-input-placeholder{color:inherit;opacity:.54}div#\\:\\$p > svg > foreignObject > section ::-webkit-file-upload-button{-webkit-appearance:button;appearance:button;font:inherit}div#\\:\\$p > svg > foreignObject > section a:hover{text-decoration:underline}div#\\:\\$p > svg > foreignObject > section ::-moz-placeholder{color:var(--fgColor-muted);opacity:1}div#\\:\\$p > svg > foreignObject > section ::placeholder{color:var(--fgColor-muted);opacity:1}div#\\:\\$p > svg > foreignObject > section hr:after,div#\\:\\$p > svg > foreignObject > section hr:before{content:\"\";display:table}div#\\:\\$p > svg > foreignObject > section hr:after{clear:both}div#\\:\\$p > svg > foreignObject > section table{border-collapse:collapse;border-spacing:0;display:block;font-variant:tabular-nums;max-width:100%;overflow:auto;width:-moz-max-content;width:max-content}div#\\:\\$p > svg > foreignObject > section td,div#\\:\\$p > svg > foreignObject > section th{padding:0}div#\\:\\$p > svg > foreignObject > section details summary{cursor:pointer}div#\\:\\$p > svg > foreignObject > section [role=button]:focus,div#\\:\\$p > svg > foreignObject > section a:focus,div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus,div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus{box-shadow:none;outline:2px solid var(--focus-outlineColor);outline-offset:-2px}div#\\:\\$p > svg > foreignObject > section [role=button]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section a:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus:not(:focus-visible){outline:1px solid transparent}div#\\:\\$p > svg > foreignObject > section [role=button]:focus-visible,div#\\:\\$p > svg > foreignObject > section a:focus-visible,div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus-visible,div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus-visible{box-shadow:none;outline:2px solid var(--focus-outlineColor);outline-offset:-2px}div#\\:\\$p > svg > foreignObject > section a:not([class]):focus,div#\\:\\$p > svg > foreignObject > section a:not([class]):focus-visible,div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus,div#\\:\\$p > svg > foreignObject > section input[type=checkbox]:focus-visible,div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus,div#\\:\\$p > svg > foreignObject > section input[type=radio]:focus-visible{outline-offset:0}div#\\:\\$p > svg > foreignObject > section kbd{background-color:var(--bgColor-muted);border-bottom-color:var(--borderColor-neutral-muted);border:1px solid var(--borderColor-neutral-muted);border-radius:6px;box-shadow:inset 0 -1px 0 var(--borderColor-neutral-muted);color:var(--fgColor-default);display:inline-block;font:11px var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);line-height:10px;padding:var(--base-size-4);vertical-align:middle}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2),div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3),div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4),div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5),div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6){font-weight:var(--base-text-weight-semibold, 600);line-height:1.25;margin-bottom:var(--base-size-16);margin-top:var(--base-size-24)}div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2){border-bottom:1px solid var(--borderColor-muted);font-size:1.5em;padding-bottom:.3em}div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2),div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3){font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3){font-size:1.25em}div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4){font-size:1em}div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4),div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5){font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5){font-size:.875em}div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6){color:var(--fgColor-muted);font-size:.85em;font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section p{margin-bottom:10px;margin-top:0}div#\\:\\$p > svg > foreignObject > section blockquote{border-left:.25em solid var(--borderColor-default);color:var(--fgColor-muted);margin:0;padding:0 1em}div#\\:\\$p > svg > foreignObject > section ol,div#\\:\\$p > svg > foreignObject > section ul{margin-bottom:0;margin-top:0;padding-left:2em}div#\\:\\$p > svg > foreignObject > section ol ol,div#\\:\\$p > svg > foreignObject > section ul ol{list-style-type:lower-roman}div#\\:\\$p > svg > foreignObject > section ol ol ol,div#\\:\\$p > svg > foreignObject > section ol ul ol,div#\\:\\$p > svg > foreignObject > section ul ol ol,div#\\:\\$p > svg > foreignObject > section ul ul ol{list-style-type:lower-alpha}div#\\:\\$p > svg > foreignObject > section dd{margin-left:0}div#\\:\\$p > svg > foreignObject > section code,div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre),div#\\:\\$p > svg > foreignObject > section samp,div#\\:\\$p > svg > foreignObject > section tt{font-family:var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);font-size:12px}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre){margin-bottom:0;margin-top:0;word-wrap:normal}div#\\:\\$p > svg > foreignObject > section .octicon{display:inline-block;overflow:visible!important;vertical-align:text-bottom;fill:currentColor}div#\\:\\$p > svg > foreignObject > section input::-webkit-inner-spin-button,div#\\:\\$p > svg > foreignObject > section input::-webkit-outer-spin-button{-webkit-appearance:none;appearance:none;margin:0}div#\\:\\$p > svg > foreignObject > section .mr-2{margin-right:var(--base-size-8, 8px)!important}div#\\:\\$p > svg > foreignObject > section:after,div#\\:\\$p > svg > foreignObject > section:before{display:table}div#\\:\\$p > svg > foreignObject > section:after{clear:both}div#\\:\\$p > svg > foreignObject > section>:first-child{margin-top:0!important}div#\\:\\$p > svg > foreignObject > section>:last-child{margin-bottom:0!important}div#\\:\\$p > svg > foreignObject > section a:not([href]){color:inherit;text-decoration:none}div#\\:\\$p > svg > foreignObject > section .absent{color:var(--fgColor-danger)}div#\\:\\$p > svg > foreignObject > section .anchor{float:left;line-height:1;margin-left:-20px;padding-right:var(--base-size-4)}div#\\:\\$p > svg > foreignObject > section .anchor:focus{outline:none}div#\\:\\$p > svg > foreignObject > section blockquote,div#\\:\\$p > svg > foreignObject > section details,div#\\:\\$p > svg > foreignObject > section dl,div#\\:\\$p > svg > foreignObject > section ol,div#\\:\\$p > svg > foreignObject > section p,div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre),div#\\:\\$p > svg > foreignObject > section table,div#\\:\\$p > svg > foreignObject > section ul{margin-bottom:var(--base-size-16);margin-top:0}div#\\:\\$p > svg > foreignObject > section blockquote>:first-child{margin-top:0}div#\\:\\$p > svg > foreignObject > section blockquote>:last-child{margin-bottom:0}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5) .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6) .octicon-link{color:var(--fgColor-default);vertical-align:middle;visibility:hidden}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5):hover .anchor,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6):hover .anchor{text-decoration:none}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5):hover .anchor .octicon-link,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6):hover .anchor .octicon-link{visibility:visible}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1) code,div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1) tt,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2) code,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2) tt,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3) code,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3) tt,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4) code,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4) tt,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5) code,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5) tt,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6) code,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6) tt{font-size:inherit;padding:0 .2em}div#\\:\\$p > svg > foreignObject > section summary :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section summary :is(h2, marp-h2),div#\\:\\$p > svg > foreignObject > section summary :is(h3, marp-h3),div#\\:\\$p > svg > foreignObject > section summary :is(h4, marp-h4),div#\\:\\$p > svg > foreignObject > section summary :is(h5, marp-h5),div#\\:\\$p > svg > foreignObject > section summary :is(h6, marp-h6){display:inline-block}div#\\:\\$p > svg > foreignObject > section summary :is(h1, marp-h1) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h2, marp-h2) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h3, marp-h3) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h4, marp-h4) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h5, marp-h5) .anchor,div#\\:\\$p > svg > foreignObject > section summary :is(h6, marp-h6) .anchor{margin-left:-40px}div#\\:\\$p > svg > foreignObject > section summary :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section summary :is(h2, marp-h2){border-bottom:0;padding-bottom:0}div#\\:\\$p > svg > foreignObject > section ol.no-list,div#\\:\\$p > svg > foreignObject > section ul.no-list{list-style-type:none;padding:0}div#\\:\\$p > svg > foreignObject > section ol[type=\"a s\"]{list-style-type:lower-alpha}div#\\:\\$p > svg > foreignObject > section ol[type=\"A s\"]{list-style-type:upper-alpha}div#\\:\\$p > svg > foreignObject > section ol[type=\"i s\"]{list-style-type:lower-roman}div#\\:\\$p > svg > foreignObject > section ol[type=\"I s\"]{list-style-type:upper-roman}div#\\:\\$p > svg > foreignObject > section div>ol:not([type]),div#\\:\\$p > svg > foreignObject > section ol[type=\"1\"]{list-style-type:decimal}div#\\:\\$p > svg > foreignObject > section ol ol,div#\\:\\$p > svg > foreignObject > section ol ul,div#\\:\\$p > svg > foreignObject > section ul ol,div#\\:\\$p > svg > foreignObject > section ul ul{margin-bottom:0;margin-top:0}div#\\:\\$p > svg > foreignObject > section li>p{margin-top:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section li+li{margin-top:.25em}div#\\:\\$p > svg > foreignObject > section dl{padding:0}div#\\:\\$p > svg > foreignObject > section dl dt{font-size:1em;font-style:italic;font-weight:var(--base-text-weight-semibold, 600);margin-top:var(--base-size-16);padding:0}div#\\:\\$p > svg > foreignObject > section dl dd{margin-bottom:var(--base-size-16);padding:0 var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section table th{font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section table td,div#\\:\\$p > svg > foreignObject > section table th{border:1px solid var(--borderColor-default);padding:6px 13px}div#\\:\\$p > svg > foreignObject > section table td>:last-child{margin-bottom:0}div#\\:\\$p > svg > foreignObject > section table tr{background-color:var(--bgColor-default);border-top:1px solid var(--borderColor-muted)}div#\\:\\$p > svg > foreignObject > section table tr:nth-child(2n){background-color:var(--bgColor-muted)}div#\\:\\$p > svg > foreignObject > section table img{background-color:transparent}div#\\:\\$p > svg > foreignObject > section img[align=right]{padding-left:20px}div#\\:\\$p > svg > foreignObject > section img[align=left]{padding-right:20px}div#\\:\\$p > svg > foreignObject > section .emoji{background-color:transparent;max-width:none;vertical-align:text-top}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame,div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame>:is(span, marp-span){display:block;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame>:is(span, marp-span){border:1px solid var(--borderColor-default);float:left;margin:13px 0 0;padding:7px;width:auto}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame :is(span, marp-span) img{display:block;float:left}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).frame :is(span, marp-span) :is(span, marp-span){clear:both;color:var(--fgColor-default);display:block;padding:5px 0 0}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-center{clear:both;display:block;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-center>:is(span, marp-span){display:block;margin:13px auto 0;overflow:hidden;text-align:center}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-center :is(span, marp-span) img{margin:0 auto;text-align:center}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-right{clear:both;display:block;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-right>:is(span, marp-span){display:block;margin:13px 0 0;overflow:hidden;text-align:right}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).align-right :is(span, marp-span) img{margin:0;text-align:right}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).float-left{display:block;float:left;margin-right:13px;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).float-left :is(span, marp-span){margin:13px 0 0}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).float-right{display:block;float:right;margin-left:13px;overflow:hidden}div#\\:\\$p > svg > foreignObject > section :is(span, marp-span).float-right>:is(span, marp-span){display:block;margin:13px auto 0;overflow:hidden;text-align:right}div#\\:\\$p > svg > foreignObject > section code,div#\\:\\$p > svg > foreignObject > section tt{background-color:var(--bgColor-neutral-muted);border-radius:6px;font-size:85%;margin:0;padding:.2em .4em;white-space:break-spaces}div#\\:\\$p > svg > foreignObject > section code br,div#\\:\\$p > svg > foreignObject > section tt br{display:none}div#\\:\\$p > svg > foreignObject > section del code{text-decoration:inherit}div#\\:\\$p > svg > foreignObject > section samp{font-size:85%}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) code{font-size:100%}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre)>code{background:transparent;border:0;margin:0;padding:0;white-space:pre;word-break:normal}div#\\:\\$p > svg > foreignObject > section .highlight{margin-bottom:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section .highlight :is(pre, marp-pre){margin-bottom:0;word-break:normal}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre){background-color:var(--bgColor-muted);border-radius:6px;color:var(--fgColor-default);font-size:85%;line-height:1.45;overflow:auto;padding:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) code,div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) tt{display:inline;line-height:inherit;margin:0;max-width:auto;overflow:visible;padding:0;word-wrap:normal;background-color:transparent;border:0}div#\\:\\$p > svg > foreignObject > section .csv-data td,div#\\:\\$p > svg > foreignObject > section .csv-data th{font-size:12px;line-height:1;overflow:hidden;padding:5px;text-align:left;white-space:nowrap}div#\\:\\$p > svg > foreignObject > section .csv-data .blob-num{background:var(--bgColor-default);border:0;padding:10px var(--base-size-8) 9px;text-align:right}div#\\:\\$p > svg > foreignObject > section .csv-data tr{border-top:0}div#\\:\\$p > svg > foreignObject > section .csv-data th{background:var(--bgColor-muted);border-top:0;font-weight:var(--base-text-weight-semibold, 600)}div#\\:\\$p > svg > foreignObject > section [data-footnote-ref]:before{content:\"[\"}div#\\:\\$p > svg > foreignObject > section [data-footnote-ref]:after{content:\"]\"}div#\\:\\$p > svg > foreignObject > section .footnotes{border-top:1px solid var(--borderColor-default);color:var(--fgColor-muted);font-size:12px}div#\\:\\$p > svg > foreignObject > section div#\\:\\$p > svg > foreignObject > section section.footnotes{--marpit-root-font-size:12px;}div#\\:\\$p > svg > foreignObject > section .footnotes ol,div#\\:\\$p > svg > foreignObject > section .footnotes ol ul{padding-left:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section .footnotes ol ul{display:inline-block;margin-top:var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section .footnotes li{position:relative}div#\\:\\$p > svg > foreignObject > section .footnotes li:target:before{border:2px solid var(--borderColor-accent-emphasis);border-radius:6px;bottom:calc(var(--base-size-8)*-1);content:\"\";left:calc(var(--base-size-24)*-1);pointer-events:none;position:absolute;right:calc(var(--base-size-8)*-1);top:calc(var(--base-size-8)*-1)}div#\\:\\$p > svg > foreignObject > section .footnotes li:target{color:var(--fgColor-default)}div#\\:\\$p > svg > foreignObject > section .footnotes .data-footnote-backref g-emoji{font-family:monospace}div#\\:\\$p > svg > foreignObject > section body:has(:modal){padding-right:var(--dialog-scrollgutter)!important}div#\\:\\$p > svg > foreignObject > section .pl-c{color:var(--color-prettylights-syntax-comment)}div#\\:\\$p > svg > foreignObject > section .pl-c1,div#\\:\\$p > svg > foreignObject > section .pl-s .pl-v{color:var(--color-prettylights-syntax-constant)}div#\\:\\$p > svg > foreignObject > section .pl-e,div#\\:\\$p > svg > foreignObject > section .pl-en{color:var(--color-prettylights-syntax-entity)}div#\\:\\$p > svg > foreignObject > section .pl-s .pl-s1,div#\\:\\$p > svg > foreignObject > section .pl-smi{color:var(--color-prettylights-syntax-storage-modifier-import)}div#\\:\\$p > svg > foreignObject > section .pl-ent{color:var(--color-prettylights-syntax-entity-tag)}div#\\:\\$p > svg > foreignObject > section .pl-k{color:var(--color-prettylights-syntax-keyword)}div#\\:\\$p > svg > foreignObject > section .pl-pds,div#\\:\\$p > svg > foreignObject > section .pl-s,div#\\:\\$p > svg > foreignObject > section .pl-s .pl-pse .pl-s1,div#\\:\\$p > svg > foreignObject > section .pl-sr,div#\\:\\$p > svg > foreignObject > section .pl-sr .pl-cce,div#\\:\\$p > svg > foreignObject > section .pl-sr .pl-sra,div#\\:\\$p > svg > foreignObject > section .pl-sr .pl-sre{color:var(--color-prettylights-syntax-string)}div#\\:\\$p > svg > foreignObject > section .pl-smw,div#\\:\\$p > svg > foreignObject > section .pl-v{color:var(--color-prettylights-syntax-variable)}div#\\:\\$p > svg > foreignObject > section .pl-bu{color:var(--color-prettylights-syntax-brackethighlighter-unmatched)}div#\\:\\$p > svg > foreignObject > section .pl-ii{background-color:var(--color-prettylights-syntax-invalid-illegal-bg);color:var(--color-prettylights-syntax-invalid-illegal-text)}div#\\:\\$p > svg > foreignObject > section .pl-c2{background-color:var(--color-prettylights-syntax-carriage-return-bg);color:var(--color-prettylights-syntax-carriage-return-text)}div#\\:\\$p > svg > foreignObject > section .pl-sr .pl-cce{color:var(--color-prettylights-syntax-string-regexp);font-weight:700}div#\\:\\$p > svg > foreignObject > section .pl-ml{color:var(--color-prettylights-syntax-markup-list)}div#\\:\\$p > svg > foreignObject > section .pl-mh,div#\\:\\$p > svg > foreignObject > section .pl-mh .pl-en,div#\\:\\$p > svg > foreignObject > section .pl-ms{color:var(--color-prettylights-syntax-markup-heading);font-weight:700}div#\\:\\$p > svg > foreignObject > section .pl-mi{color:var(--color-prettylights-syntax-markup-italic);font-style:italic}div#\\:\\$p > svg > foreignObject > section .pl-mb{color:var(--color-prettylights-syntax-markup-bold);font-weight:700}div#\\:\\$p > svg > foreignObject > section .pl-md{background-color:var(--color-prettylights-syntax-markup-deleted-bg);color:var(--color-prettylights-syntax-markup-deleted-text)}div#\\:\\$p > svg > foreignObject > section .pl-mi1{background-color:var(--color-prettylights-syntax-markup-inserted-bg);color:var(--color-prettylights-syntax-markup-inserted-text)}div#\\:\\$p > svg > foreignObject > section .pl-mc{background-color:var(--color-prettylights-syntax-markup-changed-bg);color:var(--color-prettylights-syntax-markup-changed-text)}div#\\:\\$p > svg > foreignObject > section .pl-mi2{background-color:var(--color-prettylights-syntax-markup-ignored-bg);color:var(--color-prettylights-syntax-markup-ignored-text)}div#\\:\\$p > svg > foreignObject > section .pl-mdr{color:var(--color-prettylights-syntax-meta-diff-range);font-weight:700}div#\\:\\$p > svg > foreignObject > section .pl-ba{color:var(--color-prettylights-syntax-brackethighlighter-angle)}div#\\:\\$p > svg > foreignObject > section .pl-sg{color:var(--color-prettylights-syntax-sublimelinter-gutter-mark)}div#\\:\\$p > svg > foreignObject > section .pl-corl{color:var(--color-prettylights-syntax-constant-other-reference-link);text-decoration:underline}div#\\:\\$p > svg > foreignObject > section [role=button]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section [role=tabpanel][tabindex=\"0\"]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section a:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section button:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section summary:focus:not(:focus-visible){box-shadow:none;outline:none}div#\\:\\$p > svg > foreignObject > section [tabindex=\"0\"]:focus:not(:focus-visible),div#\\:\\$p > svg > foreignObject > section details-dialog:focus:not(:focus-visible){outline:none}div#\\:\\$p > svg > foreignObject > section g-emoji{display:inline-block;font-family:Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;font-size:1em;font-style:normal!important;font-weight:var(--base-text-weight-normal, 400);line-height:1;min-width:1ch;vertical-align:-.075em}div#\\:\\$p > svg > foreignObject > section g-emoji img{height:1em;width:1em}div#\\:\\$p > svg > foreignObject > section .task-list-item{list-style-type:none}div#\\:\\$p > svg > foreignObject > section .task-list-item label{font-weight:var(--base-text-weight-normal, 400)}div#\\:\\$p > svg > foreignObject > section .task-list-item.enabled label{cursor:pointer}div#\\:\\$p > svg > foreignObject > section .task-list-item+.task-list-item{margin-top:var(--base-size-4)}div#\\:\\$p > svg > foreignObject > section .task-list-item .handle{display:none}div#\\:\\$p > svg > foreignObject > section .task-list-item-checkbox{margin:0 .2em .25em -1.4em;vertical-align:middle}div#\\:\\$p > svg > foreignObject > section ul:dir(rtl) .task-list-item-checkbox{margin:0 -1.6em .25em .2em}div#\\:\\$p > svg > foreignObject > section ol:dir(rtl) .task-list-item-checkbox{margin:0 -1.6em .25em .2em}div#\\:\\$p > svg > foreignObject > section .contains-task-list:focus-within .task-list-item-convert-container,div#\\:\\$p > svg > foreignObject > section .contains-task-list:hover .task-list-item-convert-container{display:block;height:24px;overflow:visible;width:auto;clip:auto}div#\\:\\$p > svg > foreignObject > section ::-webkit-calendar-picker-indicator{filter:invert(50%)}div#\\:\\$p > svg > foreignObject > section .markdown-alert{border-left:.25em solid var(--borderColor-default);color:inherit;margin-bottom:var(--base-size-16);padding:var(--base-size-8) var(--base-size-16)}div#\\:\\$p > svg > foreignObject > section .markdown-alert>:first-child{margin-top:0}div#\\:\\$p > svg > foreignObject > section .markdown-alert>:last-child{margin-bottom:0}div#\\:\\$p > svg > foreignObject > section .markdown-alert .markdown-alert-title{align-items:center;display:flex;font-weight:var(--base-text-weight-medium, 500);line-height:1}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-note{border-left-color:var(--borderColor-accent-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-note .markdown-alert-title{color:var(--fgColor-accent)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-important{border-left-color:var(--borderColor-done-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-important .markdown-alert-title{color:var(--fgColor-done)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-warning{border-left-color:var(--borderColor-attention-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-warning .markdown-alert-title{color:var(--fgColor-attention)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-tip{border-left-color:var(--borderColor-success-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-tip .markdown-alert-title{color:var(--fgColor-success)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-caution{border-left-color:var(--borderColor-danger-emphasis)}div#\\:\\$p > svg > foreignObject > section .markdown-alert.markdown-alert-caution .markdown-alert-title{color:var(--fgColor-danger)}div#\\:\\$p > svg > foreignObject > section>:first-child>.heading-element:first-child{margin-top:0!important}div#\\:\\$p > svg > foreignObject > section .highlight :is(pre, marp-pre):has(+.zeroclipboard-container){min-height:52px}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1){color:var(--h1-color);font-size:1.6em}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2){border-bottom:none}div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2){font-size:1.3em}div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3){font-size:1.1em}div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4){font-size:1.05em}div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5){font-size:1em}div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6){font-size:.9em}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1) strong,div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2) strong,div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3) strong,div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4) strong,div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5) strong,div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6) strong{color:var(--heading-strong-color);font-weight:inherit}div#\\:\\$p > svg > foreignObject > section :is(h1, marp-h1)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h2, marp-h2)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h3, marp-h3)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h4, marp-h4)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h5, marp-h5)::part(auto-scaling),div#\\:\\$p > svg > foreignObject > section :is(h6, marp-h6)::part(auto-scaling){max-height:563px}div#\\:\\$p > svg > foreignObject > section hr{height:0;padding-top:.25em}div#\\:\\$p > svg > foreignObject > section img{background-color:transparent}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre){border:1px solid var(--borderColor-default);line-height:1.15;overflow:visible}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre)::part(auto-scaling){max-height:529px}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs){color:var(--color-prettylights-syntax-storage-modifier-import)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-doctag),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-keyword),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-meta .hljs-keyword),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-template-tag),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-template-variable),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-type),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-variable.language_){color:var(--color-prettylights-syntax-keyword)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-title),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-title.class_),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-title.class_.inherited__),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-title.function_){color:var(--color-prettylights-syntax-entity)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-attr),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-attribute),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-literal),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-meta),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-number),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-operator),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-attr),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-class),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-id),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-variable){color:var(--color-prettylights-syntax-constant)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-meta .hljs-string),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-regexp),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-string){color:var(--color-prettylights-syntax-string)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-built_in),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-symbol){color:var(--color-prettylights-syntax-variable)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-code),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-comment),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-formula){color:var(--color-prettylights-syntax-comment)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-name),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-quote),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-pseudo),div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-selector-tag){color:var(--color-prettylights-syntax-entity-tag)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-subst){color:var(--color-prettylights-syntax-storage-modifier-import)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-section){color:var(--color-prettylights-syntax-markup-heading);font-weight:700}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-bullet){color:var(--color-prettylights-syntax-markup-list)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-emphasis){color:var(--color-prettylights-syntax-markup-italic);font-style:italic}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-strong){color:var(--color-prettylights-syntax-markup-bold);font-weight:700}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-addition){background-color:var(--color-prettylights-syntax-markup-inserted-bg);color:var(--color-prettylights-syntax-markup-inserted-text)}div#\\:\\$p > svg > foreignObject > section :is(pre, marp-pre) :where(.hljs-deletion){background-color:var(--color-prettylights-syntax-markup-deleted-bg);color:var(--color-prettylights-syntax-markup-deleted-text)}div#\\:\\$p > svg > foreignObject > section footer,div#\\:\\$p > svg > foreignObject > section header{color:var(--header-footer-color);font-size:18px;left:30px;margin:0;position:absolute}div#\\:\\$p > svg > foreignObject > section header{top:21px}div#\\:\\$p > svg > foreignObject > section footer{bottom:21px}div#\\:\\$p > svg > foreignObject > section{--h1-color:#246;--header-footer-color:hsla(0,0%,40%,.75);--heading-strong-color:#48c;--paginate-color:#777;--base-size-4:4px;--base-size-8:8px;--base-size-16:16px;--base-size-24:24px;--base-size-40:40px;align-items:stretch;display:block;flex-flow:column nowrap;font-size:29px;height:720px;padding:78.5px;place-content:safe center center;width:1280px}div#\\:\\$p > svg > foreignObject > section{--marpit-root-font-size:29px;}div#\\:\\$p > svg > foreignObject > section:where(.invert){--h1-color:#cee7ff;--header-footer-color:hsla(0,0%,60%,.75);--heading-strong-color:#7bf;--paginate-color:#999;}div#\\:\\$p > svg > foreignObject > section>:last-child,div#\\:\\$p > svg > foreignObject > section[data-footer]>:nth-last-child(2){margin-bottom:0}div#\\:\\$p > svg > foreignObject > section>:first-child,div#\\:\\$p > svg > foreignObject > section>header:first-child+*{margin-top:0}div#\\:\\$p > svg > foreignObject > section:after{bottom:21px;color:var(--paginate-color);font-size:24px;padding:0;position:absolute;right:30px}div#\\:\\$p > svg > foreignObject > section:after{--marpit-root-font-size:24px;}div#\\:\\$p > svg > foreignObject > section[data-color] :is(h1, marp-h1),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h2, marp-h2),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h3, marp-h3),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h4, marp-h4),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h5, marp-h5),div#\\:\\$p > svg > foreignObject > section[data-color] :is(h6, marp-h6){color:currentcolor}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"]{columns:initial!important;display:block!important;padding:0!important}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"]::before, div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"]::after, div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"content\"]::before, div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"content\"]::after{display:none!important}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"] > div[data-marpit-advanced-background-container]{all:initial;display:flex;flex-direction:row;height:100%;overflow:hidden;width:100%}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"] > div[data-marpit-advanced-background-container][data-marpit-advanced-background-direction=\"vertical\"]{flex-direction:column}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"][data-marpit-advanced-background-split] > div[data-marpit-advanced-background-container]{width:var(--marpit-advanced-background-split, 50%)}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"][data-marpit-advanced-background-split=\"right\"] > div[data-marpit-advanced-background-container]{margin-left:calc(100% - var(--marpit-advanced-background-split, 50%))}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"] > div[data-marpit-advanced-background-container] > figure{all:initial;background-position:center;background-repeat:no-repeat;background-size:cover;flex:auto;margin:0}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"background\"] > div[data-marpit-advanced-background-container] > figure > figcaption{position:absolute;border:0;clip:rect(0, 0, 0, 0);height:1px;margin:-1px;overflow:hidden;padding:0;white-space:nowrap;width:1px}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"content\"], div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"pseudo\"]{background:transparent!important}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background=\"pseudo\"], div#\\:\\$p > svg[data-marpit-svg] > foreignObject[data-marpit-advanced-background=\"pseudo\"]{pointer-events:none!important}div#\\:\\$p > svg > foreignObject > section[data-marpit-advanced-background-split]{width:100%;height:100%}\n</style></head><body><div class=\"bespoke-marp-osc\"><button data-bespoke-marp-osc=\"prev\" tabindex=\"-1\" title=\"Previous slide\">Previous slide</button><span data-bespoke-marp-osc=\"page\"></span><button data-bespoke-marp-osc=\"next\" tabindex=\"-1\" title=\"Next slide\">Next slide</button><button data-bespoke-marp-osc=\"fullscreen\" tabindex=\"-1\" title=\"Toggle fullscreen (f)\">Toggle fullscreen</button><button data-bespoke-marp-osc=\"presenter\" tabindex=\"-1\" title=\"Open presenter view (p)\">Open presenter view</button></div><div id=\":$p\"><svg data-marpit-svg=\"\" viewBox=\"0 0 1280 720\"><foreignObject width=\"1280\" height=\"720\"><section id=\"1\" data-theme=\"default\" lang=\"C\" style=\"--theme:default;\">\n<h1 id=\"test-slide\">Test Slide</h1>\n<h2 id=\"introduction\">Introduction</h2>\n<p>This is a test slide for Modal deployment.</p>\n<ul>\n<li>Point 1</li>\n<li>Point 2</li>\n<li>Point 3</li>\n</ul>\n</section>\n<script>!function(){\"use strict\";const t={h1:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"1\"},style:\"display: block; font-size: 2em; margin-block-start: 0.67em; margin-block-end: 0.67em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h2:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"2\"},style:\"display: block; font-size: 1.5em; margin-block-start: 0.83em; margin-block-end: 0.83em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h3:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"3\"},style:\"display: block; font-size: 1.17em; margin-block-start: 1em; margin-block-end: 1em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h4:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"4\"},style:\"display: block; margin-block-start: 1.33em; margin-block-end: 1.33em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h5:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"5\"},style:\"display: block; font-size: 0.83em; margin-block-start: 1.67em; margin-block-end: 1.67em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},h6:{proto:()=>HTMLHeadingElement,attrs:{role:\"heading\",\"aria-level\":\"6\"},style:\"display: block; font-size: 0.67em; margin-block-start: 2.33em; margin-block-end: 2.33em; margin-inline-start: 0px; margin-inline-end: 0px; font-weight: bold;\"},span:{proto:()=>HTMLSpanElement},pre:{proto:()=>HTMLElement,style:\"display: block; font-family: monospace; white-space: pre; margin: 1em 0; --marp-auto-scaling-white-space: pre;\"}},e=\"data-marp-auto-scaling-wrapper\",i=\"data-marp-auto-scaling-svg\",n=\"data-marp-auto-scaling-container\";class s extends HTMLElement{container;containerSize;containerObserver;svg;svgComputedStyle;svgPreserveAspectRatio=\"xMinYMid meet\";wrapper;wrapperSize;wrapperObserver;constructor(){super();const t=t=>([e])=>{const{width:i,height:n}=e.contentRect;this[t]={width:i,height:n},this.updateSVGRect()};this.attachShadow({mode:\"open\"}),this.containerObserver=new ResizeObserver(t(\"containerSize\")),this.wrapperObserver=new ResizeObserver(((...e)=>{t(\"wrapperSize\")(...e),this.flushSvgDisplay()}))}static get observedAttributes(){return[\"data-downscale-only\"]}connectedCallback(){this.shadowRoot.innerHTML=`\\n<style>\\n  svg[${i}] { display: block; width: 100%; height: auto; vertical-align: top; }\\n  span[${n}] { display: table; white-space: var(--marp-auto-scaling-white-space, nowrap); width: max-content; }\\n</style>\\n<div ${e}>\\n  <svg part=\"svg\" ${i}>\\n    <foreignObject><span ${n}><slot></slot></span></foreignObject>\\n  </svg>\\n</div>\\n    `.split(/\\n\\s*/).join(\"\"),this.wrapper=this.shadowRoot.querySelector(`div[${e}]`)??void 0;const t=this.svg;this.svg=this.wrapper?.querySelector(`svg[${i}]`)??void 0,this.svg!==t&&(this.svgComputedStyle=this.svg?window.getComputedStyle(this.svg):void 0),this.container=this.svg?.querySelector(`span[${n}]`)??void 0,this.observe()}disconnectedCallback(){this.svg=void 0,this.svgComputedStyle=void 0,this.wrapper=void 0,this.container=void 0,this.observe()}attributeChangedCallback(){this.observe()}flushSvgDisplay(){const{svg:t}=this;t&&(t.style.display=\"inline\",requestAnimationFrame((()=>{t.style.display=\"\"})))}observe(){this.containerObserver.disconnect(),this.wrapperObserver.disconnect(),this.wrapper&&this.wrapperObserver.observe(this.wrapper),this.container&&this.containerObserver.observe(this.container),this.svgComputedStyle&&this.observeSVGStyle(this.svgComputedStyle)}observeSVGStyle(t){const e=()=>{const i=(()=>{const e=t.getPropertyValue(\"--preserve-aspect-ratio\");if(e)return e.trim();return`x${(({textAlign:t,direction:e})=>{if(t.endsWith(\"left\"))return\"Min\";if(t.endsWith(\"right\"))return\"Max\";if(\"start\"===t||\"end\"===t){let i=\"rtl\"===e;return\"end\"===t&&(i=!i),i?\"Max\":\"Min\"}return\"Mid\"})(t)}YMid meet`})();i!==this.svgPreserveAspectRatio&&(this.svgPreserveAspectRatio=i,this.updateSVGRect()),t===this.svgComputedStyle&&requestAnimationFrame(e)};e()}updateSVGRect(){let t=Math.ceil(this.containerSize?.width??0);const e=Math.ceil(this.containerSize?.height??0);void 0!==this.dataset.downscaleOnly&&(t=Math.max(t,this.wrapperSize?.width??0));const i=this.svg?.querySelector(\":scope > foreignObject\");if(i?.setAttribute(\"width\",`${t}`),i?.setAttribute(\"height\",`${e}`),this.svg&&(this.svg.setAttribute(\"viewBox\",`0 0 ${t} ${e}`),this.svg.setAttribute(\"preserveAspectRatio\",this.svgPreserveAspectRatio),this.svg.style.height=t<=0||e<=0?\"0\":\"\"),this.container){const t=this.svgPreserveAspectRatio.toLowerCase();this.container.style.marginLeft=t.startsWith(\"xmid\")||t.startsWith(\"xmax\")?\"auto\":\"0\",this.container.style.marginRight=t.startsWith(\"xmi\")?\"auto\":\"0\"}}}const r=(t,{attrs:e={},style:i})=>class extends t{constructor(...t){super(...t);for(const[t,i]of Object.entries(e))this.hasAttribute(t)||this.setAttribute(t,i);this._shadow()}static get observedAttributes(){return[\"data-auto-scaling\"]}connectedCallback(){this._update()}attributeChangedCallback(){this._update()}_shadow(){if(!this.shadowRoot)try{this.attachShadow({mode:\"open\"})}catch(t){if(!(t instanceof Error&&\"NotSupportedError\"===t.name))throw t}return this.shadowRoot}_update(){const t=this._shadow();if(t){const e=i?`<style>:host { ${i} }</style>`:\"\";let n=\"<slot></slot>\";const{autoScaling:s}=this.dataset;if(void 0!==s){n=`<marp-auto-scaling exportparts=\"svg:auto-scaling\" ${\"downscale-only\"===s?\"data-downscale-only\":\"\"}>${n}</marp-auto-scaling>`}t.innerHTML=e+n}}};let o;const a=Symbol();let l;const c=\"marpitSVGPolyfill:setZoomFactor,\",d=Symbol(),h=Symbol();const g=()=>{const t=\"Apple Computer, Inc.\"===navigator.vendor,e=t?[u]:[],i={then:e=>(t?(async()=>{if(void 0===l){const t=document.createElement(\"canvas\");t.width=10,t.height=10;const e=t.getContext(\"2d\"),i=new Image(10,10),n=new Promise((t=>{i.addEventListener(\"load\",(()=>t()))}));i.crossOrigin=\"anonymous\",i.src=\"data:image/svg+xml;charset=utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2210%22%20height%3D%2210%22%20viewBox%3D%220%200%201%201%22%3E%3CforeignObject%20width%3D%221%22%20height%3D%221%22%20requiredExtensions%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxhtml%22%3E%3Cdiv%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxhtml%22%20style%3D%22width%3A%201px%3B%20height%3A%201px%3B%20background%3A%20red%3B%20position%3A%20relative%22%3E%3C%2Fdiv%3E%3C%2FforeignObject%3E%3C%2Fsvg%3E\",await n,e.drawImage(i,0,0),l=e.getImageData(5,5,1,1).data[3]<128}return l})().then((t=>{null==e||e(t?[u]:[])})):null==e||e([]),i)};return Object.assign(e,i)};let p,m;function u(t){const e=\"object\"==typeof t&&t.target||document,i=\"object\"==typeof t?t.zoom:t;window[h]||(Object.defineProperty(window,h,{configurable:!0,value:!0}),document.body.style.zoom=1.0001,document.body.offsetHeight,document.body.style.zoom=1,window.addEventListener(\"message\",(({data:t,origin:e})=>{if(e===window.origin)try{if(t&&\"string\"==typeof t&&t.startsWith(c)){const[,e]=t.split(\",\"),i=Number.parseFloat(e);Number.isNaN(i)||(m=i)}}catch(t){console.error(t)}})));let n=!1;Array.from(e.querySelectorAll(\"svg[data-marpit-svg]\"),(t=>{var e,s,r,o;t.style.transform||(t.style.transform=\"translateZ(0)\");const a=i||m||t.currentScale||1;p!==a&&(p=a,n=a);const l=t.getBoundingClientRect(),{length:c}=t.children;for(let i=0;i<c;i+=1){const n=t.children[i];if(n.getScreenCTM){const t=n.getScreenCTM();if(t){const i=null!==(s=null===(e=n.x)||void 0===e?void 0:e.baseVal.value)&&void 0!==s?s:0,c=null!==(o=null===(r=n.y)||void 0===r?void 0:r.baseVal.value)&&void 0!==o?o:0,d=n.children.length;for(let e=0;e<d;e+=1){const s=n.children[e];if(\"SECTION\"===s.tagName){const{style:e}=s;e.transformOrigin||(e.transformOrigin=`${-i}px ${-c}px`),e.transform=`scale(${a}) matrix(${t.a}, ${t.b}, ${t.c}, ${t.d}, ${t.e-l.left}, ${t.f-l.top}) translateZ(0.0001px)`;break}}}}}})),!1!==n&&Array.from(e.querySelectorAll(\"iframe\"),(({contentWindow:t})=>{null==t||t.postMessage(`${c}${n}`,\"null\"===window.origin?\"*\":window.origin)}))}function v({once:t=!1,target:e=document}={}){const i=function(t=document){if(t[d])return t[d];let e=!0;const i=()=>{e=!1,delete t[d]};Object.defineProperty(t,d,{configurable:!0,value:i});let n=[],s=!1;(async()=>{try{n=await g()}finally{s=!0}})();const r=()=>{for(const e of n)e({target:t});s&&0===n.length||e&&window.requestAnimationFrame(r)};return r(),i}(e);return t?(i(),()=>{}):i}p=1,m=void 0;const w=Symbol(),b=(e=document)=>{if(\"undefined\"==typeof window)throw new Error(\"Marp Core's browser script is valid only in browser context.\");if(((e=document)=>{const i=window[a];i||customElements.define(\"marp-auto-scaling\",s);for(const n of Object.keys(t)){const s=`marp-${n}`,a=t[n].proto();(o??(o=!!document.createElement(\"div\",{is:\"marp-auto-scaling\"}).outerHTML.startsWith(\"<div is\"),o))&&a!==HTMLElement?i||customElements.define(s,r(a,{style:t[n].style}),{extends:n}):(i||customElements.define(s,r(HTMLElement,t[n])),e.querySelectorAll(`${n}[is=\"${s}\"]`).forEach((t=>{t.outerHTML=t.outerHTML.replace(new RegExp(`^<${n}`,\"i\"),`<${s}`).replace(new RegExp(`</${n}>$`,\"i\"),`</${s}>`)})))}window[a]=!0})(e),e[w])return e[w];const i=v({target:e}),n=()=>{i(),delete e[w]},l=Object.assign(n,{cleanup:n,update:()=>b(e)});return Object.defineProperty(e,w,{configurable:!0,value:l}),l},y=document.currentScript;b(y?y.getRootNode():document)}();\n</script></foreignObject></svg></div><script>/*!! License: https://unpkg.com/@marp-team/marp-cli@4.2.0/lib/bespoke.js.LICENSE.txt */\n!function(){\"use strict\";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,\"default\")?e.default:e}var t,n,r=(n||(n=1,t={from:function(e,t){var n,r=1===(e.parent||e).nodeType?e.parent||e:document.querySelector(e.parent||e),o=[].filter.call(\"string\"==typeof e.slides?r.querySelectorAll(e.slides):e.slides||r.children,(function(e){return\"SCRIPT\"!==e.nodeName})),a={},i=function(e,t){return(t=t||{}).index=o.indexOf(e),t.slide=e,t},s=function(e,t){a[e]=(a[e]||[]).filter((function(e){return e!==t}))},c=function(e,t){return(a[e]||[]).reduce((function(e,n){return e&&!1!==n(t)}),!0)},l=function(e,t){o[e]&&(n&&c(\"deactivate\",i(n,t)),n=o[e],c(\"activate\",i(n,t)))},d=function(e,t){var r=o.indexOf(n)+e;c(e>0?\"next\":\"prev\",i(n,t))&&l(r,t)},u={off:s,on:function(e,t){return(a[e]||(a[e]=[])).push(t),s.bind(null,e,t)},fire:c,slide:function(e,t){if(!arguments.length)return o.indexOf(n);c(\"slide\",i(o[e],t))&&l(e,t)},next:d.bind(null,1),prev:d.bind(null,-1),parent:r,slides:o,destroy:function(e){c(\"destroy\",i(n,e)),a={}}};return(t||[]).forEach((function(e){e(u)})),n||l(0),u}}),t),o=e(r);const a=document.body,i=(...e)=>history.replaceState(...e),s=\"\",c=\"presenter\",l=\"next\",d=[\"\",c,l],u=\"bespoke-marp-\",f=`data-${u}`,m=(e,{protocol:t,host:n,pathname:r,hash:o}=location)=>{const a=e.toString();return`${t}//${n}${r}${a?\"?\":\"\"}${a}${o}`},g=()=>a.dataset.bespokeView,p=e=>new URLSearchParams(location.search).get(e),v=(e,t={})=>{const n={location,setter:i,...t},r=new URLSearchParams(n.location.search);for(const t of Object.keys(e)){const n=e[t];\"string\"==typeof n?r.set(t,n):r.delete(t)}try{n.setter({...window.history.state??{}},\"\",m(r,n.location))}catch(e){console.error(e)}},h=(()=>{const e=\"bespoke-marp\";try{return localStorage.setItem(e,e),localStorage.removeItem(e),!0}catch{return!1}})(),y=e=>{try{return localStorage.getItem(e)}catch{return null}},b=(e,t)=>{try{return localStorage.setItem(e,t),!0}catch{return!1}},w=e=>{try{return localStorage.removeItem(e),!0}catch{return!1}},x=(e,t)=>{const n=\"aria-hidden\";t?e.setAttribute(n,\"true\"):e.removeAttribute(n)},k=e=>{e.parent.classList.add(`${u}parent`),e.slides.forEach((e=>e.classList.add(`${u}slide`))),e.on(\"activate\",(t=>{const n=`${u}active`,r=t.slide,o=r.classList,a=!o.contains(n);if(e.slides.forEach((e=>{e.classList.remove(n),x(e,!0)})),o.add(n),x(r,!1),a){const e=`${n}-ready`;o.add(e),document.body.clientHeight,o.remove(e)}}))},$=e=>{let t=0,n=0;Object.defineProperty(e,\"fragments\",{enumerable:!0,value:e.slides.map((e=>[null,...e.querySelectorAll(\"[data-marpit-fragment]\")]))});const r=r=>void 0!==e.fragments[t][n+r],o=(r,o)=>{t=r,n=o,e.fragments.forEach(((e,t)=>{e.forEach(((e,n)=>{if(null==e)return;const a=t<r||t===r&&n<=o;e.setAttribute(`${f}fragment`,(a?\"\":\"in\")+\"active\");const i=`${f}current-fragment`;t===r&&n===o?e.setAttribute(i,\"current\"):e.removeAttribute(i)}))})),e.fragmentIndex=o;const a={slide:e.slides[r],index:r,fragments:e.fragments[r],fragmentIndex:o};e.fire(\"fragment\",a)};e.on(\"next\",(({fragment:a=!0})=>{if(a){if(r(1))return o(t,n+1),!1;const a=t+1;e.fragments[a]&&o(a,0)}else{const r=e.fragments[t].length;if(n+1<r)return o(t,r-1),!1;const a=e.fragments[t+1];a&&o(t+1,a.length-1)}})),e.on(\"prev\",(({fragment:a=!0})=>{if(r(-1)&&a)return o(t,n-1),!1;const i=t-1;e.fragments[i]&&o(i,e.fragments[i].length-1)})),e.on(\"slide\",(({index:t,fragment:n})=>{let r=0;if(void 0!==n){const o=e.fragments[t];if(o){const{length:e}=o;r=-1===n?e-1:Math.min(Math.max(n,0),e-1)}}o(t,r)})),o(0,0)},E=document,L=()=>!(!E.fullscreenEnabled&&!E.webkitFullscreenEnabled),S=()=>!(!E.fullscreenElement&&!E.webkitFullscreenElement),P=e=>{e.fullscreen=()=>{L()&&(async()=>{S()?(E.exitFullscreen||E.webkitExitFullscreen)?.call(E):((e=E.body)=>{(e.requestFullscreen||e.webkitRequestFullscreen)?.call(e)})()})()},document.addEventListener(\"keydown\",(t=>{\"f\"!==t.key&&\"F11\"!==t.key||t.altKey||t.ctrlKey||t.metaKey||!L()||(e.fullscreen(),t.preventDefault())}))},_=`${u}inactive`,T=(e=2e3)=>({parent:t,fire:n})=>{const r=t.classList,o=e=>n(`marp-${e?\"\":\"in\"}active`);let a;const i=()=>{a&&clearTimeout(a),a=setTimeout((()=>{r.add(_),o()}),e),r.contains(_)&&(r.remove(_),o(!0))};for(const e of[\"mousedown\",\"mousemove\",\"touchend\"])document.addEventListener(e,i);setTimeout(i,0)},I=[\"AUDIO\",\"BUTTON\",\"INPUT\",\"SELECT\",\"TEXTAREA\",\"VIDEO\"],M=e=>{e.parent.addEventListener(\"keydown\",(e=>{if(!e.target)return;const t=e.target;(I.includes(t.nodeName)||\"true\"===t.contentEditable)&&e.stopPropagation()}))},O=e=>{window.addEventListener(\"load\",(()=>{for(const t of e.slides){const e=t.querySelector(\"marp-auto-scaling, [data-auto-scaling], [data-marp-fitting]\");t.setAttribute(`${f}load`,e?\"\":\"hideable\")}}))},A=({interval:e=250}={})=>t=>{document.addEventListener(\"keydown\",(e=>{if(\" \"===e.key&&e.shiftKey)t.prev();else if(\"ArrowLeft\"===e.key||\"ArrowUp\"===e.key||\"PageUp\"===e.key)t.prev({fragment:!e.shiftKey});else if(\" \"!==e.key||e.shiftKey)if(\"ArrowRight\"===e.key||\"ArrowDown\"===e.key||\"PageDown\"===e.key)t.next({fragment:!e.shiftKey});else if(\"End\"===e.key)t.slide(t.slides.length-1,{fragment:-1});else{if(\"Home\"!==e.key)return;t.slide(0)}else t.next();e.preventDefault()}));let n,r,o=0;t.parent.addEventListener(\"wheel\",(a=>{let i=!1;const s=(e,t)=>{e&&(i=i||((e,t)=>((e,t)=>{const n=\"X\"===t?\"Width\":\"Height\";return e[`client${n}`]<e[`scroll${n}`]})(e,t)&&((e,t)=>{const{overflow:n}=e,r=e[`overflow${t}`];return\"auto\"===n||\"scroll\"===n||\"auto\"===r||\"scroll\"===r})(getComputedStyle(e),t))(e,t)),e?.parentElement&&s(e.parentElement,t)};if(0!==a.deltaX&&s(a.target,\"X\"),0!==a.deltaY&&s(a.target,\"Y\"),i)return;a.preventDefault();const c=Math.sqrt(a.deltaX**2+a.deltaY**2);if(void 0!==a.wheelDelta){if(void 0===a.webkitForce&&Math.abs(a.wheelDelta)<40)return;if(a.deltaMode===a.DOM_DELTA_PIXEL&&c<4)return}else if(a.deltaMode===a.DOM_DELTA_PIXEL&&c<12)return;r&&clearTimeout(r),r=setTimeout((()=>{n=0}),e);const l=Date.now()-o<e,d=c<=n;if(n=c,l||d)return;let u;(a.deltaX>0||a.deltaY>0)&&(u=\"next\"),(a.deltaX<0||a.deltaY<0)&&(u=\"prev\"),u&&(t[u](),o=Date.now())}))},C=(e=`.${u}osc`)=>{const t=document.querySelector(e);if(!t)return()=>{};const n=(e,n)=>{t.querySelectorAll(`[${f}osc=${JSON.stringify(e)}]`).forEach(n)};return L()||n(\"fullscreen\",(e=>e.style.display=\"none\")),h||n(\"presenter\",(e=>{e.disabled=!0,e.title=\"Presenter view is disabled due to restricted localStorage.\"})),e=>{t.addEventListener(\"click\",(t=>{if(t.target instanceof HTMLElement){const{bespokeMarpOsc:n}=t.target.dataset;n&&t.target.blur();const r={fragment:!t.shiftKey};\"next\"===n?e.next(r):\"prev\"===n?e.prev(r):\"fullscreen\"===n?e?.fullscreen():\"presenter\"===n&&e.openPresenterView()}})),e.parent.appendChild(t),e.on(\"activate\",(({index:t})=>{n(\"page\",(n=>n.textContent=`Page ${t+1} of ${e.slides.length}`))})),e.on(\"fragment\",(({index:t,fragments:r,fragmentIndex:o})=>{n(\"prev\",(e=>e.disabled=0===t&&0===o)),n(\"next\",(n=>n.disabled=t===e.slides.length-1&&o===r.length-1))})),e.on(\"marp-active\",(()=>x(t,!1))),e.on(\"marp-inactive\",(()=>x(t,!0))),L()&&(e=>{for(const t of[\"\",\"webkit\"])E.addEventListener(t+\"fullscreenchange\",e)})((()=>n(\"fullscreen\",(e=>e.classList.toggle(\"exit\",L()&&S())))))}},D=e=>{window.addEventListener(\"message\",(t=>{if(t.origin!==window.origin)return;const[n,r]=t.data.split(\":\");if(\"navigate\"===n){const[t,n]=r.split(\",\");let o=Number.parseInt(t,10),a=Number.parseInt(n,10)+1;a>=e.fragments[o].length&&(o+=1,a=0),e.slide(o,{fragment:a})}}))};var N,B,q,K,F,j,V,U={exports:{}},X=(N||(N=1,U.exports=(B=[\"area\",\"base\",\"br\",\"col\",\"command\",\"embed\",\"hr\",\"img\",\"input\",\"keygen\",\"link\",\"meta\",\"param\",\"source\",\"track\",\"wbr\"],q=function(e){return String(e).replace(/[&<>\"']/g,(function(e){return\"&\"+K[e]+\";\"}))},K={\"&\":\"amp\",\"<\":\"lt\",\">\":\"gt\",'\"':\"quot\",\"'\":\"apos\"},F=\"dangerouslySetInnerHTML\",j={className:\"class\",htmlFor:\"for\"},V={},function(e,t){var n=[],r=\"\";t=t||{};for(var o=arguments.length;o-- >2;)n.push(arguments[o]);if(\"function\"==typeof e)return t.children=n.reverse(),e(t);if(e){if(r+=\"<\"+e,t)for(var a in t)!1!==t[a]&&null!=t[a]&&a!==F&&(r+=\" \"+(j[a]?j[a]:q(a))+'=\"'+q(t[a])+'\"');r+=\">\"}if(-1===B.indexOf(e)){if(t[F])r+=t[F].__html;else for(;n.length;){var i=n.pop();if(i)if(i.pop)for(var s=i.length;s--;)n.push(i[s]);else r+=!0===V[i]?i:q(i)}r+=e?\"</\"+e+\">\":\"\"}return V[r]=!0,r})),U.exports),H=e(X);const R=({children:e})=>H(null,null,...e),W=`${u}presenter-`,J={container:`${W}container`,dragbar:`${W}dragbar-container`,next:`${W}next`,nextContainer:`${W}next-container`,noteContainer:`${W}note-container`,noteWrapper:`${W}note-wrapper`,noteButtons:`${W}note-buttons`,infoContainer:`${W}info-container`,infoPage:`${W}info-page`,infoPageText:`${W}info-page-text`,infoPagePrev:`${W}info-page-prev`,infoPageNext:`${W}info-page-next`,noteButtonsBigger:`${W}note-bigger`,noteButtonsSmaller:`${W}note-smaller`,infoTime:`${W}info-time`,infoTimer:`${W}info-timer`},Y=e=>{const{title:t}=document;document.title=\"[Presenter view]\"+(t?` - ${t}`:\"\");const n={},r=e=>(n[e]=n[e]||document.querySelector(`.${e}`),n[e]);document.body.appendChild((e=>{const t=document.createElement(\"div\");return t.className=J.container,t.appendChild(e),t.insertAdjacentHTML(\"beforeend\",H(R,null,H(\"div\",{class:J.nextContainer},H(\"iframe\",{class:J.next,src:\"?view=next\"})),H(\"div\",{class:J.dragbar}),H(\"div\",{class:J.noteContainer},H(\"div\",{class:J.noteWrapper}),H(\"div\",{class:J.noteButtons},H(\"button\",{class:J.noteButtonsSmaller,tabindex:\"-1\",title:\"Smaller notes font size\"},\"Smaller notes font size\"),H(\"button\",{class:J.noteButtonsBigger,tabindex:\"-1\",title:\"Bigger notes font size\"},\"Bigger notes font size\"))),H(\"div\",{class:J.infoContainer},H(\"div\",{class:J.infoPage},H(\"button\",{class:J.infoPagePrev,tabindex:\"-1\",title:\"Previous\"},\"Previous\"),H(\"span\",{class:J.infoPageText}),H(\"button\",{class:J.infoPageNext,tabindex:\"-1\",title:\"Next\"},\"Next\")),H(\"time\",{class:J.infoTime,title:\"Current time\"}),H(\"time\",{class:J.infoTimer,title:\"Timer\"})))),t})(e.parent)),(e=>{let t=!1;r(J.dragbar).addEventListener(\"mousedown\",(()=>{t=!0,r(J.dragbar).classList.add(\"active\")})),window.addEventListener(\"mouseup\",(()=>{t=!1,r(J.dragbar).classList.remove(\"active\")})),window.addEventListener(\"mousemove\",(e=>{if(!t)return;const n=e.clientX/document.documentElement.clientWidth*100;r(J.container).style.setProperty(\"--bespoke-marp-presenter-split-ratio\",`${Math.max(0,Math.min(100,n))}%`)})),r(J.nextContainer).addEventListener(\"click\",(()=>e.next()));const n=r(J.next),o=(a=n,(e,t)=>a.contentWindow?.postMessage(`navigate:${e},${t}`,\"null\"===window.origin?\"*\":window.origin));var a;n.addEventListener(\"load\",(()=>{r(J.nextContainer).classList.add(\"active\"),o(e.slide(),e.fragmentIndex),e.on(\"fragment\",(({index:e,fragmentIndex:t})=>o(e,t)))}));const i=document.querySelectorAll(\".bespoke-marp-note\");i.forEach((e=>{e.addEventListener(\"keydown\",(e=>e.stopPropagation())),r(J.noteWrapper).appendChild(e)})),e.on(\"activate\",(()=>i.forEach((t=>t.classList.toggle(\"active\",t.dataset.index==e.slide())))));let s=0;const c=e=>{s=Math.max(-5,s+e),r(J.noteContainer).style.setProperty(\"--bespoke-marp-note-font-scale\",(1.2**s).toFixed(4))},l=()=>c(1),d=()=>c(-1),u=r(J.noteButtonsBigger),f=r(J.noteButtonsSmaller);u.addEventListener(\"click\",(()=>{u.blur(),l()})),f.addEventListener(\"click\",(()=>{f.blur(),d()})),document.addEventListener(\"keydown\",(e=>{\"+\"===e.key&&l(),\"-\"===e.key&&d()}),!0),e.on(\"activate\",(({index:t})=>{r(J.infoPageText).textContent=`${t+1} / ${e.slides.length}`}));const m=r(J.infoPagePrev),g=r(J.infoPageNext);m.addEventListener(\"click\",(t=>{m.blur(),e.prev({fragment:!t.shiftKey})})),g.addEventListener(\"click\",(t=>{g.blur(),e.next({fragment:!t.shiftKey})})),e.on(\"fragment\",(({index:t,fragments:n,fragmentIndex:r})=>{m.disabled=0===t&&0===r,g.disabled=t===e.slides.length-1&&r===n.length-1}));let p=new Date;const v=()=>{const e=new Date,t=e=>`${Math.floor(e)}`.padStart(2,\"0\"),n=e.getTime()-p.getTime(),o=t(n/1e3%60),a=t(n/1e3/60%60),i=t(n/36e5%24);r(J.infoTime).textContent=e.toLocaleTimeString(),r(J.infoTimer).textContent=`${i}:${a}:${o}`};v(),setInterval(v,250),r(J.infoTimer).addEventListener(\"click\",(()=>{p=new Date}))})(e)},z=e=>{if(!(e=>e.syncKey&&\"string\"==typeof e.syncKey)(e))throw new Error(\"The current instance of Bespoke.js is invalid for Marp bespoke presenter plugin.\");Object.defineProperties(e,{openPresenterView:{enumerable:!0,value:G},presenterUrl:{enumerable:!0,get:Q}}),h&&document.addEventListener(\"keydown\",(t=>{\"p\"!==t.key||t.altKey||t.ctrlKey||t.metaKey||(t.preventDefault(),e.openPresenterView())}))};function G(){const{max:e,floor:t}=Math,n=e(t(.85*window.innerWidth),640),r=e(t(.85*window.innerHeight),360);return window.open(this.presenterUrl,W+this.syncKey,`width=${n},height=${r},menubar=no,toolbar=no`)}function Q(){const e=new URLSearchParams(location.search);return e.set(\"view\",\"presenter\"),e.set(\"sync\",this.syncKey),m(e)}const Z=e=>{const t=g();return t===l&&e.appendChild(document.createElement(\"span\")),{[s]:z,[c]:Y,[l]:D}[t]},ee=e=>{e.on(\"activate\",(t=>{document.querySelectorAll(\".bespoke-progress-parent > .bespoke-progress-bar\").forEach((n=>{n.style.flexBasis=100*t.index/(e.slides.length-1)+\"%\"}))}))},te=e=>{const t=Number.parseInt(e,10);return Number.isNaN(t)?null:t},ne=(e={})=>{const t={history:!0,...e};return e=>{let n=!0;const r=e=>{const t=n;try{return n=!0,e()}finally{n=t}},o=(t={fragment:!0})=>{let n=t.fragment?te(p(\"f\")||\"\"):null;((t,n)=>{const{min:r,max:o}=Math,{fragments:a,slides:i}=e,s=o(0,r(t,i.length-1)),c=o(0,r(n||0,a[s].length-1));s===e.slide()&&c===e.fragmentIndex||e.slide(s,{fragment:c})})((()=>{if(location.hash){const[t]=location.hash.slice(1).split(\":~:\");if(/^\\d+$/.test(t))return(te(t)??1)-1;const r=document.getElementById(t)||document.querySelector(`a[name=\"${CSS.escape(t)}\"]`);if(r){const{length:t}=e.slides;for(let o=0;o<t;o+=1)if(e.slides[o].contains(r)){const t=e.fragments?.[o],a=r.closest(\"[data-marpit-fragment]\");if(t&&a){const e=t.indexOf(a);e>=0&&(n=e)}return o}}}return 0})(),n)};e.on(\"fragment\",(({index:e,fragmentIndex:r})=>{n||v({f:0===r||r.toString()},{location:{...location,hash:`#${e+1}`},setter:(...e)=>t.history?history.pushState(...e):history.replaceState(...e)})})),setTimeout((()=>{o(),window.addEventListener(\"hashchange\",(()=>r((()=>{o({fragment:!1}),v({f:void 0})})))),window.addEventListener(\"popstate\",(()=>{n||r((()=>o()))})),n=!1}),0)}},re=(e={})=>{const t=e.key||window.history.state?.marpBespokeSyncKey||Math.random().toString(36).slice(2),n=`bespoke-marp-sync-${t}`;var r;r={marpBespokeSyncKey:t},v({},{setter:(e,...t)=>i({...e,...r},...t)});const o=()=>{const e=y(n);return e?JSON.parse(e):Object.create(null)},a=e=>{const t=o(),r={...t,...e(t)};return b(n,JSON.stringify(r)),r},s=()=>{window.removeEventListener(\"pageshow\",s),a((e=>({reference:(e.reference||0)+1})))};return e=>{s(),Object.defineProperty(e,\"syncKey\",{value:t,enumerable:!0});let r=!0;setTimeout((()=>{e.on(\"fragment\",(e=>{r&&a((()=>({index:e.index,fragmentIndex:e.fragmentIndex})))}))}),0),window.addEventListener(\"storage\",(t=>{if(t.key===n&&t.oldValue&&t.newValue){const n=JSON.parse(t.oldValue),o=JSON.parse(t.newValue);if(n.index!==o.index||n.fragmentIndex!==o.fragmentIndex)try{r=!1,e.slide(o.index,{fragment:o.fragmentIndex,forSync:!0})}finally{r=!0}}}));const i=()=>{const{reference:e}=o();void 0===e||e<=1?w(n):a((()=>({reference:e-1})))};window.addEventListener(\"pagehide\",(e=>{e.persisted&&window.addEventListener(\"pageshow\",s),i()})),e.on(\"destroy\",i)}},{PI:oe,abs:ae,sqrt:ie,atan2:se}=Math,ce={passive:!0},le=({slope:e=-.7,swipeThreshold:t=30}={})=>n=>{let r;const o=n.parent,a=e=>{const t=o.getBoundingClientRect();return{x:e.pageX-(t.left+t.right)/2,y:e.pageY-(t.top+t.bottom)/2}};o.addEventListener(\"touchstart\",(({touches:e})=>{r=1===e.length?a(e[0]):void 0}),ce),o.addEventListener(\"touchmove\",(e=>{if(r)if(1===e.touches.length){e.preventDefault();const t=a(e.touches[0]),n=t.x-r.x,o=t.y-r.y;r.delta=ie(ae(n)**2+ae(o)**2),r.radian=se(n,o)}else r=void 0})),o.addEventListener(\"touchend\",(o=>{if(r){if(r.delta&&r.delta>=t&&r.radian){const t=(r.radian-e+oe)%(2*oe)-oe;n[t<0?\"next\":\"prev\"](),o.stopPropagation()}r=void 0}}),ce)},de=new Map;de.clear(),de.set(\"none\",{backward:{both:void 0,incoming:void 0,outgoing:void 0},forward:{both:void 0,incoming:void 0,outgoing:void 0}});const ue={both:\"\",outgoing:\"outgoing-\",incoming:\"incoming-\"},fe={forward:\"\",backward:\"-backward\"},me=e=>`--marp-bespoke-transition-animation-${e}`,ge=e=>`--marp-transition-${e}`,pe=me(\"name\"),ve=me(\"duration\"),he=e=>new Promise((t=>{const n={},r=document.createElement(\"div\"),o=e=>{r.remove(),t(e)};r.addEventListener(\"animationstart\",(()=>o(n))),Object.assign(r.style,{animationName:e,animationDuration:\"1s\",animationFillMode:\"both\",animationPlayState:\"paused\",position:\"absolute\",pointerEvents:\"none\"}),document.body.appendChild(r);const a=getComputedStyle(r).getPropertyValue(ge(\"duration\"));a&&Number.parseFloat(a)>=0&&(n.defaultDuration=a),((e,t)=>{requestAnimationFrame((()=>{e.style.animationPlayState=\"running\",requestAnimationFrame((()=>t(void 0)))}))})(r,o)})),ye=async e=>de.has(e)?de.get(e):(e=>{const t={},n=[];for(const[r,o]of Object.entries(ue))for(const[a,i]of Object.entries(fe)){const s=`marp-${o}transition${i}-${e}`;n.push(he(s).then((e=>{t[a]=t[a]||{},t[a][r]=e?{...e,name:s}:void 0})))}return Promise.all(n).then((()=>t))})(e).then((t=>(de.set(e,t),t))),be=e=>Object.values(e).flatMap(Object.values).every((e=>!e)),we=(e,{type:t,backward:n})=>{const r=e[n?\"backward\":\"forward\"],o=(()=>{const e=r[t],n=e=>({[pe]:e.name});if(e)return n(e);if(r.both){const e=n(r.both);return\"incoming\"===t&&(e[me(\"direction\")]=\"reverse\"),e}})();return!o&&n?we(e,{type:t,backward:!1}):o||{[pe]:\"__bespoke_marp_transition_no_animation__\"}},xe=e=>{if(e)try{const t=JSON.parse(e);if((e=>{if(\"object\"!=typeof e)return!1;const t=e;return\"string\"==typeof t.name&&(void 0===t.duration||\"string\"==typeof t.duration)})(t))return t}catch{}},ke=\"_tSId\",$e=\"_tA\",Ee=\"bespoke-marp-transition-warming-up\",Le=window.matchMedia(\"(prefers-reduced-motion: reduce)\"),Se=\"__bespoke_marp_transition_reduced_outgoing__\",Pe=\"__bespoke_marp_transition_reduced_incoming__\",_e={forward:{both:void 0,incoming:{name:Pe},outgoing:{name:Se}},backward:{both:void 0,incoming:{name:Pe},outgoing:{name:Se}}},Te=e=>{if(!document.startViewTransition)return;const t=t=>(void 0!==t&&(e._tD=t),e._tD);let n;t(!1),((...e)=>{CSS.registerProperty({name:ge(\"duration\"),syntax:\"<time>\",inherits:!0,initialValue:\"-1s\"});const t=[...new Set(e).values()];return Promise.all(t.map((e=>ye(e)))).then()})(...Array.from(document.querySelectorAll(\"section[data-transition], section[data-transition-back]\")).flatMap((e=>[e.dataset.transition,e.dataset.transitionBack].flatMap((e=>{const t=xe(e);return[t?.name,t?.builtinFallback?`__builtin__${t.name}`:void 0]})).filter((e=>!!e))))).then((()=>{document.querySelectorAll(\"style\").forEach((e=>{e.innerHTML=e.innerHTML.replace(/--marp-transition-duration:[^;}]*[;}]/g,(e=>e.slice(0,-1)+\"!important\"+e.slice(-1)))}))}));const r=(n,{back:r,cond:o})=>a=>{const i=t();if(i)return!!a[$e]||!(\"object\"!=typeof i||(i.skipTransition(),!a.forSync));if(!o(a))return!0;const s=e.slides[e.slide()],c=()=>a.back??r,l=\"data-transition\"+(c()?\"-back\":\"\"),d=s.querySelector(`section[${l}]`);if(!d)return!0;const u=xe(d.getAttribute(l)??void 0);return!u||((async(e,{builtinFallback:t=!0}={})=>{let n=await ye(e);if(be(n)){if(!t)return;return n=await ye(`__builtin__${e}`),be(n)?void 0:n}return n})(u.name,{builtinFallback:u.builtinFallback}).then((e=>{if(!e){t(!0);try{n(a)}finally{t(!1)}return}let r=e;Le.matches&&(console.warn(\"Use a constant animation to transition because preferring reduced motion by viewer has detected.\"),r=_e);const o=document.getElementById(ke);o&&o.remove();const i=document.createElement(\"style\");i.id=ke,document.head.appendChild(i),((e,t)=>{const n=[`:root{${ge(\"direction\")}:${t.backward?-1:1};}`,\":root:has(.bespoke-marp-inactive){cursor:none;}\"],r=t=>{const n=e[t].both?.defaultDuration||e[t].outgoing?.defaultDuration||e[t].incoming?.defaultDuration;return\"forward\"===t?n:n||r(\"forward\")},o=t.duration||r(t.backward?\"backward\":\"forward\");void 0!==o&&n.push(`::view-transition-group(*){${ve}:${o};}`);const a=e=>Object.entries(e).map((([e,t])=>`${e}:${t};`)).join(\"\");return n.push(`::view-transition-old(root){${a(we(e,{...t,type:\"outgoing\"}))}}`,`::view-transition-new(root){${a(we(e,{...t,type:\"incoming\"}))}}`),n})(r,{backward:c(),duration:u.duration}).forEach((e=>i.sheet?.insertRule(e)));const s=document.documentElement.classList;s.add(Ee);let l=!1;const d=()=>{l||(n(a),l=!0,s.remove(Ee))},f=()=>{t(!1),i.remove(),s.remove(Ee)};try{t(!0);const e=document.startViewTransition(d);t(e),e.finished.finally(f)}catch(e){console.error(e),d(),f()}})),!1)};e.on(\"prev\",r((t=>e.prev({...t,[$e]:!0})),{back:!0,cond:e=>e.index>0&&!((e.fragment??1)&&n.fragmentIndex>0)})),e.on(\"next\",r((t=>e.next({...t,[$e]:!0})),{cond:t=>t.index+1<e.slides.length&&!(n.fragmentIndex+1<n.fragments.length)})),setTimeout((()=>{e.on(\"slide\",r((t=>e.slide(t.index,{...t,[$e]:!0})),{cond:t=>{const n=e.slide();return t.index!==n&&(t.back=t.index<n,!0)}}))}),0),e.on(\"fragment\",(e=>{n=e}))};let Ie;const Me=()=>(void 0===Ie&&(Ie=\"wakeLock\"in navigator&&navigator.wakeLock),Ie),Oe=async()=>{const e=Me();if(e)try{return await e.request(\"screen\")}catch(e){console.warn(e)}return null},Ae=async()=>{if(!Me())return;let e;const t=()=>{e&&\"visible\"===document.visibilityState&&Oe()};for(const e of[\"visibilitychange\",\"fullscreenchange\"])document.addEventListener(e,t);return e=await Oe(),e};((e=document.getElementById(\":$p\"))=>{(()=>{const e=p(\"view\");a.dataset.bespokeView=e===l||e===c?e:\"\"})();const t=(e=>{const t=p(e);return v({[e]:void 0}),t})(\"sync\")||void 0;o.from(e,((...e)=>{const t=d.findIndex((e=>g()===e));return e.map((([e,n])=>e[t]&&n)).filter((e=>e))})([[1,1,0],re({key:t})],[[1,1,1],Z(e)],[[1,1,0],M],[[1,1,1],k],[[1,0,0],T()],[[1,1,1],O],[[1,1,1],ne({history:!1})],[[1,1,0],A()],[[1,1,0],P],[[1,0,0],ee],[[1,1,0],le()],[[1,0,0],C()],[[1,0,0],Te],[[1,1,1],$],[[1,1,0],Ae]))})()}();</script></body></html>", "theme": "default", "slide_count": 1, "format": "html", "gpu_used": false}, "response_time": 11.79667}}}