import React, { useState } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { 
  BookOpen, 
  Globe, 
  Youtube, 
  ExternalLink, 
  Check, 
  X, 
  PlusCircle, 
  ArrowRight, 
  RefreshCw, 
  <PERSON>rkles, 
  Settings2,
  HelpCircle
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Platform {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  connected: boolean;
  config?: Record<string, string>;
}

interface Integration {
  id: number;
  userId: number;
  platform: string;
  platformUserId?: string;
  accessToken?: string;
  refreshToken?: string;
  tokenExpiry?: string;
  config?: Record<string, string>;
  status: 'active' | 'expired' | 'error';
  createdAt: string;
  updatedAt: string;
}

interface ConnectionFormData {
  apiKey?: string;
  secret?: string;
  domain?: string;
  username?: string;
  password?: string;
}

export default function IntegrationsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("connected");
  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionData, setConnectionData] = useState<ConnectionFormData>({});
  const [platformDialogOpen, setPlatformDialogOpen] = useState(false);

  // Define the platforms
  const platforms: Platform[] = [
    {
      id: "udemy",
      name: "Udemy",
      icon: <BookOpen size={24} />,
      color: "bg-purple-600",
      description: "Publish your courses on Udemy's global learning marketplace",
      connected: false
    },
    {
      id: "teachable",
      name: "Teachable",
      icon: <BookOpen size={24} />,
      color: "bg-green-600",
      description: "Create and sell online courses on your own Teachable school",
      connected: false
    },
    {
      id: "kajabi",
      name: "Kajabi",
      icon: <Globe size={24} />,
      color: "bg-blue-600",
      description: "Publish your courses on Kajabi's all-in-one platform",
      connected: false
    },
    {
      id: "youtube",
      name: "YouTube",
      icon: <Youtube size={24} />,
      color: "bg-red-600",
      description: "Publish course videos to your YouTube channel",
      connected: false
    }
  ];

  // Fetch the user's integrations
  const { data: integrations, isLoading } = useQuery({
    queryKey: ['/api/integrations'],
    select: (data: Integration[]) => {
      return data;
    }
  });

  // Update the platforms with connection status
  const allPlatforms = platforms.map(platform => {
    const integration = integrations?.find(i => i.platform === platform.id);
    return {
      ...platform,
      connected: !!integration,
      config: integration?.config
    };
  });

  // Get connected and available platforms
  const connectedPlatforms = allPlatforms.filter(p => p.connected);
  const availablePlatforms = allPlatforms.filter(p => !p.connected);

  // Mutation to connect a platform
  const connectMutation = useMutation({
    mutationFn: async (data: { platform: string, connectionData: ConnectionFormData }) => {
      const response = await apiRequest('POST', '/api/integrations', {
        platform: data.platform,
        config: data.connectionData
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      toast({
        title: "Platform connected successfully",
        description: `${selectedPlatform?.name} has been connected to your account.`,
      });
      setPlatformDialogOpen(false);
      setConnectionData({});
      setSelectedPlatform(null);
      setIsConnecting(false);
    },
    onError: (error) => {
      toast({
        title: "Failed to connect platform",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
      setIsConnecting(false);
    }
  });

  // Mutation to disconnect a platform
  const disconnectMutation = useMutation({
    mutationFn: async (integrationId: number) => {
      const response = await apiRequest('DELETE', `/api/integrations/${integrationId}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/integrations'] });
      toast({
        title: "Platform disconnected",
        description: "The platform has been disconnected from your account.",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to disconnect platform",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    }
  });

  // Handle connecting a platform
  const handleConnectPlatform = (platform: Platform) => {
    setSelectedPlatform(platform);
    setPlatformDialogOpen(true);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setConnectionData({
      ...connectionData,
      [e.target.name]: e.target.value
    });
  };

  // Handle form submission
  const handleSubmitConnection = (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPlatform) return;

    setIsConnecting(true);
    connectMutation.mutate({
      platform: selectedPlatform.id,
      connectionData
    });
  };

  // Handle disconnecting a platform
  const handleDisconnectPlatform = (integration: Integration) => {
    if (confirm(`Are you sure you want to disconnect ${integration.platform}?`)) {
      disconnectMutation.mutate(integration.id);
    }
  };

  // Render connection form fields based on the selected platform
  const renderConnectionFields = () => {
    if (!selectedPlatform) return null;

    switch (selectedPlatform.id) {
      case 'udemy':
        return (
          <>
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="apiKey">Client ID</Label>
                <Input
                  id="apiKey"
                  name="apiKey"
                  placeholder="Enter your Udemy Client ID"
                  value={connectionData.apiKey || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="secret">Client Secret</Label>
                <Input
                  id="secret"
                  name="secret"
                  type="password"
                  placeholder="Enter your Udemy Client Secret"
                  value={connectionData.secret || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <div className="text-sm text-muted-foreground mt-4">
              <p>
                <a 
                  href="https://www.udemy.com/user/developer/apps/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:underline flex items-center"
                >
                  Get your API credentials from Udemy <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>
          </>
        );

      case 'teachable':
        return (
          <>
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="domain">School Domain</Label>
                <Input
                  id="domain"
                  name="domain"
                  placeholder="yourschool.teachable.com"
                  value={connectionData.domain || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <Input
                  id="apiKey"
                  name="apiKey"
                  placeholder="Enter your Teachable API Key"
                  value={connectionData.apiKey || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <div className="text-sm text-muted-foreground mt-4">
              <p>
                <a 
                  href="https://teachable.com/help/api" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:underline flex items-center"
                >
                  Learn how to get your API key <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>
          </>
        );

      case 'kajabi':
        return (
          <>
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="domain">Kajabi Site</Label>
                <Input
                  id="domain"
                  name="domain"
                  placeholder="your-site.mykajabi.com"
                  value={connectionData.domain || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <Input
                  id="apiKey"
                  name="apiKey"
                  placeholder="Enter your Kajabi API Key"
                  value={connectionData.apiKey || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="secret">API Secret</Label>
                <Input
                  id="secret"
                  name="secret"
                  type="password"
                  placeholder="Enter your Kajabi API Secret"
                  value={connectionData.secret || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <div className="text-sm text-muted-foreground mt-4">
              <p>
                <a 
                  href="https://help.kajabi.com/hc/en-us/articles/4405383795227-Set-up-your-Kajabi-API-key" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:underline flex items-center"
                >
                  Learn how to get your Kajabi API keys <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>
          </>
        );

      case 'youtube':
        return (
          <>
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <Input
                  id="apiKey"
                  name="apiKey"
                  placeholder="Enter your YouTube API Key"
                  value={connectionData.apiKey || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="secret">Client Secret</Label>
                <Input
                  id="secret"
                  name="secret"
                  type="password"
                  placeholder="Enter your YouTube Client Secret"
                  value={connectionData.secret || ''}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <div className="text-sm text-muted-foreground mt-4">
              <p>
                <a 
                  href="https://developers.google.com/youtube/v3/getting-started" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-primary hover:underline flex items-center"
                >
                  Get your YouTube API credentials <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </p>
            </div>
          </>
        );

      default:
        return (
          <div className="text-center py-4">
            <p className="text-muted-foreground">Please select a platform to connect.</p>
          </div>
        );
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Platform Integrations</h1>
          <p className="text-muted-foreground">Connect your course creation platform to external learning platforms</p>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline" size="icon">
                <HelpCircle className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent className="max-w-sm">
              <p>Connect to external platforms to publish your courses, videos, and other content directly from CourseAI.</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="connected">Connected Platforms</TabsTrigger>
          <TabsTrigger value="available">Available Platforms</TabsTrigger>
        </TabsList>

        <TabsContent value="connected" className="space-y-6 mt-6">
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : connectedPlatforms.length === 0 ? (
            <div className="text-center py-12 border rounded-lg bg-slate-50 dark:bg-slate-900">
              <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <PlusCircle className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Platforms Connected</h3>
              <p className="text-muted-foreground max-w-md mx-auto mb-6">
                You haven't connected any platforms yet. Connect to external platforms to publish your courses directly.
              </p>
              <Button onClick={() => setActiveTab("available")}>
                Connect a Platform
              </Button>
            </div>
          ) : (
            <div className="grid gap-6">
              {integrations?.map((integration) => {
                const platform = allPlatforms.find(p => p.id === integration.platform);
                if (!platform) return null;

                return (
                  <Card key={integration.id} className="overflow-hidden">
                    <CardHeader className={`${platform.color} text-white flex flex-row items-center space-x-4 p-4`}>
                      <div className="bg-white/20 p-2 rounded-lg">
                        {platform.icon}
                      </div>
                      <div>
                        <CardTitle>{platform.name}</CardTitle>
                        <CardDescription className="text-white/80">
                          {integration.status === 'active' ? 'Connected and active' : 'Connection needs attention'}
                        </CardDescription>
                      </div>
                      <div className="ml-auto">
                        <Badge className="bg-white text-gray-900">
                          {integration.status === 'active' ? (
                            <span className="flex items-center">
                              <Check className="h-3 w-3 mr-1" /> Active
                            </span>
                          ) : integration.status === 'expired' ? (
                            <span className="flex items-center">
                              <RefreshCw className="h-3 w-3 mr-1" /> Expired
                            </span>
                          ) : (
                            <span className="flex items-center">
                              <X className="h-3 w-3 mr-1" /> Error
                            </span>
                          )}
                        </Badge>
                      </div>
                    </CardHeader>

                    <CardContent className="p-6">
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground mb-2">Connected On</h4>
                          <p>{new Date(integration.createdAt).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium text-muted-foreground mb-2">Last Updated</h4>
                          <p>{new Date(integration.updatedAt).toLocaleDateString()}</p>
                        </div>
                        {integration.config?.domain && (
                          <div className="md:col-span-2">
                            <h4 className="text-sm font-medium text-muted-foreground mb-2">Domain</h4>
                            <p>{integration.config.domain}</p>
                          </div>
                        )}
                      </div>

                      {integration.status === 'active' && (
                        <div className="mt-6 bg-green-50 dark:bg-green-900/20 p-4 rounded-md">
                          <div className="flex items-center space-x-2 text-green-700 dark:text-green-400">
                            <Sparkles className="h-5 w-5" />
                            <h4 className="font-medium">Ready to publish content</h4>
                          </div>
                          <p className="text-sm text-green-600 dark:text-green-500 mt-1">
                            You can now publish your courses directly to {platform.name}
                          </p>
                        </div>
                      )}

                      <div className="flex items-center justify-between mt-6">
                        <Button variant="outline" className="space-x-2">
                          <Settings2 className="h-4 w-4" />
                          <span>Manage Settings</span>
                        </Button>
                        <Button 
                          variant="destructive" 
                          onClick={() => handleDisconnectPlatform(integration)}
                        >
                          Disconnect
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>

        <TabsContent value="available" className="space-y-6 mt-6">
          <div className="grid md:grid-cols-2 gap-6">
            {availablePlatforms.map((platform) => (
              <Card key={platform.id} className="overflow-hidden">
                <CardHeader className={`${platform.color} text-white p-4`}>
                  <div className="flex items-center space-x-3">
                    <div className="bg-white/20 p-2 rounded-lg">
                      {platform.icon}
                    </div>
                    <CardTitle>{platform.name}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <p className="text-muted-foreground mb-6">{platform.description}</p>
                  <Button 
                    className="w-full"
                    onClick={() => handleConnectPlatform(platform)}
                  >
                    Connect {platform.name}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      <Dialog open={platformDialogOpen} onOpenChange={setPlatformDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Connect to {selectedPlatform?.name}</DialogTitle>
            <DialogDescription>
              Enter your API credentials to connect with {selectedPlatform?.name}.
              This will allow you to publish content directly from CourseAI.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmitConnection} className="space-y-4 py-4">
            {renderConnectionFields()}
            
            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setPlatformDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isConnecting}>
                {isConnecting ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Connecting...
                  </>
                ) : (
                  <>Connect</>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}