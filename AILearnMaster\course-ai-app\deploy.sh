#!/bin/bash

# AI Course Builder - Complete Deployment Script
# Deploys the full-stack application with Modal A100 GPU backend

set -e

echo "🚀 Starting AI Course Builder Deployment"
echo "======================================="

# Check if Modal credentials are available
if [ -z "$MODAL_TOKEN_ID" ] || [ -z "$MODAL_TOKEN_SECRET" ]; then
    echo "⚠️  Modal credentials not found in environment"
    echo "Please set MODAL_TOKEN_ID and MODAL_TOKEN_SECRET"
    echo "Get credentials from: https://modal.com/tokens"
    exit 1
fi

echo "✅ Modal credentials found"

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd backend
pip install -r requirements.txt

# Deploy Modal A100 GPU backend
echo "🔥 Deploying Modal A100 GPU backend..."
modal deploy modal_gpu_backend.py

# Get Modal deployment URL
echo "🌐 Getting Modal deployment URL..."
MODAL_URL=$(modal app show course-ai-builder --json | jq -r '.url')
export MODAL_API_URL=$MODAL_URL

echo "✅ Modal backend deployed at: $MODAL_URL"

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd ../frontend
npm install

# Build React frontend
echo "🏗️  Building React frontend..."
npm run build

echo "✅ Frontend built successfully"

# Return to backend directory
cd ../backend

# Start the application server
echo "🚀 Starting AI Course Builder server..."
echo "Frontend: React app with tabbed interface"
echo "Backend: FastAPI with Modal A100 GPU integration"
echo "GPU Services: Stable Diffusion XL, SadTalker, Chatterbox TTS, Coqui TTS, Marp"
echo ""
echo "Application will be available at: http://0.0.0.0:8000"
echo "Modal GPU Backend: $MODAL_URL"
echo ""

# Start the server
python server.py