import axios from 'axios';
import { db } from '../db';
import { sql } from 'drizzle-orm';
import { stockMediaCache, mediaLibrary } from '@shared/schema';
import { eq, and, gt } from 'drizzle-orm';

interface StockPhoto {
  id: string;
  url: string;
  src: {
    original: string;
    large2x: string;
    large: string;
    medium: string;
    small: string;
    portrait: string;
    landscape: string;
    tiny: string;
  };
  width: number;
  height: number;
  photographer: string;
  photographer_url: string;
  photographer_id: number;
  avg_color: string;
  alt: string;
}

interface StockVideo {
  id: string;
  width: number;
  height: number;
  duration: number;
  user: {
    id: number;
    name: string;
    url: string;
  };
  video_files: Array<{
    id: number;
    quality: string;
    file_type: string;
    width: number;
    height: number;
    link: string;
  }>;
  video_pictures: Array<{
    id: number;
    picture: string;
    nr: number;
  }>;
}

interface PixabayImage {
  id: number;
  pageURL: string;
  type: string;
  tags: string;
  previewURL: string;
  previewWidth: number;
  previewHeight: number;
  webformatURL: string;
  webformatWidth: number;
  webformatHeight: number;
  largeImageURL: string;
  fullHDURL: string;
  vectorURL: string;
  views: number;
  downloads: number;
  collections: number;
  likes: number;
  comments: number;
  user_id: number;
  user: string;
  userImageURL: string;
}

interface PixabayVideo {
  id: number;
  pageURL: string;
  type: string;
  tags: string;
  duration: number;
  picture_id: string;
  videos: {
    large: { url: string; width: number; height: number; size: number; };
    medium: { url: string; width: number; height: number; size: number; };
    small: { url: string; width: number; height: number; size: number; };
    tiny: { url: string; width: number; height: number; size: number; };
  };
  views: number;
  downloads: number;
  likes: number;
  comments: number;
  user_id: number;
  user: string;
  userImageURL: string;
}

export class StockMediaService {
  private pexelsApiKey: string;
  private pixabayApiKey: string;
  private cacheExpiryHours = 24; // Cache results for 24 hours

  constructor() {
    this.pexelsApiKey = process.env.PEXELS_API_KEY || '';
    this.pixabayApiKey = process.env.PIXABAY_API_KEY || '';
    
    if (!this.pexelsApiKey || !this.pixabayApiKey) {
      console.warn('Stock media API keys not configured');
    }
  }

  // Check cache first, then fetch from APIs
  async searchPhotos(query: string, page: number = 1, perPage: number = 20): Promise<any> {
    try {
      // Check cache first
      const cached = await this.getCachedResults(query, 'photo', page);
      if (cached) {
        return cached;
      }

      // Fetch from both APIs in parallel
      const [pexelsResults, pixabayResults] = await Promise.allSettled([
        this.searchPexelsPhotos(query, page, perPage),
        this.searchPixabayPhotos(query, page, perPage)
      ]);

      const combinedResults = {
        photos: [],
        total_results: 0,
        page,
        per_page: perPage,
        next_page: '',
        prev_page: ''
      };

      // Process Pexels results
      if (pexelsResults.status === 'fulfilled' && pexelsResults.value) {
        const pexelsData = pexelsResults.value;
        combinedResults.photos.push(...pexelsData.photos.map((photo: StockPhoto) => ({
          id: `pexels_${photo.id}`,
          provider: 'provider_1',
          width: photo.width,
          height: photo.height,
          url: photo.src.medium,
          src: photo.src,
          photographer: photo.photographer,
          photographer_url: photo.photographer_url,
          alt: photo.alt,
          avg_color: photo.avg_color
        })));
        combinedResults.total_results += pexelsData.total_results || 0;
      }

      // Process Pixabay results  
      if (pixabayResults.status === 'fulfilled' && pixabayResults.value) {
        const pixabayData = pixabayResults.value;
        combinedResults.photos.push(...pixabayData.hits.map((image: PixabayImage) => ({
          id: `pixabay_${image.id}`,
          provider: 'provider_2',
          width: image.webformatWidth,
          height: image.webformatHeight,
          url: image.webformatURL,
          src: {
            original: image.largeImageURL,
            large: image.webformatURL,
            medium: image.webformatURL,
            small: image.previewURL,
            tiny: image.previewURL
          },
          photographer: image.user,
          photographer_url: image.pageURL,
          alt: image.tags,
          tags: image.tags.split(', ')
        })));
        combinedResults.total_results += pixabayData.totalHits || 0;
      }

      // Cache the results
      await this.cacheResults(query, 'photo', page, combinedResults);

      return combinedResults;
    } catch (error) {
      console.error('Error searching photos:', error);
      throw error;
    }
  }

  async searchVideos(query: string, page: number = 1, perPage: number = 20): Promise<any> {
    try {
      // Check cache first
      const cached = await this.getCachedResults(query, 'video', page);
      if (cached) {
        return cached;
      }

      // Fetch from both APIs in parallel
      const [pexelsResults, pixabayResults] = await Promise.allSettled([
        this.searchPexelsVideos(query, page, perPage),
        this.searchPixabayVideos(query, page, perPage)
      ]);

      const combinedResults = {
        videos: [],
        total_results: 0,
        page,
        per_page: perPage,
        next_page: '',
        prev_page: ''
      };

      // Process Pexels results
      if (pexelsResults.status === 'fulfilled' && pexelsResults.value) {
        const pexelsData = pexelsResults.value;
        combinedResults.videos.push(...pexelsData.videos.map((video: StockVideo) => ({
          id: `pexels_${video.id}`,
          provider: 'provider_1',
          width: video.width,
          height: video.height,
          duration: video.duration,
          url: video.video_files[0]?.link,
          video_files: video.video_files,
          video_pictures: video.video_pictures,
          user: video.user
        })));
        combinedResults.total_results += pexelsData.total_results || 0;
      }

      // Process Pixabay results
      if (pixabayResults.status === 'fulfilled' && pixabayResults.value) {
        const pixabayData = pixabayResults.value;
        combinedResults.videos.push(...pixabayData.hits.map((video: PixabayVideo) => ({
          id: `pixabay_${video.id}`,
          provider: 'provider_2',
          width: video.videos.large?.width || video.videos.medium?.width,
          height: video.videos.large?.height || video.videos.medium?.height,
          duration: video.duration,
          url: video.videos.large?.url || video.videos.medium?.url,
          video_files: [
            { quality: 'large', link: video.videos.large?.url, width: video.videos.large?.width, height: video.videos.large?.height },
            { quality: 'medium', link: video.videos.medium?.url, width: video.videos.medium?.width, height: video.videos.medium?.height },
            { quality: 'small', link: video.videos.small?.url, width: video.videos.small?.width, height: video.videos.small?.height }
          ].filter(file => file.link),
          preview_image: `https://i.vimeocdn.com/video/${video.picture_id}.jpg`,
          user: {
            name: video.user,
            url: video.pageURL
          },
          tags: video.tags.split(', ')
        })));
        combinedResults.total_results += pixabayData.totalHits || 0;
      }

      // Cache the results
      await this.cacheResults(query, 'video', page, combinedResults);

      return combinedResults;
    } catch (error) {
      console.error('Error searching videos:', error);
      throw error;
    }
  }

  private async searchPexelsPhotos(query: string, page: number, perPage: number) {
    if (!this.pexelsApiKey) return null;
    
    const response = await axios.get('https://api.pexels.com/v1/search', {
      headers: {
        'Authorization': this.pexelsApiKey
      },
      params: {
        query,
        page,
        per_page: perPage
      }
    });
    
    return response.data;
  }

  private async searchPixabayPhotos(query: string, page: number, perPage: number) {
    if (!this.pixabayApiKey) return null;
    
    const response = await axios.get('https://pixabay.com/api/', {
      params: {
        key: this.pixabayApiKey,
        q: query,
        image_type: 'photo',
        orientation: 'all',
        category: 'all',
        min_width: 0,
        min_height: 0,
        order: 'popular',
        page,
        per_page: perPage,
        safesearch: 'true'
      }
    });
    
    return response.data;
  }

  private async searchPexelsVideos(query: string, page: number, perPage: number) {
    if (!this.pexelsApiKey) return null;
    
    const response = await axios.get('https://api.pexels.com/videos/search', {
      headers: {
        'Authorization': this.pexelsApiKey
      },
      params: {
        query,
        page,
        per_page: perPage
      }
    });
    
    return response.data;
  }

  private async searchPixabayVideos(query: string, page: number, perPage: number) {
    if (!this.pixabayApiKey) return null;
    
    const response = await axios.get('https://pixabay.com/api/videos/', {
      params: {
        key: this.pixabayApiKey,
        q: query,
        video_type: 'all',
        category: 'all',
        min_width: 0,
        min_height: 0,
        order: 'popular',
        page,
        per_page: perPage,
        safesearch: 'true'
      }
    });
    
    return response.data;
  }

  private async getCachedResults(query: string, type: string, page: number) {
    try {
      const now = new Date();
      const cached = await db
        .select()
        .from(stockMediaCache)
        .where(
          and(
            eq(stockMediaCache.query, query.toLowerCase()),
            eq(stockMediaCache.type, type),
            eq(stockMediaCache.page, page),
            gt(stockMediaCache.expiresAt, now)
          )
        )
        .limit(1);

      return cached[0]?.results || null;
    } catch (error) {
      console.error('Error getting cached results:', error);
      return null;
    }
  }

  private async cacheResults(query: string, type: string, page: number, results: any) {
    try {
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + this.cacheExpiryHours);

      await db.insert(stockMediaCache).values({
        query: query.toLowerCase(),
        type,
        provider: 'combined',
        results,
        totalResults: results.total_results || 0,
        page,
        expiresAt
      });
    } catch (error) {
      console.error('Error caching results:', error);
    }
  }

  // Import media to user's library
  async importToLibrary(userId: number, mediaData: any, type: 'photo' | 'video'): Promise<any> {
    try {
      // Check if already imported
      const existing = await db
        .select()
        .from(mediaLibrary)
        .where(
          and(
            eq(mediaLibrary.sourceId, mediaData.id),
            eq(mediaLibrary.type, type === 'photo' ? 'image' : 'video')
          )
        )
        .limit(1);

      if (existing.length > 0) {
        // Update download count
        await db
          .update(mediaLibrary)
          .set({ 
            downloadCount: existing[0].downloadCount + 1 
          })
          .where(eq(mediaLibrary.id, existing[0].id));
        
        return existing[0];
      }

      // Import new media
      const imported = await db.insert(mediaLibrary).values({
        userId,
        name: type === 'photo' ? (mediaData.alt || `Stock Photo ${mediaData.id}`) : `Stock Video ${mediaData.id}`,
        type: type === 'photo' ? 'image' : 'video',
        mimeType: type === 'photo' ? 'image/jpeg' : 'video/mp4',
        fileSize: 0, // We don't have exact file size from APIs
        url: mediaData.url,
        source: type === 'photo' ? 'stock_photo' : 'stock_video',
        sourceId: mediaData.id,
        sourceData: mediaData,
        tags: mediaData.tags || [],
        category: 'stock',
        isPublic: true,
        downloadCount: 1,
        duration: type === 'video' ? mediaData.duration : null
      }).returning();

      return imported[0];
    } catch (error) {
      console.error('Error importing to library:', error);
      throw error;
    }
  }

  // Get popular/trending media from cache
  async getTrendingMedia(type: 'photo' | 'video', limit: number = 20): Promise<any> {
    try {
      const popularQueries = type === 'photo' 
        ? ['business', 'technology', 'education', 'nature', 'people', 'abstract']
        : ['business', 'technology', 'nature', 'abstract', 'motion', 'presentation'];

      const randomQuery = popularQueries[Math.floor(Math.random() * popularQueries.length)];
      
      if (type === 'photo') {
        return await this.searchPhotos(randomQuery, 1, limit);
      } else {
        return await this.searchVideos(randomQuery, 1, limit);
      }
    } catch (error) {
      console.error('Error getting trending media:', error);
      throw error;
    }
  }

  // Get user's imported media
  async getUserImportedMedia(userId: number, type?: 'photo' | 'video'): Promise<any> {
    try {
      let query = db
        .select()
        .from(mediaLibrary)
        .where(
          and(
            eq(mediaLibrary.userId, userId),
            eq(mediaLibrary.source, type === 'photo' ? 'stock_photo' : type === 'video' ? 'stock_video' : 'stock_photo')
          )
        );

      if (type) {
        query = query.where(eq(mediaLibrary.type, type === 'photo' ? 'image' : 'video'));
      }

      return await query.orderBy(mediaLibrary.createdAt);
    } catch (error) {
      console.error('Error getting user imported media:', error);
      throw error;
    }
  }
}

export const stockMediaService = new StockMediaService();