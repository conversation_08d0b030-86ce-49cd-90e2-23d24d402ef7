import express from 'express';
import { db } from '../db';
import { emailTemplates } from '@shared/schema';
import { eq, like, sql, desc } from 'drizzle-orm';
import { z } from 'zod';

const router = express.Router();

// Get all email templates with pagination and filtering
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;
    const category = req.query.category as string;
    const search = req.query.search as string;

    // Build query based on filters
    let query = db.select().from(emailTemplates);
    
    if (search) {
      query = query.where(
        sql`(${emailTemplates.name} LIKE ${`%${search}%`} OR 
             ${emailTemplates.description} LIKE ${`%${search}%`} OR 
             ${emailTemplates.subject} LIKE ${`%${search}%`} OR 
             ${emailTemplates.content} LIKE ${`%${search}%`})`
      );
    }
    
    if (category) {
      query = query.where(eq(emailTemplates.category, category));
    }

    // Count total templates for pagination
    const countQuery = db.select({ count: sql`count(*)` }).from(emailTemplates);
    
    if (search) {
      countQuery.where(
        sql`(${emailTemplates.name} LIKE ${`%${search}%`} OR 
             ${emailTemplates.description} LIKE ${`%${search}%`} OR 
             ${emailTemplates.subject} LIKE ${`%${search}%`} OR 
             ${emailTemplates.content} LIKE ${`%${search}%`})`
      );
    }
    
    if (category) {
      countQuery.where(eq(emailTemplates.category, category));
    }
    
    const [{ count }] = await countQuery;
    
    // Get templates with pagination
    const templates = await query
      .orderBy(desc(emailTemplates.updatedAt))
      .limit(limit)
      .offset(offset);

    // Get unique categories for filtering
    const categoriesResult = await db
      .select({ category: emailTemplates.category })
      .from(emailTemplates)
      .groupBy(emailTemplates.category);
    
    const categories = categoriesResult
      .map(row => row.category)
      .filter(Boolean) as string[];

    res.json({
      data: templates,
      categories,
      pagination: {
        page,
        limit,
        totalItems: Number(count),
        totalPages: Math.ceil(Number(count) / limit)
      }
    });
  } catch (error: any) {
    console.error('Error fetching email templates:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get a specific email template by ID
router.get('/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const [template] = await db
      .select()
      .from(emailTemplates)
      .where(eq(emailTemplates.id, id));

    if (!template) {
      return res.status(404).json({ error: 'Email template not found' });
    }

    res.json(template);
  } catch (error: any) {
    console.error('Error fetching email template:', error);
    res.status(500).json({ error: error.message });
  }
});

// Create a new email template
router.post('/', async (req, res) => {
  try {
    // Validate request body
    const emailTemplateSchema = z.object({
      name: z.string().min(1, { message: "Template name is required" }),
      description: z.string().optional(),
      subject: z.string().min(1, { message: "Email subject is required" }),
      content: z.string().min(1, { message: "Email content is required" }),
      htmlContent: z.string().min(1, { message: "HTML content is required" }),
      category: z.string().default("general"),
      tags: z.array(z.string()).optional(),
      isDefault: z.boolean().default(false),
    });

    const validatedData = emailTemplateSchema.parse(req.body);
    
    // If this is marked as default, unset other defaults
    if (validatedData.isDefault) {
      await db
        .update(emailTemplates)
        .set({ isDefault: false })
        .where(eq(emailTemplates.isDefault, true));
    }

    // Create new template
    const [newTemplate] = await db
      .insert(emailTemplates)
      .values({
        name: validatedData.name,
        description: validatedData.description || null,
        subject: validatedData.subject,
        content: validatedData.content,
        htmlContent: validatedData.htmlContent,
        category: validatedData.category,
        tags: validatedData.tags || [],
        isDefault: validatedData.isDefault,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    res.status(201).json(newTemplate);
  } catch (error: any) {
    console.error('Error creating email template:', error);
    if (error.name === 'ZodError') {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: error.message });
  }
});

// Update an existing email template
router.put('/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    
    // Validate request body
    const emailTemplateSchema = z.object({
      name: z.string().min(1, { message: "Template name is required" }),
      description: z.string().optional(),
      subject: z.string().min(1, { message: "Email subject is required" }),
      content: z.string().min(1, { message: "Email content is required" }),
      htmlContent: z.string().min(1, { message: "HTML content is required" }),
      category: z.string().default("general"),
      tags: z.array(z.string()).optional(),
      isDefault: z.boolean().default(false),
    });

    const validatedData = emailTemplateSchema.parse(req.body);
    
    // If this is marked as default, unset other defaults
    if (validatedData.isDefault) {
      await db
        .update(emailTemplates)
        .set({ isDefault: false })
        .where(eq(emailTemplates.isDefault, true));
    }

    // Update template
    const [updatedTemplate] = await db
      .update(emailTemplates)
      .set({
        name: validatedData.name,
        description: validatedData.description || null,
        subject: validatedData.subject,
        content: validatedData.content,
        htmlContent: validatedData.htmlContent,
        category: validatedData.category,
        tags: validatedData.tags || [],
        isDefault: validatedData.isDefault,
        updatedAt: new Date(),
      })
      .where(eq(emailTemplates.id, id))
      .returning();

    if (!updatedTemplate) {
      return res.status(404).json({ error: 'Email template not found' });
    }

    res.json(updatedTemplate);
  } catch (error: any) {
    console.error('Error updating email template:', error);
    if (error.name === 'ZodError') {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: error.message });
  }
});

// Delete an email template
router.delete('/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    
    // Check if template exists
    const [template] = await db
      .select()
      .from(emailTemplates)
      .where(eq(emailTemplates.id, id));

    if (!template) {
      return res.status(404).json({ error: 'Email template not found' });
    }

    // Delete the template
    await db
      .delete(emailTemplates)
      .where(eq(emailTemplates.id, id));

    res.status(200).json({ message: 'Email template deleted successfully' });
  } catch (error: any) {
    console.error('Error deleting email template:', error);
    res.status(500).json({ error: error.message });
  }
});

// Get the default template
router.get('/default', async (req, res) => {
  try {
    const [defaultTemplate] = await db
      .select()
      .from(emailTemplates)
      .where(eq(emailTemplates.isDefault, true));

    if (!defaultTemplate) {
      return res.status(404).json({ error: 'No default email template set' });
    }

    res.json(defaultTemplate);
  } catch (error: any) {
    console.error('Error fetching default email template:', error);
    res.status(500).json({ error: error.message });
  }
});

export default router;