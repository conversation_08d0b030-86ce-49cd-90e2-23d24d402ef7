import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { 
  AlertCircle, 
  MailCheck, 
  CheckCircle,
  Settings,
  Send,
  Edit,
  ExternalLink
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import EmailLayout from './layout';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';

// Form schema for test email
const testEmailSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

// Form schema for email settings
const emailSettingsSchema = z.object({
  fromName: z.string().min(1, { message: 'From name is required' }),
  fromEmail: z.string().email({ message: 'Please enter a valid email address' }),
  replyToEmail: z.string().email({ message: 'Please enter a valid email address' }).optional().or(z.literal('')),
  emailProvider: z.string().min(1, { message: 'Email provider is required' }),
  footerText: z.string().optional(),
  enableUnsubscribeLink: z.boolean().default(true),
  enableOpenTracking: z.boolean().default(true),
  enableClickTracking: z.boolean().default(true),
});

type TestEmailFormValues = z.infer<typeof testEmailSchema>;
type EmailSettingsFormValues = z.infer<typeof emailSettingsSchema>;

const EmailSettingsPage = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('general');
  const [showApiKeyDetails, setShowApiKeyDetails] = useState(false);
  
  // Query for email settings
  const { data: settingsData, isLoading: isSettingsLoading } = useQuery({
    queryKey: ['/api/email-settings'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/email-settings');
      return res.json();
    },
  });

  // Query for checking email service status
  const { data: emailStatusData, isLoading: isStatusLoading } = useQuery({
    queryKey: ['/api/email/status'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/email/status');
      return res.json();
    },
  });

  // Form for test email
  const testEmailForm = useForm<TestEmailFormValues>({
    resolver: zodResolver(testEmailSchema),
    defaultValues: {
      email: '',
    },
  });

  // Form for email settings
  const settingsForm = useForm<EmailSettingsFormValues>({
    resolver: zodResolver(emailSettingsSchema),
    defaultValues: {
      fromName: '',
      fromEmail: '',
      replyToEmail: '',
      emailProvider: 'resend',
      footerText: '',
      enableUnsubscribeLink: true,
      enableOpenTracking: true,
      enableClickTracking: true,
    },
  });

  // Update form values when settings data is loaded
  useEffect(() => {
    if (settingsData) {
      settingsForm.reset({
        fromName: settingsData.fromName || '',
        fromEmail: settingsData.fromEmail || '',
        replyToEmail: settingsData.replyToEmail || '',
        emailProvider: settingsData.emailProvider || 'resend',
        footerText: settingsData.footerText || '',
        enableUnsubscribeLink: settingsData.enableUnsubscribeLink !== false,
        enableOpenTracking: settingsData.enableOpenTracking !== false,
        enableClickTracking: settingsData.enableClickTracking !== false,
      });
    }
  }, [settingsData, settingsForm]);

  // Mutation for sending test email
  const testEmailMutation = useMutation({
    mutationFn: async (data: TestEmailFormValues) => {
      const res = await apiRequest('POST', '/api/email/test', data);
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: 'Test email sent',
        description: 'Please check your inbox for the test email.',
        variant: 'default',
      });
      testEmailForm.reset();
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to send test email',
        description: error.message || 'An error occurred while sending the test email.',
        variant: 'destructive',
      });
    },
  });

  // Mutation for updating email settings
  const updateSettingsMutation = useMutation({
    mutationFn: async (data: EmailSettingsFormValues) => {
      const res = await apiRequest('PATCH', '/api/email-settings', data);
      return res.json();
    },
    onSuccess: () => {
      toast({
        title: 'Settings updated',
        description: 'Your email settings have been updated successfully.',
        variant: 'default',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Failed to update settings',
        description: error.message || 'An error occurred while updating the settings.',
        variant: 'destructive',
      });
    },
  });

  // Submit handler for test email form
  const onTestEmailSubmit = (data: TestEmailFormValues) => {
    testEmailMutation.mutate(data);
  };

  // Submit handler for email settings form
  const onSettingsSubmit = (data: EmailSettingsFormValues) => {
    updateSettingsMutation.mutate(data);
  };

  const renderEmailServiceStatus = () => {
    if (isStatusLoading) {
      return (
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <Settings className="mr-2 h-5 w-5" />
              <Skeleton className="h-5 w-32" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-4/5" />
          </CardContent>
        </Card>
      );
    }

    if (!emailStatusData) return null;

    return (
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Settings className="mr-2 h-5 w-5" />
            Email Service Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          {emailStatusData.configured ? (
            <Alert className="bg-emerald-50 dark:bg-emerald-950/50">
              <CheckCircle className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
              <AlertTitle className="text-emerald-600 dark:text-emerald-400">Email service is configured</AlertTitle>
              <AlertDescription className="text-emerald-700 dark:text-emerald-300">
                Your email service (Resend) is properly configured and ready to send emails.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Email service not configured</AlertTitle>
              <AlertDescription>
                {emailStatusData.message || 'Please check your API key or contact support for assistance.'}
              </AlertDescription>
            </Alert>
          )}

          {emailStatusData.configured && emailStatusData.domains && (
            <div className="mt-4">
              <h4 className="text-sm font-medium mb-2">Verified Domains</h4>
              <div className="space-y-2">
                {emailStatusData.domains.length > 0 ? (
                  emailStatusData.domains.map((domain: { id: string, name: string, status: string }) => (
                    <div key={domain.id} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <span className="font-medium">{domain.name}</span>
                        <Badge className="ml-2" variant={domain.status === 'verified' ? 'default' : 'outline'}>
                          {domain.status === 'verified' ? 'Verified' : 'Pending'}
                        </Badge>
                      </div>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">No domains configured</p>
                )}
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <p className="text-sm text-muted-foreground">
            Need help with your email setup? <a href="#" className="underline">Visit our documentation</a>.
          </p>
        </CardFooter>
      </Card>
    );
  };

  const renderTestEmailSection = () => {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Send className="mr-2 h-5 w-5" />
            Send Test Email
          </CardTitle>
          <CardDescription>
            Send a test email to verify your sender settings are working correctly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...testEmailForm}>
            <form onSubmit={testEmailForm.handleSubmit(onTestEmailSubmit)} className="space-y-4">
              <FormField
                control={testEmailForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="flex space-x-2">
                        <Input 
                          placeholder="<EMAIL>" 
                          {...field} 
                          className="flex-1"
                        />
                        <Button 
                          type="submit" 
                          disabled={testEmailMutation.isPending}
                        >
                          <Send className="mr-2 h-4 w-4" />
                          Send Test
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </CardContent>
      </Card>
    );
  };

  return (
    <EmailLayout>
      <div className="space-y-6">
        {renderEmailServiceStatus()}
        
        {renderTestEmailSection()}
        
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <Settings className="mr-2 h-5 w-5" />
              Email Settings
            </CardTitle>
            <CardDescription>
              Configure your email settings for all outgoing messages
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-3 mb-4">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="delivery">Delivery</TabsTrigger>
                <TabsTrigger value="tracking">Tracking</TabsTrigger>
              </TabsList>
              
              <Form {...settingsForm}>
                <form onSubmit={settingsForm.handleSubmit(onSettingsSubmit)} className="space-y-4">
                  <TabsContent value="general" className="space-y-4">
                    <FormField
                      control={settingsForm.control}
                      name="fromName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>From Name</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="Your Company Name" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            The name that will appear in the sender field of your emails
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={settingsForm.control}
                      name="fromEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>From Email</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="<EMAIL>" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            The email address that will be used to send emails
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={settingsForm.control}
                      name="replyToEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Reply-To Email (Optional)</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="<EMAIL>" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            The email address that recipients will reply to
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={settingsForm.control}
                      name="emailProvider"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Provider</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select an email provider" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="resend">Resend</SelectItem>
                              <SelectItem value="sendgrid">SendGrid</SelectItem>
                              <SelectItem value="smtp">Custom SMTP</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            The service used to send your emails
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={settingsForm.control}
                      name="footerText"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Footer Text (Optional)</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="© 2025 Your Company Name. All rights reserved." 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Text that will appear in the footer of all your emails
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </TabsContent>
                  
                  <TabsContent value="delivery" className="space-y-4">
                    <div className="p-4 border rounded bg-muted/50 mb-4">
                      <h3 className="text-sm font-medium mb-2">API Credentials</h3>
                      
                      <div className="mb-4">
                        <label className="text-sm font-medium mb-1 block">Resend API Key</label>
                        <div className="flex">
                          <Input 
                            type={showApiKeyDetails ? "text" : "password"} 
                            value="••••••••••••••••••••••••••••••" 
                            disabled
                            className="font-mono text-sm"
                          />
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="ml-2" 
                            onClick={() => setShowApiKeyDetails(!showApiKeyDetails)}
                          >
                            {showApiKeyDetails ? "Hide" : "Show"}
                          </Button>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          API keys are managed by your system administrator
                        </p>
                      </div>
                      
                      <p className="text-sm text-muted-foreground">
                        Your API keys are securely stored and managed through environment variables.
                        Contact your system administrator if you need to update these credentials.
                      </p>
                    </div>
                    
                    <Separator className="my-4" />
                    
                    <div>
                      <h3 className="text-sm font-medium mb-2">Sending Limits</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Daily sending limit:</span>
                          <span className="font-medium">5,000 emails</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Monthly sending limit:</span>
                          <span className="font-medium">100,000 emails</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Rate limit:</span>
                          <span className="font-medium">50 emails/minute</span>
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2">
                        These limits are based on your current plan. 
                        <a href="#" className="ml-1 underline">Upgrade your plan</a> for higher limits.
                      </p>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="tracking" className="space-y-4">
                    <FormField
                      control={settingsForm.control}
                      name="enableUnsubscribeLink"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Unsubscribe Link</FormLabel>
                            <FormDescription>
                              Automatically add an unsubscribe link to all campaign emails
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={settingsForm.control}
                      name="enableOpenTracking"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Open Tracking</FormLabel>
                            <FormDescription>
                              Track when recipients open your emails
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={settingsForm.control}
                      name="enableClickTracking"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Click Tracking</FormLabel>
                            <FormDescription>
                              Track when recipients click links in your emails
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </TabsContent>
                  
                  <div className="pt-4 border-t">
                    <Button 
                      type="submit" 
                      className="w-full sm:w-auto"
                      disabled={updateSettingsMutation.isPending}
                    >
                      {updateSettingsMutation.isPending ? 'Saving...' : 'Save Settings'}
                    </Button>
                  </div>
                </form>
              </Form>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </EmailLayout>
  );
};

export default EmailSettingsPage;