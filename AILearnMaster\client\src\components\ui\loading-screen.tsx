import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON> } from './mascot';
import { cn } from '@/lib/utils';

interface LoadingScreenProps {
  text?: string;
  subText?: string;
  fullScreen?: boolean;
  duration?: number;
  className?: string;
  mascotExpression?: 'happy' | 'thinking' | 'surprised' | 'excited' | 'working';
  withProgressBar?: boolean;
  onComplete?: () => void;
  withFunFacts?: boolean;
  withTips?: boolean;
}

const funFacts = [
  "Visual learning increases retention by up to 400% compared to text-only learning.",
  "The brain processes visual information 60,000 times faster than text.",
  "Creating short micro-learning segments can improve knowledge retention by 20%.",
  "Spaced repetition can increase memory retention by up to 200%.",
  "Multimedia learning engages multiple parts of the brain for better recall.",
  "Including quizzes in courses improves long-term retention by 50%.",
  "The average human attention span is about 8 seconds, so keep your lessons engaging!",
  "Interactive content increases engagement rates by 70%.",
  "Students retain only 10% of what they read but 90% of what they do.",
  "The 'forgetting curve' shows we forget about 70% of new information within 24 hours."
];

const tips = [
  "Use our AI course structure generator to create your outline in seconds.",
  "Add micro-learning breaks to make longer lessons more digestible.",
  "Record high-quality voiceovers with our text-to-speech feature.",
  "Use the mind mapping tool to visualize course connections.",
  "Enhance engagement with animated videos for complex topics.",
  "Preview your course as a student to test the learning experience.",
  "Add quizzes after each module to reinforce learning.",
  "Use bright colors and bold text to highlight important information.",
  "Keep your videos under 6 minutes for maximum engagement.",
  "Enhance your course with AI-generated visuals and diagrams."
];

export function LoadingScreen({
  text = "Loading...",
  subText,
  fullScreen = false,
  duration,
  className,
  mascotExpression = 'working',
  withProgressBar = true,
  onComplete,
  withFunFacts = false,
  withTips = false
}: LoadingScreenProps) {
  const [progress, setProgress] = useState(0);
  const [funFact, setFunFact] = useState('');
  const [tip, setTip] = useState('');
  const [hidden, setHidden] = useState(false);

  useEffect(() => {
    // Select a random fun fact and tip
    if (withFunFacts) {
      const randomFact = funFacts[Math.floor(Math.random() * funFacts.length)];
      setFunFact(randomFact);
    }
    
    if (withTips) {
      const randomTip = tips[Math.floor(Math.random() * tips.length)];
      setTip(randomTip);
    }

    // If a duration is provided, animate progress bar and trigger onComplete
    if (duration) {
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            if (onComplete) {
              setTimeout(() => {
                setHidden(true);
                onComplete();
              }, 500);
            }
            return 100;
          }
          return prev + (100 / (duration / 100));
        });
      }, 100);

      return () => clearInterval(interval);
    }
  }, [duration, onComplete, withFunFacts, withTips]);

  if (hidden) return null;

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center bg-white/90 backdrop-blur-sm transition-all duration-500",
        fullScreen ? "fixed inset-0 z-50" : "p-8 rounded-xl shadow-lg",
        className
      )}
    >
      <div className="flex flex-col items-center max-w-md text-center">
        <div className="relative mb-4">
          <Mascot
            size="lg"
            expression={mascotExpression}
            animated={true}
            className="mb-4"
          />
          
          {/* Circular loading indicator around mascot with gradient */}
          <div className="absolute inset-0 -m-2">
            <svg className="w-full h-full" viewBox="0 0 100 100">
              <defs>
                <linearGradient id="circle-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="var(--primary)" />
                  <stop offset="100%" stopColor="#9f6aff" />
                </linearGradient>
              </defs>
              <circle
                className="text-slate-100"
                strokeWidth="3"
                stroke="currentColor"
                fill="transparent"
                r="45"
                cx="50"
                cy="50"
              />
              <circle
                stroke="url(#circle-gradient)"
                strokeWidth="3"
                strokeDasharray={283}
                strokeDashoffset={283 - (283 * progress) / 100}
                strokeLinecap="round"
                fill="transparent"
                r="45"
                cx="50"
                cy="50"
              />
            </svg>
          </div>
        </div>

        <h3 className="text-xl font-bold gradient-heading mb-2">{text}</h3>
        
        {subText && (
          <p className="text-sm text-gray-600 mb-6">{subText}</p>
        )}
        
        {withProgressBar && (
          <div className="w-64 h-3 bg-slate-100 rounded-full mb-4 relative overflow-hidden shadow-inner">
            <div 
              className="absolute top-0 left-0 h-full bg-gradient-to-r from-primary to-[#9f6aff] transition-all duration-300 rounded-full"
              style={{ width: `${progress}%` }}
            />
          </div>
        )}
        
        {withFunFacts && funFact && (
          <div className="mt-4 p-4 gradient-card rounded-lg max-w-xs shadow-md">
            <h4 className="text-sm font-semibold gradient-text mb-1">Did you know?</h4>
            <p className="text-xs text-gray-700">{funFact}</p>
          </div>
        )}
        
        {withTips && tip && (
          <div className="mt-4 p-4 gradient-card rounded-lg max-w-xs shadow-md">
            <h4 className="text-sm font-semibold gradient-text-reverse mb-1">Pro Tip</h4>
            <p className="text-xs text-gray-700">{tip}</p>
          </div>
        )}
      </div>
    </div>
  );
}