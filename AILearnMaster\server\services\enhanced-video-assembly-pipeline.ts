/**
 * Enhanced Video Assembly Pipeline
 * Advanced FFmpeg-based video assembly with scene transitions, captions, and quality optimization
 */

import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { awsS3Service } from './awsS3Service';

const execAsync = promisify(exec);

export interface VideoAssemblyOptions {
  courseId: number;
  lessonId: number;
  lessonTitle: string;
  courseType: 'traditional' | 'avatar';
  targetDuration: number; // seconds
  quality: 'standard' | 'high' | 'ultra';
  includeSubtitles: boolean;
  transitionStyle: 'fade' | 'slide' | 'zoom' | 'none';
  outputFormat: 'mp4' | 'webm';
}

export interface MediaAsset {
  id: string;
  type: 'video' | 'audio' | 'image' | 'slide' | 'avatar';
  localPath: string;
  url: string;
  duration?: number;
  startTime?: number;
  endTime?: number;
  metadata?: {
    resolution?: string;
    fps?: number;
    bitrate?: string;
    codec?: string;
  };
}

export interface VideoAssemblyResult {
  success: boolean;
  outputUrl: string;
  duration: number;
  fileSize: number;
  quality: {
    resolution: string;
    bitrate: string;
    fps: number;
  };
  processingTime: number;
  error?: string;
}

export class EnhancedVideoAssemblyPipeline {
  private readonly TEMP_DIR = path.join(process.cwd(), 'temp', 'video-assembly');
  private readonly MAX_PROCESSING_TIME = 600000; // 10 minutes
  private readonly QUALITY_PRESETS = {
    standard: {
      resolution: '1280x720',
      bitrate: '2000k',
      fps: 24,
      preset: 'medium'
    },
    high: {
      resolution: '1920x1080',
      bitrate: '4000k',
      fps: 30,
      preset: 'slow'
    },
    ultra: {
      resolution: '1920x1080',
      bitrate: '8000k',
      fps: 60,
      preset: 'veryslow'
    }
  };

  /**
   * Assemble video lesson with enhanced pipeline
   */
  async assembleVideo(
    mediaAssets: MediaAsset[],
    options: VideoAssemblyOptions
  ): Promise<VideoAssemblyResult> {
    const startTime = Date.now();
    const workingDir = path.join(this.TEMP_DIR, `${options.courseId}-${options.lessonId}-${Date.now()}`);

    try {
      // Create working directory
      await this.createWorkingDirectory(workingDir);

      // Validate and prepare media assets
      const preparedAssets = await this.validateAndPrepareAssets(mediaAssets, workingDir);

      // Generate video assembly plan
      const assemblyPlan = this.generateAssemblyPlan(preparedAssets, options);

      // Execute video assembly
      const outputPath = await this.executeVideoAssembly(assemblyPlan, workingDir, options);

      // Generate subtitles if requested
      if (options.includeSubtitles) {
        await this.generateSubtitles(outputPath, preparedAssets, workingDir);
      }

      // Optimize video quality
      const optimizedPath = await this.optimizeVideo(outputPath, workingDir, options);

      // Upload to S3
      const s3Url = await this.uploadToS3(optimizedPath, options);

      // Get video metadata
      const metadata = await this.getVideoMetadata(optimizedPath);

      // Cleanup
      await this.cleanup(workingDir);

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        outputUrl: s3Url,
        duration: metadata.duration,
        fileSize: metadata.fileSize,
        quality: {
          resolution: metadata.resolution,
          bitrate: metadata.bitrate,
          fps: metadata.fps
        },
        processingTime
      };

    } catch (error) {
      console.error('Video assembly failed:', error);
      await this.cleanup(workingDir);
      
      return {
        success: false,
        outputUrl: '',
        duration: 0,
        fileSize: 0,
        quality: { resolution: '', bitrate: '', fps: 0 },
        processingTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Create working directory
   */
  private async createWorkingDirectory(workingDir: string): Promise<void> {
    if (!fs.existsSync(workingDir)) {
      fs.mkdirSync(workingDir, { recursive: true });
    }
  }

  /**
   * Validate and prepare media assets
   */
  private async validateAndPrepareAssets(
    mediaAssets: MediaAsset[],
    workingDir: string
  ): Promise<MediaAsset[]> {
    const preparedAssets: MediaAsset[] = [];

    for (const asset of mediaAssets) {
      try {
        // Validate asset exists and is accessible
        if (!fs.existsSync(asset.localPath)) {
          console.warn(`Asset not found: ${asset.localPath}`);
          continue;
        }

        // Get asset metadata
        const metadata = await this.getAssetMetadata(asset.localPath);
        
        // Prepare asset for processing
        const preparedAsset = {
          ...asset,
          metadata: {
            ...asset.metadata,
            ...metadata
          }
        };

        preparedAssets.push(preparedAsset);

      } catch (error) {
        console.error(`Failed to prepare asset ${asset.id}:`, error);
      }
    }

    if (preparedAssets.length === 0) {
      throw new Error('No valid media assets found');
    }

    return preparedAssets;
  }

  /**
   * Generate video assembly plan
   */
  private generateAssemblyPlan(
    assets: MediaAsset[],
    options: VideoAssemblyOptions
  ): any {
    const audioAssets = assets.filter(a => a.type === 'audio');
    const videoAssets = assets.filter(a => a.type === 'video');
    const imageAssets = assets.filter(a => a.type === 'image');
    const slideAssets = assets.filter(a => a.type === 'slide');
    const avatarAssets = assets.filter(a => a.type === 'avatar');

    const plan = {
      courseType: options.courseType,
      targetDuration: options.targetDuration,
      quality: this.QUALITY_PRESETS[options.quality],
      layers: {
        background: this.planBackgroundLayer(videoAssets, imageAssets, slideAssets, options),
        avatar: this.planAvatarLayer(avatarAssets, options),
        audio: this.planAudioLayer(audioAssets, options),
        transitions: this.planTransitions(options),
        effects: this.planEffects(options)
      }
    };

    return plan;
  }

  /**
   * Plan background layer
   */
  private planBackgroundLayer(
    videoAssets: MediaAsset[],
    imageAssets: MediaAsset[],
    slideAssets: MediaAsset[],
    options: VideoAssemblyOptions
  ): any {
    if (options.courseType === 'avatar') {
      // Avatar courses use slides and static backgrounds
      return {
        type: 'slides',
        assets: slideAssets,
        duration: options.targetDuration,
        transitions: options.transitionStyle
      };
    } else {
      // Traditional courses use dynamic video backgrounds
      return {
        type: 'video_sequence',
        assets: videoAssets,
        fallbackImages: imageAssets,
        sceneChangeInterval: 45, // seconds
        transitions: options.transitionStyle
      };
    }
  }

  /**
   * Plan avatar layer
   */
  private planAvatarLayer(avatarAssets: MediaAsset[], options: VideoAssemblyOptions): any {
    if (options.courseType === 'avatar' && avatarAssets.length > 0) {
      return {
        enabled: true,
        asset: avatarAssets[0],
        position: 'center-right',
        size: '40%',
        chromaKey: true
      };
    }
    
    return { enabled: false };
  }

  /**
   * Plan audio layer
   */
  private planAudioLayer(audioAssets: MediaAsset[], options: VideoAssemblyOptions): any {
    if (audioAssets.length === 0) {
      throw new Error('No audio assets found');
    }

    return {
      primary: audioAssets[0],
      normalization: true,
      noiseReduction: true,
      fadeIn: 0.5,
      fadeOut: 0.5
    };
  }

  /**
   * Plan transitions
   */
  private planTransitions(options: VideoAssemblyOptions): any {
    const transitionDuration = 1.0; // seconds

    switch (options.transitionStyle) {
      case 'fade':
        return { type: 'fade', duration: transitionDuration };
      case 'slide':
        return { type: 'slide', duration: transitionDuration, direction: 'left' };
      case 'zoom':
        return { type: 'zoom', duration: transitionDuration, scale: 1.1 };
      default:
        return { type: 'none', duration: 0 };
    }
  }

  /**
   * Plan effects
   */
  private planEffects(options: VideoAssemblyOptions): any {
    return {
      colorCorrection: options.quality !== 'standard',
      stabilization: options.quality === 'ultra',
      sharpening: options.quality !== 'standard',
      logoOverlay: false // Can be enabled later
    };
  }

  /**
   * Execute video assembly
   */
  private async executeVideoAssembly(
    plan: any,
    workingDir: string,
    options: VideoAssemblyOptions
  ): Promise<string> {
    const outputPath = path.join(workingDir, `assembled.${options.outputFormat}`);
    
    // Build FFmpeg command based on plan
    const ffmpegCommand = this.buildFFmpegCommand(plan, outputPath);
    
    console.log('Executing FFmpeg command:', ffmpegCommand);
    
    // Execute with timeout
    try {
      const { stdout, stderr } = await execAsync(ffmpegCommand, {
        timeout: this.MAX_PROCESSING_TIME,
        cwd: workingDir
      });
      
      console.log('FFmpeg output:', stdout);
      if (stderr) console.log('FFmpeg stderr:', stderr);
      
    } catch (error) {
      console.error('FFmpeg execution failed:', error);
      throw error;
    }

    if (!fs.existsSync(outputPath)) {
      throw new Error('Video assembly failed - output file not created');
    }

    return outputPath;
  }

  /**
   * Build FFmpeg command
   */
  private buildFFmpegCommand(plan: any, outputPath: string): string {
    let command = 'ffmpeg -y '; // -y to overwrite output

    // Add inputs
    const inputs: string[] = [];
    const filters: string[] = [];

    // Background layer
    if (plan.layers.background.type === 'video_sequence') {
      plan.layers.background.assets.forEach((asset: MediaAsset, index: number) => {
        command += `-i "${asset.localPath}" `;
        inputs.push(`[${index}:v]`);
      });
    } else if (plan.layers.background.type === 'slides') {
      plan.layers.background.assets.forEach((asset: MediaAsset, index: number) => {
        command += `-i "${asset.localPath}" `;
        inputs.push(`[${index}:v]`);
      });
    }

    // Avatar layer
    if (plan.layers.avatar.enabled) {
      command += `-i "${plan.layers.avatar.asset.localPath}" `;
      const avatarIndex = inputs.length;
      inputs.push(`[${avatarIndex}:v]`);
    }

    // Audio layer
    command += `-i "${plan.layers.audio.primary.localPath}" `;
    const audioIndex = inputs.length;

    // Build filter complex
    let filterComplex = '';

    // Background processing
    if (plan.layers.background.type === 'video_sequence') {
      filterComplex += this.buildVideoSequenceFilter(plan.layers.background, inputs);
    } else {
      filterComplex += this.buildSlideSequenceFilter(plan.layers.background, inputs);
    }

    // Avatar overlay
    if (plan.layers.avatar.enabled) {
      filterComplex += this.buildAvatarOverlayFilter(plan.layers.avatar, inputs.length - 2);
    }

    // Audio processing
    filterComplex += this.buildAudioFilter(plan.layers.audio, audioIndex);

    // Add filter complex to command
    if (filterComplex) {
      command += `-filter_complex "${filterComplex}" `;
    }

    // Output settings
    const quality = plan.quality;
    command += `-c:v libx264 -preset ${quality.preset} `;
    command += `-b:v ${quality.bitrate} -r ${quality.fps} `;
    command += `-s ${quality.resolution} `;
    command += `-c:a aac -b:a 128k `;
    command += `-t ${plan.targetDuration} `;
    command += `"${outputPath}"`;

    return command;
  }

  /**
   * Build video sequence filter
   */
  private buildVideoSequenceFilter(backgroundLayer: any, inputs: string[]): string {
    if (inputs.length === 1) {
      return `${inputs[0]}scale=${backgroundLayer.resolution || '1920:1080'}[bg];`;
    }

    let filter = '';
    
    // Scale all inputs
    inputs.forEach((input, index) => {
      filter += `${input}scale=1920:1080[v${index}];`;
    });

    // Concatenate with transitions
    const videoInputs = inputs.map((_, index) => `[v${index}]`).join('');
    filter += `${videoInputs}concat=n=${inputs.length}:v=1:a=0[bg];`;

    return filter;
  }

  /**
   * Build slide sequence filter
   */
  private buildSlideSequenceFilter(backgroundLayer: any, inputs: string[]): string {
    let filter = '';
    
    // Convert slides to video
    inputs.forEach((input, index) => {
      filter += `${input}scale=1920:1080,loop=loop=-1:size=1:start=0[slide${index}];`;
    });

    // Create timed sequence
    const slideDuration = backgroundLayer.duration / inputs.length;
    let timelineFilter = '';
    
    inputs.forEach((_, index) => {
      const startTime = index * slideDuration;
      const endTime = (index + 1) * slideDuration;
      timelineFilter += `[slide${index}]trim=start=${startTime}:end=${endTime},setpts=PTS-STARTPTS[s${index}];`;
    });

    filter += timelineFilter;
    
    // Concatenate slides
    const slideInputs = inputs.map((_, index) => `[s${index}]`).join('');
    filter += `${slideInputs}concat=n=${inputs.length}:v=1:a=0[bg];`;

    return filter;
  }

  /**
   * Build avatar overlay filter
   */
  private buildAvatarOverlayFilter(avatarLayer: any, backgroundIndex: number): string {
    const position = this.getAvatarPosition(avatarLayer.position);
    return `[bg][${backgroundIndex}:v]overlay=${position}[final];`;
  }

  /**
   * Get avatar position coordinates
   */
  private getAvatarPosition(position: string): string {
    switch (position) {
      case 'center-right': return 'W-w-50:H/2-h/2';
      case 'center-left': return '50:H/2-h/2';
      case 'bottom-right': return 'W-w-50:H-h-50';
      case 'bottom-left': return '50:H-h-50';
      default: return 'W/2-w/2:H/2-h/2'; // center
    }
  }

  /**
   * Build audio filter
   */
  private buildAudioFilter(audioLayer: any, audioIndex: number): string {
    let filter = `[${audioIndex}:a]`;
    
    if (audioLayer.normalization) {
      filter += 'loudnorm,';
    }
    
    if (audioLayer.noiseReduction) {
      filter += 'afftdn,';
    }
    
    if (audioLayer.fadeIn > 0) {
      filter += `afade=t=in:d=${audioLayer.fadeIn},`;
    }
    
    if (audioLayer.fadeOut > 0) {
      filter += `afade=t=out:d=${audioLayer.fadeOut},`;
    }
    
    // Remove trailing comma
    filter = filter.replace(/,$/, '');
    filter += '[audio];';
    
    return filter;
  }

  /**
   * Generate subtitles using OpenAI Whisper
   */
  private async generateSubtitles(
    videoPath: string,
    assets: MediaAsset[],
    workingDir: string
  ): Promise<void> {
    try {
      const audioAsset = assets.find(a => a.type === 'audio');
      if (!audioAsset) return;

      const subtitlePath = path.join(workingDir, 'subtitles.srt');
      
      // Use Whisper to generate subtitles
      const whisperCommand = `whisper "${audioAsset.localPath}" --output_dir "${workingDir}" --output_format srt`;
      
      await execAsync(whisperCommand, { timeout: 120000 }); // 2 minutes timeout
      
      // Embed subtitles in video
      const outputWithSubs = path.join(workingDir, 'final_with_subs.mp4');
      const embedCommand = `ffmpeg -y -i "${videoPath}" -i "${subtitlePath}" -c copy -c:s mov_text "${outputWithSubs}"`;
      
      await execAsync(embedCommand, { timeout: 60000 });
      
      // Replace original with subtitled version
      fs.renameSync(outputWithSubs, videoPath);
      
    } catch (error) {
      console.error('Subtitle generation failed:', error);
      // Continue without subtitles
    }
  }

  /**
   * Optimize video quality
   */
  private async optimizeVideo(
    inputPath: string,
    workingDir: string,
    options: VideoAssemblyOptions
  ): Promise<string> {
    const optimizedPath = path.join(workingDir, `optimized.${options.outputFormat}`);
    
    const quality = this.QUALITY_PRESETS[options.quality];
    
    let optimizeCommand = `ffmpeg -y -i "${inputPath}" `;
    optimizeCommand += `-c:v libx264 -preset ${quality.preset} `;
    optimizeCommand += `-crf 23 -b:v ${quality.bitrate} `;
    optimizeCommand += `-c:a aac -b:a 128k `;
    optimizeCommand += `-movflags +faststart `;
    optimizeCommand += `"${optimizedPath}"`;
    
    try {
      await execAsync(optimizeCommand, { timeout: 300000 }); // 5 minutes
      return optimizedPath;
    } catch (error) {
      console.error('Video optimization failed:', error);
      return inputPath; // Return original if optimization fails
    }
  }

  /**
   * Upload video to S3
   */
  private async uploadToS3(videoPath: string, options: VideoAssemblyOptions): Promise<string> {
    const fileName = `courses/${options.courseId}/lessons/${options.lessonId}/${options.lessonTitle.replace(/[^a-zA-Z0-9]/g, '_')}.${options.outputFormat}`;
    return await awsS3Service.uploadFile(videoPath, fileName);
  }

  /**
   * Get asset metadata
   */
  private async getAssetMetadata(filePath: string): Promise<any> {
    try {
      const command = `ffprobe -v quiet -print_format json -show_format -show_streams "${filePath}"`;
      const { stdout } = await execAsync(command);
      return JSON.parse(stdout);
    } catch (error) {
      console.error('Failed to get asset metadata:', error);
      return {};
    }
  }

  /**
   * Get video metadata
   */
  private async getVideoMetadata(videoPath: string): Promise<any> {
    try {
      const metadata = await this.getAssetMetadata(videoPath);
      const videoStream = metadata.streams?.find((s: any) => s.codec_type === 'video');
      const stats = fs.statSync(videoPath);
      
      return {
        duration: parseFloat(metadata.format?.duration || '0'),
        fileSize: stats.size,
        resolution: videoStream ? `${videoStream.width}x${videoStream.height}` : 'unknown',
        bitrate: metadata.format?.bit_rate || 'unknown',
        fps: videoStream ? eval(videoStream.r_frame_rate) : 0
      };
    } catch (error) {
      console.error('Failed to get video metadata:', error);
      return {
        duration: 0,
        fileSize: 0,
        resolution: 'unknown',
        bitrate: 'unknown',
        fps: 0
      };
    }
  }

  /**
   * Cleanup working directory
   */
  private async cleanup(workingDir: string): Promise<void> {
    try {
      if (fs.existsSync(workingDir)) {
        fs.rmSync(workingDir, { recursive: true, force: true });
      }
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  }
}

export const enhancedVideoAssemblyPipeline = new EnhancedVideoAssemblyPipeline();
