import * as coquiTTS from './coqui-tts';
import * as kokoroTTS from './kokoro-tts';
import * as elevenLabsTTS from './elevenlabs';
import { coquiVoiceDiscovery, CoquiVoiceModel } from './coquiVoiceDiscovery';

export interface Voice {
  id: string;
  name: string;
  language: string;
  gender?: string;
  style?: string;
  service: 'coqui' | 'kokoro' | 'elevenlabs';
  description?: string;
  previewUrl?: string;
}

export interface GenerationOptions {
  speed?: number;
  emotion?: string;
  style?: string;
  stability?: number;
  similarityBoost?: number;
}

export interface GenerationResult {
  success: boolean;
  audioPath?: string;
  audioUrl?: string;
  provider: string;
  error?: string;
}

/**
 * Voice Service Manager - Handles provider hierarchy and fallbacks
 * Default: Coqui TTS → Fallback: Kokoro TTS → Premium: ElevenLabs
 */
export class VoiceServiceManager {
  private readonly providerOrder = ['coqui', 'kokoro', 'elevenlabs'] as const;

  /**
   * Get all available services with their voices
   */
  async getAllServices(): Promise<any[]> {
    const services = [];

    // Coqui TTS (Primary service) - Using dynamic discovery
    try {
      const coquiVoices = await coquiVoiceDiscovery.getAllVoices();
      services.push({
        name: 'Coqui TTS',
        id: 'coqui',
        status: 'available',
        voices: coquiVoices.map(v => ({
          id: v.id,
          name: v.name,
          language: v.language,
          gender: v.gender,
          style: v.style,
          quality: v.quality,
          description: v.description,
          category: v.category,
          dataset: v.dataset,
          architecture: v.architecture
        })),
        features: ['Open Source', 'Fast Generation', 'Custom Models', 'Dynamic Discovery'],
        voiceCount: coquiVoices.length
      });
    } catch (error) {
      console.log('Coqui TTS unavailable:', error.message);
      services.push({
        name: 'Coqui TTS',
        id: 'coqui',
        status: 'unavailable',
        voices: [],
        features: ['Open Source', 'Fast Generation', 'Custom Models', 'Dynamic Discovery'],
        voiceCount: 0
      });
    }

    // Kokoro TTS (Japanese focused)
    try {
      const kokoroVoices = await kokoroTTS.getAvailableVoices();
      services.push({
        name: 'Kokoro TTS',
        id: 'kokoro',
        status: 'available',
        voices: kokoroVoices.map(v => ({
          id: v.id,
          name: v.name,
          language: v.language,
          gender: v.gender
        })),
        features: ['Japanese Focused', 'Expressive', 'Natural Prosody']
      });
    } catch (error) {
      console.log('Kokoro TTS unavailable:', error.message);
      services.push({
        name: 'Kokoro TTS',
        id: 'kokoro',
        status: 'unavailable',
        voices: [],
        features: ['Japanese Focused', 'Expressive', 'Natural Prosody']
      });
    }

    // ElevenLabs (Premium service)
    try {
      const elevenLabsVoices = await elevenLabsTTS.getVoices();
      services.push({
        name: 'ElevenLabs',
        id: 'elevenlabs',
        status: 'available',
        voices: elevenLabsVoices.map(v => ({
          id: v.voice_id,
          name: v.name,
          language: 'en-US',
          gender: v.gender || 'unknown'
        })),
        features: ['High Quality', 'Multiple Languages', 'Voice Cloning']
      });
    } catch (error) {
      console.log('ElevenLabs unavailable:', error.message);
      services.push({
        name: 'ElevenLabs',
        id: 'elevenlabs',
        status: 'unavailable',
        voices: [],
        features: ['High Quality', 'Multiple Languages', 'Voice Cloning']
      });
    }

    return services;
  }

  /**
   * Get all available voices from all providers
   */
  async getAllVoices(): Promise<Voice[]> {
    const allVoices: Voice[] = [];

    try {
      // Get Coqui voices (primary) - Using dynamic discovery
      const coquiVoices = await coquiVoiceDiscovery.getAllVoices();
      allVoices.push(...coquiVoices.map(v => ({
        id: v.id,
        name: v.name,
        language: v.language,
        gender: v.gender,
        style: v.style,
        service: 'coqui' as const,
        description: v.description
      })));
    } catch (error) {
      console.log('Coqui TTS voices unavailable:', error.message);
    }

    try {
      // Get Kokoro voices (fallback)
      const kokoroVoices = await kokoroTTS.getAvailableVoices();
      allVoices.push(...kokoroVoices.map(v => ({
        id: v.id,
        name: v.name,
        language: v.language,
        gender: v.gender,
        style: v.style,
        service: 'kokoro' as const,
        description: `High-quality ${v.style} voice`
      })));
    } catch (error) {
      console.log('Kokoro TTS voices unavailable:', error.message);
    }

    try {
      // Get ElevenLabs voices (premium, if configured)
      if (process.env.ELEVENLABS_API_KEY) {
        const elevenLabsVoices = await elevenLabsTTS.getVoices();
        allVoices.push(...elevenLabsVoices.map(v => ({
          id: v.voice_id,
          name: v.name,
          language: 'en-US', // ElevenLabs default
          service: 'elevenlabs' as const,
          description: `Premium AI voice - ${v.name}`
        })));
      }
    } catch (error) {
      console.log('ElevenLabs voices unavailable:', error.message);
    }

    return allVoices;
  }

  /**
   * Generate voice using specific service and settings (for API routes)
   */
  async generateVoice(params: {
    service: string;
    voiceId: string;
    text: string;
    settings?: any;
  }): Promise<{ audioUrl?: string; success: boolean; error?: string }> {
    try {
      const outputPath = `uploads/voice_${Date.now()}.wav`;
      const result = await this.generateSpeech(params.text, params.voiceId, outputPath, params.settings || {});
      
      if (result.success) {
        return {
          success: true,
          audioUrl: result.audioUrl || `/api/audio/${outputPath.split('/').pop()}`
        };
      } else {
        return {
          success: false,
          error: result.error || 'Voice generation failed'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `Voice generation error: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Generate speech with specific provider and voice
   */
  async generateSpeech(
    text: string,
    voiceId: string,
    outputPath: string,
    options: GenerationOptions = {}
  ): Promise<GenerationResult> {
    // Determine which service the voice belongs to
    const voice = await this.findVoiceById(voiceId);
    if (!voice) {
      return {
        success: false,
        provider: 'unknown',
        error: `Voice ${voiceId} not found`
      };
    }

    // Try generation with the specified provider first
    const result = await this.tryGenerateWithProvider(
      voice.service,
      text,
      voiceId,
      outputPath,
      options
    );

    if (result.success) {
      return result;
    }

    // If the primary provider failed, try fallback providers
    for (const provider of this.providerOrder) {
      if (provider === voice.service) continue; // Skip the one we already tried

      console.log(`Falling back to ${provider} for voice generation`);
      
      // Find a suitable voice in the fallback provider
      const fallbackVoices = await this.getVoicesForProvider(provider);
      const fallbackVoice = this.findSimilarVoice(voice, fallbackVoices);
      
      if (fallbackVoice) {
        const fallbackResult = await this.tryGenerateWithProvider(
          provider,
          text,
          fallbackVoice.id,
          outputPath,
          options
        );
        
        if (fallbackResult.success) {
          return {
            ...fallbackResult,
            provider: `${provider} (fallback)`
          };
        }
      }
    }

    return {
      success: false,
      provider: 'all_failed',
      error: 'All TTS providers failed'
    };
  }

  /**
   * Generate preview audio for voice selection
   */
  async generatePreview(voiceId: string): Promise<Buffer | null> {
    const voice = await this.findVoiceById(voiceId);
    if (!voice) return null;

    const sampleText = "This is how your course content will sound with this voice.";

    try {
      switch (voice.service) {
        case 'kokoro':
          return await kokoroTTS.generatePreview(voiceId, sampleText);
        case 'coqui':
          // Coqui doesn't have a specific preview method, use regular generation
          const tempPath = `/tmp/preview_${Date.now()}.wav`;
          await coquiTTS.textToSpeech(sampleText, tempPath, voiceId);
          return require('fs').readFileSync(tempPath);
        case 'elevenlabs':
          // ElevenLabs preview would require API implementation
          return null;
        default:
          return null;
      }
    } catch (error) {
      console.error(`Preview generation failed for ${voiceId}:`, error);
      return null;
    }
  }

  private async findVoiceById(voiceId: string): Promise<Voice | null> {
    const allVoices = await this.getAllVoices();
    return allVoices.find(v => v.id === voiceId) || null;
  }

  private async getVoicesForProvider(provider: string): Promise<Voice[]> {
    const allVoices = await this.getAllVoices();
    return allVoices.filter(v => v.service === provider);
  }

  private findSimilarVoice(originalVoice: Voice, availableVoices: Voice[]): Voice | null {
    // Try to find a voice with similar characteristics
    const candidates = availableVoices.filter(v => 
      v.language === originalVoice.language ||
      v.gender === originalVoice.gender ||
      v.style === originalVoice.style
    );

    return candidates[0] || availableVoices[0] || null;
  }

  private async tryGenerateWithProvider(
    provider: string,
    text: string,
    voiceId: string,
    outputPath: string,
    options: GenerationOptions
  ): Promise<GenerationResult> {
    try {
      let audioPath: string;

      switch (provider) {
        case 'coqui':
          audioPath = await coquiTTS.textToSpeech(text, outputPath, voiceId);
          break;
        case 'kokoro':
          audioPath = await kokoroTTS.textToSpeech(text, outputPath, voiceId, {
            speed: options.speed,
            emotion: options.emotion,
            style: options.style
          });
          break;
        case 'elevenlabs':
          if (!process.env.ELEVENLABS_API_KEY) {
            throw new Error('ElevenLabs API key not configured');
          }
          audioPath = await elevenLabsTTS.textToSpeech(
            text,
            outputPath,
            voiceId,
            options.stability,
            options.similarityBoost
          );
          break;
        default:
          throw new Error(`Unknown provider: ${provider}`);
      }

      return {
        success: true,
        audioPath,
        audioUrl: audioPath.replace(process.cwd(), ''),
        provider
      };
    } catch (error) {
      console.error(`${provider} generation failed:`, error);
      return {
        success: false,
        provider,
        error: error.message
      };
    }
  }

  /**
   * Check which services are currently available
   */
  async getServiceStatus(): Promise<Record<string, boolean>> {
    const status: Record<string, boolean> = {};

    try {
      await coquiVoiceDiscovery.getAllVoices();
      status.coqui = true;
    } catch {
      status.coqui = false;
    }

    try {
      status.kokoro = await kokoroTTS.isServiceAvailable();
    } catch {
      status.kokoro = false;
    }

    status.elevenlabs = !!process.env.ELEVENLABS_API_KEY;

    return status;
  }

  /**
   * Get voice statistics across all services
   */
  async getVoiceStatistics(): Promise<{
    total: number;
    byService: { [key: string]: number };
    byLanguage: { [key: string]: number };
    byCategory: { [key: string]: number };
    byQuality: { [key: string]: number };
  }> {
    const allVoices = await this.getAllVoices();
    const stats = {
      total: allVoices.length,
      byService: {} as { [key: string]: number },
      byLanguage: {} as { [key: string]: number },
      byCategory: {} as { [key: string]: number },
      byQuality: {} as { [key: string]: number }
    };

    allVoices.forEach(voice => {
      // Count by service
      stats.byService[voice.service] = (stats.byService[voice.service] || 0) + 1;

      // Count by language
      stats.byLanguage[voice.language] = (stats.byLanguage[voice.language] || 0) + 1;

      // Count by category (if available)
      if ('category' in voice && voice.category) {
        stats.byCategory[voice.category] = (stats.byCategory[voice.category] || 0) + 1;
      }

      // Count by quality (if available)
      if ('quality' in voice && voice.quality) {
        stats.byQuality[voice.quality] = (stats.byQuality[voice.quality] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * Refresh Coqui TTS voice cache
   */
  async refreshCoquiVoices(): Promise<CoquiVoiceModel[]> {
    return await coquiVoiceDiscovery.refreshVoices();
  }

  /**
   * Get voices for a specific service
   */
  async getVoicesForService(service: string): Promise<Voice[]> {
    const allVoices = await this.getAllVoices();
    return allVoices.filter(voice => voice.service === service);
  }

  /**
   * Test a specific voice for availability
   */
  async testVoice(voiceId: string): Promise<{ available: boolean; error?: string }> {
    const voice = await this.findVoiceById(voiceId);
    if (!voice) {
      return { available: false, error: 'Voice not found' };
    }

    if (voice.service === 'coqui') {
      return await coquiVoiceDiscovery.testVoice(voiceId);
    }

    // For other services, we can implement similar testing
    return { available: true };
  }
}

// Export singleton instance
export const voiceServiceManager = new VoiceServiceManager();