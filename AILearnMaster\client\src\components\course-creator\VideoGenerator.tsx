import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Pause, 
  Video, 
  Volume2, 
  VolumeX,
  Download, 
  Upload,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  RotateCcw,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Scissors,
  Copy,
  FileVideo,
  Clock,
  Layers,
  ArrowLeft,
  ArrowRight,
  CheckCircle2,
  <PERSON>ert<PERSON><PERSON>cle,
  Loader2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useMediaAssignments } from '@/hooks/useMediaAssignments';

interface MediaItem {
  id: string;
  name: string;
  title?: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnailUrl?: string;
  duration?: number;
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
  };
}

interface VoiceData {
  audioUrl: string;
  duration: number;
  service: string;
  voice: string;
}

interface TimelineItem {
  id: string;
  type: 'media' | 'audio' | 'text';
  startTime: number;
  duration: number;
  content: MediaItem | VoiceData | string;
  layerIndex: number;
  selected?: boolean;
}

interface VideoProject {
  id: string;
  moduleId: string;
  lessonId: string;
  title: string;
  script: string;
  voiceData?: VoiceData;
  assignedMedia?: MediaItem;
  timeline: TimelineItem[];
  totalDuration: number;
  status: 'draft' | 'processing' | 'completed' | 'error';
  outputUrl?: string;
}

interface VideoGeneratorProps {
  modules: any[];
  scripts: any;
  voices: any[];
  generatedVoices?: Record<string, VoiceData>;
  onBack: () => void;
}

const VideoGenerator: React.FC<VideoGeneratorProps> = ({
  modules,
  scripts,
  voices,
  generatedVoices = {},
  onBack
}) => {
  const [selectedProject, setSelectedProject] = useState<VideoProject | null>(null);
  const [projects, setProjects] = useState<VideoProject[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [zoom, setZoom] = useState(100);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  
  const { toast } = useToast();
  const timelineRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  
  const { getModuleMedia, getLessonMedia } = useMediaAssignments();

  // Initialize projects from modules and scripts
  useEffect(() => {
    const initialProjects: VideoProject[] = [];
    
    modules.forEach(module => {
      module.lessons?.forEach((lesson: any) => {
        const script = scripts[module.id]?.[lesson.id];
        const voiceData = generatedVoices[`${module.id}-${lesson.id}`];
        const assignedMedia = getLessonMedia(module.id, lesson.id) || getModuleMedia(module.id);
        
        if (script) {
          const timeline: TimelineItem[] = [];
          let currentTime = 0;
          
          // Add voice audio to timeline
          if (voiceData) {
            timeline.push({
              id: `audio-${module.id}-${lesson.id}`,
              type: 'audio',
              startTime: 0,
              duration: voiceData.duration,
              content: voiceData,
              layerIndex: 0
            });
            currentTime = Math.max(currentTime, voiceData.duration);
          }
          
          // Add media to timeline
          if (assignedMedia) {
            const mediaDuration = assignedMedia.type === 'video' 
              ? assignedMedia.metadata?.duration || 30
              : voiceData?.duration || 30;
              
            timeline.push({
              id: `media-${module.id}-${lesson.id}`,
              type: 'media',
              startTime: 0,
              duration: mediaDuration,
              content: assignedMedia,
              layerIndex: 1
            });
            currentTime = Math.max(currentTime, mediaDuration);
          }
          
          initialProjects.push({
            id: `${module.id}-${lesson.id}`,
            moduleId: module.id,
            lessonId: lesson.id,
            title: lesson.title,
            script,
            voiceData,
            assignedMedia,
            timeline,
            totalDuration: currentTime,
            status: 'draft'
          });
        }
      });
    });
    
    setProjects(initialProjects);
    if (initialProjects.length > 0) {
      setSelectedProject(initialProjects[0]);
    }
  }, [modules, scripts, generatedVoices, getLessonMedia, getModuleMedia]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current || !selectedProject) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const timelineWidth = rect.width;
    const clickTime = (x / timelineWidth) * selectedProject.totalDuration;
    
    setCurrentTime(Math.max(0, Math.min(clickTime, selectedProject.totalDuration)));
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      setIsPlaying(false);
      if (audioRef.current) {
        audioRef.current.pause();
      }
    } else {
      setIsPlaying(true);
      if (audioRef.current && selectedProject?.voiceData) {
        audioRef.current.currentTime = currentTime;
        audioRef.current.play();
      }
    }
  };

  const updateTimelineItem = (itemId: string, updates: Partial<TimelineItem>) => {
    if (!selectedProject) return;
    
    const updatedTimeline = selectedProject.timeline.map(item =>
      item.id === itemId ? { ...item, ...updates } : item
    );
    
    const updatedProject = {
      ...selectedProject,
      timeline: updatedTimeline
    };
    
    setSelectedProject(updatedProject);
    setProjects(prev => prev.map(p => p.id === selectedProject.id ? updatedProject : p));
  };

  const addTimelineItem = (type: 'media' | 'audio' | 'text', content: any) => {
    if (!selectedProject) return;
    
    const newItem: TimelineItem = {
      id: `${type}-${Date.now()}`,
      type,
      startTime: currentTime,
      duration: type === 'media' ? 5 : 10,
      content,
      layerIndex: selectedProject.timeline.length
    };
    
    const updatedTimeline = [...selectedProject.timeline, newItem];
    const updatedProject = {
      ...selectedProject,
      timeline: updatedTimeline,
      totalDuration: Math.max(selectedProject.totalDuration, newItem.startTime + newItem.duration)
    };
    
    setSelectedProject(updatedProject);
    setProjects(prev => prev.map(p => p.id === selectedProject.id ? updatedProject : p));
  };

  const generateVideo = async (project: VideoProject) => {
    setIsGenerating(true);
    setGenerationProgress(0);
    
    try {
      // Simulate video generation process
      const response = await fetch('/api/ai/generate-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lessonId: project.lessonId,
          moduleId: project.moduleId,
          timeline: project.timeline,
          settings: {
            resolution: '1080p',
            fps: 30,
            format: 'mp4'
          }
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to start video generation');
      }
      
      const { jobId } = await response.json();
      
      // Poll for completion
      const pollStatus = async () => {
        const statusResponse = await fetch(`/api/ai/video-status/${jobId}`);
        const status = await statusResponse.json();
        
        setGenerationProgress(status.progress || 0);
        
        if (status.status === 'completed') {
          const updatedProject = {
            ...project,
            status: 'completed' as const,
            outputUrl: status.videoUrl
          };
          
          setProjects(prev => prev.map(p => p.id === project.id ? updatedProject : p));
          if (selectedProject?.id === project.id) {
            setSelectedProject(updatedProject);
          }
          
          toast({
            title: "Video Generated",
            description: `Video for "${project.title}" has been generated successfully.`
          });
          
          setIsGenerating(false);
        } else if (status.status === 'error') {
          throw new Error('Video generation failed');
        } else {
          setTimeout(pollStatus, 2000);
        }
      };
      
      setTimeout(pollStatus, 1000);
      
    } catch (error: any) {
      console.error('Video generation error:', error);
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate video",
        variant: "destructive"
      });
      setIsGenerating(false);
    }
  };

  const renderTimelineTrack = (items: TimelineItem[], layerIndex: number) => {
    const trackItems = items.filter(item => item.layerIndex === layerIndex);
    
    return (
      <div className="relative h-16 bg-muted/30 border-b border-border">
        {trackItems.map(item => {
          const leftPercent = (item.startTime / (selectedProject?.totalDuration || 1)) * 100;
          const widthPercent = (item.duration / (selectedProject?.totalDuration || 1)) * 100;
          
          return (
            <div
              key={item.id}
              className={`absolute h-12 mt-2 rounded cursor-pointer border-2 transition-colors ${
                selectedItems.has(item.id)
                  ? 'border-primary bg-primary/20'
                  : 'border-muted-foreground bg-background'
              } ${
                item.type === 'audio' 
                  ? 'bg-blue-100 border-blue-300'
                  : item.type === 'media'
                  ? 'bg-green-100 border-green-300'
                  : 'bg-yellow-100 border-yellow-300'
              }`}
              style={{
                left: `${leftPercent}%`,
                width: `${widthPercent}%`
              }}
              onClick={() => {
                const newSelected = new Set(selectedItems);
                if (newSelected.has(item.id)) {
                  newSelected.delete(item.id);
                } else {
                  newSelected.add(item.id);
                }
                setSelectedItems(newSelected);
              }}
            >
              <div className="p-1 h-full flex items-center text-xs font-medium truncate">
                {item.type === 'audio' ? (
                  <Volume2 className="h-3 w-3 mr-1" />
                ) : item.type === 'media' ? (
                  <Video className="h-3 w-3 mr-1" />
                ) : (
                  <FileVideo className="h-3 w-3 mr-1" />
                )}
                {typeof item.content === 'object' && 'name' in item.content 
                  ? item.content.name 
                  : item.type}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderProjectCard = (project: VideoProject) => (
    <Card 
      key={project.id} 
      className={`cursor-pointer transition-all ${
        selectedProject?.id === project.id ? 'ring-2 ring-primary' : ''
      }`}
      onClick={() => setSelectedProject(project)}
    >
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">{project.title}</CardTitle>
          <Badge variant={
            project.status === 'completed' ? 'default' :
            project.status === 'processing' ? 'secondary' :
            project.status === 'error' ? 'destructive' : 'outline'
          }>
            {project.status}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="flex items-center text-xs text-muted-foreground">
            <Clock className="h-3 w-3 mr-1" />
            {formatTime(project.totalDuration)}
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              {project.voiceData && (
                <Badge variant="outline" className="text-xs">
                  <Volume2 className="h-3 w-3 mr-1" />
                  Voice
                </Badge>
              )}
              {project.assignedMedia && (
                <Badge variant="outline" className="text-xs">
                  <Video className="h-3 w-3 mr-1" />
                  Media
                </Badge>
              )}
            </div>
            
            {project.status === 'completed' ? (
              <Button size="sm" variant="outline">
                <Download className="h-3 w-3 mr-1" />
                Download
              </Button>
            ) : (
              <Button 
                size="sm" 
                onClick={(e) => {
                  e.stopPropagation();
                  generateVideo(project);
                }}
                disabled={isGenerating || !project.voiceData}
              >
                <Video className="h-3 w-3 mr-1" />
                Generate
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h2 className="text-xl font-semibold">Video Generator</h2>
            <p className="text-muted-foreground">
              Combine voice and media to create engaging video lessons
            </p>
          </div>
        </div>
      </div>

      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 min-h-0">
        {/* Project List */}
        <div className="space-y-4">
          <h3 className="font-medium">Video Projects</h3>
          <ScrollArea className="h-[500px]">
            <div className="space-y-3 pr-4">
              {projects.map(renderProjectCard)}
            </div>
          </ScrollArea>
        </div>

        {/* Timeline Editor */}
        <div className="lg:col-span-2 space-y-4">
          {selectedProject ? (
            <>
              {/* Project Info */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{selectedProject.title}</CardTitle>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        Preview
                      </Button>
                      <Button 
                        size="sm" 
                        onClick={() => generateVideo(selectedProject)}
                        disabled={isGenerating || !selectedProject.voiceData}
                      >
                        {isGenerating ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <Video className="h-4 w-4 mr-1" />
                        )}
                        {isGenerating ? 'Generating...' : 'Generate Video'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Duration:</span> {formatTime(selectedProject.totalDuration)}
                    </div>
                    <div>
                      <span className="font-medium">Tracks:</span> {selectedProject.timeline.length}
                    </div>
                    <div>
                      <span className="font-medium">Status:</span> {selectedProject.status}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Generation Progress */}
              {isGenerating && (
                <Card>
                  <CardContent className="pt-6">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Generating video...</span>
                        <span>{Math.round(generationProgress)}%</span>
                      </div>
                      <Progress value={generationProgress} />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Timeline Controls */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline" onClick={handlePlayPause}>
                        {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                      <span className="text-sm font-mono">
                        {formatTime(currentTime)} / {formatTime(selectedProject.totalDuration)}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        <ZoomOut className="h-4 w-4" />
                      </Button>
                      <span className="text-sm">{zoom}%</span>
                      <Button size="sm" variant="outline">
                        <ZoomIn className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="space-y-2">
                    <div className="flex text-xs text-muted-foreground">
                      <div className="w-16">Track</div>
                      <div className="flex-1">Timeline</div>
                    </div>
                    
                    <div className="border rounded">
                      {/* Time ruler */}
                      <div className="h-6 bg-muted/50 border-b flex items-center text-xs">
                        <div className="w-16 px-2 border-r">Time</div>
                        <div 
                          className="flex-1 relative cursor-pointer"
                          ref={timelineRef}
                          onClick={handleTimelineClick}
                        >
                          {/* Time markers */}
                          {Array.from({ length: Math.ceil(selectedProject.totalDuration / 10) }, (_, i) => (
                            <div
                              key={i}
                              className="absolute border-l border-muted-foreground"
                              style={{ left: `${(i * 10 / selectedProject.totalDuration) * 100}%` }}
                            >
                              <span className="ml-1">{formatTime(i * 10)}</span>
                            </div>
                          ))}
                          
                          {/* Playhead */}
                          <div
                            className="absolute top-0 w-0.5 h-full bg-red-500 z-10"
                            style={{ left: `${(currentTime / selectedProject.totalDuration) * 100}%` }}
                          />
                        </div>
                      </div>
                      
                      {/* Audio Track */}
                      <div className="flex">
                        <div className="w-16 px-2 py-2 border-r bg-blue-50 text-xs font-medium">
                          Audio
                        </div>
                        <div className="flex-1">
                          {renderTimelineTrack(selectedProject.timeline, 0)}
                        </div>
                      </div>
                      
                      {/* Video Track */}
                      <div className="flex">
                        <div className="w-16 px-2 py-2 border-r bg-green-50 text-xs font-medium">
                          Video
                        </div>
                        <div className="flex-1">
                          {renderTimelineTrack(selectedProject.timeline, 1)}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-12">
                  <Video className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="font-medium mb-2">Select a Project</h3>
                  <p className="text-muted-foreground">
                    Choose a video project from the list to start editing
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Hidden audio element for playback */}
      {selectedProject?.voiceData && (
        <audio
          ref={audioRef}
          src={selectedProject.voiceData.audioUrl}
          onTimeUpdate={(e) => {
            if (isPlaying) {
              setCurrentTime(e.currentTarget.currentTime);
            }
          }}
          onEnded={() => setIsPlaying(false)}
        />
      )}
    </div>
  );
};

export default VideoGenerator;