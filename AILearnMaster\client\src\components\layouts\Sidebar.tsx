import { Link, useLocation } from "wouter";
import { User } from "@/types";

interface SidebarProps {
  user: User | null;
}

export function Sidebar({ user }: SidebarProps) {
  const [location] = useLocation();
  const isAdmin = user?.role === 'admin';

  // Define navigation item types
  type NavItem = {
    icon: string;
    label: string;
    path: string;
    divider?: undefined;
  };

  type DividerItem = {
    divider: true;
    icon?: undefined;
    label?: undefined;
    path?: undefined;
  };

  type SidebarItem = NavItem | DividerItem;

  // Regular user navigation items
  const userNavItems: SidebarItem[] = [
    { icon: "ri-dashboard-line", label: "Dashboard", path: "/dashboard" },
    { icon: "ri-video-add-line", label: "My Courses", path: "/my-courses" },
    { icon: "ri-team-line", label: "Teams", path: "/teams" },
    { icon: "ri-folder-shared-line", label: "Collaborations", path: "/collaborations" },
    { icon: "ri-magic-line", label: "AI Templates", path: "/ai-templates" },
    { icon: "ri-movie-line", label: "Media Library", path: "/media-library" },
    { icon: "ri-video-line", label: "Mini Course Generator", path: "/mini-course-creator" },
    { icon: "ri-mic-line", label: "AI Voice Generator", path: "/ai-voice-generator" },
    { icon: "ri-brain-line", label: "AI Learning Assistant", path: "/chatbot" },

    { icon: "ri-trophy-line", label: "Gamification", path: "/gamification" },
    { icon: "ri-settings-3-line", label: "AI Tools", path: "/ai-tools" },
    { icon: "ri-team-line", label: "Collaboration Hub", path: "/collaboration" },
    { icon: "ri-coin-line", label: "AI Credits", path: "/ai-credits" },
    { icon: "ri-link-m", label: "Platform Integrations", path: "/platform-integrations" },
    { icon: "ri-store-line", label: "Marketplace", path: "/marketplace" },
    { icon: "ri-price-tag-3-line", label: "Pricing", path: "/pricing" },
    { icon: "ri-settings-4-line", label: "Settings", path: "/settings" },
    { icon: "ri-question-line", label: "Help Center", path: "/help" },
  ];
  
  // Admin navigation items
  const adminNavItems: SidebarItem[] = [
    { icon: "ri-dashboard-line", label: "Admin Dashboard", path: "/admin-dashboard" },
    { icon: "ri-user-line", label: "User Management", path: "/admin/users" },
    { icon: "ri-funds-line", label: "Subscriptions", path: "/admin/subscriptions" },
    { icon: "ri-settings-4-line", label: "Platform Settings", path: "/admin/settings" },
    { icon: "ri-rocket-line", label: "AI Configuration", path: "/admin/ai-config" },
    { icon: "ri-cloud-line", label: "Cloud Resources", path: "/admin/resources" },
    { icon: "ri-bar-chart-line", label: "Analytics", path: "/admin/analytics" },
    { icon: "ri-store-line", label: "Marketplace Admin", path: "/admin/marketplace" },
    { icon: "ri-bank-line", label: "Billing Management", path: "/admin/billing" },
    { icon: "ri-question-line", label: "Help Center", path: "/help" },
    { divider: true },
    { icon: "ri-user-follow-line", label: "Switch to User View", path: "/" },
  ];

  // Choose which navigation items to display
  const navItems = isAdmin && location.startsWith('/admin') ? adminNavItems : 
                  isAdmin && location === '/admin-dashboard' ? adminNavItems :
                  userNavItems;

  return (
    <aside className="bg-white border-r border-slate-200 w-full md:w-64 md:fixed md:h-full z-10 shadow-sm">
      <div className="p-4 border-b border-slate-200">
        <div className="flex items-center space-x-2">
          <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-primary to-[#9f6aff] flex items-center justify-center shadow-md">
            <i className="ri-vidicon-line text-white text-lg"></i>
          </div>
          <h1 className="text-xl font-bold gradient-heading">
            Koursia {isAdmin && <span className="text-xs bg-amber-100 text-amber-800 px-1.5 py-0.5 rounded ml-1 gradient-text-reverse">ADMIN</span>}
          </h1>
        </div>
      </div>
      
      <nav className="p-4">
        <div className="space-y-1">
          {navItems.map((item, index) => 
            item.divider ? (
              <div key={`divider-${index}`} className="h-px bg-slate-200 my-3" />
            ) : (
              <Link 
                key={item.path} 
                href={item.path}
                className={`flex items-center space-x-3 rounded-md px-3 py-2 font-medium transition-all duration-200 ${
                  location === item.path
                    ? "nav-highlight active text-primary bg-primary/5 border-l-4 border-primary shadow-sm"
                    : "text-slate-700 hover:bg-slate-50 hover:text-primary"
                }`}
              >
                <i className={`${item.icon} ${location === item.path ? 'text-lg icon-colorful' : 'text-lg'}`}></i>
                <span>{item.label}</span>
              </Link>
            )
          )}
        </div>
        
        {/* Admin quick access menu */}
        {isAdmin && !location.startsWith('/admin') && location !== '/admin-dashboard' && (
          <div className="mt-4 rounded-md bg-gradient-to-r from-primary/10 to-[#9f6aff]/10 p-3 border border-primary/10 shadow-sm">
            <Link href="/admin-dashboard">
              <div className="flex items-center justify-between cursor-pointer hover:bg-white/50 p-2 rounded-md transition-all duration-200">
                <div className="flex items-center space-x-2">
                  <i className="ri-dashboard-line text-primary icon-colorful"></i>
                  <span className="text-sm font-medium gradient-text">Admin Dashboard</span>
                </div>
                <i className="ri-arrow-right-line text-primary"></i>
              </div>
            </Link>
          </div>
        )}
        
        {/* User profile section */}
        {user && (
          <div className="mt-8 pt-4 border-t border-slate-200">
            <div className="rounded-md gradient-card p-3">
              <Link href="/profile">
                <div className="flex items-center space-x-3 cursor-pointer hover:bg-white/80 p-2 rounded-md transition-all duration-200">
                  <span className="h-10 w-10 rounded-full bg-gradient-to-br from-primary to-[#9f6aff] flex items-center justify-center text-white font-medium shadow-md">
                    {user.name ? user.name.split(' ').map(n => n[0]).join('') : user.username.charAt(0).toUpperCase()}
                  </span>
                  <div>
                    <p className="text-sm font-medium text-slate-900">{user.name || user.username}</p>
                    <div className="flex items-center">
                      <p className="text-xs gradient-text font-medium">{user.plan} Plan</p>
                      {isAdmin && (
                        <span className="ml-2 text-xs px-1.5 py-0.5 rounded bg-gradient-to-r from-primary to-[#9f6aff] text-white font-medium">Admin</span>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        )}
      </nav>
    </aside>
  );
}
