import { <PERSON><PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  Edit, 
  Loader2, 
  MoreHorizontal, 
  Plus, 
  Save, 
  Trash2, 
  UserPlus, 
  Users
} from "lucide-react";
import { useEffect, useState } from "react";
import { useLocation, useParams } from "wouter";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>rigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useUser } from "@/hooks/use-user";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Define our types based on the database schema
type Team = {
  id: number;
  name: string;
  description: string | null;
  ownerId: number;
  avatarUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
};

type TeamMember = {
  teamId: number;
  userId: number;
  role: string;
  status: string;
  joinedAt: Date;
  user?: {
    id: number;
    username: string;
    name: string;
    email: string;
    avatarUrl: string | null;
  };
};

type Course = {
  id: number;
  title: string;
  description: string;
  status: string;
  category: string;
  thumbnailUrl: string | null;
  userId: number;
  completion: number;
  createdAt: Date;
};

export default function TeamDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const teamId = parseInt(id);
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState("members");
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedName, setEditedName] = useState("");
  const [editedDescription, setEditedDescription] = useState("");
  
  // Add member dialog state
  const [isAddMemberDialogOpen, setIsAddMemberDialogOpen] = useState(false);
  const [newMemberEmail, setNewMemberEmail] = useState("");
  const [newMemberRole, setNewMemberRole] = useState("member");
  
  // Add course dialog state
  const [isAddCourseDialogOpen, setIsAddCourseDialogOpen] = useState(false);
  const [selectedCourseId, setSelectedCourseId] = useState<number | null>(null);

  // Fetch team data
  const { 
    data: team, 
    isLoading: isTeamLoading, 
    error: teamError 
  } = useQuery<Team>({
    queryKey: [`/api/teams/${teamId}`],
    enabled: !isNaN(teamId),
  });

  // Fetch team members
  const {
    data: members,
    isLoading: isMembersLoading,
    error: membersError,
  } = useQuery<TeamMember[]>({
    queryKey: [`/api/teams/${teamId}/members`],
    enabled: !isNaN(teamId),
  });

  // Fetch team courses
  const {
    data: courses,
    isLoading: isCoursesLoading,
    error: coursesError,
  } = useQuery<Course[]>({
    queryKey: [`/api/teams/${teamId}/courses`],
    enabled: !isNaN(teamId),
  });

  // Fetch user's courses for adding to team
  const {
    data: userCourses,
    isLoading: isUserCoursesLoading,
  } = useQuery<Course[]>({
    queryKey: ["/api/courses"],
    enabled: isAddCourseDialogOpen, // Only fetch when dialog is open
  });

  // Set initial edit values when team data is loaded
  useEffect(() => {
    if (team) {
      setEditedName(team.name);
      setEditedDescription(team.description || "");
    }
  }, [team]);

  // Update team mutation
  const updateTeamMutation = useMutation({
    mutationFn: async (updatedTeam: { name: string; description: string | null }) => {
      const response = await apiRequest("PATCH", `/api/teams/${teamId}`, updatedTeam);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}`] });
      setIsEditMode(false);
      toast({
        title: "Team updated",
        description: "Team information has been updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update team",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });

  // Delete team mutation
  const deleteTeamMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("DELETE", `/api/teams/${teamId}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/teams"] });
      setLocation("/teams");
      toast({
        title: "Team deleted",
        description: "Team has been deleted successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to delete team",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });

  // Add member mutation
  const addMemberMutation = useMutation({
    mutationFn: async (memberData: { email: string; role: string }) => {
      const response = await apiRequest("POST", `/api/teams/${teamId}/members`, memberData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/members`] });
      setIsAddMemberDialogOpen(false);
      setNewMemberEmail("");
      setNewMemberRole("member");
      toast({
        title: "Member added",
        description: "Team member has been added successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to add member",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });

  // Remove member mutation
  const removeMemberMutation = useMutation({
    mutationFn: async (userId: number) => {
      const response = await apiRequest("DELETE", `/api/teams/${teamId}/members/${userId}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/members`] });
      toast({
        title: "Member removed",
        description: "Team member has been removed successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to remove member",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });

  // Update member role mutation
  const updateMemberRoleMutation = useMutation({
    mutationFn: async ({ userId, role }: { userId: number; role: string }) => {
      const response = await apiRequest("PATCH", `/api/teams/${teamId}/members/${userId}`, { role });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/members`] });
      toast({
        title: "Role updated",
        description: "Member role has been updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to update role",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });

  // Add course to team mutation
  const addCourseToTeamMutation = useMutation({
    mutationFn: async (courseId: number) => {
      const response = await apiRequest("POST", `/api/teams/${teamId}/courses`, { courseId });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/courses`] });
      setIsAddCourseDialogOpen(false);
      setSelectedCourseId(null);
      toast({
        title: "Course added",
        description: "Course has been added to the team successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to add course",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });

  // Remove course from team mutation
  const removeCourseFromTeamMutation = useMutation({
    mutationFn: async (courseId: number) => {
      const response = await apiRequest("DELETE", `/api/teams/${teamId}/courses/${courseId}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/courses`] });
      toast({
        title: "Course removed",
        description: "Course has been removed from the team successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Failed to remove course",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    },
  });

  // Check if the current user is the team owner
  const isOwner = team && user ? team.ownerId === user.id : false;
  
  // Check if current user is an admin
  const isAdmin = members?.find(member => member.userId === user?.id)?.role === "admin" || isOwner;

  // Handle team update
  const handleUpdateTeam = () => {
    if (!editedName.trim()) {
      toast({
        title: "Team name is required",
        description: "Please enter a name for your team",
        variant: "destructive",
      });
      return;
    }

    updateTeamMutation.mutate({
      name: editedName,
      description: editedDescription || null,
    });
  };

  // Handle team deletion confirmation
  const handleDeleteTeam = () => {
    const confirmed = window.confirm("Are you sure you want to delete this team? This action cannot be undone.");
    if (confirmed) {
      deleteTeamMutation.mutate();
    }
  };

  // Handle adding a new member
  const handleAddMember = () => {
    if (!newMemberEmail.trim()) {
      toast({
        title: "Email is required",
        description: "Please enter an email address",
        variant: "destructive",
      });
      return;
    }

    addMemberMutation.mutate({
      email: newMemberEmail,
      role: newMemberRole,
    });
  };

  // Handle adding a course to the team
  const handleAddCourseToTeam = () => {
    if (!selectedCourseId) {
      toast({
        title: "Select a course",
        description: "Please select a course to add to the team",
        variant: "destructive",
      });
      return;
    }

    addCourseToTeamMutation.mutate(selectedCourseId);
  };

  // Handle navigation back to teams page
  const navigateToTeams = () => {
    setLocation("/teams");
  };

  // Filter out courses that are already in the team
  const availableCourses = userCourses?.filter(
    (course) => !courses?.some((teamCourse) => teamCourse.id === course.id)
  );

  if (isTeamLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (teamError || !team) {
    return (
      <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="text-center py-10">
          <p className="text-red-500">Error loading team. Please try again later.</p>
          <Button onClick={navigateToTeams} variant="outline" className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Teams
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="flex items-center gap-2">
          <Button onClick={navigateToTeams} variant="outline" size="icon" className="h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold text-slate-900">Team Details</h1>
        </div>
        {isOwner && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setIsEditMode(!isEditMode)}>
                <Edit className="mr-2 h-4 w-4" />
                {isEditMode ? "Cancel Edit" : "Edit Team"}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDeleteTeam} className="text-red-500 focus:text-red-500">
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Team
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
                {team.avatarUrl ? (
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={team.avatarUrl} alt={team.name} />
                    <AvatarFallback>
                      <Users className="h-8 w-8 text-primary" />
                    </AvatarFallback>
                  </Avatar>
                ) : (
                  <Users className="h-8 w-8 text-primary" />
                )}
              </div>
              {isEditMode ? (
                <div className="space-y-2">
                  <Input
                    value={editedName}
                    onChange={(e) => setEditedName(e.target.value)}
                    className="text-xl font-bold"
                  />
                  <Textarea
                    value={editedDescription}
                    onChange={(e) => setEditedDescription(e.target.value)}
                    placeholder="No description provided"
                    className="text-sm text-slate-500 resize-none"
                  />
                </div>
              ) : (
                <div>
                  <h2 className="text-xl font-bold">{team.name}</h2>
                  <p className="text-sm text-slate-500">
                    {team.description || "No description provided"}
                  </p>
                </div>
              )}
            </div>
            {isEditMode && (
              <Button onClick={handleUpdateTeam} disabled={updateTeamMutation.isPending}>
                {updateTeamMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 text-sm">
            <div>
              <span className="font-medium text-slate-700">Created:</span>{" "}
              {new Date(team.createdAt).toLocaleDateString()}
            </div>
            <div>
              <span className="font-medium text-slate-700">Members:</span>{" "}
              {members?.length || 0}
            </div>
            <div>
              <span className="font-medium text-slate-700">Courses:</span>{" "}
              {courses?.length || 0}
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="courses">Courses</TabsTrigger>
        </TabsList>

        <TabsContent value="members" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Team Members</h2>
            {isAdmin && (
              <Dialog open={isAddMemberDialogOpen} onOpenChange={setIsAddMemberDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Add Member
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Add Team Member</DialogTitle>
                    <DialogDescription>
                      Invite someone to join this team by email.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="email" className="text-right">
                        Email
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        value={newMemberEmail}
                        onChange={(e) => setNewMemberEmail(e.target.value)}
                        className="col-span-3"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="role" className="text-right">
                        Role
                      </Label>
                      <Select
                        value={newMemberRole}
                        onValueChange={setNewMemberRole}
                      >
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="member">Member</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleAddMember} disabled={addMemberMutation.isPending}>
                      {addMemberMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        "Add Member"
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}
          </div>

          {isMembersLoading ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : membersError ? (
            <div className="text-center py-10">
              <p className="text-red-500">Error loading members. Please try again later.</p>
            </div>
          ) : members?.length === 0 ? (
            <div className="text-center py-10">
              <Users className="h-12 w-12 mx-auto text-slate-300" />
              <h3 className="mt-4 text-lg font-medium text-slate-900">No members</h3>
              <p className="mt-1 text-sm text-slate-500">This team does not have any members yet.</p>
            </div>
          ) : (
            <div className="space-y-3 mt-4">
              {members?.map((member) => (
                <div
                  key={member.userId}
                  className="flex items-center justify-between p-3 bg-white rounded-lg border"
                >
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarFallback>
                        {member.user?.name
                          ? member.user.name.split(" ").map((n) => n[0]).join("")
                          : member.user?.username.charAt(0).toUpperCase()}
                      </AvatarFallback>
                      {member.user?.avatarUrl && (
                        <AvatarImage src={member.user.avatarUrl} alt={member.user.name} />
                      )}
                    </Avatar>
                    <div>
                      <p className="font-medium">{member.user?.name || member.user?.username}</p>
                      <p className="text-sm text-slate-500">{member.user?.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm px-2 py-1 rounded-full bg-primary/10 text-primary">
                      {member.role}
                    </span>
                    {isAdmin && member.userId !== user?.id && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() =>
                              updateMemberRoleMutation.mutate({
                                userId: member.userId,
                                role: member.role === "admin" ? "member" : "admin",
                              })
                            }
                          >
                            Change to {member.role === "admin" ? "Member" : "Admin"}
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => removeMemberMutation.mutate(member.userId)}
                            className="text-red-500 focus:text-red-500"
                          >
                            Remove from Team
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="courses" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Team Courses</h2>
            {isAdmin && (
              <Dialog open={isAddCourseDialogOpen} onOpenChange={setIsAddCourseDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Course
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Add Course to Team</DialogTitle>
                    <DialogDescription>
                      Select one of your courses to add to this team.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4">
                    {isUserCoursesLoading ? (
                      <div className="flex justify-center items-center py-4">
                        <Loader2 className="h-6 w-6 animate-spin text-primary" />
                      </div>
                    ) : !userCourses || userCourses.length === 0 ? (
                      <p className="text-center text-slate-500 py-2">
                        You don't have any courses to add.
                      </p>
                    ) : !availableCourses || availableCourses.length === 0 ? (
                      <p className="text-center text-slate-500 py-2">
                        All your courses are already added to this team.
                      </p>
                    ) : (
                      <Select
                        value={selectedCourseId?.toString() || ""}
                        onValueChange={(value) => setSelectedCourseId(parseInt(value))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a course" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableCourses.map((course) => (
                            <SelectItem key={course.id} value={course.id.toString()}>
                              {course.title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                  <DialogFooter>
                    <Button 
                      onClick={handleAddCourseToTeam} 
                      disabled={
                        addCourseToTeamMutation.isPending || 
                        !selectedCourseId || 
                        !availableCourses || 
                        availableCourses.length === 0
                      }
                    >
                      {addCourseToTeamMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        "Add Course"
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            )}
          </div>

          {isCoursesLoading ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : coursesError ? (
            <div className="text-center py-10">
              <p className="text-red-500">Error loading courses. Please try again later.</p>
            </div>
          ) : courses?.length === 0 ? (
            <div className="text-center py-10">
              <div className="h-12 w-12 mx-auto text-slate-300 flex items-center justify-center">
                <i className="ri-book-open-line text-4xl"></i>
              </div>
              <h3 className="mt-4 text-lg font-medium text-slate-900">No courses</h3>
              <p className="mt-1 text-sm text-slate-500">
                This team does not have any courses yet.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
              {courses?.map((course) => (
                <Card key={course.id} className="overflow-hidden">
                  <div className="aspect-video relative bg-slate-100">
                    {course.thumbnailUrl ? (
                      <img
                        src={course.thumbnailUrl}
                        alt={course.title}
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <div className="flex items-center justify-center w-full h-full text-slate-400">
                        <i className="ri-image-line text-4xl"></i>
                      </div>
                    )}
                    <div className="absolute top-2 right-2">
                      <span className="text-xs px-2 py-1 rounded-full bg-white/90 text-slate-700 font-medium">
                        {course.status.charAt(0).toUpperCase() + course.status.slice(1)}
                      </span>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium line-clamp-1">{course.title}</h3>
                        <p className="text-sm text-slate-500 line-clamp-2 mt-1">
                          {course.description}
                        </p>
                      </div>
                      {isAdmin && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setLocation(`/courses/${course.id}`)}>
                              View Course
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => removeCourseFromTeamMutation.mutate(course.id)}
                              className="text-red-500 focus:text-red-500"
                            >
                              Remove from Team
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                    <Separator className="my-3" />
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-slate-500">
                        {new Date(course.createdAt).toLocaleDateString()}
                      </span>
                      <span className="text-primary font-medium">{course.category}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}