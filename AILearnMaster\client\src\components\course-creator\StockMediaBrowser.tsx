import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { 
  Search, 
  Download, 
  Eye, 
  X,
  Plus,
  Image,
  Video,
  CheckCircle2,
  Filter,
  Grid3X3,
  RefreshCw,
  ChevronDown,
  SlidersHorizontal,
  Square,
  RectangleHorizontal,
  RectangleVertical,
  Check,
  Play,
  Loader2,
  ExternalLink,
  Heart,
  Star
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface StockItem {
  id: string;
  type: 'photo' | 'video';
  url: string;
  webformatURL?: string;
  largeImageURL?: string;
  previewURL?: string;
  thumbnailUrl: string;
  title?: string;
  tags: string;
  user?: string;
  photographer?: string;
  photographer_url?: string;
  source: 'pexels' | 'pixabay';
  width: number;
  height: number;
  duration?: number;
  views?: number;
  downloads?: number;
  likes?: number;
  src?: {
    medium?: string;
    large?: string;
    original?: string;
  };
  videos?: {
    medium?: {
      url: string;
      width: number;
      height: number;
    };
  };
}

interface SearchFilters {
  orientation: 'all' | 'horizontal' | 'vertical' | 'square';
  category: string;
  minWidth: number;
  minHeight: number;
  color: string;
  safeSearch: boolean;
  mediaType: 'all' | 'photo' | 'video';
}

interface StockMediaBrowserProps {
  onMediaImported?: (media: StockItem[]) => void;
}

const orientationOptions = [
  { value: 'all', label: 'All Orientations', icon: Square },
  { value: 'horizontal', label: 'Landscape', icon: RectangleHorizontal },
  { value: 'vertical', label: 'Portrait', icon: RectangleVertical },
  { value: 'square', label: 'Square', icon: Square }
];

const categoryOptions = [
  'backgrounds', 'nature', 'people', 'animals', 'food', 'travel', 
  'technology', 'business', 'education', 'health', 'sports', 'music',
  'fashion', 'architecture', 'transportation', 'science', 'abstract'
];

const colorOptions = [
  { value: '', label: 'Any Color' },
  { value: 'grayscale', label: 'Grayscale' },
  { value: 'transparent', label: 'Transparent' },
  { value: 'red', label: 'Red' },
  { value: 'orange', label: 'Orange' },
  { value: 'yellow', label: 'Yellow' },
  { value: 'green', label: 'Green' },
  { value: 'turquoise', label: 'Turquoise' },
  { value: 'blue', label: 'Blue' },
  { value: 'lilac', label: 'Lilac' },
  { value: 'pink', label: 'Pink' },
  { value: 'white', label: 'White' },
  { value: 'gray', label: 'Gray' },
  { value: 'black', label: 'Black' },
  { value: 'brown', label: 'Brown' }
];

export default function StockMediaBrowser({ onMediaImported }: StockMediaBrowserProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // State management
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    orientation: 'all',
    category: '',
    minWidth: 0,
    minHeight: 0,
    color: '',
    safeSearch: true,
    mediaType: 'all'
  });

  // Results state
  const [currentPage, setCurrentPage] = useState(1);
  const [allResults, setAllResults] = useState<StockItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [previewItem, setPreviewItem] = useState<StockItem | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'masonry'>('grid');
  const [hasMoreResults, setHasMoreResults] = useState(true);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => setDebouncedQuery(searchQuery), 500);
    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Reset pagination when search changes
  useEffect(() => {
    setCurrentPage(1);
    setAllResults([]);
    setSelectedItems(new Set());
    setHasMoreResults(true);
  }, [debouncedQuery, filters]);

  // Fetch stock media
  const { data: searchResults, isLoading, error } = useQuery({
    queryKey: ['/api/stock/search', debouncedQuery, filters, currentPage],
    queryFn: async () => {
      if (!debouncedQuery.trim()) return { items: [], totalHits: 0 };
      
      const params = new URLSearchParams({
        q: debouncedQuery,
        page: currentPage.toString(),
        per_page: '40',
        orientation: filters.orientation,
        category: filters.category,
        min_width: filters.minWidth.toString(),
        min_height: filters.minHeight.toString(),
        color: filters.color,
        safesearch: filters.safeSearch.toString(),
        media_type: filters.mediaType
      });

      // Search both photos and videos from both sources
      const [pexelsPhotos, pexelsVideos, pixabayPhotos, pixabayVideos] = await Promise.all([
        filters.mediaType === 'video' ? Promise.resolve({ photos: [] }) : 
          fetch(`/api/pexels/photos?${params}`).then(r => r.ok ? r.json() : { photos: [] }),
        filters.mediaType === 'photo' ? Promise.resolve({ videos: [] }) : 
          fetch(`/api/pexels/videos?${params}`).then(r => r.ok ? r.json() : { videos: [] }),
        filters.mediaType === 'video' ? Promise.resolve({ hits: [] }) : 
          fetch(`/api/pixabay/photos?${params}`).then(r => r.ok ? r.json() : { hits: [] }),
        filters.mediaType === 'photo' ? Promise.resolve({ hits: [] }) : 
          fetch(`/api/pixabay/videos?${params}`).then(r => r.ok ? r.json() : { hits: [] })
      ]);

      // Normalize data from both sources
      const items: StockItem[] = [];

      // Process Pexels photos
      if (pexelsPhotos.photos) {
        items.push(...pexelsPhotos.photos.map((photo: any) => ({
          id: photo.id.toString(),
          type: 'photo' as const,
          url: photo.src.large,
          thumbnailUrl: photo.src.medium,
          title: photo.alt || '',
          tags: photo.alt || '',
          photographer: photo.photographer,
          photographer_url: photo.photographer_url,
          source: 'pexels' as const,
          width: photo.width,
          height: photo.height,
          src: photo.src
        })));
      }

      // Process Pexels videos
      if (pexelsVideos.videos) {
        items.push(...pexelsVideos.videos.map((video: any) => ({
          id: video.id.toString(),
          type: 'video' as const,
          url: video.video_files[0]?.link || '',
          thumbnailUrl: video.image,
          title: '',
          tags: '',
          photographer: video.user?.name || '',
          source: 'pexels' as const,
          width: video.width,
          height: video.height,
          duration: video.duration,
          videos: {
            medium: {
              url: video.video_files[0]?.link || '',
              width: video.width,
              height: video.height
            }
          }
        })));
      }

      // Process Pixabay photos
      if (pixabayPhotos.hits) {
        items.push(...pixabayPhotos.hits.map((photo: any) => ({
          id: photo.id.toString(),
          type: 'photo' as const,
          url: photo.largeImageURL,
          webformatURL: photo.webformatURL,
          previewURL: photo.previewURL,
          thumbnailUrl: photo.webformatURL,
          title: photo.tags,
          tags: photo.tags,
          user: photo.user,
          source: 'pixabay' as const,
          width: photo.imageWidth,
          height: photo.imageHeight,
          views: photo.views,
          downloads: photo.downloads,
          likes: photo.likes
        })));
      }

      // Process Pixabay videos
      if (pixabayVideos.hits) {
        items.push(...pixabayVideos.hits.map((video: any) => ({
          id: video.id.toString(),
          type: 'video' as const,
          url: video.videos?.medium?.url || '',
          thumbnailUrl: video.picture_id,
          title: video.tags,
          tags: video.tags,
          user: video.user,
          source: 'pixabay' as const,
          width: video.videos?.medium?.width || 0,
          height: video.videos?.medium?.height || 0,
          duration: video.duration,
          views: video.views,
          downloads: video.downloads,
          likes: video.likes,
          videos: video.videos
        })));
      }

      // Shuffle and limit results
      const shuffledItems = items.sort(() => Math.random() - 0.5);
      const totalHits = Math.min(shuffledItems.length, 1000); // Reasonable limit

      return {
        items: shuffledItems.slice(0, 40),
        totalHits,
        hasMore: shuffledItems.length >= 40 && currentPage * 40 < totalHits
      };
    },
    enabled: !!debouncedQuery.trim()
  });

  // Handle search results
  useEffect(() => {
    if (searchResults) {
      if (currentPage === 1) {
        setAllResults(searchResults.items || []);
      } else {
        setAllResults(prev => [...prev, ...(searchResults.items || [])]);
      }
      setHasMoreResults(searchResults.hasMore || false);
    }
  }, [searchResults, currentPage]);

  // Infinite scroll
  const loadMore = useCallback(() => {
    if (!isLoading && hasMoreResults) {
      setCurrentPage(prev => prev + 1);
    }
  }, [isLoading, hasMoreResults]);

  // Intersection observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [loadMore]);

  // Import selected media
  const importMutation = useMutation({
    mutationFn: async (items: StockItem[]) => {
      const response = await apiRequest('POST', '/api/stock/import', {
        items: items.map(item => ({
          id: item.id,
          type: item.type,
          url: item.url,
          thumbnailUrl: item.thumbnailUrl,
          title: item.title || `${item.source} ${item.type}`,
          tags: item.tags ? item.tags.split(',').map(t => t.trim()) : [],
          photographer: item.photographer || item.user,
          source: item.source,
          width: item.width,
          height: item.height,
          duration: item.duration
        }))
      });
      return response;
    },
    onSuccess: (data: any) => {
      toast({
        title: "Media Imported Successfully",
        description: `${selectedItems.size} items added to your Media Library`,
      });
      setSelectedItems(new Set());
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      if (onMediaImported) {
        onMediaImported(data?.importedItems || []);
      }
    },
    onError: (error: any) => {
      toast({
        title: "Import Failed",
        description: error.message || "Failed to import selected media",
        variant: "destructive",
      });
    }
  });

  const handleImportSelected = () => {
    const selectedMedia = allResults.filter(item => selectedItems.has(item.id));
    if (selectedMedia.length === 0) {
      toast({
        title: "No Items Selected",
        description: "Please select some media to import",
        variant: "destructive",
      });
      return;
    }
    importMutation.mutate(selectedMedia);
  };

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedItems.size === allResults.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(allResults.map(item => item.id)));
    }
  };

  const getImageUrl = (item: StockItem) => {
    if (item.source === 'pexels') {
      return item.src?.medium || item.thumbnailUrl;
    }
    return item.webformatURL || item.thumbnailUrl;
  };

  const getHighResUrl = (item: StockItem) => {
    if (item.source === 'pexels') {
      return item.src?.large || item.url;
    }
    return item.largeImageURL || item.url;
  };

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for professional photos and videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <SlidersHorizontal className="h-4 w-4" />
            Filters
            <ChevronDown className={`h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </Button>
          <Button
            variant="outline"
            onClick={() => setViewMode(viewMode === 'grid' ? 'masonry' : 'grid')}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <Card>
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Media Type</label>
                  <Select
                    value={filters.mediaType}
                    onValueChange={(value: any) => setFilters(prev => ({ ...prev, mediaType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Media</SelectItem>
                      <SelectItem value="photo">Photos Only</SelectItem>
                      <SelectItem value="video">Videos Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Orientation</label>
                  <Select
                    value={filters.orientation}
                    onValueChange={(value: any) => setFilters(prev => ({ ...prev, orientation: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {orientationOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <option.icon className="h-4 w-4" />
                            {option.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Category</label>
                  <Select
                    value={filters.category}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Categories</SelectItem>
                      {categoryOptions.map(category => (
                        <SelectItem key={category} value={category}>
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Color</label>
                  <Select
                    value={filters.color}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, color: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {colorOptions.map(color => (
                        <SelectItem key={color.value} value={color.value}>
                          <div className="flex items-center gap-2">
                            {color.value && color.value !== 'grayscale' && color.value !== 'transparent' && (
                              <div 
                                className="w-4 h-4 rounded border"
                                style={{ backgroundColor: color.value }}
                              />
                            )}
                            {color.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Minimum Size</label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="Width"
                      value={filters.minWidth || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, minWidth: Number(e.target.value) || 0 }))}
                      className="text-xs"
                    />
                    <Input
                      type="number"
                      placeholder="Height"
                      value={filters.minHeight || ''}
                      onChange={(e) => setFilters(prev => ({ ...prev, minHeight: Number(e.target.value) || 0 }))}
                      className="text-xs"
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="safeSearch"
                    checked={filters.safeSearch}
                    onCheckedChange={(checked) => setFilters(prev => ({ ...prev, safeSearch: checked === true }))}
                  />
                  <label htmlFor="safeSearch" className="text-sm">Safe search</label>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setFilters({
                    orientation: 'all',
                    category: '',
                    minWidth: 0,
                    minHeight: 0,
                    color: '',
                    safeSearch: true,
                    mediaType: 'all'
                  })}
                >
                  Reset Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Results Header */}
      {allResults.length > 0 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <p className="text-sm text-muted-foreground">
              {allResults.length} results found
            </p>
            {selectedItems.size > 0 && (
              <Badge variant="secondary">
                {selectedItems.size} selected
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSelectAll}
              disabled={allResults.length === 0}
            >
              {selectedItems.size === allResults.length ? 'Deselect All' : 'Select All'}
            </Button>
            {selectedItems.size > 0 && (
              <Button
                onClick={handleImportSelected}
                disabled={importMutation.isPending}
                size="sm"
              >
                {importMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Importing...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Import Selected ({selectedItems.size})
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Results Grid */}
      {debouncedQuery && (
        <div className="space-y-6">
          {allResults.length > 0 ? (
            <>
              <div className={`grid gap-4 ${
                viewMode === 'grid' 
                  ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5' 
                  : 'columns-2 md:columns-3 lg:columns-4 xl:columns-5'
              }`}>
                {allResults.map((item) => (
                  <MediaCard
                    key={`${item.source}-${item.id}`}
                    item={item}
                    isSelected={selectedItems.has(item.id)}
                    onSelect={() => handleSelectItem(item.id)}
                    onPreview={() => setPreviewItem(item)}
                    getImageUrl={getImageUrl}
                    viewMode={viewMode}
                  />
                ))}
              </div>

              {/* Load More Trigger */}
              {hasMoreResults && (
                <div ref={loadMoreRef} className="flex justify-center py-8">
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-6 w-6 animate-spin" />
                      <span>Loading more results...</span>
                    </div>
                  ) : (
                    <Button variant="outline" onClick={loadMore}>
                      Load More Results
                    </Button>
                  )}
                </div>
              )}
            </>
          ) : isLoading ? (
            <div className="flex justify-center py-12">
              <div className="flex items-center gap-2">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Searching for media...</span>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No results found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search terms or filters
              </p>
            </div>
          )}
        </div>
      )}

      {/* Empty State */}
      {!debouncedQuery && (
        <div className="text-center py-16">
          <Image className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold mb-2">Discover Stock Media</h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            Search millions of high-quality photos and videos from professional sources.
            Perfect for course content, presentations, and marketing materials.
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            {['education', 'business', 'technology', 'nature', 'people'].map((term) => (
              <Button
                key={term}
                variant="outline"
                size="sm"
                onClick={() => setSearchQuery(term)}
              >
                {term}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Preview Modal */}
      <MediaPreview
        item={previewItem}
        onClose={() => setPreviewItem(null)}
        onImport={(item) => {
          setSelectedItems(prev => new Set([...prev, item.id]));
          setPreviewItem(null);
        }}
        getHighResUrl={getHighResUrl}
      />
    </div>
  );
}

// Media Card Component
interface MediaCardProps {
  item: StockItem;
  isSelected: boolean;
  onSelect: () => void;
  onPreview: () => void;
  getImageUrl: (item: StockItem) => string;
  viewMode: 'grid' | 'masonry';
}

function MediaCard({ item, isSelected, onSelect, onPreview, getImageUrl, viewMode }: MediaCardProps) {
  const aspectRatio = viewMode === 'grid' ? 'aspect-square' : '';
  
  return (
    <div className={`group relative rounded-lg overflow-hidden border hover:border-primary transition-all duration-200 cursor-pointer ${viewMode === 'masonry' ? 'break-inside-avoid mb-4' : aspectRatio}`}>
      <div className="relative overflow-hidden">
        <img
          src={getImageUrl(item)}
          alt={item.title || item.tags}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          loading="lazy"
          onClick={onPreview}
        />
        
        {/* Selection Checkbox */}
        <div className="absolute top-2 left-2">
          <Checkbox
            checked={isSelected}
            onCheckedChange={onSelect}
            onClick={(e) => e.stopPropagation()}
            className="bg-white/90 border-white shadow-md"
          />
        </div>

        {/* Type Badge */}
        <div className="absolute top-2 right-2">
          <Badge variant="secondary" className="bg-black/70 text-white text-xs">
            {item.type === 'video' ? (
              <><Video className="h-3 w-3 mr-1" /> Video</>
            ) : (
              <><Image className="h-3 w-3 mr-1" /> Photo</>
            )}
          </Badge>
        </div>

        {/* Video Play Icon */}
        {item.type === 'video' && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Play className="h-8 w-8 text-white drop-shadow-lg" />
          </div>
        )}

        {/* Hover Overlay */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
          <Button variant="secondary" size="sm" onClick={onPreview}>
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
        </div>
      </div>

      {/* Metadata */}
      <div className="p-2 bg-white">
        <div className="flex items-center justify-between text-xs">
          <Badge variant="outline" className="text-xs">
            {item.source}
          </Badge>
          <span className="text-muted-foreground">
            {item.width} × {item.height}
          </span>
        </div>
        {(item.photographer || item.user) && (
          <p className="text-xs text-muted-foreground mt-1 truncate">
            by {item.photographer || item.user}
          </p>
        )}
        {item.likes && item.likes > 0 && (
          <div className="flex items-center gap-1 mt-1">
            <Heart className="h-3 w-3 text-red-500" />
            <span className="text-xs text-muted-foreground">{item.likes}</span>
          </div>
        )}
      </div>
    </div>
  );
}

// Preview Modal Component
interface MediaPreviewProps {
  item: StockItem | null;
  onClose: () => void;
  onImport: (item: StockItem) => void;
  getHighResUrl: (item: StockItem) => string;
}

function MediaPreview({ item, onClose, onImport, getHighResUrl }: MediaPreviewProps) {
  if (!item) return null;

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Dialog open={!!item} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">
                {item.title || item.tags.split(',')[0] || 'Stock Media'}
              </h3>
              {(item.photographer || item.user) && (
                <p className="text-sm text-muted-foreground">
                  by {item.photographer || item.user}
                </p>
              )}
            </div>
            <Button onClick={onClose} variant="outline" size="sm">
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Media Display */}
          <div className="relative rounded-lg overflow-hidden bg-gray-100">
            {item.type === 'photo' ? (
              <img
                src={getHighResUrl(item)}
                alt={item.title || item.tags}
                className="w-full max-h-96 object-contain"
              />
            ) : (
              <video
                src={item.url || item.videos?.medium?.url}
                controls
                className="w-full max-h-96"
                poster={item.thumbnailUrl}
              />
            )}
          </div>

          {/* Metadata */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">Dimensions:</span> {item.width} × {item.height}
            </div>
            <div>
              <span className="font-medium">Source:</span> {item.source}
            </div>
            {item.duration && (
              <div>
                <span className="font-medium">Duration:</span> {formatDuration(item.duration)}
              </div>
            )}
            <div>
              <span className="font-medium">Type:</span> {item.type}
            </div>
            {item.views && (
              <div>
                <span className="font-medium">Views:</span> {item.views.toLocaleString()}
              </div>
            )}
            {item.downloads && (
              <div>
                <span className="font-medium">Downloads:</span> {item.downloads.toLocaleString()}
              </div>
            )}
            {item.likes && (
              <div>
                <span className="font-medium">Likes:</span> {item.likes.toLocaleString()}
              </div>
            )}
          </div>

          {/* Tags */}
          {item.tags && (
            <div>
              <span className="font-medium text-sm">Tags:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {item.tags.split(',').slice(0, 10).map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tag.trim()}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={() => onImport(item)} className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              Import to Library
            </Button>
            <Button variant="outline" asChild>
              <a href={getHighResUrl(item)} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                View Original
              </a>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}