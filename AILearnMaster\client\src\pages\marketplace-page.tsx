import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Construction, CheckCircle, BellRing } from "lucide-react";

export default function MarketplacePage() {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubscribed(true);
      setEmail("");
      
      toast({
        title: "Subscribed successfully!",
        description: "You'll be notified when the marketplace goes live.",
        variant: "default",
      });
    }, 1000);
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="flex flex-col items-center text-center max-w-3xl mx-auto">
        <div className="relative mb-8">
          <div className="absolute inset-0 bg-gradient-to-r from-primary to-purple-600 blur-lg opacity-20 rounded-full"></div>
          <div className="relative bg-slate-100 dark:bg-slate-800 rounded-full p-5 shadow-lg">
            <Construction size={50} className="text-primary" />
          </div>
        </div>
        
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-purple-600 text-transparent bg-clip-text">
          Marketplace Coming Soon
        </h1>
        
        <p className="text-xl text-muted-foreground mb-8">
          We're working hard to build a marketplace where you can discover, share, and sell 
          premium course templates, media assets, and more to enhance your course creation experience.
        </p>
        
        <div className="w-full max-w-md">
          <Card className="border border-primary/20 bg-card/50 backdrop-blur-sm shadow-lg">
            <CardContent className="p-6">
              <div className="space-y-6">
                <div className="flex items-center space-x-3 text-left">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium">Premium Templates</h3>
                    <p className="text-sm text-muted-foreground">Ready-to-use professional course templates</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 text-left">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium">Media Assets</h3>
                    <p className="text-sm text-muted-foreground">High-quality graphics, animations, and audio</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 text-left">
                  <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium">Community Sharing</h3>
                    <p className="text-sm text-muted-foreground">Share and monetize your own creations</p>
                  </div>
                </div>
              </div>
              
              <div className="h-px bg-border my-6"></div>
              
              {!isSubscribed ? (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="flex items-center">
                    <BellRing className="w-5 h-5 mr-2 text-amber-500" />
                    <h3 className="font-medium">Get notified when we launch</h3>
                  </div>
                  <div className="flex space-x-2">
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      required
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="flex-1"
                    />
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <span className="animate-spin mr-2">&#10227;</span>
                          Subscribing...
                        </>
                      ) : (
                        "Notify Me"
                      )}
                    </Button>
                  </div>
                </form>
              ) : (
                <div className="flex items-center justify-center space-x-2 p-2 bg-green-50 dark:bg-green-950/30 text-green-700 dark:text-green-400 rounded-md">
                  <CheckCircle className="w-5 h-5" />
                  <span>You're on the notification list!</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <div className="mt-12">
          <div className="relative">
            <div className="absolute top-1/2 left-0 right-0 h-px bg-border"></div>
            <div className="relative bg-background px-4 inline-block text-sm text-muted-foreground">
              Expected launch: Q2 2025
            </div>
          </div>
          
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline" className="flex items-center space-x-2" onClick={() => window.history.back()}>
              <span>Back to Dashboard</span>
            </Button>
            <Button className="flex items-center space-x-2">
              <span>Learn More About Our Plans</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}