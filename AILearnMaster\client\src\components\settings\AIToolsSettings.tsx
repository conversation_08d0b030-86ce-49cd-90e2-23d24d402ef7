import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  Cpu,
  Brain,
  Image,
  Music,
  Video,
  Laptop,
  CloudCog,
  Check,
  HelpCircle,
  Info,
  AlertTriangle,
  Database,
  UploadCloud,
  Loader2,
  Server,
  Lock,
  RefreshCw,
  CheckCircle,
} from 'lucide-react';

// Form schema for API settings
const apiSettingsSchema = z.object({
  runpodApiKey: z.string().min(1, 'RunPod API key is required'),
  awsAccessKeyId: z.string().min(1, 'AWS Access Key ID is required'),
  awsSecretAccessKey: z.string().min(1, 'AWS Secret Access Key is required'),
  s3BucketName: z.string().min(1, 'S3 Bucket name is required'),
  s3Region: z.string().min(1, 'S3 Region is required'),
  cloudFrontDomain: z.string().optional(),
});

// Form schema for endpoint settings
const endpointSettingsSchema = z.object({
  coquiEndpoint: z.string().min(1, 'Coqui TTS endpoint is required'),
  kokoroEndpoint: z.string().min(1, 'Kokoro TTS endpoint is required'),
  sadtalkerEndpoint: z.string().min(1, 'SadTalker endpoint is required'),
  marpEndpoint: z.string().min(1, 'Marp Slide Generator endpoint is required')
});

// Our specific AI models
const ourModels = {
  tts: [
    { value: 'coqui-tts', label: 'Coqui TTS' },
    { value: 'kokoro-tts', label: 'Kokoro TTS' }
  ],
  avatar: [
    { value: 'sadtalker', label: 'SadTalker' }
  ],
  slides: [
    { value: 'marp', label: 'Marp Slide Generator' }
  ]
};

const AIToolsSettings: React.FC = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('api');
  const [testing, setTesting] = useState<string | null>(null);
  
  // API settings form
  const apiForm = useForm<z.infer<typeof apiSettingsSchema>>({
    resolver: zodResolver(apiSettingsSchema),
    defaultValues: {
      runpodApiKey: '',
      awsAccessKeyId: '',
      awsSecretAccessKey: '',
      s3BucketName: '',
      s3Region: 'us-east-1',
      cloudFrontDomain: ''
    }
  });
  
  // Endpoint settings form
  const endpointForm = useForm<z.infer<typeof endpointSettingsSchema>>({
    resolver: zodResolver(endpointSettingsSchema),
    defaultValues: {
      coquiEndpoint: 'https://8x1dneg4ifrfbc-5000.proxy.runpod.net/speak',
      kokoroEndpoint: '',
      sadtalkerEndpoint: '',
      marpEndpoint: ''
    }
  });
  
  // Fetch current settings
  const { data: apiSettings, isLoading: isLoadingApiSettings } = useQuery({
    queryKey: ['/api/ai-tools/settings/api'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/ai-tools/settings/api');
        const data = await response.json();
        
        // Update form with fetched data
        apiForm.reset({
          ...data,
          awsSecretAccessKey: data.awsSecretAccessKey ? '••••••••••••••••' : '',
          runpodApiKey: data.runpodApiKey ? '••••••••••••••••' : ''
        });
        
        return data;
      } catch (error) {
        console.error('Error fetching API settings:', error);
        return null;
      }
    }
  });
  
  // Fetch endpoint settings
  const { data: endpointSettings, isLoading: isLoadingEndpointSettings } = useQuery({
    queryKey: ['/api/ai-tools/settings/endpoints'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/ai-tools/settings/endpoints');
        const data = await response.json();
        
        // Update form with fetched data
        endpointForm.reset(data);
        
        return data;
      } catch (error) {
        console.error('Error fetching endpoint settings:', error);
        return null;
      }
    }
  });
  
  // Fetch health status
  const { data: healthStatus, isLoading: isLoadingHealth } = useQuery({
    queryKey: ['/api/ai-tools/health'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/ai-tools/health');
        return await response.json();
      } catch (error) {
        console.error('Error fetching health status:', error);
        return {
          status: 'error',
          s3: false,
          runpod: false,
          endpoints: {
            mistral: false,
            coqui: false,
            kandinsky: false,
            animatediff: false
          }
        };
      }
    }
  });
  
  // Save API settings mutation
  const saveApiSettingsMutation = useMutation({
    mutationFn: async (data: z.infer<typeof apiSettingsSchema>) => {
      // Create a mutable copy of the data
      const formData = { ...data };
      
      // Remove masked values before sending to API
      if (formData.runpodApiKey === '••••••••••••••••') {
        formData.runpodApiKey = '';
      }
      
      if (formData.awsSecretAccessKey === '••••••••••••••••') {
        formData.awsSecretAccessKey = '';
      }
      
      try {
        const response = await apiRequest('POST', '/api/ai-tools/settings/api', formData);
        return await response.json();
      } catch (error) {
        console.error("Error saving API settings:", error);
        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: 'Settings Saved',
        description: 'API settings have been saved successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/ai-tools/settings/api'] });
      queryClient.invalidateQueries({ queryKey: ['/api/ai-tools/health'] });
    },
    onError: (error) => {
      toast({
        title: 'Error Saving Settings',
        description: 'There was a problem saving your settings. Please try again.',
        variant: 'destructive',
      });
    }
  });
  
  // Save endpoint settings mutation
  const saveEndpointSettingsMutation = useMutation({
    mutationFn: async (data: z.infer<typeof endpointSettingsSchema>) => {
      const response = await apiRequest('POST', '/api/ai-tools/settings/endpoints', data);
      return await response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Endpoints Saved',
        description: 'Endpoint settings have been saved successfully.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/ai-tools/settings/endpoints'] });
      queryClient.invalidateQueries({ queryKey: ['/api/ai-tools/health'] });
    },
    onError: (error) => {
      toast({
        title: 'Error Saving Endpoints',
        description: 'There was a problem saving your endpoint settings. Please try again.',
        variant: 'destructive',
      });
    }
  });
  
  // Test endpoint connection
  const testConnection = async (endpoint: string) => {
    setTesting(endpoint);
    
    try {
      const response = await apiRequest('POST', '/api/ai-tools/test-connection', { endpoint });
      
      if (response.ok) {
        toast({
          title: 'Connection Successful',
          description: `Successfully connected to ${endpoint}.`,
        });
      } else {
        toast({
          title: 'Connection Failed',
          description: `Failed to connect to ${endpoint}. Please check your settings.`,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Connection Error',
        description: `An error occurred while testing the connection to ${endpoint}.`,
        variant: 'destructive',
      });
    } finally {
      setTesting(null);
    }
  };
  
  // Handler for submitting API settings
  const onSubmitApiSettings = (data: z.infer<typeof apiSettingsSchema>) => {
    // Prevent navigation during submission
    const preventNavigation = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = '';
    };
    
    // Add event listener to prevent navigation
    window.addEventListener('beforeunload', preventNavigation);
    
    saveApiSettingsMutation.mutate(data, {
      onSettled: () => {
        // Remove event listener after mutation is settled
        window.removeEventListener('beforeunload', preventNavigation);
      }
    });
  };
  
  // Handler for submitting endpoint settings
  const onSubmitEndpointSettings = (data: z.infer<typeof endpointSettingsSchema>) => {
    // Prevent navigation during submission
    const preventNavigation = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = '';
    };
    
    // Add event listener to prevent navigation
    window.addEventListener('beforeunload', preventNavigation);
    
    saveEndpointSettingsMutation.mutate(data, {
      onSettled: () => {
        // Remove event listener after mutation is settled
        window.removeEventListener('beforeunload', preventNavigation);
      }
    });
  };
  
  // Get status indicator for an endpoint
  const getStatusIndicator = (endpoint: string) => {
    if (isLoadingHealth) {
      return <div className="h-3 w-3 rounded-full bg-slate-300"></div>;
    }
    
    if (!healthStatus) {
      return <div className="h-3 w-3 rounded-full bg-red-500"></div>;
    }
    
    const isAvailable = healthStatus.endpoints && healthStatus.endpoints[endpoint];
    
    return (
      <div className={`h-3 w-3 rounded-full ${isAvailable ? 'bg-green-500' : 'bg-red-500'}`}></div>
    );
  };
  
  return (
    <div>
      {/* Status indicators */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle>AI Tools Health Status</CardTitle>
          <CardDescription>
            Check the health status of your AI tools and required infrastructure.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-6">
            <div className="flex items-center space-x-2">
              <div className={`h-3 w-3 rounded-full ${
                healthStatus?.s3 ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span className="font-medium">S3 Storage</span>
              <span className="text-sm text-slate-500">
                {healthStatus?.s3 ? 'Connected' : 'Not Connected'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className={`h-3 w-3 rounded-full ${
                healthStatus?.runpod ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span className="font-medium">RunPod API</span>
              <span className="text-sm text-slate-500">
                {healthStatus?.runpod ? 'Connected' : 'Not Connected'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {getStatusIndicator('marp')}
              <span className="font-medium">Marp Slide Generator</span>
              <span className="text-sm text-slate-500">
                {healthStatus?.endpoints?.marp ? 'Available' : 'Unavailable'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {getStatusIndicator('coqui')}
              <span className="font-medium">Coqui TTS</span>
              <span className="text-sm text-slate-500">
                {healthStatus?.endpoints?.coqui ? 'Available' : 'Unavailable'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {getStatusIndicator('kokoro')}
              <span className="font-medium">Kokoro TTS</span>
              <span className="text-sm text-slate-500">
                {healthStatus?.endpoints?.kokoro ? 'Available' : 'Unavailable'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {getStatusIndicator('sadtalker')}
              <span className="font-medium">SadTalker</span>
              <span className="text-sm text-slate-500">
                {healthStatus?.endpoints?.sadtalker ? 'Available' : 'Unavailable'}
              </span>
            </div>
          </div>
          
          {healthStatus?.status === 'error' && (
            <Alert variant="destructive" className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Configuration Required</AlertTitle>
              <AlertDescription>
                One or more AI tools are not properly configured. Please update your settings below.
              </AlertDescription>
            </Alert>
          )}
          
          {healthStatus?.status === 'partial' && (
            <Alert className="mt-4 border-amber-200 bg-amber-50 text-amber-800">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Partial Configuration</AlertTitle>
              <AlertDescription>
                Some AI tools are configured but others are not. Complete the configuration below for full functionality.
              </AlertDescription>
            </Alert>
          )}
          
          {healthStatus?.status === 'ok' && (
            <Alert variant="default" className="mt-4 bg-green-50 border-green-200 text-green-800">
              <CheckCircle className="h-4 w-4" />
              <AlertTitle>All Systems Operational</AlertTitle>
              <AlertDescription>
                All AI tools are properly configured and ready to use.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
      
      {/* Settings tabs */}
      <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="api">API & Storage</TabsTrigger>
          <TabsTrigger value="endpoints">AI Endpoints</TabsTrigger>
        </TabsList>
        
        {/* API & Storage Settings */}
        <TabsContent value="api">
          <Form {...apiForm}>
            <form 
              onSubmit={apiForm.handleSubmit(onSubmitApiSettings)}
              className={saveApiSettingsMutation.isPending ? "submitting" : ""}
            >
              <Card>
                <CardHeader>
                  <CardTitle>API & Storage Settings</CardTitle>
                  <CardDescription>
                    Configure your RunPod API key and S3 storage settings.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">RunPod API</h3>
                    <FormField
                      control={apiForm.control}
                      name="runpodApiKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>RunPod API Key</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Input 
                                type="password" 
                                {...field} 
                                placeholder="Enter your RunPod API key" 
                              />
                              <HoverCard>
                                <HoverCardTrigger asChild>
                                  <Button 
                                    variant="ghost" 
                                    size="icon" 
                                    className="absolute right-0 top-0 h-full px-3"
                                    type="button"
                                  >
                                    <HelpCircle className="h-4 w-4 text-slate-500" />
                                  </Button>
                                </HoverCardTrigger>
                                <HoverCardContent className="w-80">
                                  <div className="space-y-2">
                                    <h4 className="font-medium">RunPod API Key</h4>
                                    <p className="text-sm text-slate-500">
                                      This key is used to access and run AI models on RunPod's GPU infrastructure.
                                      You can get your API key from the RunPod dashboard.
                                    </p>
                                    <a 
                                      href="https://www.runpod.io/console/user/settings" 
                                      target="_blank" 
                                      rel="noopener noreferrer"
                                      className="text-sm text-primary underline"
                                    >
                                      Get API Key from RunPod
                                    </a>
                                  </div>
                                </HoverCardContent>
                              </HoverCard>
                            </div>
                          </FormControl>
                          <FormDescription>
                            Your RunPod API key for accessing GPU resources.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">S3 Storage</h3>
                    <div className="grid gap-4 md:grid-cols-2">
                      <FormField
                        control={apiForm.control}
                        name="awsAccessKeyId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>AWS Access Key ID</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter your AWS Access Key ID" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={apiForm.control}
                        name="awsSecretAccessKey"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>AWS Secret Access Key</FormLabel>
                            <FormControl>
                              <Input type="password" {...field} placeholder="Enter your AWS Secret Access Key" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <div className="grid gap-4 md:grid-cols-2">
                      <FormField
                        control={apiForm.control}
                        name="s3BucketName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>S3 Bucket Name</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter your S3 bucket name" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={apiForm.control}
                        name="s3Region"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>S3 Region</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a region" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="us-east-1">US East (N. Virginia)</SelectItem>
                                <SelectItem value="us-east-2">US East (Ohio)</SelectItem>
                                <SelectItem value="us-west-1">US West (N. California)</SelectItem>
                                <SelectItem value="us-west-2">US West (Oregon)</SelectItem>
                                <SelectItem value="eu-west-1">EU West (Ireland)</SelectItem>
                                <SelectItem value="eu-central-1">EU Central (Frankfurt)</SelectItem>
                                <SelectItem value="ap-northeast-1">Asia Pacific (Tokyo)</SelectItem>
                                <SelectItem value="ap-southeast-1">Asia Pacific (Singapore)</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormField
                      control={apiForm.control}
                      name="cloudFrontDomain"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>CloudFront Domain (Optional)</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Enter your CloudFront domain" />
                          </FormControl>
                          <FormDescription>
                            Your CloudFront domain for faster content delivery (optional).
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={() => apiForm.reset()}
                    disabled={saveApiSettingsMutation.isPending}
                  >
                    Reset
                  </Button>
                  <Button 
                    type="submit"
                    disabled={saveApiSettingsMutation.isPending}
                  >
                    {saveApiSettingsMutation.isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Save Settings
                  </Button>
                </CardFooter>
              </Card>
            </form>
          </Form>
        </TabsContent>
        
        {/* AI Endpoints Settings */}
        <TabsContent value="endpoints">
          <Form {...endpointForm}>
            <form onSubmit={endpointForm.handleSubmit(onSubmitEndpointSettings)}>
              <Card>
                <CardHeader>
                  <CardTitle>AI Endpoints</CardTitle>
                  <CardDescription>
                    Configure your AI model endpoints with RunPod or custom providers.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Text Generation</h3>
                      <div className="flex items-center space-x-2">
                        {getStatusIndicator('mistral')}
                        <span className="text-sm text-slate-500">
                          {healthStatus?.endpoints?.mistral ? 'Available' : 'Unavailable'}
                        </span>
                      </div>
                    </div>
                    
                    <FormField
                      control={endpointForm.control}
                      name="marpEndpoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Marp Slide Generator Endpoint URL</FormLabel>
                          <div className="flex space-x-2">
                            <FormControl>
                              <Input 
                                {...field} 
                                placeholder="https://api.runpod.ai/v2/marp-slide-generator/run" 
                              />
                            </FormControl>
                            <Button 
                              type="button" 
                              variant="outline" 
                              onClick={() => testConnection('marp')}
                              disabled={testing === 'marp'}
                            >
                              {testing === 'marp' ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <RefreshCw className="h-4 w-4" />
                              )}
                              Test
                            </Button>
                          </div>
                          <FormDescription>
                            Endpoint for slide generation using Marp.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Speech Synthesis</h3>
                      <div className="flex items-center space-x-2">
                        {getStatusIndicator('coqui')}
                        <span className="text-sm text-slate-500">
                          {healthStatus?.endpoints?.coqui ? 'Available' : 'Unavailable'}
                        </span>
                      </div>
                    </div>
                    
                    <FormField
                      control={endpointForm.control}
                      name="coquiEndpoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Coqui TTS Endpoint URL</FormLabel>
                          <div className="flex space-x-2">
                            <FormControl>
                              <Input 
                                {...field} 
                                placeholder="https://8x1dneg4ifrfbc-5000.proxy.runpod.net/speak" 
                              />
                            </FormControl>
                            <Button 
                              type="button" 
                              variant="outline" 
                              onClick={() => testConnection('coqui')}
                              disabled={testing === 'coqui'}
                            >
                              {testing === 'coqui' ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <RefreshCw className="h-4 w-4" />
                              )}
                              Test
                            </Button>
                          </div>
                          <FormDescription>
                            RunPod Proxy endpoint for Coqui TTS speech synthesis (https://8x1dneg4ifrfbc-5000.proxy.runpod.net/speak).
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Kokoro TTS</h3>
                      <div className="flex items-center space-x-2">
                        {getStatusIndicator('kokoro')}
                        <span className="text-sm text-slate-500">
                          {healthStatus?.endpoints?.kokoro ? 'Available' : 'Unavailable'}
                        </span>
                      </div>
                    </div>
                    
                    <FormField
                      control={endpointForm.control}
                      name="kokoroEndpoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Kokoro TTS Endpoint URL</FormLabel>
                          <div className="flex space-x-2">
                            <FormControl>
                              <Input 
                                {...field} 
                                placeholder="https://api.runpod.ai/v2/kokoro-tts/run" 
                              />
                            </FormControl>
                            <Button 
                              type="button" 
                              variant="outline" 
                              onClick={() => testConnection('kokoro')}
                              disabled={testing === 'kokoro'}
                            >
                              {testing === 'kokoro' ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <RefreshCw className="h-4 w-4" />
                              )}
                              Test
                            </Button>
                          </div>
                          <FormDescription>
                            Endpoint for text-to-speech using Kokoro TTS.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">SadTalker</h3>
                      <div className="flex items-center space-x-2">
                        {getStatusIndicator('sadtalker')}
                        <span className="text-sm text-slate-500">
                          {healthStatus?.endpoints?.sadtalker ? 'Available' : 'Unavailable'}
                        </span>
                      </div>
                    </div>
                    
                    <FormField
                      control={endpointForm.control}
                      name="sadtalkerEndpoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SadTalker Endpoint URL</FormLabel>
                          <div className="flex space-x-2">
                            <FormControl>
                              <Input 
                                {...field} 
                                placeholder="https://api.runpod.ai/v2/sadtalker/run" 
                              />
                            </FormControl>
                            <Button 
                              type="button" 
                              variant="outline" 
                              onClick={() => testConnection('sadtalker')}
                              disabled={testing === 'sadtalker'}
                            >
                              {testing === 'sadtalker' ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <RefreshCw className="h-4 w-4" />
                              )}
                              Test
                            </Button>
                          </div>
                          <FormDescription>
                            Endpoint for talking avatar video generation using SadTalker.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={() => endpointForm.reset()}
                    disabled={saveEndpointSettingsMutation.isPending}
                  >
                    Reset
                  </Button>
                  <Button 
                    type="submit"
                    disabled={saveEndpointSettingsMutation.isPending}
                  >
                    {saveEndpointSettingsMutation.isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Save Endpoints
                  </Button>
                </CardFooter>
              </Card>
            </form>
          </Form>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIToolsSettings;