import { Template } from "@/types";

interface TemplateCardProps {
  template: Template;
  onClick: (template: Template) => void;
}

export function TemplateCard({ template, onClick }: TemplateCardProps) {
  return (
    <div 
      className="p-4 border border-slate-200 rounded-lg hover:border-primary-300 transition-colors cursor-pointer"
      onClick={() => onClick(template)}
    >
      <div className="flex items-start">
        <div className="h-10 w-10 flex-shrink-0 rounded-md bg-secondary-100 flex items-center justify-center text-secondary-600">
          <i className={template.icon}></i>
        </div>
        <div className="ml-4">
          <h3 className="font-medium text-slate-900">{template.name}</h3>
          <p className="text-sm text-slate-500 mt-1">{template.description}</p>
        </div>
      </div>
    </div>
  );
}
