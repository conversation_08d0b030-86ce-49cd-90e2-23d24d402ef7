import { Router, Request, Response } from 'express';
import { generateScenes } from '../services/scene-generator';
import { searchPexelsMedia } from '../services/pexels-service';
import { searchPixabayMedia } from '../services/pixabay-service';

const router = Router();

// Generate scenes from script
router.post('/generate-scenes', async (req: Request, res: Response) => {
  try {
    const { script, title, targetScenes = 6, style = 'professional' } = req.body;

    if (!script) {
      return res.status(400).json({ error: 'Script is required' });
    }

    const scenes = await generateScenes({
      script,
      title,
      targetScenes,
      style
    });

    res.json({ scenes });
  } catch (error: any) {
    console.error('Scene generation error:', error);
    res.status(500).json({ 
      error: 'Failed to generate scenes',
      message: error.message 
    });
  }
});

// Search for background media
router.post('/search-background', async (req: Request, res: Response) => {
  try {
    const { query, type = 'image', source = 'pexels' } = req.body;

    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    let results;
    if (source === 'pexels') {
      results = await searchPexelsMedia(query, type, 5);
    } else if (source === 'pixabay') {
      results = await searchPixabayMedia(query, type, 5);
    } else {
      return res.status(400).json({ error: 'Invalid source' });
    }

    res.json({ results });
  } catch (error: any) {
    console.error('Background search error:', error);
    res.status(500).json({ 
      error: 'Failed to search background media',
      message: error.message 
    });
  }
});

export default router;