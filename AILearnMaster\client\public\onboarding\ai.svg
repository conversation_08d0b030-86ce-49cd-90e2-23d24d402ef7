<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- AI Brain Illustration -->
  <g transform="translate(180, 120)">
    <!-- Brain outline -->
    <path d="M0,-50 
             C30,-50 50,-30 50,0 
             C50,30 30,50 0,50 
             C-30,50 -50,30 -50,0 
             C-50,-30 -30,-50 0,-50 Z" 
          fill="#9C5FFF" opacity="0.2" stroke="#9C5FFF" stroke-width="3"/>
    
    <!-- Neural network connections -->
    <circle cx="-20" cy="-20" r="6" fill="#9C5FFF"/>
    <circle cx="20" cy="-30" r="6" fill="#9C5FFF"/>
    <circle cx="30" cy="10" r="6" fill="#9C5FFF"/>
    <circle cx="0" cy="30" r="6" fill="#9C5FFF"/>
    <circle cx="-30" cy="10" r="6" fill="#9C5FFF"/>
    <circle cx="0" cy="0" r="8" fill="#9C5FFF"/>
    
    <!-- Network connections -->
    <line x1="-20" y1="-20" x2="0" y2="0" stroke="#9C5FFF" stroke-width="2"/>
    <line x1="20" y1="-30" x2="0" y2="0" stroke="#9C5FFF" stroke-width="2"/>
    <line x1="30" y1="10" x2="0" y2="0" stroke="#9C5FFF" stroke-width="2"/>
    <line x1="0" y1="30" x2="0" y2="0" stroke="#9C5FFF" stroke-width="2"/>
    <line x1="-30" y1="10" x2="0" y2="0" stroke="#9C5FFF" stroke-width="2"/>
    
    <!-- Pulsing animation on nodes -->
    <circle cx="-20" cy="-20" r="6" fill="#ffffff" opacity="0">
      <animate attributeName="opacity" values="0;0.8;0" dur="2s" begin="0s" repeatCount="indefinite"/>
    </circle>
    <circle cx="20" cy="-30" r="6" fill="#ffffff" opacity="0">
      <animate attributeName="opacity" values="0;0.8;0" dur="2s" begin="0.4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="30" cy="10" r="6" fill="#ffffff" opacity="0">
      <animate attributeName="opacity" values="0;0.8;0" dur="2s" begin="0.8s" repeatCount="indefinite"/>
    </circle>
    <circle cx="0" cy="30" r="6" fill="#ffffff" opacity="0">
      <animate attributeName="opacity" values="0;0.8;0" dur="2s" begin="1.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-30" cy="10" r="6" fill="#ffffff" opacity="0">
      <animate attributeName="opacity" values="0;0.8;0" dur="2s" begin="1.6s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Course structure generated by AI -->
  <g transform="translate(120, 100)">
    <!-- Course outline document -->
    <rect x="-80" y="-50" width="100" height="140" rx="5" fill="white" stroke="#9C5FFF" stroke-width="2"/>
    
    <!-- Document lines -->
    <rect x="-65" y="-35" width="70" height="8" rx="2" fill="#e1e8f0"/>
    <rect x="-65" y="-20" width="50" height="6" rx="2" fill="#e1e8f0"/>
    <rect x="-65" y="-5" width="65" height="6" rx="2" fill="#e1e8f0"/>
    <rect x="-65" y="10" width="40" height="6" rx="2" fill="#e1e8f0"/>
    <rect x="-65" y="25" width="60" height="6" rx="2" fill="#e1e8f0"/>
    <rect x="-65" y="40" width="55" height="6" rx="2" fill="#e1e8f0"/>
    <rect x="-65" y="55" width="70" height="6" rx="2" fill="#e1e8f0"/>
    <rect x="-65" y="70" width="45" height="6" rx="2" fill="#e1e8f0"/>
  </g>
  
  <!-- Text generation dots animation -->
  <g transform="translate(250, 140)">
    <circle cx="0" cy="0" r="5" fill="#9C5FFF">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" begin="0s" repeatCount="indefinite"/>
    </circle>
    <circle cx="15" cy="0" r="5" fill="#9C5FFF">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" begin="0.2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="30" cy="0" r="5" fill="#9C5FFF">
      <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" begin="0.4s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Generated content -->
  <g transform="translate(270, 100)">
    <rect x="0" y="0" width="80" height="100" rx="5" fill="white" stroke="#9C5FFF" stroke-width="2"/>
    <rect x="10" y="15" width="60" height="8" rx="2" fill="#e1e8f0"/>
    <rect x="10" y="30" width="40" height="6" rx="2" fill="#e1e8f0"/>
    <rect x="10" y="45" width="55" height="6" rx="2" fill="#e1e8f0"/>
    <rect x="10" y="60" width="50" height="6" rx="2" fill="#e1e8f0"/>
    <rect x="10" y="75" width="60" height="6" rx="2" fill="#e1e8f0"/>
  </g>
  
  <!-- Floating elements -->
  <circle cx="80" cy="60" r="10" fill="#9C5FFF" opacity="0.4"/>
  <circle cx="320" cy="220" r="15" fill="#9C5FFF" opacity="0.3"/>
  <circle cx="100" cy="230" r="8" fill="#9C5FFF" opacity="0.5"/>
</svg>