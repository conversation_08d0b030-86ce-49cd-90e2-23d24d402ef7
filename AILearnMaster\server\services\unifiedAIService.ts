/**
 * Unified AI Service - Open-Source First Approach
 * Service hierarchy: Mistral (primary) → Gemini (secondary) → OpenAI (tertiary)
 */

import { CourseStructure, GeneratedQuiz } from '@shared/schema';
import * as mistralService from './mistralPrimaryService';
import * as geminiService from './gemini';
import * as openAIService from './openAIFallbackService';

export interface AIServiceOptions {
  forceService?: 'mistral' | 'gemini' | 'openai';
  skipFallback?: boolean;
}

/**
 * Generate course structure with service hierarchy
 */
export async function generateCourseStructure(
  courseTitle: string,
  courseDescription: string,
  category: string,
  moduleCount?: number,
  options: AIServiceOptions = {}
): Promise<CourseStructure> {
  
  // Force specific service if requested
  if (options.forceService === 'openai') {
    return await openAIService.generateCourseStructureWithOpenAI(courseTitle, courseDescription, category, moduleCount);
  }
  if (options.forceService === 'gemini') {
    return await geminiService.generateCourseStructure({
      title: courseTitle,
      description: courseDescription,
      category,
      moduleCount
    });
  }
  if (options.forceService === 'mistral') {
    return await mistralService.generateCourseStructureWithMistral(courseTitle, courseDescription, category, moduleCount);
  }

  // Default hierarchy: Mistral → Gemini → OpenAI
  try {
    console.log('🧠 Attempting course structure generation with Mistral (primary)...');
    return await mistralService.generateCourseStructureWithMistral(courseTitle, courseDescription, category, moduleCount);
  } catch (mistralError) {
    console.warn('⚠️ Mistral failed, trying Gemini (secondary):', mistralError);
    
    if (options.skipFallback) {
      throw mistralError;
    }
    
    try {
      return await geminiService.generateCourseStructure({
        title: courseTitle,
        description: courseDescription,
        category,
        moduleCount
      });
    } catch (geminiError) {
      console.warn('⚠️ Gemini failed, trying OpenAI (tertiary):', geminiError);
      
      try {
        return await openAIService.generateCourseStructureWithOpenAI(courseTitle, courseDescription, category, moduleCount);
      } catch (openaiError) {
        console.error('❌ All AI services failed for course structure generation');
        throw new Error('All AI services failed. Please check your configuration and try again.');
      }
    }
  }
}

/**
 * Generate lesson script with service hierarchy
 */
export async function generateLessonScript(
  courseTitle: string,
  courseDescription: string,
  moduleTitle: string,
  moduleDescription: string,
  lessonTitle: string,
  lessonDescription: string,
  targetAudience: string,
  options: AIServiceOptions = {}
): Promise<string> {
  
  // Force specific service if requested
  if (options.forceService === 'openai') {
    return await openAIService.generateLessonScriptWithOpenAI(
      courseTitle, courseDescription, moduleTitle, moduleDescription, 
      lessonTitle, lessonDescription, targetAudience
    );
  }
  if (options.forceService === 'gemini') {
    return await geminiService.generateScript({
      title: lessonTitle,
      description: lessonDescription,
      courseTitle,
      moduleTitle,
      targetAudience
    });
  }
  if (options.forceService === 'mistral') {
    return await mistralService.generateLessonScriptWithMistral(
      courseTitle, courseDescription, moduleTitle, moduleDescription,
      lessonTitle, lessonDescription, targetAudience
    );
  }

  // Default hierarchy: Mistral → Gemini → OpenAI
  try {
    console.log('🧠 Attempting lesson script generation with Mistral (primary)...');
    return await mistralService.generateLessonScriptWithMistral(
      courseTitle, courseDescription, moduleTitle, moduleDescription,
      lessonTitle, lessonDescription, targetAudience
    );
  } catch (mistralError) {
    console.warn('⚠️ Mistral failed, trying Gemini (secondary):', mistralError);
    
    if (options.skipFallback) {
      throw mistralError;
    }
    
    try {
      return await geminiService.generateScript({
        title: lessonTitle,
        description: lessonDescription,
        courseTitle,
        moduleTitle,
        targetAudience
      });
    } catch (geminiError) {
      console.warn('⚠️ Gemini failed, trying OpenAI (tertiary):', geminiError);
      
      try {
        return await openAIService.generateLessonScriptWithOpenAI(
          courseTitle, courseDescription, moduleTitle, moduleDescription,
          lessonTitle, lessonDescription, targetAudience
        );
      } catch (openaiError) {
        console.error('❌ All AI services failed for lesson script generation');
        throw new Error('All AI services failed. Please check your configuration and try again.');
      }
    }
  }
}

/**
 * Generate quiz with service hierarchy
 */
export async function generateQuiz(
  courseTitle: string,
  courseDescription: string,
  lessonScript?: string,
  numQuestions: number = 5,
  includeFlashcards: boolean = true,
  includeSummary: boolean = true,
  difficulty: string = "medium",
  options: AIServiceOptions = {}
): Promise<GeneratedQuiz> {
  
  // Force specific service if requested
  if (options.forceService === 'openai') {
    return await openAIService.generateQuizWithOpenAI(
      courseTitle, courseDescription, lessonScript, numQuestions,
      includeFlashcards, includeSummary, difficulty
    );
  }
  if (options.forceService === 'mistral') {
    return await mistralService.generateQuizWithMistral(
      courseTitle, courseDescription, lessonScript, numQuestions,
      includeFlashcards, includeSummary, difficulty
    );
  }

  // Default hierarchy: Mistral → OpenAI (Gemini doesn't have quiz generation)
  try {
    console.log('🧠 Attempting quiz generation with Mistral (primary)...');
    return await mistralService.generateQuizWithMistral(
      courseTitle, courseDescription, lessonScript, numQuestions,
      includeFlashcards, includeSummary, difficulty
    );
  } catch (mistralError) {
    console.warn('⚠️ Mistral failed, trying OpenAI (fallback):', mistralError);
    
    if (options.skipFallback) {
      throw mistralError;
    }
    
    try {
      return await openAIService.generateQuizWithOpenAI(
        courseTitle, courseDescription, lessonScript, numQuestions,
        includeFlashcards, includeSummary, difficulty
      );
    } catch (openaiError) {
      console.error('❌ All AI services failed for quiz generation');
      throw new Error('All AI services failed. Please check your configuration and try again.');
    }
  }
}

/**
 * Generate module structure with service hierarchy
 */
export async function generateModuleStructure(
  courseTitle: string,
  courseDescription: string,
  existingModules: string[] = [],
  moduleTitle?: string,
  moduleIndex?: number,
  totalModules?: number,
  options: AIServiceOptions = {}
): Promise<{ title: string; description: string }> {
  
  // Force specific service if requested
  if (options.forceService === 'openai') {
    return await openAIService.generateModuleStructureWithOpenAI(
      courseTitle, courseDescription, existingModules, moduleTitle, moduleIndex, totalModules
    );
  }
  if (options.forceService === 'mistral') {
    return await mistralService.generateModuleStructureWithMistral(
      courseTitle, courseDescription, existingModules, moduleTitle, moduleIndex, totalModules
    );
  }

  // Default hierarchy: Mistral → OpenAI
  try {
    console.log('🧠 Attempting module structure generation with Mistral (primary)...');
    return await mistralService.generateModuleStructureWithMistral(
      courseTitle, courseDescription, existingModules, moduleTitle, moduleIndex, totalModules
    );
  } catch (mistralError) {
    console.warn('⚠️ Mistral failed, trying OpenAI (fallback):', mistralError);
    
    if (options.skipFallback) {
      throw mistralError;
    }
    
    try {
      return await openAIService.generateModuleStructureWithOpenAI(
        courseTitle, courseDescription, existingModules, moduleTitle, moduleIndex, totalModules
      );
    } catch (openaiError) {
      console.error('❌ All AI services failed for module structure generation');
      throw new Error('All AI services failed. Please check your configuration and try again.');
    }
  }
}

/**
 * Generate complete course with service hierarchy
 */
export async function generateFullCourse(
  courseTitle: string,
  courseDescription: string,
  category: string,
  options: AIServiceOptions = {}
) {
  // Force specific service if requested
  if (options.forceService === 'openai') {
    return await openAIService.generateFullCourseWithOpenAI(courseTitle, courseDescription, category);
  }
  if (options.forceService === 'mistral') {
    return await mistralService.generateFullCourseWithMistral(courseTitle, courseDescription, category);
  }

  // Default hierarchy: Mistral → OpenAI
  try {
    console.log('🧠 Attempting full course generation with Mistral (primary)...');
    return await mistralService.generateFullCourseWithMistral(courseTitle, courseDescription, category);
  } catch (mistralError) {
    console.warn('⚠️ Mistral failed, trying OpenAI (fallback):', mistralError);
    
    if (options.skipFallback) {
      throw mistralError;
    }
    
    try {
      return await openAIService.generateFullCourseWithOpenAI(courseTitle, courseDescription, category);
    } catch (openaiError) {
      console.error('❌ All AI services failed for full course generation');
      throw new Error('All AI services failed. Please check your configuration and try again.');
    }
  }
}

/**
 * Get service status and availability
 */
export async function getServiceStatus(): Promise<{
  mistral: boolean;
  gemini: boolean;
  openai: boolean;
  primary: string;
}> {
  const status = {
    mistral: false,
    gemini: false,
    openai: false,
    primary: 'mistral'
  };

  // Check Mistral (Modal A100)
  try {
    const modalEndpoint = process.env.MODAL_GPU_BASE_URL || 'https://trade-digital--courseai-opensource';
    const response = await fetch(`${modalEndpoint}-health.modal.run`, { timeout: 5000 });
    status.mistral = response.ok;
  } catch {
    status.mistral = false;
  }

  // Check Gemini
  try {
    status.gemini = !!process.env.GEMINI_API_KEY;
  } catch {
    status.gemini = false;
  }

  // Check OpenAI
  try {
    status.openai = !!process.env.OPENAI_API_KEY;
  } catch {
    status.openai = false;
  }

  return status;
}
