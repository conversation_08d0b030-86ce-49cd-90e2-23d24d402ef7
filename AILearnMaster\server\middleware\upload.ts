import multer from 'multer';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
// Security validation removed for development

// Create uploads directory if it doesn't exist
const uploadDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Set up storage for uploaded files
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // Generate a secure unique filename
    const uniqueSuffix = `${Date.now()}-${crypto.randomBytes(8).toString('hex')}`;
    // Sanitize the original extension to prevent path traversal
    const originalExt = path.extname(file.originalname).toLowerCase();
    const safeExtension = originalExt.replace(/[^a-z0-9.]/g, '');
    const filename = `${file.fieldname}-${uniqueSuffix}${safeExtension}`;
    cb(null, filename);
  }
});

// Enhanced file filter with security validation
const fileFilter = (req: Express.Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Strict whitelist of allowed MIME types
  const allowedTypes = [
    // Images (common formats only)
    'image/jpeg', 'image/png', 'image/webp',
    // Documents (safe formats only)
    'application/pdf',
    // Audio (safe formats only)
    'audio/mpeg', 'audio/wav',
    // Text files
    'text/plain'
  ];

  // Additional security checks
  const fileName = file.originalname.toLowerCase();

  // Block potentially dangerous file extensions
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar', '.php', '.asp', '.jsp'];
  const hasDangerousExtension = dangerousExtensions.some(ext => fileName.endsWith(ext));

  if (hasDangerousExtension) {
    cb(new Error(`Dangerous file extension detected: ${path.extname(fileName)}`));
    return;
  }

  // Check file name for path traversal attempts
  if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
    cb(new Error('Invalid file name: path traversal detected'));
    return;
  }

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Unsupported file type: ${file.mimetype}`));
  }
};

// Configure multer with enhanced security
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // Reduced to 5MB for security
    files: 5, // Maximum 5 files per request
    fieldNameSize: 100, // Limit field name size
    fieldSize: 1024 * 1024 // 1MB field size limit
  }
});

// Export upload with security validation middleware
export default upload;
export { validateFileUpload };