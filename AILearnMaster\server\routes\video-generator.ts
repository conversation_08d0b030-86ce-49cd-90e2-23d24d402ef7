import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import { promisify } from 'util';
import * as videoGenerator from '../services/video-generator';
import { storage } from '../storage';
// Security middleware removed for development

const mkdir = promisify(fs.mkdir);
const router = express.Router();

// Setup for file uploads
const uploadsDir = path.join(process.cwd(), 'uploads');
const imagesDir = path.join(uploadsDir, 'images');
const audioDir = path.join(uploadsDir, 'audio');
const videoDir = path.join(uploadsDir, 'videos');

// Ensure directories exist
async function ensureDirectoriesExist() {
  for (const dir of [uploadsDir, imagesDir, audioDir, videoDir]) {
    try {
      await mkdir(dir, { recursive: true });
    } catch (err) {
      if ((err as NodeJS.ErrnoException).code !== 'EEXIST') {
        throw err;
      }
    }
  }
}

// Setup multer for file uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: async (req, file, cb) => {
      await ensureDirectoriesExist();
      
      if (file.mimetype.startsWith('image/')) {
        cb(null, imagesDir);
      } else if (file.mimetype.startsWith('audio/')) {
        cb(null, audioDir);
      } else {
        cb(new Error('Unsupported file type'), '');
      }
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + crypto.randomBytes(8).toString('hex');
      // Sanitize extension to prevent path traversal
      const ext = path.extname(file.originalname).toLowerCase().replace(/[^a-z0-9.]/g, '');
      const filename = file.fieldname + '-' + uniqueSuffix + ext;
      cb(null, filename);
    }
  }),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max file size
  },
  fileFilter: (req, file, cb) => {
    const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const allowedAudioTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg'];
    
    if (allowedImageTypes.includes(file.mimetype) || allowedAudioTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Unsupported file type'), false);
    }
  }
});

// Upload images/audio files with rate limiting and security validation
router.post('/upload',
  rateLimiters.upload(),
  upload.fields([
    { name: 'images', maxCount: 5 }, // Reduced for security
    { name: 'audio', maxCount: 1 }
  ]),
  validateFileUpload,
  async (req, res) => {
  try {
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    
    if (!files || (!files.images?.length && !files.audio?.length)) {
      return res.status(400).json({ message: 'No files uploaded' });
    }
    
    // Save files to media library
    const savedMedia = [];
    
    if (files.images) {
      for (const file of files.images) {
        const mediaFile = await storage.createMedia({
          name: file.originalname,
          type: 'image',
          mimeType: file.mimetype,
          fileSize: file.size,
          url: `/uploads/images/${file.filename}`,
          userId: req.user?.id || 0,
          originalFilename: file.originalname,
        });
        savedMedia.push(mediaFile);
      }
    }
    
    if (files.audio) {
      for (const file of files.audio) {
        const mediaFile = await storage.createMedia({
          name: file.originalname,
          type: 'audio',
          mimeType: file.mimetype,
          fileSize: file.size,
          url: `/uploads/audio/${file.filename}`,
          userId: req.user?.id || 0,
          originalFilename: file.originalname,
        });
        savedMedia.push(mediaFile);
      }
    }
    
    res.status(200).json({ 
      message: 'Files uploaded successfully',
      media: savedMedia
    });
  } catch (error) {
    console.error('Error uploading files:', error);
    res.status(500).json({ 
      message: 'Error uploading files',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate a standalone video from script (no voice or TTS required)
router.post('/standalone-video', async (req, res) => {
  try {
    const { title, script, useAIVoiceover, includeSubtitles, imageIds } = req.body;
    
    if (!title || !script) {
      return res.status(400).json({ message: 'Title and script are required' });
    }
    
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }
    
    // Check if user has enough credits (AI video generation costs 200 credits)
    const user = await storage.getUser(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    const aiCredits = user.aiCredits || 0;
    const requiredCredits = 200; // Base cost for video generation
    
    if (aiCredits < requiredCredits) {
      return res.status(403).json({ 
        message: 'Insufficient AI credits',
        creditsNeeded: requiredCredits,
        creditsAvailable: aiCredits
      });
    }
    
    // Get images if imageIds are provided
    let imagePaths: string[] = [];
    if (imageIds && imageIds.length > 0) {
      const mediaItems = await Promise.all(imageIds.map(id => storage.getMediaById(id)));
      imagePaths = mediaItems
        .filter(item => item && item.type === 'image')
        .map(item => path.join(process.cwd(), item!.url.replace(/^\//, '')));
    }
    
    // If no images provided, use a placeholder
    if (imagePaths.length === 0) {
      const placeholderPath = path.join(imagesDir, 'placeholder-image.jpg');
      
      // Check if placeholder exists, if not create one
      if (!fs.existsSync(placeholderPath)) {
        // Create a blue placeholder image
        try {
          const cmd = `ffmpeg -f lavfi -i color=c=blue:s=1280x720 -frames:v 1 "${placeholderPath}"`;
          require('child_process').execSync(cmd);
        } catch (error) {
          console.error('Failed to create placeholder image:', error);
          return res.status(500).json({ message: 'Failed to create placeholder image' });
        }
      }
      
      imagePaths = [placeholderPath];
    }
    
    // Sanitize output filename
    const sanitizedTitle = title.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const outputFileName = `${sanitizedTitle}_${Date.now()}`;
    
    // Generate the video
    const video = await videoGenerator.createScriptedVideo({
      script,
      imagePaths,
      outputFileName,
    });
    
    // Save the video to the database
    const savedVideo = await storage.createMedia({
      name: title,
      type: 'video',
      mimeType: 'video/mp4',
      fileSize: fs.statSync(video.path).size,
      url: video.url,
      userId: userId,
      originalFilename: path.basename(video.path),
    });
    
    // Deduct AI credits from user
    await storage.updateUserAiCredits(userId, aiCredits - requiredCredits);
    
    res.status(200).json({
      message: 'Video generated successfully',
      video: savedVideo,
      creditsUsed: requiredCredits
    });
  } catch (error) {
    console.error('Error generating video:', error);
    res.status(500).json({ 
      message: 'Error generating video',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get all videos for the current user
router.get('/videos', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'User not authenticated' });
    }
    
    const videos = await storage.getUserMedia(userId, 'video');
    
    res.status(200).json({ videos });
  } catch (error) {
    console.error('Error fetching videos:', error);
    res.status(500).json({ 
      message: 'Error fetching videos',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Export the router as default
export default router;