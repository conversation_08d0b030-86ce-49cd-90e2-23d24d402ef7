{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:setup": "tsx scripts/database-setup.ts", "db:setup:force": "tsx scripts/database-setup.ts --force", "db:validate": "tsx scripts/database-setup.ts --validate-only", "db:report": "tsx scripts/database-setup.ts --report", "db:health": "curl -s http://localhost:3001/api/database/health | jq", "db:diagnostics": "curl -s http://localhost:3001/api/database/diagnostics | jq", "db:test": "curl -s -X POST http://localhost:3001/api/database/test | jq", "db:test-functionality": "tsx scripts/test-database-functionality.ts", "test:course-workflows": "tsx scripts/test-enhanced-course-workflows.ts", "security:audit": "tsx scripts/security-audit-tests.ts", "security:validate": "tsx scripts/validate-security-config.ts", "security:scan": "npm audit --audit-level=moderate", "security:check-env": "tsx scripts/check-environment-security.ts", "security:test-all": "tsx scripts/run-security-tests.ts", "security:production-ready": "npm run security:test-all && echo 'Security validation complete'", "build:server": "tsc -p server/tsconfig.json", "deploy:aws": "node deployment/production-env-setup.js", "deploy:infrastructure": "bash deployment/setup-aws-infrastructure.sh"}, "dependencies": {"@anthropic-ai/sdk": "^0.37.0", "@aws-sdk/client-s3": "^3.796.0", "@aws-sdk/s3-request-presigner": "^3.796.0", "@excalidraw/excalidraw": "^0.18.0", "@google/generative-ai": "^0.24.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.9.1", "@jitsi/react-sdk": "^1.4.4", "@jridgewell/trace-mapping": "^0.3.25", "@marp-team/marp-cli": "^4.1.2", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@sendgrid/mail": "^8.1.5", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tanstack/react-query": "^5.60.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/jspdf": "^1.3.3", "@types/memoizee": "^0.4.12", "@types/multer": "^1.4.12", "@types/sanitize-html": "^2.16.0", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "connect-pg-simple": "^10.0.0", "cross-fetch": "^4.1.0", "crypto": "^1.0.1", "csv-parse": "^5.6.0", "csv-writer": "^1.6.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "elevenlabs": "^1.56.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "google-auth-library": "^10.1.0", "googleapis": "^150.0.1", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.453.0", "marked": "^15.0.11", "memoizee": "^0.4.17", "memorystore": "^1.6.7", "multer": "^1.4.5-lts.2", "openai": "^4.104.0", "openid-client": "^6.5.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.1", "react-icons": "^5.5.0", "react-refresh": "^0.17.0", "react-resizable-panels": "^2.1.4", "react-responsive": "^10.0.1", "recharts": "^2.13.0", "resend": "^4.5.1", "sanitize-html": "^2.16.0", "stability-client": "^1.9.0", "stripe": "^18.2.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.2.7", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.1", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}}