#!/usr/bin/env python3
"""
Deploy Fixed Modal A100 GPU Script
Deploys the comprehensive fixed version with all 7 AI services
"""

import subprocess
import sys
import time
import requests
import json

def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} failed")
            print(f"   Error: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ {description} error: {e}")
        return False

def check_modal_installation():
    """Check if Modal is installed and configured"""
    print("🔍 Checking Modal installation...")
    
    # Check if modal is installed
    result = subprocess.run(["modal", "--version"], capture_output=True, text=True)
    if result.returncode != 0:
        print("❌ Modal CLI not found. Installing...")
        if not run_command("pip install modal", "Installing Modal"):
            return False
    else:
        print(f"✅ Modal CLI found: {result.stdout.strip()}")
    
    # Check if user is authenticated
    result = subprocess.run(["modal", "token", "current"], capture_output=True, text=True)
    if result.returncode != 0:
        print("❌ Modal authentication required")
        print("   Please run: modal token new")
        return False
    else:
        print("✅ Modal authentication verified")
    
    return True

def deploy_modal_app():
    """Deploy the Modal A100 app"""
    print("🚀 Deploying Modal A100 Comprehensive App...")
    
    # Deploy the app
    cmd = "modal deploy modal_a100_comprehensive_fixed.py"
    if not run_command(cmd, "Deploying Modal A100 app"):
        return False, None
    
    # Get the app URL
    print("🔍 Getting app URL...")
    result = subprocess.run(["modal", "app", "list"], capture_output=True, text=True)
    
    if result.returncode == 0:
        lines = result.stdout.split('\n')
        for line in lines:
            if 'courseai-a100-production' in line and 'https://' in line:
                # Extract URL from the line
                parts = line.split()
                for part in parts:
                    if part.startswith('https://'):
                        return True, part
    
    print("⚠️  Could not automatically detect app URL")
    print("   Please check Modal dashboard for the URL")
    return True, None

def test_deployment(app_url):
    """Test the deployed app"""
    if not app_url:
        print("⚠️  Skipping deployment test - no URL provided")
        return False
    
    print(f"🧪 Testing deployment at: {app_url}")
    
    try:
        # Test health endpoint
        response = requests.get(f"{app_url}/health", timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check successful")
            print(f"   GPU: {data.get('gpu_name', 'Unknown')}")
            print(f"   Status: {data.get('status', 'Unknown')}")
            
            services = data.get('services', {})
            working_services = sum(1 for status in services.values() if status is True)
            total_services = len(services)
            
            print(f"   Services: {working_services}/{total_services} working")
            
            for service, status in services.items():
                status_icon = "✅" if status is True else "❌"
                print(f"     {service}: {status_icon}")
            
            return working_services > 0
        else:
            print(f"❌ Health check failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Deployment test failed: {e}")
        return False

def update_environment_variables(app_url):
    """Update environment variables with the new Modal URL"""
    if not app_url:
        return
    
    print("🔧 Updating environment configuration...")
    
    # Update .env file if it exists
    env_files = ['.env', '.env.local', 'server/.env']
    
    for env_file in env_files:
        try:
            with open(env_file, 'r') as f:
                content = f.read()
            
            # Update or add MODAL_A100_URL
            lines = content.split('\n')
            updated = False
            
            for i, line in enumerate(lines):
                if line.startswith('MODAL_A100_URL='):
                    lines[i] = f'MODAL_A100_URL={app_url}'
                    updated = True
                    break
            
            if not updated:
                lines.append(f'MODAL_A100_URL={app_url}')
            
            with open(env_file, 'w') as f:
                f.write('\n'.join(lines))
            
            print(f"✅ Updated {env_file}")
            
        except FileNotFoundError:
            # Create new .env file
            with open(env_file, 'w') as f:
                f.write(f'MODAL_A100_URL={app_url}\n')
            print(f"✅ Created {env_file}")
            break
        except Exception as e:
            print(f"⚠️  Could not update {env_file}: {e}")

def run_comprehensive_test():
    """Run the comprehensive test script"""
    print("🧪 Running comprehensive Modal A100 tests...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_modal_a100_comprehensive.py"
        ], timeout=600)  # 10 minute timeout
        
        if result.returncode == 0:
            print("✅ All comprehensive tests passed!")
            return True
        else:
            print("⚠️  Some tests failed. Check test output for details.")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Comprehensive tests timed out")
        return False
    except Exception as e:
        print(f"❌ Error running comprehensive tests: {e}")
        return False

def main():
    """Main deployment process"""
    print("🚀 Modal A100 GPU Deployment - Comprehensive Fix")
    print("=" * 60)
    
    # Step 1: Check Modal installation
    if not check_modal_installation():
        print("❌ Modal setup failed. Please install and authenticate Modal CLI.")
        sys.exit(1)
    
    # Step 2: Deploy the app
    success, app_url = deploy_modal_app()
    if not success:
        print("❌ Deployment failed")
        sys.exit(1)
    
    print(f"✅ Deployment successful!")
    if app_url:
        print(f"   App URL: {app_url}")
    
    # Step 3: Test deployment
    if app_url:
        print("\n" + "=" * 60)
        test_success = test_deployment(app_url)
        
        if test_success:
            print("✅ Deployment test successful!")
        else:
            print("⚠️  Deployment test had issues")
    
    # Step 4: Update environment variables
    if app_url:
        print("\n" + "=" * 60)
        update_environment_variables(app_url)
    
    # Step 5: Run comprehensive tests
    print("\n" + "=" * 60)
    comprehensive_success = run_comprehensive_test()
    
    # Final report
    print("\n" + "=" * 60)
    print("📊 DEPLOYMENT SUMMARY")
    print("=" * 60)
    
    print(f"✅ Modal App Deployed: {success}")
    if app_url:
        print(f"✅ App URL: {app_url}")
        print(f"✅ Health Check: {test_success if app_url else 'Skipped'}")
        print(f"✅ Environment Updated: Yes")
    print(f"✅ Comprehensive Tests: {comprehensive_success}")
    
    if success and (not app_url or test_success) and comprehensive_success:
        print("\n🎉 Modal A100 GPU deployment completed successfully!")
        print("   All 7 AI services should now be working correctly.")
        print("\nNext steps:")
        print("1. Test the course creation workflows")
        print("2. Monitor GPU usage and performance")
        print("3. Check logs for any issues")
        
        if app_url:
            print(f"\nModal A100 URL: {app_url}")
            print("Available endpoints:")
            print("  /health - GPU health check")
            print("  /mistral - Mistral LLM content generation")
            print("  /sdxl - SDXL image generation")
            print("  /tts - Coqui TTS voice synthesis")
            print("  /api_avatar - EchoMimic V2 avatar generation")
            print("  /whisper - Whisper speech-to-text")
            print("  /slides - Marp slide generation")
            print("  /ffmpeg - FFmpeg video processing")
        
        sys.exit(0)
    else:
        print("\n⚠️  Deployment completed with some issues.")
        print("   Please check the logs above for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
