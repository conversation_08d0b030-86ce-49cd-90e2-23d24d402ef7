export interface PlatformFeature {
  name: string;
  description: string;
  icon: string;
}

export interface PlatformIntegration {
  id: number;
  name: string;
  slug: string;
  description: string;
  icon: string;
  category: string;
  isConnected: boolean;
  lastSyncedAt?: string;
  connectionId?: number;
  features: PlatformFeature[];
  authType: 'oauth' | 'api_key' | 'none';
  oauthCallbackUrl?: string;
}

export const platformCategories = {
  SOCIAL: 'social',
  EDUCATION: 'education',
  VIDEO: 'video',
  COMMUNICATION: 'communication'
};

// Default platform integrations when API fails
export const defaultPlatforms: PlatformIntegration[] = [
  {
    id: 1,
    name: 'Facebook',
    slug: 'facebook',
    category: platformCategories.SOCIAL,
    description: 'Connect with your Facebook account to share your courses on your page or groups.',
    icon: 'facebook',
    isConnected: false,
    authType: 'oauth',
    features: [
      {
        name: 'Share Courses',
        description: 'Share your courses on your Facebook page or groups',
        icon: 'share'
      },
      {
        name: 'Audience Insights',
        description: 'Get insights on your audience engagement',
        icon: 'bar-chart'
      },
      {
        name: 'User Login',
        description: 'Allow students to login with Facebook',
        icon: 'users'
      }
    ]
  },
  {
    id: 2,
    name: 'Instagram',
    slug: 'instagram',
    category: platformCategories.SOCIAL,
    description: 'Share your course content on Instagram to reach more students.',
    icon: 'instagram',
    isConnected: false,
    authType: 'oauth',
    features: [
      {
        name: 'Share Previews',
        description: 'Share course previews on your Instagram feed',
        icon: 'share'
      },
      {
        name: 'Stories Integration',
        description: 'Create Instagram stories from your course content',
        icon: 'upload'
      },
      {
        name: 'Engagement Analytics',
        description: 'Track engagement with your shared content',
        icon: 'bar-chart'
      }
    ]
  },
  {
    id: 3,
    name: 'LinkedIn',
    slug: 'linkedin',
    category: platformCategories.SOCIAL,
    description: 'Connect with LinkedIn to share your courses with your professional network.',
    icon: 'linkedin',
    isConnected: false,
    authType: 'oauth',
    features: [
      {
        name: 'Professional Sharing',
        description: 'Share your courses with your professional network',
        icon: 'share'
      },
      {
        name: 'Professional Credentials',
        description: 'Issue professional credentials to students',
        icon: 'users'
      },
      {
        name: 'Business Analytics',
        description: 'Get analytics on business engagement',
        icon: 'bar-chart'
      }
    ]
  },
  {
    id: 4,
    name: 'Twitter',
    slug: 'twitter',
    category: platformCategories.SOCIAL,
    description: 'Share your course content on Twitter to reach potential students.',
    icon: 'twitter',
    isConnected: false,
    authType: 'oauth',
    features: [
      {
        name: 'Tweet Updates',
        description: 'Share course updates and announcements',
        icon: 'share'
      },
      {
        name: 'Engagement Tracking',
        description: 'Track engagement with your course tweets',
        icon: 'bar-chart'
      },
      {
        name: 'Twitter Cards',
        description: 'Create rich Twitter cards for your courses',
        icon: 'upload'
      }
    ]
  },
  {
    id: 5,
    name: 'YouTube',
    slug: 'youtube',
    category: platformCategories.VIDEO,
    description: 'Upload your course videos to YouTube and build your audience.',
    icon: 'youtube',
    isConnected: false,
    authType: 'oauth',
    features: [
      {
        name: 'Video Upload',
        description: 'Upload course videos to YouTube',
        icon: 'upload'
      },
      {
        name: 'Channel Management',
        description: 'Manage your YouTube channel',
        icon: 'settings'
      },
      {
        name: 'Analytics',
        description: 'Track video performance and engagement',
        icon: 'bar-chart'
      }
    ]
  },
  {
    id: 6,
    name: 'TikTok',
    slug: 'tiktok',
    category: platformCategories.VIDEO,
    description: 'Promote your courses with short-form videos on TikTok.',
    icon: 'tiktok',
    isConnected: false,
    authType: 'oauth',
    features: [
      {
        name: 'Short-form Content',
        description: 'Create and share course teasers',
        icon: 'upload'
      },
      {
        name: 'Trend Participation',
        description: 'Participate in trends to boost visibility',
        icon: 'trending-up'
      },
      {
        name: 'Creator Analytics',
        description: 'Track content performance',
        icon: 'bar-chart'
      }
    ]
  },
  {
    id: 7,
    name: 'Udemy',
    slug: 'udemy',
    category: platformCategories.EDUCATION,
    description: 'Publish and sell your courses on the Udemy marketplace.',
    icon: 'udemy',
    isConnected: false,
    authType: 'api_key',
    features: [
      {
        name: 'Course Publishing',
        description: 'Publish your courses on Udemy',
        icon: 'upload'
      },
      {
        name: 'Course Sales',
        description: 'Sell your courses and track revenue',
        icon: 'dollar-sign'
      },
      {
        name: 'Instructor Analytics',
        description: 'Get insights on course performance',
        icon: 'bar-chart'
      }
    ]
  },
  {
    id: 8,
    name: 'Teachable',
    slug: 'teachable',
    category: platformCategories.EDUCATION,
    description: 'Export your courses to your Teachable school.',
    icon: 'teachable',
    isConnected: false,
    authType: 'api_key',
    features: [
      {
        name: 'School Integration',
        description: 'Export courses to your Teachable school',
        icon: 'upload'
      },
      {
        name: 'Sales Management',
        description: 'Manage course sales and revenue',
        icon: 'dollar-sign'
      },
      {
        name: 'Student Management',
        description: 'Manage student enrollments and progress',
        icon: 'users'
      }
    ]
  },
  {
    id: 9,
    name: 'Kajabi',
    slug: 'kajabi',
    category: platformCategories.EDUCATION,
    description: 'Export your courses to your Kajabi platform.',
    icon: 'education',
    isConnected: false,
    authType: 'api_key',
    features: [
      {
        name: 'Platform Export',
        description: 'Export courses to your Kajabi platform',
        icon: 'upload'
      },
      {
        name: 'Pipeline Integration',
        description: 'Integrate with Kajabi marketing pipelines',
        icon: 'trending-up'
      },
      {
        name: 'Offers Management',
        description: 'Manage course offers and bundles',
        icon: 'dollar-sign'
      }
    ]
  },
  {
    id: 10,
    name: 'Skillshare',
    slug: 'skillshare',
    category: platformCategories.EDUCATION,
    description: 'Publish your courses on Skillshare to reach their creative community.',
    icon: 'skillshare',
    isConnected: false,
    authType: 'api_key',
    features: [
      {
        name: 'Class Publishing',
        description: 'Publish your courses as Skillshare classes',
        icon: 'upload'
      },
      {
        name: 'Royalty Tracking',
        description: 'Track your earnings and minutes watched',
        icon: 'dollar-sign'
      },
      {
        name: 'Community Engagement',
        description: 'Engage with the creative community',
        icon: 'users'
      }
    ]
  }
];