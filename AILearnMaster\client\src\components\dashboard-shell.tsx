import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { MainNav } from "@/components/main-nav";
import { UserNav } from "@/components/user-nav";

interface DashboardShellProps {
  children: React.ReactNode;
}

export function DashboardShell({ children }: DashboardShellProps) {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="container flex h-16 items-center justify-between py-4">
          <MainNav />
          <UserNav />
        </div>
      </header>
      <main className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex-1 space-y-4">
          {children}
        </div>
      </main>
      <Toaster />
    </div>
  );
}