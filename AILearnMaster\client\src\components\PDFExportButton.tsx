import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FileDown, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { exportProgressToPDF } from '@/services/pdfExport';
import { useQuery } from '@tanstack/react-query';

export function PDFExportButton() {
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  // Fetch progress data
  const { data: progressData, isLoading } = useQuery({
    queryKey: ['/api/progress/export-data'],
    retry: false,
  });

  const handleExport = async () => {
    if (!progressData) {
      toast({
        title: "Unable to export",
        description: "Progress data is not available. Please try again.",
        variant: "destructive",
      });
      return;
    }

    setIsExporting(true);
    
    try {
      await exportProgressToPDF(progressData);
      
      toast({
        title: "Export successful!",
        description: "Your learning progress has been exported to PDF.",
        variant: "default",
      });
    } catch (error: any) {
      console.error('PDF export error:', error);
      toast({
        title: "Export failed",
        description: "Unable to export your progress. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Button
      onClick={handleExport}
      disabled={isExporting || isLoading || !progressData}
      className="flex items-center gap-2"
      size="sm"
    >
      {isExporting ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <FileDown className="h-4 w-4" />
      )}
      {isExporting ? 'Exporting...' : 'Export to PDF'}
    </Button>
  );
}