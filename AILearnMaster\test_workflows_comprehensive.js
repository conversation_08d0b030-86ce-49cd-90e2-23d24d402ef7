#!/usr/bin/env node

/**
 * Comprehensive Course Creation Workflows Testing Script
 * Tests both Traditional and Avatar course creation workflows
 * Powered by Mistral AI on Modal GPU infrastructure
 */

import fs from 'fs';
import path from 'path';
import FormData from 'form-data';
import fetch from 'node-fetch';

// Test configuration
const SERVER_URL = process.env.SERVER_URL || 'http://localhost:5000';
const API_BASE = `${SERVER_URL}/api/workflows`;

class WorkflowTester {
  constructor() {
    this.testResults = [];
    this.currentWorkflowId = null;
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Workflow Testing...\n');
    console.log('=' .repeat(60));

    try {
      // Test Traditional Course Workflow
      await this.testTraditionalWorkflow();
      
      // Wait between tests
      await this.delay(2000);
      
      // Test Avatar Course Workflow  
      await this.testAvatarWorkflow();
      
      // Test workflow status endpoints
      await this.testStatusEndpoints();
      
      // Test voice provider endpoints
      await this.testVoiceProviders();
      
      this.generateReport();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  async testTraditionalWorkflow() {
    console.log('\n📚 Testing Traditional Course Creation Workflow');
    console.log('-'.repeat(50));

    const testData = {
      title: "Advanced JavaScript Development",
      description: "Master modern JavaScript development techniques including ES6+, async programming, and best practices for scalable applications.",
      category: "technology",
      difficulty: "intermediate",
      voiceProvider: "coqui",
      voiceId: "v2/en_speaker_6",
      voiceSettings: {
        speed: 1.0,
        pitch: 1.0,
        volume: 1.0
      }
    };

    try {
      console.log('📤 Sending traditional course creation request...');
      
      const response = await this.makeRequest('POST', '/traditional-course', testData);
      
      if (response.success) {
        this.currentWorkflowId = response.workflowId;
        console.log(`✅ Traditional workflow started successfully`);
        console.log(`🆔 Workflow ID: ${this.currentWorkflowId}`);
        
        // Monitor progress
        await this.monitorWorkflowProgress(this.currentWorkflowId, 'Traditional Course');
        
        this.testResults.push({
          test: 'Traditional Course Workflow',
          status: 'PASSED',
          workflowId: this.currentWorkflowId,
          timestamp: new Date().toISOString()
        });
      } else {
        throw new Error(`Traditional workflow failed: ${response.error}`);
      }
      
    } catch (error) {
      console.error('❌ Traditional workflow test failed:', error.message);
      this.testResults.push({
        test: 'Traditional Course Workflow',
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async testAvatarWorkflow() {
    console.log('\n🤖 Testing Avatar Course Creation Workflow');
    console.log('-'.repeat(50));

    try {
      // Create a test avatar image file
      const avatarImagePath = await this.createTestAvatarImage();
      
      const testData = {
        title: "Data Science Fundamentals",
        description: "Learn the core concepts of data science including statistics, machine learning, and data visualization techniques.",
        category: "technology", 
        difficulty: "beginner",
        voiceProvider: "openai",
        voiceId: "alloy",
        voiceSettings: {
          speed: 1.0,
          pitch: 1.0,
          volume: 1.0
        }
      };

      console.log('📤 Sending avatar course creation request...');
      
      const response = await this.makeMultipartRequest('/avatar-course', testData, {
        avatarImage: avatarImagePath
      });
      
      if (response.success) {
        this.currentWorkflowId = response.workflowId;
        console.log(`✅ Avatar workflow started successfully`);
        console.log(`🆔 Workflow ID: ${this.currentWorkflowId}`);
        
        // Monitor progress
        await this.monitorWorkflowProgress(this.currentWorkflowId, 'Avatar Course');
        
        this.testResults.push({
          test: 'Avatar Course Workflow',
          status: 'PASSED',
          workflowId: this.currentWorkflowId,
          timestamp: new Date().toISOString()
        });
      } else {
        throw new Error(`Avatar workflow failed: ${response.error}`);
      }
      
      // Cleanup test file
      if (fs.existsSync(avatarImagePath)) {
        fs.unlinkSync(avatarImagePath);
      }
      
    } catch (error) {
      console.error('❌ Avatar workflow test failed:', error.message);
      this.testResults.push({
        test: 'Avatar Course Workflow',
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async testStatusEndpoints() {
    console.log('\n📊 Testing Status Endpoints');
    console.log('-'.repeat(50));

    try {
      if (!this.currentWorkflowId) {
        console.log('⚠️  No workflow ID available for status testing');
        return;
      }

      const statusResponse = await this.makeRequest('GET', `/status/${this.currentWorkflowId}`);
      
      if (statusResponse) {
        console.log('✅ Status endpoint working');
        console.log(`📈 Current status: ${statusResponse.status}`);
        console.log(`📋 Progress: ${statusResponse.progress}%`);
        
        this.testResults.push({
          test: 'Status Endpoints',
          status: 'PASSED',
          timestamp: new Date().toISOString()
        });
      }
      
    } catch (error) {
      console.error('❌ Status endpoint test failed:', error.message);
      this.testResults.push({
        test: 'Status Endpoints',
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async testVoiceProviders() {
    console.log('\n🎤 Testing Voice Provider Endpoints');
    console.log('-'.repeat(50));

    const providers = ['coqui', 'openai', 'elevenlabs'];
    
    for (const provider of providers) {
      try {
        console.log(`🔍 Testing ${provider} voices...`);
        
        const voicesResponse = await this.makeRequest('GET', `/voices/${provider}`);
        
        if (voicesResponse && voicesResponse.length > 0) {
          console.log(`✅ ${provider}: Found ${voicesResponse.length} voices`);
          
          this.testResults.push({
            test: `Voice Provider - ${provider}`,
            status: 'PASSED',
            voiceCount: voicesResponse.length,
            timestamp: new Date().toISOString()
          });
        } else {
          console.log(`⚠️  ${provider}: No voices available`);
        }
        
      } catch (error) {
        console.error(`❌ ${provider} voice test failed:`, error.message);
        this.testResults.push({
          test: `Voice Provider - ${provider}`,
          status: 'FAILED',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }
  }

  async monitorWorkflowProgress(workflowId, workflowType) {
    console.log(`📈 Monitoring ${workflowType} progress...`);
    
    const maxChecks = 20; // Maximum number of status checks
    const checkInterval = 3000; // 3 seconds between checks
    
    for (let i = 0; i < maxChecks; i++) {
      try {
        const status = await this.makeRequest('GET', `/status/${workflowId}`);
        
        if (status) {
          const progressBar = this.createProgressBar(status.progress || 0);
          console.log(`${progressBar} ${status.step || 'Processing...'}`);
          
          if (status.status === 'completed') {
            console.log('🎉 Workflow completed successfully!');
            if (status.results) {
              console.log('📊 Results summary:');
              this.logResults(status.results);
            }
            break;
          } else if (status.status === 'failed') {
            console.log('❌ Workflow failed');
            if (status.error) {
              console.log('Error:', status.error);
            }
            break;
          }
        }
        
        await this.delay(checkInterval);
        
      } catch (error) {
        console.error('Status check failed:', error.message);
        break;
      }
    }
  }

  async createTestAvatarImage() {
    // Create a simple test image (1x1 pixel PNG)
    const testImagePath = path.join(__dirname, 'test-avatar.png');
    const pngData = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
      0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
      0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    fs.writeFileSync(testImagePath, pngData);
    return testImagePath;
  }

  async makeRequest(method, endpoint, data = null) {
    const url = `${API_BASE}${endpoint}`;
    
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      }
    };
    
    if (data && method !== 'GET') {
      options.body = JSON.stringify(data);
    }
    
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error(`Request failed: ${method} ${url}`, error.message);
      throw error;
    }
  }

  async makeMultipartRequest(endpoint, data, files) {
    const url = `${API_BASE}${endpoint}`;
    const form = new FormData();
    
    // Add form fields
    Object.keys(data).forEach(key => {
      if (typeof data[key] === 'object') {
        form.append(key, JSON.stringify(data[key]));
      } else {
        form.append(key, data[key]);
      }
    });
    
    // Add files
    Object.keys(files).forEach(key => {
      form.append(key, fs.createReadStream(files[key]));
    });
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        body: form,
        headers: form.getHeaders()
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
      
    } catch (error) {
      console.error(`Multipart request failed: POST ${url}`, error.message);
      throw error;
    }
  }

  createProgressBar(progress, width = 30) {
    const filled = Math.round((progress / 100) * width);
    const empty = width - filled;
    return `[${'█'.repeat(filled)}${' '.repeat(empty)}] ${progress}%`;
  }

  logResults(results) {
    if (results.courseStructure) {
      console.log(`📚 Generated ${results.courseStructure.modules?.length || 0} modules`);
    }
    if (results.scripts) {
      console.log(`📝 Created ${results.scripts.length} lesson scripts`);
    }
    if (results.audioFiles) {
      console.log(`🎵 Generated ${results.audioFiles.length} audio files`);
    }
    if (results.slideFiles) {
      console.log(`📊 Created ${results.slideFiles.length} slide presentations`);
    }
    if (results.videoFiles) {
      console.log(`🎬 Assembled ${results.videoFiles.length} video files`);
    }
    if (results.avatarVideos) {
      console.log(`🤖 Generated ${results.avatarVideos.length} avatar videos`);
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  generateReport() {
    console.log('\n📋 COMPREHENSIVE TEST REPORT');
    console.log('=' .repeat(60));
    
    const passed = this.testResults.filter(t => t.status === 'PASSED').length;
    const failed = this.testResults.filter(t => t.status === 'FAILED').length;
    const total = this.testResults.length;
    
    console.log(`\n📊 Overall Results: ${passed}/${total} tests passed`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%\n`);
    
    console.log('📝 Detailed Results:');
    console.log('-'.repeat(50));
    
    this.testResults.forEach((result, index) => {
      const status = result.status === 'PASSED' ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
      
      if (result.workflowId) {
        console.log(`   🆔 Workflow ID: ${result.workflowId}`);
      }
      if (result.voiceCount) {
        console.log(`   🎤 Voices available: ${result.voiceCount}`);
      }
      if (result.error) {
        console.log(`   ❌ Error: ${result.error}`);
      }
      console.log(`   ⏰ ${result.timestamp}\n`);
    });
    
    // Save detailed report
    const reportPath = path.join(__dirname, 'workflow-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: { total, passed, failed, successRate: (passed / total) * 100 },
      results: this.testResults
    }, null, 2));
    
    console.log(`📁 Detailed report saved to: ${reportPath}`);
    
    if (failed > 0) {
      console.log('\n⚠️  Some tests failed. Check the error messages above.');
      process.exit(1);
    } else {
      console.log('\n🎉 All tests passed successfully!');
      console.log('🚀 Both Traditional and Avatar course creation workflows are working correctly.');
    }
  }
}

// Run the test suite
async function main() {
  const tester = new WorkflowTester();
  await tester.runAllTests();
}

// Check if running directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = WorkflowTester;