import OpenAI from "openai";
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize OpenAI and Google Gemini clients conditionally
let openai: OpenAI | null = null;
if (process.env.OPENAI_API_KEY) {
  openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
}
let genAI: GoogleGenerativeAI;

try {
  if (process.env.GOOGLE_API_KEY) {
    genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
    console.log("Google Gemini API initialized successfully");
  } else {
    console.warn("GOOGLE_API_KEY not found, Gemini features won't be available");
  }
} catch (error) {
  console.error("Failed to initialize Google Gemini API:", error);
}

/**
 * Generate a complete lesson script using available AI services
 */
export async function generateLessonScript(params: {
  courseTitle: string;
  courseDescription?: string;
  moduleTitle?: string;
  moduleDescription?: string;
  lessonTitle: string;
  lessonDescription?: string;
  targetAudience?: string;
  tone?: string;
  wordCount?: number;
  includeExamples?: boolean;
  keyPoints?: string[];
}): Promise<string> {
  try {
    // First try with Gemini if available
    if (genAI) {
      try {
        return await generateScriptWithGemini(params);
      } catch (geminiError) {
        console.error("Gemini script generation failed, falling back to OpenAI:", geminiError);
      }
    }

    // Fallback to OpenAI
    return await generateScriptWithOpenAI(params);
  } catch (error) {
    console.error("All script generation methods failed:", error);
    throw new Error("Failed to generate script with any available AI service");
  }
}

/**
 * Generate a script with Google's Gemini AI
 */
async function generateScriptWithGemini(params: {
  courseTitle: string;
  courseDescription?: string;
  moduleTitle?: string;
  moduleDescription?: string;
  lessonTitle: string;
  lessonDescription?: string;
  targetAudience?: string;
  tone?: string;
  wordCount?: number;
  includeExamples?: boolean;
  keyPoints?: string[];
}): Promise<string> {
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    
    // Build prompt with all available information
    let prompt = `Write a comprehensive educational script for a video lesson with the following details:
    
Title: ${params.lessonTitle}
Course: ${params.courseTitle}
`;
    
    if (params.courseDescription) {
      prompt += `Course Description: ${params.courseDescription}\n`;
    }
    
    if (params.moduleTitle) {
      prompt += `Module: ${params.moduleTitle}\n`;
    }
    
    if (params.moduleDescription) {
      prompt += `Module Description: ${params.moduleDescription}\n`;
    }
    
    if (params.lessonDescription) {
      prompt += `Lesson Description: ${params.lessonDescription}\n`;
    }
    
    if (params.targetAudience) {
      prompt += `Target Audience: ${params.targetAudience}\n`;
    }
    
    if (params.tone) {
      prompt += `Tone: ${params.tone}\n`;
    }
    
    if (params.wordCount) {
      prompt += `Word Count: Approximately ${params.wordCount} words\n`;
    }
    
    if (params.keyPoints && params.keyPoints.length > 0) {
      prompt += `\nKey Points to Cover:\n${params.keyPoints.map(point => `- ${point}`).join('\n')}\n`;
    }
    
    prompt += `\nScript Requirements:
- This script will be directly processed by a text-to-speech system
- Include only the educational content that should be spoken
- Include a clear introduction that hooks the learner
- Cover all key concepts in a logical order
- ${params.includeExamples ? "Include practical examples and applications" : "Focus on theoretical concepts"}
- Use ${params.tone || "conversational"} language appropriate for ${params.targetAudience || "beginners"}
- End with a conclusion that summarizes key points and next steps
- Ensure the script directly relates to the module "${params.moduleTitle || "Module"}"

Format the script for direct text-to-speech processing:
- No markdown formatting, headers, bullets, or other special notation
- No camera directions, visual cues, or meta commentary
- No phrases like "in this video" or references to visual elements
- Use natural language patterns with appropriate pauses
- Keep content clean and focused solely on the educational material`;

    // Generate content
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    return text;
  } catch (error) {
    console.error("Error in Gemini script generation:", error);
    throw error;
  }
}

/**
 * Generate a script with OpenAI
 */
async function generateScriptWithOpenAI(params: {
  courseTitle: string;
  courseDescription?: string;
  moduleTitle?: string;
  moduleDescription?: string;
  lessonTitle: string;
  lessonDescription?: string;
  targetAudience?: string;
  tone?: string;
  wordCount?: number;
  includeExamples?: boolean;
  keyPoints?: string[];
}): Promise<string> {
  try {
    // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert script writer for educational videos. Create a professional, clean script for a lesson titled "${params.lessonTitle}" which is part of the module "${params.moduleTitle || "Module"}" in the course "${params.courseTitle}".
          
Important instructions:
1. Focus ONLY on delivering educational content - no meta commentary, no notes to the speaker
2. Use a ${params.tone || "conversational"} tone suitable for ${params.targetAudience || "beginners"}
3. ${params.wordCount ? `The script should be approximately ${params.wordCount} words.` : ""}
4. ${params.includeExamples ? "Include practical examples and applications." : ""}
5. Write as if the script will be read directly by a text-to-speech system
6. Do NOT include any markdown formatting, headers, bullets, or other non-spoken notation
7. Do NOT include camera directions, visual cues, or any content that won't be spoken
8. Include natural pauses and emphasis where appropriate (e.g., "Let's pause and consider..." rather than "[PAUSE]")
9. Avoid phrases like "in this video" or references to visual elements
10. Ensure the script directly relates to and references the specific module "${params.moduleTitle || "Module"}"`
        },
        {
          role: "user",
          content: `Please write a clean, professional script for my lesson "${params.lessonTitle}" which is part of module "${params.moduleTitle || "Module"}" in my course "${params.courseTitle}".
          
${params.courseDescription ? `Course Description: ${params.courseDescription}` : ""}
${params.moduleDescription ? `Module Description: ${params.moduleDescription}` : ""}
${params.lessonDescription ? `Lesson Description: ${params.lessonDescription}` : ""}

${params.keyPoints && params.keyPoints.length > 0 ? `Please cover these key points:\n${params.keyPoints.map(point => `- ${point}`).join('\n')}` : ""}

This script will be directly processed by a text-to-speech system, so:
- Include only the educational content that should be spoken
- Create a compelling introduction that hooks the learner
- Organize the main content in a logical sequence
- End with a clear conclusion summarizing key points
- No metadata, formatting symbols, or instructions to the narrator
- No references to visuals or "this video"
- Mention the module "${params.moduleTitle || "Module"}" by name to reinforce the connection
- Maintain a natural, conversational flow throughout`
        }
      ],
      temperature: 0.7,
    });

    return response.choices[0].message.content || "Script generation failed. Please try again.";
  } catch (error) {
    console.error("Error in OpenAI script generation:", error);
    throw error;
  }
}

/**
 * Complete a partial script using AI
 */
export async function completePartialScript(params: {
  partialScript: string;
  courseTitle: string;
  lessonTitle: string;
  tone?: string;
  targetAudience?: string;
}): Promise<string> {
  try {
    // First try with Gemini if available
    if (genAI) {
      try {
        return await completeScriptWithGemini(params);
      } catch (geminiError) {
        console.error("Gemini script completion failed, falling back to OpenAI:", geminiError);
      }
    }

    // Fallback to OpenAI
    return await completeScriptWithOpenAI(params);
  } catch (error) {
    console.error("All script completion methods failed:", error);
    throw new Error("Failed to complete script with any available AI service");
  }
}

/**
 * Complete a partial script using Gemini
 */
async function completeScriptWithGemini(params: {
  partialScript: string;
  courseTitle: string;
  lessonTitle: string;
  tone?: string;
  targetAudience?: string;
}): Promise<string> {
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    
    // Build prompt
    const prompt = `I've started writing a script for a lesson titled "${params.lessonTitle}" in my course "${params.courseTitle}". Please help me complete it while maintaining a consistent tone and style.
    
Here's what I've written so far:

"""
${params.partialScript}
"""

Please continue and complete this script in a ${params.tone || "conversational"} tone for ${params.targetAudience || "beginners"}. Maintain the same style and voice as I've established. Add any necessary content to make this a complete lesson, including a proper conclusion.`;

    // Generate content
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();
    
    // Combine the original partial script with the AI continuation
    return params.partialScript + "\n\n" + extractCompletionFromResponse(text, params.partialScript);
  } catch (error) {
    console.error("Error in Gemini script completion:", error);
    throw error;
  }
}

/**
 * Complete a partial script using OpenAI
 */
async function completeScriptWithOpenAI(params: {
  partialScript: string;
  courseTitle: string;
  lessonTitle: string;
  tone?: string;
  targetAudience?: string;
}): Promise<string> {
  try {
    // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are helping to complete a partially written educational script. Continue the script in the same style and tone, maintaining consistency with what's already written. Complete the script to make it a comprehensive lesson.`
        },
        {
          role: "user",
          content: `I've started writing a script for a lesson titled "${params.lessonTitle}" in my course "${params.courseTitle}". Please help me complete it while maintaining a consistent ${params.tone || "conversational"} tone for ${params.targetAudience || "beginners"}.
          
Here's what I've written so far:

"""
${params.partialScript}
"""

Please continue from where I left off and complete the script. Maintain the same style and voice as I've established. Add any necessary content to make this a complete lesson, including a proper conclusion.`
        }
      ],
      temperature: 0.7,
    });

    // Combine the original partial script with the AI continuation
    const aiCompletion = response.choices[0].message.content || "";
    return params.partialScript + "\n\n" + extractCompletionFromResponse(aiCompletion, params.partialScript);
  } catch (error) {
    console.error("Error in OpenAI script completion:", error);
    throw error;
  }
}

/**
 * Generate a script segment using AI
 */
export async function generateScriptSegment(params: {
  courseTitle: string;
  lessonTitle: string;
  segmentType: string;
  existingContent?: string;
  targetAudience?: string;
  tone?: string;
}): Promise<string> {
  try {
    // First try with Gemini if available
    if (genAI) {
      try {
        return await generateSegmentWithGemini(params);
      } catch (geminiError) {
        console.error("Gemini segment generation failed, falling back to OpenAI:", geminiError);
      }
    }

    // Fallback to OpenAI
    return await generateSegmentWithOpenAI(params);
  } catch (error) {
    console.error("All script segment generation methods failed:", error);
    throw new Error("Failed to generate script segment with any available AI service");
  }
}

/**
 * Generate a script segment with Gemini
 */
async function generateSegmentWithGemini(params: {
  courseTitle: string;
  lessonTitle: string;
  segmentType: string;
  existingContent?: string;
  targetAudience?: string;
  tone?: string;
}): Promise<string> {
  try {
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro" });
    
    // Map segment type to a more descriptive request
    const segmentDescriptions: Record<string, string> = {
      intro: "Create an introduction that hooks the viewer and previews what they'll learn",
      body: "Write a main content section that explains key concepts clearly",
      conclusion: "Create a conclusion that summarizes key points and provides next steps",
      callout: "Write an important callout highlighting a critical concept or warning",
      explanation: "Create a detailed explanation that breaks down a complex concept"
    };
    
    const segmentDescription = segmentDescriptions[params.segmentType] || `Create a ${params.segmentType} section`;
    
    // Build prompt
    let prompt = `For a lesson titled "${params.lessonTitle}" in the course "${params.courseTitle}", ${segmentDescription}.
    
Use a ${params.tone || "conversational"} tone suitable for ${params.targetAudience || "beginners"}.`;

    if (params.existingContent) {
      prompt += `\n\nHere's some content that's already in the script that you should build upon or improve:\n\n"""${params.existingContent}"""\n\nPlease create an improved version of this ${params.segmentType} section.`;
    }

    // Generate content
    const result = await model.generateContent(prompt);
    const response = result.response;
    return response.text();
  } catch (error) {
    console.error("Error in Gemini script segment generation:", error);
    throw error;
  }
}

/**
 * Generate a script segment with OpenAI
 */
async function generateSegmentWithOpenAI(params: {
  courseTitle: string;
  lessonTitle: string;
  segmentType: string;
  existingContent?: string;
  targetAudience?: string;
  tone?: string;
}): Promise<string> {
  try {
    // Map segment type to a more descriptive request
    const segmentDescriptions: Record<string, string> = {
      intro: "an introduction that hooks the viewer and previews what they'll learn",
      body: "a main content section that explains key concepts clearly",
      conclusion: "a conclusion that summarizes key points and provides next steps",
      callout: "an important callout highlighting a critical concept or warning",
      explanation: "a detailed explanation that breaks down a complex concept"
    };
    
    const segmentDescription = segmentDescriptions[params.segmentType] || `a ${params.segmentType} section`;
    
    // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: `You are an expert educational script writer. Create ${segmentDescription} for a lesson titled "${params.lessonTitle}" in the course "${params.courseTitle}".
          
Use a ${params.tone || "conversational"} tone suitable for ${params.targetAudience || "beginners"}.`
        },
        {
          role: "user",
          content: `Please write ${segmentDescription} for my lesson "${params.lessonTitle}".
          
${params.existingContent ? `Here's some content that's already in the script that you should build upon or improve:\n\n"""${params.existingContent}"""\n\nPlease create an improved version of this ${params.segmentType} section.` : `Create a new ${params.segmentType} section for this lesson.`}`
        }
      ],
      temperature: 0.7,
    });

    return response.choices[0].message.content || `Failed to generate ${params.segmentType} segment. Please try again.`;
  } catch (error) {
    console.error("Error in OpenAI script segment generation:", error);
    throw error;
  }
}

/**
 * Helper function to extract the completion portion from the AI response
 * Removes any repetition of the original content
 */
function extractCompletionFromResponse(aiResponse: string, originalContent: string): string {
  // Remove any quotes or continuation markers
  let cleanedResponse = aiResponse.replace(/^.*?"""\s*|^.*?Here's a continuation:\s*/i, '');
  
  // Remove the original content if it appears in the response
  const escapedOriginal = originalContent.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const originalRegex = new RegExp(`^${escapedOriginal}\\s*`, 'i');
  cleanedResponse = cleanedResponse.replace(originalRegex, '');
  
  return cleanedResponse;
}