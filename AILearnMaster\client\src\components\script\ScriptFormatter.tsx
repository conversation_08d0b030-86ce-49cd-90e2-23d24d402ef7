import React, { useState } from 'react';
import { Textarea } from "@/components/ui/textarea";
import { Info, MessageSquare, <PERSON>rk<PERSON>, Copy } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { cn } from "@/lib/utils";

interface ScriptFormatterProps {
  script: string;
  onChange: (formattedScript: string) => void;
  minHeight?: string;
  onExtractedTextChange?: (extractedText: string) => void;
}

export function ScriptFormatter({ 
  script, 
  onChange, 
  minHeight = "300px",
  onExtractedTextChange 
}: ScriptFormatterProps) {
  const [isRawMode, setIsRawMode] = useState(false);
  const [showExtractedDialog, setShowExtractedDialog] = useState(false);
  const [extractedText, setExtractedText] = useState('');
  const { toast } = useToast();

  // Simple formatting for titles, subtitles, and regular text
  const formatScript = (text: string) => {
    if (!text || text.trim() === '') {
      return '';
    }

    // Simple HTML formatting preserving line breaks and basic structure
    const lines = text.split('\n');
    let html = '';
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      
      if (trimmedLine === '') {
        html += '<br/>';
      } else if (trimmedLine.startsWith('# ')) {
        // Main title - using '#'
        const title = trimmedLine.substring(2);
        html += `<h2 class="text-xl font-bold mt-4 mb-2 text-primary">${title}</h2>`;
      } else if (trimmedLine.startsWith('## ')) {
        // Subtitle - using '##'
        const subtitle = trimmedLine.substring(3);
        html += `<h3 class="text-lg font-semibold mt-3 mb-1">${subtitle}</h3>`;
      } else if (trimmedLine.startsWith('**') && trimmedLine.endsWith('**')) {
        // Title alternative with **Title**
        const title = trimmedLine.substring(2, trimmedLine.length - 2);
        html += `<h2 class="text-xl font-bold mt-4 mb-2 text-primary">${title}</h2>`;
      } else if (trimmedLine.startsWith('[') && trimmedLine.endsWith(']')) {
        // Stage directions [action]
        const direction = trimmedLine.substring(1, trimmedLine.length - 1);
        html += `<p class="my-2 text-gray-500 italic">[${direction}]</p>`;
      } else {
        // Regular paragraph text
        html += `<p class="my-2">${trimmedLine}</p>`;
      }
    });
    
    return html;
  };

  // Extract only the spoken text for TTS processing
  const extractSpokenText = (): string => {
    if (!script || script.trim() === '') {
      return '';
    }

    const lines = script.split('\n');
    const spokenText: string[] = [];
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      
      // Skip empty lines, titles, subtitles, and stage directions
      if (trimmedLine === '' || 
          trimmedLine.startsWith('# ') || 
          trimmedLine.startsWith('## ') ||
          (trimmedLine.startsWith('[') && trimmedLine.endsWith(']')) ||
          (trimmedLine.startsWith('**') && trimmedLine.endsWith('**'))) {
        return;
      }
      
      // Add spoken text
      spokenText.push(trimmedLine);
    });
    
    return spokenText.join(' ');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
  };

  const handleExtractText = () => {
    const spoken = extractSpokenText();
    setExtractedText(spoken);
    
    if (onExtractedTextChange) {
      onExtractedTextChange(spoken);
    }
    
    setShowExtractedDialog(true);
  };

  const handleUseExtractedText = () => {
    onChange(extractedText);
    setShowExtractedDialog(false);
    
    toast({
      title: "Script updated",
      description: "Your script has been updated with just the spoken text.",
      variant: "default"
    });
  };

  const handleCopyExtractedText = () => {
    navigator.clipboard.writeText(extractedText);
    
    toast({
      title: "Text copied",
      description: "Extracted text copied to clipboard!",
      variant: "default"
    });
  };

  return (
    <div className="script-formatter-container">
      <div className="flex justify-between items-center mb-2">
        <div className="flex space-x-2">
          <Button 
            variant={isRawMode ? "outline" : "default"} 
            size="sm" 
            onClick={() => setIsRawMode(false)}
            className="text-xs"
          >
            Preview
          </Button>
          <Button 
            variant={isRawMode ? "default" : "outline"} 
            size="sm" 
            onClick={() => setIsRawMode(true)}
            className="text-xs"
          >
            Edit
          </Button>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={handleExtractText}
          className="text-xs"
          disabled={!script || script.trim() === ''}
        >
          <MessageSquare className="h-3 w-3 mr-1" />
          Extract Spoken Text
        </Button>
      </div>
      
      <Alert variant="default" className="mb-3 bg-blue-50 text-blue-800 border-blue-200">
        <Info className="h-4 w-4 text-blue-500" />
        <AlertDescription className="text-xs">
          Use <strong># Title</strong> for main headings, <strong>## Subtitle</strong> for subheadings, and <strong>[action]</strong> for stage directions. Only regular text will be extracted for voice generation.
        </AlertDescription>
      </Alert>
      
      {isRawMode ? (
        <Textarea
          value={script}
          onChange={handleInputChange}
          className="text-sm"
          style={{ minHeight }}
          placeholder="Enter your script here..."
        />
      ) : (
        <div 
          className="relative border rounded-md p-4 bg-white overflow-auto"
          style={{ minHeight }}
        >
          <ScrollArea className="h-full">
            {script ? (
              <div 
                className="prose prose-sm max-w-none" 
                dangerouslySetInnerHTML={{ __html: formatScript(script) }}
              />
            ) : (
              <div className="text-sm text-gray-400 italic">
                Enter your script in Edit mode to see the formatted version here...
              </div>
            )}
          </ScrollArea>
        </div>
      )}

      {/* Dialog for extracted text */}
      <Dialog open={showExtractedDialog} onOpenChange={setShowExtractedDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Sparkles className="h-5 w-5 mr-2 text-indigo-500" />
              Extracted Spoken Text
              <Badge variant="outline" className="ml-2 bg-indigo-50 text-indigo-700 border-indigo-200">
                For TTS
              </Badge>
            </DialogTitle>
            <DialogDescription>
              This text contains only the spoken content, perfect for text-to-speech processing.
            </DialogDescription>
          </DialogHeader>
          
          <div className="mt-2">
            <ScrollArea className="h-56 rounded-md border p-4 bg-gray-50">
              {extractedText ? (
                <p className="text-sm text-gray-800 whitespace-pre-wrap">{extractedText}</p>
              ) : (
                <p className="text-sm text-gray-400 italic">No spoken text found in your script.</p>
              )}
            </ScrollArea>
          </div>
          
          <DialogFooter className="flex justify-between items-center sm:justify-between mt-4">
            <div>
              <span className="text-xs text-gray-500">
                {extractedText.length} characters · ~{Math.round(extractedText.length / 15)} seconds
              </span>
            </div>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleCopyExtractedText}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy
              </Button>
              <Button 
                variant="default" 
                size="sm"
                onClick={handleUseExtractedText}
              >
                Use This Text
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}