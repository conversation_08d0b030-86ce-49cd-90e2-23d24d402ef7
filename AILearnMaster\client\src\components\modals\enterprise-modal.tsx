import { useState, useEffect } from 'react';
import { Elements, PaymentElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Crown, Check, Loader2, CreditCard, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

// Initialize Stripe
if (!import.meta.env.VITE_STRIPE_PUBLIC_KEY) {
  throw new Error('Missing required Stripe key: VITE_STRIPE_PUBLIC_KEY');
}
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

interface EnterpriseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const CheckoutForm = ({ onSuccess }: { onSuccess?: () => void }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/dashboard?upgrade=success`,
        },
      });

      if (error) {
        toast({
          title: "Payment Failed",
          description: error.message || "An error occurred during payment processing",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Payment Successful!",
          description: "Welcome to Enterprise! Your account is being upgraded...",
        });
        onSuccess?.();
      }
    } catch (error) {
      toast({
        title: "Payment Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
        <PaymentElement
          options={{
            layout: 'tabs',
            defaultValues: {
              billingDetails: {
                email: '',
              }
            }
          }}
        />
      </div>
      
      <Button 
        type="submit" 
        disabled={!stripe || isProcessing}
        className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
        size="lg"
      >
        {isProcessing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing Payment...
          </>
        ) : (
          <>
            <CreditCard className="mr-2 h-4 w-4" />
            Upgrade to Enterprise - $299/month
          </>
        )}
      </Button>
      
      <p className="text-xs text-gray-500 text-center">
        Secure payment processing by Stripe. Cancel anytime.
      </p>
    </form>
  );
};

export default function EnterpriseModal({ isOpen, onClose, onSuccess }: EnterpriseModalProps) {
  const [clientSecret, setClientSecret] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'plan' | 'payment'>('plan');
  const { toast } = useToast();

  const enterpriseFeatures = [
    "Unlimited Courses & Content Creation",
    "200+ AI Credits Monthly",
    "Advanced Video Meetings & Recording",
    "Unlimited Teams & Collaboration",
    "Shared Courses & Role Management",
    "Advanced Course Analytics",
    "Priority AI Processing & Support",
    "White-label Branding & Custom Domain",
    "Custom Integrations & API Access",
    "Advanced Security & Compliance",
    "Priority Customer Support",
    "Dedicated Account Manager"
  ];

  const handleStartPayment = async () => {
    setIsLoading(true);
    try {
      const response = await apiRequest("POST", "/api/create-enterprise-subscription");
      const data = await response.json();
      
      if (data.clientSecret) {
        setClientSecret(data.clientSecret);
        setStep('payment');
      } else {
        throw new Error('No client secret received');
      }
    } catch (error) {
      toast({
        title: "Setup Error",
        description: "Failed to initialize payment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccess = () => {
    onSuccess?.();
    onClose();
    window.location.reload();
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <Crown className="h-6 w-6 text-yellow-500" />
            <div>
              <h2 className="text-xl font-bold">Upgrade to Enterprise</h2>
              <p className="text-sm text-gray-600">
                {step === 'plan' 
                  ? "Unlock all premium features and advanced collaboration tools"
                  : "Complete your Enterprise upgrade with secure payment"
                }
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'plan' ? (
            <div className="space-y-6">
              {/* Plan Details */}
              <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
                <CardHeader className="text-center">
                  <div className="flex justify-center mb-2">
                    <Crown className="h-8 w-8 text-yellow-500" />
                  </div>
                  <CardTitle className="text-2xl">Enterprise Plan</CardTitle>
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-3xl font-bold">$299</span>
                    <span className="text-gray-600">/month</span>
                    <Badge variant="secondary">Most Popular</Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {enterpriseFeatures.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-600 flex-shrink-0" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Call to Action */}
              <div className="space-y-4">
                <Button 
                  onClick={handleStartPayment}
                  disabled={isLoading}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Setting up payment...
                    </>
                  ) : (
                    <>
                      <CreditCard className="mr-2 h-4 w-4" />
                      Continue to Payment - $299/month
                    </>
                  )}
                </Button>
                
                <div className="text-center space-y-2">
                  <p className="text-sm text-gray-600">
                    🔒 Secure payment processing by Stripe
                  </p>
                  <p className="text-xs text-gray-500">
                    Cancel anytime • 30-day money-back guarantee
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Payment Form */}
              {clientSecret && (
                <Elements 
                  stripe={stripePromise} 
                  options={{ 
                    clientSecret,
                    appearance: {
                      theme: 'stripe',
                    }
                  }}
                >
                  <CheckoutForm onSuccess={handleSuccess} />
                </Elements>
              )}
              
              {/* Back Button */}
              <Button 
                variant="outline" 
                onClick={() => setStep('plan')}
                className="w-full"
              >
                ← Back to Plan Details
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}