import { Request, Response } from 'express';
import OpenAI from 'openai';

// The newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

interface ScriptGenerationRequest {
  title: string;
  description: string;
  category: string;
  targetAudience?: string;
  avatarType: string;
  avatarCharacteristics?: any;
}

export async function generateAvatarScript(req: Request, res: Response) {
  try {
    const {
      title,
      description,
      category,
      targetAudience,
      avatarType,
      avatarCharacteristics
    }: ScriptGenerationRequest = req.body;

    if (!title || !description) {
      return res.status(400).json({ 
        error: 'Title and description are required' 
      });
    }

    console.log('Generating avatar script for:', title);

    // Build detailed prompt for script generation optimized for TTS
    const prompt = buildScriptPrompt({
      title,
      description,
      category,
      targetAudience,
      avatarType,
      avatarCharacteristics
    });

    let scriptData;

    try {
      // Try OpenAI first
      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: "You are an expert course script writer specializing in creating content optimized for text-to-speech and avatar presentation. Create clear, engaging, conversational scripts that sound natural when spoken aloud. Focus on educational value and smooth delivery."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000
      });

      const content = response.choices[0].message.content?.trim();
      console.log('OpenAI script generated successfully');
      
      // Parse the response to extract structured segments
      scriptData = parseScriptResponse(content || '', title);
      
    } catch (openaiError: any) {
      console.log('OpenAI failed, trying Gemini fallback:', openaiError?.message);
      
      // Fallback to Gemini
      try {
        const { GoogleGenerativeAI } = require('@google/generative-ai');
        const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY);
        const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
        
        const result = await model.generateContent(prompt);
        const response = await result.response;
        const content = response.text().trim();
        
        console.log('Gemini script generated successfully');
        scriptData = parseScriptResponse(content, title);
        
      } catch (geminiError: any) {
        console.log('Gemini also failed:', geminiError?.message);
        // Create intelligent fallback script
        scriptData = createFallbackScript(title, description, category);
      }
    }

    // Optimize script for TTS
    const optimizedScript = optimizeForTTS(scriptData);

    return res.status(200).json({
      segments: optimizedScript.segments,
      totalDuration: optimizedScript.totalDuration,
      wordCount: optimizedScript.wordCount,
      optimizedForTTS: true,
      message: 'Script generated successfully and optimized for TTS'
    });

  } catch (error: any) {
    console.error('Error generating avatar script:', error);
    return res.status(500).json({ 
      error: 'Failed to generate script',
      details: error.message 
    });
  }
}

function buildScriptPrompt(data: ScriptGenerationRequest): string {
  let prompt = `Create a comprehensive course script for "${data.title}" that will be delivered by an AI avatar and converted to speech using text-to-speech technology.

Course Details:
- Title: ${data.title}
- Description: ${data.description}
- Category: ${data.category}
- Target Audience: ${data.targetAudience || 'General learners'}
- Avatar Type: ${data.avatarType}

Requirements for TTS Optimization:
1. Write in a conversational, natural speaking style
2. Use simple, clear language that sounds good when spoken
3. Avoid complex punctuation and abbreviations
4. Write numbers as words (e.g., "five" instead of "5")
5. Include natural pauses with commas and periods
6. Keep sentences at moderate length for better speech flow

Please create a structured script with 4-6 segments, each covering different aspects of the course topic. Each segment should be:
- 150-250 words long
- Self-contained and focused on one key concept
- Written for clear audio delivery
- Engaging and educational

Format your response as follows:
SEGMENT 1: [Title]
[Content optimized for TTS]

SEGMENT 2: [Title]
[Content optimized for TTS]

Continue this pattern for all segments. Make sure the content flows naturally from one segment to the next and provides comprehensive coverage of the topic.`;

  return prompt;
}

function parseScriptResponse(content: string, courseTitle: string): any {
  const segments = [];
  
  // Split content by segment markers
  const segmentRegex = /SEGMENT\s+(\d+):\s*(.+?)\n([\s\S]*?)(?=SEGMENT\s+\d+:|$)/gi;
  let match;
  let order = 0;

  while ((match = segmentRegex.exec(content)) !== null) {
    const title = match[2].trim();
    const segmentContent = match[3].trim();
    
    if (segmentContent) {
      segments.push({
        id: `segment-${Date.now()}-${order}`,
        title: title,
        content: segmentContent,
        duration: estimateReadingTime(segmentContent),
        order: order
      });
      order++;
    }
  }

  // If no segments were parsed, create a single segment from the entire content
  if (segments.length === 0) {
    segments.push({
      id: `segment-${Date.now()}`,
      title: `Introduction to ${courseTitle}`,
      content: content,
      duration: estimateReadingTime(content),
      order: 0
    });
  }

  return { segments };
}

function estimateReadingTime(text: string): number {
  // Average TTS speed: 150-160 words per minute
  const words = text.split(' ').length;
  return Math.ceil((words / 150) * 60); // Return in seconds
}

function optimizeForTTS(scriptData: any): any {
  const optimizedSegments = scriptData.segments.map((segment: any) => ({
    ...segment,
    content: optimizeTextForTTS(segment.content),
    duration: estimateReadingTime(segment.content)
  }));

  const totalDuration = optimizedSegments.reduce((total: number, segment: any) => 
    total + segment.duration, 0
  );

  const wordCount = optimizedSegments.reduce((total: number, segment: any) => 
    total + segment.content.split(' ').length, 0
  );

  return {
    segments: optimizedSegments,
    totalDuration,
    wordCount
  };
}

function optimizeTextForTTS(text: string): string {
  let optimized = text;
  
  // Replace numbers with words
  optimized = optimized.replace(/\b(\d+)\b/g, (match, number) => {
    const num = parseInt(number);
    if (num <= 20) {
      const numberWords = [
        'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine', 'ten',
        'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen', 'twenty'
      ];
      return numberWords[num] || number;
    }
    return number;
  });

  // Ensure proper spacing around punctuation
  optimized = optimized.replace(/([.!?])\s*([A-Z])/g, '$1 $2');
  
  // Add slight pauses after key transition words
  optimized = optimized.replace(/\b(However|Furthermore|Additionally|Moreover|Therefore|Consequently),\s*/g, '$1, ');
  
  // Clean up extra whitespace
  optimized = optimized.replace(/\s+/g, ' ').trim();
  
  return optimized;
}

function createFallbackScript(title: string, description: string, category: string): any {
  const segments = [
    {
      id: `segment-${Date.now()}-0`,
      title: `Welcome to ${title}`,
      content: `Welcome to this comprehensive course on ${title}. ${description} Throughout this course, you'll gain valuable insights and practical knowledge that you can apply immediately. Let's begin this exciting learning journey together.`,
      duration: 15,
      order: 0
    },
    {
      id: `segment-${Date.now()}-1`,
      title: `Understanding the Fundamentals`,
      content: `To master ${title}, it's essential to understand the core fundamentals. We'll explore the key concepts and principles that form the foundation of this subject. This knowledge will serve as your stepping stone to more advanced topics.`,
      duration: 12,
      order: 1
    },
    {
      id: `segment-${Date.now()}-2`,
      title: `Practical Applications`,
      content: `Now let's dive into the practical applications of what you've learned. We'll examine real-world examples and case studies that demonstrate how these concepts work in practice. This will help you understand how to apply your knowledge effectively.`,
      duration: 13,
      order: 2
    },
    {
      id: `segment-${Date.now()}-3`,
      title: `Moving Forward`,
      content: `Congratulations on completing this course on ${title}. You now have the knowledge and tools needed to succeed in ${category}. Remember to practice what you've learned and continue building upon this foundation. Thank you for your attention and dedication.`,
      duration: 14,
      order: 3
    }
  ];

  return { segments };
}