# 🚀 AILearnMaster Production Deployment Guide

## 📋 **Prerequisites**

### **Required Tools**
- AWS CLI v2.x installed and configured
- Node.js v18+ 
- Git with GitHub access
- Domain name registered (e.g., ailearn.com)

### **AWS Account Setup**
- AWS account with administrative access
- AWS CLI configured with appropriate permissions
- GitHub repository access for Amplify

---

## 🔧 **Step-by-Step Deployment Process**

### **Phase 1: Pre-Deployment Validation** ✅

```bash
# 1. Run security validation (must achieve 85+ score)
npm run security:production-ready

# 2. Verify all security implementations
npx tsx scripts/test-security-implementation.ts

# Expected output: Security Implementation Score: 100%
```

### **Phase 2: AWS Infrastructure Setup**

#### **Step 1: Configure AWS CLI**
```bash
# Configure AWS CLI with your credentials
aws configure

# Verify configuration
aws sts get-caller-identity
```

#### **Step 2: Run Infrastructure Setup Script**
```bash
# Make script executable
chmod +x deployment/setup-aws-infrastructure.sh

# Run infrastructure setup
./deployment/setup-aws-infrastructure.sh

# This creates:
# - S3 buckets for storage and backups
# - CloudFront distribution for CDN
# - IAM roles for Amplify
# - Secrets Manager entries
# - Amplify application
```

#### **Step 3: Update Secrets in AWS Secrets Manager**
```bash
# Update database secret
aws secretsmanager update-secret \
  --secret-id "ailearn-master/production/database" \
  --secret-string '{"DATABASE_URL":"your-actual-database-url-with-ssl"}'

# Update session secrets
aws secretsmanager update-secret \
  --secret-id "ailearn-master/production/session" \
  --secret-string '{"SESSION_SECRET":"your-32-char-session-secret","JWT_SECRET":"your-32-char-jwt-secret","SECRETS_ENCRYPTION_KEY":"your-32-char-encryption-key"}'

# Update AI service secrets
aws secretsmanager update-secret \
  --secret-id "ailearn-master/production/ai-services" \
  --secret-string '{"OPENAI_API_KEY":"your-openai-key","MODAL_TOKEN_ID":"your-modal-id","MODAL_TOKEN_SECRET":"your-modal-secret"}'

# Update AWS service secrets
aws secretsmanager update-secret \
  --secret-id "ailearn-master/production/aws-services" \
  --secret-string '{"AWS_ACCESS_KEY_ID":"your-access-key","AWS_SECRET_ACCESS_KEY":"your-secret-key","AWS_S3_BUCKET":"ailearn-master-storage-prod"}'
```

### **Phase 3: Amplify Application Configuration**

#### **Step 1: Connect GitHub Repository**
1. Go to AWS Amplify Console
2. Select your app: `ailearn-master-prod`
3. Connect GitHub repository: `https://github.com/hajhasni1984/AILearnMaster`
4. Select branch: `main`
5. Configure build settings to use `amplify.yml`

#### **Step 2: Configure Environment Variables**
```bash
# Set your Amplify App ID
export AMPLIFY_APP_ID="your-amplify-app-id"

# Run environment setup script
node deployment/production-env-setup.js

# This configures all production environment variables
```

#### **Step 3: Configure Custom Domain**
1. In Amplify Console, go to Domain Management
2. Add domain: `ailearn.com`
3. Add subdomains:
   - `www.ailearn.com` → main branch
   - `api.ailearn.com` → main branch (backend)
4. AWS will automatically provision SSL certificates

### **Phase 4: Database Configuration**

#### **Step 1: Verify Database SSL Configuration**
```bash
# Test database connection with SSL
npm run db:health

# Verify SSL is enforced
npm run db:verify-ssl
```

#### **Step 2: Run Database Migrations**
```bash
# Set production environment
export NODE_ENV=production

# Run migrations
npm run db:migrate

# Seed initial data if needed
npm run db:seed
```

### **Phase 5: Modal A100 GPU Configuration**

#### **Step 1: Verify Modal Configuration**
```bash
# Test Modal connectivity
modal token set --token-id YOUR_MODAL_TOKEN_ID --token-secret YOUR_MODAL_TOKEN_SECRET

# Deploy Modal functions
modal deploy course-ai-app/backend/modal_gpu_backend.py

# Test Modal endpoints
curl -X POST https://your-modal-endpoint/health
```

#### **Step 2: Configure Modal Environment**
```bash
# Set Modal environment variables
modal secret create ailearn-production \
  OPENAI_API_KEY=your-openai-key \
  AWS_ACCESS_KEY_ID=your-aws-key \
  AWS_SECRET_ACCESS_KEY=your-aws-secret
```

### **Phase 6: Deploy Application**

#### **Step 1: Trigger Amplify Build**
```bash
# Push to main branch to trigger deployment
git add .
git commit -m "Production deployment configuration"
git push origin main

# Or trigger manual build in Amplify Console
```

#### **Step 2: Monitor Deployment**
1. Watch build progress in Amplify Console
2. Check build logs for any errors
3. Verify both frontend and backend deploy successfully

---

## 🔍 **Post-Deployment Validation**

### **Step 1: Application Health Checks**
```bash
# Test frontend
curl -I https://ailearn.com
# Expected: 200 OK with security headers

# Test backend API
curl -I https://api.ailearn.com/api/health
# Expected: 200 OK

# Test database connectivity
curl https://api.ailearn.com/api/db/health
# Expected: Database connection successful
```

### **Step 2: Security Validation**
```bash
# Run production security tests
npm run security:production-ready

# Test HTTPS enforcement
curl -I http://ailearn.com
# Expected: 301 redirect to HTTPS

# Test security headers
curl -I https://ailearn.com
# Expected: CSP, HSTS, X-Frame-Options headers
```

### **Step 3: Functionality Testing**
```bash
# Test course generation workflow
curl -X POST https://api.ailearn.com/api/course-generation/generate \
  -H "Content-Type: application/json" \
  -d '{"title":"Test Course","type":"traditional"}'

# Test file upload
curl -X POST https://api.ailearn.com/api/upload \
  -F "file=@test-image.jpg"

# Test Modal AI services
curl -X POST https://api.ailearn.com/api/ai/generate-text \
  -H "Content-Type: application/json" \
  -d '{"prompt":"Generate a course outline for JavaScript basics"}'
```

### **Step 4: Performance Testing**
```bash
# Test CDN performance
curl -w "@curl-format.txt" -o /dev/null -s https://ailearn.com

# Test API response times
curl -w "@curl-format.txt" -o /dev/null -s https://api.ailearn.com/api/health

# Load test (optional)
# Use tools like Apache Bench or Artillery for load testing
```

---

## 📊 **Monitoring and Maintenance**

### **CloudWatch Monitoring**
- Application logs: `/aws/amplify/ailearn-master-prod`
- Error rate alarms configured
- Performance monitoring enabled

### **Security Monitoring**
- AWS WAF rules active
- Security headers enforced
- Rate limiting operational
- SSL/TLS certificates auto-renewed

### **Backup and Recovery**
- Database backups: Daily automated
- S3 versioning enabled
- Cross-region replication configured

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Build Failures**
```bash
# Check Amplify build logs
aws amplify get-job --app-id YOUR_APP_ID --branch-name main --job-id JOB_ID

# Common fixes:
# 1. Verify Node.js version in build settings
# 2. Check environment variables are set
# 3. Ensure all dependencies are in package.json
```

#### **Database Connection Issues**
```bash
# Test database connectivity
npm run db:health

# Common fixes:
# 1. Verify DATABASE_URL includes sslmode=require
# 2. Check database credentials in Secrets Manager
# 3. Ensure database allows connections from AWS
```

#### **Modal GPU Issues**
```bash
# Test Modal connectivity
modal token verify

# Common fixes:
# 1. Verify Modal tokens are correct
# 2. Check Modal function deployments
# 3. Ensure Modal endpoints are accessible from AWS
```

---

## ✅ **Success Criteria Checklist**

### **Infrastructure**
- [ ] AWS Amplify app created and configured
- [ ] Custom domain configured with SSL
- [ ] S3 buckets created with proper policies
- [ ] CloudFront distribution active
- [ ] Secrets Manager configured

### **Application**
- [ ] Frontend accessible via HTTPS
- [ ] Backend API responding correctly
- [ ] Database connections secure with SSL
- [ ] File uploads working through S3
- [ ] Course generation workflows functional

### **Security**
- [ ] Security score ≥ 85% in production
- [ ] All security headers present
- [ ] Rate limiting operational
- [ ] CSRF protection active
- [ ] SSL/TLS enforced everywhere

### **Performance**
- [ ] CDN delivering content efficiently
- [ ] API response times < 2 seconds
- [ ] Course generation < 20 minutes
- [ ] File uploads < 30 seconds

---

## 🎉 **Deployment Complete!**

Your AILearnMaster application is now deployed to production with:

- ✅ **Enterprise-grade security** controls
- ✅ **Scalable AWS infrastructure** 
- ✅ **AI-powered course generation** with Modal A100 GPU
- ✅ **Global content delivery** via CloudFront CDN
- ✅ **Secure file storage** with S3
- ✅ **SSL/TLS encryption** everywhere
- ✅ **Comprehensive monitoring** and alerting

**Production URL**: https://ailearn.com  
**API Endpoint**: https://api.ailearn.com  
**Admin Dashboard**: https://ailearn.com/admin  

**Next Steps**: Monitor application performance, user feedback, and security metrics. Schedule regular security audits and performance optimizations.
