"""
Mistral AI Application on Modal GPU
High-performance AI content generation for Koursia Platform
"""

import modal
import json
from typing import Dict, Any, List, Optional

# Create Modal app
app = modal.App("koursia-mistral-ai")

# Define the Modal image with required dependencies
image = modal.Image.debian_slim(python_version="3.11").pip_install(
    "transformers>=4.35.0",
    "torch>=2.0.0",
    "accelerate>=0.24.0",
    "bitsandbytes>=0.41.0",
    "sentencepiece>=0.1.99",
    "protobuf>=4.21.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0"
)

# GPU configuration for A100
GPU_CONFIG = modal.gpu.A100(size="40GB")

@app.function(
    image=image,
    gpu=GPU_CONFIG,
    timeout=300,
    container_idle_timeout=240,
    allow_concurrent_inputs=10
)
@modal.web_endpoint(method="POST", label="mistral-generate")
def generate_with_mistral(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate content using Mistral-7B-Instruct model on A100 GPU
    """
    import torch
    from transformers import AutoTokenizer, AutoModelForCausalLM
    
    # Load model and tokenizer (cached after first load)
    model_name = "mistralai/Mistral-7B-Instruct-v0.1"
    
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,
        device_map="auto",
        load_in_8bit=True  # Memory optimization
    )
    
    # Extract parameters
    prompt = request_data.get("prompt", "")
    max_tokens = request_data.get("max_tokens", 1000)
    temperature = request_data.get("temperature", 0.7)
    
    if not prompt:
        return {"error": "No prompt provided"}
    
    # Format prompt for instruction-following
    formatted_prompt = f"<s>[INST] {prompt} [/INST]"
    
    # Tokenize and generate
    inputs = tokenizer(formatted_prompt, return_tensors="pt").to(model.device)
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=max_tokens,
            temperature=temperature,
            do_sample=True,
            top_p=0.9,
            top_k=50,
            repetition_penalty=1.1,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # Decode response
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    
    # Extract only the generated part (after the instruction)
    response_start = generated_text.find("[/INST]") + 7
    response = generated_text[response_start:].strip()
    
    return {
        "choices": [
            {
                "message": {
                    "content": response
                }
            }
        ],
        "model": "mistral-7b-instruct",
        "usage": {
            "prompt_tokens": len(inputs["input_ids"][0]),
            "completion_tokens": len(outputs[0]) - len(inputs["input_ids"][0]),
            "total_tokens": len(outputs[0])
        }
    }

@app.function(
    image=image,
    gpu=GPU_CONFIG,
    timeout=300
)
@modal.web_endpoint(method="POST", label="course-structure")
def generate_course_structure(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate comprehensive course structure using Mistral AI
    """
    title = request_data.get("title", "")
    description = request_data.get("description", "")
    category = request_data.get("category", "General")
    target_audience = request_data.get("targetAudience", "All levels")
    
    prompt = f"""Create a comprehensive educational course structure for "{title}".

Course Details:
- Title: {title}
- Description: {description}
- Category: {category}
- Target Audience: {target_audience}

Generate a detailed course structure with:
1. 4-6 modules covering the topic comprehensively
2. Each module should have 3-4 lessons
3. Each lesson should have a title, description, estimated duration (5-20 minutes), and type (video/interactive/text)
4. Include learning objectives and prerequisites
5. Estimate total course duration

Return the response as valid JSON with this exact structure:
{{
  "id": {int(time.time() * 1000)},
  "title": "{title}",
  "description": "{description}",
  "category": "{category}",
  "targetAudience": "{target_audience}",
  "totalDuration": "estimated total time",
  "modules": [
    {{
      "id": 1,
      "title": "module title",
      "description": "module description",
      "lessons": [
        {{
          "id": 1,
          "title": "lesson title",
          "description": "lesson description",
          "duration": "duration in minutes",
          "type": "video"
        }}
      ]
    }}
  ],
  "estimatedCompletionTime": "time estimate",
  "difficulty": "difficulty level",
  "prerequisites": "prerequisites",
  "learningObjectives": ["objective1", "objective2", "objective3", "objective4"]
}}

Make sure the JSON is valid and complete."""

    # Use the main generation function
    response = generate_with_mistral({"prompt": prompt, "max_tokens": 2000, "temperature": 0.7})
    
    try:
        content = response["choices"][0]["message"]["content"]
        
        # Try to extract JSON from the response
        import re
        json_match = re.search(r'\{.*\}', content, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            parsed_json = json.loads(json_str)
            return parsed_json
        else:
            # Fallback structure
            return create_fallback_structure(title, description, category, target_audience)
            
    except Exception as e:
        print(f"Error parsing course structure: {e}")
        return create_fallback_structure(title, description, category, target_audience)

@app.function(
    image=image,
    gpu=GPU_CONFIG,
    timeout=180
)
@modal.web_endpoint(method="POST", label="course-description")
def generate_course_description(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate compelling course description using Mistral AI
    """
    title = request_data.get("title", "")
    category = request_data.get("category", "General")
    target_audience = request_data.get("targetAudience", "All levels")
    
    prompt = f"""Write a compelling, professional course description for "{title}".

Course Details:
- Category: {category}
- Target Audience: {target_audience}

The description should:
- Be engaging and informative (2-3 paragraphs)
- Highlight key benefits and learning outcomes
- Appeal to the target audience
- Include what students will achieve
- Be professional but accessible
- Focus on practical value and skills gained

Write only the course description text, no additional formatting or JSON."""

    response = generate_with_mistral({"prompt": prompt, "max_tokens": 500, "temperature": 0.8})
    
    try:
        content = response["choices"][0]["message"]["content"]
        return {"description": content.strip()}
    except:
        return {"description": f"Learn the fundamentals of {title} in this comprehensive course designed for {target_audience}."}

@app.function(
    image=image,
    gpu=GPU_CONFIG,
    timeout=240
)
@modal.web_endpoint(method="POST", label="lesson-script")
def generate_lesson_script(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate detailed lesson script using Mistral AI
    """
    course_title = request_data.get("courseTitle", "")
    module_title = request_data.get("moduleTitle", "")
    lesson_title = request_data.get("lessonTitle", "")
    lesson_description = request_data.get("lessonDescription", "")
    duration = request_data.get("duration", "10 minutes")
    voice_style = request_data.get("voiceStyle", "professional")
    
    prompt = f"""Create a detailed lesson script for an online course.

Course: {course_title}
Module: {module_title}
Lesson: {lesson_title}
Description: {lesson_description}
Duration: {duration}
Voice Style: {voice_style}

Create a comprehensive lesson script that includes:
1. An engaging introduction (30 seconds)
2. Main content broken into clear sections
3. Examples and practical applications
4. Key takeaways
5. A conclusion that leads to the next lesson

The script should be written for {voice_style} narration and fit within the {duration} timeframe.
Make it engaging, educational, and easy to follow.
Include natural pauses and transitions.

Return only the script text, formatted with clear sections."""

    response = generate_with_mistral({"prompt": prompt, "max_tokens": 1500, "temperature": 0.7})
    
    try:
        content = response["choices"][0]["message"]["content"]
        return {"script": content.strip()}
    except:
        return {"script": f"Welcome to {lesson_title}. In this lesson, we'll explore {lesson_description}. Let's begin our journey into this important topic."}

@app.function(image=image)
@modal.web_endpoint(method="GET", label="health")
def health_check() -> Dict[str, Any]:
    """Health check endpoint"""
    import time
    return {
        "status": "healthy",
        "service": "Mistral AI on Modal GPU",
        "timestamp": time.time(),
        "gpu": "A100-40GB"
    }

def create_fallback_structure(title: str, description: str, category: str, target_audience: str) -> Dict[str, Any]:
    """Create fallback course structure"""
    import time
    
    return {
        "id": int(time.time() * 1000),
        "title": title,
        "description": description,
        "category": category,
        "targetAudience": target_audience,
        "totalDuration": "2 hours 30 minutes",
        "modules": [
            {
                "id": 1,
                "title": "Introduction and Fundamentals",
                "description": f"Getting started with {title} - core concepts and foundations",
                "lessons": [
                    {
                        "id": 1,
                        "title": "Course Overview",
                        "description": "Introduction to the course and what you'll learn",
                        "duration": "5 min",
                        "type": "video"
                    },
                    {
                        "id": 2,
                        "title": "Key Concepts",
                        "description": "Understanding the fundamental principles",
                        "duration": "10 min",
                        "type": "video"
                    },
                    {
                        "id": 3,
                        "title": "Getting Started",
                        "description": "Your first steps in the subject",
                        "duration": "8 min",
                        "type": "video"
                    }
                ]
            },
            {
                "id": 2,
                "title": "Core Principles",
                "description": "Deep dive into the essential principles and methods",
                "lessons": [
                    {
                        "id": 4,
                        "title": "Essential Methods",
                        "description": "Learn the most important techniques",
                        "duration": "12 min",
                        "type": "video"
                    },
                    {
                        "id": 5,
                        "title": "Best Practices",
                        "description": "Industry-standard approaches and guidelines",
                        "duration": "10 min",
                        "type": "video"
                    },
                    {
                        "id": 6,
                        "title": "Common Pitfalls",
                        "description": "Avoid these common mistakes",
                        "duration": "8 min",
                        "type": "video"
                    }
                ]
            },
            {
                "id": 3,
                "title": "Practical Application",
                "description": "Hands-on exercises and real-world examples",
                "lessons": [
                    {
                        "id": 7,
                        "title": "Case Study 1",
                        "description": "Real-world example and analysis",
                        "duration": "15 min",
                        "type": "video"
                    },
                    {
                        "id": 8,
                        "title": "Practice Exercise",
                        "description": "Apply what you've learned",
                        "duration": "20 min",
                        "type": "interactive"
                    },
                    {
                        "id": 9,
                        "title": "Advanced Techniques",
                        "description": "Take your skills to the next level",
                        "duration": "12 min",
                        "type": "video"
                    }
                ]
            },
            {
                "id": 4,
                "title": "Conclusion and Next Steps",
                "description": "Wrap up and plan your continued learning journey",
                "lessons": [
                    {
                        "id": 10,
                        "title": "Course Summary",
                        "description": "Review key takeaways and concepts",
                        "duration": "8 min",
                        "type": "video"
                    },
                    {
                        "id": 11,
                        "title": "Next Steps",
                        "description": "How to continue your learning journey",
                        "duration": "6 min",
                        "type": "video"
                    },
                    {
                        "id": 12,
                        "title": "Resources and References",
                        "description": "Additional materials and further reading",
                        "duration": "5 min",
                        "type": "text"
                    }
                ]
            }
        ],
        "estimatedCompletionTime": "1-2 weeks",
        "difficulty": "Beginner to Intermediate",
        "prerequisites": "None required",
        "learningObjectives": [
            f"Understand the core concepts of {title}",
            "Apply practical techniques and methods",
            "Avoid common mistakes and pitfalls",
            "Build confidence through hands-on practice"
        ]
    }

if __name__ == "__main__":
    print("Deploying Mistral AI application to Modal...")
    print("This will create endpoints for:")
    print("- /mistral-generate (General AI generation)")
    print("- /course-structure (Course structure generation)")
    print("- /course-description (Course description generation)")
    print("- /lesson-script (Lesson script generation)")
    print("- /health (Health check)")