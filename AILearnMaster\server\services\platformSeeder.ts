import { db } from "../db";
import { platformIntegrations } from "@shared/schema";
import { eq } from "drizzle-orm";

export interface PlatformSeed {
  name: string;
  slug: string;
  category: string;
  description: string;
  iconUrl: string;
  authType: 'api_key' | 'oauth';
  apiBaseUrl: string;
  features: any[];
}

const videoPlatforms = [
  {
    name: "YouTube",
    slug: "youtube",
    category: "video_platform",
    description: "Upload and manage your course videos on YouTube, the world's largest video platform",
    iconUrl: "/images/integrations/youtube.svg",
    authType: "api_key",
    apiBaseUrl: "https://developers.google.com/youtube/v3",
    features: ["Auto Publishing", "Audience Analytics", "Monetization", "Video Hosting"]
  },
  {
    name: "Vimeo",
    slug: "vimeo",
    category: "video_platform",
    description: "Professional video hosting with premium features and customization options",
    iconUrl: "/images/integrations/vimeo.svg",
    authType: "api_key",
    apiBaseUrl: "https://developer.vimeo.com",
    features: ["Private Videos", "Custom Branding", "Review Tools", "Password Protection"]
  },
  {
    name: "Wistia",
    slug: "wistia",
    category: "video_platform",
    description: "Video hosting platform optimized for businesses with advanced analytics and engagement tools",
    iconUrl: "/images/integrations/wistia.svg",
    authType: "api_key",
    apiBaseUrl: "https://wistia.com/support/developers",
    features: ["Engagement Metrics", "Lead Generation", "Video Heatmaps", "Customizable Player"]
  }
];

export async function seedVideoPlatforms() {
  console.log("Checking for video platforms...");
  
  // Check if we already have video platforms seeded
  const existingPlatforms = await db.select({ id: platformIntegrations.id, category: platformIntegrations.category })
    .from(platformIntegrations)
    .where(eq(platformIntegrations.category, "video_platform"));
  
  if (existingPlatforms.length > 0) {
    console.log(`Found ${existingPlatforms.length} existing video platforms. Skipping seeding.`);
    return;
  }
  
  console.log("No video platforms found. Seeding video platforms...");
  
  try {
    // Insert all video platforms
    const result = await db.insert(platformIntegrations).values(videoPlatforms).returning();
    console.log(`Seeded ${result.length} video platforms successfully.`);
    return result;
  } catch (error) {
    console.error("Error seeding video platforms:", error);
    throw error;
  }
}

export async function getAllPlatforms() {
  try {
    return await db.select().from(platformIntegrations);
  } catch (error) {
    console.error("Error fetching platforms:", error);
    return [];
  }
}

export async function getPlatformBySlug(slug: string) {
  try {
    const [platform] = await db.select().from(platformIntegrations).where(eq(platformIntegrations.slug, slug));
    return platform;
  } catch (error) {
    console.error(`Error fetching platform with slug ${slug}:`, error);
    return null;
  }
}

export async function getPlatformsByCategory(category: string) {
  try {
    return await db.select().from(platformIntegrations).where(eq(platformIntegrations.category, category));
  } catch (error) {
    console.error(`Error fetching platforms with category ${category}:`, error);
    return [];
  }
}