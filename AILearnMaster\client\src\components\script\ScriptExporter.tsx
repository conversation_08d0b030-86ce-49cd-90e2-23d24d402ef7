import React from 'react';
import { Download, Lock } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useAuth } from "@/hooks/use-auth";
import { canDownloadScripts } from "@/lib/plan-restrictions";
import { useToast } from "@/hooks/use-toast";

interface ScriptExporterProps {
  title: string;
  content: string;
  disabled?: boolean;
}

export function ScriptExporter({ title, content, disabled = false }: ScriptExporterProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const canDownload = canDownloadScripts(user);
  
  // Function to download script as PDF
  const handleDownload = () => {
    // If this was a real PDF export, we would use a library like jsPDF
    // For now, we'll export as a text file for demonstration
    if (!content) {
      toast({
        title: "No content to export",
        description: "There is no script content available to export.",
        variant: "destructive",
      });
      return;
    }
    
    // Create a blob from the content
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    // Create a download link and click it
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_script.txt`;
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Script exported",
      description: "Your script has been downloaded successfully.",
    });
  };
  
  if (!canDownload) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              disabled={true} 
              variant="outline" 
              size="sm" 
              className="flex items-center space-x-1"
            >
              <Lock className="h-4 w-4 mr-1" />
              <span>Export Script</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Upgrade to a paid plan to download scripts</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }
  
  return (
    <Button 
      onClick={handleDownload} 
      disabled={disabled || !content} 
      variant="outline" 
      size="sm" 
      className="flex items-center space-x-1"
    >
      <Download className="h-4 w-4 mr-1" />
      <span>Export Script</span>
    </Button>
  );
}