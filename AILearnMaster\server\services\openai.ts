import OpenAI from 'openai';

/**
 * Verifies that an OpenAI API key is valid by making a test request
 * @param apiKey - The OpenAI API key to verify
 * @returns Promise that resolves if the key is valid, or rejects with an error if invalid
 */
export async function verifyApiKey(apiKey: string): Promise<void> {
  try {
    const openai = new OpenAI({ apiKey });
    
    // Make a minimal request to verify the API key
    const response = await openai.models.list();
    
    if (response.data && Array.isArray(response.data)) {
      // API key is valid
      return;
    }
    
    throw new Error('Invalid OpenAI API key');
  } catch (error: any) {
    if (error.status === 401 || error.message.includes('API key')) {
      throw new Error('Invalid OpenAI API key');
    }
    throw error;
  }
}

/**
 * Gets a verified API key, either from the provided key or from environment variables
 * @param userApiKey - Optional user API key to check first
 * @returns The API key to use
 */
export function getApiKey(userApiKey?: string): string {
  // Use user-provided API key if available
  if (userApi<PERSON><PERSON>) {
    return userApiKey;
  }
  
  // Fall back to system API key
  const systemApiKey = process.env.OPENAI_API_KEY;
  
  if (!systemApiKey) {
    throw new Error('No OpenAI API key available');
  }
  
  return systemApiKey;
}

// In-memory storage for video generation jobs
const activeVideoJobs: Record<string, VideoJob> = {};

interface VideoJob {
  id: string;
  title: string;
  status: 'processing' | 'completed' | 'failed';
  videoUrl?: string;
  thumbnailUrl?: string;
  error?: string;
  duration: number;
  estimatedCompletionTime: number;
  createdAt: number;
  userId: number;
  prompt: string;
  style?: string;
  narrationVoice?: string;
  animationPreference?: string;
  animationKeyframes?: any[];
}

interface VideoGenerationOptions {
  script: string;
  title: string;
  style?: string;
  animationPreference?: string;
  narrationVoice?: string;
  userId?: number;
}

/**
 * Initiates a video generation job
 * @param options - Video generation options
 * @returns The video job
 */
import { storage } from '../storage';

export async function startVideoGeneration(options: VideoGenerationOptions): Promise<VideoJob> {
  try {
    // Use OpenAI API to generate video
    const openai = new OpenAI({ apiKey: getApiKey() });
    
    // Sanitize the script to ensure it's not too long
    const script = options.script.substring(0, 2000); // Limit to 2000 chars for API compatibility
    
    // Generate a unique ID for this job
    const jobId = `video_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    // Create a job entry
    const job: VideoJob = {
      id: jobId,
      title: options.title || "Generated Video",
      status: 'processing',
      duration: 0,
      estimatedCompletionTime: Date.now() + 180000, // Estimate 3 minutes
      createdAt: Date.now(),
      userId: options.userId || 0,
      prompt: script,
      style: options.style,
      narrationVoice: options.narrationVoice,
      animationPreference: options.animationPreference
    };
    
    // Store the job in memory
    activeVideoJobs[jobId] = job;
    
    // Also store the job in the database as a media entry
    try {
      await storage.createMedia({
        userId: options.userId || 0,
        type: 'video',
        name: options.title || "Generated Video",
        url: '',  // Will be updated when processing completes
        mimeType: 'video/mp4',
        fileSize: 0,
        sourceId: jobId,
        source: 'openai',
        sourceData: {
          status: 'processing',
          estimatedCompletionTime: Date.now() + 180000,
          script: script,
          style: options.style,
          narrationVoice: options.narrationVoice
        }
      });
    } catch (dbError) {
      console.error("Failed to store video job in database:", dbError);
      // Continue even if database storage fails
    }
    
    // For testing/development, simulate a completed video after a delay
    setTimeout(async () => {
      try {
        // Simulate a completed video
        const videoUrl = 'https://example.com/video.mp4'; // Placeholder URL
        const thumbnailUrl = 'https://example.com/thumbnail.jpg'; // Placeholder URL
        const duration = 60; // Placeholder duration in seconds
        
        // Update in-memory record
        activeVideoJobs[jobId] = {
          ...activeVideoJobs[jobId],
          status: 'completed',
          videoUrl,
          thumbnailUrl,
          duration
        };
        
        // Update database record
        try {
          // First, try to retrieve the media record
          const mediaRecord = await storage.getMediaBySourceId(jobId);
          
          if (mediaRecord) {
            // Now we have the updateMedia function, we can update the record directly
            // Create an object with only the fields we want to update
            const updates = {
              url: videoUrl,
              duration,
              sourceData: JSON.parse(JSON.stringify({
                ...(typeof mediaRecord.sourceData === 'object' ? mediaRecord.sourceData : {}),
                status: 'completed',
                thumbnailUrl,
                completedAt: new Date().toISOString()
              }))
            };
            
            // Update the media record
            await storage.updateMedia(mediaRecord.id, updates);
            console.log(`Updated video job ${jobId} in database`);
          }
        } catch (dbError) {
          console.error(`Failed to update video job ${jobId} in database:`, dbError);
        }
      } catch (err) {
        console.error(`Error completing video job ${jobId}:`, err);
      }
    }, 10000); // 10 seconds delay for testing
    
    return job;
  } catch (error) {
    console.error("Error in OpenAI video generation:", error);
    throw error;
  }
}

/**
 * Gets the status of a video generation job
 * @param jobId - The ID of the job to check
 * @returns The video job, or undefined if not found
 */
export function getVideoStatus(jobId: string): VideoJob | undefined {
  return activeVideoJobs[jobId];
}

/**
 * Generates an image using OpenAI's DALL-E API
 * @param prompt - The text prompt to generate an image for
 * @param size - The size of the image to generate (default: "1024x1024")
 * @returns Promise that resolves to the generated image data
 */
export async function generateImage(prompt: string, size: string = "1024x1024"): Promise<{ url: string }> {
  try {
    const openai = new OpenAI({ apiKey: getApiKey() });
    
    // Generate image with DALL-E
    const response = await openai.images.generate({
      model: "dall-e-3",
      prompt: prompt,
      n: 1,
      size: size as any, // Type assertion to match OpenAI API requirements
      quality: "standard",
    });
    
    // Return the image URL
    if (response.data && response.data.length > 0 && response.data[0].url) {
      return { url: response.data[0].url };
    }
    
    throw new Error("Image generation failed: No image URL returned");
  } catch (error: any) {
    console.error("Error generating image with OpenAI:", error);
    
    // Return a placeholder for development/testing
    if (process.env.NODE_ENV === 'development') {
      return { url: "https://placehold.co/1024x1024?text=Image+Generation+Failed" };
    }
    
    throw new Error(`Image generation failed: ${error.message}`);
  }
}

/**
 * Generates email content based on input parameters
 * @param options - Options to control email generation including type, course, and tone
 * @returns Promise that resolves to the generated email content
 */
export async function generateEmailContent(options: {
  courseInfo?: any;
  emailType: string;
  tone?: string;
  customPrompt?: string;
}): Promise<{
  subject: string;
  content: string;
}> {
  try {
    const { courseInfo, emailType, tone, customPrompt } = options;
    
    // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const openai = new OpenAI({ apiKey: getApiKey() });
    
    // Create prompt based on email type
    let prompt = `Generate a professional email for ${emailType}`;
    
    if (courseInfo) {
      prompt += ` about the course "${courseInfo.title}"`;
      
      if (courseInfo.description) {
        prompt += `. Course description: ${courseInfo.description}`;
      }
      
      if (courseInfo.targetAudience) {
        prompt += `. Target audience: ${courseInfo.targetAudience}`;
      }
    }
    
    if (tone) {
      prompt += `. The tone should be ${tone}.`;
    }
    
    if (customPrompt) {
      prompt += ` Additional instructions: ${customPrompt}`;
    }
    
    prompt += ` Return ONLY a JSON object with 'subject' and 'content' fields. The subject should be catchy and compelling. The content should be well-formatted with paragraphs and include emojis where appropriate.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        { 
          role: "system", 
          content: "You are an expert email marketer specializing in online course promotions and communications. You know how to create compelling, conversion-focused emails that drive action."
        },
        { role: "user", content: prompt }
      ],
      response_format: { type: "json_object" },
      temperature: 0.7
    });
    
    if (!response.choices[0].message.content) {
      throw new Error("Email generation failed: No content returned");
    }
    
    const result = JSON.parse(response.choices[0].message.content);
    
    return {
      subject: result.subject || "Generated Email Subject",
      content: result.content || "Generated email content not available."
    };
  } catch (error: any) {
    console.error("Error generating email with OpenAI:", error);
    
    // Return placeholder content for development/testing
    if (process.env.NODE_ENV === 'development') {
      return {
        subject: "[AI Generation Error] Sample Email Subject",
        content: "There was an error generating the email content. Please try again later or adjust your parameters."
      };
    }
    
    throw new Error(`Email generation failed: ${error.message}`);
  }
}

/**
 * Generates landing page content based on course information
 * @param options - Course information and design preferences
 * @returns Promise that resolves to the generated landing page content
 */
export async function generateLandingPageContent(options: {
  courseInfo?: any;
  style?: string;
  colorScheme?: string;
  customPrompt?: string;
}): Promise<{
  headline: string;
  subheadline: string;
  description: string;
  benefits: string[];
  sections: Array<{ title: string; content: string }>;
  callToAction: string;
  testimonials?: Array<{ name: string; role: string; quote: string }>;
  pricing?: { regular: string; discount?: string };
  seoDescription?: string;
}> {
  try {
    const { courseInfo, style, colorScheme, customPrompt } = options;
    
    // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
    const openai = new OpenAI({ apiKey: getApiKey() });
    
    // Create detailed prompt for landing page generation
    let prompt;
    
    if (courseInfo) {
      prompt = `Generate complete content for a landing page for the online course titled "${courseInfo.title}".`;
      
      if (courseInfo.description) {
        prompt += ` Course description: ${courseInfo.description}`;
      }
      
      if (courseInfo.targetAudience) {
        prompt += `. Target audience: ${courseInfo.targetAudience}`;
      }
      
      if (courseInfo.category) {
        prompt += `. Course category: ${courseInfo.category}`;
      }
    } else {
      // Fallback if no course info is provided
      prompt = `Generate complete content for a generic online course landing page.`;
    }
    
    if (style) {
      prompt += `. The style of the landing page should be ${style}.`;
    }
    
    if (colorScheme) {
      prompt += `. Use a ${colorScheme} color scheme.`;
    }
    
    if (customPrompt) {
      prompt += ` Additional instructions: ${customPrompt}`;
    }
    
    prompt += ` Include the following components: compelling headline, engaging subheadline, detailed description, 4-6 key benefits, and "Enroll Now" as the call to action.`;
    
    // Always include testimonials and pricing information
    prompt += ` Also include 3 realistic testimonials from satisfied students.`;
    prompt += ` Include pricing information with a regular price and a discounted price.`;
    
    prompt += ` Also provide an SEO-optimized meta description for the page.`;
    
    prompt += ` Return ONLY a JSON object with fields: 'headline', 'subheadline', 'description', 'benefits' (array), 'sections' (array of objects with 'title' and 'content'), 'callToAction', 'testimonials' (array of objects with 'name', 'role', and 'quote'), 'pricing' (object with 'regular' and optional 'discount'), and 'seoDescription'.`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        { 
          role: "system", 
          content: "You are an expert landing page copywriter specializing in online course marketing. You create compelling, conversion-focused landing pages that drive enrollments and showcase course value."
        },
        { role: "user", content: prompt }
      ],
      response_format: { type: "json_object" },
      temperature: 0.7,
      max_tokens: 1500
    });
    
    if (!response.choices[0].message.content) {
      throw new Error("Landing page generation failed: No content returned");
    }
    
    const result = JSON.parse(response.choices[0].message.content);
    
    const courseTitle = courseInfo?.title || 'Online Course';
    const courseDescription = courseInfo?.description || 'A comprehensive online learning experience';
    
    // Ensure all required fields exist
    return {
      headline: result.headline || courseTitle,
      subheadline: result.subheadline || "Master new skills and advance your career",
      description: result.description || courseDescription,
      benefits: result.benefits || ["Learn at your own pace", "Expert instruction", "Practical skills", "Certificate of completion"],
      sections: result.sections || [{ title: "Course Overview", content: "This course offers comprehensive training." }],
      callToAction: result.callToAction || "Enroll Now",
      testimonials: result.testimonials || [
        { name: "Student One", role: "Professional", quote: "This course transformed my career." },
        { name: "Student Two", role: "Entrepreneur", quote: "Exactly what I needed to grow my business." },
        { name: "Student Three", role: "Beginner", quote: "Clear explanations and practical examples." }
      ],
      pricing: result.pricing || { regular: "$99", discount: "$79" },
      seoDescription: result.seoDescription || `${courseTitle} - ${courseDescription.substring(0, 100)}...`
    };
  } catch (error: any) {
    console.error("Error generating landing page with OpenAI:", error);
    
    // Return placeholder content for development/testing
    if (process.env.NODE_ENV === 'development') {
      return {
        headline: "Master New Skills with Our Course",
        subheadline: "Learn from experts and transform your career",
        description: "There was an error generating the landing page content. Please try again later.",
        benefits: ["Learn at your own pace", "Expert instruction", "Practical skills", "Certificate of completion"],
        sections: [{ title: "Course Overview", content: "This course offers comprehensive training." }],
        callToAction: "Enroll Now",
        testimonials: [
          { name: "Student One", role: "Professional", quote: "This course transformed my career." },
          { name: "Student Two", role: "Entrepreneur", quote: "Exactly what I needed to grow my business." },
          { name: "Student Three", role: "Beginner", quote: "Clear explanations and practical examples." }
        ],
        pricing: { regular: "$99", discount: "$79" },
        seoDescription: "Master New Skills with Our Course - Learn from experts and transform your career with our comprehensive training program."
      };
    }
    
    throw new Error(`Landing page generation failed: ${error.message}`);
  }
}