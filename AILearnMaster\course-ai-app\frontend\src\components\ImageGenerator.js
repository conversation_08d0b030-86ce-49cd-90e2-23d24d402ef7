import React, { useState } from 'react';
import { Card, CardContent, TextField, Button, Grid, Typography, CircularProgress, Slider, FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import { Download, Image as ImageIcon } from '@mui/icons-material';
import apiService from '../services/apiService';

const ImageGenerator = () => {
  const [formData, setFormData] = useState({
    prompt: '',
    negativePrompt: '',
    width: 1024,
    height: 1024,
    numInferenceSteps: 30,
    guidanceScale: 7.5,
    batchSize: 1
  });
  
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [error, setError] = useState('');

  const presetSizes = [
    { label: 'Square (1024x1024)', width: 1024, height: 1024 },
    { label: 'Portrait (768x1024)', width: 768, height: 1024 },
    { label: 'Landscape (1024x768)', width: 1024, height: 768 },
    { label: 'Wide (1280x720)', width: 1280, height: 720 }
  ];

  const promptTemplates = [
    "A professional instructor teaching in a modern classroom, photorealistic, high quality",
    "Digital art illustration of a person presenting data charts, clean background",
    "3D rendered character explaining concepts with gestures, animated style",
    "Minimalist icon set for educational content, flat design, vector style",
    "Detailed infographic layout with text spaces, professional design"
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleGenerate = async () => {
    if (!formData.prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const cacheKey = apiService.generateCacheKey(formData);
      const cached = apiService.getCachedResult(cacheKey);
      
      if (cached) {
        setResults(cached.images_base64.map((img, index) => ({ 
          id: Date.now() + index, 
          base64: img, 
          cached: true 
        })));
        setLoading(false);
        return;
      }

      const result = await apiService.generateImage(formData);
      
      if (result.status === 'success') {
        const newResults = result.images_base64.map((img, index) => ({
          id: Date.now() + index,
          base64: img,
          cached: result.cached || false
        }));
        setResults(newResults);
        
        if (!result.cached) {
          apiService.setCachedResult(cacheKey, result);
        }
      } else {
        setError(result.error || 'Generation failed');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = (base64, index) => {
    apiService.downloadBase64File(
      base64, 
      `ai-generated-image-${Date.now()}-${index}.png`, 
      'image/png'
    );
  };

  const useTemplate = (template) => {
    setFormData(prev => ({ ...prev, prompt: template }));
  };

  const usePresetSize = (preset) => {
    setFormData(prev => ({ 
      ...prev, 
      width: preset.width, 
      height: preset.height 
    }));
  };

  return (
    <div className="image-generator">
      <Typography variant="h4" gutterBottom>
        <ImageIcon /> Stable Diffusion XL Image Generator
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Generation Settings</Typography>
              
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Prompt"
                value={formData.prompt}
                onChange={(e) => handleInputChange('prompt', e.target.value)}
                margin="normal"
                placeholder="Describe the image you want to generate..."
              />
              
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Negative Prompt (Optional)"
                value={formData.negativePrompt}
                onChange={(e) => handleInputChange('negativePrompt', e.target.value)}
                margin="normal"
                placeholder="What to avoid in the image..."
              />

              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                Quick Templates:
              </Typography>
              <Grid container spacing={1}>
                {promptTemplates.map((template, index) => (
                  <Grid item xs={12} key={index}>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => useTemplate(template)}
                      sx={{ textAlign: 'left', justifyContent: 'flex-start' }}
                      fullWidth
                    >
                      {template.substring(0, 60)}...
                    </Button>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Advanced Settings</Typography>
              
              <FormControl fullWidth margin="normal">
                <InputLabel>Image Size</InputLabel>
                <Select
                  value={`${formData.width}x${formData.height}`}
                  onChange={(e) => {
                    const preset = presetSizes.find(p => `${p.width}x${p.height}` === e.target.value);
                    if (preset) usePresetSize(preset);
                  }}
                >
                  {presetSizes.map((preset, index) => (
                    <MenuItem key={index} value={`${preset.width}x${preset.height}`}>
                      {preset.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Typography gutterBottom sx={{ mt: 2 }}>
                Inference Steps: {formData.numInferenceSteps}
              </Typography>
              <Slider
                value={formData.numInferenceSteps}
                onChange={(e, value) => handleInputChange('numInferenceSteps', value)}
                min={10}
                max={50}
                step={5}
                marks
                valueLabelDisplay="auto"
              />

              <Typography gutterBottom sx={{ mt: 2 }}>
                Guidance Scale: {formData.guidanceScale}
              </Typography>
              <Slider
                value={formData.guidanceScale}
                onChange={(e, value) => handleInputChange('guidanceScale', value)}
                min={1}
                max={20}
                step={0.5}
                marks
                valueLabelDisplay="auto"
              />

              <Typography gutterBottom sx={{ mt: 2 }}>
                Batch Size: {formData.batchSize}
              </Typography>
              <Slider
                value={formData.batchSize}
                onChange={(e, value) => handleInputChange('batchSize', value)}
                min={1}
                max={4}
                step={1}
                marks
                valueLabelDisplay="auto"
              />

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleGenerate}
                disabled={loading}
                sx={{ mt: 3 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Generate Images'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {error && (
          <Grid item xs={12}>
            <Card sx={{ backgroundColor: '#ffebee' }}>
              <CardContent>
                <Typography color="error">{error}</Typography>
              </CardContent>
            </Card>
          </Grid>
        )}

        {results.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Generated Images {loading && '(Processing...)'}
                </Typography>
                <Grid container spacing={2}>
                  {results.map((result, index) => (
                    <Grid item xs={12} sm={6} md={4} key={result.id}>
                      <Card>
                        <img
                          src={`data:image/png;base64,${result.base64}`}
                          alt={`Generated ${index + 1}`}
                          style={{
                            width: '100%',
                            height: 'auto',
                            maxHeight: '300px',
                            objectFit: 'cover'
                          }}
                        />
                        <CardContent>
                          <Button
                            fullWidth
                            variant="outlined"
                            startIcon={<Download />}
                            onClick={() => handleDownload(result.base64, index)}
                          >
                            Download {result.cached && '(Cached)'}
                          </Button>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </div>
  );
};

export default ImageGenerator;