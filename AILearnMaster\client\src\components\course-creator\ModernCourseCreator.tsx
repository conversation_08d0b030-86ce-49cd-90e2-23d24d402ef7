import React, { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useMutation } from '@tanstack/react-query';
import { HorizontalStepIndicator, Step } from './HorizontalStepIndicator';
import { ModuleScriptEditor } from './ModuleScriptEditor';
import { 
  Layout, 
  BookOpen, 
  FileText, 
  Film, 
  HelpCircle, 
  Globe, 
  ArrowLeft, 
  ArrowRight,
  Sparkles,
  FileQuestion,
  Save,
  Presentation
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useLocation } from 'wouter';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SlideGenerationModal } from './SlideGenerationModal';

// Type definitions for the course structure
interface Lesson {
  id: string;
  title: string;
  type?: string;
  content?: string;
  duration?: number;
  complexity?: string;
}

interface Module {
  id: string;
  title: string;
  description?: string;
  complexity?: string;
  lessons: Lesson[];
}

interface CourseDetails {
  title: string;
  description: string;
  category: string;
  targetAudience?: string;
  keyTopics?: string;
  contentNotes?: string;
}

interface CourseStructure {
  modules: Module[];
}

interface CourseScripts {
  [moduleId: string]: {
    [lessonId: string]: string;
  };
}

const steps: Step[] = [
  { 
    id: 'course-details',
    title: 'Course Details',
    description: 'Set up your course fundamentals',
    icon: <BookOpen className="h-4 w-4" />
  },
  { 
    id: 'content-structure',
    title: 'Content Structure',
    description: 'Organize your modules and lessons',
    icon: <Layout className="h-4 w-4" />
  },
  { 
    id: 'scripts',
    title: 'Script Creation',
    description: 'Write scripts for your modules',
    icon: <FileText className="h-4 w-4" />
  },
  { 
    id: 'media',
    title: 'Media Selection',
    description: 'Choose images, videos, and audio',
    icon: <Film className="h-4 w-4" />
  },
  { 
    id: 'quizzes',
    title: 'Quiz Creation',
    description: 'Create assessment questions',
    icon: <HelpCircle className="h-4 w-4" />
  },
  { 
    id: 'publish',
    title: 'Publish',
    description: 'Review and publish your course',
    icon: <Globe className="h-4 w-4" />
  },
];

export function ModernCourseCreator() {
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [moduleCount, setModuleCount] = useState(5);
  const [courseDetails, setCourseDetails] = useState<CourseDetails>({
    title: '',
    description: '',
    category: 'technology',
    targetAudience: '',
    keyTopics: '',
    contentNotes: '',
  });
  const [generatedStructure, setGeneratedStructure] = useState<CourseStructure | null>(null);
  const [courseScripts, setCourseScripts] = useState<CourseScripts>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [isBalancing, setIsBalancing] = useState(false);
  const [showSlideModal, setShowSlideModal] = useState(false);
  const [currentSlideModule, setCurrentSlideModule] = useState<{ moduleId: string; moduleTitle: string; script: string } | null>(null);
  const { toast } = useToast();
  const [_, navigate] = useLocation();

  // Mutations
  const generateStructureMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest('POST', '/api/ai/generate-course-structure', data);
      if (!response.ok) {
        throw new Error('Failed to generate course structure');
      }
      return response.json();
    },
    onSuccess: (data) => {
      setGeneratedStructure(data);
      
      // Mark the courseDetails step as completed
      if (!completedSteps.includes(0)) {
        setCompletedSteps(prev => [...prev, 0]);
      }
      
      // Move to the content structure step
      setActiveStep(1);
      
      toast({
        title: "Course structure generated",
        description: "Your course structure has been created successfully.",
      });
    },
    onError: (error) => {
      console.error('Error generating course structure:', error);
      toast({
        title: "Generation failed",
        description: "An error occurred while generating the course structure. Please try again.",
        variant: "destructive"
      });
    }
  });

  const generateScriptMutation = useMutation({
    mutationFn: async ({ moduleId, moduleTitle, moduleDescription, lessonTitles }: any) => {
      const response = await apiRequest('POST', '/api/ai/generate-script', {
        prompt: `Create a comprehensive teaching script for a module titled "${moduleTitle}" 
        with description "${moduleDescription || 'No description available'}". 
        This module includes the following lessons: ${lessonTitles.join(', ')}. 
        The script should flow logically between topics and maintain educational best practices.`,
        maxTokens: 1500
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate module script');
      }
      
      const data = await response.json();
      return { moduleId, script: data.text || '' };
    },
    onSuccess: (data) => {
      setCourseScripts(prev => ({
        ...prev,
        [data.moduleId]: data.script
      }));
      
      toast({
        title: "Script generated",
        description: "Module script has been created. You can now edit it as needed.",
      });
      
      return data.script;
    }
  });

  const saveCourseMutation = useMutation({
    mutationFn: async (courseData: any) => {
      const response = await apiRequest('POST', '/api/courses', courseData);
      if (!response.ok) {
        throw new Error('Failed to save course');
      }
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Course saved",
        description: "Your course has been saved successfully.",
      });
      
      // Navigate to the course dashboard
      navigate('/courses');
    }
  });

  // Helper functions
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCourseDetails(prev => ({ ...prev, [name]: value }));
  };

  const handleCategoryChange = (value: string) => {
    setCourseDetails(prev => ({ ...prev, category: value }));
  };

  const handleModuleCountChange = (value: number[]) => {
    setModuleCount(value[0]);
  };

  const navigateToStep = (stepIndex: number) => {
    // Only allow navigation to completed steps or the next incomplete step
    if (completedSteps.includes(stepIndex) || stepIndex === activeStep || stepIndex <= Math.max(...completedSteps) + 1) {
      setActiveStep(stepIndex);
    }
  };

  const handleGenerateCourse = async () => {
    if (!courseDetails.title || !courseDetails.description) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide at least a course title and description"
      });
      return;
    }

    setIsGenerating(true);
    
    // Simulate progress updates
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 95) {
          clearInterval(interval);
          return prev;
        }
        return prev + 5;
      });
    }, 500);

    try {
      await generateStructureMutation.mutateAsync({
        ...courseDetails,
        moduleCount
      });
    } finally {
      clearInterval(interval);
      setProgress(100);
      
      setTimeout(() => {
        setIsGenerating(false);
        setProgress(0);
      }, 500);
    }
  };

  const handleScriptChange = (moduleId: string, lessonId: string, script: string) => {
    setCourseScripts(prev => ({
      ...prev,
      [moduleId]: {
        ...prev[moduleId],
        [lessonId]: script
      }
    }));
  };

  const handleGenerateScript = async (moduleId: string) => {
    if (!generatedStructure) return '';
    
    const module = generatedStructure.modules.find(m => m.id === moduleId);
    if (!module) return '';
    
    const lessonTitles = module.lessons.map(lesson => lesson.title);
    
    try {
      const script = await generateScriptMutation.mutateAsync({
        moduleId,
        moduleTitle: module.title,
        moduleDescription: module.description,
        lessonTitles
      });
      
      return script;
    } catch (error) {
      console.error('Error generating script:', error);
      toast({
        title: "Script generation failed",
        description: "Could not generate the script. Please try again.",
        variant: "destructive"
      });
      
      return '';
    }
  };

  const handleGenerateSlides = (moduleId: string, moduleTitle: string, script: string) => {
    setCurrentSlideModule({ moduleId, moduleTitle, script });
    setShowSlideModal(true);
  };

  const handleSlidesGenerated = (slideData: any) => {
    toast({
      title: "Slides Generated Successfully",
      description: `Generated ${slideData.metadata?.actual_slide_count || 'multiple'} slides for ${currentSlideModule?.moduleTitle}`,
    });
    setShowSlideModal(false);
    setCurrentSlideModule(null);
  };

  const handleSaveScripts = async () => {
    // Mark the scripts step as completed
    if (!completedSteps.includes(2)) {
      setCompletedSteps(prev => [...prev, 2]);
    }
    
    // Move to the next step
    setActiveStep(3);
    
    // In a real implementation, you would save the scripts to the server here
    return Promise.resolve();
  };

  const handleSaveCourse = async () => {
    if (!generatedStructure) return;
    
    try {
      await saveCourseMutation.mutateAsync({
        title: courseDetails.title,
        description: courseDetails.description,
        category: courseDetails.category,
        modules: generatedStructure.modules.map(module => ({
          title: module.title,
          description: module.description,
          script: courseScripts[module.id] || '',
          lessons: module.lessons.map(lesson => ({
            title: lesson.title,
            type: lesson.type || 'video',
            content: lesson.content || '',
            duration: lesson.duration || 0
          }))
        }))
      });
      
      toast({
        title: "Course saved!",
        description: "Your course has been successfully saved.",
      });
    } catch (error) {
      console.error('Error saving course:', error);
      toast({
        title: "Save failed",
        description: "Could not save the course. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleContinue = () => {
    // Add the current step to completed steps if not already there
    if (!completedSteps.includes(activeStep)) {
      setCompletedSteps(prev => [...prev, activeStep]);
    }
    
    // Move to the next step
    setActiveStep(prev => Math.min(prev + 1, steps.length - 1));
  };

  const handleBack = () => {
    setActiveStep(prev => Math.max(prev - 1, 0));
  };

  // Initialize modules with unique IDs when structure is generated
  useEffect(() => {
    if (generatedStructure) {
      const modulesWithIds = generatedStructure.modules.map(module => ({
        ...module,
        id: module.id || uuidv4(),
        lessons: module.lessons.map(lesson => ({
          ...lesson,
          id: lesson.id || uuidv4(),
        }))
      }));
      
      setGeneratedStructure({
        ...generatedStructure,
        modules: modulesWithIds
      });
    }
  }, []);

  // Render functions for each step
  const renderCourseDetailsStep = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">Course Information</h2>
        <p className="text-muted-foreground mb-6">
          Provide details about your course to help generate a well-structured learning experience.
        </p>
        
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Course Title</Label>
            <Input 
              id="title" 
              name="title"
              placeholder="e.g., Complete Web Development Bootcamp" 
              value={courseDetails.title}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Course Description</Label>
            <Textarea 
              id="description" 
              name="description"
              placeholder="Describe what your course will teach and who it's for..."
              rows={4}
              value={courseDetails.description}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={courseDetails.category} onValueChange={handleCategoryChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="technology">Technology</SelectItem>
                <SelectItem value="business">Business</SelectItem>
                <SelectItem value="design">Design</SelectItem>
                <SelectItem value="marketing">Marketing</SelectItem>
                <SelectItem value="personal-development">Personal Development</SelectItem>
                <SelectItem value="health">Health & Wellness</SelectItem>
                <SelectItem value="language">Language</SelectItem>
                <SelectItem value="arts">Arts & Creativity</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-4 pt-2">
            <div className="flex justify-between">
              <Label>Number of Modules: {moduleCount}</Label>
              <span className="text-sm text-muted-foreground">(1-10)</span>
            </div>
            <Slider
              defaultValue={[5]}
              max={10}
              min={1}
              step={1}
              value={[moduleCount]}
              onValueChange={handleModuleCountChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="targetAudience">Target Audience (Optional)</Label>
            <Input 
              id="targetAudience" 
              name="targetAudience"
              placeholder="e.g., Beginners with no prior knowledge" 
              value={courseDetails.targetAudience}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="keyTopics">Key Topics (Optional)</Label>
            <Input 
              id="keyTopics" 
              name="keyTopics"
              placeholder="e.g., HTML, CSS, JavaScript, React" 
              value={courseDetails.keyTopics}
              onChange={handleInputChange}
            />
          </div>
        </div>
      </Card>
      
      {isGenerating ? (
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-2">Generating Course Structure</h3>
          <p className="text-muted-foreground mb-4">
            Our AI is creating a structured course based on your input...
          </p>
          <div className="space-y-2">
            <Progress value={progress} className="h-2" />
            <div className="flex justify-between text-sm">
              <span>Analyzing content</span>
              <span>{Math.round(progress)}%</span>
            </div>
          </div>
        </Card>
      ) : (
        <div className="flex justify-end">
          <Button 
            onClick={handleGenerateCourse}
            disabled={!courseDetails.title || !courseDetails.description}
          >
            <Sparkles className="mr-2 h-4 w-4" />
            Generate Course Structure
          </Button>
        </div>
      )}
    </div>
  );

  const renderContentStructureStep = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">Course Structure</h2>
        <p className="text-muted-foreground mb-6">
          Review and customize your course modules and lessons.
        </p>
        
        {generatedStructure ? (
          <div className="space-y-6">
            {generatedStructure.modules.map((module, moduleIndex) => (
              <Card key={module.id} className="p-4 border-l-4 border-l-primary">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-medium">
                    Module {moduleIndex + 1}: {module.title}
                  </h3>
                  {module.complexity && (
                    <Badge variant={
                      module.complexity === 'beginner' ? 'default' : 
                      module.complexity === 'intermediate' ? 'secondary' : 
                      'outline'
                    }>
                      {module.complexity}
                    </Badge>
                  )}
                </div>
                
                {module.description && (
                  <p className="text-muted-foreground mb-3">{module.description}</p>
                )}
                
                <div className="ml-4 space-y-2 mt-3">
                  <h4 className="text-sm font-medium mb-2">Lessons:</h4>
                  {module.lessons.map((lesson, lessonIndex) => (
                    <div key={lesson.id} className="flex justify-between items-center p-2 bg-muted/40 rounded-md">
                      <span>{lessonIndex + 1}. {lesson.title}</span>
                      {lesson.complexity && (
                        <Badge variant="outline" className="text-xs">
                          {lesson.complexity}
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center p-8">
            <p className="text-muted-foreground">
              No course structure generated yet. Please go back and generate a structure.
            </p>
          </div>
        )}
      </Card>
      
      <div className="flex justify-between">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <Button 
          onClick={handleContinue}
          disabled={!generatedStructure}
        >
          Create Lesson Scripts
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderScriptsStep = () => (
    <div className="space-y-6">
      {generatedStructure ? (
        <ModuleScriptEditor 
          modules={generatedStructure.modules} 
          courseScripts={courseScripts}
          onScriptChange={handleScriptChange}
          onGenerateScript={handleGenerateScript}
          onSave={handleSaveScripts}
          onBack={() => setActiveStep(1)}
        />
      ) : (
        <Card className="p-6">
          <p className="text-center text-muted-foreground">
            No course structure available. Please go back and generate a structure.
          </p>
        </Card>
      )}
      
      <div className="flex justify-between">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <Button onClick={handleContinue}>
          Select Media Assets
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderMediaStep = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">Media Selection</h2>
        <p className="text-muted-foreground mb-6">
          This feature is coming soon. You will be able to add images, videos, and other media to your course.
        </p>
      </Card>
      
      <div className="flex justify-between">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <Button onClick={handleContinue}>
          Create Assessment
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderQuizzesStep = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">Quiz Creation</h2>
        <p className="text-muted-foreground mb-6">
          This feature is coming soon. You will be able to create quizzes and assessments for your course.
        </p>
      </Card>
      
      <div className="flex justify-between">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <Button onClick={handleContinue}>
          Review & Publish Course
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderPublishStep = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <h2 className="text-xl font-bold mb-4">Publish Course</h2>
        <p className="text-muted-foreground mb-6">
          Review your course and publish it to make it available to students.
        </p>
        
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium mb-2">Course Summary</h3>
            <div className="bg-muted/30 p-4 rounded-md">
              <p><strong>Title:</strong> {courseDetails.title}</p>
              <p><strong>Category:</strong> {courseDetails.category}</p>
              <p><strong>Modules:</strong> {generatedStructure?.modules.length || 0}</p>
              <p><strong>Lessons:</strong> {generatedStructure?.modules.reduce((total, module) => total + module.lessons.length, 0) || 0}</p>
            </div>
          </div>
          
          <Button onClick={handleSaveCourse} className="w-full">
            <Save className="mr-2 h-4 w-4" />
            Save and Publish Course
          </Button>
        </div>
      </Card>
      
      <div className="flex justify-start">
        <Button variant="outline" onClick={handleBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
      </div>
    </div>
  );

  const renderActiveStep = () => {
    switch (activeStep) {
      case 0:
        return renderCourseDetailsStep();
      case 1:
        return renderContentStructureStep();
      case 2:
        return renderScriptsStep();
      case 3:
        return renderMediaStep();
      case 4:
        return renderQuizzesStep();
      case 5:
        return renderPublishStep();
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold">Modern Course Creator</h1>
        <p className="text-muted-foreground mt-2">
          Create professional, engaging courses with AI-powered content generation
        </p>
      </div>
      
      <div className="max-w-4xl mx-auto mb-8">
        <HorizontalStepIndicator 
          steps={steps} 
          activeStep={activeStep} 
          completedSteps={completedSteps} 
          onStepClick={navigateToStep}
        />
      </div>
      
      <div className="max-w-4xl mx-auto">
        {renderActiveStep()}
      </div>
    </div>
  );
}