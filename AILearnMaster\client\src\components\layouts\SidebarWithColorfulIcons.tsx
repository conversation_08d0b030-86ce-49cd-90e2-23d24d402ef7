import { Link, useLocation } from "wouter";
import { User } from "@/types";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { ColorfulIcon, type IconCategory } from "@/components/ui/colorful-icon";
import { useState, useEffect } from "react";
import {
  LayoutDashboard,
  Video as VideoIcon,
  Users,
  FolderGit2,
  Wand2,
  Film,
  Mic,
  Brain,
  Share2,
  Sparkles,
  Link2,
  Store,
  Tag,
  Settings,
  HelpCircle,
  UserCog,
  FileText,
  PieChart,
  MessageSquare,
  LogOut,
  Cpu,
  Layers,
  Mail,
  Megaphone,
  Newspaper,
  ChevronDown,
  ChevronRight,
  CreditCard,
  ImageIcon
} from "lucide-react";



interface SidebarProps {
  user: User | null;
}

type NavItem = {
  icon: React.FC<any>;
  category: IconCategory;
  label: string;
  path: string;
  children?: NavItem[];
};

export function SidebarWithColorfulIcons({ user }: SidebarProps) {
  const [location] = useLocation();
  const isAdmin = user?.role === 'admin';
  const [expandedSections, setExpandedSections] = useState<{[key: string]: boolean}>({});
  
  // Initialize expanded state based on current location
  useEffect(() => {
    // Initialize all sections to collapsed by default
    const initialState: {[key: string]: boolean} = {};
    
    // Check current path and expand relevant section
    // Content Creation section
    if (location.startsWith('/my-courses') || location.startsWith('/course-studio') || 
        location.startsWith('/mini-course-creator') || location.startsWith('/media-library')) {
      initialState.courses = true;
    } 
    // AI Tools section
    else if (location.startsWith('/ai-templates') || location.startsWith('/ai-tools') || 
             location.startsWith('/ai-voice-generator') || location.startsWith('/ai-credits')) {
      initialState.ai = true;
    } 
    // Collaboration section
    else if (location.startsWith('/teams') || location.startsWith('/collaboration') || 
             location.startsWith('/meetings') || location.startsWith('/collaborations')) {
      initialState.collaboration = true;
    } 
    // Distribution & Marketing section
    else if (location.startsWith('/marketing') || location.startsWith('/platform-integrations')) {
      initialState.marketing = true;
    }
    // Settings & Help section
    else if (location.startsWith('/settings') || location.startsWith('/help') || 
             location.startsWith('/pricing')) {
      initialState.settings = true;
    }
    // Admin sections (for admin users)
    else if (location.startsWith('/admin/users') || location.startsWith('/admin/content') || 
             location.startsWith('/admin/analytics') || location.startsWith('/admin/support')) {
      initialState.users = true;
    }
    
    setExpandedSections(initialState);
  }, [location]);
  
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Define a function to generate the items for better type safety
  const createNavItem = (
    icon: React.FC<any>, 
    category: IconCategory, 
    label: string, 
    path: string,
    children?: NavItem[]
  ): NavItem => ({ icon, category, label, path, children });

  // Regular user navigation items - organized by workflow
  const userNavItems: NavItem[] = [
    // Dashboard - single top-level item
    createNavItem(LayoutDashboard, 'dashboard', "Dashboard", "/dashboard"),
    
    // Content Creation Hub - restructured with clearer subsections
    createNavItem(VideoIcon, 'courses', "Create", "/create", [
      // Course creation options
      createNavItem(VideoIcon, 'courses', "Create New Course", "/create"),
      createNavItem(Layers, 'courses', "Traditional Course", "/course-creation/traditional-course"),
      createNavItem(UserCog, 'ai', "Avatar Course", "/course-creation/avatar-course"),
      // Course management
      createNavItem(VideoIcon, 'courses', "My Courses", "/my-courses"),

      // Media & Resources
      createNavItem(Film, 'media', "Media Library", "/media-library")
    ]),
    
    // AI Tools - specialized AI services
    createNavItem(Wand2, 'ai', "AI Tools", "/ai-templates", [
      createNavItem(FileText, 'ai', "AI Templates", "/ai-templates"),
      createNavItem(Mic, 'media', "Voice Generator", "/ai-voice-generator"),
      createNavItem(ImageIcon, 'media', "AI Image", "/ai-image-generator"),
      createNavItem(CreditCard, 'ai', "AI Credits", "/ai-credits")
    ]),
    
    // Collaboration - team features
    createNavItem(Users, 'collaboration', "Collaborate", "/collaboration", [
      createNavItem(Users, 'users', "Teams", "/teams"),
      createNavItem(Share2, 'content', "Shared Courses", "/collaborate/shared-courses"),
      createNavItem(MessageSquare, 'collaboration', "Meetings", "/meetings")
    ]),
    
    // Distribution & Marketing - everything for promoting courses
    createNavItem(Megaphone, 'marketing', "Distribute", "/distribute", [
      // Platform Distribution
      createNavItem(Link2, 'settings', "Platform Integrations", "/platform-integrations"),
      createNavItem(Store, 'content', "Course Marketplaces", "/platform-integrations/marketplaces"),
      createNavItem(VideoIcon, 'media', "Video Platforms", "/platform-integrations/video"),
      // Marketing Tools
      createNavItem(Mail, 'marketing', "Email Marketing", "/marketing/email"),
      createNavItem(Newspaper, 'marketing', "Landing Pages", "/marketing/landing-page"),
      createNavItem(PieChart, 'analytics', "Distribution Analytics", "/marketing/analytics")
    ]),
    
    // Marketplace - top level for quick access
    createNavItem(Store, 'content', "Marketplace", "/marketplace"),
  ];
  
  // Admin navigation items - simplified structure
  const adminNavItems: NavItem[] = [
    createNavItem(LayoutDashboard, 'dashboard', "Admin Dashboard", "/admin-dashboard"),
    
    // Admin Management Section - Consolidated into single dropdown
    createNavItem(UserCog, 'users', "Administration", "/admin/users", [
      createNavItem(UserCog, 'users', "User Management", "/admin/users"),
      createNavItem(FileText, 'content', "Content Moderation", "/admin/content"),
      createNavItem(PieChart, 'analytics', "Platform Analytics", "/admin/analytics"),
      createNavItem(MessageSquare, 'users', "Support Tickets", "/admin/support"),
    ]),
    
    // User Portal - with direct links to important user features
    createNavItem(LayoutDashboard, 'dashboard', "User Portal", "/dashboard", [
      createNavItem(LayoutDashboard, 'dashboard', "Dashboard", "/dashboard"),
      createNavItem(VideoIcon, 'courses', "Create", "/my-courses"),
      createNavItem(Wand2, 'ai', "AI Tools", "/ai-templates"),
      createNavItem(Users, 'collaboration', "Collaborate", "/collaboration"),
      createNavItem(Megaphone, 'marketing', "Distribute", "/distribute"),
      createNavItem(Store, 'content', "Marketplace", "/marketplace"),
      createNavItem(Settings, 'settings', "Settings", "/settings")
    ]),
  ];

  // Select the appropriate navigation items based on user role
  const navItems = isAdmin ? adminNavItems : userNavItems;
  
  return (
    <aside className="bg-white border-r border-slate-200 w-full md:w-64 md:fixed md:h-full z-10 shadow-sm bg-gradient-to-b from-white to-slate-50/50">
      <div className="p-4 border-b border-slate-200 bg-white">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 blur-lg opacity-20"></div>
            <div className="relative w-10 h-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center shadow-md">
              <Sparkles className="text-white" size={20} />
            </div>
          </div>
          <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Koursia {isAdmin && <span className="text-xs bg-amber-100 text-amber-800 px-1.5 py-0.5 rounded ml-1">ADMIN</span>}
          </h1>
        </div>
      </div>
      
      <nav className="p-4 pb-4 overflow-y-auto max-h-[calc(100vh-76px)] flex flex-col min-h-[calc(100vh-76px)]">
        <div className="space-y-1">
          {navItems.map((item, index) => 
            item.children ? (
              <div key={item.path} className="mb-2">
                <button
                  onClick={() => toggleSection(item.category)}
                  className={`w-full flex items-center justify-between rounded-md px-3 py-2.5 font-medium transition-all duration-200 ${
                    location.startsWith(item.path)
                      ? "text-primary bg-primary/5 shadow-sm"
                      : "text-slate-700 hover:bg-slate-50 hover:text-primary"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <ColorfulIcon 
                      icon={item.icon as any} 
                      category={location.startsWith(item.path) ? item.category : "default"}
                      size={20}
                    />
                    <span>{item.label}</span>
                  </div>
                  <div>
                    {expandedSections[item.category] ? (
                      <ChevronDown size={18} className="text-slate-500" />
                    ) : (
                      <ChevronRight size={18} className="text-slate-500" />
                    )}
                  </div>
                </button>
                
                {expandedSections[item.category] && item.children && (
                  <div className="ml-5 pl-2 space-y-0.5 mt-1 pt-1 border-l border-slate-200">
                    {item.children.map((childItem) => (
                      <Link
                        key={childItem.path}
                        href={childItem.path}
                        className={`flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${
                          location === childItem.path
                            ? "nav-highlight active text-primary bg-primary/5 border-l-2 border-primary shadow-sm"
                            : "text-slate-600 hover:bg-slate-50 hover:text-primary"
                        }`}
                      >
                        <ColorfulIcon 
                          icon={childItem.icon as any} 
                          category={location === childItem.path ? childItem.category : "default"}
                          size={16}
                        />
                        <span>{childItem.label}</span>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <Link 
                key={item.path} 
                href={item.path}
                className={`flex items-center space-x-3 rounded-md px-3 py-2.5 font-medium transition-all duration-200 ${
                  location === item.path
                    ? "nav-highlight active text-primary bg-primary/5 border-l-2 border-primary shadow-sm"
                    : "text-slate-700 hover:bg-slate-50 hover:text-primary"
                }`}
              >
                <ColorfulIcon 
                  icon={item.icon as any} 
                  category={location === item.path ? item.category : "default"}
                  size={20}
                />
                <span>{item.label}</span>
              </Link>
            )
          )}
        </div>
        
        {/* Bottom section with Settings, User profile, and Logout */}
        <div className="mt-auto mb-4">
          {/* Settings & Help Section - at the bottom */}
          <div className="mb-2 border-t border-slate-200 pt-4">
            <div className="px-3 mb-1">
              <span className="text-xs font-medium text-slate-400 uppercase tracking-wider">Settings & Help</span>
            </div>
            <div className="space-y-1">
              <Link 
                href="/settings" 
                className={`flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${
                  location === "/settings"
                    ? "text-primary"
                    : "text-slate-600 hover:text-primary"
                }`}
              >
                <ColorfulIcon icon={Settings} category={location === "/settings" ? "settings" : "default"} size={16} />
                <span>Settings</span>
              </Link>
              <Link 
                href="/pricing" 
                className={`flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${
                  location === "/pricing"
                    ? "text-primary"
                    : "text-slate-600 hover:text-primary"
                }`}
              >
                <ColorfulIcon icon={Tag} category={location === "/pricing" ? "info" : "default"} size={16} />
                <span>Pricing</span>
              </Link>
              <Link 
                href="/help" 
                className={`flex items-center space-x-3 rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${
                  location === "/help"
                    ? "text-primary"
                    : "text-slate-600 hover:text-primary"
                }`}
              >
                <ColorfulIcon icon={HelpCircle} category={location === "/help" ? "info" : "default"} size={16} />
                <span>Help Center</span>
              </Link>
            </div>
          </div>
          
          {/* User Profile & Logout */}
          {user && (
            <div className="border-t border-slate-200 pt-4">
              {/* User Profile */}
              <Link href="/profile" className="flex items-center space-x-3 px-3 py-2 hover:text-primary transition-all">
                <div className="flex-shrink-0">
                  <span className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center text-sm font-medium">
                    {user.name ? user.name.charAt(0).toUpperCase() : user.username.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-slate-900 truncate">{user.name || user.username}</p>
                  <p className="text-xs text-primary font-medium">{user.plan} Plan</p>
                </div>
              </Link>
              
              {/* Logout Button */}
              <button 
                onClick={() => {
                  queryClient.setQueryData(['/api/auth/me'], null);
                  queryClient.invalidateQueries();
                  window.location.href = '/';
                  apiRequest('POST', '/api/auth/logout', {});
                }} 
                className="flex items-center space-x-3 px-3 py-2 text-red-600 font-medium hover:text-red-700 w-full text-left transition-all mt-1"
              >
                <ColorfulIcon icon={LogOut} category="warning" size={16} />
                <span>Log Out</span>
              </button>
            </div>
          )}
        </div>
      </nav>
    </aside>
  );
}