import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

interface Achievement {
  type: 'lesson_complete' | 'module_complete' | 'quiz_passed' | 'streak' | 'badge_earned' | 'course_complete';
  title: string;
  description: string;
  points?: number;
  streakCount?: number;
  badgeName?: string;
}

interface ProgressStep {
  id: string;
  title: string;
  status: 'pending' | 'active' | 'completed' | 'skipped';
  duration?: number;
}

interface MicroInteractionState {
  showCelebration: boolean;
  currentAchievement: Achievement | null;
  progressSteps: ProgressStep[];
  currentStepIndex: number;
  overallProgress: number;
  timeElapsed: number;
  streakCount: number;
  totalXP: number;
}

export function useMicroInteractions() {
  const { toast } = useToast();
  const [state, setState] = useState<MicroInteractionState>({
    showCelebration: false,
    currentAchievement: null,
    progressSteps: [],
    currentStepIndex: 0,
    overallProgress: 0,
    timeElapsed: 0,
    streakCount: 0,
    totalXP: 0,
  });

  // Initialize progress tracking for a learning session
  const initializeProgress = useCallback((steps: ProgressStep[]) => {
    setState(prev => ({
      ...prev,
      progressSteps: steps.map(step => ({ ...step, status: 'pending' })),
      currentStepIndex: 0,
      overallProgress: 0,
      timeElapsed: 0,
    }));
  }, []);

  // Update current step progress
  const updateStepProgress = useCallback((stepIndex: number, status: ProgressStep['status']) => {
    setState(prev => {
      const updatedSteps = [...prev.progressSteps];
      if (updatedSteps[stepIndex]) {
        updatedSteps[stepIndex].status = status;
        
        // Update current step index to active step
        if (status === 'active') {
          return {
            ...prev,
            progressSteps: updatedSteps,
            currentStepIndex: stepIndex,
          };
        }
      }
      
      // Calculate overall progress
      const completedSteps = updatedSteps.filter(step => step.status === 'completed').length;
      const overallProgress = (completedSteps / updatedSteps.length) * 100;
      
      return {
        ...prev,
        progressSteps: updatedSteps,
        overallProgress,
      };
    });
  }, []);

  // Complete a step and trigger micro-interactions
  const completeStep = useCallback((stepIndex: number, achievement?: Partial<Achievement>) => {
    updateStepProgress(stepIndex, 'completed');
    
    if (achievement) {
      const fullAchievement: Achievement = {
        type: achievement.type || 'lesson_complete',
        title: achievement.title || 'Step Completed!',
        description: achievement.description || 'Great job completing this step!',
        points: achievement.points || 10,
        ...achievement,
      };
      
      triggerCelebration(fullAchievement);
    }
    
    // Auto-advance to next step
    setState(prev => {
      if (stepIndex + 1 < prev.progressSteps.length) {
        const updatedSteps = [...prev.progressSteps];
        updatedSteps[stepIndex + 1].status = 'active';
        return {
          ...prev,
          progressSteps: updatedSteps,
          currentStepIndex: stepIndex + 1,
        };
      }
      return prev;
    });
  }, [updateStepProgress]);

  // Trigger achievement celebration
  const triggerCelebration = useCallback((achievement: Achievement) => {
    setState(prev => ({
      ...prev,
      showCelebration: true,
      currentAchievement: achievement,
      totalXP: prev.totalXP + (achievement.points || 0),
    }));

    // Update streak if applicable
    if (achievement.type === 'lesson_complete' || achievement.type === 'module_complete') {
      setState(prev => ({
        ...prev,
        streakCount: prev.streakCount + 1,
      }));
    }

    // Show toast notification
    toast({
      title: achievement.title,
      description: achievement.description,
      duration: 3000,
    });
  }, [toast]);

  // Close celebration modal
  const closeCelebration = useCallback(() => {
    setState(prev => ({
      ...prev,
      showCelebration: false,
      currentAchievement: null,
    }));
  }, []);

  // Update time elapsed (call this from a timer)
  const updateTimeElapsed = useCallback((seconds: number) => {
    setState(prev => ({
      ...prev,
      timeElapsed: seconds,
    }));
  }, []);

  // Trigger quiz completion
  const completeQuiz = useCallback((score: number, passed: boolean) => {
    const achievement: Achievement = {
      type: 'quiz_passed',
      title: passed ? 'Quiz Passed!' : 'Quiz Completed',
      description: passed 
        ? `Excellent work! You scored ${score}%` 
        : `You scored ${score}%. Keep practicing!`,
      points: passed ? Math.floor(score / 10) * 5 : 5,
    };
    
    triggerCelebration(achievement);
  }, [triggerCelebration]);

  // Trigger module completion
  const completeModule = useCallback((moduleName: string) => {
    const achievement: Achievement = {
      type: 'module_complete',
      title: 'Module Completed!',
      description: `You've successfully completed "${moduleName}"`,
      points: 50,
    };
    
    triggerCelebration(achievement);
  }, [triggerCelebration]);

  // Trigger course completion
  const completeCourse = useCallback((courseName: string) => {
    const achievement: Achievement = {
      type: 'course_complete',
      title: 'Course Completed!',
      description: `Congratulations! You've finished "${courseName}"`,
      points: 200,
    };
    
    triggerCelebration(achievement);
  }, [triggerCelebration]);

  // Trigger streak milestone
  const checkStreakMilestone = useCallback(() => {
    const { streakCount } = state;
    const milestones = [5, 10, 20, 30, 50, 100];
    
    if (milestones.includes(streakCount)) {
      const achievement: Achievement = {
        type: 'streak',
        title: 'Streak Milestone!',
        description: `Amazing! You've maintained a ${streakCount}-day learning streak!`,
        points: streakCount * 2,
        streakCount,
      };
      
      triggerCelebration(achievement);
    }
  }, [state.streakCount, triggerCelebration]);

  // Trigger badge earned
  const earnBadge = useCallback((badgeName: string, description: string) => {
    const achievement: Achievement = {
      type: 'badge_earned',
      title: 'Badge Earned!',
      description,
      points: 100,
      badgeName,
    };
    
    triggerCelebration(achievement);
  }, [triggerCelebration]);

  // Calculate estimated time remaining
  const getEstimatedTimeRemaining = useCallback(() => {
    const { progressSteps, currentStepIndex, timeElapsed } = state;
    
    if (currentStepIndex === 0 || timeElapsed === 0) return null;
    
    const completedSteps = currentStepIndex;
    const remainingSteps = progressSteps.length - completedSteps;
    const averageTimePerStep = timeElapsed / completedSteps;
    
    return Math.round(averageTimePerStep * remainingSteps);
  }, [state]);

  // Check for engagement patterns and trigger appropriate feedback
  const checkEngagementPatterns = useCallback(() => {
    const { timeElapsed, currentStepIndex, progressSteps } = state;
    
    // Check if user is taking too long on a step
    if (timeElapsed > 0 && currentStepIndex < progressSteps.length) {
      const currentStep = progressSteps[currentStepIndex];
      if (currentStep?.duration && timeElapsed > currentStep.duration * 1.5) {
        toast({
          title: "Need help?",
          description: "You've been on this step for a while. Consider reviewing the material or asking for help.",
          duration: 5000,
        });
      }
    }
  }, [state, toast]);

  // Auto-check engagement patterns
  useEffect(() => {
    const interval = setInterval(checkEngagementPatterns, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [checkEngagementPatterns]);

  return {
    // State
    ...state,
    estimatedTimeRemaining: getEstimatedTimeRemaining(),
    
    // Actions
    initializeProgress,
    updateStepProgress,
    completeStep,
    triggerCelebration,
    closeCelebration,
    updateTimeElapsed,
    completeQuiz,
    completeModule,
    completeCourse,
    checkStreakMilestone,
    earnBadge,
  };
}