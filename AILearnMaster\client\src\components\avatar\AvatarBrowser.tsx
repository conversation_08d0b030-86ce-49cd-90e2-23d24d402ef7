import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { 
  Loader2, 
  UserCircle, 
  Search,
  Upload, 
  ImagePlus, 
  X, 
  Check,
  Info,
  Download,
  UserPlus,
  Users
} from "lucide-react";
import FileUpload from "../FileUpload";
import MediaLibrary from "../MediaLibrary";

interface AvatarBrowserProps {
  onSelect: (data: AvatarSelectionData) => void;
  onCancel: () => void;
}

export interface AvatarSelectionData {
  type: 'upload' | 'library' | 'template' | 'stock' | 'url';
  file?: File;
  url?: string;
  sourceId?: string;
  sourceName?: string;
  avatarId?: string;
}

export function AvatarBrowser({ onSelect, onCancel }: AvatarBrowserProps) {
  const [selectedTab, setSelectedTab] = useState("templates");
  const [selectedTemplateUrl, setSelectedTemplateUrl] = useState<string | null>(null);
  const [uploadedAvatarFile, setUploadedAvatarFile] = useState<File | null>(null);
  const [uploadedAvatarUrl, setUploadedAvatarUrl] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState("");
  const [mediaDialogOpen, setMediaDialogOpen] = useState(false);
  const [stockLoading, setStockLoading] = useState(false);
  const { toast } = useToast();
  
  // File input ref for manual file uploads
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  
  // Avatar templates (predefined)
  const avatarTemplates = [
    { id: 1, name: "Professional Man", url: "/assets/avatars/professional-male.jpg" },
    { id: 2, name: "Professional Woman", url: "/assets/avatars/professional-female.jpg" },
    { id: 3, name: "Casual Man", url: "/assets/avatars/casual-male.jpg" },
    { id: 4, name: "Casual Woman", url: "/assets/avatars/casual-female.jpg" },
    { id: 5, name: "Teacher", url: "/assets/avatars/teacher.jpg" },
    { id: 6, name: "Business Presenter", url: "/assets/avatars/business-presenter.jpg" },
    { id: 7, name: "Young Professional", url: "/assets/avatars/young-professional.jpg" },
    { id: 8, name: "Senior Expert", url: "/assets/avatars/senior-expert.jpg" },
  ];
  
  // Fetch stock avatars
  const { data: stockAvatars, isLoading: isLoadingStockAvatars } = useQuery({
    queryKey: ['/api/ai/stock-avatars'],
    queryFn: async () => {
      try {
        const response = await apiRequest('GET', '/api/ai/stock-avatars');
        if (!response.ok) {
          throw new Error("Failed to fetch stock avatars");
        }
        return await response.json();
      } catch (error) {
        console.error("Error fetching stock avatars:", error);
        return [];
      }
    },
    enabled: selectedTab === "stock",
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Handle file upload (direct upload)
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Invalid file type",
          description: "Please upload an image file",
          variant: "destructive"
        });
        return;
      }
      
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please upload an image smaller than 5MB",
          variant: "destructive"
        });
        return;
      }
      
      setUploadedAvatarFile(file);
      const imageUrl = URL.createObjectURL(file);
      setUploadedAvatarUrl(imageUrl);
    }
  };
  
  // Handle template selection
  const handleTemplateSelect = async (templateUrl: string) => {
    setSelectedTemplateUrl(templateUrl);
    
    try {
      // Fetch the image from the template URL
      const response = await fetch(templateUrl);
      const blob = await response.blob();
      const file = new File([blob], `avatar-template-${Date.now()}.jpg`, { type: "image/jpeg" });
      
      setUploadedAvatarFile(file);
      setUploadedAvatarUrl(templateUrl);
    } catch (error) {
      console.error("Error selecting template:", error);
      toast({
        title: "Error selecting template",
        description: "Could not load the selected template",
        variant: "destructive"
      });
    }
  };
  
  // Handle media library selection
  const handleMediaSelection = (media: any) => {
    if (media.type !== "image") {
      toast({
        title: "Invalid media type",
        description: "Please select an image from your media library",
        variant: "destructive"
      });
      return;
    }
    
    setUploadedAvatarUrl(media.url);
    setMediaDialogOpen(false);
    
    onSelect({
      type: 'library',
      url: media.url,
      sourceId: media.id.toString(),
      sourceName: media.name
    });
  };
  
  // Handle stock avatar selection
  const handleStockAvatarSelect = (avatar: any) => {
    if (!avatar || !avatar.id) {
      toast({
        title: "Invalid avatar",
        description: "Could not select the avatar",
        variant: "destructive"
      });
      return;
    }
    
    onSelect({
      type: 'stock',
      url: avatar.url,
      avatarId: avatar.id.toString(),
      sourceName: avatar.name
    });
  };
  
  // Handle URL image input
  const handleUrlImageSubmit = async () => {
    if (!imageUrl || !imageUrl.match(/^https?:\/\/.+\.(jpg|jpeg|png|webp)$/i)) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid image URL",
        variant: "destructive"
      });
      return;
    }
    
    try {
      // Test if the URL is valid and accessible
      const response = await fetch(imageUrl, { method: 'HEAD' });
      
      if (!response.ok) {
        throw new Error("Failed to access the image URL");
      }
      
      // Valid URL, pass it back
      onSelect({
        type: 'url',
        url: imageUrl,
      });
    } catch (error) {
      toast({
        title: "Error accessing image",
        description: "Could not access the image at the provided URL",
        variant: "destructive"
      });
    }
  };
  
  // Handle the final selection
  const handleConfirmSelection = () => {
    if (!uploadedAvatarFile && !uploadedAvatarUrl && selectedTab !== "stock") {
      toast({
        title: "No avatar selected",
        description: "Please select or upload an avatar image",
        variant: "destructive"
      });
      return;
    }
    
    if (selectedTab === "upload" && uploadedAvatarFile) {
      onSelect({
        type: 'upload',
        file: uploadedAvatarFile,
        url: uploadedAvatarUrl || undefined
      });
    } else if (selectedTab === "templates" && selectedTemplateUrl) {
      onSelect({
        type: 'template',
        file: uploadedAvatarFile || undefined,
        url: selectedTemplateUrl
      });
    } else if (selectedTab === "url") {
      handleUrlImageSubmit();
    }
  };
  
  // Reset selected avatar when changing tabs
  useEffect(() => {
    setSelectedTemplateUrl(null);
    setUploadedAvatarFile(null);
    setUploadedAvatarUrl(null);
    setImageUrl("");
  }, [selectedTab]);
  
  return (
    <div className="flex flex-col space-y-6">
      <h2 className="text-2xl font-bold flex items-center">
        <UserCircle className="mr-2 h-6 w-6" />
        Select Avatar
      </h2>
      
      <Tabs 
        defaultValue="templates" 
        value={selectedTab} 
        onValueChange={setSelectedTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5">
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="upload">Upload</TabsTrigger>
          <TabsTrigger value="library">Media Library</TabsTrigger>
          <TabsTrigger value="stock">Stock Avatars</TabsTrigger>
          <TabsTrigger value="url">Image URL</TabsTrigger>
        </TabsList>
        
        {/* Templates Tab */}
        <TabsContent value="templates" className="pt-4">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {avatarTemplates.map((template) => (
              <div 
                key={template.id}
                className={`relative border rounded-lg overflow-hidden cursor-pointer transition-all ${
                  selectedTemplateUrl === template.url ? 'ring-2 ring-primary' : 'hover:shadow-md'
                }`}
                onClick={() => handleTemplateSelect(template.url)}
              >
                <img 
                  src={template.url} 
                  alt={template.name} 
                  className="w-full aspect-square object-cover" 
                />
                {selectedTemplateUrl === template.url && (
                  <div className="absolute top-2 right-2 bg-primary rounded-full p-1">
                    <Check className="h-4 w-4 text-white" />
                  </div>
                )}
                <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-1">
                  <p className="text-white text-xs text-center">{template.name}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex justify-end mt-4">
            <Button
              onClick={handleConfirmSelection}
              disabled={!selectedTemplateUrl}
            >
              Use Selected Template
            </Button>
          </div>
        </TabsContent>
        
        {/* Upload Tab */}
        <TabsContent value="upload" className="pt-4">
          <div className="flex flex-col items-center justify-center space-y-4">
            {uploadedAvatarUrl ? (
              <div className="relative w-48 h-48 mx-auto">
                <img 
                  src={uploadedAvatarUrl} 
                  alt="Avatar" 
                  className="w-full h-full object-cover rounded-lg" 
                />
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2 h-8 w-8"
                  onClick={() => {
                    setUploadedAvatarFile(null);
                    setUploadedAvatarUrl(null);
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div 
                className="w-48 h-48 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-primary"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-12 w-12 text-gray-400" />
                <p className="text-sm text-gray-500 mt-2">Click to upload avatar</p>
                <p className="text-xs text-gray-400 mt-1">PNG, JPG, WEBP up to 5MB</p>
              </div>
            )}
            
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleFileUpload}
            />
            
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => fileInputRef.current?.click()}
            >
              <ImagePlus className="h-4 w-4" />
              Select Image
            </Button>
            
            <div className="flex justify-center mt-4 w-full">
              <Button
                onClick={handleConfirmSelection}
                disabled={!uploadedAvatarFile}
                className="w-full max-w-xs"
              >
                Use Uploaded Image
              </Button>
            </div>
          </div>
        </TabsContent>
        
        {/* Media Library Tab */}
        <TabsContent value="library" className="pt-4">
          <Dialog open={mediaDialogOpen} onOpenChange={setMediaDialogOpen}>
            <DialogTrigger asChild>
              <Button className="mb-4">
                <Search className="h-4 w-4 mr-2" />
                Browse Media Library
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl w-full">
              <DialogHeader>
                <DialogTitle>Select from Media Library</DialogTitle>
              </DialogHeader>
              <div className="h-[60vh] overflow-y-auto">
                <MediaLibrary 
                  selectable={true}
                  onSelectMedia={handleMediaSelection}
                />
              </div>
            </DialogContent>
          </Dialog>
          
          <div className="flex flex-col items-center justify-center">
            <div className="text-center py-8">
              <Users className="h-12 w-12 mx-auto text-gray-400 mb-3" />
              <h3 className="text-lg font-medium">Browse your media library</h3>
              <p className="text-sm text-gray-500 mt-1">
                Select images from your existing library to use as avatars
              </p>
            </div>
          </div>
        </TabsContent>
        
        {/* Stock Avatars Tab */}
        <TabsContent value="stock" className="pt-4">
          {isLoadingStockAvatars ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
              <p className="text-sm text-gray-500">Loading stock avatars...</p>
            </div>
          ) : stockAvatars && stockAvatars.length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {stockAvatars.map((avatar: any) => (
                  <Card 
                    key={avatar.id}
                    className={`overflow-hidden cursor-pointer transition-all ${
                      selectedTemplateUrl === avatar.url ? 'ring-2 ring-primary' : 'hover:shadow-md'
                    }`}
                    onClick={() => handleStockAvatarSelect(avatar)}
                  >
                    <div className="aspect-square w-full">
                      <img 
                        src={avatar.url} 
                        alt={avatar.name} 
                        className="w-full h-full object-cover" 
                      />
                    </div>
                    <CardFooter className="p-2">
                      <p className="text-xs font-medium">{avatar.name}</p>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                No stock avatars available. Please check the connection to the avatar service.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>
        
        {/* URL Tab */}
        <TabsContent value="url" className="pt-4">
          <div className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="image-url">Image URL</Label>
              <div className="flex gap-2">
                <Input
                  id="image-url"
                  placeholder="https://example.com/image.jpg"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                />
                <Button 
                  onClick={handleUrlImageSubmit}
                  disabled={!imageUrl.trim() || !imageUrl.match(/^https?:\/\/.+$/i)}
                >
                  Use URL
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Enter a direct URL to an image (jpg, png, webp)
              </p>
            </div>
            
            {imageUrl && imageUrl.match(/^https?:\/\/.+\.(jpg|jpeg|png|webp)$/i) && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Preview:</h4>
                <div className="border rounded-md overflow-hidden w-48 h-48">
                  <img 
                    src={imageUrl} 
                    alt="URL preview" 
                    className="w-full h-full object-cover"
                    onError={() => {
                      toast({
                        title: "Image Error",
                        description: "Could not load the image from URL",
                        variant: "destructive"
                      });
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
      
      <div className="flex justify-between pt-4 mt-4 border-t">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
}

export default AvatarBrowser;