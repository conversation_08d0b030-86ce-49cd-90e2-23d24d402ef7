import { useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';

interface StockItem {
  id: string;
  type: 'photo' | 'video';
  url: string;
  thumbnailUrl: string;
  title: string;
  tags?: string;
  photographer?: string;
  user?: string;
  source: 'pexels' | 'pixabay';
  width: number;
  height: number;
  duration?: number;
  views?: number;
  likes?: number;
  downloads?: number;
}

export const useAutoImport = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const autoImportMedia = useCallback(async (item: StockItem): Promise<boolean> => {
    try {
      // Extract proper ID from composite ID
      const actualId = item.id.replace(/^(pexels|pixabay)-(photo|video)-/, '');
      
      const response = await fetch('/api/stock/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: actualId,
          type: item.type,
          url: item.url,
          thumbnailUrl: item.thumbnailUrl,
          title: item.title,
          tags: item.tags,
          photographer: item.photographer || item.user,
          source: item.source,
          width: item.width,
          height: item.height,
          duration: item.duration,
          metadata: {
            views: item.views,
            likes: item.likes,
            downloads: item.downloads,
            author: item.photographer || item.user,
            size: 0
          }
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Import failed: ${response.status}`);
      }

      const importedMedia = await response.json();
      console.log('Auto-imported media:', importedMedia);
      
      // Invalidate media queries to refresh the library
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      
      // Show success toast
      toast({
        title: "Media Added",
        description: `"${item.title}" has been automatically added to your Media Library.`
      });
      
      return true;
      
    } catch (error: any) {
      console.error('Auto-import failed:', error);
      
      // Show error toast with retry option
      toast({
        title: "Auto-Import Failed",
        description: `Failed to add "${item.title}" to your library. You can manually import it later.`,
        variant: "destructive"
      });
      
      return false;
    }
  }, [toast, queryClient]);

  const bulkAutoImport = useCallback(async (items: StockItem[]): Promise<{ success: number; failed: number }> => {
    let success = 0;
    let failed = 0;
    
    const importPromises = items.map(async (item) => {
      const result = await autoImportMedia(item);
      if (result) {
        success++;
      } else {
        failed++;
      }
    });
    
    await Promise.all(importPromises);
    
    // Show bulk import summary
    if (items.length > 1) {
      if (failed === 0) {
        toast({
          title: "All Media Added",
          description: `Successfully imported all ${success} items to your Media Library.`
        });
      } else {
        toast({
          title: "Partial Import",
          description: `Imported ${success} items. ${failed} failed and can be retried manually.`
        });
      }
    }
    
    return { success, failed };
  }, [autoImportMedia, toast]);

  return {
    autoImportMedia,
    bulkAutoImport
  };
};