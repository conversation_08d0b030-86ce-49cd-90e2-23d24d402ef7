import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle2, 
  Loader2, 
  Save, 
  BookOpen, 
  Video, 
  Settings, 
  Film,
  User,
  Mic,
  RefreshCw,
  Wand2,
  Sparkles,
  Brain,
  PenTool,
  Target,
  BookOpen as BookIcon,
  Users,
  BookMarked,
  Plus,
  Trash,
  Volume2,
  FileText,
  Image,
  Info,
  UserRound,
  Clapperboard,
  Scissors,
  FileSpreadsheet,
  Tv,
  PlayCircle,
  Lightbulb,
  SplitSquareVertical
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { AvatarSelector } from "../avatar/AvatarSelector";
import { ScriptFormatter } from "../script/ScriptFormatter";
import { useLocation } from "wouter";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";

interface Voice {
  voice_id: string;
  name: string;
  gender: 'male' | 'female';
  preview_url?: string;
  source?: string;
}

// Define schema for the course details
const courseDetailsSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  description: z.string().min(10, { message: "Description must be at least 10 characters" }),
  category: z.string().min(1, { message: "Please select a category" }),
  targetAudience: z.string().optional(),
  useAI: z.boolean().default(true),
});

// Define schema for the module details
const moduleDetailsSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  description: z.string().min(10, { message: "Description must be at least 10 characters" }),
  courseId: z.number().positive({ message: "Valid course ID is required" }),
});

// Define schema for the lesson details including avatar-specific fields
const lessonDetailsSchema = z.object({
  title: z.string().min(3, { message: "Title must be at least 3 characters" }),
  description: z.string().min(10, { message: "Description must be at least 10 characters" }),
  script: z.string().min(10, { message: "Script must be at least 10 characters" }),
  avatarFile: z.any().optional(),
  avatarUrl: z.string().optional(),
  voice: z.string().min(1, { message: "Please select a voice" }),
  enhanceAudio: z.boolean().default(true),
  // Micro-learning settings
  microLearningEnabled: z.boolean().default(false),
  microLearningSegmentCount: z.number().min(2).max(10).default(3),
  microLearningBreakInterval: z.number().min(60).default(300), // seconds
  microLearningBreakDuration: z.number().min(15).max(120).default(60), // seconds
});

type CourseDetailsFormValues = z.infer<typeof courseDetailsSchema>;
type ModuleDetailsFormValues = z.infer<typeof moduleDetailsSchema>;
type LessonDetailsFormValues = z.infer<typeof lessonDetailsSchema>;

interface AvatarCourseCreatorProps {
  onClose: () => void;
  mode?: "create" | "edit";
  courseId?: number | null;
}

export function AvatarCourseCreator({ onClose, mode = "create", courseId = null }: AvatarCourseCreatorProps) {
  const [step, setStep] = useState<'course' | 'module' | 'lesson' | 'review'>('course');
  const [isLoading, setIsLoading] = useState(false);
  const [courseCreated, setCourseCreated] = useState(false);
  const [createdCourseId, setCreatedCourseId] = useState<number | null>(courseId);
  const [createdModuleId, setCreatedModuleId] = useState<number | null>(null);
  const [createdLessonId, setCreatedLessonId] = useState<number | null>(null);
  const [videoGenerating, setVideoGenerating] = useState(false);
  const [videoProgress, setVideoProgress] = useState(0);
  const [videoJobId, setVideoJobId] = useState<string | null>(null);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  
  // Module management states
  const [modules, setModules] = useState<any[]>([]);
  const [isCreatingModule, setIsCreatingModule] = useState(true);
  const [selectedModuleId, setSelectedModuleId] = useState<number | null>(null);
  const [isGeneratingModuleAI, setIsGeneratingModuleAI] = useState(false);
  const [isGeneratingMultipleModules, setIsGeneratingMultipleModules] = useState(false);
  const [showModuleCountDialog, setShowModuleCountDialog] = useState(false);
  const [moduleCount, setModuleCount] = useState(3); // Default to 3 modules
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();
  
  // AI Assistant states
  const [showAIAssistant, setShowAIAssistant] = useState(false);
  const [aiGenerating, setAiGenerating] = useState(false);
  const [aiTopic, setAiTopic] = useState('');
  const [courseTopics, setCourseTopics] = useState<string[]>([
    'Business and Entrepreneurship',
    'Personal Development',
    'Technology and Programming',
    'Marketing and Sales',
    'Finance and Investing',
    'Health and Wellness',
    'Art and Creativity',
    'Language Learning',
    'Professional Skills',
    'Leadership and Management'
  ]);
  
  // Comprehensive list of course categories
  const [courseCategories] = useState([
    { id: 'business', name: 'Business' },
    { id: 'entrepreneurship', name: 'Entrepreneurship' },
    { id: 'marketing', name: 'Marketing' },
    { id: 'sales', name: 'Sales' },
    { id: 'finance', name: 'Finance & Investing' },
    { id: 'technology', name: 'Technology' },
    { id: 'programming', name: 'Programming & Development' },
    { id: 'data-science', name: 'Data Science & Analytics' },
    { id: 'design', name: 'Design' },
    { id: 'personal-development', name: 'Personal Development' },
    { id: 'leadership', name: 'Leadership' },
    { id: 'management', name: 'Management' },
    { id: 'communication', name: 'Communication' },
    { id: 'productivity', name: 'Productivity' },
    { id: 'health', name: 'Health & Wellness' },
    { id: 'fitness', name: 'Fitness' },
    { id: 'languages', name: 'Languages' },
    { id: 'education', name: 'Education & Teaching' },
    { id: 'creative', name: 'Creative Arts' },
    { id: 'music', name: 'Music' },
    { id: 'writing', name: 'Writing' },
    { id: 'photography', name: 'Photography & Video' },
    { id: 'career', name: 'Career Development' },
    { id: 'science', name: 'Science' },
    { id: 'engineering', name: 'Engineering' },
    { id: 'humanities', name: 'Humanities' },
    { id: 'social-sciences', name: 'Social Sciences' },
    { id: 'lifestyle', name: 'Lifestyle' },
    { id: 'hobbies', name: 'Hobbies & Interests' },
    { id: 'other', name: 'Other' }
  ]);

  // Form setup for course details
  const courseForm = useForm<CourseDetailsFormValues>({
    resolver: zodResolver(courseDetailsSchema),
    defaultValues: {
      title: "",
      description: "",
      category: "",
      targetAudience: "",
      useAI: true,
    },
  });

  // Form setup for module details
  const moduleForm = useForm<ModuleDetailsFormValues>({
    resolver: zodResolver(moduleDetailsSchema),
    defaultValues: {
      title: "Introduction",
      description: "An introduction to the course topics and what students will learn.",
      courseId: createdCourseId || 0, // Initialize with the current course ID or 0 (will be updated)
    },
    context: {
      createdCourseId // Make course ID available in the context
    }
  });

  // Form setup for lesson details with avatar-specific fields
  const lessonForm = useForm<LessonDetailsFormValues>({
    resolver: zodResolver(lessonDetailsSchema),
    defaultValues: {
      title: "Welcome to the Course",
      description: "An introduction to the course and what students will learn.",
      script: "",
      avatarUrl: "",
      voice: "",
      enhanceAudio: true,
      // Micro-learning default settings
      microLearningEnabled: false,
      microLearningSegmentCount: 3,
      microLearningBreakInterval: 300, // 5 minutes in seconds
      microLearningBreakDuration: 60, // 60 seconds
    },
  });

  // Get available voices
  const { data: voices, isLoading: loadingVoices } = useQuery({
    queryKey: ['/api/ai/all-voices'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/ai/all-voices');
      if (!response.ok) {
        throw new Error('Failed to fetch voices');
      }
      return response.json();
    }
  });
  
  // Get modules for the current course
  const { data: courseModules, isLoading: loadingModules, refetch: refetchModules } = useQuery({
    queryKey: ['/api/courses', createdCourseId, 'modules'],
    queryFn: async () => {
      if (!createdCourseId) return [];
      const response = await apiRequest('GET', `/api/courses/${createdCourseId}/modules`);
      if (!response.ok) {
        throw new Error('Failed to fetch modules');
      }
      return response.json();
    },
    enabled: !!createdCourseId && step === 'module'
  });
  
  // Handle module data updates
  useEffect(() => {
    if (courseModules) {
      setModules(courseModules || []);
      // If there are modules and none is selected, select the first one
      if (courseModules.length > 0 && !selectedModuleId) {
        setSelectedModuleId(courseModules[0].id);
        setIsCreatingModule(false);
        moduleForm.setValue("title", courseModules[0].title);
        moduleForm.setValue("description", courseModules[0].description);
        moduleForm.setValue("courseId", createdCourseId as number);
      }
    }
  }, [courseModules, selectedModuleId, createdCourseId]);
  
  // Always keep the courseId in sync with the form
  useEffect(() => {
    if (createdCourseId) {
      // Ensure the courseId is always set in the form
      moduleForm.setValue("courseId", createdCourseId);
    }
  }, [createdCourseId, moduleForm]);
  
  // Generate module content with AI
  const generateModuleWithAI = async () => {
    if (!createdCourseId) return;
    
    setIsGeneratingModuleAI(true);
    
    try {
      // Get course information first
      const courseResponse = await apiRequest('GET', `/api/courses/${createdCourseId}`);
      if (!courseResponse.ok) {
        throw new Error('Failed to fetch course details');
      }
      const courseData = await courseResponse.json();
      
      // Generate module content based on course title and description
      const response = await apiRequest('POST', '/api/ai/generate-module-structure', {
        courseTitle: courseData.title,
        courseDescription: courseData.description,
        existingModules: modules.map(m => m.title),
        moduleTitle: moduleForm.getValues().title || 'New Module'
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate module content');
      }
      
      const data = await response.json();
      
      // Update the form with generated content
      moduleForm.setValue('title', data.title || moduleForm.getValues().title || 'New Module');
      moduleForm.setValue('description', data.description || 'A comprehensive module covering key topics.');
      
      toast({
        title: 'Module Content Generated',
        description: 'AI has created content for your module.',
      });
    } catch (error) {
      console.error('Error generating module content:', error);
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate module content',
        variant: 'destructive',
      });
    } finally {
      setIsGeneratingModuleAI(false);
    }
  };
  
  // Generate multiple modules at once with AI
  const generateMultipleModules = async (count: number) => {
    if (!createdCourseId) return;

    setIsGeneratingMultipleModules(true);
    try {
      // Get course details to pass to AI
      const courseResponse = await apiRequest('GET', `/api/courses/${createdCourseId}`);
      if (!courseResponse.ok) throw new Error("Failed to fetch course details");
      const courseData = await courseResponse.json();
      
      const generatedModules = [];
      
      // Generate multiple modules in sequence
      for (let i = 0; i < count; i++) {
        try {
          // Request module generation from AI
          const aiResponse = await apiRequest('POST', '/api/ai/generate-module-structure', {
            courseTitle: courseData.title,
            courseDescription: courseData.description,
            existingModules: [...modules.map(m => m.title), ...generatedModules.map(m => m.title)],
            moduleIndex: i + 1,
            totalModules: count
          });

          if (!aiResponse.ok) throw new Error(`Failed to generate module ${i+1}`);
          
          let moduleContent;
          try {
            moduleContent = await aiResponse.json();
          } catch (jsonError) {
            console.error("Error parsing module content JSON:", jsonError);
            // Use fallback content if JSON parsing fails
            moduleContent = {
              title: `Module ${i+1}`,
              description: `Content for module ${i+1} in ${courseData.title}`
            };
          }
          
          // Create the module via API
          const createModuleResponse = await apiRequest('POST', `/api/courses/${createdCourseId}/modules`, {
            title: moduleContent.title || `Module ${i+1}`,
            description: moduleContent.description || `Content for module ${i+1}`,
            courseId: createdCourseId
          });
          
          if (!createModuleResponse.ok) {
            throw new Error(`Failed to create module ${i+1}`);
          }
          
          try {
            console.log(`Module ${i+1} creation response:`, createModuleResponse);
            const responseText = await createModuleResponse.text();
            console.log(`Module ${i+1} raw response:`, responseText);
            
            let newModule;
            try {
              newModule = JSON.parse(responseText);
              console.log(`Module ${i+1} parsed:`, newModule);
            } catch (jsonError) {
              console.error(`Failed to parse module ${i+1} JSON:`, jsonError);
              newModule = { 
                id: Math.random(), // Temporary ID just to continue
                title: moduleContent.title || `Module ${i+1}`,
                description: moduleContent.description || `Content for module ${i+1}`
              };
            }
            
            generatedModules.push(newModule);
          } catch (responseError) {
            console.error(`Error processing module ${i+1} response:`, responseError);
            throw new Error(`Failed to process module ${i+1} response`);
          }
        } catch (moduleError) {
          console.error(`Error with module ${i+1}:`, moduleError);
          // Continue with next module if one fails
          continue;
        }
      }
      
      // Refresh modules list
      refetchModules();
      
      // Select the first created module if any modules were created
      if (generatedModules.length > 0) {
        setSelectedModuleId(generatedModules[0].id);
        setIsCreatingModule(false);
        moduleForm.setValue("title", generatedModules[0].title || "");
        moduleForm.setValue("description", generatedModules[0].description || "");
      }
      
      // Force a complete refresh of modules
      await refetchModules();
      
      // Get the latest modules directly from the API to ensure we have the most up-to-date list
      try {
        const latestModulesResponse = await apiRequest('GET', `/api/courses/${createdCourseId}/modules`);
        if (latestModulesResponse.ok) {
          const latestModules = await latestModulesResponse.json();
          console.log("Latest modules after generation:", latestModules);
          
          // Update modules state with latest data
          setModules(latestModules);
          
          // If we have modules now, consider the operation successful even if our generatedModules array is empty
          if (latestModules && latestModules.length > 0) {
            setSelectedModuleId(latestModules[0].id);
            setIsCreatingModule(false);
            moduleForm.setValue("title", latestModules[0].title || "");
            moduleForm.setValue("description", latestModules[0].description || "");
            
            toast({
              title: "Modules Created Successfully",
              description: `Created ${latestModules.length} modules for your course.`,
              variant: "default",
            });
            
            return; // Exit early if we found modules
          }
        }
      } catch (refreshError) {
        console.error("Error refreshing modules:", refreshError);
      }
      
      // If we get here, no modules were found - show error message
      if (generatedModules.length === 0) {
        console.error("Failed to create any modules. API responses were received but no modules were created.");
        toast({
          title: "No Modules Created",
          description: "Could not generate any modules. Please try again or create modules manually.",
          variant: "destructive",
        });
      } else if (generatedModules.length < count) {
        toast({
          title: "Partial Success",
          description: `Created ${generatedModules.length} out of ${count} modules with AI`,
        });
      } else {
        toast({
          title: "Modules Generated",
          description: `Successfully created ${count} modules with AI`,
        });
      }
    } catch (error) {
      console.error("Error generating multiple modules:", error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Could not generate modules. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingMultipleModules(false);
      setShowModuleCountDialog(false);
    }
  };

  // No need to fetch categories since we have a comprehensive local list

  // Course creation mutation
  const createCourseMutation = useMutation({
    mutationFn: async (data: CourseDetailsFormValues) => {
      const response = await apiRequest('POST', '/api/courses', {
        ...data,
        format: 'avatar',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create course');
      }
      return response.json();
    },
    onSuccess: (data) => {
      setCreatedCourseId(data.id);
      setCourseCreated(true);
      setStep('module');
      toast({
        title: "Course created successfully",
        description: "You can now add modules and lessons to your course.",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/courses'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create course",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Module creation or update mutation
  const createModuleMutation = useMutation({
    mutationFn: async (data: ModuleDetailsFormValues) => {
      if (!createdCourseId) throw new Error('Course ID not found');
      
      let response;
      
      if (isCreatingModule) {
        // Create a new module
        response = await apiRequest('POST', `/api/courses/${createdCourseId}/modules`, data);
      } else {
        // Update an existing module
        response = await apiRequest('PATCH', `/api/modules/${selectedModuleId}`, data);
      }
      
      if (!response.ok) {
        try {
          const errorData = await response.json();
          throw new Error(errorData.message || `Failed to ${isCreatingModule ? 'create' : 'update'} module`);
        } catch (jsonError) {
          console.error('Error parsing error response:', jsonError);
          const errorText = await response.text();
          console.log('Raw error response:', errorText);
          throw new Error(`Failed to ${isCreatingModule ? 'create' : 'update'} module: ${response.status} ${response.statusText}`);
        }
      }
      
      try {
        // Try to parse response as JSON
        const responseText = await response.text();
        console.log('Module creation/update raw response:', responseText);
        
        try {
          // Attempt to parse as JSON
          return JSON.parse(responseText);
        } catch (jsonError) {
          console.error('Error parsing module creation/update response as JSON:', jsonError);
          
          // If we can't parse JSON, create a synthetic response
          // This allows the UI flow to continue working
          return {
            id: Math.floor(Math.random() * 10000) + 1, // Generate a temporary ID
            title: data.title,
            description: data.description,
            courseId: createdCourseId
          };
        }
      } catch (error) {
        console.error('Error processing response:', error);
        throw new Error('Unable to process server response');
      }
    },
    onSuccess: (data) => {
      setCreatedModuleId(data.id);
      
      // Only change step if this wasn't triggered by the "+ New" button
      // The "+ New" button already sets selectedModuleId in its own API call
      if (selectedModuleId !== null) {
        setStep('lesson');
        toast({
          title: isCreatingModule ? "Module created successfully" : "Module updated successfully",
          description: "Now you can add a lesson to this module.",
        });
      }
      
      // Refresh modules list
      queryClient.invalidateQueries({ queryKey: [`/api/courses/${createdCourseId}/modules`] });
    },
    onError: (error: Error) => {
      toast({
        title: `Failed to ${isCreatingModule ? 'create' : 'update'} module`,
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Lesson creation with avatar video generation mutation
  const createLessonMutation = useMutation({
    mutationFn: async (data: LessonDetailsFormValues) => {
      if (!createdCourseId || !createdModuleId) throw new Error('Course or Module ID not found');
      
      setIsLoading(true);
      
      // Step 1: Create the lesson
      const lessonResponse = await apiRequest('POST', `/api/courses/${createdCourseId}/modules/${createdModuleId}/lessons`, {
        title: data.title,
        description: data.description,
        script: data.script,
        type: 'avatar-video',
        microLearningEnabled: data.microLearningEnabled,
        microLearningSegmentCount: data.microLearningEnabled ? data.microLearningSegmentCount : 3,
        microLearningBreakInterval: data.microLearningEnabled ? data.microLearningBreakInterval : 300,
        microLearningBreakDuration: data.microLearningEnabled ? data.microLearningBreakDuration : 60,
      });
      
      if (!lessonResponse.ok) {
        const errorData = await lessonResponse.json();
        throw new Error(errorData.message || 'Failed to create lesson');
      }
      
      const lessonData = await lessonResponse.json();
      const lessonId = lessonData.id;
      setCreatedLessonId(lessonId);
      
      // Step 2: Generate the avatar video
      setVideoGenerating(true);
      setVideoProgress(5); // Initial progress
      
      const formData = new FormData();
      formData.append('script', data.script);
      
      if (data.avatarFile) {
        formData.append('image', data.avatarFile);
      } else if (data.avatarUrl) {
        formData.append('imageUrl', data.avatarUrl);
      }
      
      formData.append('voice', data.voice);
      formData.append('enhanceAudio', String(data.enhanceAudio || false));
      formData.append('courseId', String(createdCourseId));
      formData.append('lessonId', String(lessonId));
      formData.append('title', data.title);
      
      const response = await fetch('/api/ai/sadtalker/generate', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate video');
      }
      
      const result = await response.json();
      setVideoJobId(result.jobId);
      
      // Poll for status
      let statusComplete = false;
      const startTime = Date.now();
      const timeout = 15 * 60 * 1000; // 15 minute timeout
      
      while (!statusComplete && Date.now() - startTime < timeout) {
        await new Promise(resolve => setTimeout(resolve, 3000)); // Poll every 3 seconds
        
        const statusResponse = await apiRequest('GET', `/api/ai/sadtalker/status/${result.jobId}`);
        if (!statusResponse.ok) {
          throw new Error('Failed to check video status');
        }
        
        const statusData = await statusResponse.json();
        
        if (statusData.status === 'completed') {
          statusComplete = true;
          setVideoUrl(statusData.videoUrl);
          setVideoProgress(100);
          
          // Update the lesson with the video URL
          const updateResponse = await apiRequest('PATCH', `/api/lessons/${lessonId}`, {
            videoUrl: statusData.videoUrl,
          });
          
          if (!updateResponse.ok) {
            const errorData = await updateResponse.json();
            throw new Error(errorData.message || 'Failed to update lesson with video URL');
          }
          
          return {
            lessonId,
            videoUrl: statusData.videoUrl,
          };
        } else if (statusData.status === 'error') {
          throw new Error(statusData.error || 'Error generating video');
        } else {
          // Update progress based on estimation
          const progressPercent = Math.min(
            Math.floor((Date.now() - startTime) / (statusData.estimatedTime * 10)),
            95
          );
          setVideoProgress(progressPercent);
        }
      }
      
      if (!statusComplete) {
        throw new Error('Video generation timed out');
      }
      
      return { lessonId, videoUrl: '' };
    },
    onSuccess: (data) => {
      setIsLoading(false);
      setVideoGenerating(false);
      setStep('review');
      toast({
        title: "Lesson created successfully",
        description: "Your avatar video has been generated and attached to the lesson.",
      });
      queryClient.invalidateQueries({ queryKey: [`/api/courses/${createdCourseId}/modules/${createdModuleId}/lessons`] });
    },
    onError: (error: Error) => {
      setIsLoading(false);
      setVideoGenerating(false);
      setVideoProgress(0);
      toast({
        title: "Failed to create lesson or generate video",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  // Generate course with AI
  const generateCourseWithAI = async (topic: string) => {
    if (!topic.trim()) {
      toast({
        title: "Topic Required",
        description: "Please enter a course topic to generate content",
        variant: "destructive",
      });
      return;
    }

    setAiGenerating(true);
    
    try {
      // Call the backend API to generate course content
      const response = await apiRequest("POST", "/api/ai/generate-course-structure", {
        title: `${topic} Masterclass`,
        description: `A comprehensive course about ${topic}`,
        category: courseTopics.find(ct => topic.toLowerCase().includes(ct.toLowerCase().split(' ')[0])) || "other",
        targetAudience: `People interested in learning about ${topic}`,
      });
      
      if (!response.ok) {
        throw new Error("Failed to generate course content");
      }
      
      const data = await response.json();
      
      // Update the form with the generated content
      courseForm.setValue("title", data.title || `${topic} Masterclass`);
      courseForm.setValue("description", data.description || `A comprehensive course about ${topic}`);
      courseForm.setValue("category", data.category || "other");
      courseForm.setValue("targetAudience", data.targetAudience || `Professionals and enthusiasts interested in ${topic}`);
      
      toast({
        title: "Course Content Generated",
        description: "AI has created content for your course based on the topic you provided.",
      });
      
      setShowAIAssistant(false);
    } catch (error) {
      console.error("Error generating course content:", error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate course content",
        variant: "destructive",
      });
    } finally {
      setAiGenerating(false);
    }
  };

  // Handle module selection
  const handleSelectModule = (moduleId: number) => {
    const selectedModule = modules.find(m => m.id === moduleId);
    if (selectedModule) {
      setSelectedModuleId(moduleId);
      setIsCreatingModule(false);
      moduleForm.setValue("title", selectedModule.title);
      moduleForm.setValue("description", selectedModule.description);
      moduleForm.setValue("courseId", createdCourseId as number);
    }
  };
  
  // Handle creating new module
  const handleCreateNewModule = () => {
    if (!createdCourseId) {
      toast({
        title: "Cannot create module",
        description: "Please create a course first.",
        variant: "destructive",
      });
      return;
    }
    
    // Show loading state
    setIsLoading(true);
    
    // Create a new module directly via API call
    // This approach bypasses the form submission to avoid form context issues
    apiRequest('POST', `/api/courses/${createdCourseId}/modules`, {
      title: "New Module",
      description: "A comprehensive module covering key topics.",
      courseId: createdCourseId
    }).then(async (response) => {
      // Clear loading state
      setIsLoading(false);
      
      if (response.ok) {
        try {
          const moduleData = await response.json();
          console.log("Module created successfully:", moduleData);
          
          // Refresh the modules list
          queryClient.invalidateQueries({ queryKey: [`/api/courses/${createdCourseId}/modules`] });
          
          // Update the UI state after a successful creation
          setIsCreatingModule(false);  // We're now editing the new module
          setSelectedModuleId(moduleData.id);
          setCreatedModuleId(moduleData.id);
          
          // Let the user know it worked
          toast({
            title: "Module created",
            description: "New module has been added to your course.",
          });
          
          // Select the newly created module after the list refreshes
          setTimeout(() => {
            setSelectedModuleId(moduleData.id);
            // After selecting it, we need to update the form with the new module data
            // but only after the component has had time to process the selection change
            setTimeout(() => {
              moduleForm.setValue("title", moduleData.title);
              moduleForm.setValue("description", moduleData.description);
              moduleForm.setValue("courseId", createdCourseId);
            }, 100);
          }, 100);
        } catch (parseError) {
          console.error("Error parsing module creation response:", parseError);
          
          // Even if we can't parse the response, the module was likely created
          toast({
            title: "Module created",
            description: "Module was created but there was an issue processing the response.",
          });
          
          // Refresh the modules list
          queryClient.invalidateQueries({ queryKey: [`/api/courses/${createdCourseId}/modules`] });
        }
      } else {
        // Handle API error
        try {
          const errorData = await response.json();
          toast({
            title: "Failed to create module",
            description: errorData.message || "An error occurred while creating the module.",
            variant: "destructive",
          });
        } catch (e) {
          toast({
            title: "Failed to create module",
            description: `Server responded with status ${response.status}`,
            variant: "destructive",
          });
        }
      }
    }).catch(error => {
      // Clear loading state
      setIsLoading(false);
      
      console.error("Error creating module:", error);
      toast({
        title: "Failed to create module",
        description: error.message || "An error occurred while creating the module.",
        variant: "destructive",
      });
    });
  };
  
  // Handle module deletion
  const deleteModule = async (moduleId: number) => {
    if (!moduleId) return;
    
    try {
      const response = await apiRequest('DELETE', `/api/modules/${moduleId}`);
      if (!response.ok) {
        throw new Error('Failed to delete module');
      }
      
      toast({
        title: 'Module Deleted',
        description: 'The module has been successfully deleted',
      });
      
      // Refresh modules list
      refetchModules();
      
      // Reset selection if the deleted module was selected
      if (selectedModuleId === moduleId) {
        handleCreateNewModule();
      }
    } catch (error) {
      console.error('Error deleting module:', error);
      toast({
        title: 'Deletion Failed',
        description: error instanceof Error ? error.message : 'Failed to delete module',
        variant: 'destructive',
      });
    }
  };

  // Handle step navigation
  const handleNextStep = () => {
    switch (step) {
      case 'course':
        courseForm.handleSubmit((data) => {
          if (mode === 'create') {
            createCourseMutation.mutate(data);
          } else {
            // Handle course editing if needed
            setStep('module');
          }
        })();
        break;
      case 'module':
        moduleForm.handleSubmit((data) => {
          createModuleMutation.mutate(data);
        })();
        break;
      case 'lesson':
        lessonForm.handleSubmit((data) => {
          if (!data.avatarFile && !data.avatarUrl) {
            toast({
              title: "Avatar required",
              description: "Please select or upload an avatar image",
              variant: "destructive"
            });
            return;
          }
          createLessonMutation.mutate(data);
        })();
        break;
      case 'review':
        // Navigate to the course page
        if (createdCourseId) {
          navigate(`/courses/${createdCourseId}`);
        }
        onClose();
        break;
    }
  };

  const handlePreviousStep = () => {
    switch (step) {
      case 'module':
        setStep('course');
        break;
      case 'lesson':
        setStep('module');
        break;
      case 'review':
        setStep('lesson');
        break;
      default:
        onClose();
        break;
    }
  };

  // Handle avatar selection
  const handleAvatarChange = (file: File | null, url: string | null) => {
    lessonForm.setValue('avatarFile', file);
    lessonForm.setValue('avatarUrl', url || '');
  };

  // Handle AI-generated script (placeholder - implement if needed)
  const handleGenerateAIScript = async () => {
    setIsLoading(true);
    
    try {
      const courseDetails = courseForm.getValues();
      const moduleDetails = moduleForm.getValues();
      const lessonDetails = lessonForm.getValues();
      
      // Use AI to generate a script based on course, module, and lesson details
      const response = await apiRequest('POST', '/api/ai/generate-lesson-script', {
        courseTitle: courseDetails.title,
        courseDescription: courseDetails.description,
        moduleTitle: moduleDetails.title,
        moduleDescription: moduleDetails.description,
        lessonTitle: lessonDetails.title,
        lessonDescription: lessonDetails.description,
        targetAudience: courseDetails.targetAudience || 'learners',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate script');
      }
      
      const data = await response.json();
      lessonForm.setValue('script', data.script);
      
      toast({
        title: "Script generated",
        description: "AI has created a script based on your course details.",
      });
    } catch (error) {
      toast({
        title: "Failed to generate script",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-background z-50 overflow-y-auto">
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-3xl font-bold">Create Avatar Course</h2>
          <Button variant="ghost" onClick={onClose}>Close</Button>
        </div>
        
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div className="flex-1">
              <div className={`flex items-center justify-center rounded-full w-10 h-10 ${step === 'course' ? 'bg-primary text-primary-foreground' : 'bg-primary/20 text-muted-foreground'} mx-auto mb-2`}>
                <Settings className="h-5 w-5" />
              </div>
              <div className="text-center text-sm font-medium">Course Details</div>
            </div>
            <div className="w-full max-w-[100px] h-[2px] bg-border" />
            <div className="flex-1">
              <div className={`flex items-center justify-center rounded-full w-10 h-10 ${step === 'module' ? 'bg-primary text-primary-foreground' : step === 'lesson' || step === 'review' ? 'bg-primary/20 text-muted-foreground' : 'bg-muted text-muted-foreground'} mx-auto mb-2`}>
                <BookOpen className="h-5 w-5" />
              </div>
              <div className="text-center text-sm font-medium">Module Setup</div>
            </div>
            <div className="w-full max-w-[100px] h-[2px] bg-border" />
            <div className="flex-1">
              <div className={`flex items-center justify-center rounded-full w-10 h-10 ${step === 'lesson' ? 'bg-primary text-primary-foreground' : step === 'review' ? 'bg-primary/20 text-muted-foreground' : 'bg-muted text-muted-foreground'} mx-auto mb-2`}>
                <User className="h-5 w-5" />
              </div>
              <div className="text-center text-sm font-medium">Avatar Lesson</div>
            </div>
            <div className="w-full max-w-[100px] h-[2px] bg-border" />
            <div className="flex-1">
              <div className={`flex items-center justify-center rounded-full w-10 h-10 ${step === 'review' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'} mx-auto mb-2`}>
                <Video className="h-5 w-5" />
              </div>
              <div className="text-center text-sm font-medium">Review</div>
            </div>
          </div>
        </div>
        
        {/* Content based on current step */}
        <div className="space-y-6">
          {/* Step 1: Course Details */}
          {step === 'course' && (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Course Details</CardTitle>
                    <CardDescription>Provide information about your avatar-based course</CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAIAssistant(true)}
                    className="flex items-center gap-2"
                  >
                    <Wand2 className="h-4 w-4" />
                    <span>AI Assist</span>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <Form {...courseForm}>
                  <form className="space-y-6">
                    <FormField
                      control={courseForm.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Course Title</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter course title" {...field} />
                          </FormControl>
                          <FormDescription>
                            A clear, descriptive title for your course
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={courseForm.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Course Description</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Describe what students will learn in this course" 
                              className="min-h-32"
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            A detailed description of the course content and objectives
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={courseForm.control}
                      name="category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="max-h-[300px]">
                              <SelectGroup>
                                <SelectLabel>Business & Marketing</SelectLabel>
                                {courseCategories
                                  .filter(c => ['business', 'entrepreneurship', 'marketing', 'sales', 'finance'].includes(c.id))
                                  .map(category => (
                                    <SelectItem key={category.id} value={category.id}>
                                      {category.name}
                                    </SelectItem>
                                  ))}
                              </SelectGroup>
                              
                              <SelectGroup>
                                <SelectLabel>Technology</SelectLabel>
                                {courseCategories
                                  .filter(c => ['technology', 'programming', 'data-science', 'engineering'].includes(c.id))
                                  .map(category => (
                                    <SelectItem key={category.id} value={category.id}>
                                      {category.name}
                                    </SelectItem>
                                  ))}
                              </SelectGroup>
                              
                              <SelectGroup>
                                <SelectLabel>Personal Development</SelectLabel>
                                {courseCategories
                                  .filter(c => ['personal-development', 'leadership', 'management', 'communication', 'productivity', 'career'].includes(c.id))
                                  .map(category => (
                                    <SelectItem key={category.id} value={category.id}>
                                      {category.name}
                                    </SelectItem>
                                  ))}
                              </SelectGroup>
                              
                              <SelectGroup>
                                <SelectLabel>Health & Lifestyle</SelectLabel>
                                {courseCategories
                                  .filter(c => ['health', 'fitness', 'lifestyle', 'hobbies'].includes(c.id))
                                  .map(category => (
                                    <SelectItem key={category.id} value={category.id}>
                                      {category.name}
                                    </SelectItem>
                                  ))}
                              </SelectGroup>
                              
                              <SelectGroup>
                                <SelectLabel>Arts & Humanities</SelectLabel>
                                {courseCategories
                                  .filter(c => ['design', 'creative', 'music', 'writing', 'photography', 'humanities', 'social-sciences'].includes(c.id))
                                  .map(category => (
                                    <SelectItem key={category.id} value={category.id}>
                                      {category.name}
                                    </SelectItem>
                                  ))}
                              </SelectGroup>
                              
                              <SelectGroup>
                                <SelectLabel>Education & Languages</SelectLabel>
                                {courseCategories
                                  .filter(c => ['education', 'languages', 'science'].includes(c.id))
                                  .map(category => (
                                    <SelectItem key={category.id} value={category.id}>
                                      {category.name}
                                    </SelectItem>
                                  ))}
                              </SelectGroup>
                              
                              <SelectGroup>
                                <SelectLabel>Other</SelectLabel>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose the category that best fits your course
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={courseForm.control}
                      name="targetAudience"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Target Audience</FormLabel>
                          <FormControl>
                            <Input placeholder="Who is this course for?" {...field} />
                          </FormControl>
                          <FormDescription>
                            Describe who would benefit most from taking this course
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button onClick={handleNextStep} disabled={createCourseMutation.isPending}>
                  {createCourseMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      Next
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          )}
          
          {/* Step 2: Module Details */}
          {step === 'module' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Module List Sidebar */}
              <Card className="lg:col-span-1">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-center">
                    <CardTitle>Course Modules</CardTitle>
                    <div className="flex gap-2">
                      <Button 
                        variant="secondary" 
                        size="sm" 
                        onClick={() => setShowModuleCountDialog(true)}
                        disabled={isGeneratingMultipleModules || !createdCourseId}
                        className="flex items-center"
                      >
                        {isGeneratingMultipleModules ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : (
                          <Sparkles className="h-4 w-4 mr-1" />
                        )}
                        Auto-Generate
                      </Button>
                      <Button variant="outline" size="sm" onClick={handleCreateNewModule}>
                        <Plus className="h-4 w-4 mr-1" />
                        New
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {loadingModules ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : modules.length === 0 ? (
                    <div className="text-center py-8 space-y-3">
                      <div className="bg-muted rounded-full p-3 inline-flex">
                        <BookOpen className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <div className="space-y-1">
                        <h3 className="font-medium">No Modules Yet</h3>
                        <p className="text-sm text-muted-foreground">
                          Create your first module to add lessons
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2">
                      {modules.map((module, index) => (
                        <div 
                          key={module.id} 
                          className={`p-3 rounded-md border cursor-pointer transition-colors ${
                            selectedModuleId === module.id 
                              ? 'bg-primary/10 border-primary/30' 
                              : 'hover:bg-muted'
                          }`}
                          onClick={() => handleSelectModule(module.id)}
                        >
                          <div className="flex justify-between items-start gap-2">
                            <div>
                              <p className="font-medium text-sm">{module.title}</p>
                              <p className="text-xs text-muted-foreground truncate max-w-[200px]">
                                {module.description?.substring(0, 60)}
                                {module.description?.length > 60 ? '...' : ''}
                              </p>
                            </div>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-7 w-7"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteModule(module.id);
                              }}
                            >
                              <Trash className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                            </Button>
                          </div>
                          <div className="mt-2 flex items-center text-xs text-muted-foreground">
                            <BookOpen className="h-3 w-3 mr-1" />
                            <span>Module {index + 1}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {/* Module Form */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle>{isCreatingModule ? 'Create New Module' : 'Edit Module'}</CardTitle>
                      <CardDescription>
                        {isCreatingModule 
                          ? 'Add a new module to your course' 
                          : 'Update your module details'}
                      </CardDescription>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={generateModuleWithAI}
                      disabled={isGeneratingModuleAI || !createdCourseId}
                      className="flex items-center gap-2"
                    >
                      {isGeneratingModuleAI ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Wand2 className="h-4 w-4" />
                      )}
                      <span>AI Generate</span>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <Form {...moduleForm}>
                    <form className="space-y-6">
                      <FormField
                        control={moduleForm.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Module Title</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter module title" {...field} />
                            </FormControl>
                            <FormDescription>
                              A descriptive title for this section of your course
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={moduleForm.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Module Description</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Describe what this module covers" 
                                className="min-h-24"
                                {...field} 
                              />
                            </FormControl>
                            <FormDescription>
                              Summarize the content and learning objectives of this module
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={moduleForm.control}
                        name="courseId"
                        render={({ field }) => (
                          <FormItem className="hidden">
                            <FormControl>
                              <Input type="hidden" {...field} value={createdCourseId || ''} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </form>
                  </Form>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={handlePreviousStep}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                  <Button onClick={handleNextStep} disabled={createModuleMutation.isPending}>
                    {createModuleMutation.isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {isCreatingModule ? 'Creating...' : 'Updating...'}
                      </>
                    ) : (
                      <>
                        Next
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          )}
          
          {/* Step 3: Lesson & Avatar Setup */}
          {step === 'lesson' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left Column - Avatar & Voice Selection */}
              <div className="lg:col-span-1 space-y-6">
                <Card className="border-primary/20 shadow-sm">
                  <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent">
                    <div className="flex items-center space-x-2">
                      <User className="h-5 w-5 text-primary" />
                      <CardTitle>Avatar Setup</CardTitle>
                    </div>
                    <CardDescription>
                      Choose your talking avatar and voice
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <Form {...lessonForm}>
                      <form className="space-y-6">
                        {/* Avatar Selection - Enhanced UI */}
                        <FormItem className="space-y-4">
                          <FormLabel className="flex items-center space-x-2">
                            <Image className="h-4 w-4 text-primary" />
                            <span>Avatar Image</span>
                          </FormLabel>
                          
                          <div className="flex flex-col items-center">
                            <AvatarSelector
                              value={lessonForm.watch('avatarFile') || lessonForm.watch('avatarUrl')}
                              onChange={handleAvatarChange}
                              className="mb-2"
                            />
                            <FormDescription className="text-center max-w-xs">
                              Select or upload an image to use as your talking avatar
                            </FormDescription>
                          </div>
                        </FormItem>
                        
                        {/* Voice Selection - Enhanced UI */}
                        <FormField
                          control={lessonForm.control}
                          name="voice"
                          render={({ field }) => (
                            <FormItem className="space-y-4">
                              <FormLabel className="flex items-center space-x-2">
                                <Volume2 className="h-4 w-4 text-primary" />
                                <span>Avatar Voice</span>
                              </FormLabel>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <FormControl>
                                  <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Select a voice" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent className="max-h-[300px]">
                                  {loadingVoices ? (
                                    <div className="flex items-center justify-center p-4">
                                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                      <span>Loading voices...</span>
                                    </div>
                                  ) : (
                                    <>
                                      <SelectGroup>
                                        <SelectLabel className="flex items-center">
                                          <UserRound className="h-4 w-4 mr-2 text-pink-500" />
                                          Female Voices
                                        </SelectLabel>
                                        {voices?.filter((voice: any) => voice.gender === 'female').map((voice: any) => (
                                          <SelectItem
                                            key={`${voice.voice_id}-${voice.source || 'default'}`}
                                            value={`${voice.voice_id}:${voice.source || 'elevenlabs'}`}
                                          >
                                            <div className="flex items-center justify-between w-full">
                                              <span>{voice.name}</span>
                                              <Badge variant="outline" className="ml-2 text-xs">
                                                {voice.source || 'ElevenLabs'}
                                              </Badge>
                                            </div>
                                          </SelectItem>
                                        ))}
                                      </SelectGroup>
                                      <SelectGroup>
                                        <SelectLabel className="flex items-center">
                                          <UserRound className="h-4 w-4 mr-2 text-blue-500" />
                                          Male Voices
                                        </SelectLabel>
                                        {voices?.filter((voice: any) => voice.gender === 'male').map((voice: any) => (
                                          <SelectItem
                                            key={`${voice.voice_id}-${voice.source || 'default'}`}
                                            value={`${voice.voice_id}:${voice.source || 'elevenlabs'}`}
                                          >
                                            <div className="flex items-center justify-between w-full">
                                              <span>{voice.name}</span>
                                              <Badge variant="outline" className="ml-2 text-xs">
                                                {voice.source || 'ElevenLabs'}
                                              </Badge>
                                            </div>
                                          </SelectItem>
                                        ))}
                                      </SelectGroup>
                                    </>
                                  )}
                                </SelectContent>
                              </Select>
                              <div className="flex justify-between">
                                <FormDescription>
                                  Choose a voice for your avatar
                                </FormDescription>
                                
                                {field.value && (
                                  <Button 
                                    variant="ghost" 
                                    size="sm" 
                                    className="h-6 px-2 text-xs"
                                    type="button"
                                    onClick={() => {
                                      const testText = "Hello! This is a preview of how I'll sound in your course.";
                                      // Test voice preview functionality would go here
                                      toast({
                                        title: "Voice Preview",
                                        description: "Voice preview feature coming soon!",
                                      });
                                    }}
                                  >
                                    <PlayCircle className="h-3.5 w-3.5 mr-1" />
                                    Preview
                                  </Button>
                                )}
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        {/* Enhanced Options */}
                        <FormField
                          control={lessonForm.control}
                          name="enhanceAudio"
                          render={({ field }) => (
                            <FormItem className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                                <FormLabel className="!mt-0">Enhance Audio Quality</FormLabel>
                              </div>
                              <FormDescription className="ml-12">
                                Applies noise reduction and audio improvements
                              </FormDescription>
                            </FormItem>
                          )}
                        />
                      </form>
                    </Form>
                  </CardContent>
                </Card>
                
                {/* Lesson Details Card */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center space-x-2">
                      <BookOpen className="h-5 w-5 text-primary" />
                      <CardTitle>Lesson Details</CardTitle>
                    </div>
                    <CardDescription>Basic information about this lesson</CardDescription>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <Form {...lessonForm}>
                      <form className="space-y-5">
                        <FormField
                          control={lessonForm.control}
                          name="title"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Lesson Title</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter lesson title" {...field} />
                              </FormControl>
                              <FormDescription>
                                A clear title for your lesson
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={lessonForm.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Lesson Description</FormLabel>
                              <FormControl>
                                <Textarea 
                                  placeholder="Describe what students will learn" 
                                  className="min-h-20"
                                  {...field} 
                                />
                              </FormControl>
                              <FormDescription>
                                Summarize the content of this lesson
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        {/* Micro Learning Settings */}
                        <div className="space-y-4 border rounded-md p-4 bg-card/50">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <SplitSquareVertical className="h-4 w-4 text-primary" />
                              <h3 className="text-sm font-medium">Micro-Learning Mode</h3>
                            </div>
                            <FormField
                              control={lessonForm.control}
                              name="microLearningEnabled"
                              render={({ field }) => (
                                <FormItem className="flex items-center space-x-2 space-y-0">
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>
                          
                          {lessonForm.watch("microLearningEnabled") && (
                            <div className="space-y-4 pl-2 animate-in fade-in-50 duration-300">
                              <FormField
                                control={lessonForm.control}
                                name="microLearningSegmentCount"
                                render={({ field }) => (
                                  <FormItem className="space-y-1">
                                    <FormLabel className="text-xs">Number of Segments</FormLabel>
                                    <div className="flex items-center space-x-2">
                                      <FormControl>
                                        <Slider
                                          value={[field.value]}
                                          min={2}
                                          max={10}
                                          step={1}
                                          onValueChange={(values: number[]) => field.onChange(values[0])}
                                          className="w-[180px]"
                                        />
                                      </FormControl>
                                      <span className="w-6 text-center text-sm">{field.value}</span>
                                    </div>
                                    <FormDescription className="text-xs">
                                      Divide the lesson into segments
                                    </FormDescription>
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={lessonForm.control}
                                name="microLearningBreakInterval"
                                render={({ field }) => (
                                  <FormItem className="space-y-1">
                                    <FormLabel className="text-xs">Break Interval (seconds)</FormLabel>
                                    <div className="flex items-center space-x-2">
                                      <FormControl>
                                        <Slider
                                          value={[field.value]}
                                          min={60}
                                          max={600}
                                          step={30}
                                          onValueChange={(values: number[]) => field.onChange(values[0])}
                                          className="w-[180px]"
                                        />
                                      </FormControl>
                                      <span className="w-16 text-center text-sm">
                                        {Math.floor(field.value / 60)}:{String(field.value % 60).padStart(2, '0')}
                                      </span>
                                    </div>
                                    <FormDescription className="text-xs">
                                      Time between breaks
                                    </FormDescription>
                                  </FormItem>
                                )}
                              />
                              
                              <FormField
                                control={lessonForm.control}
                                name="microLearningBreakDuration"
                                render={({ field }) => (
                                  <FormItem className="space-y-1">
                                    <FormLabel className="text-xs">Break Duration (seconds)</FormLabel>
                                    <div className="flex items-center space-x-2">
                                      <FormControl>
                                        <Slider
                                          value={[field.value]}
                                          min={15}
                                          max={120}
                                          step={5}
                                          onValueChange={(values: number[]) => field.onChange(values[0])}
                                          className="w-[180px]"
                                        />
                                      </FormControl>
                                      <span className="w-16 text-center text-sm">
                                        {Math.floor(field.value / 60)}:{String(field.value % 60).padStart(2, '0')}
                                      </span>
                                    </div>
                                    <FormDescription className="text-xs">
                                      How long each break lasts
                                    </FormDescription>
                                  </FormItem>
                                )}
                              />
                            </div>
                          )}
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </div>
              
              {/* Right Column - Script Editor & Preview */}
              <div className="lg:col-span-2 space-y-6">
                <Card className="border-primary/20 shadow-sm">
                  <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent pb-3">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <FileText className="h-5 w-5 text-primary" />
                        <CardTitle>Avatar Script</CardTitle>
                      </div>
                      <div className="flex items-center space-x-2">
                        {/* Removed Extract button as it's now part of the enhanced ScriptFormatter */}
                        <Button 
                          size="sm" 
                          variant="secondary" 
                          onClick={handleGenerateAIScript}
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          ) : (
                            <Wand2 className="h-4 w-4 mr-2" />
                          )}
                          Generate with AI
                        </Button>
                      </div>
                    </div>
                    <CardDescription>
                      Write or generate the script your avatar will speak in the video
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="border-b pb-2 mb-4">
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Info className="h-4 w-4" />
                        <span>Best results with 20-500 words. Use headings with # and ## for emphasis.</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Script</label>
                      <ScriptFormatter
                        script={lessonForm.getValues("script") || ""}
                        onChange={(value) => lessonForm.setValue("script", value)}
                        minHeight="300px"
                        onExtractedTextChange={(extractedText) => {
                          // Store information about the extracted text to display in the UI
                          const wordCount = extractedText.trim().split(/\s+/).length;
                          const estimatedSeconds = Math.max(1, Math.ceil(wordCount / 2.5));
                          
                          // Log the information
                          console.log(`Extracted text: ${wordCount} words, ~${estimatedSeconds} seconds`);
                        }}
                      />
                      <div className="flex justify-between items-center mt-2">
                        <div className="text-sm text-muted-foreground">
                          {lessonForm.getValues("script") ? `${lessonForm.getValues("script").trim().split(/\s+/).length} words` : "0 words"}
                        </div>
                        <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                          <span>Estimated video length:</span>
                          <Badge variant="outline" className="font-mono">
                            {lessonForm.getValues("script") ? Math.max(1, Math.ceil(lessonForm.getValues("script").trim().split(/\s+/).length / 150 * 60)) : 0} sec
                          </Badge>
                        </div>
                      </div>
                      {lessonForm.formState.errors.script && (
                        <p className="text-sm font-medium text-destructive">{lessonForm.formState.errors.script.message}</p>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t pt-4">
                    <Button variant="outline" onClick={handlePreviousStep}>
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Modules
                    </Button>
                    <Button 
                      onClick={handleNextStep} 
                      disabled={createLessonMutation.isPending || videoGenerating}
                      className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary"
                    >
                      {createLessonMutation.isPending || videoGenerating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {videoGenerating ? 'Generating Video...' : 'Saving...'}
                        </>
                      ) : (
                        <>
                          Create Avatar Video
                          <Video className="ml-2 h-4 w-4" />
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>
                
                {/* Video Generation Status - Improved UI */}
                {videoGenerating && (
                  <Card className="border-primary/20 overflow-hidden">
                    <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent pb-3">
                      <div className="flex items-center space-x-2">
                        <Clapperboard className="h-5 w-5 text-primary" />
                        <CardTitle>Creating Your Avatar Video</CardTitle>
                      </div>
                      <CardDescription>
                        Please wait while SadTalker generates your talking avatar video
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6 pt-4">
                      {/* Progress Stages */}
                      <div className="space-y-4">
                        <div className="grid grid-cols-4 gap-2">
                          {[
                            { stage: 'Preparing', icon: FileSpreadsheet, complete: videoProgress >= 10 },
                            { stage: 'Audio', icon: Mic, complete: videoProgress >= 40 },
                            { stage: 'Animation', icon: Tv, complete: videoProgress >= 75 },
                            { stage: 'Finalizing', icon: Video, complete: videoProgress >= 95 }
                          ].map((step, i) => (
                            <div key={i} className="flex flex-col items-center space-y-1">
                              <div className={`w-12 h-12 rounded-full flex items-center justify-center 
                                ${step.complete 
                                  ? 'bg-primary text-primary-foreground' 
                                  : videoProgress >= (i * 25) 
                                    ? 'bg-primary/30 text-primary' 
                                    : 'bg-muted text-muted-foreground'
                                }`}>
                                <step.icon className="h-6 w-6" />
                              </div>
                              <span className="text-xs font-medium">{step.stage}</span>
                            </div>
                          ))}
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs text-muted-foreground mb-1">
                            <span>Starting</span>
                            <span>Processing</span>
                            <span>Complete</span>
                          </div>
                          <div className="relative pt-1">
                            <div className="h-2 bg-muted rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-gradient-to-r from-primary/60 to-primary transition-all" 
                                style={{ width: `${videoProgress}%` }}
                              ></div>
                            </div>
                            <div className="flex justify-between items-center mt-1">
                              <span className="text-xs font-medium text-primary">{videoProgress}% complete</span>
                              <span className="text-xs text-muted-foreground">
                                Est. time remaining: {Math.max(0, 5 - Math.round(videoProgress / 20))} min
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {/* Animation Preview */}
                      <div className="relative rounded-md overflow-hidden border h-64 bg-muted/40">
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center space-y-3">
                            <Film className="h-12 w-12 mx-auto text-primary/70 animate-pulse" />
                            <div className="space-y-1">
                              <p className="text-sm font-medium">Animating your avatar...</p>
                              <p className="text-xs text-muted-foreground">This may take 2-5 minutes</p>
                            </div>
                            <div className="flex items-center justify-center space-x-1 mt-2">
                              <span className="w-2 h-2 rounded-full bg-primary/70 animate-bounce" style={{ animationDelay: '0ms' }}></span>
                              <span className="w-2 h-2 rounded-full bg-primary/70 animate-bounce" style={{ animationDelay: '150ms' }}></span>
                              <span className="w-2 h-2 rounded-full bg-primary/70 animate-bounce" style={{ animationDelay: '300ms' }}></span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {/* Helpful Info */}
                      <div className="bg-blue-50 border border-blue-100 rounded-md p-3 text-sm text-blue-800">
                        <div className="flex">
                          <Lightbulb className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" />
                          <div>
                            <p className="font-medium mb-1">What's happening now?</p>
                            <p className="text-xs">
                              The system is converting your text to speech, generating facial animations, 
                              and syncing the audio with the avatar image to create a seamless talking video.
                            </p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}
          
          {/* Step 4: Review and Publish */}
          {step === 'review' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Course Summary</CardTitle>
                  <CardDescription>Review your avatar-based course before publishing</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-semibold">Course Title</h3>
                    <p>{courseForm.getValues().title}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold">Description</h3>
                    <p className="text-sm">{courseForm.getValues().description}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-semibold">Category</h3>
                      <p>{courseForm.getValues().category}</p>
                    </div>
                    <div>
                      <h3 className="font-semibold">Target Audience</h3>
                      <p>{courseForm.getValues().targetAudience || 'Not specified'}</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold">Module</h3>
                    <p>{moduleForm.getValues().title}</p>
                    <p className="text-sm mt-1">{moduleForm.getValues().description}</p>
                  </div>
                  <div>
                    <h3 className="font-semibold">Lesson</h3>
                    <p>{lessonForm.getValues().title}</p>
                    <p className="text-sm mt-1">{lessonForm.getValues().description}</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={handlePreviousStep}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                  <Button onClick={handleNextStep}>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    Finish
                  </Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Avatar Video Preview</CardTitle>
                  <CardDescription>
                    The generated avatar video for your lesson
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="rounded-md overflow-hidden border">
                    {videoUrl ? (
                      <video
                        src={videoUrl}
                        controls
                        className="w-full aspect-video"
                        poster="/assets/video-poster.png"
                      />
                    ) : (
                      <div className="w-full aspect-video bg-muted flex items-center justify-center">
                        <div className="text-center">
                          <Film className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">Video not available</p>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="border rounded-md p-4 bg-muted/20">
                    <h3 className="font-semibold mb-2">What's Next?</h3>
                    <p className="text-sm">
                      After finishing this setup, you can add more lessons to your course,
                      edit existing content, or publish your course to make it available to students.
                    </p>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => {
                      if (createdCourseId) {
                        navigate(`/courses/${createdCourseId}/edit`);
                        onClose();
                      }
                    }}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Continue Editing Course
                  </Button>
                </CardFooter>
              </Card>
            </div>
          )}
        </div>
      </div>
      
      {/* AI Course Creation Assistant */}
      <Dialog open={showAIAssistant} onOpenChange={setShowAIAssistant}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Wand2 className="h-5 w-5 text-primary" />
              AI Course Assistant
            </DialogTitle>
            <DialogDescription>
              Let AI help you create a professional course quickly and easily.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="topic">What would you like to create a course about?</Label>
              <Input 
                id="topic" 
                placeholder="e.g., Digital Marketing, Leadership Skills, Python Programming" 
                value={aiTopic} 
                onChange={(e) => setAiTopic(e.target.value)}
                disabled={aiGenerating}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-3 py-2">
              {courseTopics.slice(0, 6).map((topic, index) => (
                <Button 
                  key={index} 
                  variant="outline" 
                  size="sm" 
                  className="justify-start font-normal"
                  onClick={() => setAiTopic(topic)}
                  disabled={aiGenerating}
                >
                  <div className="w-full flex items-center gap-2">
                    {index === 0 && <BookMarked className="h-4 w-4 text-blue-500" />}
                    {index === 1 && <User className="h-4 w-4 text-green-500" />}
                    {index === 2 && <PenTool className="h-4 w-4 text-purple-500" />}
                    {index === 3 && <Target className="h-4 w-4 text-red-500" />}
                    {index === 4 && <BookIcon className="h-4 w-4 text-amber-500" />}
                    {index === 5 && <Users className="h-4 w-4 text-indigo-500" />}
                    <span className="truncate">{topic}</span>
                  </div>
                </Button>
              ))}
            </div>
          </div>
          
          <DialogFooter className="sm:justify-between">
            <Button
              variant="ghost"
              onClick={() => setShowAIAssistant(false)}
              disabled={aiGenerating}
            >
              Cancel
            </Button>
            <Button 
              onClick={() => generateCourseWithAI(aiTopic)} 
              disabled={!aiTopic.trim() || aiGenerating}
              className="gap-2"
            >
              {aiGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4" />
                  <span>Generate Course</span>
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Module Count Dialog */}
      <Dialog open={showModuleCountDialog} onOpenChange={setShowModuleCountDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              Auto-Generate Modules
            </DialogTitle>
            <DialogDescription>
              Let AI create your course modules automatically based on your course details.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="moduleCount">How many modules do you want to create?</Label>
              <div className="flex items-center gap-4">
                <Input
                  id="moduleCount"
                  type="number"
                  min={1}
                  max={10}
                  value={moduleCount}
                  onChange={(e) => setModuleCount(Math.max(1, Math.min(10, parseInt(e.target.value) || 1)))}
                  className="w-24"
                  disabled={isGeneratingMultipleModules}
                />
                <div className="flex-1">
                  <p className="text-sm text-muted-foreground">
                    Recommended: 3-5 modules for most courses
                  </p>
                </div>
              </div>
            </div>
            
            <div className="bg-muted/50 p-3 rounded-md mt-2">
              <p className="text-sm flex items-start">
                <Brain className="h-4 w-4 mr-2 mt-0.5 text-primary" />
                <span>AI will analyze your course details and automatically generate {moduleCount} complete modules with titles and descriptions that fit together logically.</span>
              </p>
            </div>
          </div>
          
          <DialogFooter className="sm:justify-between">
            <Button
              variant="ghost"
              onClick={() => setShowModuleCountDialog(false)}
              disabled={isGeneratingMultipleModules}
            >
              Cancel
            </Button>
            <Button
              onClick={() => generateMultipleModules(moduleCount)}
              disabled={isGeneratingMultipleModules || !createdCourseId}
              className="gap-2"
            >
              {isGeneratingMultipleModules ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Generating {moduleCount} modules...</span>
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4" />
                  <span>Generate {moduleCount} Modules</span>
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}