import React from "react";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { FileText, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DocumentViewerProps {
  content: string;
  documentUrl?: string;
  className?: string;
  maxHeight?: string;
}

export function DocumentViewer({ 
  content, 
  documentUrl, 
  className,
  maxHeight = "400px"
}: DocumentViewerProps) {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <div className="p-4 bg-muted flex items-center justify-between">
        <div className="flex items-center">
          <FileText className="h-5 w-5 text-primary mr-2" />
          <span className="font-medium">Document Preview</span>
        </div>
        
        {documentUrl && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="gap-1"
            onClick={() => window.open(documentUrl, '_blank')}
          >
            <ExternalLink className="h-4 w-4" />
            Open
          </Button>
        )}
      </div>
      
      <div 
        className={cn(
          "p-4 prose prose-sm max-w-none overflow-y-auto",
          `max-h-[${maxHeight}]`
        )}
      >
        {documentUrl ? (
          <div className="text-center">
            <p className="mb-4">Preview not available. Click "Open" to view the document.</p>
            <Button 
              onClick={() => window.open(documentUrl, '_blank')}
              className="gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Open Document
            </Button>
          </div>
        ) : (
          <div 
            dangerouslySetInnerHTML={{ 
              __html: content.replace(/\n/g, '<br />') 
            }} 
          />
        )}
      </div>
    </Card>
  );
}