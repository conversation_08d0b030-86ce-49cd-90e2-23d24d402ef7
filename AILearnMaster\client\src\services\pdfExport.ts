import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface ProgressData {
  user: {
    username: string;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  stats: {
    totalCourses: number;
    completedCourses: number;
    totalLessons: number;
    completedLessons: number;
    totalTimeSpent: number;
    streakDays: number;
    level: number;
    totalXP: number;
    totalBadges: number;
  };
  courses: Array<{
    id: number;
    title: string;
    description: string;
    progress: number;
    completedAt?: string;
    totalLessons: number;
    completedLessons: number;
  }>;
  badges: Array<{
    id: number;
    name: string;
    description: string;
    unlockedAt: string;
    icon: string;
  }>;
  recentActivity: Array<{
    type: string;
    description: string;
    date: string;
  }>;
}

export class PDFExporter {
  private doc: jsPDF;
  private pageHeight: number;
  private pageWidth: number;
  private currentY: number;
  private margin: number;

  constructor() {
    this.doc = new jsPDF('p', 'mm', 'a4');
    this.pageHeight = this.doc.internal.pageSize.height;
    this.pageWidth = this.doc.internal.pageSize.width;
    this.currentY = 20;
    this.margin = 20;
  }

  async exportProgress(data: ProgressData): Promise<void> {
    try {
      this.addHeader(data.user);
      this.addProgressSummary(data.stats);
      this.addCourseProgress(data.courses);
      this.addBadgesSection(data.badges);
      this.addRecentActivity(data.recentActivity);
      this.addFooter();

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `learning-progress-${data.user.username}-${timestamp}.pdf`;
      
      this.doc.save(filename);
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error('Failed to generate PDF export');
    }
  }

  private addHeader(user: any): void {
    // Add title
    this.doc.setFontSize(24);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Learning Progress Report', this.margin, this.currentY);
    
    this.currentY += 15;
    
    // Add user info
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    const userName = user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}` 
      : user.username;
    this.doc.text(`Student: ${userName}`, this.margin, this.currentY);
    
    this.currentY += 8;
    this.doc.text(`Email: ${user.email}`, this.margin, this.currentY);
    
    this.currentY += 8;
    this.doc.text(`Report Generated: ${new Date().toLocaleDateString()}`, this.margin, this.currentY);
    
    this.currentY += 15;
    this.addDivider();
  }

  private addProgressSummary(stats: any): void {
    this.checkPageBreak(80);
    
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Progress Summary', this.margin, this.currentY);
    
    this.currentY += 12;
    
    // Create summary box
    const boxY = this.currentY;
    this.doc.setDrawColor(200, 200, 200);
    this.doc.rect(this.margin, boxY, this.pageWidth - (this.margin * 2), 50);
    
    this.doc.setFontSize(12);
    this.doc.setFont('helvetica', 'normal');
    
    // Left column
    const leftX = this.margin + 5;
    this.doc.text(`Total Courses: ${stats.totalCourses}`, leftX, boxY + 10);
    this.doc.text(`Completed Courses: ${stats.completedCourses}`, leftX, boxY + 18);
    this.doc.text(`Total Lessons: ${stats.totalLessons}`, leftX, boxY + 26);
    this.doc.text(`Completed Lessons: ${stats.completedLessons}`, leftX, boxY + 34);
    
    // Right column
    const rightX = this.pageWidth / 2 + 10;
    this.doc.text(`Current Level: ${stats.level}`, rightX, boxY + 10);
    this.doc.text(`Total XP: ${stats.totalXP.toLocaleString()}`, rightX, boxY + 18);
    this.doc.text(`Learning Streak: ${stats.streakDays} days`, rightX, boxY + 26);
    this.doc.text(`Badges Earned: ${stats.totalBadges}`, rightX, boxY + 34);
    
    this.currentY += 60;
  }

  private addCourseProgress(courses: any[]): void {
    this.checkPageBreak(30);
    
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Course Progress', this.margin, this.currentY);
    
    this.currentY += 12;
    
    courses.forEach((course, index) => {
      this.checkPageBreak(25);
      
      // Course title
      this.doc.setFontSize(14);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text(course.title, this.margin, this.currentY);
      
      this.currentY += 8;
      
      // Progress bar
      const barWidth = 100;
      const barHeight = 6;
      const progressWidth = (course.progress / 100) * barWidth;
      
      // Background bar
      this.doc.setFillColor(240, 240, 240);
      this.doc.rect(this.margin, this.currentY - 3, barWidth, barHeight, 'F');
      
      // Progress bar
      this.doc.setFillColor(59, 130, 246); // Blue color
      this.doc.rect(this.margin, this.currentY - 3, progressWidth, barHeight, 'F');
      
      // Progress text
      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'normal');
      this.doc.text(`${course.progress}%`, this.margin + barWidth + 10, this.currentY + 1);
      
      this.currentY += 10;
      
      // Course details
      this.doc.text(`Lessons: ${course.completedLessons}/${course.totalLessons}`, this.margin, this.currentY);
      
      if (course.completedAt) {
        this.doc.text(`Completed: ${new Date(course.completedAt).toLocaleDateString()}`, this.margin + 80, this.currentY);
      }
      
      this.currentY += 15;
    });
  }

  private addBadgesSection(badges: any[]): void {
    this.checkPageBreak(30);
    
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Achievements & Badges', this.margin, this.currentY);
    
    this.currentY += 12;
    
    if (badges.length === 0) {
      this.doc.setFontSize(12);
      this.doc.setFont('helvetica', 'italic');
      this.doc.text('No badges earned yet. Keep learning to unlock achievements!', this.margin, this.currentY);
      this.currentY += 15;
      return;
    }
    
    badges.forEach((badge, index) => {
      this.checkPageBreak(20);
      
      // Badge icon (simplified as colored circle)
      this.doc.setFillColor(255, 215, 0); // Gold color
      this.doc.circle(this.margin + 5, this.currentY - 2, 3, 'F');
      
      // Badge name
      this.doc.setFontSize(12);
      this.doc.setFont('helvetica', 'bold');
      this.doc.text(badge.name, this.margin + 15, this.currentY);
      
      this.currentY += 6;
      
      // Badge description
      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'normal');
      this.doc.text(badge.description, this.margin + 15, this.currentY);
      
      this.currentY += 5;
      
      // Unlock date
      this.doc.setFontSize(9);
      this.doc.setFont('helvetica', 'italic');
      this.doc.text(`Unlocked: ${new Date(badge.unlockedAt).toLocaleDateString()}`, this.margin + 15, this.currentY);
      
      this.currentY += 10;
    });
  }

  private addRecentActivity(activities: any[]): void {
    this.checkPageBreak(30);
    
    this.doc.setFontSize(18);
    this.doc.setFont('helvetica', 'bold');
    this.doc.text('Recent Activity', this.margin, this.currentY);
    
    this.currentY += 12;
    
    activities.slice(0, 10).forEach((activity, index) => {
      this.checkPageBreak(15);
      
      this.doc.setFontSize(10);
      this.doc.setFont('helvetica', 'normal');
      
      const date = new Date(activity.date).toLocaleDateString();
      this.doc.text(`${date}: ${activity.description}`, this.margin, this.currentY);
      
      this.currentY += 8;
    });
  }

  private addFooter(): void {
    const footerY = this.pageHeight - 15;
    
    this.doc.setFontSize(10);
    this.doc.setFont('helvetica', 'italic');
    this.doc.text('Generated by Your Learning Platform', this.margin, footerY);
    
    const pageText = `Page ${this.doc.getCurrentPageInfo().pageNumber}`;
    const textWidth = this.doc.getTextWidth(pageText);
    this.doc.text(pageText, this.pageWidth - this.margin - textWidth, footerY);
  }

  private addDivider(): void {
    this.doc.setDrawColor(200, 200, 200);
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    this.currentY += 8;
  }

  private checkPageBreak(requiredSpace: number): void {
    if (this.currentY + requiredSpace > this.pageHeight - 30) {
      this.doc.addPage();
      this.currentY = 20;
    }
  }
}

export async function exportProgressToPDF(progressData: ProgressData): Promise<void> {
  const exporter = new PDFExporter();
  await exporter.exportProgress(progressData);
}