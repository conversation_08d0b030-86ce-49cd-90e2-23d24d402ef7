#!/bin/bash

# AILearnMaster AWS Infrastructure Setup Script
# This script sets up the complete AWS infrastructure for production deployment

set -e  # Exit on any error

echo "🚀 Setting up AILearnMaster AWS Infrastructure"
echo "=============================================="

# Configuration
APP_NAME="ailearn-master-prod"
REGION="us-east-1"
DOMAIN_NAME="ailearn.com"
S3_BUCKET_STORAGE="ailearn-master-storage-prod"
S3_BUCKET_BACKUPS="ailearn-master-backups-prod"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if AWS CLI is installed and configured
check_aws_cli() {
    print_status "Checking AWS CLI configuration..."
    
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    print_success "AWS CLI is configured"
}

# Create S3 buckets
create_s3_buckets() {
    print_status "Creating S3 buckets..."
    
    # Create storage bucket
    if aws s3 ls "s3://$S3_BUCKET_STORAGE" 2>&1 | grep -q 'NoSuchBucket'; then
        aws s3 mb "s3://$S3_BUCKET_STORAGE" --region $REGION
        print_success "Created S3 bucket: $S3_BUCKET_STORAGE"
    else
        print_warning "S3 bucket $S3_BUCKET_STORAGE already exists"
    fi
    
    # Create backups bucket
    if aws s3 ls "s3://$S3_BUCKET_BACKUPS" 2>&1 | grep -q 'NoSuchBucket'; then
        aws s3 mb "s3://$S3_BUCKET_BACKUPS" --region $REGION
        print_success "Created S3 bucket: $S3_BUCKET_BACKUPS"
    else
        print_warning "S3 bucket $S3_BUCKET_BACKUPS already exists"
    fi
    
    # Configure bucket policies
    print_status "Configuring S3 bucket policies..."
    
    # Storage bucket policy
    cat > /tmp/storage-bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::$S3_BUCKET_STORAGE/public/*"
        },
        {
            "Sid": "DenyInsecureConnections",
            "Effect": "Deny",
            "Principal": "*",
            "Action": "s3:*",
            "Resource": [
                "arn:aws:s3:::$S3_BUCKET_STORAGE",
                "arn:aws:s3:::$S3_BUCKET_STORAGE/*"
            ],
            "Condition": {
                "Bool": {
                    "aws:SecureTransport": "false"
                }
            }
        }
    ]
}
EOF
    
    aws s3api put-bucket-policy --bucket $S3_BUCKET_STORAGE --policy file:///tmp/storage-bucket-policy.json
    
    # Enable versioning
    aws s3api put-bucket-versioning --bucket $S3_BUCKET_STORAGE --versioning-configuration Status=Enabled
    aws s3api put-bucket-versioning --bucket $S3_BUCKET_BACKUPS --versioning-configuration Status=Enabled
    
    # Enable encryption
    aws s3api put-bucket-encryption --bucket $S3_BUCKET_STORAGE --server-side-encryption-configuration '{
        "Rules": [
            {
                "ApplyServerSideEncryptionByDefault": {
                    "SSEAlgorithm": "AES256"
                }
            }
        ]
    }'
    
    aws s3api put-bucket-encryption --bucket $S3_BUCKET_BACKUPS --server-side-encryption-configuration '{
        "Rules": [
            {
                "ApplyServerSideEncryptionByDefault": {
                    "SSEAlgorithm": "AES256"
                }
            }
        ]
    }'
    
    print_success "S3 buckets configured with security policies"
}

# Create CloudFront distribution
create_cloudfront_distribution() {
    print_status "Creating CloudFront distribution..."
    
    # Create origin access identity
    OAI_ID=$(aws cloudfront create-cloud-front-origin-access-identity \
        --cloud-front-origin-access-identity-config \
        CallerReference=$(date +%s),Comment="AILearnMaster OAI" \
        --query 'CloudFrontOriginAccessIdentity.Id' --output text)
    
    print_success "Created CloudFront Origin Access Identity: $OAI_ID"
    
    # Create distribution configuration
    cat > /tmp/cloudfront-config.json << EOF
{
    "CallerReference": "$(date +%s)",
    "Comment": "AILearnMaster CDN",
    "DefaultCacheBehavior": {
        "TargetOriginId": "S3-$S3_BUCKET_STORAGE",
        "ViewerProtocolPolicy": "redirect-to-https",
        "MinTTL": 0,
        "ForwardedValues": {
            "QueryString": false,
            "Cookies": {
                "Forward": "none"
            }
        },
        "TrustedSigners": {
            "Enabled": false,
            "Quantity": 0
        },
        "Compress": true
    },
    "Origins": {
        "Quantity": 1,
        "Items": [
            {
                "Id": "S3-$S3_BUCKET_STORAGE",
                "DomainName": "$S3_BUCKET_STORAGE.s3.amazonaws.com",
                "S3OriginConfig": {
                    "OriginAccessIdentity": "origin-access-identity/cloudfront/$OAI_ID"
                }
            }
        ]
    },
    "Enabled": true,
    "PriceClass": "PriceClass_100"
}
EOF
    
    DISTRIBUTION_ID=$(aws cloudfront create-distribution \
        --distribution-config file:///tmp/cloudfront-config.json \
        --query 'Distribution.Id' --output text)
    
    print_success "Created CloudFront distribution: $DISTRIBUTION_ID"
    echo "Distribution Domain: $(aws cloudfront get-distribution --id $DISTRIBUTION_ID --query 'Distribution.DomainName' --output text)"
}

# Create IAM roles and policies
create_iam_roles() {
    print_status "Creating IAM roles and policies..."
    
    # Create Amplify service role
    cat > /tmp/amplify-trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "amplify.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF
    
    aws iam create-role \
        --role-name AILearnMaster-AmplifyRole \
        --assume-role-policy-document file:///tmp/amplify-trust-policy.json \
        --description "Service role for AILearnMaster Amplify app" || true
    
    aws iam attach-role-policy \
        --role-name AILearnMaster-AmplifyRole \
        --policy-arn arn:aws:iam::aws:policy/AdministratorAccess-Amplify || true
    
    print_success "Created IAM roles for Amplify"
}

# Store secrets in AWS Secrets Manager
create_secrets() {
    print_status "Creating secrets in AWS Secrets Manager..."
    
    # Note: These are placeholder values - replace with actual secrets
    print_warning "Creating placeholder secrets - REPLACE WITH ACTUAL VALUES!"
    
    # Database secrets
    aws secretsmanager create-secret \
        --name "ailearn-master/production/database" \
        --description "Production database credentials" \
        --secret-string '{"DATABASE_URL":"postgresql://username:password@host:port/database?sslmode=require"}' \
        --region $REGION || true
    
    # Session secrets
    aws secretsmanager create-secret \
        --name "ailearn-master/production/session" \
        --description "Session and JWT secrets" \
        --secret-string '{"SESSION_SECRET":"REPLACE-WITH-32-CHAR-SECRET","JWT_SECRET":"REPLACE-WITH-32-CHAR-SECRET","SECRETS_ENCRYPTION_KEY":"REPLACE-WITH-32-CHAR-KEY"}' \
        --region $REGION || true
    
    # AI service secrets
    aws secretsmanager create-secret \
        --name "ailearn-master/production/ai-services" \
        --description "AI service API keys" \
        --secret-string '{"OPENAI_API_KEY":"REPLACE-WITH-OPENAI-KEY","MODAL_TOKEN_ID":"REPLACE-WITH-MODAL-ID","MODAL_TOKEN_SECRET":"REPLACE-WITH-MODAL-SECRET"}' \
        --region $REGION || true
    
    print_success "Created secrets in AWS Secrets Manager"
    print_warning "Remember to update the secret values with actual credentials!"
}

# Create Amplify app
create_amplify_app() {
    print_status "Creating AWS Amplify application..."
    
    # Create Amplify app
    APP_ID=$(aws amplify create-app \
        --name $APP_NAME \
        --description "AILearnMaster Production Application" \
        --repository "https://github.com/hajhasni1984/AILearnMaster" \
        --platform WEB \
        --iam-service-role-arn "arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):role/AILearnMaster-AmplifyRole" \
        --environment-variables NODE_ENV=production,AMPLIFY_DIFF_DEPLOY=false \
        --query 'app.appId' --output text)
    
    print_success "Created Amplify app: $APP_ID"
    
    # Create main branch
    aws amplify create-branch \
        --app-id $APP_ID \
        --branch-name main \
        --description "Production branch" \
        --enable-auto-build \
        --environment-variables NODE_ENV=production
    
    print_success "Created main branch for Amplify app"
    
    echo "Amplify App URL: https://main.$APP_ID.amplifyapp.com"
}

# Main execution
main() {
    print_status "Starting AWS infrastructure setup for AILearnMaster..."
    
    check_aws_cli
    create_s3_buckets
    create_cloudfront_distribution
    create_iam_roles
    create_secrets
    create_amplify_app
    
    print_success "AWS infrastructure setup completed!"
    
    echo ""
    echo "🎉 Infrastructure Setup Complete!"
    echo "================================="
    echo "Next steps:"
    echo "1. Update secrets in AWS Secrets Manager with actual values"
    echo "2. Configure custom domain in Amplify console"
    echo "3. Connect GitHub repository to Amplify app"
    echo "4. Configure environment variables in Amplify"
    echo "5. Deploy the application"
    echo ""
    echo "Resources created:"
    echo "- S3 Buckets: $S3_BUCKET_STORAGE, $S3_BUCKET_BACKUPS"
    echo "- CloudFront Distribution: $DISTRIBUTION_ID"
    echo "- Amplify App: $APP_ID"
    echo "- IAM Roles: AILearnMaster-AmplifyRole"
    echo "- Secrets Manager: ailearn-master/production/*"
}

# Run main function
main "$@"
