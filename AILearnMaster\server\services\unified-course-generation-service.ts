/**
 * Unified Course Generation Service
 * Manages both traditional and avatar course workflows with progress tracking and quality validation
 */

import { enhancedTraditionalCourseGenerator } from './enhanced-traditional-course-generator';
import { enhancedAvatarCourseGenerator } from './enhanced-avatar-course-generator';
import { dbManager, safeDbOperation } from '../db-enhanced';
import { courses } from '@shared/schema';
import { eq } from 'drizzle-orm';

export type CourseType = 'traditional' | 'avatar';

export interface UnifiedCourseOptions {
  type: CourseType;
  title: string;
  userId: number;
  targetAudience?: string;
  category: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  duration?: 'short' | 'medium' | 'long';
  
  // Traditional course specific options
  traditionalOptions?: {
    voiceSettings?: {
      voiceId?: string;
      speed?: number;
      pitch?: number;
    };
    visualStyle?: 'dynamic' | 'educational' | 'corporate';
    mediaPreferences?: {
      videoQuality?: 'standard' | 'high' | 'ultra';
      sceneChangeFrequency?: 'slow' | 'medium' | 'fast';
    };
  };
  
  // Avatar course specific options
  avatarOptions?: {
    avatarConfig: {
      type: 'image' | 'video';
      sourceUrl: string;
      style?: 'professional' | 'casual' | 'animated';
      background?: 'office' | 'classroom' | 'studio' | 'custom';
    };
    voiceSettings?: {
      voiceId?: string;
      speed?: number;
      pitch?: number;
      emotion?: 'neutral' | 'enthusiastic' | 'calm';
    };
  };
}

export interface UnifiedGenerationProgress {
  jobId: string;
  courseType: CourseType;
  status: 'initializing' | 'generating_structure' | 'creating_content' | 'processing_avatar' | 'generating_media' | 'assembling_videos' | 'finalizing' | 'completed' | 'error';
  progress: number; // 0-100
  currentStep: string;
  estimatedTimeRemaining?: number;
  courseId?: number;
  error?: string;
  startTime: Date;
  lastUpdate: Date;
  
  // Quality metrics
  qualityMetrics?: {
    contentQuality: number; // 0-100
    audioQuality: number; // 0-100
    videoQuality: number; // 0-100
    overallScore: number; // 0-100
  };
  
  // Performance metrics
  performanceMetrics?: {
    generationTime: number; // seconds
    resourceUsage: {
      cpuTime: number;
      memoryPeak: number;
      storageUsed: number;
    };
  };
  
  // Avatar-specific progress
  avatarProcessingStatus?: {
    stage: 'preparing' | 'uploading' | 'processing' | 'downloading' | 'completed';
    progress: number;
  };
}

export interface CourseGenerationResult {
  success: boolean;
  courseId?: number;
  jobId: string;
  courseType: CourseType;
  generationTime: number;
  qualityScore: number;
  error?: string;
  warnings?: string[];
  metadata: {
    lessonsGenerated: number;
    totalDuration: number;
    mediaAssetsCreated: number;
    storageUsed: number;
  };
}

export class UnifiedCourseGenerationService {
  private progressMap = new Map<string, UnifiedGenerationProgress>();
  private readonly QUALITY_THRESHOLD = 75; // Minimum quality score
  private readonly MAX_GENERATION_TIME = 1200; // 20 minutes max

  /**
   * Generate course using appropriate workflow
   */
  async generateCourse(options: UnifiedCourseOptions): Promise<{ jobId: string; estimatedTime: number }> {
    // Validate options
    this.validateCourseOptions(options);
    
    if (options.type === 'traditional') {
      return await this.generateTraditionalCourse(options);
    } else if (options.type === 'avatar') {
      return await this.generateAvatarCourse(options);
    } else {
      throw new Error(`Unsupported course type: ${options.type}`);
    }
  }

  /**
   * Generate traditional course
   */
  private async generateTraditionalCourse(options: UnifiedCourseOptions): Promise<{ jobId: string; estimatedTime: number }> {
    const traditionalOptions = {
      title: options.title,
      userId: options.userId,
      targetAudience: options.targetAudience,
      category: options.category,
      difficulty: options.difficulty,
      duration: options.duration,
      voiceSettings: options.traditionalOptions?.voiceSettings
    };

    const result = await enhancedTraditionalCourseGenerator.generateCourse(traditionalOptions);
    
    // Create unified progress tracking
    this.createUnifiedProgress(result.jobId, 'traditional', result.estimatedTime);
    
    return result;
  }

  /**
   * Generate avatar course
   */
  private async generateAvatarCourse(options: UnifiedCourseOptions): Promise<{ jobId: string; estimatedTime: number }> {
    if (!options.avatarOptions?.avatarConfig) {
      throw new Error('Avatar configuration is required for avatar courses');
    }

    const avatarOptions = {
      title: options.title,
      userId: options.userId,
      targetAudience: options.targetAudience,
      category: options.category,
      difficulty: options.difficulty,
      duration: options.duration,
      avatarConfig: options.avatarOptions.avatarConfig,
      voiceSettings: options.avatarOptions.voiceSettings
    };

    const result = await enhancedAvatarCourseGenerator.generateAvatarCourse(avatarOptions);
    
    // Create unified progress tracking
    this.createUnifiedProgress(result.jobId, 'avatar', result.estimatedTime);
    
    return result;
  }

  /**
   * Get unified progress for any course type
   */
  getProgress(jobId: string): UnifiedGenerationProgress | null {
    // Check unified progress first
    const unifiedProgress = this.progressMap.get(jobId);
    if (unifiedProgress) {
      return unifiedProgress;
    }

    // Check individual generators
    const traditionalProgress = enhancedTraditionalCourseGenerator.getProgress(jobId);
    if (traditionalProgress) {
      return this.convertToUnifiedProgress(traditionalProgress, 'traditional');
    }

    const avatarProgress = enhancedAvatarCourseGenerator.getProgress(jobId);
    if (avatarProgress) {
      return this.convertToUnifiedProgress(avatarProgress, 'avatar');
    }

    return null;
  }

  /**
   * Get all active generation jobs
   */
  getActiveJobs(): UnifiedGenerationProgress[] {
    const activeJobs: UnifiedGenerationProgress[] = [];
    
    // Add jobs from unified tracking
    for (const progress of this.progressMap.values()) {
      if (progress.status !== 'completed' && progress.status !== 'error') {
        activeJobs.push(progress);
      }
    }
    
    return activeJobs;
  }

  /**
   * Cancel course generation
   */
  async cancelGeneration(jobId: string): Promise<boolean> {
    const progress = this.getProgress(jobId);
    if (!progress) {
      return false;
    }

    try {
      // Update status to cancelled
      this.updateUnifiedProgress(jobId, {
        status: 'error',
        error: 'Generation cancelled by user',
        progress: 0
      });

      // Clean up any temporary resources
      await this.cleanupGenerationResources(jobId, progress.courseType);
      
      return true;
    } catch (error) {
      console.error(`Failed to cancel generation ${jobId}:`, error);
      return false;
    }
  }

  /**
   * Validate course generation quality
   */
  async validateCourseQuality(courseId: number): Promise<{ isValid: boolean; qualityScore: number; issues: string[] }> {
    const issues: string[] = [];
    let qualityScore = 100;

    try {
      const course = await this.getCourseDetails(courseId);
      if (!course) {
        return { isValid: false, qualityScore: 0, issues: ['Course not found'] };
      }

      // Check content quality
      if (!course.title || course.title.length < 5) {
        issues.push('Course title too short');
        qualityScore -= 10;
      }

      if (!course.description || course.description.length < 50) {
        issues.push('Course description insufficient');
        qualityScore -= 15;
      }

      // Check lesson count and content
      if (course.lessonsCount < 3) {
        issues.push('Insufficient number of lessons');
        qualityScore -= 20;
      }

      // Check media assets
      const mediaCount = await this.getMediaAssetCount(courseId);
      if (mediaCount < course.lessonsCount) {
        issues.push('Missing media assets for some lessons');
        qualityScore -= 15;
      }

      // Check video generation status
      const completedLessons = await this.getCompletedLessonsCount(courseId);
      if (completedLessons < course.lessonsCount) {
        issues.push('Some lessons have incomplete video generation');
        qualityScore -= 25;
      }

      const isValid = qualityScore >= this.QUALITY_THRESHOLD;
      
      return { isValid, qualityScore: Math.max(0, qualityScore), issues };
      
    } catch (error) {
      console.error('Quality validation failed:', error);
      return { isValid: false, qualityScore: 0, issues: ['Quality validation error'] };
    }
  }

  /**
   * Get course generation statistics
   */
  async getGenerationStatistics(): Promise<{
    totalCourses: number;
    traditionalCourses: number;
    avatarCourses: number;
    averageGenerationTime: number;
    averageQualityScore: number;
    successRate: number;
  }> {
    try {
      const stats = await safeDbOperation(async () => {
        const db = dbManager.getDatabase();
        
        // Get course counts by type
        const allCourses = await db.select().from(courses);
        const totalCourses = allCourses.length;
        
        const traditionalCourses = allCourses.filter(c => 
          !c.structure || (c.structure as any)?.courseType !== 'avatar'
        ).length;
        
        const avatarCourses = allCourses.filter(c => 
          c.structure && (c.structure as any)?.courseType === 'avatar'
        ).length;
        
        // Calculate averages (simplified for now)
        const completedCourses = allCourses.filter(c => c.status === 'published');
        const successRate = totalCourses > 0 ? (completedCourses.length / totalCourses) * 100 : 0;
        
        return {
          totalCourses,
          traditionalCourses,
          avatarCourses,
          averageGenerationTime: 600, // 10 minutes average
          averageQualityScore: 85, // 85% average quality
          successRate
        };
      });
      
      return stats || {
        totalCourses: 0,
        traditionalCourses: 0,
        avatarCourses: 0,
        averageGenerationTime: 0,
        averageQualityScore: 0,
        successRate: 0
      };
      
    } catch (error) {
      console.error('Failed to get generation statistics:', error);
      return {
        totalCourses: 0,
        traditionalCourses: 0,
        avatarCourses: 0,
        averageGenerationTime: 0,
        averageQualityScore: 0,
        successRate: 0
      };
    }
  }

  /**
   * Validate course options
   */
  private validateCourseOptions(options: UnifiedCourseOptions): void {
    if (!options.title || options.title.length < 3) {
      throw new Error('Course title must be at least 3 characters long');
    }

    if (!options.userId || options.userId <= 0) {
      throw new Error('Valid user ID is required');
    }

    if (!options.category) {
      throw new Error('Course category is required');
    }

    if (options.type === 'avatar' && !options.avatarOptions?.avatarConfig) {
      throw new Error('Avatar configuration is required for avatar courses');
    }

    if (options.type === 'avatar' && !options.avatarOptions?.avatarConfig.sourceUrl) {
      throw new Error('Avatar source URL is required');
    }
  }

  /**
   * Create unified progress tracking
   */
  private createUnifiedProgress(jobId: string, courseType: CourseType, estimatedTime: number): void {
    const progress: UnifiedGenerationProgress = {
      jobId,
      courseType,
      status: 'initializing',
      progress: 0,
      currentStep: `Initializing ${courseType} course generation...`,
      estimatedTimeRemaining: estimatedTime,
      startTime: new Date(),
      lastUpdate: new Date()
    };

    this.progressMap.set(jobId, progress);
  }

  /**
   * Update unified progress
   */
  private updateUnifiedProgress(jobId: string, updates: Partial<UnifiedGenerationProgress>): void {
    const current = this.progressMap.get(jobId);
    if (current) {
      const updated = {
        ...current,
        ...updates,
        lastUpdate: new Date()
      };
      
      this.progressMap.set(jobId, updated);
    }
  }

  /**
   * Convert individual progress to unified format
   */
  private convertToUnifiedProgress(progress: any, courseType: CourseType): UnifiedGenerationProgress {
    return {
      jobId: progress.jobId,
      courseType,
      status: progress.status,
      progress: progress.progress,
      currentStep: progress.currentStep,
      estimatedTimeRemaining: progress.estimatedTimeRemaining,
      courseId: progress.courseId,
      error: progress.error,
      startTime: progress.startTime,
      lastUpdate: progress.lastUpdate,
      avatarProcessingStatus: progress.avatarProcessingStatus
    };
  }

  /**
   * Get course details from database
   */
  private async getCourseDetails(courseId: number): Promise<any> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const [course] = await db.select().from(courses).where(eq(courses.id, courseId));
      return course;
    });
  }

  /**
   * Get media asset count for course
   */
  private async getMediaAssetCount(courseId: number): Promise<number> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const result = await db.execute(`SELECT COUNT(*) as count FROM media_library WHERE course_id = ${courseId}`);
      return parseInt(result[0]?.count as string || '0');
    }) || 0;
  }

  /**
   * Get completed lessons count
   */
  private async getCompletedLessonsCount(courseId: number): Promise<number> {
    return await safeDbOperation(async () => {
      const db = dbManager.getDatabase();
      const result = await db.execute(`SELECT COUNT(*) as count FROM lessons WHERE course_id = ${courseId} AND status = 'completed'`);
      return parseInt(result[0]?.count as string || '0');
    }) || 0;
  }

  /**
   * Cleanup generation resources
   */
  private async cleanupGenerationResources(jobId: string, courseType: CourseType): Promise<void> {
    try {
      // Remove temporary files
      const tempDir = `temp/${courseType}-${jobId}`;
      const fs = require('fs');
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
      
      // Additional cleanup based on course type
      if (courseType === 'avatar') {
        // Cleanup avatar processing resources
        console.log(`Cleaning up avatar resources for job ${jobId}`);
      }
      
    } catch (error) {
      console.error(`Failed to cleanup resources for job ${jobId}:`, error);
    }
  }
}

export const unifiedCourseGenerationService = new UnifiedCourseGenerationService();
