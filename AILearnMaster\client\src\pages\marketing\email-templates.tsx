import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { PaginationButton } from '@/components/ui/pagination-button';
import { useMediaQuery } from '@/hooks/use-media-query';
import {
  Eye,
  Edit,
  FileText,
  MoreVertical,
  Plus,
  Search,
  Trash2,
} from 'lucide-react';

const EmailTemplates = () => {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState<any>(null);
  const [previewTemplate, setPreviewTemplate] = useState<any>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { isDesktop } = useMediaQuery();

  // Form schema for creating/editing templates
  const templateSchema = z.object({
    name: z.string().min(1, { message: "Template name is required" }),
    description: z.string().optional(),
    subject: z.string().min(1, { message: "Email subject is required" }),
    content: z.string().min(1, { message: "Email content is required" }),
    htmlContent: z.string().min(1, { message: "HTML content is required" }),
    category: z.string().default("general"),
    tags: z.array(z.string()).optional(),
    isDefault: z.boolean().default(false),
  });

  // Create form
  const templateForm = useForm<z.infer<typeof templateSchema>>({
    resolver: zodResolver(templateSchema),
    defaultValues: {
      name: "",
      description: "",
      subject: "",
      content: "",
      htmlContent: "",
      category: "general",
      tags: [],
      isDefault: false,
    },
  });

  // Fetch templates with pagination and filtering
  const { data: templatesData, isLoading: templatesLoading } = useQuery({
    queryKey: ['/api/email-templates', page, limit, categoryFilter, searchQuery],
    queryFn: async () => {
      let url = `/api/email-templates?page=${page}&limit=${limit}`;
      if (categoryFilter) url += `&category=${categoryFilter}`;
      if (searchQuery) url += `&search=${searchQuery}`;
      const res = await apiRequest('GET', url);
      return res.json();
    },
  });

  // Create template mutation
  const createTemplateMutation = useMutation({
    mutationFn: async (values: z.infer<typeof templateSchema>) => {
      const res = await apiRequest('POST', '/api/email-templates', values);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-templates'] });
      setTemplateDialogOpen(false);
      templateForm.reset();
      toast({
        title: 'Template created',
        description: 'Email template has been created successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to create template: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Update template mutation
  const updateTemplateMutation = useMutation({
    mutationFn: async (values: z.infer<typeof templateSchema> & { id: number }) => {
      const { id, ...data } = values;
      const res = await apiRequest('PUT', `/api/email-templates/${id}`, data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-templates'] });
      setTemplateDialogOpen(false);
      setCurrentTemplate(null);
      templateForm.reset();
      toast({
        title: 'Template updated',
        description: 'Email template has been updated successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to update template: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Delete template mutation
  const deleteTemplateMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest('DELETE', `/api/email-templates/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/email-templates'] });
      toast({
        title: 'Template deleted',
        description: 'Email template has been deleted successfully.',
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: `Failed to delete template: ${error.message}`,
        variant: 'destructive',
      });
    },
  });

  // Handle opening the dialog for editing
  const handleEditTemplate = (template: any) => {
    setCurrentTemplate(template);
    templateForm.reset({
      name: template.name,
      description: template.description || "",
      subject: template.subject,
      content: template.content,
      htmlContent: template.htmlContent,
      category: template.category || "general",
      tags: template.tags || [],
      isDefault: template.isDefault || false,
    });
    setTemplateDialogOpen(true);
  };

  // Handle opening the dialog for creating
  const handleCreateTemplate = () => {
    setCurrentTemplate(null);
    templateForm.reset({
      name: "",
      description: "",
      subject: "",
      content: "",
      htmlContent: "",
      category: "general",
      tags: [],
      isDefault: false,
    });
    setTemplateDialogOpen(true);
  };

  // Handle form submission
  const onSubmit = (values: z.infer<typeof templateSchema>) => {
    if (currentTemplate) {
      updateTemplateMutation.mutate({ ...values, id: currentTemplate.id });
    } else {
      createTemplateMutation.mutate(values);
    }
  };

  // Render template cards or empty state
  const renderTemplates = () => {
    if (templatesLoading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <Card key={index} className="flex flex-col h-64">
              <CardHeader className="pb-2">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2 mt-2" />
              </CardHeader>
              <CardContent className="flex-grow">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full mt-2" />
                <Skeleton className="h-4 w-2/3 mt-2" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-9 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      );
    }

    if (!templatesData?.data?.length) {
      return (
        <Card className="flex flex-col items-center justify-center py-12">
          <FileText className="h-16 w-16 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No Email Templates Yet</h3>
          <p className="text-muted-foreground text-center max-w-md mt-2">
            Create your first email template to get started with your email marketing campaigns.
          </p>
          <Button className="mt-4" onClick={handleCreateTemplate}>
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </Card>
      );
    }

    return (
      <>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {templatesData.data.map((template: any) => (
            <Card key={template.id} className="flex flex-col h-64">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    {template.category && (
                      <Badge variant="outline" className="mt-1">
                        {template.category}
                      </Badge>
                    )}
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setPreviewTemplate(template)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Preview
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditTemplate(template)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="text-destructive focus:text-destructive"
                        onClick={() => {
                          if (window.confirm("Are you sure you want to delete this template?")) {
                            deleteTemplateMutation.mutate(template.id);
                          }
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                {template.description && (
                  <CardDescription className="line-clamp-2">{template.description}</CardDescription>
                )}
              </CardHeader>
              <CardContent className="flex-grow overflow-hidden">
                <div className="text-sm text-muted-foreground mb-2">
                  <strong>Subject:</strong> {template.subject}
                </div>
                <div className="text-sm text-muted-foreground line-clamp-3">
                  {template.content}
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full"
                  onClick={() => handleEditTemplate(template)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Template
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        {templatesData.pagination.totalPages > 1 && (
          <div className="flex justify-center mt-8">
            <PaginationButton
              currentPage={page}
              totalPages={templatesData.pagination.totalPages}
              onPageChange={setPage}
            />
          </div>
        )}
      </>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Email Templates</h2>
          <p className="text-muted-foreground">Create and manage your email templates</p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button onClick={handleCreateTemplate}>
            <Plus className="h-4 w-4 mr-2" />
            New Template
          </Button>
        </div>
      </div>

      {/* Search and filter */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-grow">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Select
          value={categoryFilter || "all"}
          onValueChange={(value) => setCategoryFilter(value === "all" ? null : value)}
        >
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {templatesData?.categories?.map((category: string) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      {renderTemplates()}

      {/* Template Create/Edit Dialog */}
      <Dialog open={templateDialogOpen} onOpenChange={setTemplateDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{currentTemplate ? "Edit Template" : "Create Template"}</DialogTitle>
            <DialogDescription>
              {currentTemplate
                ? "Update your email template details"
                : "Fill in the details to create a new email template"}
            </DialogDescription>
          </DialogHeader>

          <Form {...templateForm}>
            <form onSubmit={templateForm.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={templateForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Welcome Email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={templateForm.control}
                  name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select value={field.value} onValueChange={field.onChange}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="general">General</SelectItem>
                          <SelectItem value="newsletter">Newsletter</SelectItem>
                          <SelectItem value="promotional">Promotional</SelectItem>
                          <SelectItem value="transactional">Transactional</SelectItem>
                          <SelectItem value="announcement">Announcement</SelectItem>
                          <SelectItem value="welcome">Welcome</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={templateForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="A brief description of the template"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={templateForm.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Subject</FormLabel>
                    <FormControl>
                      <Input placeholder="Welcome to our platform!" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={templateForm.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Plain Text Content</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter the plain text version of your email content..."
                          className="h-40 resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Plain text version for email clients that don't support HTML.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={templateForm.control}
                  name="htmlContent"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>HTML Content</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="<h1>Welcome!</h1><p>Thank you for subscribing...</p>"
                          className="h-40 resize-none font-mono text-sm"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        HTML version for rich email content. Use HTML tags to format your email.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={templateForm.control}
                name="isDefault"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="cursor-pointer">Set as Default Template</FormLabel>
                      <FormDescription>
                        Use this as the default template for new campaigns
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button 
                  type="submit" 
                  disabled={
                    createTemplateMutation.isPending || 
                    updateTemplateMutation.isPending
                  }
                >
                  {createTemplateMutation.isPending || updateTemplateMutation.isPending
                    ? "Saving..."
                    : currentTemplate
                    ? "Update Template"
                    : "Create Template"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Template Preview Dialog */}
      <Dialog open={!!previewTemplate} onOpenChange={() => setPreviewTemplate(null)}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Template Preview: {previewTemplate?.name}</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Email Subject</CardTitle>
              </CardHeader>
              <CardContent>
                <p>{previewTemplate?.subject}</p>
              </CardContent>
            </Card>
            
            <Tabs defaultValue="html">
              <TabsList className="mb-4">
                <TabsTrigger value="html">HTML Content</TabsTrigger>
                <TabsTrigger value="text">Plain Text</TabsTrigger>
                <TabsTrigger value="rendered">Rendered Email</TabsTrigger>
              </TabsList>
              
              <TabsContent value="html">
                <Card>
                  <CardHeader>
                    <CardTitle>HTML Content</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-secondary p-4 rounded-md overflow-x-auto">
                      <code>{previewTemplate?.htmlContent}</code>
                    </pre>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="text">
                <Card>
                  <CardHeader>
                    <CardTitle>Plain Text Content</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <pre className="bg-secondary p-4 rounded-md whitespace-pre-wrap">
                      {previewTemplate?.content}
                    </pre>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="rendered">
                <Card>
                  <CardHeader>
                    <CardTitle>Rendered Email Preview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="border rounded-md p-4">
                      {/* Security note: Using dangerouslySetInnerHTML only for preview purposes */}
                      <div 
                        className="email-preview" 
                        dangerouslySetInnerHTML={{ __html: previewTemplate?.htmlContent || "" }} 
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
          
          <DialogFooter>
            <Button 
              onClick={() => handleEditTemplate(previewTemplate)}
              variant="outline"
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button onClick={() => setPreviewTemplate(null)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EmailTemplates;