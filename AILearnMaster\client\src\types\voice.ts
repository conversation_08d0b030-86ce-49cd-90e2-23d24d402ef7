export interface VoiceModel {
  id: string;
  name: string;
  language: string;
  gender?: 'male' | 'female' | 'mixed' | 'unknown';
  style?: string;
  quality: 'high' | 'medium' | 'basic';
  description: string;
  category: 'single-speaker' | 'multi-speaker' | 'multilingual';
  dataset?: string;
  architecture?: string;
  service: 'coqui' | 'elevenlabs' | 'openai' | 'chatterbox' | 'kokoro';
  tier?: 'free' | 'premium' | 'enterprise';
  tags?: string[];
  preview_url?: string;
  accent?: string;
  isAvailable?: boolean;
  testResult?: {
    available: boolean;
    error?: string;
    lastTested?: Date;
  };
}

export interface VoiceSettings {
  provider: 'coqui' | 'kokoro' | 'elevenlabs' | 'openai' | 'chatterbox';
  voiceId: string;
  speed: number;
  pitch: number;
  volume: number;
  stability?: number;
  similarity?: number;
  emotion?: string;
  temperature?: number;
}

export interface VoiceGenerationRequest {
  text: string;
  voice: string;
  speed?: number;
  service?: string;
  quality?: string;
  moduleId?: string;
  lessonId?: string;
  moduleTitle?: string;
  lessonTitle?: string;
  format?: 'mp3' | 'wav';
  title?: string;
}

export interface VoiceGenerationResponse {
  audioUrl?: string;
  duration?: number;
  wordCount?: number;
  voiceId?: string;
  format?: string;
  error?: string;
  success?: boolean;
}

export interface VoiceProject {
  id: string;
  moduleId: string;
  lessonId: string;
  moduleTitle: string;
  lessonTitle: string;
  script: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  audioUrl?: string;
  progress: number;
  voiceSettings?: VoiceSettings;
  error?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface VoiceService {
  id: string;
  name: string;
  description: string;
  icon?: React.ComponentType;
  status: 'available' | 'unavailable' | 'limited';
  voices: VoiceModel[];
  features: string[];
  pricing?: {
    model: 'free' | 'pay-per-use' | 'subscription';
    cost?: string;
    limits?: string;
  };
  requirements?: {
    apiKey?: boolean;
    installation?: boolean;
    gpu?: boolean;
  };
}

export interface VoiceDiscoveryResult {
  total: number;
  voices: VoiceModel[];
  services: VoiceService[];
  stats: {
    byLanguage: { [key: string]: number };
    byCategory: { [key: string]: number };
    byQuality: { [key: string]: number };
    byService: { [key: string]: number };
  };
  lastUpdated: Date;
  errors?: string[];
}

export interface VoiceTestResult {
  voiceId: string;
  available: boolean;
  error?: string;
  responseTime?: number;
  audioQuality?: 'excellent' | 'good' | 'fair' | 'poor';
  testText?: string;
  testAudioUrl?: string;
  testedAt: Date;
}

export interface VoiceFilter {
  language?: string[];
  gender?: string[];
  category?: string[];
  quality?: string[];
  service?: string[];
  tier?: string[];
  search?: string;
}

export interface VoicePreviewSettings {
  text: string;
  autoPlay: boolean;
  showWaveform: boolean;
  volume: number;
}

export interface VoiceSynthesisOptions {
  text: string;
  voiceId: string;
  service: string;
  settings: {
    speed?: number;
    pitch?: number;
    volume?: number;
    stability?: number;
    similarity?: number;
    emotion?: string;
    temperature?: number;
    pauseLength?: number;
    emphasisStrength?: number;
    breathingPauses?: boolean;
  };
  format?: 'mp3' | 'wav' | 'ogg';
  quality?: 'standard' | 'high' | 'ultra';
  metadata?: {
    title?: string;
    moduleId?: string;
    lessonId?: string;
    courseId?: string;
    userId?: string;
  };
}

// Legacy interfaces for backward compatibility
export interface Voice {
  id: string;
  name: string;
  gender?: string;
  accent?: string;
  language?: string;
  description?: string;
  preview_url?: string;
  category?: string;
  provider: 'chatterbox' | 'openai' | 'elevenlabs' | 'coqui';
  tier: 'free' | 'premium' | 'enterprise';
  tags?: string[];
}

export interface VoicePreviewSystemProps {
  selectedVoice: string;
  onVoiceSelect: (voiceId: string, provider: string) => void;
  previewText?: string;
  onVoiceSettingsChange?: (settings: any) => void;
}
