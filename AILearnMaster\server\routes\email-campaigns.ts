import { Router } from "express";
import { db } from "../db";
import { eq, and, like, sql, asc, desc } from "drizzle-orm";
import { emailCampaigns, subscriberLists, subscribers, emailTemplates, insertEmailCampaignSchema } from "@shared/schema";
import { Resend } from "resend";
import { authenticate } from "../middleware/auth";
import { isCustomDomain, sanitizeHtml } from "../utils";

const router = Router();

if (!process.env.RESEND_API_KEY) {
  console.warn("RESEND_API_KEY is not set. Email sending functionality will be limited.");
}

const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

// Create an email campaign
router.post("/", authenticate, async (req, res) => {
  try {
    const userId = req.user!.id;
    const campaignData = await insertEmailCampaignSchema.parse({
      ...req.body,
      userId,
    });

    const [campaign] = await db.insert(emailCampaigns).values(campaignData).returning();
    res.status(201).json(campaign);
  } catch (error) {
    console.error("Error creating email campaign:", error);
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unexpected error occurred" });
    }
  }
});

// Get all campaigns for the user
router.get("/", authenticate, async (req, res) => {
  try {
    const userId = req.user!.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search as string;
    const status = req.query.status as string;
    const sortBy = req.query.sortBy as string || "createdAt";
    const sortOrder = req.query.sortOrder as string || "desc";
    
    let query = db.select()
      .from(emailCampaigns)
      .where(eq(emailCampaigns.userId, userId))
      .limit(limit)
      .offset(offset);

    if (search) {
      query = query.where(like(emailCampaigns.name, `%${search}%`));
    }

    if (status) {
      query = query.where(eq(emailCampaigns.status, status));
    }

    // Apply sorting based on the sortBy and sortOrder parameters
    if (sortBy && sortOrder) {
      if (sortOrder.toLowerCase() === "asc") {
        query = query.orderBy(asc(emailCampaigns[sortBy as keyof typeof emailCampaigns]));
      } else {
        query = query.orderBy(desc(emailCampaigns[sortBy as keyof typeof emailCampaigns]));
      }
    }

    const [campaignsResult, countResult] = await Promise.all([
      query.execute(),
      db.select({ count: sql<number>`count(*)` })
        .from(emailCampaigns)
        .where(eq(emailCampaigns.userId, userId))
        .execute(),
    ]);

    res.json({
      data: campaignsResult,
      count: countResult[0].count,
      limit,
      offset,
    });
  } catch (error) {
    console.error("Error fetching email campaigns:", error);
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unexpected error occurred" });
    }
  }
});

// Get a specific campaign
router.get("/:id", authenticate, async (req, res) => {
  try {
    const userId = req.user!.id;
    const campaignId = parseInt(req.params.id);

    const [campaign] = await db.select()
      .from(emailCampaigns)
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .execute();

    if (!campaign) {
      return res.status(404).json({ error: "Campaign not found" });
    }

    res.json(campaign);
  } catch (error) {
    console.error("Error fetching email campaign:", error);
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unexpected error occurred" });
    }
  }
});

// Update a campaign
router.patch("/:id", authenticate, async (req, res) => {
  try {
    const userId = req.user!.id;
    const campaignId = parseInt(req.params.id);

    const [existingCampaign] = await db.select()
      .from(emailCampaigns)
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .execute();

    if (!existingCampaign) {
      return res.status(404).json({ error: "Campaign not found" });
    }

    // Don't allow updates if the campaign is already sent
    if (existingCampaign.status === "sent") {
      return res.status(400).json({ error: "Cannot update a campaign that has already been sent" });
    }

    const updatedData = { ...req.body, updatedAt: new Date() };
    const [campaign] = await db.update(emailCampaigns)
      .set(updatedData)
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .returning()
      .execute();

    res.json(campaign);
  } catch (error) {
    console.error("Error updating email campaign:", error);
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unexpected error occurred" });
    }
  }
});

// Delete a campaign
router.delete("/:id", authenticate, async (req, res) => {
  try {
    const userId = req.user!.id;
    const campaignId = parseInt(req.params.id);

    const [existingCampaign] = await db.select()
      .from(emailCampaigns)
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .execute();

    if (!existingCampaign) {
      return res.status(404).json({ error: "Campaign not found" });
    }

    // Don't allow deletion if the campaign is already sent
    if (existingCampaign.status === "sent") {
      return res.status(400).json({ error: "Cannot delete a campaign that has already been sent" });
    }

    await db.delete(emailCampaigns)
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .execute();

    res.json({ success: true });
  } catch (error) {
    console.error("Error deleting email campaign:", error);
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unexpected error occurred" });
    }
  }
});

// Send a test email for campaign
router.post("/:id/test", authenticate, async (req, res) => {
  try {
    if (!resend) {
      return res.status(500).json({ error: "Email sending is not configured (missing RESEND_API_KEY)" });
    }

    const userId = req.user!.id;
    const campaignId = parseInt(req.params.id);
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: "Email address is required" });
    }

    const [campaign] = await db.select()
      .from(emailCampaigns)
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .execute();

    if (!campaign) {
      return res.status(404).json({ error: "Campaign not found" });
    }

    // If the campaign uses a template, get the template content
    let emailContent = campaign.content;
    if (campaign.contentType === "template" && campaign.templateId) {
      const [template] = await db.select()
        .from(emailTemplates)
        .where(and(
          eq(emailTemplates.id, campaign.templateId),
          eq(emailTemplates.userId, userId)
        ))
        .execute();

      if (!template) {
        return res.status(404).json({ error: "Template not found" });
      }

      emailContent = template.content;
    }

    // Sanitize the HTML content
    emailContent = sanitizeHtml(emailContent);

    // Add tracking pixel if tracking is enabled
    if (campaign.trackOpens) {
      emailContent += `<img src="${process.env.BASE_URL || 'http://localhost:5000'}/api/email/track/open/${campaignId}/test" width="1" height="1" />`;
    }

    // Use campaign sender info or default values
    const fromName = campaign.fromName || "Course Creator";
    const fromEmail = campaign.fromEmail || "<EMAIL>";
    const replyToEmail = campaign.replyToEmail || fromEmail;

    // For test emails, we'll use a custom testMode flag to handle example.com differently
    let testMode = false;
    if (email.endsWith("@example.com")) {
      testMode = true;
      console.log(`Using test mode because of example.com domain`);
    }

    if (!testMode) {
      // Check if the domain is properly configured (only for real emails)
      const domain = fromEmail.split("@")[1];
      if (!isCustomDomain(domain)) {
        return res.status(400).json({
          error: "You must use a custom domain for the sender email address",
          invalidDomain: domain
        });
      }
    }

    console.log(`Sending test campaign email to: ${email}`);
    console.log(`From: ${fromName} <${fromEmail}>`);

    // Only actually send if not a test domain
    if (!testMode) {
      await resend.emails.send({
        from: `${fromName} <${fromEmail}>`,
        to: email,
        subject: `[TEST] ${campaign.subject}`,
        reply_to: replyToEmail,
        html: emailContent,
      });
    }

    res.json({
      success: true,
      message: testMode 
        ? "Test completed successfully. Note: Emails to example.com domains are not actually sent by Resend."
        : "Test email sent successfully",
      testMode,
      email,
      subject: campaign.subject,
      fromName,
      fromEmail,
    });
  } catch (error) {
    console.error("Error sending test campaign email:", error);
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unexpected error occurred" });
    }
  }
});

// Send the actual campaign to the subscriber list
router.post("/:id/send", authenticate, async (req, res) => {
  try {
    if (!resend) {
      return res.status(500).json({ error: "Email sending is not configured (missing RESEND_API_KEY)" });
    }

    const userId = req.user!.id;
    const campaignId = parseInt(req.params.id);

    const [campaign] = await db.select()
      .from(emailCampaigns)
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .execute();

    if (!campaign) {
      return res.status(404).json({ error: "Campaign not found" });
    }

    if (campaign.status === "sent") {
      return res.status(400).json({ error: "Campaign has already been sent" });
    }

    if (!campaign.listId) {
      return res.status(400).json({ error: "No subscriber list selected for this campaign" });
    }

    // Get all the subscribers in the list
    const subscriberList = await db.select()
      .from(subscribers)
      .where(eq(subscribers.listId, campaign.listId))
      .execute();

    if (subscriberList.length === 0) {
      return res.status(400).json({ error: "No subscribers in selected list" });
    }

    // If the campaign uses a template, get the template content
    let emailContent = campaign.content;
    if (campaign.contentType === "template" && campaign.templateId) {
      const [template] = await db.select()
        .from(emailTemplates)
        .where(and(
          eq(emailTemplates.id, campaign.templateId),
          eq(emailTemplates.userId, userId)
        ))
        .execute();

      if (!template) {
        return res.status(404).json({ error: "Template not found" });
      }

      emailContent = template.content;
    }

    // Sanitize the HTML content
    emailContent = sanitizeHtml(emailContent);

    // Use campaign sender info or default values
    const fromName = campaign.fromName || "Course Creator";
    const fromEmail = campaign.fromEmail || "<EMAIL>";
    const replyToEmail = campaign.replyToEmail || fromEmail;

    // Check if the domain is properly configured 
    const domain = fromEmail.split("@")[1];
    if (!isCustomDomain(domain)) {
      return res.status(400).json({
        error: "You must use a custom domain for the sender email address",
        invalidDomain: domain
      });
    }

    // Start sending the campaign in the background
    // In a production environment, this would likely use a queue system
    console.log(`Starting to send campaign: ${campaign.name} to ${subscriberList.length} subscribers`);
    
    // To avoid overwhelming the email service, we'll set this up asynchronously
    // and update the campaign status immediately
    const sentAt = new Date();
    const [updatedCampaign] = await db.update(emailCampaigns)
      .set({
        status: "sending",
        sentAt,
        updatedAt: sentAt
      })
      .where(eq(emailCampaigns.id, campaignId))
      .returning()
      .execute();

    // This would normally be handled by a background job
    // For simplicity, we'll just do it asynchronously here
    sendCampaignEmails(campaign, subscriberList, emailContent, fromName, fromEmail, replyToEmail)
      .then(sentCount => {
        console.log(`Finished sending campaign ${campaign.id} to ${sentCount} subscribers`);
        // Update the campaign with the total sent count
        db.update(emailCampaigns)
          .set({
            status: "sent",
            totalSent: sentCount,
            updatedAt: new Date()
          })
          .where(eq(emailCampaigns.id, campaignId))
          .execute()
          .catch(err => console.error("Error updating campaign after sending:", err));
      })
      .catch(err => {
        console.error(`Error sending campaign ${campaign.id}:`, err);
        // Update the campaign status to show there was an error
        db.update(emailCampaigns)
          .set({
            status: "error",
            updatedAt: new Date()
          })
          .where(eq(emailCampaigns.id, campaignId))
          .execute()
          .catch(updateErr => console.error("Error updating campaign status:", updateErr));
      });

    res.json({
      success: true,
      campaign: updatedCampaign,
      message: `Started sending campaign to ${subscriberList.length} subscribers`
    });
  } catch (error) {
    console.error("Error starting campaign send:", error);
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unexpected error occurred" });
    }
  }
});

// Schedule a campaign for future sending
router.post("/:id/schedule", authenticate, async (req, res) => {
  try {
    const userId = req.user!.id;
    const campaignId = parseInt(req.params.id);
    const { scheduledFor } = req.body;

    if (!scheduledFor) {
      return res.status(400).json({ error: "Scheduled time is required" });
    }

    const scheduleDate = new Date(scheduledFor);
    if (isNaN(scheduleDate.getTime()) || scheduleDate <= new Date()) {
      return res.status(400).json({ error: "Invalid scheduled time. Must be a future date." });
    }

    const [existingCampaign] = await db.select()
      .from(emailCampaigns)
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .execute();

    if (!existingCampaign) {
      return res.status(404).json({ error: "Campaign not found" });
    }

    if (existingCampaign.status === "sent") {
      return res.status(400).json({ error: "Cannot schedule a campaign that has already been sent" });
    }

    const [campaign] = await db.update(emailCampaigns)
      .set({
        status: "scheduled",
        scheduledFor: scheduleDate,
        updatedAt: new Date()
      })
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .returning()
      .execute();

    res.json({
      success: true,
      campaign,
      message: `Campaign scheduled for ${scheduleDate.toISOString()}`
    });
  } catch (error) {
    console.error("Error scheduling campaign:", error);
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unexpected error occurred" });
    }
  }
});

// Get campaign statistics
router.get("/:id/stats", authenticate, async (req, res) => {
  try {
    const userId = req.user!.id;
    const campaignId = parseInt(req.params.id);

    const [campaign] = await db.select()
      .from(emailCampaigns)
      .where(and(
        eq(emailCampaigns.id, campaignId),
        eq(emailCampaigns.userId, userId)
      ))
      .execute();

    if (!campaign) {
      return res.status(404).json({ error: "Campaign not found" });
    }

    // Calculate open rate and click rate
    const openRate = campaign.totalSent > 0 ? campaign.totalOpened / campaign.totalSent : 0;
    const clickRate = campaign.totalSent > 0 ? campaign.totalClicked / campaign.totalSent : 0;

    res.json({
      id: campaign.id,
      name: campaign.name,
      status: campaign.status,
      sentAt: campaign.sentAt,
      totalSent: campaign.totalSent,
      totalOpened: campaign.totalOpened,
      totalClicked: campaign.totalClicked,
      openRate: openRate,
      clickRate: clickRate,
    });
  } catch (error) {
    console.error("Error fetching campaign stats:", error);
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(500).json({ error: "An unexpected error occurred" });
    }
  }
});

// Helper function to batch send campaign emails 
async function sendCampaignEmails(campaign, subscriberList, emailContent, fromName, fromEmail, replyToEmail) {
  if (!resend) return 0;
  
  let sentCount = 0;
  const batchSize = 50; // Process in batches to avoid overwhelming the API
  
  for (let i = 0; i < subscriberList.length; i += batchSize) {
    const batch = subscriberList.slice(i, i + batchSize);
    
    const sendPromises = batch.map(subscriber => {
      // Create a unique tracking pixel for each recipient
      let personalizedContent = emailContent;
      
      if (campaign.trackOpens) {
        personalizedContent += `<img src="${process.env.BASE_URL || 'http://localhost:5000'}/api/email/track/open/${campaign.id}/${subscriber.id}" width="1" height="1" />`;
      }
      
      try {
        return resend.emails.send({
          from: `${fromName} <${fromEmail}>`,
          to: subscriber.email,
          subject: campaign.subject,
          reply_to: replyToEmail,
          html: personalizedContent,
        });
      } catch (error) {
        console.error(`Error sending to ${subscriber.email}:`, error);
        return null;
      }
    });
    
    // Wait for the current batch to complete before moving on
    try {
      const results = await Promise.allSettled(sendPromises);
      
      // Count successful sends
      sentCount += results.filter(r => r.status === "fulfilled" && r.value).length;
      
      // Slow down between batches to avoid rate limits
      if (i + batchSize < subscriberList.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } catch (error) {
      console.error(`Error processing batch ${i / batchSize + 1}:`, error);
    }
  }
  
  return sentCount;
}

export default router;