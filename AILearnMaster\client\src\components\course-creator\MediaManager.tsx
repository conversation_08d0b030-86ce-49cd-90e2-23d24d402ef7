import React, { useState, useRef } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Upload, 
  Search, 
  Image, 
  Video, 
  FileText, 
  Download, 
  Plus, 
  Trash2, 
  Eye,
  ExternalLink,
  Paperclip,
  FolderOpen,
  Grid3X3,
  List
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';

interface MediaItem {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnailUrl?: string;
  size: number;
  uploadedAt: string;
  source: 'upload' | 'pexels' | 'pixabay';
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    author?: string;
    description?: string;
  };
}

interface StockMedia {
  id: string;
  url: string;
  thumbnailUrl: string;
  type: 'image' | 'video';
  source: 'pexels' | 'pixabay';
  author: string;
  description: string;
  tags: string[];
}

interface MediaManagerProps {
  modules: any[];
  onMediaAttach: (moduleId: string, lessonId: string, mediaId: string) => void;
  onBack: () => void;
  onNext: () => void;
}

export function MediaManager({ modules, onMediaAttach, onBack, onNext }: MediaManagerProps) {
  const [activeTab, setActiveTab] = useState('uploads');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedMedia, setSelectedMedia] = useState<string[]>([]);
  const [attachmentMode, setAttachmentMode] = useState(false);
  const [selectedLesson, setSelectedLesson] = useState<{ moduleId: string; lessonId: string } | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch user uploads
  const { data: userMedia = [], isLoading: isLoadingUploads } = useQuery({
    queryKey: ['/api/media'],
  });

  // Search Pexels photos
  const { data: pexelsPhotos = [], isLoading: isLoadingPexels } = useQuery({
    queryKey: ['/api/pexels/photos', searchTerm],
    enabled: activeTab === 'pexels' && searchTerm.length > 2,
  });

  // Search Pexels videos
  const { data: pexelsVideos = [], isLoading: isLoadingPexelsVideos } = useQuery({
    queryKey: ['/api/pexels/videos', searchTerm],
    enabled: activeTab === 'pexels' && searchTerm.length > 2,
  });

  // Search Pixabay
  const { data: pixabayMedia = [], isLoading: isLoadingPixabay } = useQuery({
    queryKey: ['/api/pixabay/photos', searchTerm],
    enabled: activeTab === 'pixabay' && searchTerm.length > 2,
  });

  // File upload mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${errorText}`);
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "Upload Successful",
        description: "Your file has been uploaded successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Upload Failed",
        description: "There was an error uploading your file. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Import stock media mutation
  const importMutation = useMutation({
    mutationFn: async (media: StockMedia) => {
      const response = await fetch(`/api/${media.source}/import`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: media.id,
          url: media.url,
          thumbnailUrl: media.thumbnailUrl,
          type: media.type,
          author: media.author,
          description: media.description,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Import failed');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "Media Imported",
        description: "Stock media has been added to your library.",
      });
    },
  });

  // Delete media mutation
  const deleteMutation = useMutation({
    mutationFn: async (mediaId: string) => {
      const response = await fetch(`/api/media/${mediaId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Delete failed');
      }
      
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
      toast({
        title: "Media Deleted",
        description: "Media item has been removed from your library.",
      });
    },
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach(file => {
        if (file && file.size > 0) {
          uploadMutation.mutate(file);
        }
      });
    }
    // Reset the input to allow re-uploading the same file
    event.target.value = '';
  };

  const handleImportStockMedia = (media: StockMedia) => {
    importMutation.mutate(media);
  };

  const handleAttachMedia = (mediaId: string) => {
    if (selectedLesson) {
      onMediaAttach(selectedLesson.moduleId, selectedLesson.lessonId, mediaId);
      setAttachmentMode(false);
      setSelectedLesson(null);
      toast({
        title: "Media Attached",
        description: "Media has been attached to the lesson successfully.",
      });
    }
  };

  const getMediaIcon = (type: string) => {
    switch (type) {
      case 'image': return <Image className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const renderMediaGrid = (mediaItems: (MediaItem | StockMedia)[], isStock = false) => {
    if (viewMode === 'grid') {
      return (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {mediaItems.map((item) => (
            <Card key={item.id} className="group cursor-pointer hover:shadow-lg transition-all">
              <CardContent className="p-0">
                <div className="aspect-video relative overflow-hidden rounded-t-lg">
                  {item.type === 'image' ? (
                    <img 
                      src={item.thumbnailUrl || item.url} 
                      alt={isStock ? item.description : (item as MediaItem).name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                    />
                  ) : item.type === 'video' ? (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <Video className="h-8 w-8 text-gray-400" />
                    </div>
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <FileText className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                  
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                    {isStock ? (
                      <Button 
                        size="sm" 
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                        onClick={() => handleImportStockMedia(item as StockMedia)}
                        disabled={importMutation.isPending}
                      >
                        {importMutation.isPending ? (
                          "Adding..."
                        ) : (
                          <>
                            <Plus className="h-4 w-4 mr-1" />
                            Add to Course
                          </>
                        )}
                      </Button>
                    ) : (
                      <>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                              <Paperclip className="h-4 w-4 mr-1" />
                              Attach
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-md">
                            <DialogHeader>
                              <DialogTitle>Attach to Lesson</DialogTitle>
                            </DialogHeader>
                            <div className="space-y-4 max-h-96 overflow-y-auto">
                              {modules.map((module) => (
                                <div key={module.id}>
                                  <h4 className="font-medium mb-2 text-sm">{module.title}</h4>
                                  <div className="space-y-1 ml-2">
                                    {module.lessons?.map((lesson: any) => (
                                      <Button
                                        key={lesson.id}
                                        variant="ghost"
                                        size="sm"
                                        className="w-full justify-start text-xs"
                                        onClick={() => handleAttachMedia(item.id)}
                                      >
                                        {lesson.title}
                                      </Button>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </DialogContent>
                        </Dialog>
                        <Button 
                          size="sm" 
                          variant="secondary"
                          onClick={() => deleteMutation.mutate(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
                
                <div className="p-3">
                  <div className="flex items-center gap-2 mb-1">
                    {getMediaIcon(item.type)}
                    <h4 className="font-medium text-sm truncate">
                      {isStock ? item.description : (item as MediaItem).name}
                    </h4>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>
                      {isStock ? 
                        `by ${(item as StockMedia).author}` : 
                        formatFileSize((item as MediaItem).size)
                      }
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {item.type}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    // List view
    return (
      <div className="space-y-2">
        {mediaItems.map((item) => (
          <Card key={item.id} className="p-4">
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0">
                {getMediaIcon(item.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="font-medium truncate">
                  {isStock ? item.description : (item as MediaItem).name}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {isStock ? 
                    `by ${(item as StockMedia).author}` : 
                    formatFileSize((item as MediaItem).size)
                  }
                </p>
              </div>
              
              <div className="flex items-center gap-2">
                <Badge variant="outline">{item.type}</Badge>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4" />
                </Button>
                {isStock ? (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleImportStockMedia(item as StockMedia)}
                    disabled={importMutation.isPending}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                ) : (
                  <>
                    <Button variant="outline" size="sm">
                      <Paperclip className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => deleteMutation.mutate(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Media Library</h2>
          <p className="text-sm text-muted-foreground">
            Upload your own files or browse stock media from Pexels and Pixabay
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid3X3 className="h-4 w-4" />}
          </Button>
          
          <Button onClick={() => fileInputRef.current?.click()}>
            <Upload className="h-4 w-4 mr-2" />
            Upload Files
          </Button>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*,video/*,.pdf,.doc,.docx,.txt"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="uploads" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            My Uploads
          </TabsTrigger>
          <TabsTrigger value="pexels" className="flex items-center gap-2">
            <Image className="h-4 w-4" />
            Stock Photos
          </TabsTrigger>
          <TabsTrigger value="pixabay" className="flex items-center gap-2">
            <Video className="h-4 w-4" />
            Stock Media
          </TabsTrigger>
        </TabsList>

        {/* Search for stock media */}
        {(activeTab === 'pexels' || activeTab === 'pixabay') && (
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search for professional photos and videos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        )}

        <TabsContent value="uploads">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FolderOpen className="h-5 w-5" />
                Your Media Files
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingUploads ? (
                <div className="flex items-center justify-center h-48">
                  <div className="text-muted-foreground">Loading your media...</div>
                </div>
              ) : userMedia.length === 0 ? (
                <div className="text-center py-12">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No media files yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Upload images, videos, and documents to get started
                  </p>
                  <Button onClick={() => fileInputRef.current?.click()}>
                    <Plus className="h-4 w-4 mr-2" />
                    Upload Your First File
                  </Button>
                </div>
              ) : (
                renderMediaGrid(userMedia)
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pexels">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                Professional Stock Photos
              </CardTitle>
            </CardHeader>
            <CardContent>
              {searchTerm.length <= 2 ? (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Search Professional Stock Photos</h3>
                  <p className="text-muted-foreground mb-4">
                    Enter at least 3 characters to search for high-quality images and videos
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    <Badge 
                      variant="outline" 
                      className="cursor-pointer hover:bg-gray-100"
                      onClick={() => setSearchTerm('business')}
                    >
                      business
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className="cursor-pointer hover:bg-gray-100"
                      onClick={() => setSearchTerm('technology')}
                    >
                      technology
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className="cursor-pointer hover:bg-gray-100"
                      onClick={() => setSearchTerm('education')}
                    >
                      education
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className="cursor-pointer hover:bg-gray-100"
                      onClick={() => setSearchTerm('people')}
                    >
                      people
                    </Badge>
                  </div>
                </div>
              ) : isLoadingPexels ? (
                <div className="flex items-center justify-center h-48">
                  <div className="text-muted-foreground">Searching Pexels...</div>
                </div>
              ) : (
                <div className="space-y-6">
                  {pexelsPhotos.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3">Photos</h4>
                      {renderMediaGrid(pexelsPhotos, true)}
                    </div>
                  )}
                  {pexelsVideos.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3">Videos</h4>
                      {renderMediaGrid(pexelsVideos, true)}
                    </div>
                  )}
                  {pexelsPhotos.length === 0 && pexelsVideos.length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No results found for "{searchTerm}"</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pixabay">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Video className="h-5 w-5" />
                Stock Media Collection
              </CardTitle>
            </CardHeader>
            <CardContent>
              {searchTerm.length <= 2 ? (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Search Stock Media Collection</h3>
                  <p className="text-muted-foreground mb-4">
                    Enter at least 3 characters to search for professional images and videos
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    <Badge 
                      variant="outline" 
                      className="cursor-pointer hover:bg-gray-100"
                      onClick={() => setSearchTerm('nature')}
                    >
                      nature
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className="cursor-pointer hover:bg-gray-100"
                      onClick={() => setSearchTerm('office')}
                    >
                      office
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className="cursor-pointer hover:bg-gray-100"
                      onClick={() => setSearchTerm('learning')}
                    >
                      learning
                    </Badge>
                    <Badge 
                      variant="outline" 
                      className="cursor-pointer hover:bg-gray-100"
                      onClick={() => setSearchTerm('abstract')}
                    >
                      abstract
                    </Badge>
                  </div>
                </div>
              ) : isLoadingPixabay ? (
                <div className="flex items-center justify-center h-48">
                  <div className="text-muted-foreground">Searching Pixabay...</div>
                </div>
              ) : pixabayMedia.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No results found for "{searchTerm}"</p>
                </div>
              ) : (
                renderMediaGrid(pixabayMedia, true)
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Back to Scripts
        </Button>
        <Button onClick={onNext}>
          Review & Publish Course
        </Button>
      </div>
    </div>
  );
}