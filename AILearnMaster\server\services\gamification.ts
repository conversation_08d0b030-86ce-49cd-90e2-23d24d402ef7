import { eq, desc, and, gte, sql } from "drizzle-orm";
import { db } from "../db";
import {
  badges,
  userBadges,
  learningActivities,
  userStats,
  dailyChallenges,
  userChallengeProgress,
  type Badge,
  type UserBadge,
  type LearningActivity,
  type DailyChallenge,
  type UserChallengeProgress,
  type InsertBadge,
  type InsertUserBadge,
  type InsertLearningActivity,
  type InsertDailyChallenge,
  type InsertUserChallengeProgress
} from "@shared/schema";

export class GamificationService {
  // Badge Management
  async createBadge(badge: InsertBadge): Promise<Badge> {
    const [newBadge] = await db.insert(badges).values(badge).returning();
    return newBadge;
  }

  async getAllBadges(): Promise<Badge[]> {
    return await db.select().from(badges).where(eq(badges.isActive, true));
  }

  async getBadgeById(id: number): Promise<Badge | undefined> {
    const [badge] = await db.select().from(badges).where(eq(badges.id, id));
    return badge;
  }

  // User Badge Management
  async getUserBadges(userId: number): Promise<(UserBadge & { badge: Badge })[]> {
    return await db
      .select({
        id: userBadges.id,
        userId: userBadges.userId,
        badgeId: userBadges.badgeId,
        earnedAt: userBadges.earnedAt,
        progress: userBadges.progress,
        isDisplayed: userBadges.isDisplayed,
        badge: badges
      })
      .from(userBadges)
      .innerJoin(badges, eq(userBadges.badgeId, badges.id))
      .where(eq(userBadges.userId, userId))
      .orderBy(desc(userBadges.earnedAt));
  }

  async awardBadge(userId: number, badgeId: number): Promise<UserBadge | null> {
    try {
      // Check if user already has this badge
      const existingBadge = await db
        .select()
        .from(userBadges)
        .where(and(eq(userBadges.userId, userId), eq(userBadges.badgeId, badgeId)));

      if (existingBadge.length > 0) {
        return null; // User already has this badge
      }

      // Get badge details for XP reward
      const badge = await this.getBadgeById(badgeId);
      if (!badge) return null;

      // Award the badge
      const [userBadge] = await db
        .insert(userBadges)
        .values({ userId, badgeId })
        .returning();

      // Update user stats
      await this.addXpToUser(userId, badge.xpReward);
      await this.incrementUserBadgeCount(userId);

      return userBadge;
    } catch (error) {
      console.error("Error awarding badge:", error);
      return null;
    }
  }

  // Learning Activity Tracking
  async recordActivity(activity: InsertLearningActivity): Promise<LearningActivity> {
    const [newActivity] = await db.insert(learningActivities).values(activity).returning();
    
    // Add XP to user
    if (activity.xpEarned && activity.xpEarned > 0) {
      await this.addXpToUser(activity.userId, activity.xpEarned);
    }

    // Update user's last activity date for streak tracking
    await this.updateUserLastActivity(activity.userId);

    // Check for badge eligibility
    await this.checkAndAwardBadges(activity.userId, activity.activityType);

    return newActivity;
  }

  async getUserActivities(userId: number, limit: number = 50): Promise<LearningActivity[]> {
    return await db
      .select()
      .from(learningActivities)
      .where(eq(learningActivities.userId, userId))
      .orderBy(desc(learningActivities.createdAt))
      .limit(limit);
  }

  // XP and Level Management
  async addXpToUser(userId: number, xpAmount: number): Promise<void> {
    // Get current user stats
    const [currentStats] = await db
      .select()
      .from(userStats)
      .where(eq(userStats.userId, userId));

    if (!currentStats) return;

    const newTotalXp = (currentStats.totalXp || 0) + xpAmount;
    const newLevel = this.calculateLevelFromXp(newTotalXp);

    await db
      .update(userStats)
      .set({
        totalXp: newTotalXp,
        level: newLevel
      })
      .where(eq(userStats.userId, userId));

    // Check for level-up badges
    if (newLevel > (currentStats.level || 1)) {
      await this.checkLevelUpBadges(userId, newLevel);
    }
  }

  private calculateLevelFromXp(totalXp: number): number {
    // Simple leveling formula: each level requires 100 more XP than the previous
    // Level 1: 0-99 XP, Level 2: 100-299 XP, Level 3: 300-599 XP, etc.
    let level = 1;
    let xpRequired = 100;
    let currentXp = totalXp;

    while (currentXp >= xpRequired) {
      currentXp -= xpRequired;
      level++;
      xpRequired += 100; // Each level requires 100 more XP
    }

    return level;
  }

  // Daily Challenges
  async getTodaysChallenges(): Promise<DailyChallenge[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return await db
      .select()
      .from(dailyChallenges)
      .where(
        and(
          eq(dailyChallenges.isActive, true),
          gte(dailyChallenges.validUntil, today)
        )
      );
  }

  async getUserChallengeProgress(userId: number, date?: Date): Promise<(UserChallengeProgress & { challenge: DailyChallenge })[]> {
    const targetDate = date || new Date();
    targetDate.setHours(0, 0, 0, 0);

    return await db
      .select({
        id: userChallengeProgress.id,
        userId: userChallengeProgress.userId,
        challengeId: userChallengeProgress.challengeId,
        progress: userChallengeProgress.progress,
        completed: userChallengeProgress.completed,
        completedAt: userChallengeProgress.completedAt,
        date: userChallengeProgress.date,
        challenge: dailyChallenges
      })
      .from(userChallengeProgress)
      .innerJoin(dailyChallenges, eq(userChallengeProgress.challengeId, dailyChallenges.id))
      .where(
        and(
          eq(userChallengeProgress.userId, userId),
          gte(userChallengeProgress.date, targetDate)
        )
      );
  }

  async updateChallengeProgress(userId: number, challengeId: number, progressIncrement: number): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get or create progress record
    const [existingProgress] = await db
      .select()
      .from(userChallengeProgress)
      .where(
        and(
          eq(userChallengeProgress.userId, userId),
          eq(userChallengeProgress.challengeId, challengeId),
          gte(userChallengeProgress.date, today)
        )
      );

    const challenge = await db
      .select()
      .from(dailyChallenges)
      .where(eq(dailyChallenges.id, challengeId));

    if (!challenge[0]) return;

    if (existingProgress) {
      const newProgress = existingProgress.progress + progressIncrement;
      const isCompleted = newProgress >= challenge[0].targetValue;

      await db
        .update(userChallengeProgress)
        .set({
          progress: newProgress,
          completed: isCompleted,
          completedAt: isCompleted ? new Date() : null
        })
        .where(eq(userChallengeProgress.id, existingProgress.id));

      // Award XP if challenge completed
      if (isCompleted && !existingProgress.completed) {
        await this.addXpToUser(userId, challenge[0].xpReward);
      }
    } else {
      const newProgress = progressIncrement;
      const isCompleted = newProgress >= challenge[0].targetValue;

      await db.insert(userChallengeProgress).values({
        userId,
        challengeId,
        progress: newProgress,
        completed: isCompleted,
        completedAt: isCompleted ? new Date() : null,
        date: today
      });

      // Award XP if challenge completed
      if (isCompleted) {
        await this.addXpToUser(userId, challenge[0].xpReward);
      }
    }
  }

  // Streak Management
  async updateUserLastActivity(userId: number): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const [currentStats] = await db
      .select()
      .from(userStats)
      .where(eq(userStats.userId, userId));

    if (!currentStats) return;

    const lastActivityDate = currentStats.lastActivityDate;
    let newStreakDays = currentStats.streakDays || 0;

    if (lastActivityDate) {
      const lastDate = new Date(lastActivityDate);
      lastDate.setHours(0, 0, 0, 0);
      
      const daysDiff = Math.floor((today.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff === 1) {
        // Consecutive day - increment streak
        newStreakDays += 1;
      } else if (daysDiff > 1) {
        // Streak broken - reset to 1
        newStreakDays = 1;
      }
      // If daysDiff === 0, it's the same day, keep current streak
    } else {
      // First activity ever
      newStreakDays = 1;
    }

    await db
      .update(userStats)
      .set({
        lastActivityDate: today,
        streakDays: newStreakDays,
        longestStreak: Math.max(newStreakDays, currentStats.longestStreak || 0)
      })
      .where(eq(userStats.userId, userId));

    // Check for streak badges
    await this.checkStreakBadges(userId, newStreakDays);
  }

  // Badge Checking Logic
  private async checkAndAwardBadges(userId: number, activityType: string): Promise<void> {
    const allBadges = await this.getAllBadges();
    
    for (const badge of allBadges) {
      const condition = badge.condition as any;
      
      if (condition.type === 'activity_count') {
        await this.checkActivityCountBadge(userId, badge, activityType);
      } else if (condition.type === 'course_completion') {
        await this.checkCourseCompletionBadge(userId, badge);
      } else if (condition.type === 'lesson_streak') {
        await this.checkLessonStreakBadge(userId, badge);
      }
    }
  }

  private async checkActivityCountBadge(userId: number, badge: Badge, activityType: string): Promise<void> {
    const condition = badge.condition as any;
    
    if (condition.activityType && condition.activityType !== activityType) {
      return;
    }

    const [activityCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(learningActivities)
      .where(
        and(
          eq(learningActivities.userId, userId),
          condition.activityType ? eq(learningActivities.activityType, condition.activityType) : undefined
        )
      );

    if (activityCount.count >= condition.count) {
      await this.awardBadge(userId, badge.id);
    }
  }

  private async checkCourseCompletionBadge(userId: number, badge: Badge): Promise<void> {
    const condition = badge.condition as any;
    
    const [completionCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(learningActivities)
      .where(
        and(
          eq(learningActivities.userId, userId),
          eq(learningActivities.activityType, 'course_completed')
        )
      );

    if (completionCount.count >= condition.count) {
      await this.awardBadge(userId, badge.id);
    }
  }

  private async checkLessonStreakBadge(userId: number, badge: Badge): Promise<void> {
    const condition = badge.condition as any;
    const [currentStats] = await db
      .select()
      .from(userStats)
      .where(eq(userStats.userId, userId));

    if (currentStats && (currentStats.streakDays || 0) >= condition.days) {
      await this.awardBadge(userId, badge.id);
    }
  }

  private async checkLevelUpBadges(userId: number, newLevel: number): Promise<void> {
    const allBadges = await this.getAllBadges();
    
    for (const badge of allBadges) {
      const condition = badge.condition as any;
      
      if (condition.type === 'level_reached' && newLevel >= condition.level) {
        await this.awardBadge(userId, badge.id);
      }
    }
  }

  private async checkStreakBadges(userId: number, streakDays: number): Promise<void> {
    const allBadges = await this.getAllBadges();
    
    for (const badge of allBadges) {
      const condition = badge.condition as any;
      
      if (condition.type === 'streak' && streakDays >= condition.days) {
        await this.awardBadge(userId, badge.id);
      }
    }
  }

  private async incrementUserBadgeCount(userId: number): Promise<void> {
    await db
      .update(userStats)
      .set({
        totalBadges: sql`${userStats.totalBadges} + 1`
      })
      .where(eq(userStats.userId, userId));
  }

  // User Progress Summary
  async getUserProgressSummary(userId: number): Promise<{
    level: number;
    totalXp: number;
    xpToNextLevel: number;
    totalBadges: number;
    streakDays: number;
    longestStreak: number;
    recentBadges: (UserBadge & { badge: Badge })[];
    todaysChallenges: (UserChallengeProgress & { challenge: DailyChallenge })[];
  }> {
    const [stats] = await db.select().from(userStats).where(eq(userStats.userId, userId));
    
    if (!stats) {
      throw new Error("User stats not found");
    }

    const currentLevel = stats.level || 1;
    const totalXp = stats.totalXp || 0;
    const xpToNextLevel = this.calculateXpToNextLevel(totalXp, currentLevel);

    const recentBadges = await db
      .select({
        id: userBadges.id,
        userId: userBadges.userId,
        badgeId: userBadges.badgeId,
        earnedAt: userBadges.earnedAt,
        progress: userBadges.progress,
        isDisplayed: userBadges.isDisplayed,
        badge: badges
      })
      .from(userBadges)
      .innerJoin(badges, eq(userBadges.badgeId, badges.id))
      .where(eq(userBadges.userId, userId))
      .orderBy(desc(userBadges.earnedAt))
      .limit(5);

    const todaysChallenges = await this.getUserChallengeProgress(userId);

    return {
      level: currentLevel,
      totalXp,
      xpToNextLevel,
      totalBadges: stats.totalBadges || 0,
      streakDays: stats.streakDays || 0,
      longestStreak: stats.longestStreak || 0,
      recentBadges,
      todaysChallenges
    };
  }

  private calculateXpToNextLevel(totalXp: number, currentLevel: number): number {
    let xpUsed = 0;
    let xpRequired = 100;
    
    // Calculate XP used for current level
    for (let i = 1; i < currentLevel; i++) {
      xpUsed += xpRequired;
      xpRequired += 100;
    }
    
    const xpInCurrentLevel = totalXp - xpUsed;
    return xpRequired - xpInCurrentLevel;
  }
}

export const gamificationService = new GamificationService();