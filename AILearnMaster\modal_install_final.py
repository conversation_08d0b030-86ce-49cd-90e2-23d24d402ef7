#!/usr/bin/env python3
"""
Final Modal installation approach
"""

import subprocess
import sys
import os

def main():
    print("Installing Modal...")
    
    # Remove TTS temporarily to avoid conflicts
    try:
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "tts", "-y"], 
                      stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    except:
        pass
    
    # Install Modal
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "modal>=0.60.0"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("Modal installed successfully")
        else:
            print(f"Installation failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    # Test Modal
    try:
        import modal
        app = modal.App("test")
        print(f"Modal working! Version: {modal.__version__}")
        return True
    except Exception as e:
        print(f"Modal test failed: {e}")
        return False

if __name__ == "__main__":
    main()