import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { testCourseStructureAPI } from "../test-api";
import { Loader2 } from "lucide-react";

export default function ApiTestPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleTestApi = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await testCourseStructureAPI();
      setResult(response);
      console.log("API test response:", response);
    } catch (err) {
      console.error("API test failed:", err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6">API Testing Page</h1>
      
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Course Structure Generation API Test</CardTitle>
            <CardDescription>
              Test if the course structure generation API is working correctly
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleTestApi} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Testing...
                </>
              ) : (
                "Test Course Structure API"
              )}
            </Button>
            
            {error && (
              <div className="mt-4 p-4 bg-red-50 text-red-800 rounded-md">
                <h3 className="font-semibold">Error:</h3>
                <p>{error}</p>
              </div>
            )}
            
            {result && (
              <div className="mt-4">
                <h3 className="font-semibold mb-2">
                  Test {result.success ? "succeeded" : "failed"}:
                </h3>
                <pre className="p-4 bg-slate-100 rounded-md overflow-auto max-h-96 text-xs">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <p className="text-sm text-muted-foreground">
              This page tests the course structure generation API. The debug endpoint doesn't require authentication.
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}