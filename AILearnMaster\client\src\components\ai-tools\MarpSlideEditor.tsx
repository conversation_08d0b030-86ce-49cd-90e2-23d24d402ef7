import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Loader2, Download, Upload, FileCode, FileText } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface MarpSlideEditorProps {
  onSlidesGenerated?: (url: string, format: string) => void;
  defaultMarkdown?: string;
  courseId?: number;
  lessonId?: number;
  heading?: string;
  description?: string;
}

export function MarpSlideEditor({
  onSlidesGenerated,
  defaultMarkdown = '',
  courseId,
  lessonId,
  heading = 'Marp Slide Editor',
  description = 'Create presentation slides using Markdown with Marp syntax.'
}: MarpSlideEditorProps) {
  const { toast } = useToast();
  const [markdown, setMarkdown] = useState(defaultMarkdown);
  const [format, setFormat] = useState<'html' | 'pdf' | 'pptx'>('html');
  const [generating, setGenerating] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const [generatedUrl, setGeneratedUrl] = useState<string | null>(null);
  
  // Generate slides from lesson content
  const generateFromLesson = async () => {
    if (!courseId || !lessonId) {
      toast({
        title: 'Course or lesson missing',
        description: 'Course ID and lesson ID are required to generate slides from lesson content',
        variant: 'destructive'
      });
      return;
    }
    
    setGenerating(true);
    
    try {
      const response = await apiRequest(
        'POST',
        `/api/courses/${courseId}/lessons/${lessonId}/generate-slides`,
        {}
      );
      
      const data = await response.json();
      
      if (data.markdown) {
        setMarkdown(data.markdown);
        if (data.previewHtml) {
          setPreview(data.previewHtml);
        }
      } else {
        toast({
          title: 'Generation failed',
          description: data.message || 'Failed to generate slides from lesson',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error generating slides from lesson:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate slides from lesson content',
        variant: 'destructive'
      });
    } finally {
      setGenerating(false);
    }
  };
  
  // Process slides in the selected format
  const processSlides = async () => {
    if (!markdown.trim()) {
      toast({
        title: 'Content required',
        description: 'Please enter markdown content for your slides',
        variant: 'destructive'
      });
      return;
    }
    
    setGenerating(true);
    
    try {
      const response = await apiRequest(
        'POST',
        '/api/ai-tools/marp-slides',
        {
          markdown,
          format,
          courseId,
          lessonId
        }
      );
      
      const data = await response.json();
      
      if (data.url) {
        setGeneratedUrl(data.url);
        if (onSlidesGenerated) {
          onSlidesGenerated(data.url, format);
        }
        
        toast({
          title: 'Slides processed successfully',
          description: `Your slides have been generated in ${format.toUpperCase()} format`
        });
      } else {
        toast({
          title: 'Processing failed',
          description: data.message || 'Failed to process slides',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error processing slides:', error);
      toast({
        title: 'Error',
        description: 'Failed to process slides',
        variant: 'destructive'
      });
    } finally {
      setGenerating(false);
    }
  };
  
  // For importing a markdown file
  const handleFileImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = (event) => {
      const content = event.target?.result as string;
      setMarkdown(content);
    };
    reader.readAsText(file);
    
    // Reset file input
    e.target.value = '';
  };
  
  const downloadMarkdown = () => {
    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `slides-${Date.now()}.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  const getMarpBoilerplate = () => {
    const boilerplate = `---
marp: true
theme: default
paginate: true
backgroundColor: #fff
---

# My Presentation Title

## Author Name

---

# First Slide

- Point 1
- Point 2
- Point 3

---

# Second Slide

![width:500px](https://via.placeholder.com/500x300)

---

# Code Example

\`\`\`javascript
function hello() {
  console.log('Hello world!');
}
\`\`\`

---

# Thank You!

**Contact Information:**
- Email: <EMAIL>
- Website: www.example.com
`;
    
    setMarkdown(boilerplate);
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{heading}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-[600px]">
          <div className="flex flex-col h-full">
            <div className="flex justify-between items-center mb-2">
              <div className="text-sm font-medium">Markdown Editor</div>
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={getMarpBoilerplate}
                  title="Get template"
                  disabled={generating}
                >
                  <FileText className="h-4 w-4" />
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  title="Import markdown"
                  disabled={generating}
                  onClick={() => document.getElementById('file-import')?.click()}
                >
                  <Upload className="h-4 w-4" />
                  <input 
                    id="file-import" 
                    type="file" 
                    accept=".md,.markdown,.txt" 
                    className="hidden" 
                    onChange={handleFileImport} 
                  />
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  title="Download markdown"
                  disabled={generating || !markdown.trim()}
                  onClick={downloadMarkdown}
                >
                  <Download className="h-4 w-4" />
                </Button>
                {courseId && lessonId && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    title="Generate from lesson"
                    disabled={generating}
                    onClick={generateFromLesson}
                  >
                    {generating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      'Generate from Lesson'
                    )}
                  </Button>
                )}
              </div>
            </div>
            
            <Textarea 
              value={markdown} 
              onChange={(e) => setMarkdown(e.target.value)}
              className="flex-1 font-mono text-sm resize-none"
              placeholder="# Enter your Marp markdown here..."
              disabled={generating}
            />
          </div>
          
          <div className="border rounded-lg p-4 h-full flex flex-col">
            <div className="flex justify-between items-center mb-2">
              <div className="text-sm font-medium">Preview & Export</div>
              <div className="flex items-center space-x-2">
                <Select 
                  value={format} 
                  onValueChange={(value: 'html' | 'pdf' | 'pptx') => setFormat(value)}
                  disabled={generating}
                >
                  <SelectTrigger className="w-[100px]">
                    <SelectValue placeholder="Format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="html">HTML</SelectItem>
                    <SelectItem value="pdf">PDF</SelectItem>
                    <SelectItem value="pptx">PowerPoint</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button 
                  size="sm"
                  disabled={generating || !markdown.trim()}
                  onClick={processSlides}
                >
                  {generating ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <FileCode className="h-4 w-4 mr-2" />
                  )}
                  Process Slides
                </Button>
              </div>
            </div>
            
            <div className="flex-1 overflow-auto bg-white rounded border border-border p-4">
              {preview ? (
                <div 
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: preview }}
                />
              ) : (
                <div className="h-full flex items-center justify-center text-muted-foreground">
                  {markdown.trim() ? (
                    <div className="text-center">
                      <p>Preview will be shown here</p>
                      <p className="text-xs">Click "Process Slides" to generate a preview</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <p>No content yet</p>
                      <p className="text-xs">Enter markdown content or import a file to begin</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        {generatedUrl && (
          <div className="flex justify-end w-full">
            <a 
              href={generatedUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center"
            >
              <Button>
                <Download className="h-4 w-4 mr-2" />
                Download {format.toUpperCase()}
              </Button>
            </a>
          </div>
        )}
      </CardFooter>
    </Card>
  );
}

export default MarpSlideEditor;