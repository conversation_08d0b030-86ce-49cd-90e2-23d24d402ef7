import { useState, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Upload, Search, Image, Video, FileText, X, Plus, ArrowLeft, Folder, Globe, Play } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { StockMediaBrowser } from './StockMediaBrowser';

interface MediaItem {
  id: number;
  name: string;
  type: 'image' | 'video' | 'audio' | 'document';
  url: string;
  mimeType: string;
  fileSize: number;
  createdAt: string;
}

interface StockMedia {
  id: string;
  type: 'image' | 'video';
  url: string;
  thumbnail?: string;
  title: string;
  source: 'pexels' | 'pixabay';
  tags?: string[];
}

interface MediaLibraryProps {
  onSelectMedia?: (media: MediaItem | StockMedia) => void;
  selectedMedia?: (MediaItem | StockMedia)[];
  mode?: 'select' | 'manage';
}

export function MediaLibrary({ onSelectMedia, selectedMedia = [], mode = 'manage' }: MediaLibraryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [showStockMedia, setShowStockMedia] = useState(false);
  const [stockSearchQuery, setStockSearchQuery] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch user's media
  const { data: userMedia = [], isLoading: loadingUserMedia } = useQuery({
    queryKey: ['/api/media'],
  });

  // Search Pexels photos
  const { data: pexelsPhotos = [], isLoading: loadingPexelsPhotos } = useQuery({
    queryKey: ['/api/pexels/photos', stockSearchQuery],
    enabled: showStockMedia && stockSearchQuery.length > 0,
  });

  // Search Pexels videos
  const { data: pexelsVideos = [], isLoading: loadingPexelsVideos } = useQuery({
    queryKey: ['/api/pexels/videos', stockSearchQuery],
    enabled: showStockMedia && stockSearchQuery.length > 0,
  });

  // Search Pixabay media
  const { data: pixabayPhotos = [], isLoading: loadingPixabayPhotos } = useQuery({
    queryKey: ['/api/pixabay/photos', stockSearchQuery],
    enabled: showStockMedia && stockSearchQuery.length > 0,
  });

  const { data: pixabayVideos = [], isLoading: loadingPixabayVideos } = useQuery({
    queryKey: ['/api/pixabay/videos', stockSearchQuery],
    enabled: showStockMedia && stockSearchQuery.length > 0,
  });

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('name', file.name);

      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Upload failed');
      }

      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: 'Upload successful',
        description: `${data.name} has been uploaded successfully.`,
      });
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Upload failed',
        description: error.message || 'Failed to upload file',
        variant: 'destructive',
      });
    },
  });

  // Import stock media mutation
  const importMutation = useMutation({
    mutationFn: async (media: StockMedia) => {
      const endpoint = media.source === 'pexels' ? '/api/pexels/import' : '/api/pixabay/import';
      return apiRequest(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          mediaId: media.id,
          type: media.type,
          url: media.url,
          title: media.title,
        }),
      });
    },
    onSuccess: () => {
      toast({
        title: 'Media imported',
        description: 'Stock media has been imported to your library.',
      });
      queryClient.invalidateQueries({ queryKey: ['/api/media'] });
    },
    onError: (error: any) => {
      toast({
        title: 'Import failed',
        description: error.message || 'Failed to import media',
        variant: 'destructive',
      });
    },
  });

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    for (const file of Array.from(files)) {
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: 'File too large',
          description: `${file.name} is larger than 10MB limit.`,
          variant: 'destructive',
        });
        continue;
      }
      uploadMutation.mutate(file);
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };



  const handleSelectMedia = (media: MediaItem | StockMedia) => {
    if (onSelectMedia) {
      onSelectMedia(media);
    }
  };

  const isSelected = (media: MediaItem | StockMedia) => {
    return selectedMedia.some(selected => 
      ('id' in selected && 'id' in media && selected.id === media.id) ||
      ('url' in selected && 'url' in media && selected.url === media.url)
    );
  };

  const getMediaIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="w-4 h-4" />;
      case 'video':
        return <Video className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const stockPhotos = [...(pexelsPhotos || []), ...(pixabayPhotos || [])];
  const stockVideos = [...(pexelsVideos || []), ...(pixabayVideos || [])];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
      {/* Enhanced Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" className="flex items-center gap-2 hover:bg-blue-50">
                <ArrowLeft className="w-4 h-4" />
                Back
              </Button>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Image className="w-4 h-4 text-white" />
                </div>
                <h1 className="text-2xl font-bold text-gray-900">Media Library</h1>
              </div>
            </div>
            <Button variant="outline" className="flex items-center gap-2 hover:bg-blue-50">
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Enhanced Action Bar */}
        <div className="bg-white rounded-xl shadow-sm border p-6 mb-8">
          <div className="flex flex-wrap items-center gap-6">
            {/* Quick Access Buttons */}
            <div className="flex items-center gap-4">
              <Button 
                variant="ghost" 
                className="flex items-center gap-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                onClick={() => setActiveTab('all')}
              >
                <Folder className="w-4 h-4" />
                My Media
              </Button>
              <Button 
                variant="ghost" 
                className="flex items-center gap-2 text-gray-600 hover:text-green-600 hover:bg-green-50"
                onClick={() => setShowStockMedia(true)}
              >
                <Globe className="w-4 h-4" />
                Stock Media
              </Button>

            </div>

            {/* Enhanced Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input 
                  placeholder="Search your media library..." 
                  className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500 bg-gray-50 focus:bg-white transition-all"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              <Button 
                onClick={() => fileInputRef.current?.click()}
                disabled={uploadMutation.isPending}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all"
              >
                <Upload className="w-4 h-4" />
                {uploadMutation.isPending ? 'Uploading...' : 'Upload'}
              </Button>

            </div>
          </div>
        </div>

        {/* Enhanced Main Content Card */}
        <Card className="bg-white shadow-lg border-0 rounded-xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
            <CardTitle className="text-xl font-semibold flex items-center gap-2">
              <Image className="w-5 h-5" />
              Your Media Collection
            </CardTitle>
            <p className="text-blue-100 mt-1">Organize, search, and manage all your media assets</p>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-6 bg-gray-50 p-1 m-6 mb-0 rounded-lg">
                <TabsTrigger value="all" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">All</TabsTrigger>
                <TabsTrigger value="images" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">Images</TabsTrigger>
                <TabsTrigger value="videos" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">Videos</TabsTrigger>
                <TabsTrigger value="documents" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">Documents</TabsTrigger>
                <TabsTrigger value="pexels-media" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">Pexels Media</TabsTrigger>
                <TabsTrigger value="pixabay-media" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">Pixabay Media</TabsTrigger>
              </TabsList>

              {/* Hidden file input */}
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*,video/*,audio/*,.pdf,.doc,.docx"
                onChange={handleFileUpload}
                className="hidden"
              />

            <TabsContent value="all" className="p-6">
              {loadingUserMedia ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="aspect-video bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse rounded-xl"></div>
                  ))}
                </div>
              ) : userMedia && userMedia.length === 0 ? (
                <div className="text-center py-12">
                  <Image className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No media found in your library.</h3>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {userMedia && userMedia.map((media: MediaItem) => (
                    <Card 
                      key={media.id} 
                      className={`group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1 ${
                        isSelected(media) ? 'ring-2 ring-blue-500 shadow-lg' : 'hover:shadow-lg'
                      }`}
                      onClick={() => handleSelectMedia(media)}
                    >
                      <CardContent className="p-4">
                        <div className="aspect-video bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl mb-4 overflow-hidden relative">
                          {media.type === 'image' ? (
                            <>
                              <img 
                                src={media.url} 
                                alt={media.name}
                                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                              />
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-300"></div>
                            </>
                          ) : (
                            <div className="flex flex-col items-center justify-center h-full">
                              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                                {getMediaIcon(media.type)}
                              </div>
                              <span className="text-sm font-medium text-gray-600">{media.type.toUpperCase()}</span>
                            </div>
                          )}
                        </div>
                        <div className="space-y-3">
                          <h4 className="font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors">{media.name}</h4>
                          <div className="flex items-center justify-between">
                            <Badge variant="secondary" className="bg-blue-50 text-blue-700">{media.type}</Badge>
                            <span className="text-xs text-gray-500 font-medium">
                              {formatFileSize(media.fileSize)}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="images" className="space-y-4">
              {loadingUserMedia ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="aspect-video bg-gray-200 animate-pulse rounded-lg"></div>
                  ))}
                </div>
              ) : userMedia && userMedia.filter((m: MediaItem) => m.type === 'image').length === 0 ? (
                <div className="text-center py-12">
                  <Image className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No images found in your library.</h3>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {userMedia && userMedia.filter((m: MediaItem) => m.type === 'image').map((media: MediaItem) => (
                    <Card 
                      key={media.id} 
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        isSelected(media) ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => handleSelectMedia(media)}
                    >
                      <CardContent className="p-4">
                        <div className="aspect-video bg-gray-100 rounded-lg mb-3 overflow-hidden">
                          <img 
                            src={media.url} 
                            alt={media.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="space-y-2">
                          <h4 className="font-medium truncate">{media.name}</h4>
                          <div className="flex items-center justify-between">
                            <Badge variant="secondary">{media.type}</Badge>
                            <span className="text-xs text-gray-500">
                              {formatFileSize(media.fileSize)}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="videos" className="space-y-4">
              {loadingUserMedia ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="aspect-video bg-gray-200 animate-pulse rounded-lg"></div>
                  ))}
                </div>
              ) : userMedia && userMedia.filter((m: MediaItem) => m.type === 'video').length === 0 ? (
                <div className="text-center py-12">
                  <Video className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No videos found in your library.</h3>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {userMedia && userMedia.filter((m: MediaItem) => m.type === 'video').map((media: MediaItem) => (
                    <Card 
                      key={media.id} 
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        isSelected(media) ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => handleSelectMedia(media)}
                    >
                      <CardContent className="p-4">
                        <div className="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                          <div className="flex flex-col items-center gap-2">
                            <Video className="w-8 h-8 text-gray-400" />
                            <span className="text-sm text-gray-500">VIDEO</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <h4 className="font-medium truncate">{media.name}</h4>
                          <div className="flex items-center justify-between">
                            <Badge variant="secondary">{media.type}</Badge>
                            <span className="text-xs text-gray-500">
                              {formatFileSize(media.fileSize)}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="documents" className="space-y-4">
              {loadingUserMedia ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="aspect-video bg-gray-200 animate-pulse rounded-lg"></div>
                  ))}
                </div>
              ) : userMedia && userMedia.filter((m: MediaItem) => m.type === 'document').length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found in your library.</h3>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {userMedia && userMedia.filter((m: MediaItem) => m.type === 'document').map((media: MediaItem) => (
                    <Card 
                      key={media.id} 
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        isSelected(media) ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => handleSelectMedia(media)}
                    >
                      <CardContent className="p-4">
                        <div className="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                          <div className="flex flex-col items-center gap-2">
                            <FileText className="w-8 h-8 text-gray-400" />
                            <span className="text-sm text-gray-500">DOCUMENT</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <h4 className="font-medium truncate">{media.name}</h4>
                          <div className="flex items-center justify-between">
                            <Badge variant="secondary">{media.type}</Badge>
                            <span className="text-xs text-gray-500">
                              {formatFileSize(media.fileSize)}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="stock-media" className="space-y-4">
              <div className="text-center py-12">
                <Globe className="w-16 h-16 mx-auto text-green-500 mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Stock Media Search</h3>
                <p className="text-gray-600 mb-6">Find high-quality images and videos from professional stock libraries</p>
                <Button 
                  onClick={() => setShowStockMedia(true)}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <Search className="w-4 h-4 mr-2" />
                  Browse Stock Media
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="stock-photos" className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    placeholder="Search photos..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {searchQuery.length === 0 ? (
                <div className="text-center py-12">
                  <Search className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Search for photos</h3>
                  <p className="text-gray-500">Enter a search term to find high-quality stock photos</p>
                </div>
              ) : loadingPexelsPhotos || loadingPixabayPhotos ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="aspect-video bg-gray-200 animate-pulse rounded-lg"></div>
                  ))}
                </div>
              ) : stockPhotos.length === 0 ? (
                <div className="text-center py-12">
                  <Image className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No photos found</h3>
                  <p className="text-gray-500">Try a different search term</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {stockPhotos.map((photo: StockMedia) => (
                    <Card 
                      key={`${photo.source}-${photo.id}`}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        isSelected(photo) ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => handleSelectMedia(photo)}
                    >
                      <CardContent className="p-4">
                        <div className="aspect-video bg-gray-100 rounded-lg mb-3 overflow-hidden">
                          <img 
                            src={photo.thumbnail || photo.url} 
                            alt={photo.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="space-y-2">
                          <h4 className="font-medium truncate">{photo.title}</h4>
                          <div className="flex items-center justify-between">
                            <Badge variant="outline">{photo.source}</Badge>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                importMutation.mutate(photo);
                              }}
                              disabled={importMutation.isPending}
                            >
                              Import
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="stock-videos" className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    placeholder="Search videos..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {searchQuery.length === 0 ? (
                <div className="text-center py-12">
                  <Search className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Search for videos</h3>
                  <p className="text-gray-500">Enter a search term to find high-quality stock videos</p>
                </div>
              ) : loadingPexelsVideos || loadingPixabayVideos ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="aspect-video bg-gray-200 animate-pulse rounded-lg"></div>
                  ))}
                </div>
              ) : stockVideos.length === 0 ? (
                <div className="text-center py-12">
                  <Video className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No videos found</h3>
                  <p className="text-gray-500">Try a different search term</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {stockVideos.map((video: StockMedia) => (
                    <Card 
                      key={`${video.source}-${video.id}`}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        isSelected(video) ? 'ring-2 ring-blue-500' : ''
                      }`}
                      onClick={() => handleSelectMedia(video)}
                    >
                      <CardContent className="p-4">
                        <div className="aspect-video bg-gray-100 rounded-lg mb-3 overflow-hidden">
                          <img 
                            src={video.thumbnail || video.url} 
                            alt={video.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="space-y-2">
                          <h4 className="font-medium truncate">{video.title}</h4>
                          <div className="flex items-center justify-between">
                            <Badge variant="outline">{video.source}</Badge>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                importMutation.mutate(video);
                              }}
                              disabled={importMutation.isPending}
                            >
                              Import
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
        </Card>



        {/* Stock Media Modal */}
        <Dialog open={showStockMedia} onOpenChange={setShowStockMedia}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Globe className="w-5 h-5 text-green-600" />
                Stock Media Library
              </DialogTitle>
              <DialogDescription>
                Browse and import high-quality stock photos and videos for your projects.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              {/* Stock Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={stockSearchQuery}
                  onChange={(e) => setStockSearchQuery(e.target.value)}
                  placeholder="Search for photos and videos..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>

              {/* Stock Media Results */}
              <div className="max-h-96 overflow-y-auto">
                {stockSearchQuery ? (
                  <div className="space-y-6">
                    {/* Photos Section */}
                    {stockPhotos.length > 0 && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Photos</h3>
                        <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                          {stockPhotos.map((photo: StockMedia) => (
                            <div key={photo.id} className="group relative aspect-square bg-gray-100 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200">
                              <img
                                src={photo.thumbnail || photo.url}
                                alt={photo.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                              />
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-200 flex items-end p-2">
                                <Button
                                  size="sm"
                                  onClick={() => importMutation.mutate(photo)}
                                  disabled={importMutation.isPending}
                                  className="bg-green-600 hover:bg-green-700 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                >
                                  <Plus className="w-3 h-3 mr-1" />
                                  Add
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Videos Section */}
                    {stockVideos.length > 0 && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-3">Videos</h3>
                        <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                          {stockVideos.map((video: StockMedia) => (
                            <div key={video.id} className="group relative aspect-square bg-gray-100 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-200">
                              <img
                                src={video.thumbnail || video.url}
                                alt={video.title}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                              />
                              <div className="absolute top-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                                <Play className="w-3 h-3" />
                              </div>
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-200 flex items-end p-2">
                                <Button
                                  size="sm"
                                  onClick={() => importMutation.mutate(video)}
                                  disabled={importMutation.isPending}
                                  className="bg-green-600 hover:bg-green-700 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                >
                                  <Plus className="w-3 h-3 mr-1" />
                                  Add
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Loading States */}
                    {(loadingPexelsPhotos || loadingPexelsVideos || loadingPixabayPhotos || loadingPixabayVideos) && (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-2"></div>
                        <p className="text-gray-600">Searching stock media...</p>
                      </div>
                    )}

                    {/* No Results */}
                    {stockSearchQuery.length > 0 && stockPhotos.length === 0 && stockVideos.length === 0 && 
                     !loadingPexelsPhotos && !loadingPexelsVideos && !loadingPixabayPhotos && !loadingPixabayVideos && (
                      <div className="text-center py-8">
                        <Globe className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                        <h3 className="text-lg font-medium text-gray-900 mb-1">No results found</h3>
                        <p className="text-gray-600">Try different keywords or check your search terms.</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Search className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 mb-1">Search Stock Media</h3>
                    <p className="text-gray-600">Enter keywords to find high-quality photos and videos.</p>
                  </div>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Stock Media Browser */}
        <StockMediaBrowser
          isOpen={showStockMedia}
          onClose={() => setShowStockMedia(false)}
          onSelectMedia={onSelectMedia}
        />
      </div>
    </div>
  );
}