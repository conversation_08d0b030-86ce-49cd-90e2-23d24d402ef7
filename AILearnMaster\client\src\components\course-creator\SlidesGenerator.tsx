import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, Ta<PERSON>Content } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { FileText, Loader2, Download, Check, AlertCircle, Settings2, Palette, Brain, Sparkles } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Progress } from '@/components/ui/progress';

interface SlidesGeneratorProps {
  courseId: number;
  courseName: string;
}

type SlideTheme = 'professional' | 'creative' | 'minimal' | 'dark' | 'light';
type SlideLayout = 'standard' | 'title-content' | 'title-split' | 'full-image';

export default function SlidesGenerator({ courseId, courseName }: SlidesGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [slideUrl, setSlideUrl] = useState<string | null>(null);
  const [generationError, setGenerationError] = useState<string | null>(null);
  const [selectedTheme, setSelectedTheme] = useState<SlideTheme>('professional');
  const [selectedLayout, setSelectedLayout] = useState<SlideLayout>('standard');
  const [options, setOptions] = useState({
    includeModuleTitles: true,
    includeLessonTitles: true,
    includeMedia: true,
    includeNotes: false,
    autoPaginate: true,
    includeTableOfContents: true,
  });
  const { toast } = useToast();

  const handleOptionChange = (option: keyof typeof options) => {
    setOptions(prev => ({
      ...prev,
      [option]: !prev[option]
    }));
  };

  const handleGenerateSlides = async () => {
    try {
      setIsGenerating(true);
      setProgress(0);
      setGenerationError(null);
      setSlideUrl(null);

      // Start the slide generation process
      const response = await apiRequest('POST', `/api/courses/${courseId}/generate-slides`, {
        theme: selectedTheme,
        layout: selectedLayout,
        options
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to generate slides');
      }

      const data = await response.json();

      // Poll for progress if the generation is async
      if (data.jobId) {
        const pollInterval = setInterval(async () => {
          try {
            const statusResponse = await apiRequest('GET', `/api/slides/status/${data.jobId}`);
            const statusData = await statusResponse.json();

            setProgress(statusData.progress);

            if (statusData.status === 'completed') {
              clearInterval(pollInterval);
              setSlideUrl(statusData.url);
              setIsGenerating(false);
              toast({
                title: 'Slide Generation Complete',
                description: 'Your slides are ready to download!',
                variant: 'default',
              });
            } else if (statusData.status === 'failed') {
              clearInterval(pollInterval);
              setIsGenerating(false);
              setGenerationError(statusData.error || 'Slide generation failed');
              toast({
                title: 'Slide Generation Failed',
                description: statusData.error || 'There was an error generating your slides.',
                variant: 'destructive',
              });
            }
          } catch (error) {
            console.error('Error polling slide generation status:', error);
          }
        }, 2000);
      } else if (data.url) {
        // If result was returned immediately
        setSlideUrl(data.url);
        setIsGenerating(false);
        toast({
          title: 'Slide Generation Complete',
          description: 'Your slides are ready to download!',
          variant: 'default',
        });
      }
    } catch (error) {
      console.error('Error generating slides:', error);
      setIsGenerating(false);
      setGenerationError(error instanceof Error ? error.message : 'Unknown error occurred');
      toast({
        title: 'Slide Generation Failed',
        description: error instanceof Error ? error.message : 'There was an error generating your slides.',
        variant: 'destructive',
      });
    }
  };

  const ThemePreview = ({ theme }: { theme: SlideTheme }) => {
    // Theme color mapping
    const colors: Record<SlideTheme, string> = {
      professional: 'bg-blue-100 border-blue-500',
      creative: 'bg-purple-100 border-purple-500',
      minimal: 'bg-gray-100 border-gray-500',
      dark: 'bg-gray-800 border-gray-900',
      light: 'bg-white border-gray-200',
    };

    const textColors: Record<SlideTheme, string> = {
      professional: 'text-blue-800',
      creative: 'text-purple-800',
      minimal: 'text-gray-800',
      dark: 'text-white',
      light: 'text-gray-800',
    };

    return (
      <div className={`w-16 h-12 rounded border ${colors[theme]} flex flex-col justify-center items-center p-1 overflow-hidden`}>
        <div className={`w-full h-3 ${textColors[theme]} text-[6px] font-bold border-b border-current mb-1`}>Sample Title</div>
        <div className={`w-full h-5 ${textColors[theme]} text-[4px] flex flex-col space-y-[2px]`}>
          <div className="w-full h-[2px] bg-current rounded-full opacity-70" />
          <div className="w-3/4 h-[2px] bg-current rounded-full opacity-70" />
          <div className="w-5/6 h-[2px] bg-current rounded-full opacity-70" />
        </div>
      </div>
    );
  };

  const LayoutPreview = ({ layout }: { layout: SlideLayout }) => {
    // Layout previews
    switch (layout) {
      case 'standard':
        return (
          <div className="w-16 h-12 rounded border border-gray-300 flex flex-col p-1">
            <div className="w-full h-3 bg-gray-200 mb-1" />
            <div className="w-full h-6 bg-gray-100" />
          </div>
        );
      case 'title-content':
        return (
          <div className="w-16 h-12 rounded border border-gray-300 flex flex-col p-1">
            <div className="w-full h-2 bg-gray-200 mb-1" />
            <div className="w-full h-8 bg-gray-100" />
          </div>
        );
      case 'title-split':
        return (
          <div className="w-16 h-12 rounded border border-gray-300 flex flex-col p-1">
            <div className="w-full h-2 bg-gray-200 mb-1" />
            <div className="w-full h-8 flex">
              <div className="w-1/2 h-full bg-gray-300" />
              <div className="w-1/2 h-full bg-gray-100" />
            </div>
          </div>
        );
      case 'full-image':
        return (
          <div className="w-16 h-12 rounded border border-gray-300 relative overflow-hidden">
            <div className="w-full h-full bg-gray-300" />
            <div className="absolute bottom-0 w-full h-3 bg-gray-800 bg-opacity-50" />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-primary" />
          Slide Generation
        </CardTitle>
        <CardDescription>
          Convert your course content into structured JSON slides data
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="design">
          <TabsList className="mb-4">
            <TabsTrigger value="design">
              <Palette className="h-4 w-4 mr-2" />
              Design
            </TabsTrigger>
            <TabsTrigger value="content">
              <Settings2 className="h-4 w-4 mr-2" />
              Content Options
            </TabsTrigger>
          </TabsList>

          <TabsContent value="design">
            <div className="grid gap-4">
              <div>
                <Label htmlFor="theme-select">Select Theme</Label>
                <div className="grid grid-cols-2 gap-4 mt-2">
                  <div>
                    <Select value={selectedTheme} onValueChange={(v) => setSelectedTheme(v as SlideTheme)}>
                      <SelectTrigger id="theme-select">
                        <SelectValue placeholder="Select Theme" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="professional">Professional</SelectItem>
                        <SelectItem value="creative">Creative</SelectItem>
                        <SelectItem value="minimal">Minimal</SelectItem>
                        <SelectItem value="dark">Dark</SelectItem>
                        <SelectItem value="light">Light</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center justify-center">
                    <ThemePreview theme={selectedTheme} />
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <Label htmlFor="layout-select">Select Layout</Label>
                <div className="grid grid-cols-2 gap-4 mt-2">
                  <div>
                    <Select value={selectedLayout} onValueChange={(v) => setSelectedLayout(v as SlideLayout)}>
                      <SelectTrigger id="layout-select">
                        <SelectValue placeholder="Select Layout" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="standard">Standard</SelectItem>
                        <SelectItem value="title-content">Title & Content</SelectItem>
                        <SelectItem value="title-split">Split Layout</SelectItem>
                        <SelectItem value="full-image">Full Image</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center justify-center">
                    <LayoutPreview layout={selectedLayout} />
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="content">
            <ScrollArea className="h-[200px] rounded-md border p-4">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-module-titles" 
                    checked={options.includeModuleTitles}
                    onCheckedChange={() => handleOptionChange('includeModuleTitles')}
                  />
                  <Label 
                    htmlFor="include-module-titles"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Include Module Titles
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-lesson-titles" 
                    checked={options.includeLessonTitles}
                    onCheckedChange={() => handleOptionChange('includeLessonTitles')}
                  />
                  <Label 
                    htmlFor="include-lesson-titles"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Include Lesson Titles
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-media" 
                    checked={options.includeMedia}
                    onCheckedChange={() => handleOptionChange('includeMedia')}
                  />
                  <Label 
                    htmlFor="include-media"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Include Media (Images & Screenshots)
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-notes" 
                    checked={options.includeNotes}
                    onCheckedChange={() => handleOptionChange('includeNotes')}
                  />
                  <Label 
                    htmlFor="include-notes"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Include Speaker Notes
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="auto-paginate" 
                    checked={options.autoPaginate}
                    onCheckedChange={() => handleOptionChange('autoPaginate')}
                  />
                  <Label 
                    htmlFor="auto-paginate"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Auto-Paginate Content
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="include-toc" 
                    checked={options.includeTableOfContents}
                    onCheckedChange={() => handleOptionChange('includeTableOfContents')}
                  />
                  <Label 
                    htmlFor="include-toc"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Include Table of Contents
                  </Label>
                </div>
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>

        {isGenerating && (
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Generating slides...</span>
              <span className="text-sm font-medium">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {generationError && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-red-800">{generationError}</p>
          </div>
        )}

        {slideUrl && !isGenerating && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center justify-between">
            <div className="flex items-center">
              <Check className="h-5 w-5 text-green-500 mr-2" />
              <p className="text-sm text-green-800">Slides successfully generated!</p>
            </div>
            <Button asChild size="sm" className="gap-1">
              <a href={slideUrl} download={`${courseName.replace(/\s+/g, '-').toLowerCase()}-slides.json`}>
                <Download className="h-4 w-4" />
                Download
              </a>
            </Button>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        <p className="text-xs text-muted-foreground">
          Slide generation may take a few minutes for large courses
        </p>
        <Button 
          onClick={handleGenerateSlides} 
          disabled={isGenerating}
          className="gap-1"
        >
          {isGenerating ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <FileText className="h-4 w-4" />
              Generate Slides
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}