import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'wouter';
import { useUser } from '@/hooks/use-user';
import { useMutation } from '@tanstack/react-query';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import {
  User,
  CreditCard,
  Settings,
  LogOut,
  ChevronDown,
  UserCheck,
  Award,
  BookOpen
} from 'lucide-react';
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from '@/components/ui/badge';

export function ProfileDropdown() {
  const { user } = useUser();
  const [, navigate] = useLocation();
  const { toast } = useToast();

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/auth/logout");
      if (!response.ok) {
        throw new Error("Logout failed");
      }
      return true;
    },
    onSuccess: () => {
      // First directly set the auth query data to null to ensure immediate logout state
      queryClient.setQueryData(['/api/auth/me'], null);
      
      // Then invalidate all queries to refresh data
      queryClient.invalidateQueries();
      
      toast({
        title: "Logged out",
        description: "You have been successfully logged out",
      });
      
      // Redirect to landing page
      window.location.href = '/';
    },
    onError: () => {
      toast({
        title: "Logout failed",
        description: "Failed to log out. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  // Get user initials for avatar fallback
  const getInitials = () => {
    if (!user?.name) return 'U';
    
    const nameParts = user.name.split(' ');
    if (nameParts.length === 1) return nameParts[0][0].toUpperCase();
    return (nameParts[0][0] + nameParts[nameParts.length - 1][0]).toUpperCase();
  };

  // Get a color based on username (for consistent avatar colors)
  const getUserColor = () => {
    if (!user?.username) return 'hsl(var(--primary))';
    
    const colors = [
      'hsl(var(--primary))',
      'hsl(var(--primary) / 0.8)',
      '#22c55e',
      '#3b82f6',
      '#a855f7',
      '#ec4899',
      '#f97316',
      '#14b8a6',
    ];
    
    const index = Array.from(user.username).reduce(
      (acc, char) => acc + char.charCodeAt(0), 0
    ) % colors.length;
    
    return colors[index];
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button 
          className="flex items-center rounded-full border border-slate-200 p-1 hover:bg-slate-100 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
          aria-label="User menu"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage src={user?.avatarUrl || ""} alt={user?.name || "User"} />
            <AvatarFallback 
              style={{ backgroundColor: getUserColor(), color: 'white' }}
            >
              {getInitials()}
            </AvatarFallback>
          </Avatar>
          <ChevronDown className="h-4 w-4 text-slate-500 ml-1 mr-1" />
        </button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user?.name || 'Guest'}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user?.email || ''}
            </p>
            <div className="flex items-center mt-1">
              <Badge 
                variant="outline" 
                className="text-xs border-green-500 text-green-600"
              >
                <UserCheck className="h-3 w-3 mr-1" /> 
                {user ? 'Logged in' : 'Guest'}
              </Badge>
              
              {user?.plan && user.plan !== 'free' && (
                <Badge 
                  className="ml-2 text-xs bg-primary/90"
                >
                  <Award className="h-3 w-3 mr-1" /> 
                  {user.plan}
                </Badge>
              )}
            </div>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/profile" className="cursor-pointer">
              <User className="h-4 w-4 mr-2" />
              <span>Profile</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link href="/pricing" className="cursor-pointer">
              <CreditCard className="h-4 w-4 mr-2" />
              <span>Subscription</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link href="/media-library" className="cursor-pointer">
              <BookOpen className="h-4 w-4 mr-2" />
              <span>Media Library</span>
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link href="/profile" className="cursor-pointer">
              <Settings className="h-4 w-4 mr-2" />
              <span>Settings</span>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          className="cursor-pointer text-red-600 focus:text-red-700" 
          onClick={handleLogout}
          disabled={logoutMutation.isPending}
        >
          {logoutMutation.isPending ? (
            <>
              <span className="h-4 w-4 mr-2 animate-spin rounded-full border-b-2 border-red-600" />
              <span>Logging out...</span>
            </>
          ) : (
            <>
              <LogOut className="h-4 w-4 mr-2" />
              <span>Log out</span>
            </>
          )}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}