import { ReactNode } from "react";
import { Link } from "wouter";
import { ArrowUp } from "lucide-react";
import { PublicHeader } from "./PublicHeader";

interface PublicLayoutProps {
  children: ReactNode;
  hideHeader?: boolean;
  hideFooter?: boolean;
}

export function PublicLayout({ children, hideHeader = false, hideFooter = false }: PublicLayoutProps) {
  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header/Navbar - Only show if not explicitly hidden */}
      {!hideHeader && <PublicHeader />}

      {/* Main Content */}
      <main className="flex-grow">
        {children}
      </main>

      {/* Footer - Only show if not explicitly hidden */}
      {!hideFooter && (
        <footer className="bg-gray-900">
          <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
            <div className="xl:grid xl:grid-cols-4 xl:gap-8">
              <div className="grid grid-cols-2 gap-8 xl:col-span-4">
                <div className="md:grid md:grid-cols-2 md:gap-8">
                  <div>
                    <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">
                      PRODUCT
                    </h3>
                    <ul role="list" className="mt-4 space-y-4">
                      <li>
                        <Link to="/product/features" className="text-base text-gray-400 hover:text-gray-300">
                          Features
                        </Link>
                      </li>
                      <li>
                        <Link to="/product/pricing" className="text-base text-gray-400 hover:text-gray-300">
                          Pricing
                        </Link>
                      </li>
                      <li>
                        <Link to="/product/integrations" className="text-base text-gray-400 hover:text-gray-300">
                          Integrations
                        </Link>
                      </li>
                      <li>
                        <Link to="/product/marketplace" className="text-base text-gray-400 hover:text-gray-300">
                          Marketplace
                        </Link>
                      </li>
                    </ul>
                  </div>
                  <div className="mt-12 md:mt-0">
                    <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">
                      SUPPORT
                    </h3>
                    <ul role="list" className="mt-4 space-y-4">
                      <li>
                        <Link to="/support/documentation" className="text-base text-gray-400 hover:text-gray-300">
                          Documentation
                        </Link>
                      </li>
                      <li>
                        <Link to="/support/tutorials" className="text-base text-gray-400 hover:text-gray-300">
                          Tutorials
                        </Link>
                      </li>
                      <li>
                        <Link to="/support/help-center" className="text-base text-gray-400 hover:text-gray-300">
                          Help Center
                        </Link>
                      </li>
                      <li>
                        <Link to="/support/contact-us" className="text-base text-gray-400 hover:text-gray-300">
                          Contact Us
                        </Link>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="md:grid md:grid-cols-2 md:gap-8">
                  <div>
                    <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">
                      COMPANY
                    </h3>
                    <ul role="list" className="mt-4 space-y-4">
                      <li>
                        <Link to="/company/about-us" className="text-base text-gray-400 hover:text-gray-300">
                          About Us
                        </Link>
                      </li>
                      <li>
                        <Link to="/company/blog" className="text-base text-gray-400 hover:text-gray-300">
                          Blog
                        </Link>
                      </li>
                      <li>
                        <Link to="/company/careers" className="text-base text-gray-400 hover:text-gray-300">
                          Careers
                        </Link>
                      </li>
                      <li>
                        <Link to="/company/press" className="text-base text-gray-400 hover:text-gray-300">
                          Press
                        </Link>
                      </li>
                    </ul>
                  </div>
                  <div className="mt-12 md:mt-0">
                    <h3 className="text-sm font-semibold text-gray-300 tracking-wider uppercase">
                      LEGAL
                    </h3>
                    <ul role="list" className="mt-4 space-y-4">
                      <li>
                        <Link to="/legal/privacy-policy" className="text-base text-gray-400 hover:text-gray-300">
                          Privacy Policy
                        </Link>
                      </li>
                      <li>
                        <Link to="/legal/terms-of-service" className="text-base text-gray-400 hover:text-gray-300">
                          Terms of Service
                        </Link>
                      </li>
                      <li>
                        <Link to="/legal/cookie-policy" className="text-base text-gray-400 hover:text-gray-300">
                          Cookie Policy
                        </Link>
                      </li>
                      <li>
                        <Link to="/legal/gdpr" className="text-base text-gray-400 hover:text-gray-300">
                          GDPR
                        </Link>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-12 border-t border-gray-700 pt-8">
              <div className="flex items-center justify-center mb-4">
                <button 
                  onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                  className="flex items-center justify-center p-2 bg-primary/20 hover:bg-primary/30 rounded-full text-white transition-colors duration-200"
                  title="Back to top"
                >
                  <ArrowUp className="h-5 w-5" />
                </button>
              </div>
              <p className="text-base text-gray-400 xl:text-center">
                &copy; {new Date().getFullYear()} Koursia. All rights reserved.
              </p>
            </div>
          </div>
        </footer>
      )}
    </div>
  );
}