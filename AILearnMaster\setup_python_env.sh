#!/bin/bash

# Python Environment Setup Script
# This script properly initializes the Python environment for Modal development

echo "Setting up Python environment for Modal..."

# Check if we have any Python executable available
PYTHON_CMD=""

# Try different Python commands
for cmd in python3.11 python3 python; do
    if command -v $cmd &> /dev/null; then
        PYTHON_CMD=$cmd
        echo "Found Python: $cmd"
        break
    fi
done

# If no system Python found, try to find one in Nix store
if [ -z "$PYTHON_CMD" ]; then
    echo "No system Python found, searching Nix store..."
    
    # Look for Python in common Nix store locations
    for python_path in $(find /nix/store -maxdepth 2 -name "python3*" -type f -executable 2>/dev/null | head -5); do
        if [ -x "$python_path" ]; then
            PYTHON_CMD="$python_path"
            echo "Found Nix Python: $python_path"
            break
        fi
    done
fi

if [ -n "$PYTHON_CMD" ]; then
    echo "Using Python: $PYTHON_CMD"
    
    # Check Python version
    $PYTHON_CMD --version
    
    # Try to install Modal
    echo "Installing Modal..."
    $PYTHON_CMD -m pip install modal --user --upgrade 2>/dev/null || {
        echo "Pip install failed, trying alternative method..."
        $PYTHON_CMD -c "
import subprocess
import sys
try:
    subprocess.check_call([sys.executable, '-m', 'ensurepip', '--user'])
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'modal', '--user'])
    print('Modal installed successfully')
except Exception as e:
    print(f'Installation failed: {e}')
"
    }
    
    # Test Modal import
    echo "Testing Modal import..."
    $PYTHON_CMD -c "
try:
    import modal
    print('SUCCESS: Modal imported successfully!')
except ImportError as e:
    print(f'FAILED: Modal import failed - {e}')
except Exception as e:
    print(f'ERROR: {e}')
"
    
    # Run the modal example if import works
    echo "Running modal_example.py..."
    $PYTHON_CMD modal_example.py
    
else
    echo "ERROR: No Python interpreter found"
    echo "Please ensure Python is properly installed in your environment"
    exit 1
fi