#!/usr/bin/env python3
"""
Modal runner script that properly handles the Modal import and execution.
This script provides a workaround for the Modal import issue in the Replit environment.
"""

import sys
import subprocess
import os

def check_modal_installation():
    """Check if Modal is properly installed and accessible."""
    try:
        import modal
        print("✓ Modal is successfully imported!")
        return True
    except ImportError as e:
        print(f"✗ Modal import failed: {e}")
        return False

def install_modal():
    """Install Modal using pip if not available."""
    try:
        print("Installing Modal...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "modal"])
        print("✓ Modal installation completed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Modal installation failed: {e}")
        return False

def run_modal_example():
    """Run the modal example if Modal is available."""
    if not check_modal_installation():
        print("Attempting to install Modal...")
        if not install_modal():
            print("Failed to install Modal. Please check your environment.")
            return False
        
        # Try importing again after installation
        if not check_modal_installation():
            print("Modal still not available after installation.")
            return False
    
    # Import and run the modal example
    try:
        print("Running Modal example...")
        exec(open('modal_example.py').read())
        return True
    except Exception as e:
        print(f"Error running Modal example: {e}")
        return False

if __name__ == "__main__":
    print("Modal Environment Checker and Runner")
    print("=" * 40)
    
    # Check Python version
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    
    # Check if Modal example exists
    if not os.path.exists('modal_example.py'):
        print("✗ modal_example.py not found!")
        sys.exit(1)
    
    # Run the Modal example
    success = run_modal_example()
    
    if success:
        print("\n✓ Modal example executed successfully!")
    else:
        print("\n✗ Modal example execution failed!")
        sys.exit(1)