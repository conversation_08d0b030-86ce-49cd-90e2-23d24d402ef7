# Frontend Integration Guide

## 🔗 Connecting React/Next.js to Modal GPU Services

This guide shows how to integrate the deployed Modal GPU services with your React/Next.js frontend.

## 📡 API Service Configuration

### Environment Variables
Create a `.env.local` file in your Next.js project:

```env
# Modal GPU API Endpoints
NEXT_PUBLIC_MODAL_HEALTH_URL=https://trade-digital--courseai-a100-simple-health.modal.run
NEXT_PUBLIC_MODAL_MISTRAL_URL=https://trade-digital--courseai-a100-simple-api-mistral.modal.run
NEXT_PUBLIC_MODAL_TTS_URL=https://trade-digital--courseai-a100-simple-api-tts.modal.run
NEXT_PUBLIC_MODAL_VOICES_URL=https://trade-digital--courseai-a100-simple-api-voices.modal.run
NEXT_PUBLIC_MODAL_SLIDES_URL=https://trade-digital--courseai-a100-simple-api-slides.modal.run
```

## 🛠️ API Service Layer

### Create API Service (`lib/modalApi.js`)

```javascript
class ModalAPIService {
  constructor() {
    this.baseUrls = {
      health: process.env.NEXT_PUBLIC_MODAL_HEALTH_URL,
      mistral: process.env.NEXT_PUBLIC_MODAL_MISTRAL_URL,
      tts: process.env.NEXT_PUBLIC_MODAL_TTS_URL,
      voices: process.env.NEXT_PUBLIC_MODAL_VOICES_URL,
      slides: process.env.NEXT_PUBLIC_MODAL_SLIDES_URL,
    };
  }

  async checkHealth() {
    try {
      const response = await fetch(this.baseUrls.health);
      return await response.json();
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }

  async generateText(prompt, maxTokens = 200, temperature = 0.7) {
    try {
      const response = await fetch(this.baseUrls.mistral, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          max_tokens: maxTokens,
          temperature,
        }),
      });
      return await response.json();
    } catch (error) {
      console.error('Text generation failed:', error);
      throw error;
    }
  }

  async generateSpeech(text, voiceId = 'tts_models/en/ljspeech/tacotron2-DDC') {
    try {
      const response = await fetch(this.baseUrls.tts, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          voice_id: voiceId,
        }),
      });
      return await response.json();
    } catch (error) {
      console.error('Speech generation failed:', error);
      throw error;
    }
  }

  async getAvailableVoices() {
    try {
      const response = await fetch(this.baseUrls.voices);
      return await response.json();
    } catch (error) {
      console.error('Voice discovery failed:', error);
      throw error;
    }
  }

  async generateSlides(markdown, theme = 'default') {
    try {
      const response = await fetch(this.baseUrls.slides, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          markdown,
          theme,
        }),
      });
      return await response.json();
    } catch (error) {
      console.error('Slide generation failed:', error);
      throw error;
    }
  }
}

export const modalAPI = new ModalAPIService();
```

## 🎯 React Hooks for AI Services

### Custom Hooks (`hooks/useModalAI.js`)

```javascript
import { useState, useCallback } from 'react';
import { modalAPI } from '../lib/modalApi';

export function useTextGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateText = useCallback(async (prompt, options = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await modalAPI.generateText(
        prompt,
        options.maxTokens,
        options.temperature
      );
      
      if (result.success) {
        return result.generated_text;
      } else {
        throw new Error(result.error || 'Text generation failed');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { generateText, loading, error };
}

export function useSpeechGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateSpeech = useCallback(async (text, voiceId) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await modalAPI.generateSpeech(text, voiceId);
      
      if (result.success) {
        // Convert base64 to audio blob
        const audioData = atob(result.audio_base64);
        const audioArray = new Uint8Array(audioData.length);
        for (let i = 0; i < audioData.length; i++) {
          audioArray[i] = audioData.charCodeAt(i);
        }
        const audioBlob = new Blob([audioArray], { type: 'audio/wav' });
        return URL.createObjectURL(audioBlob);
      } else {
        throw new Error(result.error || 'Speech generation failed');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { generateSpeech, loading, error };
}

export function useSlideGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateSlides = useCallback(async (markdown, theme) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await modalAPI.generateSlides(markdown, theme);
      
      if (result.success) {
        return result.html_content;
      } else {
        throw new Error(result.error || 'Slide generation failed');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { generateSlides, loading, error };
}
```

## 🎨 Example React Components

### Course Content Generator Component

```jsx
import { useState } from 'react';
import { useTextGeneration } from '../hooks/useModalAI';

export function CourseContentGenerator() {
  const [topic, setTopic] = useState('');
  const [content, setContent] = useState('');
  const { generateText, loading, error } = useTextGeneration();

  const handleGenerate = async () => {
    if (!topic.trim()) return;
    
    try {
      const prompt = `Create a comprehensive lesson about ${topic}:`;
      const generatedContent = await generateText(prompt, {
        maxTokens: 300,
        temperature: 0.7
      });
      setContent(generatedContent);
    } catch (err) {
      console.error('Generation failed:', err);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Course Content Generator</h2>
      
      <div className="mb-4">
        <input
          type="text"
          value={topic}
          onChange={(e) => setTopic(e.target.value)}
          placeholder="Enter course topic..."
          className="w-full p-3 border rounded-lg"
        />
      </div>
      
      <button
        onClick={handleGenerate}
        disabled={loading || !topic.trim()}
        className="bg-blue-500 text-white px-6 py-2 rounded-lg disabled:opacity-50"
      >
        {loading ? 'Generating...' : 'Generate Content'}
      </button>
      
      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-lg">
          Error: {error}
        </div>
      )}
      
      {content && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold mb-2">Generated Content:</h3>
          <p className="whitespace-pre-wrap">{content}</p>
        </div>
      )}
    </div>
  );
}
```

### Audio Player Component

```jsx
import { useState, useRef } from 'react';
import { useSpeechGeneration } from '../hooks/useModalAI';

export function TextToSpeechPlayer({ text }) {
  const [audioUrl, setAudioUrl] = useState(null);
  const audioRef = useRef(null);
  const { generateSpeech, loading, error } = useSpeechGeneration();

  const handleGenerateAudio = async () => {
    try {
      const url = await generateSpeech(text);
      setAudioUrl(url);
    } catch (err) {
      console.error('Audio generation failed:', err);
    }
  };

  return (
    <div className="flex items-center space-x-4">
      <button
        onClick={handleGenerateAudio}
        disabled={loading || !text}
        className="bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50"
      >
        {loading ? 'Generating...' : '🔊 Generate Audio'}
      </button>
      
      {audioUrl && (
        <audio ref={audioRef} controls src={audioUrl} className="flex-1">
          Your browser does not support audio playback.
        </audio>
      )}
      
      {error && (
        <span className="text-red-500 text-sm">{error}</span>
      )}
    </div>
  );
}
```

## 🚀 Integration Checklist

### ✅ **Setup Steps**
1. Add environment variables to `.env.local`
2. Install required dependencies: `npm install`
3. Create API service layer (`lib/modalApi.js`)
4. Create custom hooks (`hooks/useModalAI.js`)
5. Implement components using the hooks

### ✅ **Testing Integration**
1. Test health check endpoint first
2. Verify text generation with simple prompts
3. Test slide generation with basic markdown
4. Validate voice discovery functionality
5. Test TTS with short text samples

### ✅ **Error Handling**
1. Implement loading states for all AI operations
2. Add error boundaries for component failures
3. Provide fallback content when services are unavailable
4. Add retry logic for failed requests

### ✅ **Performance Optimization**
1. Cache generated content when appropriate
2. Implement request debouncing for user inputs
3. Use React.memo for expensive components
4. Consider lazy loading for AI-heavy features

## 🔧 Troubleshooting

### Common Issues
- **CORS Errors**: Modal endpoints should allow cross-origin requests
- **Timeout Issues**: Increase timeout for TTS and text generation
- **Rate Limiting**: Implement request queuing for high usage
- **Audio Playback**: Ensure proper MIME types for generated audio

### Debug Tools
- Use browser dev tools to monitor network requests
- Check Modal dashboard for service health and logs
- Implement client-side logging for API interactions
- Test endpoints directly with curl/Postman first

---

**Ready to integrate!** Your Modal GPU services are deployed and ready for frontend integration.
