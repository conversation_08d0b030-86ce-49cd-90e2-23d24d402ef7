# Frontend Integration Guide

## 🔗 Connecting React/Next.js to Modal GPU Services

This guide shows how to integrate the deployed Modal GPU services with your React/Next.js frontend.

## 📡 API Service Configuration

### Environment Variables
Create a `.env.local` file in your Next.js project:

```env
# Modal GPU API Endpoints
NEXT_PUBLIC_MODAL_HEALTH_URL=https://trade-digital--courseai-a100-simple-health.modal.run
NEXT_PUBLIC_MODAL_MISTRAL_URL=https://trade-digital--courseai-a100-simple-api-mistral.modal.run
NEXT_PUBLIC_MODAL_TTS_URL=https://trade-digital--courseai-a100-simple-api-tts.modal.run
NEXT_PUBLIC_MODAL_VOICES_URL=https://trade-digital--courseai-a100-simple-api-voices.modal.run
NEXT_PUBLIC_MODAL_SLIDES_URL=https://trade-digital--courseai-a100-simple-api-slides.modal.run
NEXT_PUBLIC_MODAL_AVATAR_URL=https://trade-digital--courseai-a100-simple-api-avatar.modal.run
NEXT_PUBLIC_MODAL_COURSE_AVATAR_URL=https://trade-digital--courseai-a100-simple-api-course-avatar.modal.run
```

## 🛠️ API Service Layer

### Create API Service (`lib/modalApi.js`)

```javascript
class ModalAPIService {
  constructor() {
    this.baseUrls = {
      health: process.env.NEXT_PUBLIC_MODAL_HEALTH_URL,
      mistral: process.env.NEXT_PUBLIC_MODAL_MISTRAL_URL,
      tts: process.env.NEXT_PUBLIC_MODAL_TTS_URL,
      voices: process.env.NEXT_PUBLIC_MODAL_VOICES_URL,
      slides: process.env.NEXT_PUBLIC_MODAL_SLIDES_URL,
      avatar: process.env.NEXT_PUBLIC_MODAL_AVATAR_URL,
      courseAvatar: process.env.NEXT_PUBLIC_MODAL_COURSE_AVATAR_URL,
    };
  }

  async checkHealth() {
    try {
      const response = await fetch(this.baseUrls.health);
      return await response.json();
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }

  async generateText(prompt, maxTokens = 200, temperature = 0.7) {
    try {
      const response = await fetch(this.baseUrls.mistral, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          max_tokens: maxTokens,
          temperature,
        }),
      });
      return await response.json();
    } catch (error) {
      console.error('Text generation failed:', error);
      throw error;
    }
  }

  async generateSpeech(text, voiceId = 'tts_models/en/ljspeech/tacotron2-DDC') {
    try {
      const response = await fetch(this.baseUrls.tts, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          voice_id: voiceId,
        }),
      });
      return await response.json();
    } catch (error) {
      console.error('Speech generation failed:', error);
      throw error;
    }
  }

  async getAvailableVoices() {
    try {
      const response = await fetch(this.baseUrls.voices);
      return await response.json();
    } catch (error) {
      console.error('Voice discovery failed:', error);
      throw error;
    }
  }

  async generateSlides(markdown, theme = 'default') {
    try {
      const response = await fetch(this.baseUrls.slides, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          markdown,
          theme,
        }),
      });
      return await response.json();
    } catch (error) {
      console.error('Slide generation failed:', error);
      throw error;
    }
  }

  async generateAvatar(refImageBase64, audioBase64, config = {}) {
    try {
      const response = await fetch(this.baseUrls.avatar, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ref_image_base64: refImageBase64,
          audio_base64: audioBase64,
          config,
        }),
      });
      return await response.json();
    } catch (error) {
      console.error('Avatar generation failed:', error);
      throw error;
    }
  }

  async generateCourseAvatar(prompt, refImageBase64, voiceId = 'tts_models/en/ljspeech/tacotron2-DDC', config = {}) {
    try {
      const response = await fetch(this.baseUrls.courseAvatar, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          ref_image_base64: refImageBase64,
          voice_id: voiceId,
          config,
        }),
      });
      return await response.json();
    } catch (error) {
      console.error('Course avatar generation failed:', error);
      throw error;
    }
  }
}

export const modalAPI = new ModalAPIService();
```

## 🎯 React Hooks for AI Services

### Custom Hooks (`hooks/useModalAI.js`)

```javascript
import { useState, useCallback } from 'react';
import { modalAPI } from '../lib/modalApi';

export function useTextGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateText = useCallback(async (prompt, options = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await modalAPI.generateText(
        prompt,
        options.maxTokens,
        options.temperature
      );
      
      if (result.success) {
        return result.generated_text;
      } else {
        throw new Error(result.error || 'Text generation failed');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { generateText, loading, error };
}

export function useSpeechGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateSpeech = useCallback(async (text, voiceId) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await modalAPI.generateSpeech(text, voiceId);
      
      if (result.success) {
        // Convert base64 to audio blob
        const audioData = atob(result.audio_base64);
        const audioArray = new Uint8Array(audioData.length);
        for (let i = 0; i < audioData.length; i++) {
          audioArray[i] = audioData.charCodeAt(i);
        }
        const audioBlob = new Blob([audioArray], { type: 'audio/wav' });
        return URL.createObjectURL(audioBlob);
      } else {
        throw new Error(result.error || 'Speech generation failed');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { generateSpeech, loading, error };
}

export function useSlideGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateSlides = useCallback(async (markdown, theme) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await modalAPI.generateSlides(markdown, theme);
      
      if (result.success) {
        return result.html_content;
      } else {
        throw new Error(result.error || 'Slide generation failed');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { generateSlides, loading, error };
}

export function useAvatarGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateAvatar = useCallback(async (refImageBase64, audioBase64, config) => {
    setLoading(true);
    setError(null);

    try {
      const result = await modalAPI.generateAvatar(refImageBase64, audioBase64, config);

      if (result.success) {
        return result.video_base64;
      } else {
        throw new Error(result.error || 'Avatar generation failed');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { generateAvatar, loading, error };
}

export function useCourseAvatarGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const generateCourseAvatar = useCallback(async (prompt, refImageBase64, voiceId, config) => {
    setLoading(true);
    setError(null);

    try {
      const result = await modalAPI.generateCourseAvatar(prompt, refImageBase64, voiceId, config);

      if (result.success) {
        return {
          generatedText: result.generated_text,
          videoBase64: result.video_base64,
          videoInfo: result.video_info,
          audioInfo: result.audio_info
        };
      } else {
        throw new Error(result.error || 'Course avatar generation failed');
      }
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return { generateCourseAvatar, loading, error };
}
```

## 🎨 Example React Components

### Course Content Generator Component

```jsx
import { useState } from 'react';
import { useTextGeneration } from '../hooks/useModalAI';

export function CourseContentGenerator() {
  const [topic, setTopic] = useState('');
  const [content, setContent] = useState('');
  const { generateText, loading, error } = useTextGeneration();

  const handleGenerate = async () => {
    if (!topic.trim()) return;
    
    try {
      const prompt = `Create a comprehensive lesson about ${topic}:`;
      const generatedContent = await generateText(prompt, {
        maxTokens: 300,
        temperature: 0.7
      });
      setContent(generatedContent);
    } catch (err) {
      console.error('Generation failed:', err);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Course Content Generator</h2>
      
      <div className="mb-4">
        <input
          type="text"
          value={topic}
          onChange={(e) => setTopic(e.target.value)}
          placeholder="Enter course topic..."
          className="w-full p-3 border rounded-lg"
        />
      </div>
      
      <button
        onClick={handleGenerate}
        disabled={loading || !topic.trim()}
        className="bg-blue-500 text-white px-6 py-2 rounded-lg disabled:opacity-50"
      >
        {loading ? 'Generating...' : 'Generate Content'}
      </button>
      
      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-lg">
          Error: {error}
        </div>
      )}
      
      {content && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold mb-2">Generated Content:</h3>
          <p className="whitespace-pre-wrap">{content}</p>
        </div>
      )}
    </div>
  );
}
```

### Audio Player Component

```jsx
import { useState, useRef } from 'react';
import { useSpeechGeneration } from '../hooks/useModalAI';

export function TextToSpeechPlayer({ text }) {
  const [audioUrl, setAudioUrl] = useState(null);
  const audioRef = useRef(null);
  const { generateSpeech, loading, error } = useSpeechGeneration();

  const handleGenerateAudio = async () => {
    try {
      const url = await generateSpeech(text);
      setAudioUrl(url);
    } catch (err) {
      console.error('Audio generation failed:', err);
    }
  };

  return (
    <div className="flex items-center space-x-4">
      <button
        onClick={handleGenerateAudio}
        disabled={loading || !text}
        className="bg-green-500 text-white px-4 py-2 rounded disabled:opacity-50"
      >
        {loading ? 'Generating...' : '🔊 Generate Audio'}
      </button>

      {audioUrl && (
        <audio ref={audioRef} controls src={audioUrl} className="flex-1">
          Your browser does not support audio playbook.
        </audio>
      )}

      {error && (
        <span className="text-red-500 text-sm">{error}</span>
      )}
    </div>
  );
}
```

### Avatar Generation Component

```jsx
import { useState, useRef } from 'react';
import { useCourseAvatarGeneration } from '../hooks/useModalAI';

export function CourseAvatarGenerator() {
  const [prompt, setPrompt] = useState('');
  const [avatarImage, setAvatarImage] = useState(null);
  const [generatedVideo, setGeneratedVideo] = useState(null);
  const [generatedText, setGeneratedText] = useState('');
  const { generateCourseAvatar, loading, error } = useCourseAvatarGeneration();

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64 = e.target.result.split(',')[1]; // Remove data:image/... prefix
        setAvatarImage(base64);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleGenerateAvatar = async () => {
    if (!prompt.trim() || !avatarImage) return;

    try {
      const result = await generateCourseAvatar(
        prompt,
        avatarImage,
        'tts_models/en/ljspeech/tacotron2-DDC',
        {
          width: 512,
          height: 512,
          fps: 24,
          max_frames: 120
        }
      );

      // Convert base64 video to blob URL
      const videoData = atob(result.videoBase64);
      const videoArray = new Uint8Array(videoData.length);
      for (let i = 0; i < videoData.length; i++) {
        videoArray[i] = videoData.charCodeAt(i);
      }
      const videoBlob = new Blob([videoArray], { type: 'video/mp4' });
      const videoUrl = URL.createObjectURL(videoBlob);

      setGeneratedVideo(videoUrl);
      setGeneratedText(result.generatedText);
    } catch (err) {
      console.error('Avatar generation failed:', err);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Course Avatar Generator</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Course Topic</label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Enter course topic or content..."
              className="w-full p-3 border rounded-lg h-32"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Avatar Image</label>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="w-full p-3 border rounded-lg"
            />
          </div>

          <button
            onClick={handleGenerateAvatar}
            disabled={loading || !prompt.trim() || !avatarImage}
            className="w-full bg-blue-500 text-white px-6 py-3 rounded-lg disabled:opacity-50"
          >
            {loading ? 'Generating Avatar...' : '🎭 Generate Course Avatar'}
          </button>

          {error && (
            <div className="p-3 bg-red-100 text-red-700 rounded-lg">
              Error: {error}
            </div>
          )}
        </div>

        {/* Output Section */}
        <div className="space-y-4">
          {generatedText && (
            <div>
              <h3 className="font-semibold mb-2">Generated Content:</h3>
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm">{generatedText}</p>
              </div>
            </div>
          )}

          {generatedVideo && (
            <div>
              <h3 className="font-semibold mb-2">Avatar Video:</h3>
              <video
                src={generatedVideo}
                controls
                className="w-full rounded-lg"
                style={{ maxHeight: '400px' }}
              >
                Your browser does not support video playback.
              </video>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
```

## 🚀 Integration Checklist

### ✅ **Setup Steps**
1. Add environment variables to `.env.local`
2. Install required dependencies: `npm install`
3. Create API service layer (`lib/modalApi.js`)
4. Create custom hooks (`hooks/useModalAI.js`)
5. Implement components using the hooks

### ✅ **Testing Integration**
1. Test health check endpoint first
2. Verify text generation with simple prompts
3. Test slide generation with basic markdown
4. Validate voice discovery functionality
5. Test TTS with short text samples

### ✅ **Error Handling**
1. Implement loading states for all AI operations
2. Add error boundaries for component failures
3. Provide fallback content when services are unavailable
4. Add retry logic for failed requests

### ✅ **Performance Optimization**
1. Cache generated content when appropriate
2. Implement request debouncing for user inputs
3. Use React.memo for expensive components
4. Consider lazy loading for AI-heavy features

## 🔧 Troubleshooting

### Common Issues
- **CORS Errors**: Modal endpoints should allow cross-origin requests
- **Timeout Issues**: Increase timeout for TTS and text generation
- **Rate Limiting**: Implement request queuing for high usage
- **Audio Playback**: Ensure proper MIME types for generated audio

### Debug Tools
- Use browser dev tools to monitor network requests
- Check Modal dashboard for service health and logs
- Implement client-side logging for API interactions
- Test endpoints directly with curl/Postman first

---

**Ready to integrate!** Your Modal GPU services are deployed and ready for frontend integration.
