import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import NotFound from "@/pages/not-found";
import Dashboard from "@/pages/dashboard";
import AdminDashboard from "@/pages/admin-dashboard";
import PricingPage from "@/pages/pricing";
import ProfilePage from "@/pages/profile";
import VideoGeneratorPage from "@/pages/video-generator";
import ApiTestPage from "@/pages/api-test";
import MicroLearningDemo from "@/pages/micro-learning-demo";
import Checkout from "@/pages/checkout";
import PaymentSuccess from "@/pages/payment-success";
import StripeTest from "@/pages/stripe-test";
import MediaLibraryPage from "@/pages/media-library";
import AITemplatesPage from "@/pages/ai-templates";
import MyCoursesPage from "@/pages/my-courses";
import AuthPage from "@/pages/auth-page";
import MarketplacePage from "@/pages/marketplace-page";
import TTSTest from "@/pages/tts-test";
import SettingsPage from "@/pages/settings";
import LandingPage from "@/pages/landing-page";
import { AuthProvider, useAuth } from "@/hooks/use-auth";
import { MainLayout } from "./components/layouts/MainLayout";
import { ProtectedRoute } from "./components/protected-route";

function LoadingScreen() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-slate-50">
      <div className="flex flex-col items-center">
        <div className="w-12 h-12 rounded-md bg-primary flex items-center justify-center mb-4">
          <svg className="animate-spin h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <p className="text-slate-600">Loading CourseAI...</p>
      </div>
    </div>
  );
}

function AppContent() {
  const { user, isLoading } = useAuth();
  
  return (
    <>
      <Switch>
        {/* Landing page (publicly accessible) */}
        <Route path="/">
          {() => <LandingPage />}
        </Route>

        {/* Auth page (publicly accessible) */}
        <Route path="/auth">
          {() => <AuthPage />}
        </Route>
        
        {/* Dashboard route (protected) */}
        <Route path="/dashboard">
          {() => (
            <ProtectedRoute>
              <MainLayout user={user} isLoading={isLoading}>
                <Dashboard />
              </MainLayout>
            </ProtectedRoute>
          )}
        </Route>
        
        {/* Admin dashboard route (admin only) */}
        <Route path="/admin-dashboard">
          {() => (
            <ProtectedRoute adminOnly>
              <MainLayout user={user} isLoading={isLoading}>
                <AdminDashboard />
              </MainLayout>
            </ProtectedRoute>
          )}
        </Route>
        
        {/* Protected routes */}
        <Route>
          {(params) => (
            <ProtectedRoute>
              <MainLayout user={user} isLoading={isLoading}>
                <Switch>
                  <Route path="/pricing">
                    {() => <PricingPage />}
                  </Route>
                  <Route path="/profile">
                    {() => <ProfilePage />}
                  </Route>
                  <Route path="/settings">
                    {() => <SettingsPage />}
                  </Route>
                  <Route path="/video-generator">
                    {() => <VideoGeneratorPage />}
                  </Route>
                  <Route path="/api-test">
                    {() => <ApiTestPage />}
                  </Route>
                  <Route path="/micro-learning-demo">
                    {() => <MicroLearningDemo />}
                  </Route>
                  <Route path="/checkout">
                    {() => <Checkout />}
                  </Route>
                  <Route path="/payment-success">
                    {() => <PaymentSuccess />}
                  </Route>
                  <Route path="/stripe-test">
                    {() => <StripeTest />}
                  </Route>
                  <Route path="/media-library">
                    {() => <MediaLibraryPage />}
                  </Route>
                  <Route path="/marketplace">
                    {() => <MarketplacePage />}
                  </Route>
                  <Route path="/tts-test">
                    {() => <TTSTest />}
                  </Route>
                  <Route path="/ai-templates">
                    {() => <AITemplatesPage />}
                  </Route>
                  <Route path="/my-courses">
                    {() => <MyCoursesPage />}
                  </Route>
                  {/* Fallback to 404 */}
                  <Route>
                    {() => <NotFound />}
                  </Route>
                </Switch>
              </MainLayout>
            </ProtectedRoute>
          )}
        </Route>
      </Switch>
    </>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <AppContent />
        <Toaster />
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;