# Mistral AI on Modal GPU Setup Instructions

This guide will help you deploy Mistral AI on Modal GPU infrastructure for high-performance course generation in your Koursia Platform.

## Prerequisites

1. **Modal Account**: Sign up at [modal.com](https://modal.com)
2. **Modal CLI**: Install the Modal command-line interface
3. **Python**: Python 3.8+ installed locally

## Step 1: Install Modal CLI

```bash
pip install modal
```

## Step 2: Authenticate with Modal

```bash
modal token new
```

This will open your browser to authenticate with your Modal account.

## Step 3: Deploy the Mistral AI Application

1. **Deploy the application**:
```bash
modal deploy modal_mistral_app.py
```

2. **Get your application URL**:
After deployment, Modal will provide you with URLs like:
- `https://your-username--koursia-mistral-ai-mistral-generate.modal.run`
- `https://your-username--koursia-mistral-ai-course-structure.modal.run`
- etc.

## Step 4: Configure Environment Variables

Add these environment variables to your `.env` file:

```env
# Modal Configuration for Mistral AI
MODAL_BASE_URL=https://your-username--koursia-mistral-ai.modal.run
MODAL_API_KEY=your-modal-api-key

# Optional: Enable AI generation (default: true)
USE_MISTRAL_AI=true
```

## Step 5: Test the Deployment

1. **Test the health endpoint**:
```bash
curl https://your-username--koursia-mistral-ai-health.modal.run
```

2. **Test course structure generation**:
```bash
curl -X POST https://your-username--koursia-mistral-ai-course-structure.modal.run \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Web Development Fundamentals",
    "description": "Learn modern web development",
    "category": "Technology",
    "targetAudience": "Beginners"
  }'
```

## Step 6: Verify Integration

1. Log into your Koursia Platform
2. Try creating a traditional course
3. Check that course structure generation works properly
4. Verify that AI-generated content appears instead of template content

## Available Endpoints

Your deployed Modal application will have these endpoints:

- **`/mistral-generate`**: General AI text generation
- **`/course-structure`**: Generate comprehensive course structures
- **`/course-description`**: Generate course descriptions
- **`/lesson-script`**: Generate lesson scripts for TTS
- **`/health`**: Health check endpoint

## Cost Optimization

The deployment includes several cost optimizations:

1. **A100-40GB GPU**: Balanced performance and cost
2. **Container Idle Timeout**: 240 seconds to avoid unnecessary charges
3. **8-bit Model Loading**: Reduces memory usage
4. **Concurrent Inputs**: Up to 10 requests can be processed simultaneously

## Troubleshooting

### Common Issues

1. **Authentication Errors**:
   - Run `modal token new` again
   - Check that you're logged into the correct Modal account

2. **Deployment Failures**:
   - Ensure Python 3.8+ is installed
   - Check that all dependencies are available
   - Verify your Modal account has GPU access

3. **API Errors**:
   - Check that MODAL_BASE_URL is correct
   - Verify the endpoints are responding
   - Check Modal dashboard for error logs

### Fallback Mode

If Modal is not configured or unavailable, the system automatically falls back to template-based course generation. You'll see this message in logs:

```
Using fallback course structure generation
```

## GPU Usage and Billing

- **GPU Type**: A100-40GB (high-performance)
- **Billing**: Pay-per-second when GPU is active
- **Auto-scaling**: Containers start on demand
- **Timeout**: Containers stop after 240 seconds of inactivity

## Next Steps

Once deployed, your Koursia Platform will use Mistral AI for:

1. **Course Structure Generation**: AI-powered course outlines
2. **Content Creation**: Lesson descriptions and learning objectives
3. **Script Writing**: Detailed lesson scripts for TTS
4. **Description Generation**: Compelling course descriptions

All AI generation will now use Mistral-7B-Instruct running on A100 GPU for high-quality, fast content generation.

## Support

If you encounter issues:

1. Check Modal dashboard for logs
2. Verify environment variables are set correctly
3. Test endpoints directly using curl
4. Check the fallback mode is working

The system is designed to be resilient - if Modal is unavailable, it will continue working with template-based generation until the issue is resolved.