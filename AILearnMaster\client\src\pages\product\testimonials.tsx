import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  <PERSON>uote, Star, ArrowRight, Sparkles, Users, 
  TrendingUp, Award, CheckCircle, Play,
  FileVideo, Mic, Brain, Globe
} from "lucide-react";
import { <PERSON> } from "wouter";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.5 }
  }
};

export default function TestimonialsPage() {
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Online Course Creator",
      company: "Tech Skills Academy",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      quote: "CourseAI revolutionized my course creation process. What used to take weeks now takes hours. The SadTalker avatar feature is absolutely incredible - my students love the personal touch it adds to every lesson.",
      metrics: {
        coursesSold: "50K+",
        timeReduction: "85%",
        engagement: "+40%"
      },
      category: "Education"
    },
    {
      id: 2,
      name: "Marcus Rodriguez",
      role: "Corporate Training Manager",
      company: "Fortune 500 Company",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      quote: "The AI-powered content generation and A100 GPU acceleration have transformed our employee training programs. We've reduced production costs by 70% while improving course quality and engagement rates.",
      metrics: {
        employeesTrained: "10K+",
        costReduction: "70%",
        satisfaction: "95%"
      },
      category: "Corporate"
    },
    {
      id: 3,
      name: "Dr. Emily Watson",
      role: "Medical Education Director",
      company: "Healthcare Institute",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      quote: "The multi-language support and voice synthesis quality are outstanding. We're now delivering medical training in 15 languages with consistent, professional narration. The Chatterbox TTS voices are incredibly natural.",
      metrics: {
        languages: "15",
        medicalCourses: "200+",
        globalReach: "50 countries"
      },
      category: "Healthcare"
    },
    {
      id: 4,
      name: "Alex Thompson",
      role: "Content Creator & Influencer",
      company: "Independent Creator",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      quote: "As a solo creator, I needed something powerful yet easy to use. The avatar generation from just a photo and the automated slide creation with Marp have made me look like I have a whole production team.",
      metrics: {
        followers: "500K+",
        coursesCreated: "25",
        revenue: "+300%"
      },
      category: "Creator Economy"
    },
    {
      id: 5,
      name: "Jennifer Park",
      role: "Learning & Development Specialist",
      company: "Tech Startup",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      quote: "The team collaboration features and API integrations fit perfectly into our existing workflow. The ability to generate professional avatar videos at scale has been a game-changer for our onboarding process.",
      metrics: {
        onboardingTime: "-60%",
        teamEfficiency: "+45%",
        newHires: "1K+"
      },
      category: "Startup"
    },
    {
      id: 6,
      name: "David Kim",
      role: "Educational Technology Consultant",
      company: "EdTech Solutions",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      quote: "I've evaluated dozens of course creation platforms. CourseAI's combination of AI content generation, real video production with SadTalker, and enterprise-grade voice synthesis is unmatched in the market.",
      metrics: {
        clientsSaved: "$2M+",
        platforms: "20+",
        satisfaction: "98%"
      },
      category: "Consulting"
    }
  ];

  const stats = [
    {
      number: "50,000+",
      label: "Courses Created",
      icon: <FileVideo className="h-8 w-8 text-blue-600" />
    },
    {
      number: "1M+",
      label: "Students Reached",
      icon: <Users className="h-8 w-8 text-purple-600" />
    },
    {
      number: "25+",
      label: "Languages Supported",
      icon: <Globe className="h-8 w-8 text-green-600" />
    },
    {
      number: "98%",
      label: "Customer Satisfaction",
      icon: <Award className="h-8 w-8 text-orange-600" />
    }
  ];

  const industries = [
    { name: "Education", count: "2,500+", growth: "+45%" },
    { name: "Corporate Training", count: "1,800+", growth: "+60%" },
    { name: "Healthcare", count: "900+", growth: "+38%" },
    { name: "Technology", count: "1,200+", growth: "+52%" },
    { name: "Creator Economy", count: "3,100+", growth: "+73%" },
    { name: "Consulting", count: "600+", growth: "+41%" }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <motion.section 
        className="relative overflow-hidden pt-24 pb-16"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400 rounded-full opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-400 rounded-full opacity-10 blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center"
            variants={itemVariants}
          >
            <motion.div
              className="inline-flex items-center space-x-2 mb-6"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 blur-lg opacity-20"></div>
                <Sparkles className="relative h-12 w-12 text-blue-600" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Success Stories
              </span>
            </motion.div>

            <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Loved by creators and
              <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                trusted by enterprises
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              See how CourseAI is transforming course creation across industries with 
              AI-powered content generation, avatar videos, and professional voice synthesis.
            </p>

            <motion.div 
              className="flex flex-col sm:flex-row gap-4 justify-center"
              variants={itemVariants}
            >
              <Link href="/auth">
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Start Creating <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Button 
                variant="outline" 
                size="lg"
                className="border-2 border-gray-300 hover:border-blue-500 px-8 py-4 text-lg bg-white/80 backdrop-blur-sm"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Stories
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Stats Section */}
      <motion.section 
        className="py-16 bg-white/50 backdrop-blur-sm border-y border-gray-200/50"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div 
                key={index}
                className="text-center space-y-4"
                variants={itemVariants}
              >
                <div className="w-16 h-16 mx-auto rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 flex items-center justify-center">
                  {stat.icon}
                </div>
                <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Testimonials Grid */}
      <motion.section 
        className="py-20"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              What our customers say
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Real stories from educators, creators, and enterprises who've transformed 
              their course creation with CourseAI
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                variants={itemVariants}
                whileHover={{ scale: 1.02, y: -5 }}
                transition={{ duration: 0.2 }}
              >
                <Card className="h-full bg-white/60 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardHeader className="space-y-4">
                    {/* Quote Icon */}
                    <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 flex items-center justify-center">
                      <Quote className="h-6 w-6 text-blue-600" />
                    </div>

                    {/* Rating */}
                    <div className="flex items-center space-x-1">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>

                    {/* Category Badge */}
                    <Badge variant="outline" className="w-fit">
                      {testimonial.category}
                    </Badge>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    {/* Quote */}
                    <blockquote className="text-gray-700 leading-relaxed italic">
                      "{testimonial.quote}"
                    </blockquote>

                    {/* Metrics */}
                    <div className="grid grid-cols-3 gap-2 pt-4 border-t border-gray-200/50">
                      {Object.entries(testimonial.metrics).map(([key, value], index) => (
                        <div key={index} className="text-center">
                          <div className="text-lg font-bold text-blue-600">{value}</div>
                          <div className="text-xs text-gray-500 capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Author */}
                    <div className="flex items-center space-x-3 pt-4 border-t border-gray-200/50">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                        <AvatarFallback className="bg-gradient-to-r from-blue-100 to-purple-100 text-blue-600 font-semibold">
                          {testimonial.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-semibold text-gray-900">{testimonial.name}</div>
                        <div className="text-sm text-gray-600">{testimonial.role}</div>
                        <div className="text-sm text-gray-500">{testimonial.company}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Industries Section */}
      <motion.section 
        className="py-20 bg-white/50 backdrop-blur-sm"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Trusted across industries
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              CourseAI powers course creation for leading organizations worldwide
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {industries.map((industry, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
                className="text-center space-y-3"
              >
                <Card className="p-6 bg-white/60 backdrop-blur-sm border border-gray-200/50 hover:shadow-lg transition-all duration-300">
                  <div className="space-y-3">
                    <div className="text-2xl font-bold text-gray-900">{industry.count}</div>
                    <div className="text-sm font-medium text-gray-700">{industry.name}</div>
                    <div className="flex items-center justify-center space-x-1">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-green-600 font-medium">{industry.growth}</span>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section 
        className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white relative overflow-hidden"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full opacity-5 blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full opacity-5 blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div variants={itemVariants}>
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Join thousands of success stories
            </h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
              Start creating professional courses with AI-powered content generation, 
              avatar videos, and enterprise-grade voice synthesis today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <Button 
                  size="lg" 
                  variant="secondary"
                  className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Start Free Trial <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/product/pricing">
                <Button 
                  size="lg" 
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold transition-all duration-300"
                >
                  View Pricing
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </motion.section>
    </div>
  );
}