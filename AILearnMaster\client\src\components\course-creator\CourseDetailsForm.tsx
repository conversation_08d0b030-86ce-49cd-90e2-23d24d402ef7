import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { useQuery, useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { ChevronRight, ArrowRight, Sparkles, Wand2, Loader2, Video, <PERSON>, Brain, Target } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

// Define the schema for the form
export const courseDetailsSchema = z.object({
  title: z.string().min(5, {
    message: "Title must be at least 5 characters",
  }),
  category: z.string().min(1, {
    message: "Please select a category",
  }),
  description: z.string().min(20, {
    message: "Description must be at least 20 characters",
  }),
  targetAudience: z.string().optional(),
  useAI: z.boolean().default(true),
});

export type CourseDetailsFormValues = z.infer<typeof courseDetailsSchema>;

interface CourseDetailsFormProps {
  onSubmit: (values: CourseDetailsFormValues) => void;
  defaultValues?: Partial<CourseDetailsFormValues>;
  isEdit?: boolean;
}

export function CourseDetailsForm({ 
  onSubmit, 
  defaultValues = {
    title: "",
    category: "",
    description: "",
    targetAudience: "",
    useAI: true,
  },
  isEdit = false 
}: CourseDetailsFormProps) {
  const { toast } = useToast();
  const [showAIAssistant, setShowAIAssistant] = useState(false);
  const [aiTopic, setAiTopic] = useState("");
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  const [isGeneratingAudience, setIsGeneratingAudience] = useState(false);
  const [isGeneratingTopics, setIsGeneratingTopics] = useState(false);

  const form = useForm<CourseDetailsFormValues>({
    resolver: zodResolver(courseDetailsSchema),
    defaultValues,
  });

  // AI Mutations for generating content
  const generateDescriptionMutation = useMutation({
    mutationFn: async (data: { title: string; category: string }) => {
      const response = await apiRequest('POST', '/api/ai/generate-description', data);
      if (!response.ok) {
        throw new Error('Failed to generate description');
      }
      return response.json();
    },
    onSuccess: (data) => {
      form.setValue('description', data.description);
      setIsGeneratingDescription(false);
      toast({
        title: "Description Generated!",
        description: "AI has created a compelling course description for you.",
      });
    },
    onError: (error) => {
      setIsGeneratingDescription(false);
      toast({
        title: "Generation Failed",
        description: "Could not generate description. Please try again.",
        variant: "destructive"
      });
    }
  });

  const generateAudienceMutation = useMutation({
    mutationFn: async (data: { title: string; description: string; category: string }) => {
      const response = await apiRequest('POST', '/api/ai/generate-audience', data);
      if (!response.ok) {
        throw new Error('Failed to generate target audience');
      }
      return response.json();
    },
    onSuccess: (data) => {
      form.setValue('targetAudience', data.targetAudience);
      setIsGeneratingAudience(false);
      toast({
        title: "Target Audience Generated!",
        description: "AI has identified the ideal audience for your course.",
      });
    },
    onError: (error) => {
      setIsGeneratingAudience(false);
      toast({
        title: "Generation Failed",
        description: "Could not generate target audience. Please try again.",
        variant: "destructive"
      });
    }
  });

  const generateCourseContentMutation = useMutation({
    mutationFn: async (topic: string) => {
      const response = await apiRequest("POST", "/api/ai/generate-course-structure", {
        title: `${topic} Masterclass`,
        description: `A comprehensive course about ${topic}`,
        category: "technology",
        targetAudience: `People interested in learning about ${topic}`,
      });
      if (!response.ok) {
        throw new Error("Failed to generate course content");
      }
      return response.json();
    },
    onSuccess: (data) => {
      form.setValue("title", data.title || `${aiTopic} Masterclass`);
      form.setValue("description", data.description || `A comprehensive course about ${aiTopic}`);
      form.setValue("category", data.category || "technology");
      form.setValue("targetAudience", data.targetAudience || `Professionals and enthusiasts interested in ${aiTopic}`);
      
      setShowAIAssistant(false);
      setAiTopic("");
      
      toast({
        title: "Course Content Generated",
        description: "AI has created content for your course based on the topic you provided.",
      });
    },
    onError: (error) => {
      toast({
        title: "Generation Failed",
        description: "Failed to generate course content. Please try again.",
        variant: "destructive"
      });
    }
  });

  // AI Handler Functions
  const handleGenerateDescription = () => {
    const title = form.getValues('title');
    const category = form.getValues('category');
    
    if (!title) {
      toast({
        title: "Missing title",
        description: "Please provide a course title first.",
        variant: "destructive"
      });
      return;
    }
    
    setIsGeneratingDescription(true);
    generateDescriptionMutation.mutate({ title, category });
  };

  const handleGenerateAudience = () => {
    const title = form.getValues('title');
    const description = form.getValues('description');
    const category = form.getValues('category');
    
    if (!title || !description) {
      toast({
        title: "Missing information",
        description: "Please provide a title and description first.",
        variant: "destructive"
      });
      return;
    }
    
    setIsGeneratingAudience(true);
    generateAudienceMutation.mutate({ title, description, category });
  };

  const handleAITopicGenerate = () => {
    if (!aiTopic.trim()) {
      toast({
        title: "Missing topic",
        description: "Please enter a topic for your course.",
        variant: "destructive"
      });
      return;
    }
    
    generateCourseContentMutation.mutate(aiTopic);
  };

  return (
    <div className="space-y-6">

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Course Title</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Introduction to Web Development" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="technology">Technology & Programming</SelectItem>
                        <SelectItem value="business">Business & Entrepreneurship</SelectItem>
                        <SelectItem value="design">Design & Creative Arts</SelectItem>
                        <SelectItem value="marketing">Marketing & Sales</SelectItem>
                        <SelectItem value="personal-development">Personal Development</SelectItem>
                        <SelectItem value="finance">Finance & Investing</SelectItem>
                        <SelectItem value="health">Health & Wellness</SelectItem>
                        <SelectItem value="education">Education & Teaching</SelectItem>
                        <SelectItem value="language">Language Learning</SelectItem>
                        <SelectItem value="lifestyle">Lifestyle & Hobbies</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the main category that best represents your course
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>Course Description</FormLabel>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleGenerateDescription}
                        disabled={isGeneratingDescription || !form.getValues('title')}
                        className="gap-2 text-xs"
                      >
                        {isGeneratingDescription ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <Wand2 className="h-3 w-3" />
                        )}
                        AI Generate
                      </Button>
                    </div>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe what your course is about and what students will learn" 
                        className="min-h-[120px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Provide a clear overview of your course content and learning outcomes
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <div className="space-y-6">
              <FormField
                control={form.control}
                name="targetAudience"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>Target Audience (Optional)</FormLabel>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleGenerateAudience}
                        disabled={isGeneratingAudience || !form.getValues('title') || !form.getValues('description')}
                        className="gap-2 text-xs"
                      >
                        {isGeneratingAudience ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : (
                          <Target className="h-3 w-3" />
                        )}
                        AI Generate
                      </Button>
                    </div>
                    <FormControl>
                      <Textarea 
                        placeholder="Who is this course designed for?" 
                        className="min-h-[80px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormDescription>
                      Specify who would benefit most from taking your course
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="useAI"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base flex items-center">
                        <Sparkles className="text-primary h-4 w-4 mr-2" />
                        AI-Assisted Creation
                      </FormLabel>
                      <FormDescription>
                        Enable AI features to help generate content and structure
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            
              <Card className="mt-6 bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Video className="h-4 w-4 text-blue-600" />
                    Avatar Course Features
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-sm text-muted-foreground space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        <Users className="h-3 w-3 mr-1" />
                        AI Avatar
                      </Badge>
                      <span>Professional AI presenter</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        <Brain className="h-3 w-3 mr-1" />
                        Smart Script
                      </Badge>
                      <span>AI-generated course content</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        <Video className="h-3 w-3 mr-1" />
                        Auto Video
                      </Badge>
                      <span>Automated video production</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
          
          <Separator className="my-6" />
          
          <div className="flex justify-end">
            <Button type="submit" className="gap-2">
              {isEdit ? 'Update Details' : 'Generate Course Structure'}
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}