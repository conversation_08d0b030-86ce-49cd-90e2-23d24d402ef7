import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Volume2, 
  Play, 
  Pause, 
  ArrowRight, 
  ArrowLeft, 
  User,
  Mic,
  Settings,
  Zap,
  Crown,
  Loader2,
  Download,
  TestTube,
  Key,
  CheckCircle,
  AlertCircle,
  Cpu
} from 'lucide-react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';

interface VoiceSelectorProps {
  onSubmit: (data: any) => void;
  onBack: () => void;
  defaultValues?: any;
  scriptData: any;
}

export function VoiceSelector({ 
  onSubmit, 
  onBack, 
  defaultValues,
  scriptData 
}: VoiceSelectorProps) {
  const [activeService, setActiveService] = useState('local');
  const [selectedVoice, setSelectedVoice] = useState(defaultValues?.voiceId || '');
  const [speed, setSpeed] = useState(defaultValues?.speed || 1.0);
  const [pitch, setPitch] = useState(defaultValues?.pitch || 1.0);
  const [premiumApiKey, setPremiumApiKey] = useState('');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPreview, setCurrentPreview] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const { toast } = useToast();

  // Fetch available TTS services and voices
  const { data: ttsServices, isLoading: servicesLoading } = useQuery({
    queryKey: ['/api/ai/tts-services'],
    queryFn: () => fetch('/api/ai/tts-services').then(res => res.json())
  });

  // Fetch local TTS voices
  const { data: localVoices, isLoading: localLoading } = useQuery({
    queryKey: ['/api/ai/local-voices'],
    queryFn: () => fetch('/api/ai/local-voices').then(res => res.json()),
    enabled: activeService === 'local'
  });

  // Fetch premium voices
  const { data: premiumVoices, isLoading: premiumLoading, refetch: refetchPremium } = useQuery({
    queryKey: ['/api/ai/premium-voices'],
    queryFn: () => fetch('/api/ai/premium-voices').then(res => res.json()),
    enabled: activeService === 'premium',
    retry: false
  });

  // TTS Preview Mutation
  const previewMutation = useMutation({
    mutationFn: async ({ text, voiceId, service }: { text: string, voiceId: string, service: string }) => {
      const endpoint = service === 'premium' ? '/api/ai/premium-preview' : '/api/ai/local-preview';
      const body: any = { text, voiceId, speed, pitch };
      
      if (service === 'premium' && premiumApiKey) {
        body.apiKey = premiumApiKey;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to generate preview');
      }

      const blob = await response.blob();
      return URL.createObjectURL(blob);
    },
    onSuccess: (audioUrl) => {
      setCurrentPreview(audioUrl);
      if (audioRef.current) {
        audioRef.current.src = audioUrl;
        audioRef.current.play();
        setIsPlaying(true);
      }
    },
    onError: (error: any) => {
      toast({
        title: "Preview Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // API Key Test Mutation
  const testApiKeyMutation = useMutation({
    mutationFn: async (apiKey: string) => {
      const response = await fetch('/api/ai/test-elevenlabs-key', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey })
      });
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "API Key Valid",
        description: "ElevenLabs API key is working correctly"
      });
      refetchElevenLabs();
    },
    onError: (error: any) => {
      toast({
        title: "Invalid API Key",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const handlePreview = () => {
    if (!selectedVoice) {
      toast({
        title: "No Voice Selected",
        description: "Please select a voice first",
        variant: "destructive"
      });
      return;
    }

    const previewText = scriptData?.segments?.[0]?.content?.substring(0, 200) || 
                       "Hello! This is a preview of your selected voice for the avatar course.";
    
    previewMutation.mutate({
      text: previewText,
      voiceId: selectedVoice,
      service: activeService
    });
  };

  const handleAudioEnd = () => {
    setIsPlaying(false);
  };

  const togglePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  const handleSubmit = () => {
    if (!selectedVoice) {
      toast({
        title: "No Voice Selected",
        description: "Please select a voice before continuing",
        variant: "destructive"
      });
      return;
    }

    const voiceData = {
      voiceId: selectedVoice,
      service: activeService,
      speed,
      pitch,
      elevenLabsApiKey: activeService === 'elevenlabs' ? elevenLabsApiKey : undefined,
      optimizedForTTS: true
    };

    onSubmit(voiceData);
  };

  return (
    <div className="space-y-6">
      {/* Hidden Audio Element */}
      <audio ref={audioRef} onEnded={handleAudioEnd} className="hidden" />

      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">Choose Voice Settings</h2>
        <p className="text-muted-foreground">
          Select and test the perfect voice for your avatar course
        </p>
      </div>

      {/* Script Preview */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Mic className="h-4 w-4 text-blue-600" />
            Script Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center gap-4 text-sm text-blue-800">
            <div className="flex items-center gap-1">
              <Badge variant="secondary">{scriptData?.segments?.length || 0} segments</Badge>
            </div>
            <div>
              {scriptData?.wordCount || 0} words
            </div>
            <div>
              Est. {Math.ceil((scriptData?.totalDuration || 0) / 60)} minutes
            </div>
          </div>
        </CardContent>
      </Card>

      {/* TTS Service Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Text-to-Speech Service
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeService} onValueChange={setActiveService} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="coqui" className="gap-2">
                <Zap className="h-4 w-4" />
                Coqui TTS (Primary)
              </TabsTrigger>
              <TabsTrigger value="kokoro" className="gap-2">
                <Cpu className="h-4 w-4" />
                Kokoro TTS (Fallback)
              </TabsTrigger>
            </TabsList>

            {/* Coqui TTS Section */}
            <TabsContent value="coqui" className="space-y-4">
              <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-800">
                  Coqui TTS is available and ready to use
                </span>
              </div>

              {coquiLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading Coqui voices...</span>
                </div>
              ) : (
                <div className="space-y-3">
                  <Label>Select Coqui Voice</Label>
                  <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a Coqui voice" />
                    </SelectTrigger>
                    <SelectContent>
                      {coquiVoices?.voices?.map((voice: any) => (
                        <SelectItem key={voice.id} value={voice.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{voice.name}</span>
                            <span className="text-xs text-muted-foreground">
                              {voice.language} • {voice.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </TabsContent>

            {/* ElevenLabs Section */}
            <TabsContent value="elevenlabs" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                  <Key className="h-4 w-4 text-amber-600" />
                  <span className="text-sm text-amber-800">
                    ElevenLabs requires your personal API key
                  </span>
                </div>

                <div className="space-y-3">
                  <Label>ElevenLabs API Key</Label>
                  <div className="flex gap-2">
                    <Input
                      type="password"
                      placeholder="Enter your ElevenLabs API key"
                      value={elevenLabsApiKey}
                      onChange={(e) => setElevenLabsApiKey(e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      variant="outline"
                      onClick={() => testApiKeyMutation.mutate(elevenLabsApiKey)}
                      disabled={!elevenLabsApiKey || testApiKeyMutation.isPending}
                      className="gap-2"
                    >
                      {testApiKeyMutation.isPending ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <TestTube className="h-4 w-4" />
                      )}
                      Test
                    </Button>
                  </div>
                </div>

                {elevenLabsLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading ElevenLabs voices...</span>
                  </div>
                ) : elevenLabsVoices?.length > 0 ? (
                  <div className="space-y-3">
                    <Label>Select ElevenLabs Voice</Label>
                    <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose an ElevenLabs voice" />
                      </SelectTrigger>
                      <SelectContent>
                        {elevenLabsVoices.map((voice: any) => (
                          <SelectItem key={voice.voice_id} value={voice.voice_id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{voice.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {voice.category} • Premium Quality
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                ) : (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Please enter a valid ElevenLabs API key to access premium voices.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Voice Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            Voice Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Speech Speed: {speed}x</Label>
              <Slider
                value={[speed]}
                onValueChange={(value) => setSpeed(value[0])}
                min={0.5}
                max={2.0}
                step={0.1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Slower</span>
                <span>Faster</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Pitch: {pitch}x</Label>
              <Slider
                value={[pitch]}
                onValueChange={(value) => setPitch(value[0])}
                min={0.5}
                max={2.0}
                step={0.1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Lower</span>
                <span>Higher</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Voice Preview */}
      {selectedVoice && (
        <Card className="border-l-4 border-l-primary">
          <CardHeader className="pb-3">
            <CardTitle className="text-base">Voice Preview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="font-medium">
                  {activeService === 'coqui' 
                    ? coquiVoices?.voices?.find((v: any) => v.id === selectedVoice)?.name
                    : elevenLabsVoices?.find((v: any) => v.voice_id === selectedVoice)?.name
                  }
                </h4>
                <p className="text-sm text-muted-foreground">
                  {activeService === 'coqui' ? 'Coqui TTS' : 'ElevenLabs'} • 
                  Speed: {speed}x • Pitch: {pitch}x
                </p>
              </div>
              
              <div className="flex items-center gap-2">
                {currentPreview && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={togglePlayPause}
                    className="gap-2"
                  >
                    {isPlaying ? (
                      <Pause className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                    {isPlaying ? 'Pause' : 'Play'}
                  </Button>
                )}
                
                <Button
                  onClick={handlePreview}
                  disabled={!selectedVoice || previewMutation.isPending}
                  className="gap-2"
                >
                  {previewMutation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <TestTube className="h-4 w-4" />
                  )}
                  Test Voice
                </Button>
              </div>
            </div>
            
            <div className="text-xs text-muted-foreground">
              Preview uses the first segment of your script: "{scriptData?.segments?.[0]?.content?.substring(0, 100)}..."
            </div>
          </CardContent>
        </Card>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack} className="gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Script
        </Button>
        <Button 
          onClick={handleSubmit} 
          disabled={!selectedVoice}
          className="gap-2"
        >
          Continue to Preview
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}