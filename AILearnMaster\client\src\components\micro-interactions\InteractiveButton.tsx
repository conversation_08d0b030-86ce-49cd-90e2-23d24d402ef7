import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface InteractiveButtonProps {
  children: React.ReactNode;
  onClick?: () => void | Promise<void>;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  rippleEffect?: boolean;
  bounceOnClick?: boolean;
  glowEffect?: boolean;
  successFeedback?: boolean;
}

export default function InteractiveButton({
  children,
  onClick,
  variant = 'default',
  size = 'default',
  disabled = false,
  loading = false,
  className = '',
  rippleEffect = true,
  bounceOnClick = true,
  glowEffect = false,
  successFeedback = false,
  ...props
}: InteractiveButtonProps) {
  const [isClicked, setIsClicked] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);

  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    // Bounce animation
    if (bounceOnClick) {
      setIsClicked(true);
      setTimeout(() => setIsClicked(false), 150);
    }

    // Ripple effect
    if (rippleEffect) {
      const rect = event.currentTarget.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      const newRipple = { id: Date.now(), x, y };
      
      setRipples(prev => [...prev, newRipple]);
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== newRipple.id));
      }, 600);
    }

    // Execute click handler
    if (onClick) {
      try {
        await onClick();
        
        // Success feedback
        if (successFeedback) {
          setShowSuccess(true);
          setTimeout(() => setShowSuccess(false), 1000);
        }
      } catch (error) {
        console.error('Button click error:', error);
      }
    }
  };

  return (
    <motion.div
      className="relative inline-block"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: bounceOnClick ? 0.98 : 1 }}
      animate={isClicked ? { scale: [1, 0.95, 1] } : {}}
      transition={{ duration: 0.15 }}
    >
      <Button
        variant={variant}
        size={size}
        disabled={disabled || loading}
        onClick={handleClick}
        className={`
          relative overflow-hidden transition-all duration-200
          ${glowEffect ? 'shadow-lg hover:shadow-xl' : ''}
          ${showSuccess ? 'bg-green-500 text-white' : ''}
          ${className}
        `}
        {...props}
      >
        {/* Ripple Effects */}
        {ripples.map((ripple) => (
          <motion.span
            key={ripple.id}
            className="absolute bg-white/30 rounded-full pointer-events-none"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          />
        ))}

        {/* Glow Effect */}
        {glowEffect && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-md"
            animate={{
              opacity: [0, 0.5, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        )}

        {/* Button Content */}
        <span className="relative z-10 flex items-center gap-2">
          {loading && <Loader2 className="h-4 w-4 animate-spin" />}
          {showSuccess ? (
            <motion.span
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center gap-2"
            >
              ✓ Success!
            </motion.span>
          ) : (
            children
          )}
        </span>
      </Button>
    </motion.div>
  );
}