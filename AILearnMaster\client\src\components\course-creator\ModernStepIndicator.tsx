import React from "react";
import { CheckCircle2, Circle } from "lucide-react";
import { cn } from "@/lib/utils";

export interface Step {
  number?: number;
  title?: string;
  label?: string;
  description?: string;
  icon?: React.ReactNode;
}

export interface ModernStepIndicatorProps {
  steps: Step[];
  currentStep: number;
  completedSteps?: number[];
  onStepClick?: (stepNumber: number) => void;
  allowSkip?: boolean;
}

export function ModernStepIndicator({
  steps,
  currentStep,
  completedSteps = [],
  onStepClick,
  allowSkip = false,
}: ModernStepIndicatorProps) {
  return (
    <div className="space-y-4">
      {steps.map((step, index) => {
        const isActive = currentStep === index;
        const isCompleted = completedSteps.includes(index);
        const isClickable = isCompleted || isActive || allowSkip;

        return (
          <div 
            key={index} 
            className={cn(
              "flex items-start p-3 rounded-lg transition-colors",
              isActive ? "bg-primary/10" : isCompleted ? "bg-primary/5" : "bg-background",
              isClickable && "cursor-pointer hover:bg-primary/5"
            )}
            onClick={() => isClickable && onStepClick?.(index)}
          >
            <div className="flex-shrink-0 mr-3 mt-0.5">
              {isCompleted ? (
                <CheckCircle2 
                  className="h-5 w-5 text-primary" 
                />
              ) : (
                <Circle 
                  className={cn(
                    "h-5 w-5",
                    isActive ? "text-primary" : "text-muted-foreground"
                  )}
                />
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <h3 
                  className={cn(
                    "text-sm font-medium",
                    isActive || isCompleted ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  {step.title || step.label}
                </h3>
                
                {/* Safely render icon if it exists */}
                {step.icon && (
                  <span className={cn(
                    "ml-2",
                    isActive || isCompleted ? "text-primary" : "text-muted-foreground"
                  )}>
                    {step.icon}
                  </span>
                )}
              </div>
              
              {step.description && (
                <p className="text-xs text-muted-foreground mt-0.5 line-clamp-1">
                  {step.description}
                </p>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}