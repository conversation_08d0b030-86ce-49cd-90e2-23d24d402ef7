import { Router } from 'express';
import { format, subDays } from 'date-fns';

const marketingAnalyticsRouter = Router();

// Route to get page views data
marketingAnalyticsRouter.get('/views', async (req, res) => {
  try {
    const { from, to } = req.query;
    const fromDate = from ? new Date(from as string) : subDays(new Date(), 30);
    const toDate = to ? new Date(to as string) : new Date();
    
    // Calculate number of days between dates
    const diffTime = Math.abs(toDate.getTime() - fromDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    const data = [];
    
    for (let i = 0; i < diffDays; i++) {
      const date = new Date(fromDate);
      date.setDate(fromDate.getDate() + i);
      
      data.push({
        date: format(date, 'MMM dd'),
        'Landing Pages': Math.floor(Math.random() * 300) + 100,
        'Email Campaigns': Math.floor(Math.random() * 200) + 50,
      });
    }
    
    res.json(data);
  } catch (error) {
    console.error('Error getting page views data:', error);
    res.status(500).json({ error: 'Failed to get page views data' });
  }
});

// Route to get conversion data
marketingAnalyticsRouter.get('/conversions', async (req, res) => {
  try {
    const { from, to } = req.query;
    const fromDate = from ? new Date(from as string) : subDays(new Date(), 30);
    const toDate = to ? new Date(to as string) : new Date();
    
    // Calculate number of days between dates
    const diffTime = Math.abs(toDate.getTime() - fromDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    const data = [];
    
    for (let i = 0; i < diffDays; i++) {
      const date = new Date(fromDate);
      date.setDate(fromDate.getDate() + i);
      
      const visitors = Math.floor(Math.random() * 500) + 200;
      const conversions = Math.floor(visitors * (Math.random() * 0.15 + 0.05));
      
      data.push({
        date: format(date, 'MMM dd'),
        visitors,
        conversions,
        rate: Number((conversions / visitors * 100).toFixed(1))
      });
    }
    
    res.json(data);
  } catch (error) {
    console.error('Error getting conversion data:', error);
    res.status(500).json({ error: 'Failed to get conversion data' });
  }
});

// Route to get email stats
marketingAnalyticsRouter.get('/email-stats', async (req, res) => {
  try {
    const stats = {
      sent: Math.floor(Math.random() * 5000) + 1000,
      opened: Math.floor(Math.random() * 3000) + 500,
      clicked: Math.floor(Math.random() * 1000) + 100,
      bounced: Math.floor(Math.random() * 100) + 10,
      unsubscribed: Math.floor(Math.random() * 50) + 5,
      openRate: Math.random() * 0.4 + 0.2,
      clickRate: Math.random() * 0.2 + 0.05
    };
    
    res.json(stats);
  } catch (error) {
    console.error('Error getting email stats:', error);
    res.status(500).json({ error: 'Failed to get email stats' });
  }
});

// Route to get landing page stats
marketingAnalyticsRouter.get('/landing-pages', async (req, res) => {
  try {
    const landingPages = [
      {
        id: 1,
        title: "Master the Art of Photography",
        visitors: Math.floor(Math.random() * 1000) + 200,
        conversions: Math.floor(Math.random() * 100) + 10,
        conversionRate: Math.random() * 0.1 + 0.02
      },
      {
        id: 2,
        title: "Advanced Digital Marketing",
        visitors: Math.floor(Math.random() * 1000) + 200,
        conversions: Math.floor(Math.random() * 100) + 10,
        conversionRate: Math.random() * 0.1 + 0.02
      },
      {
        id: 3,
        title: "Complete Web Development",
        visitors: Math.floor(Math.random() * 1000) + 200,
        conversions: Math.floor(Math.random() * 100) + 10,
        conversionRate: Math.random() * 0.1 + 0.02
      }
    ];
    
    res.json(landingPages);
  } catch (error) {
    console.error('Error getting landing page stats:', error);
    res.status(500).json({ error: 'Failed to get landing page stats' });
  }
});

export default marketingAnalyticsRouter;