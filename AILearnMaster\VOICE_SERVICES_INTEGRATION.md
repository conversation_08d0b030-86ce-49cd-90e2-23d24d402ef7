# Voice Services Integration Guide

## Overview

The Course AI Platform integrates three premium voice services through a unified interface, providing users with comprehensive text-to-speech options for both Traditional and Avatar course creation workflows.

## Available Voice Services

### 1. Chatterbox TTS (Enterprise Tier)
- **Technology**: A100 80G GPU-powered voice synthesis
- **Voices**: 10 premium English voices (5 male, 5 female)
- **Features**: 
  - GPU acceleration for high-quality synthesis
  - Batch processing capabilities
  - Voice cloning functionality
  - Temperature control for expression variety
- **API Endpoint**: `/api/chatterbox-tts/`
- **Fallback**: Local TTS processing when A100 unavailable

### 2. OpenAI TTS (Premium Tier)
- **Technology**: Professional-grade neural voice synthesis
- **Voices**: 6 natural voices (alloy, echo, fable, onyx, nova, shimmer)
- **Features**:
  - Natural speech patterns and intonation
  - Multiple language support
  - Fast generation times
  - High audio quality
- **API Endpoint**: `/api/ai/text-to-speech`
- **Speed Control**: 0.5x to 2.0x playback speed

### 3. ElevenLabs (Premium Tier)
- **Technology**: Ultra-realistic voice synthesis with emotional range
- **Voices**: Extensive voice library with custom voice options
- **Features**:
  - Emotional expression and tone variation
  - Voice cloning from samples
  - Multilingual support
  - Advanced stability and similarity controls
- **API Endpoint**: `/api/ai/elevenlabs-tts`
- **Controls**: Stability, similarity boost, and style settings

## Integration Architecture

### Unified Voice Service Selector
- Component: `VoiceServiceSelector.tsx`
- Location: `client/src/components/voice/`
- Purpose: Provides consistent voice selection across both course creation flows

### Course Creation Flows

#### Traditional Course Flow
- **Component**: `VoiceGenerationModal.tsx`
- **Integration**: Comprehensive voice service selection with batch processing
- **Features**: 
  - Service comparison and preview
  - Bulk voice generation for all lessons
  - Progress tracking and status monitoring

#### Avatar Course Flow
- **Component**: `SimpleVoiceSelector.tsx` (updated to use VoiceServiceSelector)
- **Integration**: Voice selection for avatar narration
- **Features**:
  - Service selection optimized for avatar generation
  - Preview functionality with sample text
  - Settings tailored for talking head videos

### API Routes

#### Chatterbox TTS Routes
```
GET  /api/chatterbox-tts/voices     - List available voices
POST /api/chatterbox-tts/generate  - Generate speech
POST /api/chatterbox-tts/batch     - Batch generation
POST /api/chatterbox-tts/clone     - Voice cloning
GET  /api/chatterbox-tts/health    - Service health check
```

#### SadTalker Avatar Routes
```
GET  /api/sadtalker/health          - Avatar service status
POST /api/sadtalker/generate-avatar - Generate avatar video
POST /api/sadtalker/generate-course - Generate avatar course
GET  /api/sadtalker/settings        - Avatar generation settings
```

## User Experience Features

### Service Selection Interface
1. **Visual Service Cards**: Each service displays with tier badges and feature highlights
2. **Voice Preview**: Real-time audio preview for all voices across services
3. **Settings Customization**: Service-specific controls (temperature, speed, stability)
4. **Intelligent Defaults**: Chatterbox TTS as default for premium experience

### Quality Tiers
- **Enterprise**: Chatterbox TTS (A100 GPU acceleration)
- **Premium**: OpenAI TTS and ElevenLabs (Professional quality)
- **Standard**: Local fallback options

### Cost Optimization
- **Intelligent Routing**: A100 GPU when available, local processing as fallback
- **Batch Processing**: Efficient handling of multiple voice generation requests
- **Caching**: Voice previews and frequently used audio cached for performance

## Technical Implementation

### Service Registration
```typescript
const voiceServices = [
  {
    id: 'chatterbox',
    name: 'Chatterbox TTS',
    tier: 'enterprise',
    features: ['GPU Acceleration', 'Batch Processing', 'Voice Cloning']
  },
  {
    id: 'openai',
    name: 'OpenAI TTS', 
    tier: 'premium',
    features: ['Natural Speech', 'Multiple Languages', 'Fast Generation']
  },
  {
    id: 'elevenlabs',
    name: 'ElevenLabs',
    tier: 'premium', 
    features: ['Emotional Range', 'Voice Cloning', 'Ultra Realistic']
  }
];
```

### Backend Service Integration
- **Modal A100 Integration**: `server/services/chatterboxModalTTS.ts`
- **SadTalker Service**: `server/services/sadTalkerA100Service.ts`
- **Unified API**: Consistent response format across all services

### Error Handling
- **Graceful Degradation**: Automatic fallback to available services
- **User Notifications**: Clear error messages and service status
- **Health Monitoring**: Real-time service availability checking

## Deployment Configuration

### Environment Variables Required
```
MODAL_TOKEN_ID=<modal_token_id>
MODAL_TOKEN_SECRET=<modal_token_secret>
OPENAI_API_KEY=<openai_api_key>
ELEVENLABS_API_KEY=<elevenlabs_api_key>
```

### Modal A100 Setup
- **GPU Configuration**: A100 80G for maximum performance
- **Cost Management**: ~$2.50-4.00/hour usage optimization
- **Fallback Strategy**: Local TTS when GPU unavailable

## Usage Examples

### Traditional Course Voice Generation
1. Navigate to Course Creation → Traditional Course
2. Complete course structure and scripts
3. Click "Create Voices" to open Voice Generation Studio
4. Select preferred voice service (Chatterbox TTS recommended)
5. Choose voice and adjust settings
6. Generate voices for all lessons with progress tracking

### Avatar Course Voice Selection
1. Navigate to Course Creation → Avatar Course
2. Complete course details, avatar selection, and script creation
3. In Voice Settings step, choose from three voice services
4. Preview voices with sample text from course content
5. Proceed to avatar video generation with selected voice

## Performance Metrics

### Voice Generation Speed
- **Chatterbox TTS**: ~2-5 seconds per 100 words (A100 GPU)
- **OpenAI TTS**: ~1-3 seconds per 100 words
- **ElevenLabs**: ~3-7 seconds per 100 words

### Quality Benchmarks
- **Audio Format**: MP3, 44.1kHz, 128kbps minimum
- **Latency**: Sub-1 second preview generation
- **Reliability**: 99.5% uptime with fallback systems

## Future Enhancements

### Planned Features
- Custom voice training for enterprise users
- Real-time voice synthesis during course playback
- Advanced emotion and tone controls
- Multi-language voice generation expansion
- Voice analytics and optimization recommendations

### Integration Roadmap
- LMS-specific voice optimization
- Mobile app voice generation
- Offline voice synthesis capabilities
- Advanced batch processing with scheduling