import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Trash2, MessageSquare, Plus, Search, Clock, Check, X, Archive, Brain, FileText, Edit, Book } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { ChatSession } from "@/types/chat";
import ChatInterface from "./ChatInterface";

interface ChatSessionManagerProps {
  courseId?: number;
  lessonId?: number;
  initialView?: 'chat' | 'list';
}

const ChatSessionManager = ({ courseId, lessonId, initialView = 'list' }: ChatSessionManagerProps) => {
  const [activeSession, setActiveSession] = useState<ChatSession | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewSessionDialog, setShowNewSessionDialog] = useState(false);
  const [newSessionTitle, setNewSessionTitle] = useState('');
  const [view, setView] = useState<'chat' | 'list'>(initialView);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Query to fetch chat sessions
  const { data: sessions, isLoading } = useQuery<ChatSession[]>({
    queryKey: ['/api/chatbot/sessions'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/chatbot/sessions');
      return res.json();
    }
  });

  // Mutation to create a new session
  const createSessionMutation = useMutation({
    mutationFn: async (title: string) => {
      const res = await apiRequest('POST', '/api/chatbot/sessions', {
        title,
        courseId,
        lessonId
      });
      return res.json();
    },
    onSuccess: (newSession) => {
      queryClient.invalidateQueries({ queryKey: ['/api/chatbot/sessions'] });
      setActiveSession(newSession);
      setView('chat');
      toast({
        title: 'Chat session created',
        description: 'You can now start chatting with the AI assistant',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Could not create chat session',
        variant: 'destructive',
      });
    }
  });

  // Mutation to delete a session
  const deleteSessionMutation = useMutation({
    mutationFn: async (sessionId: number) => {
      await apiRequest('DELETE', `/api/chatbot/sessions/${sessionId}`);
      return sessionId;
    },
    onSuccess: (sessionId) => {
      queryClient.invalidateQueries({ queryKey: ['/api/chatbot/sessions'] });
      if (activeSession?.id === sessionId) {
        setActiveSession(null);
        setView('list');
      }
      toast({
        title: 'Chat session deleted',
        description: 'The chat session has been deleted',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Could not delete chat session',
        variant: 'destructive',
      });
    }
  });

  // Mutation to archive a session
  const archiveSessionMutation = useMutation({
    mutationFn: async (sessionId: number) => {
      const res = await apiRequest('PATCH', `/api/chatbot/sessions/${sessionId}`, {
        status: 'archived'
      });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/chatbot/sessions'] });
      toast({
        title: 'Chat session archived',
        description: 'The chat session has been archived',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Could not archive chat session',
        variant: 'destructive',
      });
    }
  });

  // Mutation to rename a session
  const renameSessionMutation = useMutation({
    mutationFn: async ({ sessionId, title }: { sessionId: number; title: string }) => {
      const res = await apiRequest('PATCH', `/api/chatbot/sessions/${sessionId}`, {
        title
      });
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/chatbot/sessions'] });
      toast({
        title: 'Chat session renamed',
        description: 'The chat session has been renamed',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Could not rename chat session',
        variant: 'destructive',
      });
    }
  });

  // Create a new session
  const handleCreateSession = () => {
    const title = newSessionTitle.trim() || 'New Chat';
    createSessionMutation.mutate(title);
    setNewSessionTitle('');
    setShowNewSessionDialog(false);
  };

  // Delete a session with confirmation
  const handleDeleteSession = (sessionId: number) => {
    if (window.confirm('Are you sure you want to delete this chat session? This action cannot be undone.')) {
      deleteSessionMutation.mutate(sessionId);
    }
  };

  // Archive a session
  const handleArchiveSession = (sessionId: number) => {
    archiveSessionMutation.mutate(sessionId);
  };

  // Rename a session
  const handleRenameSession = (sessionId: number, title: string) => {
    if (title.trim()) {
      renameSessionMutation.mutate({ sessionId, title });
    }
  };

  // Filter sessions by search query and active/archived status
  const filterSessions = (sessions: ChatSession[] = [], tab: 'active' | 'archived') => {
    return sessions.filter(session => {
      const matchesSearch = session.title.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesTab = tab === 'active' ? session.status !== 'archived' : session.status === 'archived';
      return matchesSearch && matchesTab;
    });
  };

  // Format date to a readable string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Open a session
  const openSession = (session: ChatSession) => {
    setActiveSession(session);
    setView('chat');
  };

  // Back to session list
  const backToSessionList = () => {
    setView('list');
  };

  // If in chat view, show the chat interface
  if (view === 'chat' && activeSession) {
    return (
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-between mb-4">
          <Button variant="outline" onClick={backToSessionList} className="gap-1">
            <MessageSquare className="h-4 w-4" />
            <span>Back to Sessions</span>
          </Button>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const newTitle = prompt('Enter new title:', activeSession.title);
                if (newTitle) {
                  handleRenameSession(activeSession.id, newTitle);
                }
              }}
            >
              <Edit className="h-4 w-4 mr-1" />
              Rename
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleArchiveSession(activeSession.id)}
            >
              <Archive className="h-4 w-4 mr-1" />
              Archive
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteSession(activeSession.id)}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </div>
        </div>
        
        <ChatInterface
          sessionId={activeSession.id}
          courseId={activeSession.courseId || undefined}
          lessonId={activeSession.lessonId || undefined}
        />
      </div>
    );
  }

  // Otherwise, show the session list
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            <span>AI Learning Assistant</span>
          </div>
          <Dialog open={showNewSessionDialog} onOpenChange={setShowNewSessionDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-1" />
                New Chat
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Chat Session</DialogTitle>
              </DialogHeader>
              <div className="py-4">
                <label htmlFor="title" className="text-sm font-medium mb-2 block">Chat Title (optional)</label>
                <Input
                  id="title"
                  placeholder="e.g., Questions about Module 3"
                  value={newSessionTitle}
                  onChange={(e) => setNewSessionTitle(e.target.value)}
                />
                {courseId && (
                  <div className="mt-4 p-3 bg-muted rounded-md">
                    <div className="flex items-center gap-2 mb-1 text-sm font-medium">
                      <Book className="h-4 w-4 text-primary" />
                      <span>This chat will be associated with:</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {lessonId ? "A specific lesson in your course" : "Your entire course"}
                    </p>
                  </div>
                )}
              </div>
              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">
                    Cancel
                  </Button>
                </DialogClose>
                <Button onClick={handleCreateSession}>
                  Create Chat
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardTitle>
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search chat sessions"
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <p className="text-muted-foreground">Loading sessions...</p>
          </div>
        ) : (
          <Tabs defaultValue="active">
            <TabsList className="mb-4 w-full">
              <TabsTrigger value="active" className="flex-1">Active Chats</TabsTrigger>
              <TabsTrigger value="archived" className="flex-1">Archived</TabsTrigger>
            </TabsList>
            
            <TabsContent value="active">
              <SessionList
                sessions={filterSessions(sessions, 'active')}
                onOpenSession={openSession}
                onDeleteSession={handleDeleteSession}
                onArchiveSession={handleArchiveSession}
                onRenameSession={handleRenameSession}
                formatDate={formatDate}
              />
            </TabsContent>
            
            <TabsContent value="archived">
              <SessionList
                sessions={filterSessions(sessions, 'archived')}
                onOpenSession={openSession}
                onDeleteSession={handleDeleteSession}
                onArchiveSession={handleArchiveSession}
                onRenameSession={handleRenameSession}
                formatDate={formatDate}
                isArchived
              />
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};

interface SessionListProps {
  sessions: ChatSession[];
  onOpenSession: (session: ChatSession) => void;
  onDeleteSession: (sessionId: number) => void;
  onArchiveSession: (sessionId: number) => void;
  onRenameSession: (sessionId: number, title: string) => void;
  formatDate: (dateString: string) => string;
  isArchived?: boolean;
}

const SessionList = ({
  sessions,
  onOpenSession,
  onDeleteSession,
  onArchiveSession,
  onRenameSession,
  formatDate,
  isArchived
}: SessionListProps) => {
  if (sessions.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          {isArchived 
            ? "No archived chats found" 
            : "No active chats found. Start a new conversation by clicking 'New Chat'."
          }
        </p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-[500px] pr-4">
      <div className="space-y-2">
        {sessions.map((session) => (
          <div key={session.id} className="group flex flex-col border rounded-md p-3 hover:bg-accent/50 transition-colors">
            <div className="flex justify-between items-start">
              <div 
                className="flex-1 cursor-pointer"
                onClick={() => onOpenSession(session)}
              >
                <h3 className="font-medium text-primary truncate">{session.title}</h3>
                <p className="text-xs text-muted-foreground mt-1 flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDate(session.updatedAt)}
                </p>
                {(session.courseId || session.lessonId) && (
                  <div className="mt-1.5 flex items-center">
                    <FileText className="h-3 w-3 text-muted-foreground mr-1" />
                    <span className="text-xs text-muted-foreground">
                      {session.lessonId ? "Lesson-specific" : "Course-wide"}
                    </span>
                  </div>
                )}
              </div>
              <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  variant="ghost" 
                  size="sm"
                  onClick={() => {
                    const newTitle = prompt('Enter new title:', session.title);
                    if (newTitle) {
                      onRenameSession(session.id, newTitle);
                    }
                  }}
                >
                  <Edit className="h-3.5 w-3.5" />
                </Button>
                {isArchived ? (
                  <Button
                    variant="ghost" 
                    size="sm"
                    onClick={() => onArchiveSession(session.id)}
                  >
                    <Check className="h-3.5 w-3.5" />
                  </Button>
                ) : (
                  <Button
                    variant="ghost" 
                    size="sm"
                    onClick={() => onArchiveSession(session.id)}
                  >
                    <Archive className="h-3.5 w-3.5" />
                  </Button>
                )}
                <Button
                  variant="ghost" 
                  size="sm"
                  onClick={() => onDeleteSession(session.id)}
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </ScrollArea>
  );
};

export default ChatSessionManager;