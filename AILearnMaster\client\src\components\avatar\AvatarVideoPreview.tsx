import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { VideoPlayer } from "@/components/media/VideoPlayer";
import { 
  ArrowLeft, 
  RefreshCw, 
  CheckCircle2, 
  ChevronRight,
  Play,
  Loader2, 
  XCircle,
  Video,
  User,
  Volume2,
  Image as ImageIcon
} from "lucide-react";
import { cn } from "@/lib/utils";

export interface AvatarVideoPreviewProps {
  onContinue: () => void;
  onBack: () => void;
  avatarUrl: string;
  script: string;
  voiceId: string;
  backgroundUrl?: string;
  videoUrl?: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'failed';
  isGenerating: boolean;
}

export function AvatarVideoPreview({
  onContinue,
  onBack,
  avatarUrl,
  script,
  voiceId,
  backgroundUrl,
  videoUrl,
  status,
  isGenerating
}: AvatarVideoPreviewProps) {
  const [videoKey, setVideoKey] = useState(Date.now()); // Key used to force video player reload
  
  // Determine if we show the script snippet or full script
  const scriptPreview = script.length > 150 
    ? script.substring(0, 150) + "..." 
    : script;
    
  // Prepare component state based on generation status
  const isVideoReady = status === 'completed' && videoUrl;
  const hasError = status === 'failed';
  const isProcessing = status === 'in_progress' || isGenerating;
  const canGenerate = status === 'not_started' || status === 'failed';
  
  const handleGenerateClick = () => {
    // When we click generate, we'll call the parent's onContinue callback
    // This will trigger the video generation API call in the parent component
    onContinue();
  };
  
  const handleContinueClick = () => {
    onContinue();
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Preview Your Video</h2>
        <p className="text-muted-foreground mb-6">
          Generate the final talking avatar video with your selected avatar, script, voice, and background.
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-[2fr,1fr] gap-6">
        <div className="space-y-6">
          {/* Video preview area */}
          <Card className={cn(
            "overflow-hidden relative min-h-[400px] flex items-center justify-center",
            isProcessing && "border-primary/50"
          )}>
            {isVideoReady ? (
              <VideoPlayer 
                key={videoKey}
                src={videoUrl!}
                title="Talking Avatar Video" 
                autoPlay={false}
                controls={true}
                className="w-full"
              />
            ) : hasError ? (
              <div className="flex flex-col items-center justify-center p-10 text-center">
                <XCircle className="h-16 w-16 text-destructive mb-4" />
                <h3 className="text-xl font-medium">Video Generation Failed</h3>
                <p className="text-muted-foreground mt-2 mb-6 max-w-md">
                  There was an error generating your talking avatar video. Please try again.
                </p>
                <Button
                  variant="default"
                  onClick={handleGenerateClick}
                  disabled={isProcessing}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            ) : isProcessing ? (
              <div className="flex flex-col items-center justify-center p-10 text-center">
                <div className="relative mb-6">
                  <Loader2 className="h-16 w-16 text-primary animate-spin" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Video className="h-6 w-6 text-primary" />
                  </div>
                </div>
                <h3 className="text-xl font-medium">Generating Your Video</h3>
                <p className="text-muted-foreground mt-2 max-w-md">
                  This process may take a minute or two. We're combining your avatar, script,
                  voice, and background to create your video.
                </p>
                
                <div className="w-full max-w-md mt-6">
                  <div className="relative w-full bg-muted h-2 rounded-full overflow-hidden">
                    <div className="absolute inset-0 bg-primary/30 rounded-full overflow-hidden"></div>
                    <div className="absolute h-full bg-primary rounded-full w-1/2 animate-progress"></div>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">Processing video...</p>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-10 text-center">
                <Video className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-xl font-medium">Ready to Generate</h3>
                <p className="text-muted-foreground mt-2 mb-6 max-w-md">
                  Click the button below to generate your talking avatar video with the settings you've chosen.
                </p>
                <Button
                  variant="default"
                  size="lg"
                  onClick={handleGenerateClick}
                  className="gap-2"
                >
                  <Play className="h-4 w-4" />
                  Generate Video
                </Button>
              </div>
            )}
          </Card>
          
          {/* Generation details */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Video Details</h3>
              
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="bg-muted rounded-md p-2 shrink-0">
                    <User className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Avatar</h4>
                    <div className="flex items-center mt-1">
                      <div className="h-10 w-10 rounded-full overflow-hidden border">
                        <img 
                          src={avatarUrl} 
                          alt="Selected Avatar" 
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <span className="text-sm ml-3 text-muted-foreground">Selected Avatar</span>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex items-start gap-4">
                  <div className="bg-muted rounded-md p-2 shrink-0">
                    <svg className="h-5 w-5 text-primary" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M7 10L12 15L17 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      <path d="M12 15V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Script</h4>
                    <p className="text-sm mt-1 text-muted-foreground">
                      {scriptPreview}
                    </p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex items-start gap-4">
                  <div className="bg-muted rounded-md p-2 shrink-0">
                    <Volume2 className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Voice</h4>
                    <p className="text-sm mt-1 text-muted-foreground">
                      Voice ID: {voiceId || "None selected"}
                    </p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex items-start gap-4">
                  <div className="bg-muted rounded-md p-2 shrink-0">
                    <ImageIcon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium">Background</h4>
                    {backgroundUrl ? (
                      <div className="flex items-center mt-1">
                        <div className="h-10 w-16 rounded-sm overflow-hidden border">
                          <img 
                            src={backgroundUrl} 
                            alt="Selected Background" 
                            className="h-full w-full object-cover"
                          />
                        </div>
                        <span className="text-sm ml-3 text-muted-foreground">Selected Background</span>
                      </div>
                    ) : (
                      <p className="text-sm mt-1 text-muted-foreground">
                        No background selected
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card>
            <CardContent className="p-6 space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-1">Status</h3>
                <StatusIndicator status={status} />
              </div>
              
              <div className="bg-muted p-4 rounded-md">
                <h4 className="text-sm font-medium flex items-center mb-2">
                  <InfoIcon className="h-4 w-4 mr-2" /> Important Information
                </h4>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Video generation may take 1-2 minutes</li>
                  <li>• The video will sync avatar movements with the script</li>
                  <li>• You can regenerate the video if you're not satisfied</li>
                  <li>• Once generated, the video is saved to your account</li>
                </ul>
              </div>
              
              <div className="flex flex-col gap-2 pt-2">
                <Button 
                  variant="default"
                  onClick={handleContinueClick}
                  disabled={!isVideoReady && !canGenerate}
                >
                  {isVideoReady ? (
                    <>
                      <CheckCircle2 className="h-4 w-4 mr-2" /> Continue
                    </>
                  ) : canGenerate ? (
                    <>
                      <Play className="h-4 w-4 mr-2" /> Generate Video
                    </>
                  ) : (
                    <>
                      <ChevronRight className="h-4 w-4 mr-2" /> Continue When Ready
                    </>
                  )}
                </Button>
                <Button 
                  variant="outline"
                  onClick={onBack}
                  disabled={isProcessing}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" /> Back
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

interface StatusIndicatorProps {
  status: 'not_started' | 'in_progress' | 'completed' | 'failed';
}

function StatusIndicator({ status }: StatusIndicatorProps) {
  return (
    <div className="flex items-center gap-2 mt-1">
      {status === 'completed' ? (
        <>
          <div className="h-2.5 w-2.5 rounded-full bg-green-500"></div>
          <span className="text-sm text-green-600 font-medium">Ready to use</span>
        </>
      ) : status === 'in_progress' ? (
        <>
          <div className="h-2.5 w-2.5 rounded-full bg-amber-500 animate-pulse"></div>
          <span className="text-sm text-amber-600 font-medium">Processing</span>
        </>
      ) : status === 'failed' ? (
        <>
          <div className="h-2.5 w-2.5 rounded-full bg-destructive"></div>
          <span className="text-sm text-destructive font-medium">Generation failed</span>
        </>
      ) : (
        <>
          <div className="h-2.5 w-2.5 rounded-full bg-slate-300"></div>
          <span className="text-sm text-muted-foreground">Not generated</span>
        </>
      )}
    </div>
  );
}

// Info icon component
function InfoIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <path d="M12 16v-4" />
      <path d="M12 8h.01" />
    </svg>
  );
}