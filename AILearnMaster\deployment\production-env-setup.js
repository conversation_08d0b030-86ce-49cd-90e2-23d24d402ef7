#!/usr/bin/env node

/**
 * Production Environment Setup Script
 * Configures AWS Amplify environment variables from AWS Secrets Manager
 */

const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');

// Configure AWS SDK
AWS.config.update({ region: 'us-east-1' });
const secretsManager = new AWS.SecretsManager();
const amplify = new AWS.Amplify();

const APP_ID = process.env.AMPLIFY_APP_ID;
const BRANCH_NAME = 'main';

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

/**
 * Retrieve secrets from AWS Secrets Manager
 */
async function getSecrets() {
  log('🔐 Retrieving secrets from AWS Secrets Manager...', 'blue');
  
  const secretNames = [
    'ailearn-master/production/database',
    'ailearn-master/production/session',
    'ailearn-master/production/ai-services',
    'ailearn-master/production/aws-services'
  ];
  
  const secrets = {};
  
  for (const secretName of secretNames) {
    try {
      const result = await secretsManager.getSecretValue({ SecretId: secretName }).promise();
      const secretData = JSON.parse(result.SecretString);
      Object.assign(secrets, secretData);
      log(`✅ Retrieved secrets from ${secretName}`, 'green');
    } catch (error) {
      log(`❌ Failed to retrieve ${secretName}: ${error.message}`, 'red');
      throw error;
    }
  }
  
  return secrets;
}

/**
 * Configure production environment variables
 */
function configureEnvironmentVariables(secrets) {
  log('⚙️ Configuring production environment variables...', 'blue');
  
  const environmentVariables = {
    // Core Application Settings
    NODE_ENV: 'production',
    PORT: '3001',
    
    // Security Settings
    SESSION_SECRET: secrets.SESSION_SECRET,
    JWT_SECRET: secrets.JWT_SECRET,
    SECRETS_ENCRYPTION_KEY: secrets.SECRETS_ENCRYPTION_KEY,
    
    // Database Configuration
    DATABASE_URL: secrets.DATABASE_URL,
    
    // CORS and Security
    ALLOWED_ORIGINS: 'https://ailearn.com,https://www.ailearn.com',
    ENABLE_CSRF: 'true',
    ENABLE_RATE_LIMITING: 'true',
    ENABLE_SECURITY_HEADERS: 'true',
    
    // AWS Services
    AWS_ACCESS_KEY_ID: secrets.AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY: secrets.AWS_SECRET_ACCESS_KEY,
    AWS_REGION: 'us-east-1',
    AWS_S3_BUCKET: secrets.AWS_S3_BUCKET || 'ailearn-master-storage-prod',
    AWS_CLOUDFRONT_DOMAIN: secrets.AWS_CLOUDFRONT_DOMAIN,
    
    // AI Services
    OPENAI_API_KEY: secrets.OPENAI_API_KEY,
    MODAL_TOKEN_ID: secrets.MODAL_TOKEN_ID,
    MODAL_TOKEN_SECRET: secrets.MODAL_TOKEN_SECRET,
    ELEVENLABS_API_KEY: secrets.ELEVENLABS_API_KEY || '',
    
    // Modal Configuration
    MODAL_ENVIRONMENT: 'production',
    MODAL_WARM_CONTAINERS: '1',
    MODAL_MAX_CONCURRENT_JOBS: '5',
    
    // Security Configuration
    AI_PROMPT_SANITIZATION: 'true',
    AI_CONTENT_FILTERING: 'true',
    AI_RATE_LIMITING: 'true',
    AI_MAX_TOKENS: '2000',
    AI_TIMEOUT_MS: '30000',
    
    // Rate Limiting
    RATE_LIMIT_GENERAL_MAX: '300',
    RATE_LIMIT_GENERAL_WINDOW: '900000',
    RATE_LIMIT_AUTH_MAX: '5',
    RATE_LIMIT_AUTH_WINDOW: '900000',
    RATE_LIMIT_AI_MAX: '3',
    RATE_LIMIT_AI_WINDOW: '60000',
    RATE_LIMIT_UPLOAD_MAX: '2',
    RATE_LIMIT_UPLOAD_WINDOW: '60000',
    
    // File Upload Security
    MAX_FILE_SIZE: '5242880',
    MAX_FILES_PER_REQUEST: '5',
    ALLOWED_FILE_TYPES: 'image/jpeg,image/png,image/webp,audio/mpeg,audio/wav',
    
    // Session Configuration
    SESSION_MAX_AGE: '28800000',
    SESSION_SECURE: 'true',
    SESSION_HTTP_ONLY: 'true',
    SESSION_SAME_SITE: 'strict',
    
    // Logging and Monitoring
    LOG_LEVEL: 'warn',
    ENABLE_ACCESS_LOGS: 'true',
    ENABLE_ERROR_LOGS: 'true',
    ENABLE_SECURITY_LOGS: 'true',
    ENABLE_HEALTH_CHECKS: 'true',
    HEALTH_CHECK_INTERVAL: '30000',
    
    // Deployment Information
    DEPLOYMENT_VERSION: '1.0.0',
    DEPLOYMENT_DATE: new Date().toISOString().split('T')[0],
    DEPLOYMENT_ENVIRONMENT: 'production'
  };
  
  return environmentVariables;
}

/**
 * Update Amplify environment variables
 */
async function updateAmplifyEnvironment(environmentVariables) {
  if (!APP_ID) {
    log('❌ AMPLIFY_APP_ID environment variable not set', 'red');
    throw new Error('AMPLIFY_APP_ID is required');
  }
  
  log(`🚀 Updating Amplify app ${APP_ID} environment variables...`, 'blue');
  
  try {
    const params = {
      appId: APP_ID,
      branchName: BRANCH_NAME,
      environmentVariables: environmentVariables
    };
    
    await amplify.updateBranch(params).promise();
    log('✅ Successfully updated Amplify environment variables', 'green');
    
    // Log configured variables (without sensitive values)
    log('\n📋 Configured Environment Variables:', 'cyan');
    Object.keys(environmentVariables).forEach(key => {
      const value = environmentVariables[key];
      const isSensitive = key.includes('SECRET') || key.includes('KEY') || key.includes('PASSWORD') || key.includes('TOKEN');
      const displayValue = isSensitive ? '***HIDDEN***' : value;
      log(`   ${key}: ${displayValue}`, 'yellow');
    });
    
  } catch (error) {
    log(`❌ Failed to update Amplify environment: ${error.message}`, 'red');
    throw error;
  }
}

/**
 * Generate .env.production file for local testing
 */
function generateLocalEnvFile(environmentVariables) {
  log('📄 Generating .env.production file for local testing...', 'blue');
  
  const envContent = Object.entries(environmentVariables)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  const envFilePath = path.join(process.cwd(), '.env.production');
  fs.writeFileSync(envFilePath, envContent);
  
  log(`✅ Generated ${envFilePath}`, 'green');
  log('⚠️  Remember to keep this file secure and never commit it to version control!', 'yellow');
}

/**
 * Validate environment configuration
 */
async function validateEnvironment(environmentVariables) {
  log('🔍 Validating environment configuration...', 'blue');
  
  const requiredVars = [
    'NODE_ENV',
    'DATABASE_URL',
    'SESSION_SECRET',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'MODAL_TOKEN_ID',
    'MODAL_TOKEN_SECRET'
  ];
  
  const missingVars = requiredVars.filter(varName => !environmentVariables[varName]);
  
  if (missingVars.length > 0) {
    log(`❌ Missing required environment variables: ${missingVars.join(', ')}`, 'red');
    throw new Error('Missing required environment variables');
  }
  
  // Validate secret strength
  const secrets = ['SESSION_SECRET', 'JWT_SECRET', 'SECRETS_ENCRYPTION_KEY'];
  for (const secret of secrets) {
    if (environmentVariables[secret] && environmentVariables[secret].length < 32) {
      log(`❌ ${secret} must be at least 32 characters long`, 'red');
      throw new Error(`Weak ${secret}`);
    }
  }
  
  // Validate database URL has SSL
  if (environmentVariables.DATABASE_URL && !environmentVariables.DATABASE_URL.includes('sslmode=require')) {
    log('❌ Database URL must include sslmode=require for production', 'red');
    throw new Error('Database SSL not enforced');
  }
  
  log('✅ Environment configuration validation passed', 'green');
}

/**
 * Main execution function
 */
async function main() {
  try {
    log('🚀 Starting production environment setup...', 'magenta');
    log('=' .repeat(50), 'magenta');
    
    // Retrieve secrets from AWS Secrets Manager
    const secrets = await getSecrets();
    
    // Configure environment variables
    const environmentVariables = configureEnvironmentVariables(secrets);
    
    // Validate configuration
    await validateEnvironment(environmentVariables);
    
    // Update Amplify environment
    if (APP_ID) {
      await updateAmplifyEnvironment(environmentVariables);
    } else {
      log('⚠️  AMPLIFY_APP_ID not set, skipping Amplify update', 'yellow');
    }
    
    // Generate local .env file
    generateLocalEnvFile(environmentVariables);
    
    log('\n🎉 Production environment setup completed successfully!', 'green');
    log('=' .repeat(50), 'green');
    
    log('\n📋 Next Steps:', 'cyan');
    log('1. Verify all secrets are correctly set in AWS Secrets Manager', 'yellow');
    log('2. Test the application locally with: NODE_ENV=production npm start', 'yellow');
    log('3. Deploy to Amplify and verify all functionality', 'yellow');
    log('4. Run security validation: npm run security:production-ready', 'yellow');
    
  } catch (error) {
    log(`\n❌ Production environment setup failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  getSecrets,
  configureEnvironmentVariables,
  updateAmplifyEnvironment,
  validateEnvironment
};
