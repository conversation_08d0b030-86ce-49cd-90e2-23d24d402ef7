import sanitizeHtmlLib from 'sanitize-html';

/**
 * Sanitizes HTML content to prevent XSS attacks
 * @param html HTML content to sanitize
 * @returns Sanitized HTML content
 */
export function sanitizeHtml(html: string): string {
  return sanitizeHtmlLib(html, {
    allowedTags: sanitizeHtmlLib.defaults.allowedTags.concat(['img', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span', 'div']),
    allowedAttributes: {
      ...sanitizeHtmlLib.defaults.allowedAttributes,
      '*': ['class', 'style'],
      'img': ['src', 'alt', 'width', 'height', 'style'],
      'a': ['href', 'name', 'target', 'rel'],
    },
    allowedStyles: {
      '*': {
        'color': [/.*/],
        'background-color': [/.*/],
        'text-align': [/.*/],
        'font-size': [/.*/],
        'font-weight': [/.*/],
        'font-family': [/.*/],
        'text-decoration': [/.*/],
        'padding': [/.*/],
        'margin': [/.*/],
        'display': [/.*/],
        'width': [/.*/],
        'height': [/.*/],
        'max-width': [/.*/],
        'max-height': [/.*/],
        'border': [/.*/],
        'border-radius': [/.*/],
      },
    },
  });
}

/**
 * Checks if a domain is configured properly for sending emails
 * @param domain Domain to check
 * @returns True if the domain is valid for sending emails
 */
export function isCustomDomain(domain: string): boolean {
  // Always reject these domains as they're typically not authorized
  const invalidDomains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'aol.com',
    'icloud.com',
    'protonmail.com',
    'mail.com',
    'zoho.com',
    'resend.dev', // This one is special - needs to be configured in Resend
  ];

  if (invalidDomains.includes(domain.toLowerCase())) {
    return false;
  }

  // For development/testing purposes, consider hardcoding valid domains
  // This would normally be based on domains verified in Resend or your email provider
  const validDomains = [
    'example.com', // Placeholder for testing
    'yourdomain.com', // Replace with actual domains
  ];

  if (process.env.NODE_ENV === 'development') {
    // In development, consider all domains valid except the invalid ones
    return true;
  }

  return validDomains.includes(domain.toLowerCase());
}