import React from 'react';

export const StoryboardHint = (
  <div className="space-y-3">
    <p>The <strong>Storyboard Editor</strong> helps you plan your course videos visually.</p>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Key features:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Create different scene types (title, content, summary, quiz, transitions)</li>
        <li>Drag and drop to reorder scenes</li>
        <li>Add narration text for voice-over</li>
        <li>Set duration for each scene</li>
        <li>Upload or generate images for visuals</li>
      </ul>
    </div>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Tips:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Use title scenes for introducing new sections</li>
        <li>Keep content scenes focused on a single idea</li>
        <li>Add summary scenes to reinforce key points</li>
        <li>Use transitions to create smooth flow between topics</li>
      </ul>
    </div>
  </div>
);

export const PdfDownloadHint = (
  <div className="space-y-3">
    <p>The <strong>PDF Download</strong> feature allows you to export your course content.</p>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">What's included:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Course title and description</li>
        <li>All module content with headings</li>
        <li>Lesson content, including scripts</li>
        <li>References and resources</li>
        <li>Visual elements from your course</li>
      </ul>
    </div>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Use cases:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Create handouts for students</li>
        <li>Share course outlines with collaborators</li>
        <li>Have offline access to your content</li>
        <li>Print materials for in-person sessions</li>
      </ul>
    </div>
  </div>
);

export const VoiceSelectionHint = (
  <div className="space-y-3">
    <p>The <strong>Voice Selection</strong> tool lets you choose the perfect narrator for your course.</p>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Features:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Multiple voice options with different styles and accents</li>
        <li>Preview voice samples before selecting</li>
        <li>Adjust speech parameters like speed and pitch</li>
        <li>Support for 25+ languages</li>
      </ul>
    </div>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Best practices:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Choose a voice that matches your course topic and tone</li>
        <li>Use the same voice throughout your course for consistency</li>
        <li>For multiple narrators, select voices that complement each other</li>
        <li>Coqui TTS is our primary option with high-quality results</li>
      </ul>
    </div>
  </div>
);

export const MicroLearningHint = (
  <div className="space-y-3">
    <p>The <strong>Micro-Learning Mode</strong> breaks your course into bite-sized segments for better knowledge retention.</p>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">How it works:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Automatically divides content into 5-10 minute segments</li>
        <li>Adds knowledge checks between segments</li>
        <li>Provides recap summaries of key points</li>
        <li>Creates natural breaks to improve focus</li>
      </ul>
    </div>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Why use it:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Improves information retention by up to 60%</li>
        <li>Reduces cognitive load for learners</li>
        <li>Makes complex topics more approachable</li>
        <li>Allows for flexible learning schedules</li>
      </ul>
    </div>
  </div>
);

export const AIVideoGenerationHint = (
  <div className="space-y-3">
    <p>The <strong>AI Video Generation</strong> tool creates professional videos from your content.</p>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Features:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Converts your script into engaging visual content</li>
        <li>Adds animations and visual elements automatically</li>
        <li>Synchronizes narration with on-screen elements</li>
        <li>Supports custom branding and styling</li>
      </ul>
    </div>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Credits:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Video generation requires at least 200 AI credits</li>
        <li>Higher quality or longer videos use more credits</li>
        <li>Credits are separate from your subscription plan</li>
        <li>Free users can preview with watermarked videos</li>
      </ul>
    </div>
  </div>
);

export const TemplateSelectionHint = (
  <div className="space-y-3">
    <p>The <strong>Course Templates</strong> give you a head start on creating professional content.</p>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Template types:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>Subject-specific templates for various disciplines</li>
        <li>Pedagogical frameworks (e.g., 70/20/10, Bloom's Taxonomy)</li>
        <li>Learning formats (e.g., case studies, workshops)</li>
        <li>Industry-specific training templates</li>
      </ul>
    </div>
    
    <div className="space-y-2">
      <p className="font-semibold text-sm">Customization:</p>
      <ul className="list-disc pl-5 text-sm space-y-1">
        <li>All templates are fully customizable</li>
        <li>Add, remove, or reorder sections as needed</li>
        <li>Replace placeholder content with your own</li>
        <li>Save your customized templates for future use</li>
      </ul>
    </div>
  </div>
);

// Map string keys to hint content
export const hintMap: Record<string, React.ReactNode> = {
  storyboard: StoryboardHint,
  pdfDownload: PdfDownloadHint,
  voiceSelection: VoiceSelectionHint,
  microLearning: MicroLearningHint,
  aiVideoGeneration: AIVideoGenerationHint,
  templateSelection: TemplateSelectionHint
};