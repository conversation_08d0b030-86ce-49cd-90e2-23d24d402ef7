import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Pause, 
  Square, 
  Download, 
  Upload, 
  Video, 
  Image, 
  Eye,
  Wand2,
  FileVideo,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Trash2,
  Plus,
  Info,
  ArrowRight,
  GripVertical,
  X
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import MediaSelector from './MediaSelector';
import { useQuery } from '@tanstack/react-query';

interface VideoProject {
  id: string;
  moduleId: string;
  lessonId: string;
  title: string;
  script: string;
  status: 'draft' | 'processing' | 'completed' | 'error';
  progress: number;
  assignedMedia?: any;
  audioUrl?: string;
  videoUrl?: string;
  createdAt: Date;
  subtitles?: string;
}

interface MediaItem {
  id: string;
  type: 'image' | 'video';
  name: string;
  url: string;
  duration?: number;
  thumbnailUrl?: string;
}

interface VideoProductionStudioProps {
  courseScripts?: Record<string, Record<string, string>>;
  onScriptChange?: (moduleId: string, lessonId: string, script: string) => void;
  currentModuleId?: string;
  currentLessonId?: string;
}

export default function VideoProductionStudio({ 
  courseScripts = {}, 
  onScriptChange,
  currentModuleId = "module-0",
  currentLessonId = "lesson-0-0"
}: VideoProductionStudioProps) {
  const [videoProjects, setVideoProjects] = useState<VideoProject[]>([]);
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [showMediaSelector, setShowMediaSelector] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaItem[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();
  const videoRef = useRef<HTMLVideoElement>(null);

  // Check if voice exists for current lesson
  const checkVoiceExists = async (moduleId: string, lessonId: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/ai/voice-status/${moduleId}/${lessonId}`);
      if (response.ok) {
        const text = await response.text();
        if (text.trim()) {
          const data = JSON.parse(text);
          return data.exists;
        }
      }
    } catch (error) {
      console.log('Voice check failed:', error);
    }
    return false;
  };

  const [hasVoice, setHasVoice] = useState(false);

  useEffect(() => {
    const checkVoice = async () => {
      const voiceExists = await checkVoiceExists(currentModuleId, currentLessonId);
      setHasVoice(voiceExists);
    };
    checkVoice();
  }, [currentModuleId, currentLessonId]);

  // Initialize project if script exists
  useEffect(() => {
    if (!courseScripts || typeof courseScripts !== 'object') {
      return;
    }
    
    const script = courseScripts[currentModuleId]?.[currentLessonId];
    if (script && script.trim()) {
      const existingProject = videoProjects.find(p => p.moduleId === currentModuleId && p.lessonId === currentLessonId);
      if (!existingProject) {
        const newProject: VideoProject = {
          id: `${currentModuleId}-${currentLessonId}`,
          moduleId: currentModuleId,
          lessonId: currentLessonId,
          title: `Lesson ${currentLessonId.split('-').pop()}`,
          script,
          status: 'draft',
          progress: 0,
          createdAt: new Date()
        };
        setVideoProjects(prev => [...prev, newProject]);
        setSelectedProject(newProject.id);
      }
    }
  }, [courseScripts, currentModuleId, currentLessonId, videoProjects]);

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = Array.from(selectedMedia);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setSelectedMedia(items);
  };

  const removeMediaItem = (index: number) => {
    setSelectedMedia(prev => prev.filter((_, i) => i !== index));
  };

  const generateVideo = async () => {
    if (!selectedProject) {
      toast({
        title: "No Project Selected",
        description: "Please select a video project first.",
        variant: "destructive"
      });
      return;
    }

    const project = videoProjects.find(p => p.id === selectedProject);
    if (!project) return;

    if (!hasVoice) {
      toast({
        title: "Voice Required",
        description: "Please generate voice in the Script section first.",
        variant: "destructive"
      });
      return;
    }

    if (selectedMedia.length === 0) {
      toast({
        title: "Media Required",
        description: "Please select at least one media item for the video.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    
    try {
      // Update project status
      setVideoProjects(prev => prev.map(p => 
        p.id === selectedProject ? { ...p, status: 'processing' as const, progress: 10 } : p
      ));

      const response = await fetch('/api/ai/assemble-video', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          moduleId: project.moduleId,
          lessonId: project.lessonId,
          script: project.script,
          mediaItems: selectedMedia,
          includeSubtitles: true
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to start video assembly: ${errorText}`);
      }

      const responseText = await response.text();
      if (!responseText.trim()) {
        throw new Error('Empty response from server');
      }

      const { jobId } = JSON.parse(responseText);

      // Poll for completion
      const pollProgress = async () => {
        try {
          const statusResponse = await fetch(`/api/ai/video-status/${jobId}`);
          if (statusResponse.ok) {
            const status = await statusResponse.json();
            
            setVideoProjects(prev => prev.map(p => 
              p.id === selectedProject ? { 
                ...p, 
                progress: status.progress,
                status: status.status === 'completed' ? 'completed' as const : 'processing' as const
              } : p
            ));

            if (status.status === 'completed') {
              setVideoProjects(prev => prev.map(p => 
                p.id === selectedProject ? { 
                  ...p, 
                  videoUrl: status.videoUrl,
                  audioUrl: status.audioUrl,
                  subtitles: status.subtitles,
                  status: 'completed' as const
                } : p
              ));
              
              toast({
                title: "Video Generated Successfully!",
                description: "Your video lesson is ready for preview.",
              });
              setIsGenerating(false);
            } else if (status.status === 'error') {
              throw new Error(status.error || 'Video generation failed');
            } else {
              setTimeout(pollProgress, 2000);
            }
          }
        } catch (error) {
          console.error('Polling error:', error);
          setTimeout(pollProgress, 3000);
        }
      };

      setTimeout(pollProgress, 1000);

    } catch (error: any) {
      console.error('Video generation error:', error);
      setVideoProjects(prev => prev.map(p => 
        p.id === selectedProject ? { ...p, status: 'error' as const } : p
      ));
      
      toast({
        title: "Video Generation Failed",
        description: error.message || "An error occurred during video generation.",
        variant: "destructive"
      });
      setIsGenerating(false);
    }
  };

  const selectedProjectData = videoProjects.find(p => p.id === selectedProject);

  return (
    <div className="space-y-6">
      {/* Voice Generation Notice */}
      <Alert className="border-blue-200 bg-blue-50">
        <Info className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <div className="flex items-center justify-between">
            <div>
              <strong>Voice Generation Required:</strong> Generate voice in the Script section before creating videos.
              {hasVoice && <Badge className="ml-2 bg-green-100 text-green-800">Voice Ready</Badge>}
              {!hasVoice && <Badge className="ml-2 bg-yellow-100 text-yellow-800">Voice Missing</Badge>}
            </div>
            <ArrowRight className="h-4 w-4 text-blue-600" />
          </div>
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Panel - Project List & Media Selection */}
        <div className="lg:col-span-1 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileVideo className="h-5 w-5" />
                Video Projects
              </CardTitle>
            </CardHeader>
            <CardContent>
              {videoProjects.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Video className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No video projects yet.</p>
                  <p className="text-sm">Scripts will appear here automatically.</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {videoProjects.map(project => (
                    <div
                      key={project.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-all ${
                        selectedProject === project.id
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => setSelectedProject(project.id)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{project.title}</h4>
                        <Badge variant={
                          project.status === 'completed' ? 'default' :
                          project.status === 'processing' ? 'secondary' :
                          project.status === 'error' ? 'destructive' : 'outline'
                        }>
                          {project.status === 'processing' ? (
                            <div className="flex items-center gap-1">
                              <RefreshCw className="h-3 w-3 animate-spin" />
                              {project.progress}%
                            </div>
                          ) : project.status === 'completed' ? (
                            <CheckCircle className="h-3 w-3" />
                          ) : project.status === 'error' ? (
                            <AlertCircle className="h-3 w-3" />
                          ) : (
                            'Draft'
                          )}
                        </Badge>
                      </div>
                      {project.status === 'processing' && (
                        <Progress value={project.progress} className="h-2" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Media Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Image className="h-5 w-5" />
                Media Selection
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button 
                  onClick={() => setShowMediaSelector(true)}
                  className="w-full"
                  variant="outline"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Media
                </Button>

                {selectedMedia.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      Selected Media ({selectedMedia.length})
                    </Label>
                    <DragDropContext onDragEnd={handleDragEnd}>
                      <Droppable droppableId="media-list">
                        {(provided) => (
                          <div
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                            className="space-y-2"
                          >
                            {selectedMedia.map((item, index) => (
                              <Draggable key={item.id} draggableId={item.id} index={index}>
                                {(provided) => (
                                  <div
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    className="flex items-center gap-2 p-2 bg-muted rounded border"
                                  >
                                    <div {...provided.dragHandleProps}>
                                      <GripVertical className="h-4 w-4 text-muted-foreground" />
                                    </div>
                                    {item.type === 'video' ? (
                                      <Video className="h-4 w-4" />
                                    ) : (
                                      <Image className="h-4 w-4" />
                                    )}
                                    <span className="flex-1 text-sm truncate">{item.name}</span>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => removeMediaItem(index)}
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                )}
                              </Draggable>
                            ))}
                            {provided.placeholder}
                          </div>
                        )}
                      </Droppable>
                    </DragDropContext>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Panel - Video Preview & Controls */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Video Assembly
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={generateVideo}
                    disabled={!selectedProject || isGenerating || !hasVoice || selectedMedia.length === 0}
                    className="flex items-center gap-2"
                  >
                    <Wand2 className="h-4 w-4" />
                    {isGenerating ? 'Assembling...' : 'Generate Video'}
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedProjectData ? (
                <div className="space-y-6">
                  {/* Project Info */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Project</Label>
                      <p className="text-sm text-muted-foreground">{selectedProjectData.title}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Status</Label>
                      <p className="text-sm text-muted-foreground capitalize">{selectedProjectData.status}</p>
                    </div>
                  </div>

                  <Separator />

                  {/* Video Preview */}
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Preview</Label>
                    <div className="aspect-video bg-black rounded-lg flex items-center justify-center">
                      {selectedProjectData.videoUrl ? (
                        <video
                          ref={videoRef}
                          src={selectedProjectData.videoUrl}
                          controls
                          className="w-full h-full rounded-lg"
                        />
                      ) : (
                        <div className="text-center text-white/60">
                          <Video className="h-16 w-16 mx-auto mb-4" />
                          <p>Video preview will appear here</p>
                          <p className="text-sm mt-2">
                            {!hasVoice && "Generate voice first, then "}
                            {selectedMedia.length === 0 && "select media, then "}
                            click "Generate Video"
                          </p>
                        </div>
                      )}
                    </div>

                    {selectedProjectData.videoUrl && (
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12 text-muted-foreground">
                  <Video className="h-16 w-16 mx-auto mb-4 opacity-50" />
                  <p>Select a project to start video assembly</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Media Selector Modal */}
      {showMediaSelector && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-4xl h-3/4 flex flex-col">
            <div className="p-4 border-b flex items-center justify-between">
              <h2 className="text-lg font-semibold">Select Media</h2>
              <Button
                variant="ghost"
                onClick={() => setShowMediaSelector(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex-1 p-4">
              <div className="grid grid-cols-3 gap-4">
                {/* Sample media items */}
                {[
                  { id: '1', name: 'Business Meeting', type: 'image', url: '/api/placeholder/400/300' },
                  { id: '2', name: 'Analytics Chart', type: 'image', url: '/api/placeholder/400/300' },
                  { id: '3', name: 'Team Discussion', type: 'video', url: '/api/placeholder/400/300' },
                  { id: '4', name: 'Growth Graph', type: 'image', url: '/api/placeholder/400/300' },
                  { id: '5', name: 'Office Workspace', type: 'image', url: '/api/placeholder/400/300' },
                  { id: '6', name: 'Data Presentation', type: 'video', url: '/api/placeholder/400/300' }
                ].map(item => (
                  <div
                    key={item.id}
                    className="border rounded-lg p-3 cursor-pointer hover:border-primary transition-colors"
                    onClick={() => {
                      setSelectedMedia(prev => [...prev, {
                        id: item.id,
                        type: item.type as 'image' | 'video',
                        name: item.name,
                        url: item.url
                      }]);
                      setShowMediaSelector(false);
                    }}
                  >
                    <div className="aspect-video bg-muted rounded mb-2 flex items-center justify-center">
                      {item.type === 'video' ? (
                        <Video className="h-8 w-8 text-muted-foreground" />
                      ) : (
                        <Image className="h-8 w-8 text-muted-foreground" />
                      )}
                    </div>
                    <p className="text-sm font-medium truncate">{item.name}</p>
                    <p className="text-xs text-muted-foreground capitalize">{item.type}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}