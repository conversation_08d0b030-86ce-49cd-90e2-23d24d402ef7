import React, { useState, useEffect } from 'react';
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { Button } from './button';
import { Loader2, CreditCard, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Card } from './card';
import { useQueryClient } from '@tanstack/react-query';

interface CheckoutFormProps {
  sessionId: string;
  onSuccess: () => void;
  selectedPlan: any;
}

export function CheckoutForm({ sessionId, onSuccess, selectedPlan }: CheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const queryClient = useQueryClient();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'succeeded' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [redirecting, setRedirecting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet. Make sure to disable
      // form submission until Stripe.js has loaded.
      return;
    }

    setIsProcessing(true);
    setPaymentStatus('processing');
    setErrorMessage(null);

    // Get a reference to a mounted CardElement
    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      setErrorMessage("Payment form failed to load. Please try again.");
      setIsProcessing(false);
      setPaymentStatus('error');
      return;
    }

    try {
      // Use your card Element to create a payment method
      const { error, paymentIntent } = await stripe.confirmCardPayment(sessionId, {
        payment_method: {
          card: cardElement,
          billing_details: {
            // You can include additional customer details here
          }
        }
      });

      if (error) {
        setErrorMessage(error.message || "An error occurred with your payment.");
        setPaymentStatus('error');
        toast({
          title: "Payment Failed",
          description: error.message || "Your payment could not be processed.",
          variant: "destructive",
        });
      } else if (paymentIntent.status === 'succeeded') {
        // The payment succeeded!
        setPaymentStatus('succeeded');
        toast({
          title: "Payment Successful",
          description: "Your subscription has been processed successfully. Redirecting you to dashboard...",
          variant: "default",
        });

        // Update the user's subscription in the database
        try {
          await apiRequest('POST', '/api/payments/update-subscription', {
            paymentIntentId: paymentIntent.id,
            planId: selectedPlan.id
          });
          
          // Refresh user data to get updated subscription status
          queryClient.invalidateQueries({ queryKey: ['/api/auth/me'] });
          
          // Set redirecting state to true for UI feedback
          setRedirecting(true);
          
          // Let the parent component handle the redirection to dashboard
          // The parent component will handle this via onSuccess
          
        } catch (updateError) {
          console.error('Error updating subscription:', updateError);
          // Don't show this error to the user since the payment was successful
        }

        // Call the onSuccess callback
        onSuccess();
      }
    } catch (error: any) {
      setErrorMessage(error.message || "An unexpected error occurred.");
      setPaymentStatus('error');
      toast({
        title: "Payment Error",
        description: error.message || "An unexpected error occurred with your payment.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {paymentStatus === 'succeeded' ? (
        <div className="text-center py-4">
          <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Payment Successful!</h3>
          <p>Your subscription has been activated.</p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="bg-gray-50 p-4 rounded-md mb-4">
            <h3 className="font-medium text-gray-700 mb-2">Order Summary</h3>
            <div className="flex justify-between mb-1">
              <span>{selectedPlan.name} Plan</span>
              <span>${selectedPlan.monthlyPrice}/mo</span>
            </div>
            <div className="border-t pt-2 mt-2 flex justify-between font-medium">
              <span>Total</span>
              <span>${selectedPlan.monthlyPrice}/month</span>
            </div>
          </div>

          <div className="space-y-4">
            <label className="block text-sm font-medium text-gray-700">
              Card Details
            </label>
            <div className="border rounded-md p-3">
              <CardElement
                options={{
                  style: {
                    base: {
                      fontSize: '16px',
                      color: '#424770',
                      '::placeholder': {
                        color: '#aab7c4',
                      },
                    },
                    invalid: {
                      color: '#9e2146',
                    },
                  },
                }}
              />
            </div>
            {errorMessage && (
              <div className="text-red-500 text-sm mt-1">{errorMessage}</div>
            )}
          </div>

          <Button
            type="submit"
            className="w-full"
            disabled={!stripe || isProcessing}
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Pay ${selectedPlan.monthlyPrice}
              </>
            )}
          </Button>
          
          <div className="text-xs text-gray-500 text-center mt-4">
            <p>Your payment is secure. We use Stripe's secure payment system.</p>
            <p className="mt-1">Test card: 4242 4242 4242 4242 | Any future date | Any 3 digits for CVC</p>
          </div>
        </form>
      )}
    </div>
  );
}