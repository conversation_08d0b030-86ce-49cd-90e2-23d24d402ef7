import { db } from "../server/db";
import { badges, dailyChallenges } from "@shared/schema";

const defaultBadges = [
  // Learning Badges
  {
    name: "First Steps",
    description: "Complete your first lesson",
    icon: "🎯",
    category: "learning",
    rarity: "common",
    xpReward: 50,
    condition: {
      type: "activity_count",
      activityType: "lesson_completed",
      count: 1
    }
  },
  {
    name: "Knowledge Seeker",
    description: "Complete 10 lessons",
    icon: "📚",
    category: "learning",
    rarity: "common",
    xpReward: 100,
    condition: {
      type: "activity_count",
      activityType: "lesson_completed",
      count: 10
    }
  },
  {
    name: "Scholar",
    description: "Complete 50 lessons",
    icon: "🎓",
    category: "learning",
    rarity: "rare",
    xpReward: 250,
    condition: {
      type: "activity_count",
      activityType: "lesson_completed",
      count: 50
    }
  },
  {
    name: "Master Learner",
    description: "Complete 100 lessons",
    icon: "👑",
    category: "learning",
    rarity: "epic",
    xpReward: 500,
    condition: {
      type: "activity_count",
      activityType: "lesson_completed",
      count: 100
    }
  },

  // Course Completion Badges
  {
    name: "Course Finisher",
    description: "Complete your first course",
    icon: "🏆",
    category: "achievement",
    rarity: "rare",
    xpReward: 200,
    condition: {
      type: "course_completion",
      count: 1
    }
  },
  {
    name: "Course Collector",
    description: "Complete 5 courses",
    icon: "🎖️",
    category: "achievement",
    rarity: "epic",
    xpReward: 500,
    condition: {
      type: "course_completion",
      count: 5
    }
  },
  {
    name: "Learning Machine",
    description: "Complete 10 courses",
    icon: "🤖",
    category: "achievement",
    rarity: "legendary",
    xpReward: 1000,
    condition: {
      type: "course_completion",
      count: 10
    }
  },

  // Streak Badges
  {
    name: "Getting Started",
    description: "Learn for 3 days in a row",
    icon: "🔥",
    category: "streak",
    rarity: "common",
    xpReward: 75,
    condition: {
      type: "streak",
      days: 3
    }
  },
  {
    name: "Consistency King",
    description: "Learn for 7 days in a row",
    icon: "⚡",
    category: "streak",
    rarity: "rare",
    xpReward: 150,
    condition: {
      type: "streak",
      days: 7
    }
  },
  {
    name: "Streak Master",
    description: "Learn for 30 days in a row",
    icon: "🌟",
    category: "streak",
    rarity: "epic",
    xpReward: 400,
    condition: {
      type: "streak",
      days: 30
    }
  },
  {
    name: "Unstoppable",
    description: "Learn for 100 days in a row",
    icon: "💎",
    category: "streak",
    rarity: "legendary",
    xpReward: 1000,
    condition: {
      type: "streak",
      days: 100
    }
  },

  // Level Badges
  {
    name: "Level Up",
    description: "Reach level 5",
    icon: "⭐",
    category: "milestone",
    rarity: "common",
    xpReward: 100,
    condition: {
      type: "level_reached",
      level: 5
    }
  },
  {
    name: "Rising Star",
    description: "Reach level 10",
    icon: "🌠",
    category: "milestone",
    rarity: "rare",
    xpReward: 200,
    condition: {
      type: "level_reached",
      level: 10
    }
  },
  {
    name: "Expert",
    description: "Reach level 25",
    icon: "🏅",
    category: "milestone",
    rarity: "epic",
    xpReward: 500,
    condition: {
      type: "level_reached",
      level: 25
    }
  },
  {
    name: "Legend",
    description: "Reach level 50",
    icon: "👑",
    category: "milestone",
    rarity: "legendary",
    xpReward: 1000,
    condition: {
      type: "level_reached",
      level: 50
    }
  },

  // Special Activity Badges
  {
    name: "Early Bird",
    description: "Start a course before 9 AM",
    icon: "🌅",
    category: "achievement",
    rarity: "rare",
    xpReward: 150,
    condition: {
      type: "activity_count",
      activityType: "early_morning_learning",
      count: 1
    }
  },
  {
    name: "Night Owl",
    description: "Learn after 10 PM",
    icon: "🦉",
    category: "achievement",
    rarity: "rare",
    xpReward: 150,
    condition: {
      type: "activity_count",
      activityType: "late_night_learning",
      count: 1
    }
  },
  {
    name: "Speed Learner",
    description: "Complete 5 lessons in one day",
    icon: "⚡",
    category: "achievement",
    rarity: "epic",
    xpReward: 300,
    condition: {
      type: "activity_count",
      activityType: "daily_lesson_burst",
      count: 1
    }
  }
];

const defaultChallenges = [
  {
    name: "Daily Lesson",
    description: "Complete 1 lesson today",
    icon: "📖",
    targetType: "lessons_completed",
    targetValue: 1,
    xpReward: 25,
    isActive: true,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours from now
  },
  {
    name: "Study Session",
    description: "Spend 30 minutes learning today",
    icon: "⏰",
    targetType: "time_spent",
    targetValue: 1800, // 30 minutes in seconds
    xpReward: 50,
    isActive: true,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000)
  },
  {
    name: "Course Explorer",
    description: "Start a new course today",
    icon: "🚀",
    targetType: "courses_started",
    targetValue: 1,
    xpReward: 75,
    isActive: true,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000)
  },
  {
    name: "Knowledge Marathon",
    description: "Complete 3 lessons today",
    icon: "🏃‍♂️",
    targetType: "lessons_completed",
    targetValue: 3,
    xpReward: 100,
    isActive: true,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000)
  },
  {
    name: "Deep Dive",
    description: "Spend 1 hour learning today",
    icon: "🏊‍♀️",
    targetType: "time_spent",
    targetValue: 3600, // 1 hour in seconds
    xpReward: 150,
    isActive: true,
    validFrom: new Date(),
    validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000)
  }
];

async function initializeGamification() {
  try {
    console.log("🎮 Initializing gamification system...");

    // Check if badges already exist
    const existingBadges = await db.select().from(badges).limit(1);
    
    if (existingBadges.length === 0) {
      console.log("📋 Creating default badges...");
      await db.insert(badges).values(defaultBadges);
      console.log(`✅ Created ${defaultBadges.length} default badges`);
    } else {
      console.log("🎯 Badges already exist, skipping badge creation");
    }

    // Check if challenges already exist
    const existingChallenges = await db.select().from(dailyChallenges).limit(1);
    
    if (existingChallenges.length === 0) {
      console.log("🎯 Creating default challenges...");
      await db.insert(dailyChallenges).values(defaultChallenges);
      console.log(`✅ Created ${defaultChallenges.length} default challenges`);
    } else {
      console.log("🏆 Challenges already exist, skipping challenge creation");
    }

    console.log("🎉 Gamification system initialized successfully!");
    
  } catch (error) {
    console.error("❌ Error initializing gamification system:", error);
    throw error;
  }
}

// Run initialization if this file is executed directly
initializeGamification()
  .then(() => {
    console.log("✨ Gamification initialization complete!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Gamification initialization failed:", error);
    process.exit(1);
  });

export { initializeGamification };