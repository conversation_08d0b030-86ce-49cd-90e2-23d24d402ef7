import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { v4 as uuidv4 } from 'uuid';
import { StockVideoSelector } from "./StockVideoSelector";
import { 
  AlertCircle, 
  Upload, 
  File, 
  FileText, 
  Film, 
  Image, 
  ArrowRight, 
  ArrowLeft, 
  X, 
  Search,
  Play,
  CheckCircle,
  ExternalLink
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Section, Lesson, CourseStructureData } from "./CourseSectionsBuilder";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";

interface ContentUploaderProps {
  courseStructure: CourseStructureData;
  onSubmit: (data: CourseStructureData) => void;
  onBack: () => void;
}

export function ContentUploader({ courseStructure, onSubmit, onBack }: ContentUploaderProps) {
  const [currentStructure, setCurrentStructure] = useState<CourseStructureData>(courseStructure);
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(
    courseStructure.sections.length > 0 ? courseStructure.sections[0].id : null
  );
  const [selectedLessonId, setSelectedLessonId] = useState<string | null>(null);
  const [isMediaLibraryOpen, setIsMediaLibraryOpen] = useState(false);
  const [isStockVideoSelectorOpen, setIsStockVideoSelectorOpen] = useState(false);
  const [videoSearchTerm, setVideoSearchTerm] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();

  // Get the selected section
  const selectedSection = selectedSectionId 
    ? currentStructure.sections.find(section => section.id === selectedSectionId) 
    : null;

  // Get the selected lesson
  const selectedLesson = selectedLessonId && selectedSection
    ? selectedSection.lessons.find(lesson => lesson.id === selectedLessonId)
    : null;

  // Flag to check if content is uploaded for all lessons
  const allLessonsHaveContent = currentStructure.sections.every(section => 
    section.lessons.every(lesson => {
      if (lesson.type === 'video') return !!lesson.videoUrl;
      if (lesson.type === 'text') return !!lesson.content && lesson.content.length > 0;
      if (lesson.type === 'quiz') return true; // Quizzes are handled in the next step
      return false;
    })
  );

  // Function to update lesson content
  const updateLessonContent = (sectionId: string, lessonId: string, updates: Partial<Lesson>) => {
    setCurrentStructure(prevStructure => ({
      sections: prevStructure.sections.map(section => 
        section.id === sectionId
          ? {
              ...section,
              lessons: section.lessons.map(lesson => 
                lesson.id === lessonId
                  ? { ...lesson, ...updates }
                  : lesson
              )
            }
          : section
      )
    }));
  };

  // Handle text content change
  const handleTextContentChange = (content: string) => {
    if (!selectedSectionId || !selectedLessonId) return;
    
    updateLessonContent(selectedSectionId, selectedLessonId, { content });
  };

  // Handle video URL change
  const handleVideoUrlChange = (videoUrl: string) => {
    if (!selectedSectionId || !selectedLessonId) return;
    
    updateLessonContent(selectedSectionId, selectedLessonId, { videoUrl });
  };

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || !event.target.files.length || !selectedLessonId || !selectedSectionId) return;
    
    const file = event.target.files[0];
    const formData = new FormData();
    formData.append('file', file);
    
    setIsUploading(true);
    
    try {
      const response = await fetch('/api/media/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload file');
      }
      
      const data = await response.json();
      
      if (selectedLesson?.type === 'video') {
        updateLessonContent(selectedSectionId, selectedLessonId, { videoUrl: data.url });
      }
      
      toast({
        title: 'File uploaded',
        description: 'Your file has been successfully uploaded.',
      });
    } catch (error) {
      toast({
        title: 'Upload failed',
        description: 'Failed to upload your file. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Handle lesson selection
  const handleLessonSelect = (sectionId: string, lessonId: string) => {
    setSelectedSectionId(sectionId);
    setSelectedLessonId(lessonId);
  };

  // Handle submit
  const handleSubmit = () => {
    if (!allLessonsHaveContent) {
      toast({
        title: "Some lessons are missing content",
        description: "Please add content to all lessons before proceeding.",
        variant: "destructive",
      });
      return;
    }
    
    onSubmit(currentStructure);
  };

  // Media library mock data (in a real app, this would come from an API)
  const mockVideos = [
    { id: 1, title: "Introduction to Web Development", url: "https://example.com/videos/intro-web-dev.mp4", thumbnail: "https://via.placeholder.com/300x169?text=Web+Dev+Intro" },
    { id: 2, title: "HTML Fundamentals", url: "https://example.com/videos/html-fundamentals.mp4", thumbnail: "https://via.placeholder.com/300x169?text=HTML+Fundamentals" },
    { id: 3, title: "CSS Styling Basics", url: "https://example.com/videos/css-basics.mp4", thumbnail: "https://via.placeholder.com/300x169?text=CSS+Basics" },
    { id: 4, title: "JavaScript for Beginners", url: "https://example.com/videos/js-beginners.mp4", thumbnail: "https://via.placeholder.com/300x169?text=JS+Beginners" },
    { id: 5, title: "Responsive Design", url: "https://example.com/videos/responsive-design.mp4", thumbnail: "https://via.placeholder.com/300x169?text=Responsive+Design" },
    { id: 6, title: "Modern Web Framework Introduction", url: "https://example.com/videos/frameworks.mp4", thumbnail: "https://via.placeholder.com/300x169?text=Web+Frameworks" },
  ];
  
  // Filter videos based on search term
  const filteredVideos = mockVideos.filter(video => 
    video.title.toLowerCase().includes(videoSearchTerm.toLowerCase())
  );

  // Handle media library item selection
  const handleMediaSelect = (url: string) => {
    if (!selectedSectionId || !selectedLessonId) return;
    
    updateLessonContent(selectedSectionId, selectedLessonId, { videoUrl: url });
    setIsMediaLibraryOpen(false);
  };

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Content Upload</h2>
      <p className="text-muted-foreground">
        Add content to your lessons. Each lesson can be a video, text, or a quiz.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left sidebar - Course structure navigation */}
        <div className="md:col-span-1">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Course Structure</CardTitle>
              <CardDescription>
                Select a lesson to add content
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[450px]">
                <Accordion type="single" collapsible className="px-4 pb-4">
                  {currentStructure.sections.map((section) => (
                    <AccordionItem key={section.id} value={section.id}>
                      <AccordionTrigger className="py-3 text-lg">{section.title}</AccordionTrigger>
                      <AccordionContent>
                        <div className="pl-2 space-y-2">
                          {section.lessons.map((lesson) => {
                            // Determine if lesson has content
                            let hasContent = false;
                            if (lesson.type === 'video') hasContent = !!lesson.videoUrl;
                            else if (lesson.type === 'text') hasContent = !!lesson.content && lesson.content.length > 0;
                            else if (lesson.type === 'quiz') hasContent = true; // Quizzes handled in next step
                            
                            return (
                              <div 
                                key={lesson.id}
                                className={`flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors ${
                                  selectedLessonId === lesson.id 
                                    ? 'bg-muted' 
                                    : 'hover:bg-muted/50'
                                }`}
                                onClick={() => handleLessonSelect(section.id, lesson.id)}
                              >
                                <div className="flex items-center space-x-2">
                                  {lesson.type === 'video' && (
                                    <Film className="h-4 w-4 text-blue-500" />
                                  )}
                                  {lesson.type === 'text' && (
                                    <FileText className="h-4 w-4 text-green-500" />
                                  )}
                                  <span className="text-sm font-medium">{lesson.title}</span>
                                </div>
                                {hasContent && (
                                  <CheckCircle className="h-4 w-4 text-green-500" />
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Main content area */}
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>
                {selectedLesson ? selectedLesson.title : 'No lesson selected'}
              </CardTitle>
              <CardDescription>
                {selectedLesson 
                  ? `Adding ${selectedLesson.type} content` 
                  : 'Select a lesson from the sidebar to start adding content'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!selectedLesson ? (
                <div className="text-center py-12 text-muted-foreground">
                  <FileText className="mx-auto h-12 w-12 mb-4 text-muted-foreground/70" />
                  <p>Please select a lesson from the sidebar to add content</p>
                </div>
              ) : selectedLesson.type === 'video' ? (
                /* Video content editor */
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="videoUrl">Video URL</Label>
                    <div className="space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => setIsMediaLibraryOpen(true)}
                      >
                        <Image className="h-4 w-4 mr-2" />
                        Media Library
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsStockVideoSelectorOpen(true)}
                      >
                        <Film className="h-4 w-4 mr-2" />
                        Stock Media
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                      >
                        <label htmlFor="videoUpload" className="cursor-pointer flex items-center">
                          <Upload className="h-4 w-4 mr-2" />
                          Upload
                        </label>
                        <input 
                          id="videoUpload"
                          type="file"
                          accept="video/*"
                          onChange={handleFileUpload}
                          className="hidden"
                        />
                      </Button>
                    </div>
                  </div>
                  
                  <Input
                    id="videoUrl"
                    placeholder="Enter video URL or embed code"
                    value={selectedLesson.videoUrl || ''}
                    onChange={(e) => handleVideoUrlChange(e.target.value)}
                  />
                  
                  {selectedLesson.videoUrl ? (
                    <AspectRatio ratio={16 / 9} className="overflow-hidden rounded-md border bg-muted">
                      <div className="flex items-center justify-center h-full">
                        <div className="relative w-full h-full">
                          <iframe
                            src={selectedLesson.videoUrl}
                            className="absolute inset-0 w-full h-full"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                          ></iframe>
                        </div>
                      </div>
                    </AspectRatio>
                  ) : (
                    <AspectRatio ratio={16 / 9} className="overflow-hidden rounded-md border bg-muted">
                      <div className="flex flex-col items-center justify-center h-full text-muted-foreground space-y-2">
                        <Film className="h-12 w-12" />
                        <p>No video selected</p>
                        <p className="text-xs">Upload a video or select one from the media library</p>
                      </div>
                    </AspectRatio>
                  )}

                  <div className="text-sm text-muted-foreground">
                    <p>Supported video formats: MP4, WebM</p>
                    <p>For best results, use 16:9 aspect ratio videos</p>
                  </div>
                </div>
              ) : selectedLesson.type === 'text' ? (
                /* Text content editor */
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="textContent">Lesson Content</Label>
                    <div className="space-x-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                      >
                        <label htmlFor="documentUpload" className="cursor-pointer flex items-center">
                          <Upload className="h-4 w-4 mr-2" />
                          Import Document
                        </label>
                        <input 
                          id="documentUpload"
                          type="file"
                          accept=".txt,.md,.doc,.docx"
                          className="hidden"
                        />
                      </Button>
                    </div>
                  </div>
                  
                  <Textarea
                    id="textContent"
                    placeholder="Enter your lesson content here..."
                    value={selectedLesson.content || ''}
                    onChange={(e) => handleTextContentChange(e.target.value)}
                    className="min-h-[300px]"
                  />
                  
                  <div className="text-sm text-muted-foreground">
                    <p>For best results, use clear formatting and structure content with headings and lists</p>
                  </div>
                </div>
              ) : selectedLesson.type === 'quiz' ? (
                /* Quiz content placeholder */
                <div className="text-center py-8 border-2 border-dashed rounded-md">
                  <AlertCircle className="mx-auto h-12 w-12 mb-4 text-yellow-500" />
                  <h3 className="text-lg font-medium mb-2">Quiz Content</h3>
                  <p className="text-muted-foreground mb-4">Quizzes will be created in the next step</p>
                  <Button 
                    variant="outline" 
                    onClick={() => onSubmit(currentStructure)}
                  >
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Skip to Quiz Builder
                  </Button>
                </div>
              ) : null}
            </CardContent>
          </Card>
        </div>
      </div>

      <Separator className="my-6" />

      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={onBack}
          className="gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Structure
        </Button>
        
        <Button
          onClick={handleSubmit}
          className="gap-2"
          disabled={!allLessonsHaveContent}
        >
          Continue to Quizzes
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Media Library Dialog */}
      <Dialog open={isMediaLibraryOpen} onOpenChange={setIsMediaLibraryOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Media Library</DialogTitle>
            <DialogDescription>
              Select a video from your media library to use in this lesson
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search videos..."
                  className="pl-8"
                  value={videoSearchTerm}
                  onChange={(e) => setVideoSearchTerm(e.target.value)}
                />
              </div>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Upload
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredVideos.map((video) => (
                <div 
                  key={video.id} 
                  className="border rounded-md overflow-hidden cursor-pointer hover:border-primary transition-colors"
                  onClick={() => handleMediaSelect(video.url)}
                >
                  <AspectRatio ratio={16 / 9}>
                    <img 
                      src={video.thumbnail} 
                      alt={video.title}
                      className="object-cover w-full h-full"
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 bg-black/30 transition-opacity">
                      <Play className="h-12 w-12 text-white" />
                    </div>
                  </AspectRatio>
                  <div className="p-2">
                    <h4 className="font-medium text-sm">{video.title}</h4>
                  </div>
                </div>
              ))}
              
              {filteredVideos.length === 0 && (
                <div className="col-span-2 text-center py-12 text-muted-foreground">
                  <p>No videos found matching your search</p>
                </div>
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsMediaLibraryOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Stock Video Selector */}
      <StockVideoSelector
        open={isStockVideoSelectorOpen}
        onOpenChange={setIsStockVideoSelectorOpen}
        onSelect={(videoUrl, source, videoId) => {
          if (selectedSectionId && selectedLessonId) {
            updateLessonContent(selectedSectionId, selectedLessonId, { videoUrl });
            toast({
              title: "Stock video added",
              description: `Video from ${source} has been added to your lesson.`,
            });
          }
        }}
      />
    </div>
  );
}