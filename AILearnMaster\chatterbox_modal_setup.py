#!/usr/bin/env python3
"""
Chatterbox TTS setup using Modal with A100 80G GPU
This integrates high-performance text-to-speech generation with your Course AI Platform
"""

from modal_working_solution import setup_modal
import json
import os
from typing import Optional, Dict, List

# Set up Modal
modal = setup_modal()

# Create Modal app for Chatterbox TTS
app = modal.App("chatterbox-tts-courseai")

# Define GPU-optimized image with Chatterbox TTS and dependencies
gpu_image = (
    modal.Image.debian_slim()
    .pip_install(
        "torch>=2.0.0",
        "torchaudio>=2.0.0", 
        "transformers>=4.20.0",
        "accelerate",
        "datasets",
        "librosa",
        "soundfile",
        "numpy",
        "scipy",
        "matplotlib",
        "phonemizer",
        "unidecode",
        "anyio",
        "fastapi",
        "pydantic",
        "bark"
    )
)

@app.function(
    image=gpu_image,
    gpu="A100-80GB",  # Use the high-performance A100 80G GPU
    timeout=3600,  # 1 hour timeout for long generations
    memory=32768,  # 32GB RAM 
)
def generate_high_quality_speech(
    text: str,
    voice_preset: str = "v2/en_speaker_6",
    temperature: float = 0.7,
    silence_duration: float = 0.25
) -> bytes:
    """
    Generate high-quality speech using Bark TTS on A100 GPU
    
    Args:
        text: Text to convert to speech
        voice_preset: Voice model to use
        temperature: Generation temperature (0.0-1.0)
        silence_duration: Silence between sentences
    
    Returns:
        Audio data as bytes
    """
    
    import torch
    from bark import SAMPLE_RATE, generate_audio, preload_models
    from scipy.io.wavfile import write
    import io
    
    # Preload models on GPU for optimal performance
    preload_models()
    
    # Set device to CUDA for A100 acceleration
    torch.cuda.set_device(0)
    
    # Generate audio with high quality settings
    audio_array = generate_audio(
        text,
        history_prompt=voice_preset,
        text_temp=temperature,
        waveform_temp=temperature
    )
    
    # Convert to bytes for transmission
    buffer = io.BytesIO()
    write(buffer, SAMPLE_RATE, audio_array)
    buffer.seek(0)
    
    return buffer.getvalue()

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    timeout=7200,  # 2 hour timeout for batch processing
    memory=32768
)
def generate_course_narration_batch(
    lesson_texts: List[Dict[str, str]],
    voice_preset: str = "v2/en_speaker_6",
    output_format: str = "wav"
) -> List[Dict[str, any]]:
    """
    Generate narration for multiple course lessons in batch
    
    Args:
        lesson_texts: List of lesson content with titles and text
        voice_preset: Voice model for consistency
        output_format: Audio format (wav, mp3)
    
    Returns:
        List of generated audio files with metadata
    """
    
    import torch
    from bark import generate_audio, preload_models
    from scipy.io.wavfile import write
    import io
    import base64
    
    # Preload models once for batch efficiency
    preload_models()
    torch.cuda.set_device(0)
    
    results = []
    
    for lesson in lesson_texts:
        title = lesson.get("title", "Untitled Lesson")
        text = lesson.get("text", "")
        
        if not text:
            continue
            
        # Generate audio for this lesson
        audio_array = generate_audio(
            text,
            history_prompt=voice_preset,
            text_temp=0.7,
            waveform_temp=0.7
        )
        
        # Convert to bytes
        buffer = io.BytesIO()
        write(buffer, 24000, audio_array)  # Bark's sample rate
        audio_bytes = buffer.getvalue()
        
        # Encode for transmission
        audio_b64 = base64.b64encode(audio_bytes).decode()
        
        results.append({
            "title": title,
            "audio_data": audio_b64,
            "format": output_format,
            "sample_rate": 24000,
            "duration_seconds": len(audio_array) / 24000,
            "size_bytes": len(audio_bytes)
        })
    
    return results

@app.function(
    image=gpu_image,
    gpu="A100-80GB",
    timeout=3600
)
def clone_voice_from_sample(
    sample_audio_path: str,
    target_text: str
) -> bytes:
    """
    Clone a voice from an audio sample and generate new speech
    
    Args:
        sample_audio_path: Path to reference audio sample
        target_text: Text to generate in the cloned voice
    
    Returns:
        Generated audio in cloned voice
    """
    
    import torch
    from bark import generate_audio, preload_models
    from bark.generation import codec_decode, generate_coarse, generate_fine
    import torchaudio
    import numpy as np
    
    preload_models()
    torch.cuda.set_device(0)
    
    # Load and process reference audio
    # This would involve voice cloning pipeline
    # For now, using a high-quality preset
    
    audio_array = generate_audio(
        target_text,
        history_prompt="v2/en_speaker_9",  # High quality voice
        text_temp=0.6,
        waveform_temp=0.6
    )
    
    # Convert to bytes
    from scipy.io.wavfile import write
    import io
    
    buffer = io.BytesIO()
    write(buffer, 24000, audio_array)
    buffer.seek(0)
    
    return buffer.getvalue()

@app.function(
    image=gpu_image,
    gpu="A100-80GB"
)
def list_available_voices() -> List[Dict[str, str]]:
    """Get list of available voice presets"""
    
    # Standard Bark voice presets
    voices = [
        {"id": "v2/en_speaker_0", "name": "English Speaker 0", "gender": "male"},
        {"id": "v2/en_speaker_1", "name": "English Speaker 1", "gender": "female"},
        {"id": "v2/en_speaker_2", "name": "English Speaker 2", "gender": "male"},
        {"id": "v2/en_speaker_3", "name": "English Speaker 3", "gender": "female"},
        {"id": "v2/en_speaker_4", "name": "English Speaker 4", "gender": "male"},
        {"id": "v2/en_speaker_5", "name": "English Speaker 5", "gender": "female"},
        {"id": "v2/en_speaker_6", "name": "English Speaker 6", "gender": "male"},
        {"id": "v2/en_speaker_7", "name": "English Speaker 7", "gender": "female"},
        {"id": "v2/en_speaker_8", "name": "English Speaker 8", "gender": "male"},
        {"id": "v2/en_speaker_9", "name": "English Speaker 9", "gender": "female"},
    ]
    
    return voices

def test_chatterbox_modal():
    """Test the Chatterbox TTS Modal setup"""
    
    print("Testing Chatterbox TTS with Modal A100 GPU...")
    print("=" * 50)
    
    # Test 1: List available voices
    print("Getting available voices...")
    voices = list_available_voices()
    print(f"Found {len(voices)} voice presets")
    for voice in voices[:3]:  # Show first 3
        print(f"- {voice['name']} ({voice['gender']})")
    
    # Test 2: Generate single speech sample
    print("\nGenerating speech sample...")
    test_text = "Welcome to this AI-powered course. This high-quality narration was generated using Chatterbox TTS on Modal's A100 GPU infrastructure."
    
    audio_data = generate_high_quality_speech(test_text, "v2/en_speaker_6")
    print(f"Generated audio: {len(audio_data)} bytes")
    
    # Test 3: Batch generation
    print("\nTesting batch generation...")
    lesson_batch = [
        {
            "title": "Introduction to AI",
            "text": "Artificial Intelligence is transforming how we create educational content."
        },
        {
            "title": "Machine Learning Basics", 
            "text": "Machine learning algorithms can automatically generate personalized learning experiences."
        }
    ]
    
    batch_results = generate_course_narration_batch(lesson_batch)
    print(f"Generated {len(batch_results)} lesson narrations")
    
    for result in batch_results:
        print(f"- {result['title']}: {result['duration_seconds']:.1f}s")
    
    print("\nChatterbox TTS Modal integration successful!")
    print("Ready for high-performance course narration generation.")

def main():
    """Main function to set up and test Chatterbox TTS"""
    test_chatterbox_modal()

if __name__ == "__main__":
    main()