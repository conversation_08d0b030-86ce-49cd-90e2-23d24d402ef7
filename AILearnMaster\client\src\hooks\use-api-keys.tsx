import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from './use-toast';

interface ApiKey {
  id: number;
  service: string;
  isVerified: boolean;
  isActive: boolean;
  lastVerified: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

interface ApiKeyVerifyRequest {
  service: string;
  apiKey: string;
}

export function useApiKeys() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch all user API keys
  const {
    data: apiKeys,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['/api/api-keys'],
    queryFn: async (): Promise<ApiKey[]> => {
      const response = await apiRequest('GET', '/api/api-keys');
      return await response.json();
    }
  });

  // Verify and save an API key
  const verifyApiKeyMutation = useMutation({
    mutationFn: async ({ service, apiKey }: ApiKeyVerifyRequest) => {
      const response = await apiRequest('POST', '/api/api-keys/verify', { service, apiKey });
      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/api-keys'] });
      toast({
        title: 'API key verified',
        description: `Your ${data.service} API key has been verified and saved successfully.`,
      });
    },
    onError: (error: Error) => {
      toast({
        variant: 'destructive',
        title: 'Verification failed',
        description: error.message || 'Failed to verify API key. Please check and try again.',
      });
    },
  });

  // Delete an API key
  const deleteApiKeyMutation = useMutation({
    mutationFn: async (keyId: number) => {
      const response = await apiRequest('DELETE', `/api/api-keys/${keyId}`);
      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/api-keys'] });
      toast({
        title: 'API key deleted',
        description: 'Your API key has been deleted successfully.',
      });
    },
    onError: (error: Error) => {
      toast({
        variant: 'destructive',
        title: 'Deletion failed',
        description: error.message || 'Failed to delete API key. Please try again.',
      });
    },
  });

  // Toggle API key active status
  const toggleApiKeyMutation = useMutation({
    mutationFn: async (keyId: number) => {
      const response = await apiRequest('PATCH', `/api/api-keys/${keyId}/toggle`);
      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/api-keys'] });
      toast({
        title: data.key.isActive ? 'API key activated' : 'API key deactivated',
        description: `Your API key has been ${data.key.isActive ? 'activated' : 'deactivated'} successfully.`,
      });
    },
    onError: (error: Error) => {
      toast({
        variant: 'destructive',
        title: 'Update failed',
        description: error.message || 'Failed to update API key status. Please try again.',
      });
    },
  });

  // Get API key by service
  const getApiKeyByService = (service: string): ApiKey | undefined => {
    return apiKeys?.find(key => key.service === service && key.isActive);
  };

  return {
    apiKeys,
    isLoading,
    error,
    refetch,
    verifyApiKey: verifyApiKeyMutation.mutate,
    deleteApiKey: deleteApiKeyMutation.mutate,
    toggleApiKey: toggleApiKeyMutation.mutate,
    getApiKeyByService,
    isVerifying: verifyApiKeyMutation.isPending,
    isDeleting: deleteApiKeyMutation.isPending,
    isToggling: toggleApiKeyMutation.isPending,
  };
}