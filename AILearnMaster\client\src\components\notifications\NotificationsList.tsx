import { Notification } from '../../types/notifications';
import NotificationItem from './NotificationItem';
import EmptyState from '../../components/EmptyState';
import { Bell } from 'lucide-react';

interface NotificationsListProps {
  notifications: Notification[];
  onMarkAsRead: (id: number) => void;
  onDelete: (id: number) => void;
  isLoading?: boolean;
  showEmptyState?: boolean;
  showViewAll?: boolean;
  maxHeight?: string;
  className?: string;
}

export default function NotificationsList({
  notifications,
  onMarkAsRead,
  onDelete,
  isLoading = false,
  showEmptyState = true,
  maxHeight = "350px",
  className = "",
}: NotificationsListProps) {
  // Loading state
  if (isLoading) {
    return (
      <div className={`flex justify-center items-center py-8 ${className}`}>
        <div className="h-6 w-6 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Empty state
  if ((!notifications || notifications.length === 0) && showEmptyState) {
    return (
      <EmptyState
        icon={<Bell className="h-10 w-10" />}
        title="No notifications"
        description="You don't have any notifications at the moment."
        className={className}
      />
    );
  }

  return (
    <div 
      className={`overflow-y-auto ${className}`}
      style={{ maxHeight }}
    >
      {notifications.map(notification => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          onMarkAsRead={onMarkAsRead}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
}
