import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Brain, MessageSquarePlus } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { ChatSession } from "@/types/chat";
import ChatInterface from "./ChatInterface";

interface CourseChatWidgetProps {
  courseId?: number;
  lessonId?: number;
  title?: string;
}

const CourseChatWidget = ({ courseId, lessonId, title = "Course Assistant" }: CourseChatWidgetProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSession, setActiveSession] = useState<ChatSession | null>(null);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Query to fetch chat sessions for this course/lesson
  const { data: sessions } = useQuery<ChatSession[]>({
    queryKey: ['/api/chatbot/sessions', { courseId, lessonId }],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/chatbot/sessions');
      const allSessions = await res.json();
      
      // Filter sessions for this course/lesson
      return allSessions.filter((session: ChatSession) => {
        if (lessonId) {
          return session.lessonId === lessonId;
        }
        if (courseId) {
          return session.courseId === courseId && !session.lessonId;
        }
        return false;
      });
    },
    enabled: isOpen // Only run query when dialog is open
  });

  // Mutation to create a new session
  const createSessionMutation = useMutation({
    mutationFn: async () => {
      // Create descriptive title based on context
      let sessionTitle = title;
      
      const res = await apiRequest('POST', '/api/chatbot/sessions', {
        title: sessionTitle,
        courseId,
        lessonId
      });
      return res.json();
    },
    onSuccess: (newSession) => {
      queryClient.invalidateQueries({ queryKey: ['/api/chatbot/sessions'] });
      setActiveSession(newSession);
      toast({
        title: 'Chat session created',
        description: 'You can now start chatting with the AI assistant',
      });
    },
    onError: () => {
      toast({
        title: 'Error',
        description: 'Could not create chat session',
        variant: 'destructive',
      });
    }
  });

  // When the dialog opens, find or create a session
  useEffect(() => {
    if (isOpen && sessions && sessions.length > 0) {
      // Use the most recent session
      setActiveSession(sessions[0]);
    } else if (isOpen && sessions && sessions.length === 0) {
      // Create a new session
      createSessionMutation.mutate();
    }
  }, [isOpen, sessions]);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="flex gap-2 items-center">
          <Brain className="h-4 w-4" />
          <span>Ask AI Assistant</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-3xl h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            <span>AI Learning Assistant</span>
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col flex-1 overflow-hidden h-full pt-2">
          {activeSession ? (
            <ChatInterface 
              sessionId={activeSession.id}
              courseId={courseId}
              lessonId={lessonId}
            />
          ) : (
            <div className="flex justify-center items-center h-full">
              <p>Loading conversation...</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CourseChatWidget;