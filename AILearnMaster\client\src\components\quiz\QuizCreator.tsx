import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus, 
  Trash2, 
  Edit3, 
  Save, 
  Play, 
  Brain, 
  CheckCircle2,
  XCircle,
  Eye,
  Shuffle,
  Clock,
  Target
} from 'lucide-react';
import { useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';

interface Question {
  id: string;
  type: 'multiple_choice' | 'true_false' | 'fill_blank' | 'short_answer';
  question: string;
  options?: string[];
  correctAnswer: string | number;
  explanation?: string;
  points: number;
}

interface Quiz {
  id?: string;
  title: string;
  description: string;
  moduleId?: string;
  lessonId?: string;
  questions: Question[];
  settings: {
    timeLimit?: number;
    passingScore: number;
    randomizeQuestions: boolean;
    showCorrectAnswers: boolean;
    allowRetakes: boolean;
  };
}

interface QuizCreatorProps {
  courseId?: string;
  moduleId?: string;
  lessonId?: string;
  onSave?: (quiz: Quiz) => void;
  onCancel?: () => void;
}

export default function QuizCreator({ 
  courseId, 
  moduleId, 
  lessonId, 
  onSave, 
  onCancel 
}: QuizCreatorProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('create');
  const [quiz, setQuiz] = useState<Quiz>({
    title: '',
    description: '',
    moduleId,
    lessonId,
    questions: [],
    settings: {
      passingScore: 70,
      randomizeQuestions: false,
      showCorrectAnswers: true,
      allowRetakes: true
    }
  });
  const [currentQuestion, setCurrentQuestion] = useState<Question>({
    id: '',
    type: 'multiple_choice',
    question: '',
    options: ['', '', '', ''],
    correctAnswer: 0,
    explanation: '',
    points: 1
  });
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [contentForAI, setContentForAI] = useState('');

  // AI Generation Mutation
  const generateQuizMutation = useMutation({
    mutationFn: async (params: { content: string; questionCount: number; difficulty: string }) => {
      const response = await fetch('/api/quiz-generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: params.content,
          questionCount: params.questionCount,
          difficulty: params.difficulty
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        let errorMessage = 'Failed to generate quiz';
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.message || errorMessage;
        } catch {
          errorMessage = errorText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      return result;
    },
    onSuccess: (data) => {
      if (data.questions && Array.isArray(data.questions)) {
        const generatedQuestions: Question[] = data.questions.map((q: any, index: number) => ({
          id: `q-${Date.now()}-${index}`,
          type: q.type === 'true_false' ? 'true_false' : 'multiple_choice',
          question: q.question,
          options: q.type === 'true_false' ? ['True', 'False'] : q.options,
          correctAnswer: q.correctAnswer || 0,
          explanation: q.explanation || '',
          points: q.points || 1
        }));

        setQuiz(prev => ({
          ...prev,
          questions: [...prev.questions, ...generatedQuestions]
        }));

        toast({
          title: "Questions Generated",
          description: `Added ${generatedQuestions.length} questions to your quiz`,
        });
        setActiveTab('questions');
      }
    },
    onError: (error: any) => {
      toast({
        title: "Generation Failed",
        description: error.message || "Failed to generate questions. Please try again.",
        variant: "destructive",
      });
    }
  });

  const addQuestion = () => {
    if (!currentQuestion.question.trim()) {
      toast({
        title: "Question Required",
        description: "Please enter a question before adding it to the quiz.",
        variant: "destructive",
      });
      return;
    }

    const questionToAdd: Question = {
      ...currentQuestion,
      id: editingIndex !== null ? quiz.questions[editingIndex].id : `q-${Date.now()}`,
    };

    if (editingIndex !== null) {
      // Update existing question
      const updatedQuestions = [...quiz.questions];
      updatedQuestions[editingIndex] = questionToAdd;
      setQuiz(prev => ({ ...prev, questions: updatedQuestions }));
      setEditingIndex(null);
      toast({
        title: "Question Updated",
        description: "The question has been successfully updated.",
      });
    } else {
      // Add new question
      setQuiz(prev => ({
        ...prev,
        questions: [...prev.questions, questionToAdd]
      }));
      toast({
        title: "Question Added",
        description: "The question has been added to your quiz.",
      });
    }

    // Reset form
    setCurrentQuestion({
      id: '',
      type: 'multiple_choice',
      question: '',
      options: ['', '', '', ''],
      correctAnswer: 0,
      explanation: '',
      points: 1
    });
  };

  const editQuestion = (index: number) => {
    setCurrentQuestion(quiz.questions[index]);
    setEditingIndex(index);
    setActiveTab('create');
  };

  const deleteQuestion = (index: number) => {
    setQuiz(prev => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index)
    }));
    toast({
      title: "Question Deleted",
      description: "The question has been removed from your quiz.",
    });
  };

  const handleQuestionTypeChange = (type: Question['type']) => {
    let newOptions = currentQuestion.options;
    let newCorrectAnswer = currentQuestion.correctAnswer;

    if (type === 'true_false') {
      newOptions = ['True', 'False'];
      newCorrectAnswer = 0;
    } else if (type === 'multiple_choice') {
      newOptions = ['', '', '', ''];
      newCorrectAnswer = 0;
    } else {
      newOptions = undefined;
      newCorrectAnswer = '';
    }

    setCurrentQuestion(prev => ({
      ...prev,
      type,
      options: newOptions,
      correctAnswer: newCorrectAnswer
    }));
  };

  const handleSave = () => {
    if (!quiz.title.trim()) {
      toast({
        title: "Title Required",
        description: "Please enter a title for your quiz.",
        variant: "destructive",
      });
      return;
    }

    if (quiz.questions.length === 0) {
      toast({
        title: "Questions Required",
        description: "Please add at least one question to your quiz.",
        variant: "destructive",
      });
      return;
    }

    onSave?.(quiz);
    toast({
      title: "Quiz Saved",
      description: "Your quiz has been saved successfully.",
    });
  };

  const generateWithAI = () => {
    if (!contentForAI.trim()) {
      toast({
        title: "Content Required",
        description: "Please provide some content for AI to generate questions from.",
        variant: "destructive",
      });
      return;
    }

    generateQuizMutation.mutate({
      content: contentForAI,
      questionCount: 5,
      difficulty: 'medium'
    });
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Quiz Creator</h1>
          <p className="text-muted-foreground">Create engaging quizzes with multiple question types</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={quiz.questions.length === 0}>
            <Save className="h-4 w-4 mr-2" />
            Save Quiz
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="info">Quiz Info</TabsTrigger>
          <TabsTrigger value="ai-generate">AI Generate</TabsTrigger>
          <TabsTrigger value="create">Create Question</TabsTrigger>
          <TabsTrigger value="questions">
            Questions ({quiz.questions.length})
          </TabsTrigger>
        </TabsList>

        {/* Quiz Information Tab */}
        <TabsContent value="info" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quiz Information</CardTitle>
              <CardDescription>Set up basic information and settings for your quiz</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Quiz Title</label>
                  <Input
                    placeholder="Enter quiz title"
                    value={quiz.title}
                    onChange={(e) => setQuiz(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Passing Score (%)</label>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={quiz.settings.passingScore}
                    onChange={(e) => setQuiz(prev => ({
                      ...prev,
                      settings: { ...prev.settings, passingScore: parseInt(e.target.value) || 70 }
                    }))}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  placeholder="Describe what this quiz covers"
                  value={quiz.description}
                  onChange={(e) => setQuiz(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="randomize"
                    checked={quiz.settings.randomizeQuestions}
                    onChange={(e) => setQuiz(prev => ({
                      ...prev,
                      settings: { ...prev.settings, randomizeQuestions: e.target.checked }
                    }))}
                  />
                  <label htmlFor="randomize" className="text-sm">Randomize Questions</label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="showAnswers"
                    checked={quiz.settings.showCorrectAnswers}
                    onChange={(e) => setQuiz(prev => ({
                      ...prev,
                      settings: { ...prev.settings, showCorrectAnswers: e.target.checked }
                    }))}
                  />
                  <label htmlFor="showAnswers" className="text-sm">Show Correct Answers</label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="retakes"
                    checked={quiz.settings.allowRetakes}
                    onChange={(e) => setQuiz(prev => ({
                      ...prev,
                      settings: { ...prev.settings, allowRetakes: e.target.checked }
                    }))}
                  />
                  <label htmlFor="retakes" className="text-sm">Allow Retakes</label>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Time Limit (minutes)</label>
                  <Input
                    type="number"
                    min="0"
                    placeholder="No limit"
                    value={quiz.settings.timeLimit || ''}
                    onChange={(e) => setQuiz(prev => ({
                      ...prev,
                      settings: { ...prev.settings, timeLimit: e.target.value ? parseInt(e.target.value) : undefined }
                    }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* AI Generation Tab */}
        <TabsContent value="ai-generate" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI Question Generator
              </CardTitle>
              <CardDescription>
                Provide content and let AI generate quiz questions automatically
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Content for Question Generation</label>
                <Textarea
                  placeholder="Paste lesson content, study material, or key concepts here..."
                  value={contentForAI}
                  onChange={(e) => setContentForAI(e.target.value)}
                  className="min-h-[200px]"
                />
              </div>
              <Button 
                onClick={generateWithAI} 
                disabled={generateQuizMutation.isPending || !contentForAI.trim()}
                className="w-full"
              >
                {generateQuizMutation.isPending ? (
                  <>
                    <Brain className="h-4 w-4 mr-2 animate-pulse" />
                    Generating Questions...
                  </>
                ) : (
                  <>
                    <Brain className="h-4 w-4 mr-2" />
                    Generate Questions with AI
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Create Question Tab */}
        <TabsContent value="create" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {editingIndex !== null ? 'Edit Question' : 'Create New Question'}
              </CardTitle>
              <CardDescription>
                {editingIndex !== null ? 'Modify the existing question' : 'Add a new question to your quiz'}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Question Type</label>
                  <Select 
                    value={currentQuestion.type} 
                    onValueChange={handleQuestionTypeChange}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                      <SelectItem value="true_false">True/False</SelectItem>
                      <SelectItem value="fill_blank">Fill in the Blank</SelectItem>
                      <SelectItem value="short_answer">Short Answer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Points</label>
                  <Input
                    type="number"
                    min="1"
                    value={currentQuestion.points}
                    onChange={(e) => setCurrentQuestion(prev => ({
                      ...prev,
                      points: parseInt(e.target.value) || 1
                    }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Question</label>
                <Textarea
                  placeholder="Enter your question here"
                  value={currentQuestion.question}
                  onChange={(e) => setCurrentQuestion(prev => ({
                    ...prev,
                    question: e.target.value
                  }))}
                />
              </div>

              {/* Question Type Specific Fields */}
              {(currentQuestion.type === 'multiple_choice' || currentQuestion.type === 'true_false') && (
                <div className="space-y-4">
                  <label className="text-sm font-medium">Answer Options</label>
                  {currentQuestion.options?.map((option, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="correctAnswer"
                        checked={currentQuestion.correctAnswer === index}
                        onChange={() => setCurrentQuestion(prev => ({
                          ...prev,
                          correctAnswer: index
                        }))}
                      />
                      <Input
                        placeholder={`Option ${index + 1}`}
                        value={option}
                        onChange={(e) => {
                          const newOptions = [...(currentQuestion.options || [])];
                          newOptions[index] = e.target.value;
                          setCurrentQuestion(prev => ({
                            ...prev,
                            options: newOptions
                          }));
                        }}
                        disabled={currentQuestion.type === 'true_false'}
                      />
                    </div>
                  ))}
                </div>
              )}

              {(currentQuestion.type === 'fill_blank' || currentQuestion.type === 'short_answer') && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Correct Answer</label>
                  <Input
                    placeholder="Enter the correct answer"
                    value={currentQuestion.correctAnswer}
                    onChange={(e) => setCurrentQuestion(prev => ({
                      ...prev,
                      correctAnswer: e.target.value
                    }))}
                  />
                </div>
              )}

              <div className="space-y-2">
                <label className="text-sm font-medium">Explanation (Optional)</label>
                <Textarea
                  placeholder="Explain why this answer is correct"
                  value={currentQuestion.explanation}
                  onChange={(e) => setCurrentQuestion(prev => ({
                    ...prev,
                    explanation: e.target.value
                  }))}
                />
              </div>

              <Button onClick={addQuestion} className="w-full">
                {editingIndex !== null ? (
                  <>
                    <Edit3 className="h-4 w-4 mr-2" />
                    Update Question
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Question
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Questions List Tab */}
        <TabsContent value="questions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Quiz Questions</CardTitle>
              <CardDescription>
                Review and manage all questions in your quiz
              </CardDescription>
            </CardHeader>
            <CardContent>
              {quiz.questions.length === 0 ? (
                <div className="text-center py-8">
                  <Target className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No questions yet</h3>
                  <p className="text-muted-foreground mb-4">
                    Start by creating questions or using AI generation
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button onClick={() => setActiveTab('create')}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Question
                    </Button>
                    <Button variant="outline" onClick={() => setActiveTab('ai-generate')}>
                      <Brain className="h-4 w-4 mr-2" />
                      Use AI
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {quiz.questions.map((question, index) => (
                    <Card key={question.id} className="border-l-4 border-l-blue-500">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline">
                                {question.type.replace('_', ' ').toUpperCase()}
                              </Badge>
                              <Badge variant="secondary">
                                {question.points} {question.points === 1 ? 'point' : 'points'}
                              </Badge>
                            </div>
                            <h4 className="font-medium mb-2">
                              {index + 1}. {question.question}
                            </h4>
                            {question.options && (
                              <div className="space-y-1">
                                {question.options.map((option, optIndex) => (
                                  <div key={optIndex} className="flex items-center gap-2">
                                    {question.correctAnswer === optIndex ? (
                                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                                    ) : (
                                      <XCircle className="h-4 w-4 text-gray-300" />
                                    )}
                                    <span className={question.correctAnswer === optIndex ? 'text-green-700 font-medium' : ''}>
                                      {option}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            )}
                            {question.explanation && (
                              <p className="text-sm text-muted-foreground mt-2">
                                <strong>Explanation:</strong> {question.explanation}
                              </p>
                            )}
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => editQuestion(index)}
                            >
                              <Edit3 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => deleteQuestion(index)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}