import React from 'react';
import { cn } from '@/lib/utils';
import { Check } from 'lucide-react';

export interface Step {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface HorizontalStepIndicatorProps {
  steps: Step[];
  activeStep: number;
  completedSteps: number[];
  onStepClick: (stepIndex: number) => void;
}

export function HorizontalStepIndicator({
  steps,
  activeStep,
  completedSteps = [],
  onStepClick,
}: HorizontalStepIndicatorProps) {
  // Safety check to prevent undefined iteration
  if (!steps || !Array.isArray(steps) || steps.length === 0) {
    return null;
  }

  // Ensure activeStep is within bounds
  const safeActiveStep = Math.max(0, Math.min(activeStep, steps.length - 1));

  return (
    <div className="w-full">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center flex-1 last:flex-none">
            {/* Step Circle */}
            <button
              type="button"
              onClick={() => onStepClick(index)}
              disabled={index > Math.max(...completedSteps, safeActiveStep) + 1}
              className={cn(
                'relative flex h-10 w-10 shrink-0 items-center justify-center rounded-full border transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50',
                index === safeActiveStep
                  ? 'border-primary bg-background text-primary'
                  : completedSteps.includes(index)
                  ? 'border-primary bg-primary text-primary-foreground'
                  : 'border-input bg-background text-muted-foreground hover:text-foreground'
              )}
            >
              {completedSteps.includes(index) ? (
                <Check className="h-4 w-4" />
              ) : step.icon ? (
                step.icon
              ) : (
                <span>{index + 1}</span>
              )}
              
              {/* Step Label (below the circle) */}
              <div className="absolute top-12 left-1/2 -translate-x-1/2 whitespace-nowrap">
                <p className={cn(
                  'text-xs font-medium',
                  index === safeActiveStep
                    ? 'text-foreground'
                    : 'text-muted-foreground'
                )}>
                  {step.title}
                </p>
              </div>
            </button>

            {/* Connector Line (between circles) */}
            {index < steps.length - 1 && (
              <div className={cn(
                'h-0.5 flex-1 mx-1',
                completedSteps.includes(index) ? 'bg-primary' : 'bg-border'
              )} />
            )}
          </div>
        ))}
      </div>

      {/* Step Description */}
      <div className="mt-10 text-center">
        <p className="text-sm text-muted-foreground">
          {steps[safeActiveStep]?.description || ''}
        </p>
      </div>
    </div>
  );
}