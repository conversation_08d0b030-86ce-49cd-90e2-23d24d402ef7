import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Loader2,
  Search,
  Filter,
  Image,
  Video,
  Upload,
  Grid3X3,
  Maximize,
  Square,
  ArrowUpDown,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

// Pixabay interfaces based on their API response
interface PixabayPhoto {
  id: number;
  pageURL: string;
  type: string;
  tags: string;
  previewURL: string;
  previewWidth: number;
  previewHeight: number;
  webformatURL: string;
  webformatWidth: number;
  webformatHeight: number;
  largeImageURL: string;
  imageWidth: number;
  imageHeight: number;
  imageSize: number;
  views: number;
  downloads: number;
  likes: number;
  comments: number;
  user_id: number;
  user: string;
  userImageURL: string;
}

interface PixabayVideo {
  id: number;
  pageURL: string;
  type: string;
  tags: string;
  duration: number;
  picture_id: string;
  videos: {
    large: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
    medium: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
    small: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
    tiny: {
      url: string;
      width: number;
      height: number;
      size: number;
    };
  };
  views: number;
  downloads: number;
  likes: number;
  comments: number;
  user_id: number;
  user: string;
  userImageURL: string;
}

interface PixabayPhotoResponse {
  total: number;
  totalHits: number;
  hits: PixabayPhoto[];
}

interface PixabayVideoResponse {
  total: number;
  totalHits: number;
  hits: PixabayVideo[];
}

interface PixabayMediaSearchProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (media: { id: number; type: string; url: string; name: string; size: number }) => void;
}

type Orientation = "all" | "horizontal" | "vertical";
type ImageType = "all" | "photo" | "illustration" | "vector";
type VideoType = "all" | "film" | "animation";
type Category = "" | "backgrounds" | "fashion" | "nature" | "science" | "education" | "feelings" | "health" | "people" | "religion" | "places" | "animals" | "industry" | "computer" | "food" | "sports" | "transportation" | "travel" | "buildings" | "business" | "music";

export function PixabayMediaSearch({ isOpen, onClose, onSelect }: PixabayMediaSearchProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("photos");
  const [searchPerformed, setSearchPerformed] = useState(false);
  const [orientation, setOrientation] = useState<Orientation>("all");
  const [imageType, setImageType] = useState<ImageType>("all");
  const [videoType, setVideoType] = useState<VideoType>("all");
  const [category, setCategory] = useState<Category>("");
  const { toast } = useToast();

  // Construct the query parameters
  const getQueryParams = () => {
    const params = new URLSearchParams();
    params.append("query", searchQuery);
    
    if (orientation !== "all") {
      params.append("orientation", orientation);
    }
    
    if (activeTab === "photos" && imageType !== "all") {
      params.append("imageType", imageType);
    }
    
    if (activeTab === "videos" && videoType !== "all") {
      params.append("videoType", videoType);
    }
    
    if (category) {
      params.append("category", category);
    }
    
    return params.toString();
  };

  const {
    data: photosData,
    isLoading: isLoadingPhotos,
    error: photosError,
  } = useQuery<PixabayPhotoResponse>({
    queryKey: ["/api/pixabay/photos", searchQuery, orientation, imageType, category],
    queryFn: () => 
      fetch(`/api/pixabay/photos?${getQueryParams()}`).then(res => res.json()),
    enabled: searchQuery !== "" && activeTab === "photos" && searchPerformed,
  });

  const {
    data: videosData,
    isLoading: isLoadingVideos,
    error: videosError,
  } = useQuery<PixabayVideoResponse>({
    queryKey: ["/api/pixabay/videos", searchQuery, videoType],
    queryFn: () => 
      fetch(`/api/pixabay/videos?${getQueryParams()}`).then(res => res.json()),
    enabled: searchQuery !== "" && activeTab === "videos" && searchPerformed,
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      setSearchPerformed(true);
    }
  };

  const handleImportMedia = async (item: PixabayPhoto | PixabayVideo, type: "photo" | "video") => {
    try {
      // Determine file size (estimate since we don't have the actual size)
      const estimatedSize = type === "photo" 
        ? 500 * 1024 // Assume 500KB for photos
        : 5 * 1024 * 1024; // Assume 5MB for videos
      
      const mediaType = type === "photo" ? "image" : "video";
      const name = type === "photo" 
        ? ((item as PixabayPhoto).tags.split(',')[0] || `Pixabay Photo ${item.id}`)
        : `Pixabay Video ${item.id}`;
      
      // Add media to the library via API
      const response = await apiRequest("POST", "/api/pixabay/import", {
        id: item.id,
        type,
        title: name,
        description: type === "photo" 
          ? `Photo by ${(item as PixabayPhoto).user}` 
          : `Video by ${(item as PixabayVideo).user}`
      });
      
      if (!response.ok) {
        throw new Error("Failed to import media from Pixabay");
      }
      
      const importedMedia = await response.json();
      
      // Update media library cache
      queryClient.invalidateQueries({ queryKey: ["/api/media"] });
      
      toast({
        title: "Media imported successfully",
        description: `The ${type} has been added to your media library.`
      });
      
      // Close the dialog and trigger the onSelect callback
      onClose();
      onSelect({
        id: importedMedia.id,
        type: mediaType,
        url: importedMedia.url,
        name: importedMedia.title,
        size: importedMedia.fileSize
      });
    } catch (error: any) {
      console.error("Import error:", error);
      toast({
        title: "Import failed",
        description: error.message || "There was an error importing the media.",
        variant: "destructive"
      });
    }
  };

  // Helper function to determine orientation based on dimensions
  const getPhotoOrientation = (width: number, height: number): Orientation => {
    return width > height ? "horizontal" : "vertical";
  };

  return (
    <Dialog open={isOpen} onOpenChange={() => onClose()}>
      <DialogContent className="sm:max-w-[1000px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Search Pixabay Media</DialogTitle>
          <DialogDescription>
            Find and import free photos and videos from Pixabay
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSearch} className="flex gap-2 my-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search for photos or videos..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button type="submit" disabled={!searchQuery.trim()}>
            <Search className="w-4 h-4 mr-2" />
            Search
          </Button>
        </form>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
            <TabsList className="grid w-full sm:w-auto grid-cols-2">
              <TabsTrigger value="photos" className="flex items-center gap-1">
                <Image className="w-4 h-4" />
                Photos
              </TabsTrigger>
              <TabsTrigger value="videos" className="flex items-center gap-1">
                <Video className="w-4 h-4" />
                Videos
              </TabsTrigger>
            </TabsList>

            <div className="flex flex-wrap gap-2 items-center">
              <div className="flex items-center">
                <Filter className="w-4 h-4 mr-2 text-muted-foreground" />
                <span className="text-sm font-medium mr-2">Filters:</span>
              </div>

              {activeTab === "photos" && (
                <>
                  <Select value={orientation} onValueChange={(value) => setOrientation(value as Orientation)}>
                    <SelectTrigger className="w-[130px] h-9">
                      <SelectValue placeholder="Orientation" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all" className="flex items-center gap-2">
                        <Grid3X3 className="w-4 h-4" /> All
                      </SelectItem>
                      <SelectItem value="horizontal" className="flex items-center gap-2">
                        <Maximize className="w-4 h-4" /> Horizontal
                      </SelectItem>
                      <SelectItem value="vertical" className="flex items-center gap-2">
                        <ArrowUpDown className="w-4 h-4" /> Vertical
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={imageType} onValueChange={(value) => setImageType(value as ImageType)}>
                    <SelectTrigger className="w-[130px] h-9">
                      <SelectValue placeholder="Image Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="photo">Photo</SelectItem>
                      <SelectItem value="illustration">Illustration</SelectItem>
                      <SelectItem value="vector">Vector</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={category} onValueChange={(value) => setCategory(value as Category)}>
                    <SelectTrigger className="w-[130px] h-9">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Categories</SelectItem>
                      <SelectItem value="animals">Animals</SelectItem>
                      <SelectItem value="backgrounds">Backgrounds</SelectItem>
                      <SelectItem value="buildings">Buildings</SelectItem>
                      <SelectItem value="business">Business</SelectItem>
                      <SelectItem value="computer">Computer</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                      <SelectItem value="fashion">Fashion</SelectItem>
                      <SelectItem value="feelings">Feelings</SelectItem>
                      <SelectItem value="food">Food</SelectItem>
                      <SelectItem value="health">Health</SelectItem>
                      <SelectItem value="industry">Industry</SelectItem>
                      <SelectItem value="music">Music</SelectItem>
                      <SelectItem value="nature">Nature</SelectItem>
                      <SelectItem value="people">People</SelectItem>
                      <SelectItem value="places">Places</SelectItem>
                      <SelectItem value="religion">Religion</SelectItem>
                      <SelectItem value="science">Science</SelectItem>
                      <SelectItem value="sports">Sports</SelectItem>
                      <SelectItem value="transportation">Transportation</SelectItem>
                      <SelectItem value="travel">Travel</SelectItem>
                    </SelectContent>
                  </Select>
                </>
              )}

              {activeTab === "videos" && (
                <Select value={videoType} onValueChange={(value) => setVideoType(value as VideoType)}>
                  <SelectTrigger className="w-[130px] h-9">
                    <SelectValue placeholder="Video Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="film">Film</SelectItem>
                    <SelectItem value="animation">Animation</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>

          {/* Active filters display */}
          {searchPerformed && (orientation !== "all" || imageType !== "all" || videoType !== "all" || category !== "") && (
            <div className="flex flex-wrap gap-2 mb-4">
              <span className="text-sm text-muted-foreground">Active filters:</span>
              {orientation !== "all" && (
                <Badge variant="outline" className="flex items-center gap-1">
                  {orientation.charAt(0).toUpperCase() + orientation.slice(1)}
                  <button
                    onClick={() => setOrientation("all")}
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {imageType !== "all" && activeTab === "photos" && (
                <Badge variant="outline" className="flex items-center gap-1">
                  Type: {imageType.charAt(0).toUpperCase() + imageType.slice(1)}
                  <button
                    onClick={() => setImageType("all")}
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {videoType !== "all" && activeTab === "videos" && (
                <Badge variant="outline" className="flex items-center gap-1">
                  Type: {videoType.charAt(0).toUpperCase() + videoType.slice(1)}
                  <button
                    onClick={() => setVideoType("all")}
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {category !== "" && (
                <Badge variant="outline" className="flex items-center gap-1">
                  Category: {category.charAt(0).toUpperCase() + category.slice(1)}
                  <button
                    onClick={() => setCategory("")}
                    className="ml-1 hover:bg-muted rounded-full p-0.5"
                  >
                    ×
                  </button>
                </Badge>
              )}
            </div>
          )}

          <TabsContent value="photos" className="space-y-4">
            {isLoadingPhotos ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : photosError ? (
              <div className="text-center text-red-500 py-8">
                Error loading photos: {(photosError as Error).message}
              </div>
            ) : photosData && Array.isArray(photosData.hits) && photosData.hits.length > 0 ? (
              <>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    Found {photosData?.totalHits ?? 0} photos
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {photosData?.hits?.map((photo: PixabayPhoto) => (
                    <Card key={photo.id} className="overflow-hidden group">
                      <div className="aspect-video relative overflow-hidden">
                        <img
                          src={photo.webformatURL}
                          alt={photo.tags || `Photo by ${photo.user}`}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <Button 
                            size="sm" 
                            onClick={() => handleImportMedia(photo, "photo")}
                            className="bg-white/90 text-black hover:bg-white"
                          >
                            <Upload className="h-4 w-4 mr-1" />
                            Import
                          </Button>
                        </div>
                      </div>
                      <CardContent className="p-3">
                        <div className="flex flex-col gap-1">
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-muted-foreground truncate flex-1">
                              By {photo.user}
                            </div>
                            <Button 
                              size="sm" 
                              variant="ghost"
                              onClick={() => handleImportMedia(photo, "photo")}
                            >
                              Import
                            </Button>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{photo.imageWidth}×{photo.imageHeight}</span>
                            <Badge variant="outline" className="text-xs">
                              {getPhotoOrientation(photo.imageWidth, photo.imageHeight)}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                {photosData && typeof photosData.totalHits === 'number' && Array.isArray(photosData.hits) && photosData.totalHits > photosData.hits.length && (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground mb-2">
                      Showing {photosData.hits.length} of {photosData.totalHits} results
                    </p>
                  </div>
                )}
              </>
            ) : searchPerformed ? (
              <div className="text-center py-8">
                No photos found. Try a different search term or adjust your filters.
              </div>
            ) : null}
          </TabsContent>

          <TabsContent value="videos" className="space-y-4">
            {isLoadingVideos ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : videosError ? (
              <div className="text-center text-red-500 py-8">
                Error loading videos: {(videosError as Error).message}
              </div>
            ) : videosData && Array.isArray(videosData.hits) && videosData.hits.length > 0 ? (
              <>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    Found {videosData?.totalHits ?? 0} videos
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {videosData?.hits?.map((video: PixabayVideo) => (
                    <Card key={video.id} className="overflow-hidden group">
                      <div className="aspect-video relative overflow-hidden">
                        <img
                          src={`https://i.vimeocdn.com/video/${video.picture_id}_640x360.jpg`}
                          alt={`Video by ${video.user}`}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                          <div className="text-white font-medium bg-black/50 px-2 py-1 rounded">
                            {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}
                          </div>
                        </div>
                        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <Button 
                            size="sm" 
                            onClick={() => handleImportMedia(video, "video")}
                            className="bg-white/90 text-black hover:bg-white"
                          >
                            <Upload className="h-4 w-4 mr-1" />
                            Import
                          </Button>
                        </div>
                      </div>
                      <CardContent className="p-3">
                        <div className="flex flex-col gap-1">
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-muted-foreground truncate flex-1">
                              By {video.user}
                            </div>
                            <Button 
                              size="sm" 
                              variant="ghost"
                              onClick={() => handleImportMedia(video, "video")}
                            >
                              Import
                            </Button>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{video.videos.large.width}×{video.videos.large.height}</span>
                            <Badge variant="outline" className="text-xs">
                              {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                {videosData && typeof videosData.totalHits === 'number' && Array.isArray(videosData.hits) && videosData.totalHits > videosData.hits.length && (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground mb-2">
                      Showing {videosData.hits.length} of {videosData.totalHits} results
                    </p>
                  </div>
                )}
              </>
            ) : searchPerformed ? (
              <div className="text-center py-8">
                No videos found. Try a different search term or adjust your filters.
              </div>
            ) : null}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}