import { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { 
  PlusCircle, 
  ListChecks, 
  Calendar, 
  CheckCircle2, 
  Clock, 
  UserPlus, 
  CalendarCheck,
  Tag,
  Edit,
  Trash2
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { format } from 'date-fns';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

// Types
type User = {
  id: number;
  name: string;
  username: string;
  avatarUrl?: string;
};

type TaskPriority = 'low' | 'medium' | 'high';

type TaskStatus = 'todo' | 'in-progress' | 'review' | 'completed';

type Task = {
  id: string;
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  assignee?: User;
  dueDate?: Date;
  createdAt: Date;
  tags: string[];
  checklist?: {
    id: string;
    text: string;
    completed: boolean;
  }[];
};

interface TasksBoardProps {
  courseId?: number;
  teamId?: number;
  initialTasks?: Task[];
}

// Sample data
const sampleUsers: User[] = [
  { id: 1, name: 'Current User', username: 'currentuser' },
  { id: 2, name: 'Alex Johnson', username: 'alexj' },
  { id: 3, name: 'Maria Garcia', username: 'mariag' },
  { id: 4, name: 'James Wilson', username: 'jamesw' },
];

const sampleTasks: Task[] = [
  {
    id: '1',
    title: 'Create course outline',
    description: 'Develop a detailed outline for all course modules',
    status: 'completed',
    priority: 'high',
    assignee: sampleUsers[1],
    dueDate: new Date(2025, 3, 20),
    createdAt: new Date(2025, 3, 10),
    tags: ['planning', 'content'],
    checklist: [
      { id: '1-1', text: 'Research similar courses', completed: true },
      { id: '1-2', text: 'Define learning objectives', completed: true },
      { id: '1-3', text: 'Organize content sequence', completed: true },
    ]
  },
  {
    id: '2',
    title: 'Record introduction video',
    description: 'Create a 2-minute video introducing the course and instructors',
    status: 'in-progress',
    priority: 'medium',
    assignee: sampleUsers[0],
    dueDate: new Date(2025, 4, 5),
    createdAt: new Date(2025, 3, 25),
    tags: ['video', 'intro'],
    checklist: [
      { id: '2-1', text: 'Write script', completed: true },
      { id: '2-2', text: 'Setup recording environment', completed: true },
      { id: '2-3', text: 'Record video', completed: false },
      { id: '2-4', text: 'Edit and upload', completed: false },
    ]
  },
  {
    id: '3',
    title: 'Create interactive quizzes',
    description: 'Develop assessment quizzes for each module',
    status: 'todo',
    priority: 'medium',
    assignee: sampleUsers[2],
    dueDate: new Date(2025, 4, 15),
    createdAt: new Date(2025, 3, 26),
    tags: ['assessment', 'interactive'],
  },
  {
    id: '4',
    title: 'Design course visuals',
    description: 'Create slides, graphics, and other visual elements',
    status: 'review',
    priority: 'high',
    assignee: sampleUsers[3],
    dueDate: new Date(2025, 4, 10),
    createdAt: new Date(2025, 3, 22),
    tags: ['design', 'visuals'],
    checklist: [
      { id: '4-1', text: 'Create slide template', completed: true },
      { id: '4-2', text: 'Design diagrams', completed: true },
      { id: '4-3', text: 'Prepare illustrations', completed: true },
      { id: '4-4', text: 'Final quality check', completed: false },
    ]
  },
  {
    id: '5',
    title: 'Review module 1 content',
    description: 'Proofread and review the content for module 1',
    status: 'todo',
    priority: 'low',
    dueDate: new Date(2025, 4, 8),
    createdAt: new Date(2025, 3, 27),
    tags: ['review', 'content'],
  },
];

// Helper functions for UI elements
function getPriorityBadge(priority: TaskPriority) {
  switch (priority) {
    case 'high':
      return <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">High</Badge>;
    case 'medium':
      return <Badge variant="outline" className="bg-amber-100 text-amber-800 hover:bg-amber-100">Medium</Badge>;
    case 'low':
      return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">Low</Badge>;
    default:
      return null;
  }
}

function getStatusColor(status: TaskStatus) {
  switch (status) {
    case 'todo':
      return 'text-slate-800';
    case 'in-progress':
      return 'text-blue-600';
    case 'review':
      return 'text-amber-600';
    case 'completed':
      return 'text-emerald-600';
    default:
      return 'text-slate-800';
  }
}

export default function TasksBoard({ 
  courseId, 
  teamId, 
  initialTasks = sampleTasks 
}: TasksBoardProps) {
  const { toast } = useToast();
  const [tasks, setTasks] = useState<Task[]>(initialTasks);
  const [showAddTaskDialog, setShowAddTaskDialog] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [taskView, setTaskView] = useState<'board' | 'list'>('board');
  const [filteredStatus, setFilteredStatus] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [newTask, setNewTask] = useState<Partial<Task>>({
    title: '',
    description: '',
    status: 'todo',
    priority: 'medium',
    tags: [],
    checklist: [],
    createdAt: new Date(),
  });
  
  // Filter tasks based on search query and status filter
  const filteredTasks = tasks.filter(task => {
    const matchesSearch = 
      searchQuery === '' || 
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesStatus = !filteredStatus || task.status === filteredStatus;
    
    return matchesSearch && matchesStatus;
  });
  
  // Group tasks by status for the board view
  const groupedTasks = {
    'todo': filteredTasks.filter(task => task.status === 'todo'),
    'in-progress': filteredTasks.filter(task => task.status === 'in-progress'),
    'review': filteredTasks.filter(task => task.status === 'review'),
    'completed': filteredTasks.filter(task => task.status === 'completed'),
  };
  
  // Handle task status change via drag and drop
  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    
    const { source, destination, draggableId } = result;
    
    // If dropped in the same column at the same position, do nothing
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return;
    }
    
    // Find the task
    const taskIndex = tasks.findIndex(task => task.id === draggableId);
    if (taskIndex === -1) return;
    
    // Create a new array of tasks
    const newTasks = [...tasks];
    
    // Update the task status
    newTasks[taskIndex] = {
      ...newTasks[taskIndex],
      status: destination.droppableId as TaskStatus
    };
    
    // Update state
    setTasks(newTasks);
    
    toast({
      title: 'Task Updated',
      description: `"${newTasks[taskIndex].title}" moved to ${destination.droppableId.replace('-', ' ')}`,
    });
  };
  
  // Handle adding a new task
  const handleAddTask = () => {
    if (!newTask.title) {
      toast({
        title: 'Error',
        description: 'Task title is required',
        variant: 'destructive',
      });
      return;
    }
    
    const task: Task = {
      id: Date.now().toString(),
      title: newTask.title,
      description: newTask.description || '',
      status: newTask.status as TaskStatus || 'todo',
      priority: newTask.priority as TaskPriority || 'medium',
      assignee: newTask.assignee,
      dueDate: newTask.dueDate,
      createdAt: new Date(),
      tags: newTask.tags || [],
      checklist: newTask.checklist || [],
    };
    
    setTasks([task, ...tasks]);
    setShowAddTaskDialog(false);
    setNewTask({
      title: '',
      description: '',
      status: 'todo',
      priority: 'medium',
      tags: [],
      checklist: [],
      createdAt: new Date(),
    });
    
    toast({
      title: 'Task Added',
      description: `"${task.title}" has been added to the board`,
    });
  };
  
  // Handle editing an existing task
  const handleEditTask = () => {
    if (!editingTask) return;
    
    const updatedTasks = tasks.map(task => 
      task.id === editingTask.id ? editingTask : task
    );
    
    setTasks(updatedTasks);
    setEditingTask(null);
    
    toast({
      title: 'Task Updated',
      description: `"${editingTask.title}" has been updated`,
    });
  };
  
  // Handle deleting a task
  const handleDeleteTask = (taskId: string) => {
    setTasks(tasks.filter(task => task.id !== taskId));
    
    toast({
      title: 'Task Deleted',
      description: 'The task has been deleted from the board',
    });
  };
  
  // Handle completing a task
  const handleCompleteTask = (taskId: string) => {
    const updatedTasks = tasks.map(task => 
      task.id === taskId ? { ...task, status: 'completed' as TaskStatus } : task
    );
    
    setTasks(updatedTasks);
    
    toast({
      title: 'Task Completed',
      description: 'The task has been marked as completed',
    });
  };
  
  // Render a task card
  const renderTaskCard = (task: Task) => (
    <Card className={`mb-3 ${task.status === 'completed' ? 'opacity-70' : ''}`}>
      <CardContent className="p-3">
        <div className="flex justify-between items-start mb-2">
          <div className="space-y-1">
            <h3 className={`font-medium line-clamp-2 ${task.status === 'completed' ? 'line-through text-slate-500' : ''}`}>
              {task.title}
            </h3>
            {task.description && (
              <p className="text-sm text-slate-500 line-clamp-2">
                {task.description}
              </p>
            )}
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="10" cy="6" r="1" />
                  <circle cx="10" cy="10" r="1" />
                  <circle cx="10" cy="14" r="1" />
                </svg>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setEditingTask(task)}>
                <Edit className="mr-2 h-4 w-4" /> Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleCompleteTask(task.id)}>
                <CheckCircle2 className="mr-2 h-4 w-4" /> Mark as Completed
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleDeleteTask(task.id)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        <div className="flex flex-wrap gap-1 mt-2 mb-3">
          {task.tags.map((tag, index) => (
            <Badge key={index} variant="outline" className="bg-slate-100 text-slate-800">
              {tag}
            </Badge>
          ))}
        </div>
        
        <div className="flex justify-between items-center text-sm">
          <div className="flex items-center">
            {task.assignee ? (
              <div className="flex items-center">
                <Avatar className="h-6 w-6 mr-1">
                  {task.assignee.avatarUrl ? (
                    <AvatarImage src={task.assignee.avatarUrl} alt={task.assignee.name} />
                  ) : (
                    <AvatarFallback>{task.assignee.name.charAt(0)}</AvatarFallback>
                  )}
                </Avatar>
                <span className="text-xs text-slate-500">{task.assignee.name}</span>
              </div>
            ) : (
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-6 px-1.5 text-xs"
                onClick={() => setEditingTask(task)}
              >
                <UserPlus className="h-3 w-3 mr-1" /> Assign
              </Button>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {getPriorityBadge(task.priority)}
            
            {task.dueDate && (
              <div className="flex items-center text-xs text-slate-500">
                <Clock className="h-3 w-3 mr-1" />
                <span>{format(new Date(task.dueDate), 'MMM d')}</span>
              </div>
            )}
          </div>
        </div>
        
        {task.checklist && task.checklist.length > 0 && (
          <div className="mt-3 pt-3 border-t border-slate-100">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs text-slate-500">
                Checklist ({task.checklist.filter(item => item.completed).length}/{task.checklist.length})
              </span>
              <div className="h-1.5 w-16 bg-slate-100 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-emerald-500" 
                  style={{ 
                    width: `${(task.checklist.filter(item => item.completed).length / task.checklist.length) * 100}%` 
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Task Board</h2>
        <Button onClick={() => setShowAddTaskDialog(true)}>
          <PlusCircle className="h-4 w-4 mr-2" /> Add Task
        </Button>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-3 mb-4">
        <div className="flex-grow relative">
          <Input
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search tasks..."
            className="pl-9"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Select value={filteredStatus || ''} onValueChange={(value) => setFilteredStatus(value || null)}>
            <SelectTrigger className="w-36">
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Statuses</SelectItem>
              <SelectItem value="todo">To Do</SelectItem>
              <SelectItem value="in-progress">In Progress</SelectItem>
              <SelectItem value="review">In Review</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
          
          <Tabs value={taskView} onValueChange={(value) => setTaskView(value as 'board' | 'list')}>
            <TabsList className="h-9">
              <TabsTrigger value="board" className="h-8 px-3">
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                  </svg>
                  Board
                </div>
              </TabsTrigger>
              <TabsTrigger value="list" className="h-8 px-3">
                <div className="flex items-center">
                  <ListChecks className="h-4 w-4 mr-1" />
                  List
                </div>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
      
      {/* Board View */}
      <TabsContent value="board" className={taskView === 'board' ? 'block' : 'hidden'}>
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* To Do Column */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-3 w-3 rounded-full bg-slate-400"></div>
                <h3 className="font-medium">To Do</h3>
                <span className="bg-slate-100 text-slate-600 rounded-full px-2 py-0.5 text-xs">
                  {groupedTasks['todo'].length}
                </span>
              </div>
              
              <Droppable droppableId="todo">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="bg-slate-50 p-2 rounded-lg min-h-[200px]"
                  >
                    {groupedTasks['todo'].map((task, index) => (
                      <Draggable key={task.id} draggableId={task.id} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                          >
                            {renderTaskCard(task)}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
            
            {/* In Progress Column */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                <h3 className="font-medium">In Progress</h3>
                <span className="bg-blue-100 text-blue-600 rounded-full px-2 py-0.5 text-xs">
                  {groupedTasks['in-progress'].length}
                </span>
              </div>
              
              <Droppable droppableId="in-progress">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="bg-blue-50 p-2 rounded-lg min-h-[200px]"
                  >
                    {groupedTasks['in-progress'].map((task, index) => (
                      <Draggable key={task.id} draggableId={task.id} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                          >
                            {renderTaskCard(task)}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
            
            {/* Review Column */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-3 w-3 rounded-full bg-amber-500"></div>
                <h3 className="font-medium">In Review</h3>
                <span className="bg-amber-100 text-amber-600 rounded-full px-2 py-0.5 text-xs">
                  {groupedTasks['review'].length}
                </span>
              </div>
              
              <Droppable droppableId="review">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="bg-amber-50 p-2 rounded-lg min-h-[200px]"
                  >
                    {groupedTasks['review'].map((task, index) => (
                      <Draggable key={task.id} draggableId={task.id} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                          >
                            {renderTaskCard(task)}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
            
            {/* Completed Column */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 mb-2">
                <div className="h-3 w-3 rounded-full bg-emerald-500"></div>
                <h3 className="font-medium">Completed</h3>
                <span className="bg-emerald-100 text-emerald-600 rounded-full px-2 py-0.5 text-xs">
                  {groupedTasks['completed'].length}
                </span>
              </div>
              
              <Droppable droppableId="completed">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="bg-emerald-50 p-2 rounded-lg min-h-[200px]"
                  >
                    {groupedTasks['completed'].map((task, index) => (
                      <Draggable key={task.id} draggableId={task.id} index={index}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                          >
                            {renderTaskCard(task)}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </div>
          </div>
        </DragDropContext>
      </TabsContent>
      
      {/* List View */}
      <TabsContent value="list" className={taskView === 'list' ? 'block' : 'hidden'}>
        {filteredTasks.length === 0 ? (
          <div className="text-center py-8 bg-slate-50 rounded-lg border-2 border-dashed border-slate-200">
            <ListChecks className="h-12 w-12 mx-auto text-slate-300 mb-3" />
            <h3 className="text-lg font-medium text-slate-900 mb-1">
              No tasks found
            </h3>
            <p className="text-slate-500 mb-4">
              There are no tasks matching your search criteria.
            </p>
            <Button 
              variant="outline" 
              onClick={() => {
                setSearchQuery('');
                setFilteredStatus(null);
              }}
            >
              Clear Filters
            </Button>
          </div>
        ) : (
          <Card>
            <CardContent className="p-0">
              <table className="w-full">
                <thead className="border-b">
                  <tr>
                    <th className="text-left p-3 text-sm font-medium text-slate-500">Task</th>
                    <th className="text-left p-3 text-sm font-medium text-slate-500">Status</th>
                    <th className="text-left p-3 text-sm font-medium text-slate-500">Assignee</th>
                    <th className="text-left p-3 text-sm font-medium text-slate-500">Due Date</th>
                    <th className="text-left p-3 text-sm font-medium text-slate-500">Priority</th>
                    <th className="text-left p-3 text-sm font-medium text-slate-500">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTasks.map((task) => (
                    <tr key={task.id} className="border-b hover:bg-slate-50">
                      <td className="p-3">
                        <div className="flex flex-col">
                          <span 
                            className={`font-medium ${task.status === 'completed' ? 'line-through text-slate-500' : ''}`}
                          >
                            {task.title}
                          </span>
                          {task.description && (
                            <span className="text-sm text-slate-500 truncate max-w-xs">
                              {task.description}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="p-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)} bg-opacity-10`}>
                          {task.status === 'todo' ? 'To Do' : 
                           task.status === 'in-progress' ? 'In Progress' :
                           task.status === 'review' ? 'In Review' : 'Completed'}
                        </span>
                      </td>
                      <td className="p-3">
                        {task.assignee ? (
                          <div className="flex items-center">
                            <Avatar className="h-6 w-6 mr-2">
                              {task.assignee.avatarUrl ? (
                                <AvatarImage src={task.assignee.avatarUrl} alt={task.assignee.name} />
                              ) : (
                                <AvatarFallback>{task.assignee.name.charAt(0)}</AvatarFallback>
                              )}
                            </Avatar>
                            <span className="text-sm">{task.assignee.name}</span>
                          </div>
                        ) : (
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-7 px-2"
                            onClick={() => setEditingTask(task)}
                          >
                            <UserPlus className="h-3.5 w-3.5 mr-1" /> Assign
                          </Button>
                        )}
                      </td>
                      <td className="p-3">
                        {task.dueDate ? (
                          <div className="flex items-center text-sm">
                            <CalendarCheck className="h-3.5 w-3.5 mr-1.5 text-slate-400" />
                            {format(new Date(task.dueDate), 'MMM d, yyyy')}
                          </div>
                        ) : (
                          <span className="text-slate-400 text-sm">No date</span>
                        )}
                      </td>
                      <td className="p-3">
                        {getPriorityBadge(task.priority)}
                      </td>
                      <td className="p-3">
                        <div className="flex gap-1">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0" 
                            onClick={() => setEditingTask(task)}
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0" 
                            onClick={() => handleCompleteTask(task.id)}
                          >
                            <CheckCircle2 className="h-4 w-4" />
                            <span className="sr-only">Complete</span>
                          </Button>
                          
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-600" 
                            onClick={() => handleDeleteTask(task.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </CardContent>
          </Card>
        )}
      </TabsContent>
      
      {/* Add Task Dialog */}
      <Dialog open={showAddTaskDialog} onOpenChange={setShowAddTaskDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Task</DialogTitle>
            <DialogDescription>
              Create a new task for the collaboration board.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Title
              </Label>
              <Input
                id="title"
                value={newTask.title}
                onChange={(e) => setNewTask({...newTask, title: e.target.value})}
                className="col-span-3"
                placeholder="Task title"
              />
            </div>
            
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <Textarea
                id="description"
                value={newTask.description}
                onChange={(e) => setNewTask({...newTask, description: e.target.value})}
                className="col-span-3"
                rows={3}
                placeholder="Describe the task"
              />
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="status" className="text-right">
                Status
              </Label>
              <Select 
                value={newTask.status} 
                onValueChange={(value) => setNewTask({...newTask, status: value as TaskStatus})}
              >
                <SelectTrigger id="status" className="col-span-3">
                  <SelectValue placeholder="Select a status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="todo">To Do</SelectItem>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="review">In Review</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="priority" className="text-right">
                Priority
              </Label>
              <Select 
                value={newTask.priority} 
                onValueChange={(value) => setNewTask({...newTask, priority: value as TaskPriority})}
              >
                <SelectTrigger id="priority" className="col-span-3">
                  <SelectValue placeholder="Select a priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="assignee" className="text-right">
                Assignee
              </Label>
              <Select 
                value={newTask.assignee?.id.toString()} 
                onValueChange={(value) => {
                  const assignee = sampleUsers.find(user => user.id.toString() === value);
                  setNewTask({...newTask, assignee});
                }}
              >
                <SelectTrigger id="assignee" className="col-span-3">
                  <SelectValue placeholder="Assign to someone" />
                </SelectTrigger>
                <SelectContent>
                  {sampleUsers.map(user => (
                    <SelectItem key={user.id} value={user.id.toString()}>
                      {user.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="dueDate" className="text-right">
                Due Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="col-span-3 text-left justify-start px-3"
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {newTask.dueDate ? format(newTask.dueDate, 'PPP') : "Select due date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <div className="p-2">
                    <Calendar
                      mode="single"
                      selected={newTask.dueDate}
                      onSelect={(date) => {
                        if (date instanceof Date) {
                          setNewTask({...newTask, dueDate: date});
                        }
                      }}
                      initialFocus
                    />
                  </div>
                </PopoverContent>
              </Popover>
            </div>
            
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tags" className="text-right">
                Tags
              </Label>
              <div className="col-span-3">
                <Input
                  id="tags"
                  placeholder="Add tags separated by commas"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ',') {
                      e.preventDefault();
                      const input = e.currentTarget;
                      const value = input.value.trim();
                      
                      if (value && !newTask.tags?.includes(value)) {
                        setNewTask({
                          ...newTask, 
                          tags: [...(newTask.tags || []), value]
                        });
                      }
                      
                      input.value = '';
                    }
                  }}
                />
                
                <div className="flex flex-wrap gap-1 mt-2">
                  {newTask.tags?.map((tag, index) => (
                    <Badge 
                      key={index} 
                      variant="outline" 
                      className="bg-slate-100 text-slate-800 hover:bg-slate-100 flex items-center gap-1"
                    >
                      <Tag className="h-3 w-3" /> {tag}
                      <button
                        onClick={() => {
                          setNewTask({
                            ...newTask,
                            tags: newTask.tags?.filter((_, i) => i !== index)
                          });
                        }}
                        className="pl-1 text-slate-500 hover:text-slate-700"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddTaskDialog(false)}>
              Cancel
            </Button>
            <Button type="submit" onClick={handleAddTask}>
              Add Task
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Task Dialog */}
      <Dialog open={!!editingTask} onOpenChange={(open) => !open && setEditingTask(null)}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Task</DialogTitle>
            <DialogDescription>
              Make changes to the task details.
            </DialogDescription>
          </DialogHeader>
          
          {editingTask && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-title" className="text-right">
                  Title
                </Label>
                <Input
                  id="edit-title"
                  value={editingTask.title}
                  onChange={(e) => setEditingTask({...editingTask, title: e.target.value})}
                  className="col-span-3"
                />
              </div>
              
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="edit-description" className="text-right pt-2">
                  Description
                </Label>
                <Textarea
                  id="edit-description"
                  value={editingTask.description}
                  onChange={(e) => setEditingTask({...editingTask, description: e.target.value})}
                  className="col-span-3"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-status" className="text-right">
                  Status
                </Label>
                <Select 
                  value={editingTask.status} 
                  onValueChange={(value) => setEditingTask({...editingTask, status: value as TaskStatus})}
                >
                  <SelectTrigger id="edit-status" className="col-span-3">
                    <SelectValue placeholder="Select a status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="todo">To Do</SelectItem>
                    <SelectItem value="in-progress">In Progress</SelectItem>
                    <SelectItem value="review">In Review</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-priority" className="text-right">
                  Priority
                </Label>
                <Select 
                  value={editingTask.priority} 
                  onValueChange={(value) => setEditingTask({...editingTask, priority: value as TaskPriority})}
                >
                  <SelectTrigger id="edit-priority" className="col-span-3">
                    <SelectValue placeholder="Select a priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-assignee" className="text-right">
                  Assignee
                </Label>
                <Select 
                  value={editingTask.assignee?.id.toString()} 
                  onValueChange={(value) => {
                    const assignee = sampleUsers.find(user => user.id.toString() === value);
                    setEditingTask({...editingTask, assignee});
                  }}
                >
                  <SelectTrigger id="edit-assignee" className="col-span-3">
                    <SelectValue placeholder="Assign to someone" />
                  </SelectTrigger>
                  <SelectContent>
                    {sampleUsers.map(user => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-dueDate" className="text-right">
                  Due Date
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="col-span-3 text-left justify-start px-3"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {editingTask.dueDate 
                        ? format(new Date(editingTask.dueDate), 'PPP') 
                        : "Select due date"
                      }
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <div className="p-2">
                      <Calendar
                        mode="single"
                        selected={editingTask.dueDate ? new Date(editingTask.dueDate) : undefined}
                        onSelect={(date) => {
                          if (date instanceof Date) {
                            setEditingTask({...editingTask, dueDate: date});
                          }
                        }}
                        initialFocus
                      />
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-tags" className="text-right">
                  Tags
                </Label>
                <div className="col-span-3">
                  <Input
                    id="edit-tags"
                    placeholder="Add tags separated by commas"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ',') {
                        e.preventDefault();
                        const input = e.currentTarget;
                        const value = input.value.trim();
                        
                        if (value && !editingTask.tags.includes(value)) {
                          setEditingTask({
                            ...editingTask, 
                            tags: [...editingTask.tags, value]
                          });
                        }
                        
                        input.value = '';
                      }
                    }}
                  />
                  
                  <div className="flex flex-wrap gap-1 mt-2">
                    {editingTask.tags.map((tag, index) => (
                      <Badge 
                        key={index} 
                        variant="outline" 
                        className="bg-slate-100 text-slate-800 hover:bg-slate-100 flex items-center gap-1"
                      >
                        <Tag className="h-3 w-3" /> {tag}
                        <button
                          onClick={() => {
                            setEditingTask({
                              ...editingTask,
                              tags: editingTask.tags.filter((_, i) => i !== index)
                            });
                          }}
                          className="pl-1 text-slate-500 hover:text-slate-700"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-4 items-start gap-4">
                <Label className="text-right pt-2">
                  Checklist
                </Label>
                <div className="col-span-3 space-y-2">
                  {editingTask.checklist && editingTask.checklist.map((item, index) => (
                    <div key={item.id} className="flex items-start gap-2">
                      <Checkbox
                        id={`checklist-${item.id}`}
                        checked={item.completed}
                        onCheckedChange={(checked) => {
                          const updatedChecklist = [...editingTask.checklist!];
                          updatedChecklist[index].completed = checked as boolean;
                          setEditingTask({
                            ...editingTask,
                            checklist: updatedChecklist
                          });
                        }}
                        className="mt-1"
                      />
                      <div className="flex-grow">
                        <Input
                          value={item.text}
                          onChange={(e) => {
                            const updatedChecklist = [...editingTask.checklist!];
                            updatedChecklist[index].text = e.target.value;
                            setEditingTask({
                              ...editingTask,
                              checklist: updatedChecklist
                            });
                          }}
                          className="h-8"
                        />
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-slate-500"
                        onClick={() => {
                          setEditingTask({
                            ...editingTask,
                            checklist: editingTask.checklist!.filter((_, i) => i !== index)
                          });
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setEditingTask({
                        ...editingTask,
                        checklist: [
                          ...(editingTask.checklist || []),
                          { id: Date.now().toString(), text: '', completed: false }
                        ]
                      });
                    }}
                  >
                    <PlusCircle className="h-4 w-4 mr-2" /> Add Item
                  </Button>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setEditingTask(null)}
            >
              Cancel
            </Button>
            <Button onClick={handleEditTask}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}