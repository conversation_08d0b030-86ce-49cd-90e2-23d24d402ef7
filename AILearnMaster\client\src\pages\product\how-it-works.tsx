import React from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Zap, Video, Globe, Brain, FileVideo, Mic, Settings, 
  ArrowRight, CheckCircle2, Play, Sparkles, Clock,
  Users, BarChart3, Share2, Pa<PERSON>
} from "lucide-react";
import { Link } from "wouter";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: { duration: 0.6 }
  }
};

export default function HowItWorksPage() {
  const steps = [
    {
      number: 1,
      title: "AI-Powered Course Generation",
      description: "Transform your ideas into professional course content with advanced AI technology",
      icon: <Brain className="h-12 w-12" />,
      gradient: "from-blue-600 to-purple-600",
      details: [
        "Input your course topic or learning objectives",
        "AI generates comprehensive course structure automatically",
        "Get detailed module and lesson outlines with learning objectives",
        "Professional scripts created in your voice and teaching style",
        "Content enhanced with examples, analogies, and engaging elements"
      ]
    },
    {
      number: 2,
      title: "Avatar Video Creation",
      description: "Create lifelike talking head videos using SadTalker AI and A100 GPU acceleration",
      icon: <FileVideo className="h-12 w-12" />,
      gradient: "from-purple-600 to-pink-600",
      details: [
        "Upload a single photo to create your avatar presenter",
        "SadTalker AI generates realistic talking head videos",
        "A100 GPU acceleration for high-quality video processing",
        "Synchronized lip-sync with natural facial expressions",
        "Professional video output in HD quality"
      ]
    },
    {
      number: 3,
      title: "Voice Synthesis & Audio",
      description: "Professional voice narration with multiple TTS providers and customization options",
      icon: <Mic className="h-12 w-12" />,
      gradient: "from-pink-600 to-red-600",
      details: [
        "Chatterbox TTS with 10 enterprise-grade voices",
        "OpenAI TTS for natural, professional narration",
        "ElevenLabs for ultra-realistic emotional voice synthesis",
        "Fine-tune speed, pitch, volume, and stability settings",
        "Support for 25+ languages and accents"
      ]
    },
    {
      number: 4,
      title: "Media Production & Assembly",
      description: "Automated slide generation and video assembly with professional tools",
      icon: <Video className="h-12 w-12" />,
      gradient: "from-red-600 to-orange-600",
      details: [
        "Marp-powered slide generation from course content",
        "FFmpeg video assembly for professional output",
        "Automated synchronization of audio, video, and slides",
        "Custom branding and visual styling options",
        "Export in multiple formats and resolutions"
      ]
    },
    {
      number: 5,
      title: "Distribution & Analytics",
      description: "Publish to multiple platforms and track performance with detailed analytics",
      icon: <Globe className="h-12 w-12" />,
      gradient: "from-orange-600 to-yellow-600",
      details: [
        "One-click publishing to Udemy, Teachable, Kajabi, and more",
        "Hosted course platform with custom domains",
        "Advanced analytics and learner engagement tracking",
        "Social media integration for course promotion",
        "API access for custom integrations"
      ]
    }
  ];

  const benefits = [
    {
      icon: <Clock className="h-8 w-8 text-blue-600" />,
      title: "10x Faster Creation",
      description: "Create professional courses in hours, not weeks"
    },
    {
      icon: <Settings className="h-8 w-8 text-purple-600" />,
      title: "No Technical Skills",
      description: "AI handles all the complex video and audio processing"
    },
    {
      icon: <Palette className="h-8 w-8 text-pink-600" />,
      title: "Professional Quality",
      description: "Enterprise-grade output with A100 GPU acceleration"
    },
    {
      icon: <Users className="h-8 w-8 text-green-600" />,
      title: "Scale Globally",
      description: "Multi-language support and worldwide distribution"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <motion.section 
        className="relative overflow-hidden pt-24 pb-16"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400 rounded-full opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-400 rounded-full opacity-10 blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center"
            variants={itemVariants}
          >
            <motion.div
              className="inline-flex items-center space-x-2 mb-6"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 blur-lg opacity-20"></div>
                <Sparkles className="relative h-12 w-12 text-blue-600" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                How It Works
              </span>
            </motion.div>

            <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              From idea to course in
              <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                5 simple steps
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Our AI-powered platform transforms your knowledge into professional video courses 
              with avatar presenters, voice synthesis, and automated production.
            </p>

            <motion.div 
              className="flex flex-col sm:flex-row gap-4 justify-center"
              variants={itemVariants}
            >
              <Link href="/auth">
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Start Creating <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Button 
                variant="outline" 
                size="lg"
                className="border-2 border-gray-300 hover:border-blue-500 px-8 py-4 text-lg bg-white/80 backdrop-blur-sm"
              >
                <Play className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Steps Section */}
      <motion.section 
        className="py-20"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-20">
            {steps.map((step, index) => (
              <motion.div 
                key={step.number}
                className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12`}
                variants={itemVariants}
              >
                {/* Content */}
                <div className="flex-1 space-y-6">
                  <div className="flex items-center space-x-4">
                    <div className={`p-4 rounded-2xl bg-gradient-to-r ${step.gradient} text-white shadow-lg`}>
                      {step.icon}
                    </div>
                    <Badge 
                      variant="outline" 
                      className="text-lg px-4 py-2 border-gray-300"
                    >
                      Step {step.number}
                    </Badge>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-3xl font-bold text-gray-900">
                      {step.title}
                    </h3>
                    <p className="text-xl text-gray-600 leading-relaxed">
                      {step.description}
                    </p>
                  </div>

                  <ul className="space-y-3">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start space-x-3">
                        <CheckCircle2 className="h-6 w-6 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 leading-relaxed">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Visual */}
                <div className="flex-1">
                  <motion.div
                    className="relative"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="bg-white/60 backdrop-blur-sm border border-gray-200/50 shadow-2xl">
                      <CardHeader className="text-center space-y-4">
                        <div className={`w-20 h-20 mx-auto rounded-2xl bg-gradient-to-r ${step.gradient} flex items-center justify-center text-white shadow-lg`}>
                          {step.icon}
                        </div>
                        <div className="text-6xl font-bold bg-gradient-to-r from-gray-300 to-gray-500 bg-clip-text text-transparent">
                          {step.number.toString().padStart(2, '0')}
                        </div>
                      </CardHeader>
                      <CardContent className="text-center">
                        <h4 className="text-xl font-semibold text-gray-800 mb-2">
                          {step.title}
                        </h4>
                        <p className="text-gray-600">
                          {step.description}
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Benefits Section */}
      <motion.section 
        className="py-20 bg-white/50 backdrop-blur-sm"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Why Choose CourseAI?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience the power of AI-driven course creation with professional results
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ duration: 0.2 }}
              >
                <Card className="h-full text-center bg-white/60 backdrop-blur-sm border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardHeader className="space-y-4">
                    <div className="w-16 h-16 mx-auto rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 flex items-center justify-center">
                      {benefit.icon}
                    </div>
                    <CardTitle className="text-xl font-semibold text-gray-900">
                      {benefit.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-600 leading-relaxed">
                      {benefit.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section 
        className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white relative overflow-hidden"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
      >
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full opacity-5 blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full opacity-5 blur-3xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div variants={itemVariants}>
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Ready to start creating?
            </h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
              Join thousands of educators and creators who are already using CourseAI 
              to build professional courses with AI-powered avatar videos.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth">
                <Button 
                  size="lg" 
                  variant="secondary"
                  className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Get Started Free <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/product/features">
                <Button 
                  size="lg" 
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg font-semibold transition-all duration-300"
                >
                  Explore Features
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </motion.section>
    </div>
  );
}