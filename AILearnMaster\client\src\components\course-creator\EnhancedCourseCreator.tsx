import React, { useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Check, ArrowRight, ArrowLeft, FileText, Layers, Video, Book, Upload, PenTool } from "lucide-react";
import { v4 as uuidv4 } from 'uuid';
import { CourseStructureGenerationParams } from '@/lib/ai';
import { apiRequest } from '@/lib/queryClient';

interface CourseDetails {
  title: string;
  description: string;
  category: string;
}

interface Module {
  id: string;
  title: string;
  description: string;
  lessons: Lesson[];
}

interface Lesson {
  id: string;
  title: string;
  description: string;
  type: string;
}

interface CourseStructure {
  title: string;
  description: string;
  modules: Module[];
}

const EnhancedCourseCreator: React.FC = () => {
  const { toast } = useToast();
  const [activeStep, setActiveStep] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [generationComplete, setGenerationComplete] = useState(false);
  const [moduleCount, setModuleCount] = useState(5);
  
  const [courseDetails, setCourseDetails] = useState<CourseDetails>({
    title: '',
    description: '',
    category: 'technology'
  });

  const [courseStructure, setCourseStructure] = useState<CourseStructure>({
    title: '',
    description: '',
    modules: []
  });

  const steps = [
    { id: 'details', title: 'Course Details', icon: <FileText className="w-5 h-5" /> },
    { id: 'structure', title: 'Content Structure', icon: <Layers className="w-5 h-5" /> },
    { id: 'scripts', title: 'Script Generation', icon: <PenTool className="w-5 h-5" /> },
    { id: 'media', title: 'Media', icon: <Video className="w-5 h-5" /> },
    { id: 'quizzes', title: 'Quizzes', icon: <Book className="w-5 h-5" /> },
    { id: 'publish', title: 'Publish', icon: <Upload className="w-5 h-5" /> }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setCourseDetails(prev => ({ ...prev, [name]: value }));
  };

  const handleCategoryChange = (value: string) => {
    setCourseDetails(prev => ({ ...prev, category: value }));
  };

  const handleModuleCountChange = (value: number[]) => {
    setModuleCount(value[0]);
  };

  const generateCourseStructure = async (params: CourseStructureGenerationParams) => {
    try {
      const response = await apiRequest('POST', '/api/ai/generate-course-structure', params);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error generating course structure:', error);
      throw error;
    }
  };

  const handleGenerateCourse = async () => {
    if (!courseDetails.title || !courseDetails.description) {
      toast({
        title: 'Missing information',
        description: 'Please fill in all required fields.',
        variant: 'destructive'
      });
      return;
    }

    setIsGenerating(true);
    setProgress(0);

    // Simulate progress while waiting for API
    const timer = setInterval(() => {
      setProgress(prev => {
        if (prev >= 95) {
          clearInterval(timer);
          return 95;
        }
        return prev + 5;
      });
    }, 500);

    try {
      const response = await generateCourseStructure({
        title: courseDetails.title,
        description: courseDetails.description,
        category: courseDetails.category,
        moduleCount: moduleCount
      });

      setCourseStructure({
        title: courseDetails.title,
        description: courseDetails.description,
        modules: response.modules.map(module => ({
          id: uuidv4(),
          title: module.title,
          description: module.description || '',
          lessons: module.lessons.map(lesson => ({
            id: uuidv4(),
            title: lesson.title,
            description: lesson.description,
            type: 'video'
          }))
        }))
      });

      // Complete progress and show success
      clearInterval(timer);
      setProgress(100);
      setGenerationComplete(true);
      
      setTimeout(() => {
        setIsGenerating(false);
        setActiveStep(1); // Move to content structure step
      }, 1000);

      toast({
        title: 'Course structure generated!',
        description: 'Your course modules and lessons are ready.',
      });
    } catch (error) {
      clearInterval(timer);
      setIsGenerating(false);
      console.error('Error generating course structure:', error);
      
      toast({
        title: 'Generation failed',
        description: 'There was an error generating your course structure. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const renderCourseDetailsStep = () => (
    <div className="space-y-6 py-4">
      <Card>
        <CardHeader>
          <CardTitle>Course Information</CardTitle>
          <CardDescription>
            Provide details about your course to help generate a well-structured learning experience.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Course Title</Label>
            <Input 
              id="title" 
              name="title"
              placeholder="e.g., Complete Web Development Bootcamp" 
              value={courseDetails.title}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Course Description</Label>
            <Textarea 
              id="description" 
              name="description"
              placeholder="Describe what your course will teach and who it's for..."
              rows={4}
              value={courseDetails.description}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="category">Category</Label>
            <Select value={courseDetails.category} onValueChange={handleCategoryChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="technology">Technology</SelectItem>
                <SelectItem value="business">Business</SelectItem>
                <SelectItem value="design">Design</SelectItem>
                <SelectItem value="marketing">Marketing</SelectItem>
                <SelectItem value="personal-development">Personal Development</SelectItem>
                <SelectItem value="health">Health & Wellness</SelectItem>
                <SelectItem value="language">Language</SelectItem>
                <SelectItem value="arts">Arts & Creativity</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-4 pt-2">
            <div className="flex justify-between">
              <Label>Number of Modules: {moduleCount}</Label>
              <span className="text-sm text-muted-foreground">(1-10)</span>
            </div>
            <Slider
              defaultValue={[5]}
              max={10}
              min={1}
              step={1}
              value={[moduleCount]}
              onValueChange={handleModuleCountChange}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>
      
      <div className="flex justify-end gap-2">
        {isGenerating ? (
          <Card className="w-full p-4">
            <CardHeader className="p-0 pb-4">
              <CardTitle className="text-lg">Generating Course Structure</CardTitle>
              <CardDescription>
                Our AI is creating a structured course based on your input...
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0 space-y-4">
              <Progress value={progress} className="h-2" />
              <div className="text-sm text-muted-foreground">
                {generationComplete ? 'Generation complete!' : 'This may take a moment...'}
              </div>
            </CardContent>
          </Card>
        ) : (
          <Button onClick={handleGenerateCourse} className="w-full sm:w-auto">
            Generate Course Structure <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );

  const renderContentStructureStep = () => (
    <div className="space-y-6 py-4">
      <Card>
        <CardHeader>
          <CardTitle>Course Structure</CardTitle>
          <CardDescription>
            Review and refine your course's structure with connected modules and lessons.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {courseStructure.modules.map((module, index) => (
            <div key={module.id} className="mb-6 last:mb-0">
              <div className="flex items-center p-3 bg-muted rounded-t-lg border border-border">
                <div className="bg-primary text-primary-foreground w-8 h-8 rounded-full flex items-center justify-center mr-3">
                  {index + 1}
                </div>
                <div>
                  <h3 className="font-medium">{module.title}</h3>
                  <p className="text-sm text-muted-foreground">{module.description}</p>
                </div>
              </div>
              <div className="border border-t-0 border-border rounded-b-lg divide-y">
                {module.lessons.map((lesson, lessonIndex) => (
                  <div key={lesson.id} className="p-3 pl-14 relative">
                    <div className="absolute left-5 top-3 w-6 h-6 rounded-full bg-muted-foreground/20 flex items-center justify-center text-xs">
                      {lessonIndex + 1}
                    </div>
                    <h4 className="text-sm font-medium">{lesson.title}</h4>
                    <p className="text-xs text-muted-foreground">{lesson.description}</p>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
      
      <div className="flex justify-between">
        <Button variant="outline" onClick={() => setActiveStep(0)}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <Button onClick={() => setActiveStep(2)}>
          Continue to Scripts
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );

  const renderProgressBar = () => (
    <div className="flex items-center w-full max-w-4xl mx-auto mt-8">
      {steps.map((step, index) => (
        <React.Fragment key={step.id}>
          <div
            className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
              index < activeStep
                ? 'bg-primary border-primary text-primary-foreground'
                : index === activeStep
                ? 'border-primary text-primary'
                : 'border-muted-foreground/30 text-muted-foreground/50'
            }`}
          >
            {index < activeStep ? (
              <Check className="w-5 h-5" />
            ) : (
              step.icon
            )}
          </div>
          {index < steps.length - 1 && (
            <div
              className={`flex-1 h-1 ${
                index < activeStep ? 'bg-primary' : 'bg-muted-foreground/30'
              }`}
            ></div>
          )}
        </React.Fragment>
      ))}
    </div>
  );

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return renderCourseDetailsStep();
      case 1:
        return renderContentStructureStep();
      case 2:
        return <div className="py-4">Script Generation (Coming Soon)</div>;
      case 3:
        return <div className="py-4">Media Selection (Coming Soon)</div>;
      case 4:
        return <div className="py-4">Quiz Creation (Coming Soon)</div>;
      case 5:
        return <div className="py-4">Publish Course (Coming Soon)</div>;
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold">Enhanced Course Creator</h1>
        <p className="text-muted-foreground mt-2">
          Create professional, engaging courses with AI-powered content generation
        </p>
      </div>
      
      {renderProgressBar()}
      
      <div className="max-w-4xl mx-auto mt-8">
        {renderStepContent()}
      </div>
    </div>
  );
};

export default EnhancedCourseCreator;