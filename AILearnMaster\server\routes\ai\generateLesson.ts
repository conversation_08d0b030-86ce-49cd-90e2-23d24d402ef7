import { Request, Response } from 'express';
import OpenAI from 'openai';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export async function generateLesson(req: Request, res: Response) {
  try {
    const { moduleTitle, moduleDescription, existingLessons, courseTitle, targetAudience } = req.body;

    if (!moduleTitle) {
      return res.status(400).json({ error: 'Module title is required' });
    }

    const existingLessonTitles = existingLessons?.map((l: any) => l.title).join(', ') || 'None';

    const prompt = `You are an expert instructional designer. Create a new lesson for a course module.

Module: "${moduleTitle}"
Module Description: ${moduleDescription || 'Not provided'}
Course: "${courseTitle}"
Target Audience: ${targetAudience}
Existing Lessons: ${existingLessonTitles}

Create a NEW lesson that:
1. Complements the existing lessons (don't duplicate topics)
2. Fits logically within the module theme
3. Is appropriate for the target audience
4. Has a clear learning objective

Respond with JSON in this exact format:
{
  "title": "Lesson Title (3-8 words)",
  "description": "Brief description of what learners will achieve (1-2 sentences)"
}`;

    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [
        {
          role: "system",
          content: "You are an expert instructional designer who creates engaging, practical lessons. Always respond with valid JSON."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      response_format: { type: "json_object" },
      temperature: 0.7,
      max_tokens: 200
    });

    const result = JSON.parse(response.choices[0].message.content || '{}');

    if (!result.title || !result.description) {
      throw new Error('Invalid response format from AI');
    }

    res.json(result);
  } catch (error: any) {
    console.error('Error generating lesson:', error);
    res.status(500).json({ 
      error: 'Failed to generate lesson', 
      details: error.message 
    });
  }
}