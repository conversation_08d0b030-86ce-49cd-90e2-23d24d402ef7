import { apiRequest } from "./queryClient";

export interface ScriptGenerationParams {
  title: string;
  description: string;
  targetAudience?: string;
}

export interface LessonScriptGenerationParams {
  courseTitle: string;
  courseDescription: string;
  moduleTitle: string;
  moduleDescription: string;
  lessonTitle: string;
  lessonDescription: string;
  targetAudience: string;
}

export interface CourseStructureGenerationParams {
  title: string;
  description: string;
  category: string;
  targetAudience?: string;
  keyTopics?: string;
  contentNotes?: string;
  moduleCount?: number;
}

export interface GeneratedScript {
  script: string;
}

export interface GeneratedCourseStructure {
  title: string;
  description: string;
  category: string;
  targetAudience?: string;
  modules: {
    title: string;
    description?: string;
    lessons: {
      title: string;
      description: string;
    }[];
  }[];
}

export interface ImageGenerationParams {
  prompt: string;
  style?: string;
  size?: string;
}

export interface GeneratedImage {
  imageUrl: string;
}

export interface ImageAnalysisParams {
  imageUrl: string;
  prompt: string;
}

export interface ImageAnalysisResult {
  analysis: string;
}

// New video generation interfaces
export type AnimationPreference = 'minimal' | 'moderate' | 'dynamic';

export interface VideoGenerationParams {
  script: string;
  title: string;
  style?: string;
  animationPreference?: AnimationPreference;
  duration?: number; // in seconds
  narrationVoice?: string;
  // Additional parameters for free video mode
  freeVideoImport?: boolean; // Flag to indicate this is a free video import, not AI generation
  videoUrl?: string; // Direct URL to video for free mode
  source?: 'pixabay' | 'pexels'; // Source of the free video
  sourceId?: string; // ID of the video in the source platform
  videos?: any[]; // For multiple video selection
}

export interface VideoAnimationKeyframe {
  timestamp: number; // seconds from start
  description: string;
  imagePrompt?: string;
}

export interface GeneratedVideo {
  id: string; // Unique identifier for the video
  videoUrl?: string; // Can be undefined while processing
  thumbnailUrl?: string; // Can be undefined while processing
  duration?: number; // Can be undefined for newly created videos
  title?: string; // Can be undefined for stock videos
  animationKeyframes?: VideoAnimationKeyframe[];
  processingStatus: 'queued' | 'processing' | 'completed' | 'failed';
  processingProgress?: number; // Percentage of completion (0-100)
  estimatedCompletionTime?: number; // Unix timestamp
  error?: string; // Error message if processing failed
  createdAt: string; // ISO string date when the video was created
}

/**
 * Generate a script for a lesson using Gemini AI
 */
export async function generateScript(params: ScriptGenerationParams): Promise<GeneratedScript> {
  try {
    const response = await apiRequest("POST", "/api/ai/generate-script", params);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Failed to generate script");
    }
    
    return await response.json();
  } catch (error) {
    console.error("Script generation error:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to generate script");
  }
}

/**
 * Generate a script for a specific lesson within a module using Gemini AI
 */
export async function generateLessonScript(params: LessonScriptGenerationParams): Promise<GeneratedScript> {
  try {
    // First try to get a response from the API
    try {
      const response = await apiRequest("POST", "/api/ai/generate-lesson-script", params);
      const data = await response.json();
      if (data && data.script) {
        return data;
      }
      // If API fails or returns no script, fall back to template (handled in catch)
      throw new Error("API did not return a valid script");
    } catch (apiError) {
      // Import the demo template
      const { generateDemoScriptTemplate } = await import('./demo-script-template');
      
      // Generate a structured template as fallback
      console.log("Using fallback template for script generation");
      const templateScript = generateDemoScriptTemplate(params.lessonTitle, params.lessonDescription);
      
      return { script: templateScript };
    }
  } catch (error) {
    console.error("Lesson script generation error:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to generate lesson script");
  }
}

/**
 * Generate a course structure with enhanced logical progression between modules
 * This uses pedagogically-focused AI that creates a proper learning sequence
 */
export async function generateCourseStructure(params: CourseStructureGenerationParams): Promise<GeneratedCourseStructure> {
  try {
    console.log("Generating course structure with module count:", params.moduleCount);
    // Use our enhanced course structure generator endpoint
    const response = await apiRequest("POST", "/api/ai/generate-course-structure", params);
    
    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }
    
    const data = await response.json();
    console.log("Successfully received course structure with logical progression");
    return data;
  } catch (error) {
    console.error("Course structure generation error:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to generate course structure");
  }
}

export interface TextToSpeechParams {
  text: string;
  voice?: string;
  speed?: number;
}

export interface TextToSpeechResult {
  audioUrl: string;
  duration: number;
}

/**
 * Convert text to speech using TTS service
 * Note: Will need integration with a third-party service
 */
export async function convertTextToSpeech(params: TextToSpeechParams): Promise<TextToSpeechResult> {
  try {
    const response = await apiRequest("POST", "/api/ai/text-to-speech", params);
    return await response.json();
  } catch (error) {
    console.error("Text-to-speech error:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to convert text to speech");
  }
}

/**
 * Generate an image based on a text prompt
 */
export async function generateImage(params: ImageGenerationParams): Promise<GeneratedImage> {
  try {
    const response = await apiRequest("POST", "/api/ai/generate-image", params);
    return await response.json();
  } catch (error) {
    console.error("Image generation error:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to generate image");
  }
}

/**
 * Analyze an image using Gemini Vision capabilities
 */
export async function analyzeImage(params: ImageAnalysisParams): Promise<ImageAnalysisResult> {
  try {
    const response = await apiRequest("POST", "/api/ai/analyze-image", params);
    return await response.json();
  } catch (error) {
    console.error("Image analysis error:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to analyze image");
  }
}

/**
 * Generate an animated educational video with narration from a script
 */
export async function generateVideo(params: VideoGenerationParams): Promise<GeneratedVideo> {
  try {
    const response = await apiRequest("POST", "/api/ai/generate-video", params);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Failed to generate video");
    }
    
    return await response.json();
  } catch (error) {
    console.error("Video generation error:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to generate video");
  }
}

/**
 * Check the status of a video being generated
 */
export async function checkVideoStatus(videoId: string): Promise<GeneratedVideo> {
  try {
    const response = await apiRequest("GET", `/api/ai/video-status/${videoId}`);
    return await response.json();
  } catch (error) {
    console.error("Video status check error:", error);
    throw new Error(error instanceof Error ? error.message : "Failed to check video status");
  }
}
