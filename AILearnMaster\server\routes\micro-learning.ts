import express, { Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { 
  insertMicroLearningSegmentSchema, 
  insertMicroLearningKnowledgeCheckSchema,
  insertMicroLearningUserProgressSchema,
  insertUserKnowledgeCheckResponseSchema
} from '@shared/schema';

const router = express.Router();

// Authentication middleware
const checkAuth = (req: Request, res: Response, next: Function) => {
  if (!req.session?.userId) {
    return res.status(401).json({ message: "Unauthorized" });
  }
  next();
};

// Create a micro-learning segment
router.post('/api/micro-learning/segments', checkAuth, async (req: Request, res: Response) => {
  try {
    const result = insertMicroLearningSegmentSchema.safeParse(req.body);
    
    if (!result.success) {
      return res.status(400).json({ 
        message: "Invalid data", 
        errors: result.error.format() 
      });
    }
    
    const segment = await storage.createMicroLearningSegment(result.data);
    return res.status(201).json(segment);
  } catch (error) {
    console.error("Error creating micro-learning segment:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

// Get all segments for a lesson
router.get('/api/micro-learning/segments/lesson/:lessonId', async (req: Request, res: Response) => {
  try {
    const lessonId = parseInt(req.params.lessonId);
    
    if (isNaN(lessonId)) {
      return res.status(400).json({ message: "Invalid lesson ID" });
    }
    
    const segments = await storage.getMicroLearningSegmentsByLessonId(lessonId);
    return res.status(200).json(segments);
  } catch (error) {
    console.error("Error fetching micro-learning segments:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

// Create a knowledge check
router.post('/api/micro-learning/knowledge-checks', checkAuth, async (req: Request, res: Response) => {
  try {
    const result = insertMicroLearningKnowledgeCheckSchema.safeParse(req.body);
    
    if (!result.success) {
      return res.status(400).json({ 
        message: "Invalid data", 
        errors: result.error.format() 
      });
    }
    
    const knowledgeCheck = await storage.createMicroLearningKnowledgeCheck(result.data);
    return res.status(201).json(knowledgeCheck);
  } catch (error) {
    console.error("Error creating knowledge check:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

// Get all knowledge checks for a lesson
router.get('/api/micro-learning/knowledge-checks/lesson/:lessonId', async (req: Request, res: Response) => {
  try {
    const lessonId = parseInt(req.params.lessonId);
    
    if (isNaN(lessonId)) {
      return res.status(400).json({ message: "Invalid lesson ID" });
    }
    
    const knowledgeChecks = await storage.getMicroLearningKnowledgeChecksByLessonId(lessonId);
    return res.status(200).json(knowledgeChecks);
  } catch (error) {
    console.error("Error fetching knowledge checks:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

// Create or update user progress
router.post('/api/micro-learning/progress', checkAuth, async (req: Request, res: Response) => {
  try {
    const schema = insertMicroLearningUserProgressSchema.omit({ 
      userId: true
    });
    
    const result = schema.safeParse(req.body);
    
    if (!result.success) {
      return res.status(400).json({ 
        message: "Invalid data", 
        errors: result.error.format() 
      });
    }

    // Check if progress already exists for this user and lesson
    if (!req.session.userId) {
      return res.status(401).json({ message: "Unauthorized - user ID not found" });
    }
    
    const existingProgress = await storage.getMicroLearningUserProgressByUserAndLesson(
      req.session.userId,
      result.data.lessonId
    );

    let progress;
    if (existingProgress) {
      // Update existing progress
      progress = await storage.updateMicroLearningUserProgress(
        existingProgress.id,
        {
          ...result.data,
          userId: req.session.userId,
          updatedAt: new Date()
        }
      );
    } else {
      // Create new progress entry
      progress = await storage.createMicroLearningUserProgress({
        ...result.data,
        userId: req.session.userId
      });
    }
    
    return res.status(200).json(progress);
  } catch (error) {
    console.error("Error updating user progress:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

// Get user progress for a lesson
router.get('/api/micro-learning/progress/lesson/:lessonId', checkAuth, async (req: Request, res: Response) => {
  try {
    const lessonId = parseInt(req.params.lessonId);
    
    if (isNaN(lessonId)) {
      return res.status(400).json({ message: "Invalid lesson ID" });
    }
    
    if (!req.session.userId) {
      return res.status(401).json({ message: "Unauthorized - user ID not found" });
    }
    
    const progress = await storage.getMicroLearningUserProgressByUserAndLesson(
      req.session.userId,
      lessonId
    );
    
    if (!progress) {
      return res.status(404).json({ message: "No progress found" });
    }
    
    return res.status(200).json(progress);
  } catch (error) {
    console.error("Error fetching user progress:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

// Submit a knowledge check response
router.post('/api/micro-learning/knowledge-check/response', checkAuth, async (req: Request, res: Response) => {
  try {
    // Use a custom schema for the MicroLearningPlayer's simplified payload
    const schema = z.object({
      lessonId: z.number(),
      knowledgeCheckId: z.string(),
      userAnswer: z.number(),
      isCorrect: z.boolean().optional(),
      correct: z.boolean().optional()
    });
    
    const result = schema.safeParse(req.body);
    
    if (!result.success) {
      return res.status(400).json({ 
        message: "Invalid data", 
        errors: result.error.format() 
      });
    }
    
    // Check if user is authenticated
    if (!req.session.userId) {
      return res.status(401).json({ message: "Unauthorized - user ID not found" });
    }
    
    // Form the full user knowledge check response data to match schema
    const responseData = {
      userId: req.session.userId,
      knowledgeCheckId: Number(result.data.knowledgeCheckId), // Convert to number as schema expects
      userAnswer: result.data.userAnswer,
      correct: result.data.correct !== undefined ? result.data.correct : (result.data.isCorrect !== undefined ? result.data.isCorrect : false)
    };
    
    // Save the user's response
    const response = await storage.createUserKnowledgeCheckResponse(responseData);
    
    return res.status(201).json({
      ...response,
      isCorrect: responseData.correct, // Return as isCorrect for client compatibility
      correct: responseData.correct    // Also include the correct field for future use
    });
  } catch (error) {
    console.error("Error submitting knowledge check response:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

// Get all responses by a user for a lesson
router.get('/api/micro-learning/knowledge-checks/responses/lesson/:lessonId', checkAuth, async (req: Request, res: Response) => {
  try {
    const lessonId = parseInt(req.params.lessonId);
    
    if (isNaN(lessonId)) {
      return res.status(400).json({ message: "Invalid lesson ID" });
    }
    
    if (!req.session.userId) {
      return res.status(401).json({ message: "Unauthorized - user ID not found" });
    }
    
    const responses = await storage.getUserKnowledgeCheckResponsesByLessonId(
      req.session.userId,
      lessonId
    );
    
    return res.status(200).json(responses);
  } catch (error) {
    console.error("Error fetching knowledge check responses:", error);
    return res.status(500).json({ message: "Server error" });
  }
});

export default router;