"use client";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Content,
  Root,
  Trigger,
  createCollapsibleScope
} from "./chunk-J4C7HFH5.js";
import "./chunk-HRCTDOCG.js";
import "./chunk-OD433RWB.js";
import "./chunk-CPFL7ZFD.js";
import "./chunk-DKHUMOWT.js";
import "./chunk-NRN5YYFF.js";
import "./chunk-BTIBV3P6.js";
import "./chunk-LSQNWB54.js";
import "./chunk-RPCDYKBN.js";
import "./chunk-KBTYAULA.js";
import "./chunk-QCHXOAYK.js";
import "./chunk-WOOG5QLI.js";
export {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Content,
  Root,
  Trigger,
  createCollapsibleScope
};
//# sourceMappingURL=@radix-ui_react-collapsible.js.map
