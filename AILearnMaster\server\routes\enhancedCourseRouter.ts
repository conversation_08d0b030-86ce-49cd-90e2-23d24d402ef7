import express, { Request, Response } from 'express';
import { z } from 'zod';
import { generateEnhancedCourseStructure } from '../services/enhancedCourseGenerator';

const router = express.Router();

// Create schema for course structure generation
const courseStructureSchema = z.object({
  title: z.string(),
  description: z.string().optional().default(""),
  category: z.string(),
  targetAudience: z.string().optional(),
  keyTopics: z.string().optional(),
  contentNotes: z.string().optional(),
  moduleCount: z.number().int().positive().optional()
});

// Enhanced course structure generation endpoint
router.post('/generate-course-structure', async (req: Request, res: Response) => {
  try {
    // For development, we're skipping authentication check
    // In production, we would check req.session.userId or use middleware
    // if (!req.isAuthenticated() || !req.user) {
    //   return res.status(401).json({ message: "Not authenticated" });
    // }

    // Validate request data
    const validationResult = courseStructureSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({ 
        message: "Invalid request data", 
        errors: validationResult.error.format() 
      });
    }
    
    const courseData = validationResult.data;
    
    // Generate enhanced course structure
    try {
      console.log("Enhanced course structure requested for:", courseData.title);
      const courseStructure = await generateEnhancedCourseStructure(courseData);
      console.log("Enhanced course structure generation completed successfully");
      return res.status(200).json(courseStructure);
    } catch (aiError) {
      console.error("Enhanced course generation error:", aiError);
      return res.status(500).json({ 
        message: "Failed to generate enhanced course structure", 
        error: aiError instanceof Error ? aiError.message : String(aiError)
      });
    }
  } catch (error) {
    console.error("Server error in enhanced course generation:", error);
    return res.status(500).json({ 
      message: "Server error", 
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

export default router;