import React, { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { Mail, UserPlus, Settings } from 'lucide-react';

interface Course {
  id: number;
  title: string;
  description: string;
  status: string;
  category: string;
  thumbnailUrl: string | null;
  completion: number;
  createdAt: Date;
  updatedAt: Date;
  collaboratorCount?: number;
}

interface AddCollaboratorDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AddCollaboratorDialog({ isOpen, onClose }: AddCollaboratorDialogProps) {
  const { toast } = useToast();
  const [selectedCourseId, setSelectedCourseId] = useState<string>("");
  const [collaboratorEmail, setCollaboratorEmail] = useState("");
  const [collaboratorRole, setCollaboratorRole] = useState("viewer");
  const [collaboratorCanEdit, setCollaboratorCanEdit] = useState(false);

  // Fetch user's own courses for sharing
  const { data: ownedCourses, isLoading: coursesLoading } = useQuery<Course[]>({
    queryKey: ["/api/my-courses-for-sharing"],
    enabled: isOpen
  });

  // Add collaborator mutation
  const addCollaboratorMutation = useMutation({
    mutationFn: ({ courseId, email, role, canEdit }: { courseId: number, email: string, role: string, canEdit: boolean }) =>
      apiRequest('POST', `/api/courses/${courseId}/collaborators`, { email, role, canEdit }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/my-collaborations"] });
      queryClient.invalidateQueries({ queryKey: ["/api/my-courses-for-sharing"] });
      handleClose();
      toast({
        title: "Collaborator Added",
        description: "The user has been successfully added as a collaborator.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add collaborator. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleClose = () => {
    setSelectedCourseId("");
    setCollaboratorEmail("");
    setCollaboratorRole("viewer");
    setCollaboratorCanEdit(false);
    onClose();
  };

  const handleAddCollaborator = () => {
    if (!selectedCourseId || !collaboratorEmail.trim()) {
      toast({
        title: "Missing Information",
        description: "Please select a course and enter a valid email address.",
        variant: "destructive",
      });
      return;
    }

    addCollaboratorMutation.mutate({
      courseId: parseInt(selectedCourseId),
      email: collaboratorEmail.trim(),
      role: collaboratorRole,
      canEdit: collaboratorCanEdit
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="w-5 h-5" />
            Add Collaborator
          </DialogTitle>
          <DialogDescription>
            Invite someone to collaborate on one of your courses. They'll be able to view and edit based on the permissions you set.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Course Selection */}
          <div className="space-y-2">
            <Label htmlFor="course-select">Select Course</Label>
            <Select value={selectedCourseId} onValueChange={setSelectedCourseId}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a course to share" />
              </SelectTrigger>
              <SelectContent>
                {coursesLoading ? (
                  <SelectItem value="loading" disabled>Loading courses...</SelectItem>
                ) : ownedCourses?.length ? (
                  ownedCourses.map((course) => (
                    <SelectItem key={course.id} value={course.id.toString()}>
                      <div className="flex flex-col">
                        <span className="font-medium">{course.title}</span>
                        <span className="text-xs text-muted-foreground">
                          {course.category} • {course.collaboratorCount || 0} collaborators
                        </span>
                      </div>
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="none" disabled>No courses available for sharing</SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Email Input */}
          <div className="space-y-2">
            <Label htmlFor="collaborator-email">Collaborator Email</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="collaborator-email"
                type="email"
                placeholder="Enter email address"
                value={collaboratorEmail}
                onChange={(e) => setCollaboratorEmail(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Role Selection */}
          <div className="space-y-2">
            <Label htmlFor="role-select">Role</Label>
            <Select value={collaboratorRole} onValueChange={setCollaboratorRole}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="viewer">
                  <div className="flex flex-col">
                    <span className="font-medium">Viewer</span>
                    <span className="text-xs text-muted-foreground">Can view course content only</span>
                  </div>
                </SelectItem>
                <SelectItem value="editor">
                  <div className="flex flex-col">
                    <span className="font-medium">Editor</span>
                    <span className="text-xs text-muted-foreground">Can view and edit course content</span>
                  </div>
                </SelectItem>
                <SelectItem value="admin">
                  <div className="flex flex-col">
                    <span className="font-medium">Admin</span>
                    <span className="text-xs text-muted-foreground">Full access including managing collaborators</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Permissions */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Permissions
            </Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="can-edit"
                  checked={collaboratorCanEdit}
                  onCheckedChange={(checked) => setCollaboratorCanEdit(checked === true)}
                />
                <Label htmlFor="can-edit" className="text-sm">
                  Allow editing course content
                </Label>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleAddCollaborator}
            disabled={addCollaboratorMutation.isPending || !selectedCourseId || !collaboratorEmail.trim()}
          >
            {addCollaboratorMutation.isPending ? "Adding..." : "Add Collaborator"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}