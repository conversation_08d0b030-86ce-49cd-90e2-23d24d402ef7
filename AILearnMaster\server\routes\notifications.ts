import express from 'express';

const router = express.Router();

// Simple notification endpoints that return empty data for now
router.get('/', async (req, res) => {
  res.json([]);
});

router.get('/unread', async (req, res) => {
  res.json([]);
});

router.get('/count', async (req, res) => {
  res.json({ count: 0 });
});

router.get('/types', async (req, res) => {
  res.json([]);
});

router.get('/preferences', async (req, res) => {
  res.json({});
});

export default router;