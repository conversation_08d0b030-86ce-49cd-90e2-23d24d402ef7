modules = ["nodejs-20", "bash", "web", "postgresql-16", "python-3.11", "python3"]
run = "npm run dev"
hidden = [".config", ".git", "generated-icon.png", "node_modules", "dist"]

[nix]
channel = "stable-24_05"
packages = ["espeak", "ffmpeg", "ffmpeg-full", "freetype", "imagemagickBig", "lcms2", "libimagequant", "libjpeg", "libtiff", "libwebp", "libxcrypt", "nano", "openjpeg", "tcl", "tk", "zlib", "mailutils", "litterbox", "imagemagick"]

[deployment]
deploymentTarget = "cloudrun"
run = ["sh", "-c", "npm run dev"]

[workflows]
runButton = "Run Application"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Start application"

[[workflows.workflow]]
name = "Start application"
author = "agent"

[workflows.workflow.metadata]
agentRequireRestartOnSave = false

[[workflows.workflow.tasks]]
task = "packager.installForAll"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"
waitForPort = 5000

[[workflows.workflow]]
name = "Start Application"
author = 40460325
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "run"

[[workflows.workflow]]
name = "Run Application"
author = 40460325
mode = "sequential"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "npm run dev"

[[ports]]
localPort = 3000
externalPort = 3001

[[ports]]
localPort = 5000
externalPort = 80

[[ports]]
localPort = 5001
externalPort = 3000

[[ports]]
localPort = 8080
externalPort = 8080
