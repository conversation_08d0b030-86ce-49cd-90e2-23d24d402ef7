import { useEffect, useState } from 'react';
import { 
  Eye, 
  Type, 
  RotateCcw, 
  Sun,
  SunMedium,
  SunDim,
  <PERSON><PERSON>ointer
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";

// Define types for accessibility settings
type ColorContrastLevel = 'default' | 'enhanced' | 'high';
type FontSize = 'default' | 'large' | 'x-large';
type MotionReduction = 'default' | 'reduced';

interface AccessibilitySettings {
  colorContrast: ColorContrastLevel;
  fontSize: FontSize;
  motionReduction: MotionReduction;
}

const defaultSettings: AccessibilitySettings = {
  colorContrast: 'default',
  fontSize: 'default',
  motionReduction: 'default',
};

export function SimpleAccessibilityMenu() {
  const [settings, setSettings] = useState<AccessibilitySettings>(defaultSettings);
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  
  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('accessibility-settings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);
        applySettings(parsedSettings);
      } catch (error) {
        console.error('Failed to parse saved accessibility settings:', error);
      }
    }
  }, []);
  
  // Function to apply settings to the document/html element
  const applySettings = (settings: AccessibilitySettings) => {
    const htmlElement = document.documentElement;
    
    // Apply color contrast classes
    htmlElement.classList.remove('color-contrast-enhanced', 'color-contrast-high');
    if (settings.colorContrast === 'enhanced') {
      htmlElement.classList.add('color-contrast-enhanced');
    } else if (settings.colorContrast === 'high') {
      htmlElement.classList.add('color-contrast-high');
    }
    
    // Apply font size classes
    htmlElement.classList.remove('font-size-large', 'font-size-x-large');
    if (settings.fontSize === 'large') {
      htmlElement.classList.add('font-size-large');
    } else if (settings.fontSize === 'x-large') {
      htmlElement.classList.add('font-size-x-large');
    }
    
    // Apply motion reduction
    htmlElement.classList.remove('motion-reduced');
    if (settings.motionReduction === 'reduced') {
      htmlElement.classList.add('motion-reduced');
    }
  };
  
  const setColorContrast = (level: ColorContrastLevel) => {
    const newSettings = { ...settings, colorContrast: level };
    setSettings(newSettings);
    localStorage.setItem('accessibility-settings', JSON.stringify(newSettings));
    applySettings(newSettings);
  };
  
  const setFontSize = (size: FontSize) => {
    const newSettings = { ...settings, fontSize: size };
    setSettings(newSettings);
    localStorage.setItem('accessibility-settings', JSON.stringify(newSettings));
    applySettings(newSettings);
  };
  
  const setMotionReduction = (motion: MotionReduction) => {
    const newSettings = { ...settings, motionReduction: motion };
    setSettings(newSettings);
    localStorage.setItem('accessibility-settings', JSON.stringify(newSettings));
    applySettings(newSettings);
  };
  
  const handleContrastChange = (value: string) => {
    setColorContrast(value as ColorContrastLevel);
  };
  
  const handleFontSizeChange = (value: string) => {
    setFontSize(value as FontSize);
  };
  
  const handleMotionChange = (value: string) => {
    setMotionReduction(value as MotionReduction);
  };
  
  const resetSettings = () => {
    setSettings(defaultSettings);
    localStorage.setItem('accessibility-settings', JSON.stringify(defaultSettings));
    applySettings(defaultSettings);
  };
  
  const handleReset = () => {
    resetSettings();
    toast({
      title: "Settings Reset",
      description: "Accessibility settings have been reset to defaults."
    });
  };
  
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="outline" 
          size="icon" 
          className="rounded-full hover:bg-primary/10 hover:text-primary transition-all duration-200" 
          aria-label="Accessibility settings"
        >
          <Eye className="h-5 w-5 icon-colorful" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 gradient-card" align="end">
        <Card className="border-0 shadow-none bg-transparent">
          <CardHeader className="px-0 pt-0">
            <CardTitle className="text-lg font-semibold flex items-center gradient-heading">
              <Eye className="h-5 w-5 mr-2 icon-colorful" />
              Accessibility Settings
            </CardTitle>
            <CardDescription>
              Customize your viewing experience
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 px-0">
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center">
                <Sun className="h-4 w-4 mr-2" />
                Color Contrast
              </Label>
              <RadioGroup 
                defaultValue={settings.colorContrast} 
                value={settings.colorContrast} 
                onValueChange={handleContrastChange}
                className="grid grid-cols-3 gap-2 pt-1"
              >
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="default" id="contrast-default" className="sr-only" />
                  <Label 
                    htmlFor="contrast-default" 
                    className={`flex-1 cursor-pointer rounded-md border border-gray-200 px-3 py-2 text-sm font-medium ${settings.colorContrast === 'default' ? 'bg-primary/10 border-primary text-primary' : 'hover:bg-gray-100'}`}
                  >
                    <span className="flex items-center justify-center">
                      <SunDim className="h-4 w-4 mr-1" />
                      Default
                    </span>
                  </Label>
                </div>
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="enhanced" id="contrast-enhanced" className="sr-only" />
                  <Label 
                    htmlFor="contrast-enhanced" 
                    className={`flex-1 cursor-pointer rounded-md border border-gray-200 px-3 py-2 text-sm font-medium ${settings.colorContrast === 'enhanced' ? 'bg-primary/10 border-primary text-primary' : 'hover:bg-gray-100'}`}
                  >
                    <span className="flex items-center justify-center">
                      <SunMedium className="h-4 w-4 mr-1" />
                      Enhanced
                    </span>
                  </Label>
                </div>
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="high" id="contrast-high" className="sr-only" />
                  <Label 
                    htmlFor="contrast-high" 
                    className={`flex-1 cursor-pointer rounded-md border border-gray-200 px-3 py-2 text-sm font-medium ${settings.colorContrast === 'high' ? 'bg-primary/10 border-primary text-primary' : 'hover:bg-gray-100'}`}
                  >
                    <span className="flex items-center justify-center">
                      <Sun className="h-4 w-4 mr-1" />
                      High
                    </span>
                  </Label>
                </div>
              </RadioGroup>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center">
                <Type className="h-4 w-4 mr-2" />
                Font Size
              </Label>
              <RadioGroup 
                defaultValue={settings.fontSize} 
                value={settings.fontSize} 
                onValueChange={handleFontSizeChange}
                className="grid grid-cols-3 gap-2 pt-1"
              >
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="default" id="font-default" className="sr-only" />
                  <Label 
                    htmlFor="font-default" 
                    className={`flex-1 cursor-pointer rounded-md border border-gray-200 px-3 py-2 text-sm font-medium ${settings.fontSize === 'default' ? 'bg-primary/10 border-primary text-primary' : 'hover:bg-gray-100'}`}
                  >
                    Default
                  </Label>
                </div>
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="large" id="font-large" className="sr-only" />
                  <Label 
                    htmlFor="font-large" 
                    className={`flex-1 cursor-pointer rounded-md border border-gray-200 px-3 py-2 text-sm font-medium ${settings.fontSize === 'large' ? 'bg-primary/10 border-primary text-primary' : 'hover:bg-gray-100'}`}
                  >
                    Large
                  </Label>
                </div>
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="x-large" id="font-x-large" className="sr-only" />
                  <Label 
                    htmlFor="font-x-large" 
                    className={`flex-1 cursor-pointer rounded-md border border-gray-200 px-3 py-2 text-sm font-medium ${settings.fontSize === 'x-large' ? 'bg-primary/10 border-primary text-primary' : 'hover:bg-gray-100'}`}
                  >
                    X-Large
                  </Label>
                </div>
              </RadioGroup>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <Label className="text-sm font-medium flex items-center">
                <MousePointer className="h-4 w-4 mr-2" />
                Motion Sensitivity
              </Label>
              <RadioGroup 
                defaultValue={settings.motionReduction} 
                value={settings.motionReduction}
                onValueChange={handleMotionChange}
                className="grid grid-cols-2 gap-2 pt-1"
              >
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="default" id="motion-default" className="sr-only" />
                  <Label 
                    htmlFor="motion-default" 
                    className={`flex-1 cursor-pointer rounded-md border border-gray-200 px-3 py-2 text-sm font-medium ${settings.motionReduction === 'default' ? 'bg-primary/10 border-primary text-primary' : 'hover:bg-gray-100'}`}
                  >
                    Default
                  </Label>
                </div>
                <div className="flex items-center space-x-1">
                  <RadioGroupItem value="reduced" id="motion-reduced" className="sr-only" />
                  <Label 
                    htmlFor="motion-reduced" 
                    className={`flex-1 cursor-pointer rounded-md border border-gray-200 px-3 py-2 text-sm font-medium ${settings.motionReduction === 'reduced' ? 'bg-primary/10 border-primary text-primary' : 'hover:bg-gray-100'}`}
                  >
                    Reduced
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </CardContent>
          <CardFooter className="px-0 pb-0 pt-2 flex justify-between">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setOpen(false)}
              className="hover:text-primary transition-colors duration-200"
            >
              Close
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleReset}
              className="flex items-center hover:bg-primary/10 hover:text-primary transition-all duration-200 border-primary/20"
            >
              <RotateCcw className="h-4 w-4 mr-1 icon-colorful" />
              Reset
            </Button>
          </CardFooter>
        </Card>
      </PopoverContent>
    </Popover>
  );
}